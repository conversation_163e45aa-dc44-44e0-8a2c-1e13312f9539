import { useState, useMemo, useCallback } from 'react';

interface UseTablePaginationProps {
  data: any[];
  currentPage?: number;
  totalPages?: number;
  rowsPerPage?: number;
  onPageChange?: (page: number) => void;
}

interface UseTablePaginationReturn {
  paginatedData: any[];
  currentPage: number;
  totalPages: number;
  handlePageChange: (page: number) => void;
  startIndex: number;
  endIndex: number;
}

export const useTablePagination = ({
  data,
  currentPage: externalCurrentPage = 1,
  totalPages: externalTotalPages,
  rowsPerPage = 10,
  onPageChange,
}: UseTablePaginationProps): UseTablePaginationReturn => {
  const [internalCurrentPage, setInternalCurrentPage] = useState(externalCurrentPage);

  // Determine if we're using external or internal pagination
  const isExternalPagination = Boolean(externalTotalPages && onPageChange);
  const currentPage = isExternalPagination ? externalCurrentPage : internalCurrentPage;

  // Memoized pagination logic
  const paginatedData = useMemo(() => {
    if (isExternalPagination) {
      return data; // External pagination - data is already paginated
    }

    const startIndex = (currentPage - 1) * rowsPerPage;
    return data.slice(startIndex, startIndex + rowsPerPage);
  }, [data, currentPage, rowsPerPage, isExternalPagination]);

  const totalPages = useMemo(() => {
    return externalTotalPages || Math.ceil(data.length / rowsPerPage);
  }, [externalTotalPages, data.length, rowsPerPage]);

  const handlePageChange = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        if (onPageChange) {
          onPageChange(page);
        } else {
          setInternalCurrentPage(page);
        }
      }
    },
    [totalPages, onPageChange]
  );

  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = Math.min(currentPage * rowsPerPage, data.length);

  return {
    paginatedData,
    currentPage,
    totalPages,
    handlePageChange,
    startIndex,
    endIndex,
  };
}; 