"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ofetch";
exports.ids = ["vendor-chunks/ofetch"];
exports.modules = {

/***/ "(ssr)/./node_modules/ofetch/dist/node.mjs":
/*!*******************************************!*\
  !*** ./node_modules/ofetch/dist/node.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $fetch: () => (/* binding */ $fetch),\n/* harmony export */   AbortController: () => (/* binding */ AbortController),\n/* harmony export */   FetchError: () => (/* reexport safe */ _shared_ofetch_03887fc3_mjs__WEBPACK_IMPORTED_MODULE_3__.F),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   createFetch: () => (/* reexport safe */ _shared_ofetch_03887fc3_mjs__WEBPACK_IMPORTED_MODULE_3__.c),\n/* harmony export */   createFetchError: () => (/* reexport safe */ _shared_ofetch_03887fc3_mjs__WEBPACK_IMPORTED_MODULE_3__.a),\n/* harmony export */   createNodeFetch: () => (/* binding */ createNodeFetch),\n/* harmony export */   fetch: () => (/* binding */ fetch),\n/* harmony export */   ofetch: () => (/* binding */ ofetch)\n/* harmony export */ });\n/* harmony import */ var node_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:http */ \"node:http\");\n/* harmony import */ var node_https__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:https */ \"node:https\");\n/* harmony import */ var node_fetch_native__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node-fetch-native */ \"(ssr)/./node_modules/node-fetch-native/dist/index.mjs\");\n/* harmony import */ var _shared_ofetch_03887fc3_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./shared/ofetch.03887fc3.mjs */ \"(ssr)/./node_modules/ofetch/dist/shared/ofetch.03887fc3.mjs\");\n\n\n\n\n\n\n\n\nfunction createNodeFetch() {\n  const useKeepAlive = JSON.parse(process.env.FETCH_KEEP_ALIVE || \"false\");\n  if (!useKeepAlive) {\n    return node_fetch_native__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n  }\n  const agentOptions = { keepAlive: true };\n  const httpAgent = new node_http__WEBPACK_IMPORTED_MODULE_0__.Agent(agentOptions);\n  const httpsAgent = new node_https__WEBPACK_IMPORTED_MODULE_1__.Agent(agentOptions);\n  const nodeFetchOptions = {\n    agent(parsedURL) {\n      return parsedURL.protocol === \"http:\" ? httpAgent : httpsAgent;\n    }\n  };\n  return function nodeFetchWithKeepAlive(input, init) {\n    return (0,node_fetch_native__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input, { ...nodeFetchOptions, ...init });\n  };\n}\nconst fetch = globalThis.fetch ? (...args) => globalThis.fetch(...args) : createNodeFetch();\nconst Headers = globalThis.Headers || node_fetch_native__WEBPACK_IMPORTED_MODULE_2__.Headers;\nconst AbortController = globalThis.AbortController || node_fetch_native__WEBPACK_IMPORTED_MODULE_2__.AbortController;\nconst ofetch = (0,_shared_ofetch_03887fc3_mjs__WEBPACK_IMPORTED_MODULE_3__.c)({ fetch, Headers, AbortController });\nconst $fetch = ofetch;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ofetch/dist/node.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/ofetch/dist/shared/ofetch.03887fc3.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/ofetch/dist/shared/ofetch.03887fc3.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   F: () => (/* binding */ FetchError),\n/* harmony export */   a: () => (/* binding */ createFetchError),\n/* harmony export */   c: () => (/* binding */ createFetch)\n/* harmony export */ });\n/* harmony import */ var destr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! destr */ \"(ssr)/./node_modules/destr/dist/index.mjs\");\n/* harmony import */ var ufo__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ufo */ \"(ssr)/./node_modules/ufo/dist/index.mjs\");\n\n\n\nclass FetchError extends Error {\n  constructor(message, opts) {\n    super(message, opts);\n    this.name = \"FetchError\";\n    if (opts?.cause && !this.cause) {\n      this.cause = opts.cause;\n    }\n  }\n}\nfunction createFetchError(ctx) {\n  const errorMessage = ctx.error?.message || ctx.error?.toString() || \"\";\n  const method = ctx.request?.method || ctx.options?.method || \"GET\";\n  const url = ctx.request?.url || String(ctx.request) || \"/\";\n  const requestStr = `[${method}] ${JSON.stringify(url)}`;\n  const statusStr = ctx.response ? `${ctx.response.status} ${ctx.response.statusText}` : \"<no response>\";\n  const message = `${requestStr}: ${statusStr}${errorMessage ? ` ${errorMessage}` : \"\"}`;\n  const fetchError = new FetchError(\n    message,\n    ctx.error ? { cause: ctx.error } : void 0\n  );\n  for (const key of [\"request\", \"options\", \"response\"]) {\n    Object.defineProperty(fetchError, key, {\n      get() {\n        return ctx[key];\n      }\n    });\n  }\n  for (const [key, refKey] of [\n    [\"data\", \"_data\"],\n    [\"status\", \"status\"],\n    [\"statusCode\", \"status\"],\n    [\"statusText\", \"statusText\"],\n    [\"statusMessage\", \"statusText\"]\n  ]) {\n    Object.defineProperty(fetchError, key, {\n      get() {\n        return ctx.response && ctx.response[refKey];\n      }\n    });\n  }\n  return fetchError;\n}\n\nconst payloadMethods = new Set(\n  Object.freeze([\"PATCH\", \"POST\", \"PUT\", \"DELETE\"])\n);\nfunction isPayloadMethod(method = \"GET\") {\n  return payloadMethods.has(method.toUpperCase());\n}\nfunction isJSONSerializable(value) {\n  if (value === void 0) {\n    return false;\n  }\n  const t = typeof value;\n  if (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n    return true;\n  }\n  if (t !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(value)) {\n    return true;\n  }\n  if (value.buffer) {\n    return false;\n  }\n  return value.constructor && value.constructor.name === \"Object\" || typeof value.toJSON === \"function\";\n}\nconst textTypes = /* @__PURE__ */ new Set([\n  \"image/svg\",\n  \"application/xml\",\n  \"application/xhtml\",\n  \"application/html\"\n]);\nconst JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\nfunction detectResponseType(_contentType = \"\") {\n  if (!_contentType) {\n    return \"json\";\n  }\n  const contentType = _contentType.split(\";\").shift() || \"\";\n  if (JSON_RE.test(contentType)) {\n    return \"json\";\n  }\n  if (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n    return \"text\";\n  }\n  return \"blob\";\n}\nfunction resolveFetchOptions(request, input, defaults, Headers) {\n  const headers = mergeHeaders(\n    input?.headers ?? request?.headers,\n    defaults?.headers,\n    Headers\n  );\n  let query;\n  if (defaults?.query || defaults?.params || input?.params || input?.query) {\n    query = {\n      ...defaults?.params,\n      ...defaults?.query,\n      ...input?.params,\n      ...input?.query\n    };\n  }\n  return {\n    ...defaults,\n    ...input,\n    query,\n    params: query,\n    headers\n  };\n}\nfunction mergeHeaders(input, defaults, Headers) {\n  if (!defaults) {\n    return new Headers(input);\n  }\n  const headers = new Headers(defaults);\n  if (input) {\n    for (const [key, value] of Symbol.iterator in input || Array.isArray(input) ? input : new Headers(input)) {\n      headers.set(key, value);\n    }\n  }\n  return headers;\n}\nasync function callHooks(context, hooks) {\n  if (hooks) {\n    if (Array.isArray(hooks)) {\n      for (const hook of hooks) {\n        await hook(context);\n      }\n    } else {\n      await hooks(context);\n    }\n  }\n}\n\nconst retryStatusCodes = /* @__PURE__ */ new Set([\n  408,\n  // Request Timeout\n  409,\n  // Conflict\n  425,\n  // Too Early (Experimental)\n  429,\n  // Too Many Requests\n  500,\n  // Internal Server Error\n  502,\n  // Bad Gateway\n  503,\n  // Service Unavailable\n  504\n  // Gateway Timeout\n]);\nconst nullBodyResponses = /* @__PURE__ */ new Set([101, 204, 205, 304]);\nfunction createFetch(globalOptions = {}) {\n  const {\n    fetch = globalThis.fetch,\n    Headers = globalThis.Headers,\n    AbortController = globalThis.AbortController\n  } = globalOptions;\n  async function onError(context) {\n    const isAbort = context.error && context.error.name === \"AbortError\" && !context.options.timeout || false;\n    if (context.options.retry !== false && !isAbort) {\n      let retries;\n      if (typeof context.options.retry === \"number\") {\n        retries = context.options.retry;\n      } else {\n        retries = isPayloadMethod(context.options.method) ? 0 : 1;\n      }\n      const responseCode = context.response && context.response.status || 500;\n      if (retries > 0 && (Array.isArray(context.options.retryStatusCodes) ? context.options.retryStatusCodes.includes(responseCode) : retryStatusCodes.has(responseCode))) {\n        const retryDelay = typeof context.options.retryDelay === \"function\" ? context.options.retryDelay(context) : context.options.retryDelay || 0;\n        if (retryDelay > 0) {\n          await new Promise((resolve) => setTimeout(resolve, retryDelay));\n        }\n        return $fetchRaw(context.request, {\n          ...context.options,\n          retry: retries - 1\n        });\n      }\n    }\n    const error = createFetchError(context);\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(error, $fetchRaw);\n    }\n    throw error;\n  }\n  const $fetchRaw = async function $fetchRaw2(_request, _options = {}) {\n    const context = {\n      request: _request,\n      options: resolveFetchOptions(\n        _request,\n        _options,\n        globalOptions.defaults,\n        Headers\n      ),\n      response: void 0,\n      error: void 0\n    };\n    if (context.options.method) {\n      context.options.method = context.options.method.toUpperCase();\n    }\n    if (context.options.onRequest) {\n      await callHooks(context, context.options.onRequest);\n    }\n    if (typeof context.request === \"string\") {\n      if (context.options.baseURL) {\n        context.request = (0,ufo__WEBPACK_IMPORTED_MODULE_0__.withBase)(context.request, context.options.baseURL);\n      }\n      if (context.options.query) {\n        context.request = (0,ufo__WEBPACK_IMPORTED_MODULE_0__.withQuery)(context.request, context.options.query);\n        delete context.options.query;\n      }\n      if (\"query\" in context.options) {\n        delete context.options.query;\n      }\n      if (\"params\" in context.options) {\n        delete context.options.params;\n      }\n    }\n    if (context.options.body && isPayloadMethod(context.options.method)) {\n      if (isJSONSerializable(context.options.body)) {\n        context.options.body = typeof context.options.body === \"string\" ? context.options.body : JSON.stringify(context.options.body);\n        context.options.headers = new Headers(context.options.headers || {});\n        if (!context.options.headers.has(\"content-type\")) {\n          context.options.headers.set(\"content-type\", \"application/json\");\n        }\n        if (!context.options.headers.has(\"accept\")) {\n          context.options.headers.set(\"accept\", \"application/json\");\n        }\n      } else if (\n        // ReadableStream Body\n        \"pipeTo\" in context.options.body && typeof context.options.body.pipeTo === \"function\" || // Node.js Stream Body\n        typeof context.options.body.pipe === \"function\"\n      ) {\n        if (!(\"duplex\" in context.options)) {\n          context.options.duplex = \"half\";\n        }\n      }\n    }\n    let abortTimeout;\n    if (!context.options.signal && context.options.timeout) {\n      const controller = new AbortController();\n      abortTimeout = setTimeout(() => {\n        const error = new Error(\n          \"[TimeoutError]: The operation was aborted due to timeout\"\n        );\n        error.name = \"TimeoutError\";\n        error.code = 23;\n        controller.abort(error);\n      }, context.options.timeout);\n      context.options.signal = controller.signal;\n    }\n    try {\n      context.response = await fetch(\n        context.request,\n        context.options\n      );\n    } catch (error) {\n      context.error = error;\n      if (context.options.onRequestError) {\n        await callHooks(\n          context,\n          context.options.onRequestError\n        );\n      }\n      return await onError(context);\n    } finally {\n      if (abortTimeout) {\n        clearTimeout(abortTimeout);\n      }\n    }\n    const hasBody = (context.response.body || // https://github.com/unjs/ofetch/issues/324\n    // https://github.com/unjs/ofetch/issues/294\n    // https://github.com/JakeChampion/fetch/issues/1454\n    context.response._bodyInit) && !nullBodyResponses.has(context.response.status) && context.options.method !== \"HEAD\";\n    if (hasBody) {\n      const responseType = (context.options.parseResponse ? \"json\" : context.options.responseType) || detectResponseType(context.response.headers.get(\"content-type\") || \"\");\n      switch (responseType) {\n        case \"json\": {\n          const data = await context.response.text();\n          const parseFunction = context.options.parseResponse || destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n          context.response._data = parseFunction(data);\n          break;\n        }\n        case \"stream\": {\n          context.response._data = context.response.body || context.response._bodyInit;\n          break;\n        }\n        default: {\n          context.response._data = await context.response[responseType]();\n        }\n      }\n    }\n    if (context.options.onResponse) {\n      await callHooks(\n        context,\n        context.options.onResponse\n      );\n    }\n    if (!context.options.ignoreResponseError && context.response.status >= 400 && context.response.status < 600) {\n      if (context.options.onResponseError) {\n        await callHooks(\n          context,\n          context.options.onResponseError\n        );\n      }\n      return await onError(context);\n    }\n    return context.response;\n  };\n  const $fetch = async function $fetch2(request, options) {\n    const r = await $fetchRaw(request, options);\n    return r._data;\n  };\n  $fetch.raw = $fetchRaw;\n  $fetch.native = (...args) => fetch(...args);\n  $fetch.create = (defaultOptions = {}, customGlobalOptions = {}) => createFetch({\n    ...globalOptions,\n    ...customGlobalOptions,\n    defaults: {\n      ...globalOptions.defaults,\n      ...customGlobalOptions.defaults,\n      ...defaultOptions\n    }\n  });\n  return $fetch;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ofetch/dist/shared/ofetch.03887fc3.mjs\n");

/***/ })

};
;