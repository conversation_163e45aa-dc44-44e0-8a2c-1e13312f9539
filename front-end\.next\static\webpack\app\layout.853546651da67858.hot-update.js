"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, notification.data.tradeId // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(notification.data.tradeId, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId\n        });\n        // Fetch real trade details if tradeId exists\n        let activeTrade = undefined;\n        if (notification.data.tradeId) {\n            try {\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', notification.data.tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(notification.data.tradeId));\n                if (tradeResponse.status === 200) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    activeTrade = {\n                        id: tradeResponse.data.id,\n                        status: tradeResponse.data.status,\n                        tradeId: tradeResponse.data.id,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: notification.data.tradeId,\n                    status: 'escrowed',\n                    tradeId: notification.data.tradeId,\n                    from: buyerId,\n                    to: sellerId\n                };\n            }\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Close existing modal if it exists to prevent duplicates\n        closeModal(modalId);\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: createReleaseFunction(notification),\n                onRefund: createRefundFunction(notification),\n                onReport: ()=>{},\n                onAccept: createAcceptFunction(notification),\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 703,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 702,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 727,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 736,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 735,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 766,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 773,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 769,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 803,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 857,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 850,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 926,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 787,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 764,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"30eeb9ded06d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzBlZWI5ZGVkMDZkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});