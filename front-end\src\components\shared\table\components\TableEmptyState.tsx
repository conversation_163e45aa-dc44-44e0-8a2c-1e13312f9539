import { motion } from 'framer-motion';
import React from 'react';

interface TableEmptyStateProps {
  icon: React.ReactNode;
  message: string;
}

const TableEmptyState: React.FC<TableEmptyStateProps> = ({ icon, message }) => (
  <motion.div
    className="flex flex-col items-center justify-center py-16 px-6"
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.4 }}
  >
    <motion.div
      className="text-gray-400 mb-4"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
    >
      {icon}
    </motion.div>
    <motion.p
      className="text-gray-600 text-lg font-medium text-center max-w-md"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      {message}
    </motion.p>
  </motion.div>
);

export { TableEmptyState }; 