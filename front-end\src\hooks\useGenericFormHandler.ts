import React, { useCallback } from 'react';

type FormErrors<T> = Record<keyof T, string>;

interface UseGenericFormHandlerOptions<T> {
  formData: T;
  setFormData: React.Dispatch<React.SetStateAction<T>>;
  errors: FormErrors<T>;
  setErrors: React.Dispatch<React.SetStateAction<FormErrors<T>>>;
  validationRules?: Partial<
    Record<keyof T, (value: any, formData?: T) => string>
  >;
}

export const useGenericFormHandler = <T extends Record<string, any>>({
  formData,
  setFormData,
  errors,
  setErrors,
  validationRules = {},
}: UseGenericFormHandlerOptions<T>) => {
  const handleChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));

      // Clear error when user starts typing
      if (errors[name as keyof T]) {
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    [setFormData, errors, setErrors]
  );

  const handleCheckboxChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = e.target;
      setFormData((prev) => ({ ...prev, [name]: checked }));

      // Clear error when user changes checkbox
      if (errors[name as keyof T]) {
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    [setFormData, errors, setErrors]
  );

  const handleBlur = useCallback(
    (
      e: React.FocusEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;
      const fieldName = name as keyof T;

      // Run validation if rule exists
      const validationRule = validationRules[fieldName];
      if (validationRule) {
        const error = validationRule(value, formData);
        setErrors((prev) => ({ ...prev, [name]: error }));
      }
    },
    [validationRules, formData, setErrors]
  );

  const setFieldValue = useCallback(
    (name: keyof T, value: T[keyof T]) => {
      setFormData((prev) => ({ ...prev, [name]: value }));

      // Clear error when programmatically setting value
      if (errors[name]) {
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    [setFormData, errors, setErrors]
  );

  const validateField = useCallback(
    (name: keyof T, value: T[keyof T]): string => {
      const validationRule = validationRules[name];
      if (validationRule) {
        return validationRule(value, formData);
      }
      return '';
    },
    [validationRules, formData]
  );

  const clearFieldError = useCallback(
    (name: keyof T) => {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    },
    [setErrors]
  );

  return {
    handleChange,
    handleCheckboxChange,
    handleBlur,
    setFieldValue,
    validateField,
    clearFieldError,
  };
};
