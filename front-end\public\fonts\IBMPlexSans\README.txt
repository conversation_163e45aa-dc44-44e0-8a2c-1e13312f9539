IBM Plex Sans Variable Font
===========================

This download contains IBM Plex Sans as both variable fonts and static fonts.

IBM Plex Sans is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  IBMPlexSans-VariableFont_wdth,wght.ttf
  IBMPlexSans-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for IBM Plex Sans:
  static/IBMPlexSans_Condensed-Thin.ttf
  static/IBMPlexSans_Condensed-ExtraLight.ttf
  static/IBMPlexSans_Condensed-Light.ttf
  static/IBMPlexSans_Condensed-Regular.ttf
  static/IBMPlexSans_Condensed-Medium.ttf
  static/IBMPlexSans_Condensed-SemiBold.ttf
  static/IBMPlexSans_Condensed-Bold.ttf
  static/IBMPlexSans_SemiCondensed-Thin.ttf
  static/IBMPlexSans_SemiCondensed-ExtraLight.ttf
  static/IBMPlexSans_SemiCondensed-Light.ttf
  static/IBMPlexSans_SemiCondensed-Regular.ttf
  static/IBMPlexSans_SemiCondensed-Medium.ttf
  static/IBMPlexSans_SemiCondensed-SemiBold.ttf
  static/IBMPlexSans_SemiCondensed-Bold.ttf
  static/IBMPlexSans-Thin.ttf
  static/IBMPlexSans-ExtraLight.ttf
  static/IBMPlexSans-Light.ttf
  static/IBMPlexSans-Regular.ttf
  static/IBMPlexSans-Medium.ttf
  static/IBMPlexSans-SemiBold.ttf
  static/IBMPlexSans-Bold.ttf
  static/IBMPlexSans_Condensed-ThinItalic.ttf
  static/IBMPlexSans_Condensed-ExtraLightItalic.ttf
  static/IBMPlexSans_Condensed-LightItalic.ttf
  static/IBMPlexSans_Condensed-Italic.ttf
  static/IBMPlexSans_Condensed-MediumItalic.ttf
  static/IBMPlexSans_Condensed-SemiBoldItalic.ttf
  static/IBMPlexSans_Condensed-BoldItalic.ttf
  static/IBMPlexSans_SemiCondensed-ThinItalic.ttf
  static/IBMPlexSans_SemiCondensed-ExtraLightItalic.ttf
  static/IBMPlexSans_SemiCondensed-LightItalic.ttf
  static/IBMPlexSans_SemiCondensed-Italic.ttf
  static/IBMPlexSans_SemiCondensed-MediumItalic.ttf
  static/IBMPlexSans_SemiCondensed-SemiBoldItalic.ttf
  static/IBMPlexSans_SemiCondensed-BoldItalic.ttf
  static/IBMPlexSans-ThinItalic.ttf
  static/IBMPlexSans-ExtraLightItalic.ttf
  static/IBMPlexSans-LightItalic.ttf
  static/IBMPlexSans-Italic.ttf
  static/IBMPlexSans-MediumItalic.ttf
  static/IBMPlexSans-SemiBoldItalic.ttf
  static/IBMPlexSans-BoldItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
