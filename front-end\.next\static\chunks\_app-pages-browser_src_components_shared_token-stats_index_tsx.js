"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_token-stats_index_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-column.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartColumn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n            key: \"c24i48\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17V9\",\n            key: \"2bz60n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17V5\",\n            key: \"1frdt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17v-3\",\n            key: \"17ska0\"\n        }\n    ]\n];\nconst ChartColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-column\", __iconNode);\n //# sourceMappingURL=chart-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 17 13.5 8.5 8.5 13.5 2 7\",\n            key: \"1r2t7k\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 17 22 17 22 11\",\n            key: \"11uiuu\"\n        }\n    ]\n];\nconst TrendingDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-down\", __iconNode);\n //# sourceMappingURL=trending-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\",\n            key: \"975kel\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"17ys0d\"\n        }\n    ]\n];\nconst User = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user\", __iconNode);\n //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Volume2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 9a5 5 0 0 1 0 6\",\n            key: \"1q6k2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n            key: \"ijwkga\"\n        }\n    ]\n];\nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-2\", __iconNode);\n //# sourceMappingURL=volume-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shared/token-stats/index.tsx":
/*!*****************************************************!*\
  !*** ./src/components/shared/token-stats/index.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_LoadingSkeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/LoadingSkeletons */ \"(app-pages-browser)/./src/components/ui/LoadingSkeletons.tsx\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TokenStats = (param)=>{\n    let { className, symbols = [\n        'ANAS/USDTaaa'\n    ], creator = 'By Anas Sharif', tokenImage = '/images/placeholder.png', stats, loading = false } = param;\n    _s();\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSymbol, setSelectedSymbol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(symbols[0]);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const formatPercentage = (value)=>{\n        return value.toFixed(2) + '%';\n    };\n    const formatVolume = (value)=>{\n        if (value >= 1000000) {\n            return (value / 1000000).toFixed(1) + 'M';\n        } else if (value >= 1000) {\n            return (value / 1000).toFixed(1) + 'K';\n        }\n        return value.toString();\n    };\n    const statsDisplay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TokenStats.useMemo[statsDisplay]\": ()=>[\n                {\n                    label: t('tokenStats.marketValue'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    color: 'text-[#FF6600]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.low24h'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    color: 'text-red-600',\n                    bgColor: 'bg-red-50',\n                    borderColor: 'border-red-200'\n                },\n                {\n                    label: t('tokenStats.high24h'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    color: 'text-[#F58A38]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.volume'),\n                    value: formatVolume(stats.volume24h),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    color: 'text-[#FF6600]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.return24h'),\n                    value: formatPercentage(stats.return24h),\n                    icon: stats.return24h >= 0 ? _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    color: stats.return24h >= 0 ? 'text-[#F58A38]' : 'text-red-600',\n                    bgColor: stats.return24h >= 0 ? 'bg-[#FFF7F1]' : 'bg-red-50',\n                    borderColor: stats.return24h >= 0 ? 'border-[#F58A38]/30' : 'border-red-200'\n                }\n            ]\n    }[\"TokenStats.useMemo[statsDisplay]\"], [\n        stats.usd,\n        stats.volume24h,\n        stats.return24h\n    ]);\n    const statsValueMaxLength = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TokenStats.useMemo[statsValueMaxLength]\": ()=>{\n            return Math.max(...statsDisplay.map({\n                \"TokenStats.useMemo[statsValueMaxLength]\": (stat)=>stat.value.length\n            }[\"TokenStats.useMemo[statsValueMaxLength]\"]));\n        }\n    }[\"TokenStats.useMemo[statsValueMaxLength]\"], [\n        statsDisplay\n    ]);\n    const toggleDropdown = ()=>{\n        setIsDropdownOpen(!isDropdownOpen);\n    };\n    const handleSelectSymbol = (newSymbol)=>{\n        setSelectedSymbol(newSymbol);\n        setIsDropdownOpen(false);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSkeletons__WEBPACK_IMPORTED_MODULE_2__.StatsSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n            lineNumber: 117,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        className: \"w-full bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden \".concat(className),\n        ..._lib_animations__WEBPACK_IMPORTED_MODULE_3__.fadeInUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"flex items-center space-x-4\",\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"h-12 w-12 rounded-xl bg-gradient-to-br from-[#FF6600] to-[#F58A38] flex items-center justify-center shadow-lg overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            type: 'spring',\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 24,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h3, {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: selectedSymbol\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                            className: \"flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-lg hover:bg-gray-100\",\n                                                            onClick: toggleDropdown,\n                                                            whileHover: {\n                                                                scale: 1.05\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                animate: {\n                                                                    rotate: isDropdownOpen ? 180 : 0\n                                                                },\n                                                                transition: {\n                                                                    duration: 0.3\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    size: 18\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                                            children: isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                className: \"absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-xl z-50 border border-gray-200 overflow-hidden\",\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    y: -10,\n                                                                    scale: 0.95\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    y: 0,\n                                                                    scale: 1\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    y: -10,\n                                                                    scale: 0.95\n                                                                },\n                                                                transition: {\n                                                                    duration: 0.2\n                                                                },\n                                                                children: symbols.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                        className: \"flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                                        onClick: ()=>handleSelectSymbol(symbol),\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -10\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: index * 0.05\n                                                                        },\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 rounded-lg bg-gray-100 mr-3 flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    size: 16,\n                                                                                    className: \"text-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                    lineNumber: 191,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: symbol\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                        lineNumber: 194,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: t('tokenStats.tradingPair')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                        lineNumber: 197,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                lineNumber: 193,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"flex items-center space-x-2 mt-1\",\n                                            initial: {\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: creator\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"flex items-center space-x-2\",\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 bg-[#FFF7F1] rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-[#FF6600]\",\n                                        children: t('tokenStats.activeTrading')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-[#F58A38] rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"grid gap-4 \".concat(statsValueMaxLength > 10 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4' : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5'),\n                    variants: _lib_animations__WEBPACK_IMPORTED_MODULE_3__.staggerContainer,\n                    initial: \"initial\",\n                    animate: \"animate\",\n                    children: statsDisplay.map((stat, index)=>{\n                        const IconComponent = stat.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"relative group \".concat(stat.bgColor, \" \").concat(stat.borderColor, \" border rounded-xl p-4 transition-all duration-300 hover:shadow-md cursor-pointer\"),\n                            variants: {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    transition: {\n                                        delay: 0.5 + index * 0.1\n                                    }\n                                }\n                            },\n                            whileHover: {\n                                scale: 1.03,\n                                y: -2\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 \".concat(stat.bgColor, \" rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                size: 16,\n                                                className: stat.color\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                            className: \"text-lg font-bold \".concat(stat.color, \" group-hover:scale-105 transition-transform duration-200\"),\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-white/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TokenStats, \"5/+DKeN2xwwHIN8HSK/oWuyMbLk=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = TokenStats;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TokenStats);\nvar _c;\n$RefreshReg$(_c, \"TokenStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/token-stats/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/LoadingSkeletons.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/LoadingSkeletons.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommentsSkeleton: () => (/* binding */ CommentsSkeleton),\n/* harmony export */   DashboardSkeleton: () => (/* binding */ DashboardSkeleton),\n/* harmony export */   ProductCardSkeleton: () => (/* binding */ ProductCardSkeleton),\n/* harmony export */   StatsSkeleton: () => (/* binding */ StatsSkeleton),\n/* harmony export */   TableSkeleton: () => (/* binding */ TableSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TableSkeleton,StatsSkeleton,CommentsSkeleton,ProductCardSkeleton,DashboardSkeleton auto */ \n\n\n// Animation variants\nconst shimmer = {\n    animate: {\n        backgroundPosition: [\n            '200% 0',\n            '-200% 0'\n        ]\n    },\n    transition: {\n        duration: 2,\n        ease: 'linear',\n        repeat: Infinity\n    }\n};\nconst pulse = {\n    animate: {\n        opacity: [\n            0.6,\n            1,\n            0.6\n        ]\n    },\n    transition: {\n        duration: 1.5,\n        ease: 'easeInOut',\n        repeat: Infinity\n    }\n};\nconst wave = {\n    animate: {\n        backgroundPosition: [\n            '-200% 0',\n            '200% 0'\n        ]\n    },\n    transition: {\n        duration: 1.5,\n        ease: 'linear',\n        repeat: Infinity\n    }\n};\n// Base skeleton component with improved accessibility\nconst SkeletonBase = (param)=>{\n    let { className = '', children, variant = 'rectangular', width, height, animation = 'shimmer', 'data-testid': dataTestId, 'aria-label': ariaLabel } = param;\n    const getAnimationVariant = ()=>{\n        switch(animation){\n            case 'pulse':\n                return pulse;\n            case 'wave':\n                return wave;\n            case 'shimmer':\n            default:\n                return shimmer;\n        }\n    };\n    const getVariantClasses = ()=>{\n        switch(variant){\n            case 'circular':\n                return 'rounded-full';\n            case 'text':\n                return 'rounded';\n            case 'rectangular':\n            default:\n                return 'rounded';\n        }\n    };\n    const style = {\n        width: width,\n        height: height\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] \".concat(getVariantClasses(), \" \").concat(className),\n        style: style,\n        ...getAnimationVariant(),\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        role: \"status\",\n        \"aria-live\": \"polite\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SkeletonBase;\n// Table skeleton with improved structure\nconst TableSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-5 bg-neutral-200 rounded-2xl\",\n        \"data-testid\": props['data-testid'] || 'table-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading table data\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-8 w-48 mb-6 rounded\",\n                \"aria-label\": \"Loading table title\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 118,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 3\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-32 rounded\",\n                                \"aria-label\": \"Loading row \".concat(i + 1, \" column 1\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading row \".concat(i + 1, \" column 2\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-16 rounded\",\n                                \"aria-label\": \"Loading row \".concat(i + 1, \" column 3\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-8 w-20 rounded\",\n                                \"aria-label\": \"Loading row \".concat(i + 1, \" action\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 119,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 112,\n        columnNumber: 3\n    }, undefined);\n_c1 = TableSkeleton;\n// Header skeleton with improved accessibility\nconst HeaderSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center mb-8\",\n        \"data-testid\": props['data-testid'] || 'header-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading page header\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-12 w-64 rounded\",\n                        \"aria-label\": \"Loading page title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-6 w-48 rounded\",\n                        \"aria-label\": \"Loading page subtitle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 140,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-10 w-32 rounded\",\n                \"aria-label\": \"Loading header action\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 144,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 134,\n        columnNumber: 3\n    }, undefined);\n_c2 = HeaderSkeleton;\n// Stats skeleton with improved structure\nconst StatsSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border p-6\",\n        \"data-testid\": props['data-testid'] || 'stats-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading statistics\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-12 h-12 rounded-full mr-4\",\n                        \"aria-label\": \"Loading stats icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-5 w-32 rounded\",\n                                \"aria-label\": \"Loading stats title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading stats subtitle\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 156,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-5 gap-4\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 5\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-16 rounded mb-2\",\n                                \"aria-label\": \"Loading stat \".concat(i + 1, \" label\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-5 w-20 rounded\",\n                                \"aria-label\": \"Loading stat \".concat(i + 1, \" value\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 167,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined);\n_c3 = StatsSkeleton;\n// Comments skeleton with improved structure\nconst CommentsSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        \"data-testid\": props['data-testid'] || 'comments-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading comments\",\n        children: Array.from({\n            length: 3\n        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-14 h-14 rounded-full\",\n                        \"aria-label\": \"Loading comment \".concat(i + 1, \" avatar\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading comment \".concat(i + 1, \" author\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-full rounded\",\n                                \"aria-label\": \"Loading comment \".concat(i + 1, \" content\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-3 w-20 rounded\",\n                                \"aria-label\": \"Loading comment \".concat(i + 1, \" timestamp\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, i, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 180,\n        columnNumber: 3\n    }, undefined);\n_c4 = CommentsSkeleton;\nconst ProductCardSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-md p-6\",\n        \"data-testid\": props['data-testid'] || 'product-card-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading product card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-10 h-10 rounded-full mr-3\",\n                        \"aria-label\": \"Loading product avatar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading product name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-3 w-16 rounded\",\n                                \"aria-label\": \"Loading product category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 209,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-16 w-full rounded mb-4\",\n                \"aria-label\": \"Loading product image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 220,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                role: \"presentation\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-full rounded\",\n                        \"aria-label\": \"Loading product description line 1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-3/4 rounded\",\n                        \"aria-label\": \"Loading product description line 2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-1/2 rounded\",\n                        \"aria-label\": \"Loading product description line 3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 221,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 border rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-6 w-20 rounded mb-3\",\n                        \"aria-label\": \"Loading price label\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-8 w-24 rounded\",\n                                \"aria-label\": \"Loading price\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-10 w-32 rounded-full\",\n                                \"aria-label\": \"Loading action button\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 226,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 203,\n        columnNumber: 3\n    }, undefined);\n_c5 = ProductCardSkeleton;\n// Dashboard skeleton with improved structure\nconst DashboardSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        \"data-testid\": props['data-testid'] || 'dashboard-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading dashboard\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 244,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 6\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-32 rounded-lg\",\n                        \"aria-label\": \"Loading dashboard card \".concat(i + 1)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 245,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 254,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 238,\n        columnNumber: 3\n    }, undefined);\n_c6 = DashboardSkeleton;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"SkeletonBase\");\n$RefreshReg$(_c1, \"TableSkeleton\");\n$RefreshReg$(_c2, \"HeaderSkeleton\");\n$RefreshReg$(_c3, \"StatsSkeleton\");\n$RefreshReg$(_c4, \"CommentsSkeleton\");\n$RefreshReg$(_c5, \"ProductCardSkeleton\");\n$RefreshReg$(_c6, \"DashboardSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadingSkeletons.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/animations.ts":
/*!*******************************!*\
  !*** ./src/lib/animations.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buttonHover: () => (/* binding */ buttonHover),\n/* harmony export */   cardHover: () => (/* binding */ cardHover),\n/* harmony export */   fadeInUp: () => (/* binding */ fadeInUp),\n/* harmony export */   staggerContainer: () => (/* binding */ staggerContainer)\n/* harmony export */ });\nconst fadeInUp = {\n    initial: {\n        opacity: 0,\n        y: 20\n    },\n    animate: {\n        opacity: 1,\n        y: 0\n    },\n    exit: {\n        opacity: 0,\n        y: -20\n    }\n};\n// Stagger container for lists\nconst staggerContainer = {\n    initial: {},\n    animate: {\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.1\n        }\n    },\n    exit: {\n        transition: {\n            staggerChildren: 0.05,\n            staggerDirection: -1\n        }\n    }\n};\n// Card hover animations\nconst cardHover = {\n    scale: 1.02,\n    y: -5,\n    transition: {\n        duration: 0.2,\n        ease: [\n            0.4,\n            0.0,\n            0.2,\n            1\n        ]\n    }\n};\n// Button animations\nconst buttonHover = {\n    scale: 1.02,\n    transition: {\n        duration: 0.2,\n        ease: [\n            0.4,\n            0.0,\n            0.2,\n            1\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/animations.ts\n"));

/***/ })

}]);