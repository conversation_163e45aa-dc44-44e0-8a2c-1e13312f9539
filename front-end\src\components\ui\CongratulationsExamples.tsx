"use client";

import React, { useState } from 'react';
import { CongratulationsModal, CongratulationType } from './CongratulationsModal';
import { useCongratulationsModal } from '@/hooks/useCongratulationsModal';

/**
 * Congratulations Modal Examples and Demo
 * 
 * This component demonstrates all the features and variations of the
 * advanced congratulations modal system.
 */

export const CongratulationsExamplesDemo: React.FC = () => {
  const [demoModalOpen, setDemoModalOpen] = useState(false);
  const [demoType, setDemoType] = useState<CongratulationType>('purchase_success');

  // Initialize the congratulations modal hook
  const congratulationsModal = useCongratulationsModal({
    onNavigateToChat: (chatRoomId) => {
      console.log('Navigate to chat:', chatRoomId);
      alert(`Would navigate to chat room: ${chatRoomId}`);
    },
    onViewTransaction: (txId) => {
      console.log('View transaction:', txId);
      alert(`Would view transaction: ${txId}`);
    },
    onShareSuccess: (type, txId) => {
      console.log('Share success:', type, txId);
      alert(`Would share ${type} success${txId ? ` for transaction ${txId}` : ''}`);
    },
    onContinueShopping: () => {
      console.log('Continue shopping');
      alert('Would navigate to shopping page');
    }
  });

  const modalTypes: { type: CongratulationType; label: string; description: string }[] = [
    {
      type: 'purchase_success',
      label: 'Purchase Success',
      description: 'Escrow transaction created successfully'
    },
    {
      type: 'transaction_complete',
      label: 'Transaction Complete',
      description: 'General transaction completion'
    },
    {
      type: 'escrow_created',
      label: 'Escrow Created',
      description: 'Funds secured in escrow'
    },
    {
      type: 'funds_released',
      label: 'Funds Released',
      description: 'Payment released to seller'
    },
    {
      type: 'refund_processed',
      label: 'Refund Processed',
      description: 'Refund completed successfully'
    },
    {
      type: 'dispute_resolved',
      label: 'Dispute Resolved',
      description: 'Dispute resolution completed'
    },
    {
      type: 'general_success',
      label: 'General Success',
      description: 'Generic success message'
    }
  ];

  const showDemoModal = (type: CongratulationType) => {
    setDemoType(type);
    setDemoModalOpen(true);
  };

  const showHookExample = (type: CongratulationType) => {
    const txId = `tx_${Date.now()}`;
    const amount = '2.5 SOL';
    
    switch (type) {
      case 'purchase_success':
        congratulationsModal.showPurchaseSuccess(txId, amount, 'chat_123');
        break;
      case 'funds_released':
        congratulationsModal.showFundsReleased(txId, amount);
        break;
      case 'refund_processed':
        congratulationsModal.showRefundProcessed(txId, amount);
        break;
      case 'dispute_resolved':
        congratulationsModal.showDisputeResolved('dispute_456', 'Resolved in favor of buyer');
        break;
      default:
        congratulationsModal.showTransactionComplete(txId, amount);
    }
  };

  return (
    <div className="space-y-8 p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Advanced Congratulations Modal System
        </h1>

        {/* Features Overview */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">🎨 Visual Design</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Modern, celebratory design</li>
                <li>• Professional styling</li>
                <li>• Consistent with UI system</li>
                <li>• Mobile-responsive layout</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">✨ Advanced Animations</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Framer Motion integration</li>
                <li>• Confetti particle effects</li>
                <li>• Success icon animations</li>
                <li>• Staggered text animations</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">🚀 Enhanced Features</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Multiple congratulations types</li>
                <li>• Customizable messages</li>
                <li>• Action buttons</li>
                <li>• Auto-dismiss timer</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">♿ User Experience</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Clear success messaging</li>
                <li>• Next steps guidance</li>
                <li>• Accessibility features</li>
                <li>• Keyboard navigation</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Modal Types Demo */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Modal Types</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modalTypes.map((item) => (
              <div key={item.type} className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">{item.label}</h3>
                <p className="text-sm text-gray-600 mb-4">{item.description}</p>
                <div className="space-y-2">
                  <button
                    onClick={() => showDemoModal(item.type)}
                    className="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
                  >
                    Show Demo Modal
                  </button>
                  <button
                    onClick={() => showHookExample(item.type)}
                    className="w-full bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 transition-colors"
                  >
                    Show Hook Example
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Integration Examples */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Integration Examples</h2>
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Escrow Purchase Success</h3>
              <pre className="text-sm text-gray-600 overflow-x-auto">
{`congratulationsModal.showPurchaseSuccess(
  'tx_abc123',
  '2.5 SOL',
  'chat_room_456'
);`}
              </pre>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Funds Released</h3>
              <pre className="text-sm text-gray-600 overflow-x-auto">
{`congratulationsModal.showFundsReleased(
  'tx_def456',
  '1.8 SOL'
);`}
              </pre>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Custom Configuration</h3>
              <pre className="text-sm text-gray-600 overflow-x-auto">
{`congratulationsModal.showCongratulations({
  type: 'general_success',
  title: 'Custom Success!',
  message: 'Your custom operation completed.',
  actions: [
    {
      label: 'Continue',
      onClick: () => console.log('Continue'),
      variant: 'primary'
    }
  ],
  showConfetti: true,
  autoDismiss: false
});`}
              </pre>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section className="bg-blue-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-900">Usage Guidelines</h2>
          <div className="space-y-3 text-blue-800">
            <div>
              <strong>When to Use:</strong> Use congratulations modals for significant positive outcomes like successful purchases, completed transactions, or resolved disputes.
            </div>
            <div>
              <strong>Auto-dismiss:</strong> Enable auto-dismiss for informational successes, disable for actions requiring user decision.
            </div>
            <div>
              <strong>Confetti:</strong> Use confetti for celebratory moments like purchases or achievements, disable for routine operations.
            </div>
            <div>
              <strong>Actions:</strong> Provide clear next steps with action buttons. Primary actions should guide users to the most logical next step.
            </div>
            <div>
              <strong>Accessibility:</strong> Always include descriptive text and ensure keyboard navigation works properly.
            </div>
          </div>
        </section>
      </div>

      {/* Demo Modal */}
      <CongratulationsModal
        isOpen={demoModalOpen}
        onClose={() => setDemoModalOpen(false)}
        type={demoType}
        transactionId="demo_tx_123456789"
        amount="2.5 SOL"
        actions={[
          {
            label: 'Continue',
            onClick: () => setDemoModalOpen(false),
            variant: 'primary'
          },
          {
            label: 'View Details',
            onClick: () => alert('Would view details'),
            variant: 'secondary'
          }
        ]}
        metrics={[
          {
            label: 'Processing Time',
            value: '2.3 seconds',
            icon: <span>⏱️</span>
          },
          {
            label: 'Network Fee',
            value: '0.001 SOL',
            icon: <span>⛽</span>
          }
        ]}
        autoDismiss={false}
        showConfetti={true}
      />

      {/* Hook-managed Modal */}
      <CongratulationsModal {...congratulationsModal.getModalProps()} />
    </div>
  );
};

export default CongratulationsExamplesDemo;
