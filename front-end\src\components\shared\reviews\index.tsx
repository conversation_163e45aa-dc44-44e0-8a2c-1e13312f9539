"use client";

import Image from "next/image";
import React, { useEffect, useState } from "react";

import { postPerkReviews } from "../../../axios/requests";
import { useAppContext } from "../../../contexts/AppContext";
import { useTranslation } from '@/hooks/useTranslation';
import { showErrorToast, showSuccessToast } from "@/utils/errorHandling";

interface Review {
  id: number;
  name: string;
  date: string;
  review: string;
  avatar?: string;
}

interface ReviewsProps {
  perkId: any;
  reviews?: Review[];
  canPost: false;
  onSort?: () => void;
  onShowDetails?: (review: Review) => void;
}

const defaultReviews: Review[] = [
  {
    id: 1,
    name: "User",
    date: "Apr 18, 2024",
    review: "I think this coin has good projects",
    avatar: "/images/placeholder.png",
  },
  {
    id: 2,
    name: "User",
    date: "Apr 18, 2024",
    review: "No, they are selling bad products",
    avatar: "/images/placeholder.png",
  },
  {
    id: 3,
    name: "User",
    date: "Apr 17, 2024",
    review: "I lost money with this investment",
    avatar: "/images/placeholder.png",
  },
];

const Reviews: React.FC<ReviewsProps> = ({
  perkId,
  reviews = defaultReviews,
  canPost = false,
  onSort,
  onShowDetails,
}) => {
  const [newComment, setNewComment] = useState("");
  const [reviewsShow, setReviewsShow] = useState<any[]>([]);
  const { state, logout } = useAppContext();
  const { t } = useTranslation();

  useEffect(() => {
    setReviewsShow(reviews);
  }, [reviews]); // Run the effect when `id` changes

  // Transform incoming raw comments to internal format
  const displayedReviews = reviewsShow.map((review, index) => {
    const dateObj = new Date(review.updatedAt);
    const formattedDate = dateObj.toLocaleString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
      month: "short",
      day: "numeric",
      year: "numeric",
    });

    return {
      id: review.id ?? index,
      name: review.UserName || "Anonymous",
      date: formattedDate, // e.g., "09:15 AM May 14, 2025"
      review: review.Review || "",
      avatar: review.Avatar || "/images/placeholder.png",
    };
  });

  const handlePostComment = async () => {
    if (!newComment.trim()) {
      showErrorToast("Review cannot be empty.");
      return;
    }

    const userId = state?.userBo?.id;
    const username = state?.userBo?.username;
    const avatar = "/images/placeholder.png";

    if (!userId) {
      showErrorToast("Please login!");
      return;
    }

    try {
      const response = await postPerkReviews({
        userId,
        perkId: perkId,
        username,
        review: newComment,
        avatar,
      });
      setReviewsShow(response.data);
      showSuccessToast("Thanks for posting your comment.");

      setNewComment(""); // Clear the input field after posting
    } catch (error) {
      showErrorToast("Failed to post comment. Please try again.");
      console.error("Post comment error:", error);
    }
  };

  // Function to determine margin bottom based on review index
  const getMarginBottom = (index: number) => {
    if (index === 0) return "mb-[28px]";
    if (index === 1) return "mb-[31px]";
    if (index === 2) return "mb-[30px]";
    return "mb-6"; // Default margin for any additional reviews
  };

  return (
    <div className="w-full flex flex-col border border-[#F58A38] rounded-[10px] overflow-hidden">
      <div className="w-full h-[76px] px-[99px] py-4 bg-[#F58A38] flex justify-center items-center gap-2.5">
        <div className="text-center justify-center text-white text-2xl font-semibold font-['IBM_Plex_Sans'] leading-normal">
          {t('reviews.title', 'Reviews')}
        </div>
      </div>

      <div className="flex flex-col flex-1">
        <div className="w-full h-98 bg-[#FFF7F1] rounded-bl-[10px] rounded-br-[10px] p-0 flex flex-col">
          {/* Scrollable Comments Section */}
          <div className="flex-1 overflow-y-auto p-6">
            {displayedReviews.map((review, index) => (
              <div
                key={review.id}
                className={`flex items-center pl-6 ${getMarginBottom(index)}`}
              >
                <div className="w-14 h-14 bg-stone-300 rounded-[100px] relative mr-4">
                  {review.avatar && (
                    <Image
                      src={review.avatar}
                      alt={review.name}
                      fill
                      className="object-cover rounded-[100px]"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex flex-col justify-start items-start">
                    <div className="text-black text-base font-semibold font-['IBM_Plex_Sans'] leading-normal">
                      {review.name}
                    </div>
                    <div className="text-black text-xs font-light font-['IBM_Plex_Sans'] leading-normal">
                      {review.review}
                    </div>
                    <div className="text-gray-500 text-[11px] mt-1 font-['IBM_Plex_Sans']">
                      {review.date}
                    </div>
                  </div>
                </div>
                <div className="w-8 h-8 bg-white/30 rounded-[50px] backdrop-blur-[1.50px] flex items-center justify-center">
                  <Image
                    src="/icons/hurt.svg"
                    alt="Heart icon"
                    width={16}
                    height={14}
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="px-6 py-5">
            {!canPost ? (
              <div className="w-full h-[67px] bg-[#C4C4C4] rounded-lg flex items-center justify-center text-white text-xl font-semibold font-['IBM_Plex_Sans'] leading-normal mb-2 hover:bg-black hover:text-white transition-colors duration-300 cursor-pointer text-center">
                {t('reviews.mustHavePerk', 'You should have this perk to Review')}
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <input
                  type="text"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder={t('reviews.writeAReview', 'Write a review...')}
                  className="flex-1 h-12 px-4 rounded-lg border border-gray-300 focus:outline-none"
                />
                <div
                  onClick={handlePostComment}
                  className="w-24 h-12 bg-[#C4C4C4] rounded-lg flex items-center justify-center text-white text-lg font-semibold font-['IBM_Plex_Sans'] hover:bg-black hover:text-white transition-colors duration-300 cursor-pointer"
                >
                  {t('reviews.post', 'Post')}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reviews;
