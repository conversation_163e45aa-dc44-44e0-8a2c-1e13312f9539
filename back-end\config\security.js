// config/env.js
require("dotenv").config();
const bs58 = require("bs58");
const { Keypair } = require("@solana/web3.js");

function required(name) {
  const value = process.env[name];
  if (!value) {
    throw new Error(`❌ Missing required environment variable: ${name}`);
  }
  return value;
}

module.exports = {
  PORT: process.env.PORT || 8081,

  DB: {
    HOST: required("DATABASE_HOST"),
    DIALECT: required("DATABASE_DIALECT"),
    NAME: required("DATABASE_NAME"),
    USERNAME: required("DATABASE_USERNAME"),
    PASSWORD: process.env.DATABASE_PASSWORD || "",
    PORT: process.env.DATABASE_PORT || 3306,
    MAX_CONNECTION: process.env.MYSQL_MAX_CONNECTION || 10,
  },

  JWT_SECRET: required("JWT_SECRET"),

  HELIUS_API_KEY: required("HELIUS_API_KEY"),

  CONNECTION_URL: required("CONNECTIONURL"),
  NEXT_PUBLIC_RPC: required("NEXT_PUBLIC_RPC"),
  PROGRAM_ID: required("PROGRAM_ID"),

  ADMIN_PRIVATE_KEY: required("ADMIN_PRIVATE_KEY"),
  AIRDROP_WALLET_PRIVATE_KEY: required("AIRDROP_WALLET_PRIVATE_KEY"),

  ISLIVE: process.env.ISLIVE === "true",

  CORS_ORIGINS: required("CORS_ORIGINS")
    .split(",")
    .map((origin) => origin.trim()),

  MAX_PAGE: process.env.MAX_PAGE || 15,

  // Contract initialization parameters
  INITIAL_VIRTUAL_TOKEN_RESERVES:
    process.env.INITIAL_VIRTUAL_TOKEN_RESERVES || "1073000191000000", // 1_073_000_191 * 1e6
  INITIAL_VIRTUAL_SOL_RESERVES:
    process.env.INITIAL_VIRTUAL_SOL_RESERVES || (30 * 1e9).toString(), // 30 * LAMPORTS_PER_SOL
  TOKEN_TOTAL_SUPPLY: process.env.TOKEN_TOTAL_SUPPLY || "1000000000000000", // 1_000_000_000 * 1e6
  CREATOR_VAULT_AMOUNT: process.env.CREATOR_VAULT_AMOUNT || "1000000000000", // 1_000_000 * 1e6
  GRADUATION_THRESHOLD:
    process.env.GRADUATION_THRESHOLD || (2 * 1e9).toString(), // 2 * LAMPORTS_PER_SOL
  TRADING_FEE_BPS: process.env.TRADING_FEE_BPS || "100", // 1%
  FIRST_BUY_FEE_SOL: process.env.FIRST_BUY_FEE_SOL || (0.02 * 1e9).toString(), // 0.02 * LAMPORTS_PER_SOL
  FEE_RECIPIENT:
    process.env.FEE_RECIPIENT ||
    Keypair.fromSecretKey(
      bs58.decode(process.env.ADMIN_PRIVATE_KEY),
    ).publicKey.toBase58(),
  UPDATE_GLOBAL_CONFIG: process.env.UPDATE_GLOBAL_CONFIG === "true",
  NEW_AUTHORITY:
    process.env.UPDATE_GLOBAL_CONFIG === "true"
      ? required("NEW_AUTHORITY")
      : process.env.NEW_AUTHORITY,

  // Add more required environment vars below as needed
};
