/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/toml";
exports.ids = ["vendor-chunks/toml"];
exports.modules = {

/***/ "(ssr)/./node_modules/toml/index.js":
/*!************************************!*\
  !*** ./node_modules/toml/index.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parser = __webpack_require__(/*! ./lib/parser */ \"(ssr)/./node_modules/toml/lib/parser.js\");\nvar compiler = __webpack_require__(/*! ./lib/compiler */ \"(ssr)/./node_modules/toml/lib/compiler.js\");\n\nmodule.exports = {\n  parse: function(input) {\n    var nodes = parser.parse(input.toString());\n    return compiler.compile(nodes);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdG9tbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsNkRBQWM7QUFDbkMsZUFBZSxtQkFBTyxDQUFDLGlFQUFnQjs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFx0b21sXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgcGFyc2VyID0gcmVxdWlyZSgnLi9saWIvcGFyc2VyJyk7XG52YXIgY29tcGlsZXIgPSByZXF1aXJlKCcuL2xpYi9jb21waWxlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgcGFyc2U6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgdmFyIG5vZGVzID0gcGFyc2VyLnBhcnNlKGlucHV0LnRvU3RyaW5nKCkpO1xuICAgIHJldHVybiBjb21waWxlci5jb21waWxlKG5vZGVzKTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/toml/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/toml/lib/compiler.js":
/*!*******************************************!*\
  !*** ./node_modules/toml/lib/compiler.js ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
eval("\nfunction compile(nodes) {\n  var assignedPaths = [];\n  var valueAssignments = [];\n  var currentPath = \"\";\n  var data = Object.create(null);\n  var context = data;\n  var arrayMode = false;\n\n  return reduce(nodes);\n\n  function reduce(nodes) {\n    var node;\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      switch (node.type) {\n      case \"Assign\":\n        assign(node);\n        break;\n      case \"ObjectPath\":\n        setPath(node);\n        break;\n      case \"ArrayPath\":\n        addTableArray(node);\n        break;\n      }\n    }\n\n    return data;\n  }\n\n  function genError(err, line, col) {\n    var ex = new Error(err);\n    ex.line = line;\n    ex.column = col;\n    throw ex;\n  }\n\n  function assign(node) {\n    var key = node.key;\n    var value = node.value;\n    var line = node.line;\n    var column = node.column;\n\n    var fullPath;\n    if (currentPath) {\n      fullPath = currentPath + \".\" + key;\n    } else {\n      fullPath = key;\n    }\n    if (typeof context[key] !== \"undefined\") {\n      genError(\"Cannot redefine existing key '\" + fullPath + \"'.\", line, column);\n    }\n\n    context[key] = reduceValueNode(value);\n\n    if (!pathAssigned(fullPath)) {\n      assignedPaths.push(fullPath);\n      valueAssignments.push(fullPath);\n    }\n  }\n\n\n  function pathAssigned(path) {\n    return assignedPaths.indexOf(path) !== -1;\n  }\n\n  function reduceValueNode(node) {\n    if (node.type === \"Array\") {\n      return reduceArrayWithTypeChecking(node.value);\n    } else if (node.type === \"InlineTable\") {\n      return reduceInlineTableNode(node.value);\n    } else {\n      return node.value;\n    }\n  }\n\n  function reduceInlineTableNode(values) {\n    var obj = Object.create(null);\n    for (var i = 0; i < values.length; i++) {\n      var val = values[i];\n      if (val.value.type === \"InlineTable\") {\n        obj[val.key] = reduceInlineTableNode(val.value.value);\n      } else if (val.type === \"InlineTableValue\") {\n        obj[val.key] = reduceValueNode(val.value);\n      }\n    }\n\n    return obj;\n  }\n\n  function setPath(node) {\n    var path = node.value;\n    var quotedPath = path.map(quoteDottedString).join(\".\");\n    var line = node.line;\n    var column = node.column;\n\n    if (pathAssigned(quotedPath)) {\n      genError(\"Cannot redefine existing key '\" + path + \"'.\", line, column);\n    }\n    assignedPaths.push(quotedPath);\n    context = deepRef(data, path, Object.create(null), line, column);\n    currentPath = path;\n  }\n\n  function addTableArray(node) {\n    var path = node.value;\n    var quotedPath = path.map(quoteDottedString).join(\".\");\n    var line = node.line;\n    var column = node.column;\n\n    if (!pathAssigned(quotedPath)) {\n      assignedPaths.push(quotedPath);\n    }\n    assignedPaths = assignedPaths.filter(function(p) {\n      return p.indexOf(quotedPath) !== 0;\n    });\n    assignedPaths.push(quotedPath);\n    context = deepRef(data, path, [], line, column);\n    currentPath = quotedPath;\n\n    if (context instanceof Array) {\n      var newObj = Object.create(null);\n      context.push(newObj);\n      context = newObj;\n    } else {\n      genError(\"Cannot redefine existing key '\" + path + \"'.\", line, column);\n    }\n  }\n\n  // Given a path 'a.b.c', create (as necessary) `start.a`,\n  // `start.a.b`, and `start.a.b.c`, assigning `value` to `start.a.b.c`.\n  // If `a` or `b` are arrays and have items in them, the last item in the\n  // array is used as the context for the next sub-path.\n  function deepRef(start, keys, value, line, column) {\n    var traversed = [];\n    var traversedPath = \"\";\n    var path = keys.join(\".\");\n    var ctx = start;\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      traversed.push(key);\n      traversedPath = traversed.join(\".\");\n      if (typeof ctx[key] === \"undefined\") {\n        if (i === keys.length - 1) {\n          ctx[key] = value;\n        } else {\n          ctx[key] = Object.create(null);\n        }\n      } else if (i !== keys.length - 1 && valueAssignments.indexOf(traversedPath) > -1) {\n        // already a non-object value at key, can't be used as part of a new path\n        genError(\"Cannot redefine existing key '\" + traversedPath + \"'.\", line, column);\n      }\n\n      ctx = ctx[key];\n      if (ctx instanceof Array && ctx.length && i < keys.length - 1) {\n        ctx = ctx[ctx.length - 1];\n      }\n    }\n\n    return ctx;\n  }\n\n  function reduceArrayWithTypeChecking(array) {\n    // Ensure that all items in the array are of the same type\n    var firstType = null;\n    for (var i = 0; i < array.length; i++) {\n      var node = array[i];\n      if (firstType === null) {\n        firstType = node.type;\n      } else {\n        if (node.type !== firstType) {\n          genError(\"Cannot add value of type \" + node.type + \" to array of type \" +\n            firstType + \".\", node.line, node.column);\n        }\n      }\n    }\n\n    // Recursively reduce array of nodes into array of the nodes' values\n    return array.map(reduceValueNode);\n  }\n\n  function quoteDottedString(str) {\n    if (str.indexOf(\".\") > -1) {\n      return \"\\\"\" + str + \"\\\"\";\n    } else {\n      return str;\n    }\n  }\n}\n\nmodule.exports = {\n  compile: compile\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/toml/lib/compiler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/toml/lib/parser.js":
/*!*****************************************!*\
  !*** ./node_modules/toml/lib/parser.js ***!
  \*****************************************/
/***/ ((module) => {

eval("module.exports = (function() {\n  /*\n   * Generated by PEG.js 0.8.0.\n   *\n   * http://pegjs.majda.cz/\n   */\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function SyntaxError(message, expected, found, offset, line, column) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.offset   = offset;\n    this.line     = line;\n    this.column   = column;\n\n    this.name     = \"SyntaxError\";\n  }\n\n  peg$subclass(SyntaxError, Error);\n\n  function parse(input) {\n    var options = arguments.length > 1 ? arguments[1] : {},\n\n        peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = [],\n        peg$c1 = function() { return nodes },\n        peg$c2 = peg$FAILED,\n        peg$c3 = \"#\",\n        peg$c4 = { type: \"literal\", value: \"#\", description: \"\\\"#\\\"\" },\n        peg$c5 = void 0,\n        peg$c6 = { type: \"any\", description: \"any character\" },\n        peg$c7 = \"[\",\n        peg$c8 = { type: \"literal\", value: \"[\", description: \"\\\"[\\\"\" },\n        peg$c9 = \"]\",\n        peg$c10 = { type: \"literal\", value: \"]\", description: \"\\\"]\\\"\" },\n        peg$c11 = function(name) { addNode(node('ObjectPath', name, line, column)) },\n        peg$c12 = function(name) { addNode(node('ArrayPath', name, line, column)) },\n        peg$c13 = function(parts, name) { return parts.concat(name) },\n        peg$c14 = function(name) { return [name] },\n        peg$c15 = function(name) { return name },\n        peg$c16 = \".\",\n        peg$c17 = { type: \"literal\", value: \".\", description: \"\\\".\\\"\" },\n        peg$c18 = \"=\",\n        peg$c19 = { type: \"literal\", value: \"=\", description: \"\\\"=\\\"\" },\n        peg$c20 = function(key, value) { addNode(node('Assign', value, line, column, key)) },\n        peg$c21 = function(chars) { return chars.join('') },\n        peg$c22 = function(node) { return node.value },\n        peg$c23 = \"\\\"\\\"\\\"\",\n        peg$c24 = { type: \"literal\", value: \"\\\"\\\"\\\"\", description: \"\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\"\" },\n        peg$c25 = null,\n        peg$c26 = function(chars) { return node('String', chars.join(''), line, column) },\n        peg$c27 = \"\\\"\",\n        peg$c28 = { type: \"literal\", value: \"\\\"\", description: \"\\\"\\\\\\\"\\\"\" },\n        peg$c29 = \"'''\",\n        peg$c30 = { type: \"literal\", value: \"'''\", description: \"\\\"'''\\\"\" },\n        peg$c31 = \"'\",\n        peg$c32 = { type: \"literal\", value: \"'\", description: \"\\\"'\\\"\" },\n        peg$c33 = function(char) { return char },\n        peg$c34 = function(char) { return char},\n        peg$c35 = \"\\\\\",\n        peg$c36 = { type: \"literal\", value: \"\\\\\", description: \"\\\"\\\\\\\\\\\"\" },\n        peg$c37 = function() { return '' },\n        peg$c38 = \"e\",\n        peg$c39 = { type: \"literal\", value: \"e\", description: \"\\\"e\\\"\" },\n        peg$c40 = \"E\",\n        peg$c41 = { type: \"literal\", value: \"E\", description: \"\\\"E\\\"\" },\n        peg$c42 = function(left, right) { return node('Float', parseFloat(left + 'e' + right), line, column) },\n        peg$c43 = function(text) { return node('Float', parseFloat(text), line, column) },\n        peg$c44 = \"+\",\n        peg$c45 = { type: \"literal\", value: \"+\", description: \"\\\"+\\\"\" },\n        peg$c46 = function(digits) { return digits.join('') },\n        peg$c47 = \"-\",\n        peg$c48 = { type: \"literal\", value: \"-\", description: \"\\\"-\\\"\" },\n        peg$c49 = function(digits) { return '-' + digits.join('') },\n        peg$c50 = function(text) { return node('Integer', parseInt(text, 10), line, column) },\n        peg$c51 = \"true\",\n        peg$c52 = { type: \"literal\", value: \"true\", description: \"\\\"true\\\"\" },\n        peg$c53 = function() { return node('Boolean', true, line, column) },\n        peg$c54 = \"false\",\n        peg$c55 = { type: \"literal\", value: \"false\", description: \"\\\"false\\\"\" },\n        peg$c56 = function() { return node('Boolean', false, line, column) },\n        peg$c57 = function() { return node('Array', [], line, column) },\n        peg$c58 = function(value) { return node('Array', value ? [value] : [], line, column) },\n        peg$c59 = function(values) { return node('Array', values, line, column) },\n        peg$c60 = function(values, value) { return node('Array', values.concat(value), line, column) },\n        peg$c61 = function(value) { return value },\n        peg$c62 = \",\",\n        peg$c63 = { type: \"literal\", value: \",\", description: \"\\\",\\\"\" },\n        peg$c64 = \"{\",\n        peg$c65 = { type: \"literal\", value: \"{\", description: \"\\\"{\\\"\" },\n        peg$c66 = \"}\",\n        peg$c67 = { type: \"literal\", value: \"}\", description: \"\\\"}\\\"\" },\n        peg$c68 = function(values) { return node('InlineTable', values, line, column) },\n        peg$c69 = function(key, value) { return node('InlineTableValue', value, line, column, key) },\n        peg$c70 = function(digits) { return \".\" + digits },\n        peg$c71 = function(date) { return  date.join('') },\n        peg$c72 = \":\",\n        peg$c73 = { type: \"literal\", value: \":\", description: \"\\\":\\\"\" },\n        peg$c74 = function(time) { return time.join('') },\n        peg$c75 = \"T\",\n        peg$c76 = { type: \"literal\", value: \"T\", description: \"\\\"T\\\"\" },\n        peg$c77 = \"Z\",\n        peg$c78 = { type: \"literal\", value: \"Z\", description: \"\\\"Z\\\"\" },\n        peg$c79 = function(date, time) { return node('Date', new Date(date + \"T\" + time + \"Z\"), line, column) },\n        peg$c80 = function(date, time) { return node('Date', new Date(date + \"T\" + time), line, column) },\n        peg$c81 = /^[ \\t]/,\n        peg$c82 = { type: \"class\", value: \"[ \\\\t]\", description: \"[ \\\\t]\" },\n        peg$c83 = \"\\n\",\n        peg$c84 = { type: \"literal\", value: \"\\n\", description: \"\\\"\\\\n\\\"\" },\n        peg$c85 = \"\\r\",\n        peg$c86 = { type: \"literal\", value: \"\\r\", description: \"\\\"\\\\r\\\"\" },\n        peg$c87 = /^[0-9a-f]/i,\n        peg$c88 = { type: \"class\", value: \"[0-9a-f]i\", description: \"[0-9a-f]i\" },\n        peg$c89 = /^[0-9]/,\n        peg$c90 = { type: \"class\", value: \"[0-9]\", description: \"[0-9]\" },\n        peg$c91 = \"_\",\n        peg$c92 = { type: \"literal\", value: \"_\", description: \"\\\"_\\\"\" },\n        peg$c93 = function() { return \"\" },\n        peg$c94 = /^[A-Za-z0-9_\\-]/,\n        peg$c95 = { type: \"class\", value: \"[A-Za-z0-9_\\\\-]\", description: \"[A-Za-z0-9_\\\\-]\" },\n        peg$c96 = function(d) { return d.join('') },\n        peg$c97 = \"\\\\\\\"\",\n        peg$c98 = { type: \"literal\", value: \"\\\\\\\"\", description: \"\\\"\\\\\\\\\\\\\\\"\\\"\" },\n        peg$c99 = function() { return '\"'  },\n        peg$c100 = \"\\\\\\\\\",\n        peg$c101 = { type: \"literal\", value: \"\\\\\\\\\", description: \"\\\"\\\\\\\\\\\\\\\\\\\"\" },\n        peg$c102 = function() { return '\\\\' },\n        peg$c103 = \"\\\\b\",\n        peg$c104 = { type: \"literal\", value: \"\\\\b\", description: \"\\\"\\\\\\\\b\\\"\" },\n        peg$c105 = function() { return '\\b' },\n        peg$c106 = \"\\\\t\",\n        peg$c107 = { type: \"literal\", value: \"\\\\t\", description: \"\\\"\\\\\\\\t\\\"\" },\n        peg$c108 = function() { return '\\t' },\n        peg$c109 = \"\\\\n\",\n        peg$c110 = { type: \"literal\", value: \"\\\\n\", description: \"\\\"\\\\\\\\n\\\"\" },\n        peg$c111 = function() { return '\\n' },\n        peg$c112 = \"\\\\f\",\n        peg$c113 = { type: \"literal\", value: \"\\\\f\", description: \"\\\"\\\\\\\\f\\\"\" },\n        peg$c114 = function() { return '\\f' },\n        peg$c115 = \"\\\\r\",\n        peg$c116 = { type: \"literal\", value: \"\\\\r\", description: \"\\\"\\\\\\\\r\\\"\" },\n        peg$c117 = function() { return '\\r' },\n        peg$c118 = \"\\\\U\",\n        peg$c119 = { type: \"literal\", value: \"\\\\U\", description: \"\\\"\\\\\\\\U\\\"\" },\n        peg$c120 = function(digits) { return convertCodePoint(digits.join('')) },\n        peg$c121 = \"\\\\u\",\n        peg$c122 = { type: \"literal\", value: \"\\\\u\", description: \"\\\"\\\\\\\\u\\\"\" },\n\n        peg$currPos          = 0,\n        peg$reportedPos      = 0,\n        peg$cachedPos        = 0,\n        peg$cachedPosDetails = { line: 1, column: 1, seenCR: false },\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$cache = {},\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$reportedPos, peg$currPos);\n    }\n\n    function offset() {\n      return peg$reportedPos;\n    }\n\n    function line() {\n      return peg$computePosDetails(peg$reportedPos).line;\n    }\n\n    function column() {\n      return peg$computePosDetails(peg$reportedPos).column;\n    }\n\n    function expected(description) {\n      throw peg$buildException(\n        null,\n        [{ type: \"other\", description: description }],\n        peg$reportedPos\n      );\n    }\n\n    function error(message) {\n      throw peg$buildException(message, null, peg$reportedPos);\n    }\n\n    function peg$computePosDetails(pos) {\n      function advance(details, startPos, endPos) {\n        var p, ch;\n\n        for (p = startPos; p < endPos; p++) {\n          ch = input.charAt(p);\n          if (ch === \"\\n\") {\n            if (!details.seenCR) { details.line++; }\n            details.column = 1;\n            details.seenCR = false;\n          } else if (ch === \"\\r\" || ch === \"\\u2028\" || ch === \"\\u2029\") {\n            details.line++;\n            details.column = 1;\n            details.seenCR = true;\n          } else {\n            details.column++;\n            details.seenCR = false;\n          }\n        }\n      }\n\n      if (peg$cachedPos !== pos) {\n        if (peg$cachedPos > pos) {\n          peg$cachedPos = 0;\n          peg$cachedPosDetails = { line: 1, column: 1, seenCR: false };\n        }\n        advance(peg$cachedPosDetails, peg$cachedPos, pos);\n        peg$cachedPos = pos;\n      }\n\n      return peg$cachedPosDetails;\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildException(message, expected, pos) {\n      function cleanupExpected(expected) {\n        var i = 1;\n\n        expected.sort(function(a, b) {\n          if (a.description < b.description) {\n            return -1;\n          } else if (a.description > b.description) {\n            return 1;\n          } else {\n            return 0;\n          }\n        });\n\n        while (i < expected.length) {\n          if (expected[i - 1] === expected[i]) {\n            expected.splice(i, 1);\n          } else {\n            i++;\n          }\n        }\n      }\n\n      function buildMessage(expected, found) {\n        function stringEscape(s) {\n          function hex(ch) { return ch.charCodeAt(0).toString(16).toUpperCase(); }\n\n          return s\n            .replace(/\\\\/g,   '\\\\\\\\')\n            .replace(/\"/g,    '\\\\\"')\n            .replace(/\\x08/g, '\\\\b')\n            .replace(/\\t/g,   '\\\\t')\n            .replace(/\\n/g,   '\\\\n')\n            .replace(/\\f/g,   '\\\\f')\n            .replace(/\\r/g,   '\\\\r')\n            .replace(/[\\x00-\\x07\\x0B\\x0E\\x0F]/g, function(ch) { return '\\\\x0' + hex(ch); })\n            .replace(/[\\x10-\\x1F\\x80-\\xFF]/g,    function(ch) { return '\\\\x'  + hex(ch); })\n            .replace(/[\\u0180-\\u0FFF]/g,         function(ch) { return '\\\\u0' + hex(ch); })\n            .replace(/[\\u1080-\\uFFFF]/g,         function(ch) { return '\\\\u'  + hex(ch); });\n        }\n\n        var expectedDescs = new Array(expected.length),\n            expectedDesc, foundDesc, i;\n\n        for (i = 0; i < expected.length; i++) {\n          expectedDescs[i] = expected[i].description;\n        }\n\n        expectedDesc = expected.length > 1\n          ? expectedDescs.slice(0, -1).join(\", \")\n              + \" or \"\n              + expectedDescs[expected.length - 1]\n          : expectedDescs[0];\n\n        foundDesc = found ? \"\\\"\" + stringEscape(found) + \"\\\"\" : \"end of input\";\n\n        return \"Expected \" + expectedDesc + \" but \" + foundDesc + \" found.\";\n      }\n\n      var posDetails = peg$computePosDetails(pos),\n          found      = pos < input.length ? input.charAt(pos) : null;\n\n      if (expected !== null) {\n        cleanupExpected(expected);\n      }\n\n      return new SyntaxError(\n        message !== null ? message : buildMessage(expected, found),\n        expected,\n        found,\n        pos,\n        posDetails.line,\n        posDetails.column\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 0,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseline();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parseline();\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c1();\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseline() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 49 + 1,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseS();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parseS();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseexpression();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseS();\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = [];\n            s5 = peg$parsecomment();\n            while (s5 !== peg$FAILED) {\n              s4.push(s5);\n              s5 = peg$parsecomment();\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parseNL();\n              if (s6 !== peg$FAILED) {\n                while (s6 !== peg$FAILED) {\n                  s5.push(s6);\n                  s6 = peg$parseNL();\n                }\n              } else {\n                s5 = peg$c2;\n              }\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseEOF();\n              }\n              if (s5 !== peg$FAILED) {\n                s1 = [s1, s2, s3, s4, s5];\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = [];\n        s2 = peg$parseS();\n        if (s2 !== peg$FAILED) {\n          while (s2 !== peg$FAILED) {\n            s1.push(s2);\n            s2 = peg$parseS();\n          }\n        } else {\n          s1 = peg$c2;\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          s3 = peg$parseNL();\n          if (s3 !== peg$FAILED) {\n            while (s3 !== peg$FAILED) {\n              s2.push(s3);\n              s3 = peg$parseNL();\n            }\n          } else {\n            s2 = peg$c2;\n          }\n          if (s2 === peg$FAILED) {\n            s2 = peg$parseEOF();\n          }\n          if (s2 !== peg$FAILED) {\n            s1 = [s1, s2];\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseNL();\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseexpression() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 2,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parsecomment();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsepath();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsetablearray();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseassignment();\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsecomment() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 49 + 3,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c3;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c4); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$currPos;\n        peg$silentFails++;\n        s5 = peg$parseNL();\n        if (s5 === peg$FAILED) {\n          s5 = peg$parseEOF();\n        }\n        peg$silentFails--;\n        if (s5 === peg$FAILED) {\n          s4 = peg$c5;\n        } else {\n          peg$currPos = s4;\n          s4 = peg$c2;\n        }\n        if (s4 !== peg$FAILED) {\n          if (input.length > peg$currPos) {\n            s5 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c6); }\n          }\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$c2;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$c2;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$currPos;\n          peg$silentFails++;\n          s5 = peg$parseNL();\n          if (s5 === peg$FAILED) {\n            s5 = peg$parseEOF();\n          }\n          peg$silentFails--;\n          if (s5 === peg$FAILED) {\n            s4 = peg$c5;\n          } else {\n            peg$currPos = s4;\n            s4 = peg$c2;\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c6); }\n            }\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$c2;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$c2;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          s1 = [s1, s2];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 49 + 4,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c7;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c8); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseS();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parseS();\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parsetable_key();\n          if (s3 !== peg$FAILED) {\n            s4 = [];\n            s5 = peg$parseS();\n            while (s5 !== peg$FAILED) {\n              s4.push(s5);\n              s5 = peg$parseS();\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c9;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c10); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c11(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetablearray() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 49 + 5,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c7;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c8); }\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 91) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseS();\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parsetable_key();\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parseS();\n              while (s6 !== peg$FAILED) {\n                s5.push(s6);\n                s6 = peg$parseS();\n              }\n              if (s5 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 93) {\n                  s6 = peg$c9;\n                  peg$currPos++;\n                } else {\n                  s6 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c10); }\n                }\n                if (s6 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 93) {\n                    s7 = peg$c9;\n                    peg$currPos++;\n                  } else {\n                    s7 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c10); }\n                  }\n                  if (s7 !== peg$FAILED) {\n                    peg$reportedPos = s0;\n                    s1 = peg$c12(s4);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetable_key() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 6,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsedot_ended_table_key_part();\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parsedot_ended_table_key_part();\n        }\n      } else {\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsetable_key_part();\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c13(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsetable_key_part();\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c14(s1);\n        }\n        s0 = s1;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetable_key_part() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 7,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseS();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parseS();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsekey();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseS();\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c15(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = [];\n        s2 = peg$parseS();\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseS();\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parsequoted_key();\n          if (s2 !== peg$FAILED) {\n            s3 = [];\n            s4 = peg$parseS();\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              s4 = peg$parseS();\n            }\n            if (s3 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c15(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsedot_ended_table_key_part() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 49 + 8,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseS();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parseS();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsekey();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseS();\n          }\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s4 = peg$c16;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c17); }\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parseS();\n              while (s6 !== peg$FAILED) {\n                s5.push(s6);\n                s6 = peg$parseS();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c15(s2);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = [];\n        s2 = peg$parseS();\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseS();\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parsequoted_key();\n          if (s2 !== peg$FAILED) {\n            s3 = [];\n            s4 = peg$parseS();\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              s4 = peg$parseS();\n            }\n            if (s3 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 46) {\n                s4 = peg$c16;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c17); }\n              }\n              if (s4 !== peg$FAILED) {\n                s5 = [];\n                s6 = peg$parseS();\n                while (s6 !== peg$FAILED) {\n                  s5.push(s6);\n                  s6 = peg$parseS();\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$reportedPos = s0;\n                  s1 = peg$c15(s2);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseassignment() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 49 + 9,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsekey();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseS();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parseS();\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 61) {\n            s3 = peg$c18;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c19); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = [];\n            s5 = peg$parseS();\n            while (s5 !== peg$FAILED) {\n              s4.push(s5);\n              s5 = peg$parseS();\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsevalue();\n              if (s5 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c20(s1, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsequoted_key();\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          s3 = peg$parseS();\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseS();\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 61) {\n              s3 = peg$c18;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c19); }\n            }\n            if (s3 !== peg$FAILED) {\n              s4 = [];\n              s5 = peg$parseS();\n              while (s5 !== peg$FAILED) {\n                s4.push(s5);\n                s5 = peg$parseS();\n              }\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsevalue();\n                if (s5 !== peg$FAILED) {\n                  peg$reportedPos = s0;\n                  s1 = peg$c20(s1, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsekey() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 10,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseASCII_BASIC();\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseASCII_BASIC();\n        }\n      } else {\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c21(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsequoted_key() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 11,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsedouble_quoted_single_line_string();\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c22(s1);\n      }\n      s0 = s1;\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsesingle_quoted_single_line_string();\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c22(s1);\n        }\n        s0 = s1;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsevalue() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 12,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parsestring();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsedatetime();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsefloat();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseinteger();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parseboolean();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsearray();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parseinline_table();\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 13,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parsedouble_quoted_multiline_string();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsedouble_quoted_single_line_string();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsesingle_quoted_multiline_string();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsesingle_quoted_single_line_string();\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsedouble_quoted_multiline_string() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 14,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 3) === peg$c23) {\n        s1 = peg$c23;\n        peg$currPos += 3;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c24); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseNL();\n        if (s2 === peg$FAILED) {\n          s2 = peg$c25;\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parsemultiline_string_char();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parsemultiline_string_char();\n          }\n          if (s3 !== peg$FAILED) {\n            if (input.substr(peg$currPos, 3) === peg$c23) {\n              s4 = peg$c23;\n              peg$currPos += 3;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c24); }\n            }\n            if (s4 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c26(s3);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsedouble_quoted_single_line_string() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 49 + 15,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c27;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c28); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parsestring_char();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parsestring_char();\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c27;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c28); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c26(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesingle_quoted_multiline_string() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 16,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 3) === peg$c29) {\n        s1 = peg$c29;\n        peg$currPos += 3;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c30); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseNL();\n        if (s2 === peg$FAILED) {\n          s2 = peg$c25;\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parsemultiline_literal_char();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parsemultiline_literal_char();\n          }\n          if (s3 !== peg$FAILED) {\n            if (input.substr(peg$currPos, 3) === peg$c29) {\n              s4 = peg$c29;\n              peg$currPos += 3;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c30); }\n            }\n            if (s4 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c26(s3);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesingle_quoted_single_line_string() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 49 + 17,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 39) {\n        s1 = peg$c31;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c32); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseliteral_char();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parseliteral_char();\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 39) {\n            s3 = peg$c31;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c32); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c26(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring_char() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 18,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parseESCAPED();\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$currPos;\n        peg$silentFails++;\n        if (input.charCodeAt(peg$currPos) === 34) {\n          s2 = peg$c27;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c28); }\n        }\n        peg$silentFails--;\n        if (s2 === peg$FAILED) {\n          s1 = peg$c5;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$c2;\n        }\n        if (s1 !== peg$FAILED) {\n          if (input.length > peg$currPos) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c6); }\n          }\n          if (s2 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c33(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseliteral_char() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 19,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      peg$silentFails++;\n      if (input.charCodeAt(peg$currPos) === 39) {\n        s2 = peg$c31;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c32); }\n      }\n      peg$silentFails--;\n      if (s2 === peg$FAILED) {\n        s1 = peg$c5;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.length > peg$currPos) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c6); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c33(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsemultiline_string_char() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 20,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parseESCAPED();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsemultiline_string_delim();\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$currPos;\n          peg$silentFails++;\n          if (input.substr(peg$currPos, 3) === peg$c23) {\n            s2 = peg$c23;\n            peg$currPos += 3;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c24); }\n          }\n          peg$silentFails--;\n          if (s2 === peg$FAILED) {\n            s1 = peg$c5;\n          } else {\n            peg$currPos = s1;\n            s1 = peg$c2;\n          }\n          if (s1 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s2 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c6); }\n            }\n            if (s2 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c34(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsemultiline_string_delim() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 21,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 92) {\n        s1 = peg$c35;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c36); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseNL();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseNLS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseNLS();\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c37();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsemultiline_literal_char() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 22,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      peg$silentFails++;\n      if (input.substr(peg$currPos, 3) === peg$c29) {\n        s2 = peg$c29;\n        peg$currPos += 3;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c30); }\n      }\n      peg$silentFails--;\n      if (s2 === peg$FAILED) {\n        s1 = peg$c5;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.length > peg$currPos) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c6); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c33(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefloat() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 49 + 23,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsefloat_text();\n      if (s1 === peg$FAILED) {\n        s1 = peg$parseinteger_text();\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 101) {\n          s2 = peg$c38;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c39); }\n        }\n        if (s2 === peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 69) {\n            s2 = peg$c40;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c41); }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseinteger_text();\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c42(s1, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsefloat_text();\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c43(s1);\n        }\n        s0 = s1;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefloat_text() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 49 + 24,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 43) {\n        s1 = peg$c44;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c45); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = peg$c25;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$currPos;\n        s3 = peg$parseDIGITS();\n        if (s3 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c16;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c17); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseDIGITS();\n            if (s5 !== peg$FAILED) {\n              s3 = [s3, s4, s5];\n              s2 = s3;\n            } else {\n              peg$currPos = s2;\n              s2 = peg$c2;\n            }\n          } else {\n            peg$currPos = s2;\n            s2 = peg$c2;\n          }\n        } else {\n          peg$currPos = s2;\n          s2 = peg$c2;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c46(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 45) {\n          s1 = peg$c47;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c48); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$currPos;\n          s3 = peg$parseDIGITS();\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s4 = peg$c16;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c17); }\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parseDIGITS();\n              if (s5 !== peg$FAILED) {\n                s3 = [s3, s4, s5];\n                s2 = s3;\n              } else {\n                peg$currPos = s2;\n                s2 = peg$c2;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$c2;\n            }\n          } else {\n            peg$currPos = s2;\n            s2 = peg$c2;\n          }\n          if (s2 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c49(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseinteger() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 25,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseinteger_text();\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c50(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseinteger_text() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 26,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 43) {\n        s1 = peg$c44;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c45); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = peg$c25;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseDIGIT_OR_UNDER();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseDIGIT_OR_UNDER();\n          }\n        } else {\n          s2 = peg$c2;\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$currPos;\n          peg$silentFails++;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c16;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c17); }\n          }\n          peg$silentFails--;\n          if (s4 === peg$FAILED) {\n            s3 = peg$c5;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$c2;\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c46(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 45) {\n          s1 = peg$c47;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c48); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          s3 = peg$parseDIGIT_OR_UNDER();\n          if (s3 !== peg$FAILED) {\n            while (s3 !== peg$FAILED) {\n              s2.push(s3);\n              s3 = peg$parseDIGIT_OR_UNDER();\n            }\n          } else {\n            s2 = peg$c2;\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$currPos;\n            peg$silentFails++;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s4 = peg$c16;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c17); }\n            }\n            peg$silentFails--;\n            if (s4 === peg$FAILED) {\n              s3 = peg$c5;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$c2;\n            }\n            if (s3 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c49(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseboolean() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 27,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 4) === peg$c51) {\n        s1 = peg$c51;\n        peg$currPos += 4;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c52); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c53();\n      }\n      s0 = s1;\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.substr(peg$currPos, 5) === peg$c54) {\n          s1 = peg$c54;\n          peg$currPos += 5;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c55); }\n        }\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c56();\n        }\n        s0 = s1;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsearray() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 28,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c7;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c8); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parsearray_sep();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parsearray_sep();\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 93) {\n            s3 = peg$c9;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c10); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c57();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 91) {\n          s1 = peg$c7;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parsearray_value();\n          if (s2 === peg$FAILED) {\n            s2 = peg$c25;\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 93) {\n              s3 = peg$c9;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c10); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c58(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 91) {\n            s1 = peg$c7;\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c8); }\n          }\n          if (s1 !== peg$FAILED) {\n            s2 = [];\n            s3 = peg$parsearray_value_list();\n            if (s3 !== peg$FAILED) {\n              while (s3 !== peg$FAILED) {\n                s2.push(s3);\n                s3 = peg$parsearray_value_list();\n              }\n            } else {\n              s2 = peg$c2;\n            }\n            if (s2 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s3 = peg$c9;\n                peg$currPos++;\n              } else {\n                s3 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c10); }\n              }\n              if (s3 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c59(s2);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 91) {\n              s1 = peg$c7;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c8); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = [];\n              s3 = peg$parsearray_value_list();\n              if (s3 !== peg$FAILED) {\n                while (s3 !== peg$FAILED) {\n                  s2.push(s3);\n                  s3 = peg$parsearray_value_list();\n                }\n              } else {\n                s2 = peg$c2;\n              }\n              if (s2 !== peg$FAILED) {\n                s3 = peg$parsearray_value();\n                if (s3 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 93) {\n                    s4 = peg$c9;\n                    peg$currPos++;\n                  } else {\n                    s4 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c10); }\n                  }\n                  if (s4 !== peg$FAILED) {\n                    peg$reportedPos = s0;\n                    s1 = peg$c60(s2, s3);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsearray_value() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 29,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsearray_sep();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsearray_sep();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsevalue();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parsearray_sep();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parsearray_sep();\n          }\n          if (s3 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c61(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsearray_value_list() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 49 + 30,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsearray_sep();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsearray_sep();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsevalue();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parsearray_sep();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parsearray_sep();\n          }\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s4 = peg$c62;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c63); }\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parsearray_sep();\n              while (s6 !== peg$FAILED) {\n                s5.push(s6);\n                s6 = peg$parsearray_sep();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c61(s2);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsearray_sep() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 31,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parseS();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseNL();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsecomment();\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseinline_table() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 49 + 32,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 123) {\n        s1 = peg$c64;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c65); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseS();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parseS();\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseinline_table_assignment();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseinline_table_assignment();\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = [];\n            s5 = peg$parseS();\n            while (s5 !== peg$FAILED) {\n              s4.push(s5);\n              s5 = peg$parseS();\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 125) {\n                s5 = peg$c66;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c67); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c68(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseinline_table_assignment() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10;\n\n      var key    = peg$currPos * 49 + 33,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseS();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parseS();\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parsekey();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseS();\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$parseS();\n          }\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 61) {\n              s4 = peg$c18;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c19); }\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parseS();\n              while (s6 !== peg$FAILED) {\n                s5.push(s6);\n                s6 = peg$parseS();\n              }\n              if (s5 !== peg$FAILED) {\n                s6 = peg$parsevalue();\n                if (s6 !== peg$FAILED) {\n                  s7 = [];\n                  s8 = peg$parseS();\n                  while (s8 !== peg$FAILED) {\n                    s7.push(s8);\n                    s8 = peg$parseS();\n                  }\n                  if (s7 !== peg$FAILED) {\n                    if (input.charCodeAt(peg$currPos) === 44) {\n                      s8 = peg$c62;\n                      peg$currPos++;\n                    } else {\n                      s8 = peg$FAILED;\n                      if (peg$silentFails === 0) { peg$fail(peg$c63); }\n                    }\n                    if (s8 !== peg$FAILED) {\n                      s9 = [];\n                      s10 = peg$parseS();\n                      while (s10 !== peg$FAILED) {\n                        s9.push(s10);\n                        s10 = peg$parseS();\n                      }\n                      if (s9 !== peg$FAILED) {\n                        peg$reportedPos = s0;\n                        s1 = peg$c69(s2, s6);\n                        s0 = s1;\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$c2;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$c2;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = [];\n        s2 = peg$parseS();\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseS();\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parsekey();\n          if (s2 !== peg$FAILED) {\n            s3 = [];\n            s4 = peg$parseS();\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              s4 = peg$parseS();\n            }\n            if (s3 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 61) {\n                s4 = peg$c18;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c19); }\n              }\n              if (s4 !== peg$FAILED) {\n                s5 = [];\n                s6 = peg$parseS();\n                while (s6 !== peg$FAILED) {\n                  s5.push(s6);\n                  s6 = peg$parseS();\n                }\n                if (s5 !== peg$FAILED) {\n                  s6 = peg$parsevalue();\n                  if (s6 !== peg$FAILED) {\n                    peg$reportedPos = s0;\n                    s1 = peg$c69(s2, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$c2;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$c2;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesecfragment() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 34,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c16;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c17); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseDIGITS();\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c70(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsedate() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11;\n\n      var key    = peg$currPos * 49 + 35,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parseDIGIT_OR_UNDER();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseDIGIT_OR_UNDER();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parseDIGIT_OR_UNDER();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseDIGIT_OR_UNDER();\n            if (s5 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 45) {\n                s6 = peg$c47;\n                peg$currPos++;\n              } else {\n                s6 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c48); }\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseDIGIT_OR_UNDER();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parseDIGIT_OR_UNDER();\n                  if (s8 !== peg$FAILED) {\n                    if (input.charCodeAt(peg$currPos) === 45) {\n                      s9 = peg$c47;\n                      peg$currPos++;\n                    } else {\n                      s9 = peg$FAILED;\n                      if (peg$silentFails === 0) { peg$fail(peg$c48); }\n                    }\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parseDIGIT_OR_UNDER();\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parseDIGIT_OR_UNDER();\n                        if (s11 !== peg$FAILED) {\n                          s2 = [s2, s3, s4, s5, s6, s7, s8, s9, s10, s11];\n                          s1 = s2;\n                        } else {\n                          peg$currPos = s1;\n                          s1 = peg$c2;\n                        }\n                      } else {\n                        peg$currPos = s1;\n                        s1 = peg$c2;\n                      }\n                    } else {\n                      peg$currPos = s1;\n                      s1 = peg$c2;\n                    }\n                  } else {\n                    peg$currPos = s1;\n                    s1 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s1;\n                  s1 = peg$c2;\n                }\n              } else {\n                peg$currPos = s1;\n                s1 = peg$c2;\n              }\n            } else {\n              peg$currPos = s1;\n              s1 = peg$c2;\n            }\n          } else {\n            peg$currPos = s1;\n            s1 = peg$c2;\n          }\n        } else {\n          peg$currPos = s1;\n          s1 = peg$c2;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c71(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetime() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10;\n\n      var key    = peg$currPos * 49 + 36,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parseDIGIT_OR_UNDER();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseDIGIT_OR_UNDER();\n        if (s3 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 58) {\n            s4 = peg$c72;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c73); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseDIGIT_OR_UNDER();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseDIGIT_OR_UNDER();\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 58) {\n                  s7 = peg$c72;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c73); }\n                }\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parseDIGIT_OR_UNDER();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parseDIGIT_OR_UNDER();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsesecfragment();\n                      if (s10 === peg$FAILED) {\n                        s10 = peg$c25;\n                      }\n                      if (s10 !== peg$FAILED) {\n                        s2 = [s2, s3, s4, s5, s6, s7, s8, s9, s10];\n                        s1 = s2;\n                      } else {\n                        peg$currPos = s1;\n                        s1 = peg$c2;\n                      }\n                    } else {\n                      peg$currPos = s1;\n                      s1 = peg$c2;\n                    }\n                  } else {\n                    peg$currPos = s1;\n                    s1 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s1;\n                  s1 = peg$c2;\n                }\n              } else {\n                peg$currPos = s1;\n                s1 = peg$c2;\n              }\n            } else {\n              peg$currPos = s1;\n              s1 = peg$c2;\n            }\n          } else {\n            peg$currPos = s1;\n            s1 = peg$c2;\n          }\n        } else {\n          peg$currPos = s1;\n          s1 = peg$c2;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c74(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetime_with_offset() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16;\n\n      var key    = peg$currPos * 49 + 37,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parseDIGIT_OR_UNDER();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseDIGIT_OR_UNDER();\n        if (s3 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 58) {\n            s4 = peg$c72;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c73); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseDIGIT_OR_UNDER();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseDIGIT_OR_UNDER();\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 58) {\n                  s7 = peg$c72;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c73); }\n                }\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parseDIGIT_OR_UNDER();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parseDIGIT_OR_UNDER();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsesecfragment();\n                      if (s10 === peg$FAILED) {\n                        s10 = peg$c25;\n                      }\n                      if (s10 !== peg$FAILED) {\n                        if (input.charCodeAt(peg$currPos) === 45) {\n                          s11 = peg$c47;\n                          peg$currPos++;\n                        } else {\n                          s11 = peg$FAILED;\n                          if (peg$silentFails === 0) { peg$fail(peg$c48); }\n                        }\n                        if (s11 === peg$FAILED) {\n                          if (input.charCodeAt(peg$currPos) === 43) {\n                            s11 = peg$c44;\n                            peg$currPos++;\n                          } else {\n                            s11 = peg$FAILED;\n                            if (peg$silentFails === 0) { peg$fail(peg$c45); }\n                          }\n                        }\n                        if (s11 !== peg$FAILED) {\n                          s12 = peg$parseDIGIT_OR_UNDER();\n                          if (s12 !== peg$FAILED) {\n                            s13 = peg$parseDIGIT_OR_UNDER();\n                            if (s13 !== peg$FAILED) {\n                              if (input.charCodeAt(peg$currPos) === 58) {\n                                s14 = peg$c72;\n                                peg$currPos++;\n                              } else {\n                                s14 = peg$FAILED;\n                                if (peg$silentFails === 0) { peg$fail(peg$c73); }\n                              }\n                              if (s14 !== peg$FAILED) {\n                                s15 = peg$parseDIGIT_OR_UNDER();\n                                if (s15 !== peg$FAILED) {\n                                  s16 = peg$parseDIGIT_OR_UNDER();\n                                  if (s16 !== peg$FAILED) {\n                                    s2 = [s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16];\n                                    s1 = s2;\n                                  } else {\n                                    peg$currPos = s1;\n                                    s1 = peg$c2;\n                                  }\n                                } else {\n                                  peg$currPos = s1;\n                                  s1 = peg$c2;\n                                }\n                              } else {\n                                peg$currPos = s1;\n                                s1 = peg$c2;\n                              }\n                            } else {\n                              peg$currPos = s1;\n                              s1 = peg$c2;\n                            }\n                          } else {\n                            peg$currPos = s1;\n                            s1 = peg$c2;\n                          }\n                        } else {\n                          peg$currPos = s1;\n                          s1 = peg$c2;\n                        }\n                      } else {\n                        peg$currPos = s1;\n                        s1 = peg$c2;\n                      }\n                    } else {\n                      peg$currPos = s1;\n                      s1 = peg$c2;\n                    }\n                  } else {\n                    peg$currPos = s1;\n                    s1 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s1;\n                  s1 = peg$c2;\n                }\n              } else {\n                peg$currPos = s1;\n                s1 = peg$c2;\n              }\n            } else {\n              peg$currPos = s1;\n              s1 = peg$c2;\n            }\n          } else {\n            peg$currPos = s1;\n            s1 = peg$c2;\n          }\n        } else {\n          peg$currPos = s1;\n          s1 = peg$c2;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c74(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsedatetime() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 49 + 38,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsedate();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 84) {\n          s2 = peg$c75;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c76); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parsetime();\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 90) {\n              s4 = peg$c77;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c78); }\n            }\n            if (s4 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c79(s1, s3);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsedate();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 84) {\n            s2 = peg$c75;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c76); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parsetime_with_offset();\n            if (s3 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c80(s1, s3);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$c2;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseS() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 39,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      if (peg$c81.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c82); }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseNL() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 40,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      if (input.charCodeAt(peg$currPos) === 10) {\n        s0 = peg$c83;\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c84); }\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 13) {\n          s1 = peg$c85;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c86); }\n        }\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 10) {\n            s2 = peg$c83;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c84); }\n          }\n          if (s2 !== peg$FAILED) {\n            s1 = [s1, s2];\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseNLS() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 41,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$parseNL();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseS();\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseEOF() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 42,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      peg$silentFails++;\n      if (input.length > peg$currPos) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c6); }\n      }\n      peg$silentFails--;\n      if (s1 === peg$FAILED) {\n        s0 = peg$c5;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseHEX() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 43,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      if (peg$c87.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c88); }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseDIGIT_OR_UNDER() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 44,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      if (peg$c89.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c90); }\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 95) {\n          s1 = peg$c91;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c92); }\n        }\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c93();\n        }\n        s0 = s1;\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseASCII_BASIC() {\n      var s0;\n\n      var key    = peg$currPos * 49 + 45,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      if (peg$c94.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c95); }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseDIGITS() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 49 + 46,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseDIGIT_OR_UNDER();\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseDIGIT_OR_UNDER();\n        }\n      } else {\n        s1 = peg$c2;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c96(s1);\n      }\n      s0 = s1;\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseESCAPED() {\n      var s0, s1;\n\n      var key    = peg$currPos * 49 + 47,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 2) === peg$c97) {\n        s1 = peg$c97;\n        peg$currPos += 2;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c98); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$reportedPos = s0;\n        s1 = peg$c99();\n      }\n      s0 = s1;\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.substr(peg$currPos, 2) === peg$c100) {\n          s1 = peg$c100;\n          peg$currPos += 2;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c101); }\n        }\n        if (s1 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c102();\n        }\n        s0 = s1;\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          if (input.substr(peg$currPos, 2) === peg$c103) {\n            s1 = peg$c103;\n            peg$currPos += 2;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c104); }\n          }\n          if (s1 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c105();\n          }\n          s0 = s1;\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.substr(peg$currPos, 2) === peg$c106) {\n              s1 = peg$c106;\n              peg$currPos += 2;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c107); }\n            }\n            if (s1 !== peg$FAILED) {\n              peg$reportedPos = s0;\n              s1 = peg$c108();\n            }\n            s0 = s1;\n            if (s0 === peg$FAILED) {\n              s0 = peg$currPos;\n              if (input.substr(peg$currPos, 2) === peg$c109) {\n                s1 = peg$c109;\n                peg$currPos += 2;\n              } else {\n                s1 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c110); }\n              }\n              if (s1 !== peg$FAILED) {\n                peg$reportedPos = s0;\n                s1 = peg$c111();\n              }\n              s0 = s1;\n              if (s0 === peg$FAILED) {\n                s0 = peg$currPos;\n                if (input.substr(peg$currPos, 2) === peg$c112) {\n                  s1 = peg$c112;\n                  peg$currPos += 2;\n                } else {\n                  s1 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c113); }\n                }\n                if (s1 !== peg$FAILED) {\n                  peg$reportedPos = s0;\n                  s1 = peg$c114();\n                }\n                s0 = s1;\n                if (s0 === peg$FAILED) {\n                  s0 = peg$currPos;\n                  if (input.substr(peg$currPos, 2) === peg$c115) {\n                    s1 = peg$c115;\n                    peg$currPos += 2;\n                  } else {\n                    s1 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c116); }\n                  }\n                  if (s1 !== peg$FAILED) {\n                    peg$reportedPos = s0;\n                    s1 = peg$c117();\n                  }\n                  s0 = s1;\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parseESCAPED_UNICODE();\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseESCAPED_UNICODE() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10;\n\n      var key    = peg$currPos * 49 + 48,\n          cached = peg$cache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 2) === peg$c118) {\n        s1 = peg$c118;\n        peg$currPos += 2;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c119); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$currPos;\n        s3 = peg$parseHEX();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parseHEX();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseHEX();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseHEX();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseHEX();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parseHEX();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parseHEX();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parseHEX();\n                      if (s10 !== peg$FAILED) {\n                        s3 = [s3, s4, s5, s6, s7, s8, s9, s10];\n                        s2 = s3;\n                      } else {\n                        peg$currPos = s2;\n                        s2 = peg$c2;\n                      }\n                    } else {\n                      peg$currPos = s2;\n                      s2 = peg$c2;\n                    }\n                  } else {\n                    peg$currPos = s2;\n                    s2 = peg$c2;\n                  }\n                } else {\n                  peg$currPos = s2;\n                  s2 = peg$c2;\n                }\n              } else {\n                peg$currPos = s2;\n                s2 = peg$c2;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$c2;\n            }\n          } else {\n            peg$currPos = s2;\n            s2 = peg$c2;\n          }\n        } else {\n          peg$currPos = s2;\n          s2 = peg$c2;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$reportedPos = s0;\n          s1 = peg$c120(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$c2;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.substr(peg$currPos, 2) === peg$c121) {\n          s1 = peg$c121;\n          peg$currPos += 2;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c122); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$currPos;\n          s3 = peg$parseHEX();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseHEX();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parseHEX();\n              if (s5 !== peg$FAILED) {\n                s6 = peg$parseHEX();\n                if (s6 !== peg$FAILED) {\n                  s3 = [s3, s4, s5, s6];\n                  s2 = s3;\n                } else {\n                  peg$currPos = s2;\n                  s2 = peg$c2;\n                }\n              } else {\n                peg$currPos = s2;\n                s2 = peg$c2;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$c2;\n            }\n          } else {\n            peg$currPos = s2;\n            s2 = peg$c2;\n          }\n          if (s2 !== peg$FAILED) {\n            peg$reportedPos = s0;\n            s1 = peg$c120(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$c2;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$c2;\n        }\n      }\n\n      peg$cache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      var nodes = [];\n\n      function genError(err, line, col) {\n        var ex = new Error(err);\n        ex.line = line;\n        ex.column = col;\n        throw ex;\n      }\n\n      function addNode(node) {\n        nodes.push(node);\n      }\n\n      function node(type, value, line, column, key) {\n        var obj = { type: type, value: value, line: line(), column: column() };\n        if (key) obj.key = key;\n        return obj;\n      }\n\n      function convertCodePoint(str, line, col) {\n        var num = parseInt(\"0x\" + str);\n\n        if (\n          !isFinite(num) ||\n          Math.floor(num) != num ||\n          num < 0 ||\n          num > 0x10FFFF ||\n          (num > 0xD7FF && num < 0xE000)\n        ) {\n          genError(\"Invalid Unicode escape code: \" + str, line, col);\n        } else {\n          return fromCodePoint(num);\n        }\n      }\n\n      function fromCodePoint() {\n        var MAX_SIZE = 0x4000;\n        var codeUnits = [];\n        var highSurrogate;\n        var lowSurrogate;\n        var index = -1;\n        var length = arguments.length;\n        if (!length) {\n          return '';\n        }\n        var result = '';\n        while (++index < length) {\n          var codePoint = Number(arguments[index]);\n          if (codePoint <= 0xFFFF) { // BMP code point\n            codeUnits.push(codePoint);\n          } else { // Astral code point; split in surrogate halves\n            // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n            codePoint -= 0x10000;\n            highSurrogate = (codePoint >> 10) + 0xD800;\n            lowSurrogate = (codePoint % 0x400) + 0xDC00;\n            codeUnits.push(highSurrogate, lowSurrogate);\n          }\n          if (index + 1 == length || codeUnits.length > MAX_SIZE) {\n            result += String.fromCharCode.apply(null, codeUnits);\n            codeUnits.length = 0;\n          }\n        }\n        return result;\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail({ type: \"end\", description: \"end of input\" });\n      }\n\n      throw peg$buildException(null, peg$maxFailExpected, peg$maxFailPos);\n    }\n  }\n\n  return {\n    SyntaxError: SyntaxError,\n    parse:       parse\n  };\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/toml/lib/parser.js\n");

/***/ })

};
;