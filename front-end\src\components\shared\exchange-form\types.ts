interface TokenDetails {
  graduated?: string;
  poolAddress?: string;
  [key: string]: any;
}

export interface TokenOption {
  id: number;
  symbol: string;
  name: string;
  icon?: string;
  tokenAddress?: string;
  balance: number;
  creatorWallet?: string;
  tokenDetails?: TokenDetails;
}

export interface ExchangeFormProps {
  fromOptions: TokenOption[];
  toOptions: TokenOption[];
  classname?: string;
}

export interface TokenInputProps {
  label: string;
  token: TokenOption;
  amount: string;
  setAmount: (value: string) => void;
  onTokenChange: (token: TokenOption) => void;
  tokenOptions: TokenOption[];
  badge?: string;
  showSymbolInAvailable?: boolean;
  loading?: boolean;
}

export interface SwapButtonProps {
  onClick: () => void;
  disabled: boolean;
  isLoading: boolean;
}

export interface RefreshButtonProps {
  onClick: () => void;
  isRefreshing: boolean;
  isLoading: boolean;
} 