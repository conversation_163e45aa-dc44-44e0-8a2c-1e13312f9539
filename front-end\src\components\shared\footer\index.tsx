'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import React from 'react';

import { staggerContainer } from '@/lib/animations';
import { useTranslation } from '../../../hooks/useTranslation';


export default function Footer() {
  const { t } = useTranslation();
  const footerLinks = [
    { href: '/about-us', label: t('footer.about') },
    { href: '/how-it-works', label: t('footer.howItWorks') },
    { href: '/privacy', label: t('footer.privacy') },
  ];

  return (
    <motion.footer
      className="w-full h-56 relative bg-white overflow-hidden flex flex-col items-center justify-center"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true, margin: '-100px' }}
    >
      <motion.div
        variants={staggerContainer}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        className="flex flex-col items-center"
      >
        <motion.div
          className="text-center text-black text-2xl font-semibold font-['IBM_Plex_Sans'] leading-tight tracking-wide mb-8"
          variants={{
            initial: { opacity: 0, y: 20 },
            animate: { opacity: 1, y: 0 },
          }}
          whileHover={{
            scale: 1.05,
            color: '#FF6600',
            transition: { duration: 0.3 },
          }}
        >
          FUNHi
        </motion.div>

        <motion.div
          className="flex flex-wrap justify-center items-center gap-8 sm:gap-12 md:gap-16"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          {footerLinks.map((link, index) => (
            <motion.div
              key={link.href}
              variants={{
                initial: { opacity: 0, y: 20 },
                animate: {
                  opacity: 1,
                  y: 0,
                  transition: { delay: 0.1 + index * 0.1 },
                },
              }}
            >
              <Link href={link.href}>
                <motion.span
                  className={`text-center text-gray-800 font-semibold font-['IBM_Plex_Sans'] leading-snug transition-colors cursor-pointer ${
                    index === 1 ? 'text-xl leading-7' : 'text-base opacity-90'
                  }`}
                  whileHover={{
                    color: '#FF6600',
                    scale: 1.05,
                    transition: { duration: 0.2 },
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  {link.label}
                </motion.span>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="mt-8 w-full max-w-md h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        />

        <motion.div
          className="mt-4 text-center text-gray-500 text-sm font-['IBM_Plex_Sans']"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          viewport={{ once: true }}
        >
          {t('footer.copyright')}
        </motion.div>
      </motion.div>

      <motion.div
        className="absolute -top-10 -left-10 w-20 h-20 bg-[#FF6600]/10 rounded-full"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
      <motion.div
        className="absolute -bottom-5 -right-5 w-16 h-16 bg-[#FF6600]/20 rounded-full"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 2,
        }}
      />
    </motion.footer>
  );
}
