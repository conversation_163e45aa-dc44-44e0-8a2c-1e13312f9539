"use client";
import React, { useState } from "react";
import { MapPin, Calendar, Users, UserRoundPlus, ChevronRight } from "lucide-react";
import TradeChart from "@/components/shared/chart";
import { useAppContext } from "@/contexts/AppContext";
import { useEffect } from "react";
import { getUserPurchasesSummary, fetchTokenDetails, fetchFollowers, fetchFollowing, followUser, unfollowUser, updateProfile } from "@/axios/requests";
import CoinCard from "@/components/profile/CoinCard";
import ProfileHeader from "@/components/profile/ProfileHeader";
import PerksCarousel from "@/components/profile/PerksCarousel";
import PerksList from "@/components/profile/PerksList";
import { Coin, Perk } from "@/components/profile/types";
import { ProductCardSkeleton } from "@/components/ui/LoadingSkeletons";
import { TabContentLoader } from "@/components/ui/TabContentLoader";
import { motion, AnimatePresence } from "framer-motion";
import CustomDropdown from "@/components/ui/CustomDropdown";
import { useTranslation } from '@/hooks/useTranslation';
const mockPerks = [
  { 
    perkId: "1", 
    name: "White luxury suit - 3 pieces", 
    image: "/api/placeholder/300/200",
    price: "2,121,551 AYZ ~ $130",
    soldCount: "41885 orders"
  },
  { 
    perkId: "2", 
    name: "Black luxury suit - 3 pieces", 
    image: "/api/placeholder/300/200",
    price: "2,321,551 AYZ ~ $150",
    soldCount: "49896 orders"
  },
  { 
    perkId: "3", 
    name: "White luxury suit - 3 pieces", 
    image: "/api/placeholder/300/200",
    price: "2,121,551 AYZ ~ $130",
    soldCount: "417896 orders"
  },
  { 
    perkId: "4", 
    name: "Black luxury suit - 3 pieces", 
    image: "/api/placeholder/300/200",
    price: "2,321,551 AYZ ~ $150",
    soldCount: "4922 orders"
  },
  { 
    perkId: "5", 
    name: "Black luxury suit - 3 pieces", 
    image: "/api/placeholder/300/200",
    price: "2,321,551 AYZ ~ $150",
    soldCount: "4922 orders"
  }
];


const Profile = () => {
  const [activeTab, setActiveTab] = useState("coins");
  const [coins, setCoins] = useState<Coin[]>([]);
  const [perks, setPerks] = useState<Perk[]>([]);
  const [loading, setLoading] = useState(true);
  const { state } = useAppContext();
  const [coinDetails, setCoinDetails] = useState<any>(null);
  const [myPerks, setMyPerks] = useState<Perk[]>([]);
  const [page, setPage] = useState(0);
  const CARDS_PER_PAGE = 4;
  const CARDS_PER_VIEW = 3;
  const SKELETON_COUNT = 2;
  const SKELETON_CAROUSEL_COUNT = 3;
  const cardsPerPage = CARDS_PER_PAGE;
  const totalPages = Math.ceil((myPerks?.length || 0) / cardsPerPage);
  const visiblePerks = myPerks?.slice(page * cardsPerPage, (page + 1) * cardsPerPage) || [];
  const [carouselIndex, setCarouselIndex] = useState(0);
  const cardsPerView = React.useMemo(() => (typeof window !== 'undefined' && window.innerWidth >= 1024 ? CARDS_PER_VIEW : 1), []);
  const maxCarouselIndex = React.useMemo(() => Math.max(0, mockPerks.length - cardsPerView), [mockPerks.length, cardsPerView]);
  const [followers, setFollowers] = useState<any[]>([]); // Can be typed if User type is expanded
  const [following, setFollowing] = useState<any[]>([]); // Can be typed if User type is expanded
  const [isFollowing, setIsFollowing] = useState(false);
  const [followLoading, setFollowLoading] = useState(false);
  const [allowMessages, setAllowMessages] = useState(true);
  const [allowMessagesLoading, setAllowMessagesLoading] = useState(false);
  const [perkSort, setPerkSort] = useState('sales');
  const sortedPerks = React.useMemo(() => {
    const list = (perks.length > 0 ? perks : myPerks).slice();
    if (perkSort === 'sales') {
      return list.sort((a, b) => Number(b.soldCount || 0) - Number(a.soldCount || 0));
    } else if (perkSort === 'price') {
      return list.sort((a, b) => Number(b.price) - Number(a.price));
    } else if (perkSort === 'name') {
      return list.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }
    return list;
  }, [perks, myPerks, perkSort]);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslation();

  // Determine if viewing own profile (for now, always true, but should be dynamic if viewing others)
  const isOwnProfile = true; // Replace with logic if supporting viewing other profiles

  useEffect(() => {
    const fetchData = async () => {
      if (!state?.userBo?.id) return;
      setLoading(true);
      setError(null);
      try {
        const res = await getUserPurchasesSummary({ userId: state.userBo.id });
        setCoins(res.data?.PurchaseToken || []);
        setPerks(res.data?.PurchasedPerks || []);
        console.log("Datatttt",res.data?.PurchaseToken)
        console.log("NEw",res.data?.MyPerks)

        setMyPerks(res.data?.MyPerks || []);
        // Fetch followers/following
        const followersRes = await fetchFollowers(state.userBo.id);
        setFollowers(followersRes.followers || []);
        const followingRes = await fetchFollowing(state.userBo.id);
        setFollowing(followingRes.following || []);
        // Check if current user is following this profile
        const myId = state.userBo.id;
        setIsFollowing(!!(followersRes.followers || []).find((f: any) => f.followerId == myId));
        // Fetch allowMessages from user profile
        setAllowMessages(res.data?.allowMessages !== false);
        // Fetch details for the first coin (or selected coin)
        if (res.data?.PurchaseToken && res.data.PurchaseToken.length > 0) {
          const firstCoin = res.data.PurchaseToken[0];
          const id = firstCoin.tokenDetails?.tokenId || firstCoin.tokenDetails?.tokenAddress;
          if (id) {
            const detailsRes = await fetchTokenDetails(id.toString());
            setCoinDetails(detailsRes.data);
          }
        } else {
          setCoinDetails(null);
        }
      } catch (e) {
        setCoins([]);
        setPerks([]);
        setCoinDetails(null);
        setFollowers([]);
        setFollowing([]);
        setIsFollowing(false);
        setAllowMessages(true);
        setError("Failed to load profile data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [state?.userBo?.id]);

  // Handler for follow/unfollow
  const handleFollow = async () => {
    if (!state?.userBo?.id) return;
    setFollowLoading(true);
    try {
      await followUser(state.userBo.id, state.userBo.id);
      const followersRes = await fetchFollowers(state.userBo.id);
      setFollowers(followersRes.followers || []);
      setIsFollowing(true);
    } catch (e) {} finally {
      setFollowLoading(false);
    }
  };
  const handleUnfollow = async () => {
    if (!state?.userBo?.id) return;
    setFollowLoading(true);
    try {
      await unfollowUser(state.userBo.id, state.userBo.id);
      const followersRes = await fetchFollowers(state.userBo.id);
      setFollowers(followersRes.followers || []);
      setIsFollowing(false);
    } catch (e) {} finally {
      setFollowLoading(false);
    }
  };

  // Handler for allowMessages toggle
  const handleAllowMessagesToggle = async () => {
    if (!state?.userBo?.id) return;
    setAllowMessagesLoading(true);
    const newAllowMessages = !allowMessages;
    setAllowMessages(newAllowMessages);
    try {
      await updateProfile({ userId: state.userBo.id, allowMessages: newAllowMessages });
      // Refetch profile data to get the latest allowMessages value
      const res = await getUserPurchasesSummary({ userId: state.userBo.id });
      setAllowMessages(res.data?.allowMessages === true);
    } catch (e) {} finally {
      setAllowMessagesLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="flex flex-col items-center justify-center mt-5 w-full">
        <ProfileHeader
          isOwnProfile={isOwnProfile}
          followersCount={followers.length}
          followingCount={following.length}
          isFollowing={isFollowing}
          followLoading={followLoading}
          allowMessages={allowMessages}
          allowMessagesLoading={allowMessagesLoading}
          onFollow={handleFollow}
          onUnfollow={handleUnfollow}
          onAllowMessagesToggle={handleAllowMessagesToggle}
        />
      </div>
      <div className="w-full p-4 mt-3">
        {/* Title */}
        <h3 className="text-3xl font-bold text-[#FF6600] text-center mb-4">
          {t('profilePage.profileInformation')}
        </h3>

        {/* Toggle Button */}
        <div className="flex justify-center mt-8">
          <div
            className="relative rounded-full p-1 flex"
            style={{ backgroundColor: "#FF6600" }}
          >
            <button
              onClick={() => setActiveTab("coins")}
              className={`relative z-10 py-5 px-12 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === "coins"
                  ? "bg-white text-[#FF6600] "
                  : "bg-transparent text-white "
              }`}
            >
              {t('profilePage.coins')}
            </button>
            <button
              onClick={() => setActiveTab("items")}
              className={`relative z-10 py-5 px-12 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === "items"
                  ? "bg-white text-[#FF6600]"
                  : "bg-transparent text-white "
              }`}
            >
              {t('profilePage.items')}
            </button>
          </div>
        </div>

        {/* Content based on active tab */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 40 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -40 }}
            transition={{ duration: 0.3 }}
            className="mt-4 w-full"
          >
            {error && (
              <div className="w-full text-center text-red-600 font-semibold py-4">
                {t('profilePage.loadError')}
              </div>
            )}
            {loading ? (
              <div className="grid w-full gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 place-items-center">
                {Array.from({ length: SKELETON_COUNT }).map((_, idx) => (
                  <ProductCardSkeleton key={idx} />
                ))}
              </div>
            ) : activeTab === "coins" ? (
              <div className="grid w-full gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 place-items-center">
                {coins.length === 0 ? (
                  <div className="text-center text-gray-500 py-10 col-span-full">{t('profilePage.noCoinsFound')}</div>
                ) : (
                  coins.map((coin: Coin, idx: number) => (
                    <CoinCard key={coin.tokenDetails?.tokenId || idx}   coin={{
                      ...coin,
                      ...coin.tokenDetails // flatten tokenDetails into the top level
                    }} />
                  ))
                )}
              </div>
            ) : (
              <div className="w-full">
                <div
                  className="w-full rounded-2xl mt-4 mb-4 p-4 bg-[#FF6600]"
                >
                  {/* Header row */}
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-2">
                      <img src={sortedPerks[0]?.image || "/api/placeholder/40/40"} alt="Avatar" className="w-8 h-8 rounded-full" />
                      <span className="text-white font-semibold">{sortedPerks[0]?.name || t('profilePage.perkToken')}</span>
                    </div>
                    {/* Sort by dropdown */}
                    <CustomDropdown
                      label={t('profilePage.sortBy')}
                      options={[
                        { value: "sales", label: t('profilePage.sales') },
                        { value: "price", label: t('profilePage.price') },
                        { value: "name", label: t('profilePage.name') },
                      ]}
                      selectedValue={perkSort}
                      onChange={setPerkSort}
                      className="min-w-[120px] [&>span]:text-white"
                    />
                  </div>
                  {/* Mobile: vertical list */}
                  {loading ? (
                    <div className="flex flex-col gap-6 w-full lg:hidden">
                      {Array.from({ length: SKELETON_COUNT }).map((_, idx) => (
                        <ProductCardSkeleton key={idx} />
                      ))}
                    </div>
                  ) : (
                    <PerksList sortedPerks={sortedPerks} />
                  )}
                  {/* Desktop: carousel */}
                  {loading ? (
                    <div className="relative w-full items-center hidden lg:flex">
                      {Array.from({ length: SKELETON_CAROUSEL_COUNT }).map((_, idx) => (
                        <div className="mx-2" key={idx}>
                          <ProductCardSkeleton />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <PerksCarousel
                      sortedPerks={sortedPerks}
                      carouselIndex={carouselIndex}
                      cardsPerView={cardsPerView}
                      maxCarouselIndex={maxCarouselIndex}
                      setCarouselIndex={setCarouselIndex}
                    />
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Profile;