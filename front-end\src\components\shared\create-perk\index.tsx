"use client";

import React from 'react';
import dynamic from 'next/dynamic';

const CreatePerkForm = dynamic(() => import('@/components/perks/create-perk-form').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});



export default function CreatePerkPage({ onClose }: { onClose: () => void }) {
  return (
    <div className="min-h-screen bg-[#000000c7] flex items-center justify-center py-12 px-4 fixed top-0 left-0 w-full h-full z-10">
      <CreatePerkForm onClose={onClose} />
    </div>
  );
} 