"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bignumber.js";
exports.ids = ["vendor-chunks/bignumber.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/bignumber.js/bignumber.mjs":
/*!*************************************************!*\
  !*** ./node_modules/bignumber.js/bignumber.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BigNumber: () => (/* binding */ BigNumber),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *      bignumber.js v9.3.0\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */\r\n\r\n\r\nvar\r\n  isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i,\r\n  mathceil = Math.ceil,\r\n  mathfloor = Math.floor,\r\n\r\n  bignumberError = '[BigNumber Error] ',\r\n  tooManyDigits = bignumberError + 'Number primitive has more than 15 significant digits: ',\r\n\r\n  BASE = 1e14,\r\n  LOG_BASE = 14,\r\n  MAX_SAFE_INTEGER = 0x1fffffffffffff,         // 2^53 - 1\r\n  // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\r\n  POWS_TEN = [1, 10, 100, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9, 1e10, 1e11, 1e12, 1e13],\r\n  SQRT_BASE = 1e7,\r\n\r\n  // EDITABLE\r\n  // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\r\n  // the arguments to toExponential, toFixed, toFormat, and toPrecision.\r\n  MAX = 1E9;                                   // 0 to MAX_INT32\r\n\r\n\r\n/*\r\n * Create and return a BigNumber constructor.\r\n */\r\nfunction clone(configObject) {\r\n  var div, convertBase, parseNumeric,\r\n    P = BigNumber.prototype = { constructor: BigNumber, toString: null, valueOf: null },\r\n    ONE = new BigNumber(1),\r\n\r\n\r\n    //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\r\n\r\n\r\n    // The default values below must be integers within the inclusive ranges stated.\r\n    // The values can also be changed at run-time using BigNumber.set.\r\n\r\n    // The maximum number of decimal places for operations involving division.\r\n    DECIMAL_PLACES = 20,                     // 0 to MAX\r\n\r\n    // The rounding mode used when rounding to the above decimal places, and when using\r\n    // toExponential, toFixed, toFormat and toPrecision, and round (default value).\r\n    // UP         0 Away from zero.\r\n    // DOWN       1 Towards zero.\r\n    // CEIL       2 Towards +Infinity.\r\n    // FLOOR      3 Towards -Infinity.\r\n    // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    ROUNDING_MODE = 4,                       // 0 to 8\r\n\r\n    // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\r\n\r\n    // The exponent value at and beneath which toString returns exponential notation.\r\n    // Number type: -7\r\n    TO_EXP_NEG = -7,                         // 0 to -MAX\r\n\r\n    // The exponent value at and above which toString returns exponential notation.\r\n    // Number type: 21\r\n    TO_EXP_POS = 21,                         // 0 to MAX\r\n\r\n    // RANGE : [MIN_EXP, MAX_EXP]\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // Number type: -324  (5e-324)\r\n    MIN_EXP = -1e7,                          // -1 to -MAX\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // Number type:  308  (1.7976931348623157e+308)\r\n    // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\r\n    MAX_EXP = 1e7,                           // 1 to MAX\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    CRYPTO = false,                          // true or false\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP        0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN      1 The remainder has the same sign as the dividend.\r\n    //             This modulo mode is commonly known as 'truncated division' and is\r\n    //             equivalent to (a % n) in JavaScript.\r\n    // FLOOR     3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\r\n    // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\r\n    //             The remainder is always positive.\r\n    //\r\n    // The truncated division, floored division, Euclidian division and IEEE 754 remainder\r\n    // modes are commonly used for the modulus operation.\r\n    // Although the other rounding modes can also be used, they may not give useful results.\r\n    MODULO_MODE = 1,                         // 0 to 9\r\n\r\n    // The maximum number of significant digits of the result of the exponentiatedBy operation.\r\n    // If POW_PRECISION is 0, there will be unlimited significant digits.\r\n    POW_PRECISION = 0,                       // 0 to MAX\r\n\r\n    // The format specification used by the BigNumber.prototype.toFormat method.\r\n    FORMAT = {\r\n      prefix: '',\r\n      groupSize: 3,\r\n      secondaryGroupSize: 0,\r\n      groupSeparator: ',',\r\n      decimalSeparator: '.',\r\n      fractionGroupSize: 0,\r\n      fractionGroupSeparator: '\\xA0',        // non-breaking space\r\n      suffix: ''\r\n    },\r\n\r\n    // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\r\n    // '-', '.', whitespace, or repeated character.\r\n    // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\r\n    ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz',\r\n    alphabetHasNormalDecimalDigits = true;\r\n\r\n\r\n  //------------------------------------------------------------------------------------------\r\n\r\n\r\n  // CONSTRUCTOR\r\n\r\n\r\n  /*\r\n   * The BigNumber constructor and exported function.\r\n   * Create and return a new instance of a BigNumber object.\r\n   *\r\n   * v {number|string|BigNumber} A numeric value.\r\n   * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n   */\r\n  function BigNumber(v, b) {\r\n    var alphabet, c, caseChanged, e, i, isNum, len, str,\r\n      x = this;\r\n\r\n    // Enable constructor call without `new`.\r\n    if (!(x instanceof BigNumber)) return new BigNumber(v, b);\r\n\r\n    if (b == null) {\r\n\r\n      if (v && v._isBigNumber === true) {\r\n        x.s = v.s;\r\n\r\n        if (!v.c || v.e > MAX_EXP) {\r\n          x.c = x.e = null;\r\n        } else if (v.e < MIN_EXP) {\r\n          x.c = [x.e = 0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.c = v.c.slice();\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      if ((isNum = typeof v == 'number') && v * 0 == 0) {\r\n\r\n        // Use `1 / n` to handle minus zero also.\r\n        x.s = 1 / v < 0 ? (v = -v, -1) : 1;\r\n\r\n        // Fast path for integers, where n < 2147483648 (2**31).\r\n        if (v === ~~v) {\r\n          for (e = 0, i = v; i >= 10; i /= 10, e++);\r\n\r\n          if (e > MAX_EXP) {\r\n            x.c = x.e = null;\r\n          } else {\r\n            x.e = e;\r\n            x.c = [v];\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        str = String(v);\r\n      } else {\r\n\r\n        if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\r\n\r\n        x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n      // Exponential form?\r\n      if ((i = str.search(/e/i)) > 0) {\r\n\r\n        // Determine exponent.\r\n        if (e < 0) e = i;\r\n        e += +str.slice(i + 1);\r\n        str = str.substring(0, i);\r\n      } else if (e < 0) {\r\n\r\n        // Integer.\r\n        e = str.length;\r\n      }\r\n\r\n    } else {\r\n\r\n      // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n      intCheck(b, 2, ALPHABET.length, 'Base');\r\n\r\n      // Allow exponential notation to be used with base 10 argument, while\r\n      // also rounding to DECIMAL_PLACES as with other bases.\r\n      if (b == 10 && alphabetHasNormalDecimalDigits) {\r\n        x = new BigNumber(v);\r\n        return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\r\n      }\r\n\r\n      str = String(v);\r\n\r\n      if (isNum = typeof v == 'number') {\r\n\r\n        // Avoid potential interpretation of Infinity and NaN as base 44+ values.\r\n        if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\r\n\r\n        x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\r\n\r\n        // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n        if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, '').length > 15) {\r\n          throw Error\r\n           (tooManyDigits + v);\r\n        }\r\n      } else {\r\n        x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      alphabet = ALPHABET.slice(0, b);\r\n      e = i = 0;\r\n\r\n      // Check that str is a valid base b number.\r\n      // Don't use RegExp, so alphabet can contain special characters.\r\n      for (len = str.length; i < len; i++) {\r\n        if (alphabet.indexOf(c = str.charAt(i)) < 0) {\r\n          if (c == '.') {\r\n\r\n            // If '.' is not the first character and it has not be found before.\r\n            if (i > e) {\r\n              e = len;\r\n              continue;\r\n            }\r\n          } else if (!caseChanged) {\r\n\r\n            // Allow e.g. hexadecimal 'FF' as well as 'ff'.\r\n            if (str == str.toUpperCase() && (str = str.toLowerCase()) ||\r\n                str == str.toLowerCase() && (str = str.toUpperCase())) {\r\n              caseChanged = true;\r\n              i = -1;\r\n              e = 0;\r\n              continue;\r\n            }\r\n          }\r\n\r\n          return parseNumeric(x, String(v), isNum, b);\r\n        }\r\n      }\r\n\r\n      // Prevent later check for length on converted number.\r\n      isNum = false;\r\n      str = convertBase(str, b, 10, x.s);\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n      else e = str.length;\r\n    }\r\n\r\n    // Determine leading zeros.\r\n    for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n    // Determine trailing zeros.\r\n    for (len = str.length; str.charCodeAt(--len) === 48;);\r\n\r\n    if (str = str.slice(i, ++len)) {\r\n      len -= i;\r\n\r\n      // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n      if (isNum && BigNumber.DEBUG &&\r\n        len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\r\n          throw Error\r\n           (tooManyDigits + (x.s * v));\r\n      }\r\n\r\n       // Overflow?\r\n      if ((e = e - i - 1) > MAX_EXP) {\r\n\r\n        // Infinity.\r\n        x.c = x.e = null;\r\n\r\n      // Underflow?\r\n      } else if (e < MIN_EXP) {\r\n\r\n        // Zero.\r\n        x.c = [x.e = 0];\r\n      } else {\r\n        x.e = e;\r\n        x.c = [];\r\n\r\n        // Transform base\r\n\r\n        // e is the base 10 exponent.\r\n        // i is where to slice str to get the first element of the coefficient array.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (e < 0) i += LOG_BASE;  // i < 1\r\n\r\n        if (i < len) {\r\n          if (i) x.c.push(+str.slice(0, i));\r\n\r\n          for (len -= LOG_BASE; i < len;) {\r\n            x.c.push(+str.slice(i, i += LOG_BASE));\r\n          }\r\n\r\n          i = LOG_BASE - (str = str.slice(i)).length;\r\n        } else {\r\n          i -= len;\r\n        }\r\n\r\n        for (; i--; str += '0');\r\n        x.c.push(+str);\r\n      }\r\n    } else {\r\n\r\n      // Zero.\r\n      x.c = [x.e = 0];\r\n    }\r\n  }\r\n\r\n\r\n  // CONSTRUCTOR PROPERTIES\r\n\r\n\r\n  BigNumber.clone = clone;\r\n\r\n  BigNumber.ROUND_UP = 0;\r\n  BigNumber.ROUND_DOWN = 1;\r\n  BigNumber.ROUND_CEIL = 2;\r\n  BigNumber.ROUND_FLOOR = 3;\r\n  BigNumber.ROUND_HALF_UP = 4;\r\n  BigNumber.ROUND_HALF_DOWN = 5;\r\n  BigNumber.ROUND_HALF_EVEN = 6;\r\n  BigNumber.ROUND_HALF_CEIL = 7;\r\n  BigNumber.ROUND_HALF_FLOOR = 8;\r\n  BigNumber.EUCLID = 9;\r\n\r\n\r\n  /*\r\n   * Configure infrequently-changing library-wide settings.\r\n   *\r\n   * Accept an object with the following optional properties (if the value of a property is\r\n   * a number, it must be an integer within the inclusive range stated):\r\n   *\r\n   *   DECIMAL_PLACES   {number}           0 to MAX\r\n   *   ROUNDING_MODE    {number}           0 to 8\r\n   *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n   *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n   *   CRYPTO           {boolean}          true or false\r\n   *   MODULO_MODE      {number}           0 to 9\r\n   *   POW_PRECISION       {number}           0 to MAX\r\n   *   ALPHABET         {string}           A string of two or more unique characters which does\r\n   *                                       not contain '.'.\r\n   *   FORMAT           {object}           An object with some of the following properties:\r\n   *     prefix                 {string}\r\n   *     groupSize              {number}\r\n   *     secondaryGroupSize     {number}\r\n   *     groupSeparator         {string}\r\n   *     decimalSeparator       {string}\r\n   *     fractionGroupSize      {number}\r\n   *     fractionGroupSeparator {string}\r\n   *     suffix                 {string}\r\n   *\r\n   * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n   *\r\n   * E.g.\r\n   * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n   *\r\n   * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n   *\r\n   * Return an object with the properties current values.\r\n   */\r\n  BigNumber.config = BigNumber.set = function (obj) {\r\n    var p, v;\r\n\r\n    if (obj != null) {\r\n\r\n      if (typeof obj == 'object') {\r\n\r\n        // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'DECIMAL_PLACES')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          DECIMAL_PLACES = v;\r\n        }\r\n\r\n        // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\r\n        // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'ROUNDING_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 8, p);\r\n          ROUNDING_MODE = v;\r\n        }\r\n\r\n        // EXPONENTIAL_AT {number|number[]}\r\n        // Integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\r\n        // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'EXPONENTIAL_AT')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, 0, p);\r\n            intCheck(v[1], 0, MAX, p);\r\n            TO_EXP_NEG = v[0];\r\n            TO_EXP_POS = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\r\n          }\r\n        }\r\n\r\n        // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\r\n        // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\r\n        if (obj.hasOwnProperty(p = 'RANGE')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, -1, p);\r\n            intCheck(v[1], 1, MAX, p);\r\n            MIN_EXP = v[0];\r\n            MAX_EXP = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            if (v) {\r\n              MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\r\n            } else {\r\n              throw Error\r\n               (bignumberError + p + ' cannot be zero: ' + v);\r\n            }\r\n          }\r\n        }\r\n\r\n        // CRYPTO {boolean} true or false.\r\n        // '[BigNumber Error] CRYPTO not true or false: {v}'\r\n        // '[BigNumber Error] crypto unavailable'\r\n        if (obj.hasOwnProperty(p = 'CRYPTO')) {\r\n          v = obj[p];\r\n          if (v === !!v) {\r\n            if (v) {\r\n              if (typeof crypto != 'undefined' && crypto &&\r\n               (crypto.getRandomValues || crypto.randomBytes)) {\r\n                CRYPTO = v;\r\n              } else {\r\n                CRYPTO = !v;\r\n                throw Error\r\n                 (bignumberError + 'crypto unavailable');\r\n              }\r\n            } else {\r\n              CRYPTO = v;\r\n            }\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' not true or false: ' + v);\r\n          }\r\n        }\r\n\r\n        // MODULO_MODE {number} Integer, 0 to 9 inclusive.\r\n        // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'MODULO_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 9, p);\r\n          MODULO_MODE = v;\r\n        }\r\n\r\n        // POW_PRECISION {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'POW_PRECISION')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          POW_PRECISION = v;\r\n        }\r\n\r\n        // FORMAT {object}\r\n        // '[BigNumber Error] FORMAT not an object: {v}'\r\n        if (obj.hasOwnProperty(p = 'FORMAT')) {\r\n          v = obj[p];\r\n          if (typeof v == 'object') FORMAT = v;\r\n          else throw Error\r\n           (bignumberError + p + ' not an object: ' + v);\r\n        }\r\n\r\n        // ALPHABET {string}\r\n        // '[BigNumber Error] ALPHABET invalid: {v}'\r\n        if (obj.hasOwnProperty(p = 'ALPHABET')) {\r\n          v = obj[p];\r\n\r\n          // Disallow if less than two characters,\r\n          // or if it contains '+', '-', '.', whitespace, or a repeated character.\r\n          if (typeof v == 'string' && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\r\n            alphabetHasNormalDecimalDigits = v.slice(0, 10) == '0123456789';\r\n            ALPHABET = v;\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' invalid: ' + v);\r\n          }\r\n        }\r\n\r\n      } else {\r\n\r\n        // '[BigNumber Error] Object expected: {v}'\r\n        throw Error\r\n         (bignumberError + 'Object expected: ' + obj);\r\n      }\r\n    }\r\n\r\n    return {\r\n      DECIMAL_PLACES: DECIMAL_PLACES,\r\n      ROUNDING_MODE: ROUNDING_MODE,\r\n      EXPONENTIAL_AT: [TO_EXP_NEG, TO_EXP_POS],\r\n      RANGE: [MIN_EXP, MAX_EXP],\r\n      CRYPTO: CRYPTO,\r\n      MODULO_MODE: MODULO_MODE,\r\n      POW_PRECISION: POW_PRECISION,\r\n      FORMAT: FORMAT,\r\n      ALPHABET: ALPHABET\r\n    };\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if v is a BigNumber instance, otherwise return false.\r\n   *\r\n   * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n   *\r\n   * v {any}\r\n   *\r\n   * '[BigNumber Error] Invalid BigNumber: {v}'\r\n   */\r\n  BigNumber.isBigNumber = function (v) {\r\n    if (!v || v._isBigNumber !== true) return false;\r\n    if (!BigNumber.DEBUG) return true;\r\n\r\n    var i, n,\r\n      c = v.c,\r\n      e = v.e,\r\n      s = v.s;\r\n\r\n    out: if ({}.toString.call(c) == '[object Array]') {\r\n\r\n      if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\r\n\r\n        // If the first element is zero, the BigNumber value must be zero.\r\n        if (c[0] === 0) {\r\n          if (e === 0 && c.length === 1) return true;\r\n          break out;\r\n        }\r\n\r\n        // Calculate number of digits that c[0] should have, based on the exponent.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (i < 1) i += LOG_BASE;\r\n\r\n        // Calculate number of digits of c[0].\r\n        //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\r\n        if (String(c[0]).length == i) {\r\n\r\n          for (i = 0; i < c.length; i++) {\r\n            n = c[i];\r\n            if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\r\n          }\r\n\r\n          // Last element cannot be zero, unless it is the only element.\r\n          if (n !== 0) return true;\r\n        }\r\n      }\r\n\r\n    // Infinity/NaN\r\n    } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\r\n      return true;\r\n    }\r\n\r\n    throw Error\r\n      (bignumberError + 'Invalid BigNumber: ' + v);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the maximum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.maximum = BigNumber.max = function () {\r\n    return maxOrMin(arguments, -1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the minimum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.minimum = BigNumber.min = function () {\r\n    return maxOrMin(arguments, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n   * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n   * zeros are produced).\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n   * '[BigNumber Error] crypto unavailable'\r\n   */\r\n  BigNumber.random = (function () {\r\n    var pow2_53 = 0x20000000000000;\r\n\r\n    // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\r\n    // Check if Math.random() produces more than 32 bits of randomness.\r\n    // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\r\n    // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\r\n    var random53bitInt = (Math.random() * pow2_53) & 0x1fffff\r\n     ? function () { return mathfloor(Math.random() * pow2_53); }\r\n     : function () { return ((Math.random() * 0x40000000 | 0) * 0x800000) +\r\n       (Math.random() * 0x800000 | 0); };\r\n\r\n    return function (dp) {\r\n      var a, b, e, k, v,\r\n        i = 0,\r\n        c = [],\r\n        rand = new BigNumber(ONE);\r\n\r\n      if (dp == null) dp = DECIMAL_PLACES;\r\n      else intCheck(dp, 0, MAX);\r\n\r\n      k = mathceil(dp / LOG_BASE);\r\n\r\n      if (CRYPTO) {\r\n\r\n        // Browsers supporting crypto.getRandomValues.\r\n        if (crypto.getRandomValues) {\r\n\r\n          a = crypto.getRandomValues(new Uint32Array(k *= 2));\r\n\r\n          for (; i < k;) {\r\n\r\n            // 53 bits:\r\n            // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\r\n            // 11111 11111111 11111111 11111111 11100000 00000000 00000000\r\n            // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\r\n            //                                     11111 11111111 11111111\r\n            // 0x20000 is 2^21.\r\n            v = a[i] * 0x20000 + (a[i + 1] >>> 11);\r\n\r\n            // Rejection sampling:\r\n            // 0 <= v < 9007199254740992\r\n            // Probability that v >= 9e15, is\r\n            // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\r\n            if (v >= 9e15) {\r\n              b = crypto.getRandomValues(new Uint32Array(2));\r\n              a[i] = b[0];\r\n              a[i + 1] = b[1];\r\n            } else {\r\n\r\n              // 0 <= v <= 8999999999999999\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 2;\r\n            }\r\n          }\r\n          i = k / 2;\r\n\r\n        // Node.js supporting crypto.randomBytes.\r\n        } else if (crypto.randomBytes) {\r\n\r\n          // buffer\r\n          a = crypto.randomBytes(k *= 7);\r\n\r\n          for (; i < k;) {\r\n\r\n            // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\r\n            // 0x100000000 is 2^32, 0x1000000 is 2^24\r\n            // 11111 11111111 11111111 11111111 11111111 11111111 11111111\r\n            // 0 <= v < 9007199254740992\r\n            v = ((a[i] & 31) * 0x1000000000000) + (a[i + 1] * 0x10000000000) +\r\n               (a[i + 2] * 0x100000000) + (a[i + 3] * 0x1000000) +\r\n               (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\r\n\r\n            if (v >= 9e15) {\r\n              crypto.randomBytes(7).copy(a, i);\r\n            } else {\r\n\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 7;\r\n            }\r\n          }\r\n          i = k / 7;\r\n        } else {\r\n          CRYPTO = false;\r\n          throw Error\r\n           (bignumberError + 'crypto unavailable');\r\n        }\r\n      }\r\n\r\n      // Use Math.random.\r\n      if (!CRYPTO) {\r\n\r\n        for (; i < k;) {\r\n          v = random53bitInt();\r\n          if (v < 9e15) c[i++] = v % 1e14;\r\n        }\r\n      }\r\n\r\n      k = c[--i];\r\n      dp %= LOG_BASE;\r\n\r\n      // Convert trailing digits to zeros according to dp.\r\n      if (k && dp) {\r\n        v = POWS_TEN[LOG_BASE - dp];\r\n        c[i] = mathfloor(k / v) * v;\r\n      }\r\n\r\n      // Remove trailing elements which are zero.\r\n      for (; c[i] === 0; c.pop(), i--);\r\n\r\n      // Zero?\r\n      if (i < 0) {\r\n        c = [e = 0];\r\n      } else {\r\n\r\n        // Remove leading elements which are zero and adjust exponent accordingly.\r\n        for (e = -1 ; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\r\n\r\n        // Count the digits of the first element of c to determine leading zeros, and...\r\n        for (i = 1, v = c[0]; v >= 10; v /= 10, i++);\r\n\r\n        // adjust the exponent accordingly.\r\n        if (i < LOG_BASE) e -= LOG_BASE - i;\r\n      }\r\n\r\n      rand.e = e;\r\n      rand.c = c;\r\n      return rand;\r\n    };\r\n  })();\r\n\r\n\r\n   /*\r\n   * Return a BigNumber whose value is the sum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.sum = function () {\r\n    var i = 1,\r\n      args = arguments,\r\n      sum = new BigNumber(args[0]);\r\n    for (; i < args.length;) sum = sum.plus(args[i++]);\r\n    return sum;\r\n  };\r\n\r\n\r\n  // PRIVATE FUNCTIONS\r\n\r\n\r\n  // Called by BigNumber and BigNumber.prototype.toString.\r\n  convertBase = (function () {\r\n    var decimal = '0123456789';\r\n\r\n    /*\r\n     * Convert string of baseIn to an array of numbers of baseOut.\r\n     * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n     * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n     */\r\n    function toBaseOut(str, baseIn, baseOut, alphabet) {\r\n      var j,\r\n        arr = [0],\r\n        arrL,\r\n        i = 0,\r\n        len = str.length;\r\n\r\n      for (; i < len;) {\r\n        for (arrL = arr.length; arrL--; arr[arrL] *= baseIn);\r\n\r\n        arr[0] += alphabet.indexOf(str.charAt(i++));\r\n\r\n        for (j = 0; j < arr.length; j++) {\r\n\r\n          if (arr[j] > baseOut - 1) {\r\n            if (arr[j + 1] == null) arr[j + 1] = 0;\r\n            arr[j + 1] += arr[j] / baseOut | 0;\r\n            arr[j] %= baseOut;\r\n          }\r\n        }\r\n      }\r\n\r\n      return arr.reverse();\r\n    }\r\n\r\n    // Convert a numeric string of baseIn to a numeric string of baseOut.\r\n    // If the caller is toString, we are converting from base 10 to baseOut.\r\n    // If the caller is BigNumber, we are converting from baseIn to base 10.\r\n    return function (str, baseIn, baseOut, sign, callerIsToString) {\r\n      var alphabet, d, e, k, r, x, xc, y,\r\n        i = str.indexOf('.'),\r\n        dp = DECIMAL_PLACES,\r\n        rm = ROUNDING_MODE;\r\n\r\n      // Non-integer.\r\n      if (i >= 0) {\r\n        k = POW_PRECISION;\r\n\r\n        // Unlimited precision.\r\n        POW_PRECISION = 0;\r\n        str = str.replace('.', '');\r\n        y = new BigNumber(baseIn);\r\n        x = y.pow(str.length - i);\r\n        POW_PRECISION = k;\r\n\r\n        // Convert str as if an integer, then restore the fraction part by dividing the\r\n        // result by its base raised to a power.\r\n\r\n        y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, '0'),\r\n         10, baseOut, decimal);\r\n        y.e = y.c.length;\r\n      }\r\n\r\n      // Convert the number as integer.\r\n\r\n      xc = toBaseOut(str, baseIn, baseOut, callerIsToString\r\n       ? (alphabet = ALPHABET, decimal)\r\n       : (alphabet = decimal, ALPHABET));\r\n\r\n      // xc now represents str as an integer and converted to baseOut. e is the exponent.\r\n      e = k = xc.length;\r\n\r\n      // Remove trailing zeros.\r\n      for (; xc[--k] == 0; xc.pop());\r\n\r\n      // Zero?\r\n      if (!xc[0]) return alphabet.charAt(0);\r\n\r\n      // Does str represent an integer? If so, no need for the division.\r\n      if (i < 0) {\r\n        --e;\r\n      } else {\r\n        x.c = xc;\r\n        x.e = e;\r\n\r\n        // The sign is needed for correct rounding.\r\n        x.s = sign;\r\n        x = div(x, y, dp, rm, baseOut);\r\n        xc = x.c;\r\n        r = x.r;\r\n        e = x.e;\r\n      }\r\n\r\n      // xc now represents str converted to baseOut.\r\n\r\n      // The index of the rounding digit.\r\n      d = e + dp + 1;\r\n\r\n      // The rounding digit: the digit to the right of the digit that may be rounded up.\r\n      i = xc[d];\r\n\r\n      // Look at the rounding digits and mode to determine whether to round up.\r\n\r\n      k = baseOut / 2;\r\n      r = r || d < 0 || xc[d + 1] != null;\r\n\r\n      r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n            : i > k || i == k &&(rm == 4 || r || rm == 6 && xc[d - 1] & 1 ||\r\n             rm == (x.s < 0 ? 8 : 7));\r\n\r\n      // If the index of the rounding digit is not greater than zero, or xc represents\r\n      // zero, then the result of the base conversion is zero or, if rounding up, a value\r\n      // such as 0.00001.\r\n      if (d < 1 || !xc[0]) {\r\n\r\n        // 1^-dp or 0\r\n        str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\r\n      } else {\r\n\r\n        // Truncate xc to the required number of decimal places.\r\n        xc.length = d;\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          // Rounding up may mean the previous digit has to be rounded up and so on.\r\n          for (--baseOut; ++xc[--d] > baseOut;) {\r\n            xc[d] = 0;\r\n\r\n            if (!d) {\r\n              ++e;\r\n              xc = [1].concat(xc);\r\n            }\r\n          }\r\n        }\r\n\r\n        // Determine trailing zeros.\r\n        for (k = xc.length; !xc[--k];);\r\n\r\n        // E.g. [4, 11, 15] becomes 4bf.\r\n        for (i = 0, str = ''; i <= k; str += alphabet.charAt(xc[i++]));\r\n\r\n        // Add leading zeros, decimal point and trailing zeros as required.\r\n        str = toFixedPoint(str, e, alphabet.charAt(0));\r\n      }\r\n\r\n      // The caller will add the sign.\r\n      return str;\r\n    };\r\n  })();\r\n\r\n\r\n  // Perform division in the specified base. Called by div and convertBase.\r\n  div = (function () {\r\n\r\n    // Assume non-zero x and k.\r\n    function multiply(x, k, base) {\r\n      var m, temp, xlo, xhi,\r\n        carry = 0,\r\n        i = x.length,\r\n        klo = k % SQRT_BASE,\r\n        khi = k / SQRT_BASE | 0;\r\n\r\n      for (x = x.slice(); i--;) {\r\n        xlo = x[i] % SQRT_BASE;\r\n        xhi = x[i] / SQRT_BASE | 0;\r\n        m = khi * xlo + xhi * klo;\r\n        temp = klo * xlo + ((m % SQRT_BASE) * SQRT_BASE) + carry;\r\n        carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\r\n        x[i] = temp % base;\r\n      }\r\n\r\n      if (carry) x = [carry].concat(x);\r\n\r\n      return x;\r\n    }\r\n\r\n    function compare(a, b, aL, bL) {\r\n      var i, cmp;\r\n\r\n      if (aL != bL) {\r\n        cmp = aL > bL ? 1 : -1;\r\n      } else {\r\n\r\n        for (i = cmp = 0; i < aL; i++) {\r\n\r\n          if (a[i] != b[i]) {\r\n            cmp = a[i] > b[i] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return cmp;\r\n    }\r\n\r\n    function subtract(a, b, aL, base) {\r\n      var i = 0;\r\n\r\n      // Subtract b from a.\r\n      for (; aL--;) {\r\n        a[aL] -= i;\r\n        i = a[aL] < b[aL] ? 1 : 0;\r\n        a[aL] = i * base + a[aL] - b[aL];\r\n      }\r\n\r\n      // Remove leading zeros.\r\n      for (; !a[0] && a.length > 1; a.splice(0, 1));\r\n    }\r\n\r\n    // x: dividend, y: divisor.\r\n    return function (x, y, dp, rm, base) {\r\n      var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0,\r\n        yL, yz,\r\n        s = x.s == y.s ? 1 : -1,\r\n        xc = x.c,\r\n        yc = y.c;\r\n\r\n      // Either NaN, Infinity or 0?\r\n      if (!xc || !xc[0] || !yc || !yc[0]) {\r\n\r\n        return new BigNumber(\r\n\r\n         // Return NaN if either NaN, or both Infinity or 0.\r\n         !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN :\r\n\r\n          // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\r\n          xc && xc[0] == 0 || !yc ? s * 0 : s / 0\r\n       );\r\n      }\r\n\r\n      q = new BigNumber(s);\r\n      qc = q.c = [];\r\n      e = x.e - y.e;\r\n      s = dp + e + 1;\r\n\r\n      if (!base) {\r\n        base = BASE;\r\n        e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\r\n        s = s / LOG_BASE | 0;\r\n      }\r\n\r\n      // Result exponent may be one less then the current value of e.\r\n      // The coefficients of the BigNumbers from convertBase may have trailing zeros.\r\n      for (i = 0; yc[i] == (xc[i] || 0); i++);\r\n\r\n      if (yc[i] > (xc[i] || 0)) e--;\r\n\r\n      if (s < 0) {\r\n        qc.push(1);\r\n        more = true;\r\n      } else {\r\n        xL = xc.length;\r\n        yL = yc.length;\r\n        i = 0;\r\n        s += 2;\r\n\r\n        // Normalise xc and yc so highest order digit of yc is >= base / 2.\r\n\r\n        n = mathfloor(base / (yc[0] + 1));\r\n\r\n        // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\r\n        // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\r\n        if (n > 1) {\r\n          yc = multiply(yc, n, base);\r\n          xc = multiply(xc, n, base);\r\n          yL = yc.length;\r\n          xL = xc.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xc.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL; rem[remL++] = 0);\r\n        yz = yc.slice();\r\n        yz = [0].concat(yz);\r\n        yc0 = yc[0];\r\n        if (yc[1] >= base / 2) yc0++;\r\n        // Not necessary, but to prevent trial digit n > base, when using base 3.\r\n        // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\r\n\r\n        do {\r\n          n = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yc, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, n.\r\n\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // n is how many times the divisor goes into the current remainder.\r\n            n = mathfloor(rem0 / yc0);\r\n\r\n            //  Algorithm:\r\n            //  product = divisor multiplied by trial digit (n).\r\n            //  Compare product and remainder.\r\n            //  If product is greater than remainder:\r\n            //    Subtract divisor from product, decrement trial digit.\r\n            //  Subtract product from remainder.\r\n            //  If product was less than remainder at the last compare:\r\n            //    Compare new remainder and divisor.\r\n            //    If remainder is greater than divisor:\r\n            //      Subtract divisor from remainder, increment trial digit.\r\n\r\n            if (n > 1) {\r\n\r\n              // n may be > base only when base is 3.\r\n              if (n >= base) n = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiply(yc, n, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              // If product > remainder then trial digit n too high.\r\n              // n is 1 too high about 5% of the time, and is not known to have\r\n              // ever been more than 1 too high.\r\n              while (compare(prod, rem, prodL, remL) == 1) {\r\n                n--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yc, prodL, base);\r\n                prodL = prod.length;\r\n                cmp = 1;\r\n              }\r\n            } else {\r\n\r\n              // n is 0 or 1, cmp is -1.\r\n              // If n is 0, there is no need to compare yc and rem again below,\r\n              // so change cmp to 1 to avoid it.\r\n              // If n is 1, leave cmp as -1, so yc and rem are compared again.\r\n              if (n == 0) {\r\n\r\n                // divisor < remainder, so n must be at least 1.\r\n                cmp = n = 1;\r\n              }\r\n\r\n              // product = divisor\r\n              prod = yc.slice();\r\n              prodL = prod.length;\r\n            }\r\n\r\n            if (prodL < remL) prod = [0].concat(prod);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n            remL = rem.length;\r\n\r\n             // If product was < remainder.\r\n            if (cmp == -1) {\r\n\r\n              // Compare divisor and new remainder.\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              // Trial digit n too low.\r\n              // n is 1 too low about 5% of the time, and very rarely 2 too low.\r\n              while (compare(yc, rem, yL, remL) < 1) {\r\n                n++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yc, remL, base);\r\n                remL = rem.length;\r\n              }\r\n            }\r\n          } else if (cmp === 0) {\r\n            n++;\r\n            rem = [0];\r\n          } // else cmp === 1 and n will be 0\r\n\r\n          // Add the next digit, n, to the result array.\r\n          qc[i++] = n;\r\n\r\n          // Update the remainder.\r\n          if (rem[0]) {\r\n            rem[remL++] = xc[xi] || 0;\r\n          } else {\r\n            rem = [xc[xi]];\r\n            remL = 1;\r\n          }\r\n        } while ((xi++ < xL || rem[0] != null) && s--);\r\n\r\n        more = rem[0] != null;\r\n\r\n        // Leading zero?\r\n        if (!qc[0]) qc.splice(0, 1);\r\n      }\r\n\r\n      if (base == BASE) {\r\n\r\n        // To calculate q.e, first get the number of digits of qc[0].\r\n        for (i = 1, s = qc[0]; s >= 10; s /= 10, i++);\r\n\r\n        round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\r\n\r\n      // Caller is convertBase.\r\n      } else {\r\n        q.e = e;\r\n        q.r = +more;\r\n      }\r\n\r\n      return q;\r\n    };\r\n  })();\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n   * notation rounded to the specified decimal places or significant digits.\r\n   *\r\n   * n: a BigNumber.\r\n   * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n   * rm: the rounding mode.\r\n   * id: 1 (toExponential) or 2 (toPrecision).\r\n   */\r\n  function format(n, i, rm, id) {\r\n    var c0, e, ne, len, str;\r\n\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n\r\n    if (!n.c) return n.toString();\r\n\r\n    c0 = n.c[0];\r\n    ne = n.e;\r\n\r\n    if (i == null) {\r\n      str = coeffToString(n.c);\r\n      str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS)\r\n       ? toExponential(str, ne)\r\n       : toFixedPoint(str, ne, '0');\r\n    } else {\r\n      n = round(new BigNumber(n), i, rm);\r\n\r\n      // n.e may have changed if the value was rounded up.\r\n      e = n.e;\r\n\r\n      str = coeffToString(n.c);\r\n      len = str.length;\r\n\r\n      // toPrecision returns exponential notation if the number of significant digits\r\n      // specified is less than the number of digits necessary to represent the integer\r\n      // part of the value in fixed-point notation.\r\n\r\n      // Exponential notation.\r\n      if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\r\n\r\n        // Append zeros?\r\n        for (; len < i; str += '0', len++);\r\n        str = toExponential(str, e);\r\n\r\n      // Fixed-point notation.\r\n      } else {\r\n        i -= ne;\r\n        str = toFixedPoint(str, e, '0');\r\n\r\n        // Append zeros?\r\n        if (e + 1 > len) {\r\n          if (--i > 0) for (str += '.'; i--; str += '0');\r\n        } else {\r\n          i += e - len;\r\n          if (i > 0) {\r\n            if (e + 1 == len) str += '.';\r\n            for (; i--; str += '0');\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return n.s < 0 && c0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // Handle BigNumber.max and BigNumber.min.\r\n  // If any number is NaN, return NaN.\r\n  function maxOrMin(args, n) {\r\n    var k, y,\r\n      i = 1,\r\n      x = new BigNumber(args[0]);\r\n\r\n    for (; i < args.length; i++) {\r\n      y = new BigNumber(args[i]);\r\n      if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\r\n        x = y;\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  /*\r\n   * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n   * Called by minus, plus and times.\r\n   */\r\n  function normalise(n, c, e) {\r\n    var i = 1,\r\n      j = c.length;\r\n\r\n     // Remove trailing zeros.\r\n    for (; !c[--j]; c.pop());\r\n\r\n    // Calculate the base 10 exponent. First get the number of digits of c[0].\r\n    for (j = c[0]; j >= 10; j /= 10, i++);\r\n\r\n    // Overflow?\r\n    if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\r\n\r\n      // Infinity.\r\n      n.c = n.e = null;\r\n\r\n    // Underflow?\r\n    } else if (e < MIN_EXP) {\r\n\r\n      // Zero.\r\n      n.c = [n.e = 0];\r\n    } else {\r\n      n.e = e;\r\n      n.c = c;\r\n    }\r\n\r\n    return n;\r\n  }\r\n\r\n\r\n  // Handle values that fail the validity test in BigNumber.\r\n  parseNumeric = (function () {\r\n    var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i,\r\n      dotAfter = /^([^.]+)\\.$/,\r\n      dotBefore = /^\\.([^.]+)$/,\r\n      isInfinityOrNaN = /^-?(Infinity|NaN)$/,\r\n      whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\r\n\r\n    return function (x, str, isNum, b) {\r\n      var base,\r\n        s = isNum ? str : str.replace(whitespaceOrPlus, '');\r\n\r\n      // No exception on ±Infinity or NaN.\r\n      if (isInfinityOrNaN.test(s)) {\r\n        x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\r\n      } else {\r\n        if (!isNum) {\r\n\r\n          // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\r\n          s = s.replace(basePrefix, function (m, p1, p2) {\r\n            base = (p2 = p2.toLowerCase()) == 'x' ? 16 : p2 == 'b' ? 2 : 8;\r\n            return !b || b == base ? p1 : m;\r\n          });\r\n\r\n          if (b) {\r\n            base = b;\r\n\r\n            // E.g. '1.' to '1', '.1' to '0.1'\r\n            s = s.replace(dotAfter, '$1').replace(dotBefore, '0.$1');\r\n          }\r\n\r\n          if (str != s) return new BigNumber(s, base);\r\n        }\r\n\r\n        // '[BigNumber Error] Not a number: {n}'\r\n        // '[BigNumber Error] Not a base {b} number: {n}'\r\n        if (BigNumber.DEBUG) {\r\n          throw Error\r\n            (bignumberError + 'Not a' + (b ? ' base ' + b : '') + ' number: ' + str);\r\n        }\r\n\r\n        // NaN\r\n        x.s = null;\r\n      }\r\n\r\n      x.c = x.e = null;\r\n    }\r\n  })();\r\n\r\n\r\n  /*\r\n   * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n   * If r is truthy, it is known that there are more digits after the rounding digit.\r\n   */\r\n  function round(x, sd, rm, r) {\r\n    var d, i, j, k, n, ni, rd,\r\n      xc = x.c,\r\n      pows10 = POWS_TEN;\r\n\r\n    // if x is not Infinity or NaN...\r\n    if (xc) {\r\n\r\n      // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      // n is a base 1e14 number, the value of the element of array x.c containing rd.\r\n      // ni is the index of n within x.c.\r\n      // d is the number of digits of n.\r\n      // i is the index of rd within n including leading zeros.\r\n      // j is the actual index of rd within n (if < 0, rd is a leading zero).\r\n      out: {\r\n\r\n        // Get the number of digits of the first element of xc.\r\n        for (d = 1, k = xc[0]; k >= 10; k /= 10, d++);\r\n        i = sd - d;\r\n\r\n        // If the rounding digit is in the first element of xc...\r\n        if (i < 0) {\r\n          i += LOG_BASE;\r\n          j = sd;\r\n          n = xc[ni = 0];\r\n\r\n          // Get the rounding digit at index j of n.\r\n          rd = mathfloor(n / pows10[d - j - 1] % 10);\r\n        } else {\r\n          ni = mathceil((i + 1) / LOG_BASE);\r\n\r\n          if (ni >= xc.length) {\r\n\r\n            if (r) {\r\n\r\n              // Needed by sqrt.\r\n              for (; xc.length <= ni; xc.push(0));\r\n              n = rd = 0;\r\n              d = 1;\r\n              i %= LOG_BASE;\r\n              j = i - LOG_BASE + 1;\r\n            } else {\r\n              break out;\r\n            }\r\n          } else {\r\n            n = k = xc[ni];\r\n\r\n            // Get the number of digits of n.\r\n            for (d = 1; k >= 10; k /= 10, d++);\r\n\r\n            // Get the index of rd within n.\r\n            i %= LOG_BASE;\r\n\r\n            // Get the index of rd within n, adjusted for leading zeros.\r\n            // The number of leading zeros of n is given by LOG_BASE - d.\r\n            j = i - LOG_BASE + d;\r\n\r\n            // Get the rounding digit at index j of n.\r\n            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\r\n          }\r\n        }\r\n\r\n        r = r || sd < 0 ||\r\n\r\n        // Are there any non-zero digits after the rounding digit?\r\n        // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\r\n        // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\r\n         xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\r\n\r\n        r = rm < 4\r\n         ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n         : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 &&\r\n\r\n          // Check whether the digit to the left of the rounding digit is odd.\r\n          ((i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10) & 1 ||\r\n           rm == (x.s < 0 ? 8 : 7));\r\n\r\n        if (sd < 1 || !xc[0]) {\r\n          xc.length = 0;\r\n\r\n          if (r) {\r\n\r\n            // Convert sd to decimal places.\r\n            sd -= x.e + 1;\r\n\r\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\r\n            x.e = -sd || 0;\r\n          } else {\r\n\r\n            // Zero.\r\n            xc[0] = x.e = 0;\r\n          }\r\n\r\n          return x;\r\n        }\r\n\r\n        // Remove excess digits.\r\n        if (i == 0) {\r\n          xc.length = ni;\r\n          k = 1;\r\n          ni--;\r\n        } else {\r\n          xc.length = ni + 1;\r\n          k = pows10[LOG_BASE - i];\r\n\r\n          // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n          // j > 0 means i > number of leading zeros of n.\r\n          xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\r\n        }\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          for (; ;) {\r\n\r\n            // If the digit to be rounded up is in the first element of xc...\r\n            if (ni == 0) {\r\n\r\n              // i will be the length of xc[0] before k is added.\r\n              for (i = 1, j = xc[0]; j >= 10; j /= 10, i++);\r\n              j = xc[0] += k;\r\n              for (k = 1; j >= 10; j /= 10, k++);\r\n\r\n              // if i != k the length has increased.\r\n              if (i != k) {\r\n                x.e++;\r\n                if (xc[0] == BASE) xc[0] = 1;\r\n              }\r\n\r\n              break;\r\n            } else {\r\n              xc[ni] += k;\r\n              if (xc[ni] != BASE) break;\r\n              xc[ni--] = 0;\r\n              k = 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Remove trailing zeros.\r\n        for (i = xc.length; xc[--i] === 0; xc.pop());\r\n      }\r\n\r\n      // Overflow? Infinity.\r\n      if (x.e > MAX_EXP) {\r\n        x.c = x.e = null;\r\n\r\n      // Underflow? Zero.\r\n      } else if (x.e < MIN_EXP) {\r\n        x.c = [x.e = 0];\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  function valueOf(n) {\r\n    var str,\r\n      e = n.e;\r\n\r\n    if (e === null) return n.toString();\r\n\r\n    str = coeffToString(n.c);\r\n\r\n    str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n      ? toExponential(str, e)\r\n      : toFixedPoint(str, e, '0');\r\n\r\n    return n.s < 0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // PROTOTYPE/INSTANCE METHODS\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n   */\r\n  P.absoluteValue = P.abs = function () {\r\n    var x = new BigNumber(this);\r\n    if (x.s < 0) x.s = 1;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return\r\n   *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   *   0 if they have the same value,\r\n   *   or null if the value of either is NaN.\r\n   */\r\n  P.comparedTo = function (y, b) {\r\n    return compare(this, new BigNumber(y, b));\r\n  };\r\n\r\n\r\n  /*\r\n   * If dp is undefined or null or true or false, return the number of decimal places of the\r\n   * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   *\r\n   * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.decimalPlaces = P.dp = function (dp, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), dp + x.e + 1, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last number.\r\n    if (v = c[v]) for (; v % 10 == 0; v /= 10, n--);\r\n    if (n < 0) n = 0;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n / 0 = I\r\n   *  n / N = N\r\n   *  n / I = 0\r\n   *  0 / n = 0\r\n   *  0 / 0 = N\r\n   *  0 / N = N\r\n   *  0 / I = 0\r\n   *  N / n = N\r\n   *  N / 0 = N\r\n   *  N / N = N\r\n   *  N / I = N\r\n   *  I / n = I\r\n   *  I / 0 = I\r\n   *  I / N = N\r\n   *  I / I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n   * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.dividedBy = P.div = function (y, b) {\r\n    return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n   * BigNumber by the value of BigNumber(y, b).\r\n   */\r\n  P.dividedToIntegerBy = P.idiv = function (y, b) {\r\n    return div(this, new BigNumber(y, b), 0, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n   *\r\n   * If m is present, return the result modulo m.\r\n   * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n   *\r\n   * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n   * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n   *\r\n   * n {number|string|BigNumber} The exponent. An integer.\r\n   * [m] {number|string|BigNumber} The modulus.\r\n   *\r\n   * '[BigNumber Error] Exponent not an integer: {n}'\r\n   */\r\n  P.exponentiatedBy = P.pow = function (n, m) {\r\n    var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y,\r\n      x = this;\r\n\r\n    n = new BigNumber(n);\r\n\r\n    // Allow NaN and ±Infinity, but not other non-integers.\r\n    if (n.c && !n.isInteger()) {\r\n      throw Error\r\n        (bignumberError + 'Exponent not an integer: ' + valueOf(n));\r\n    }\r\n\r\n    if (m != null) m = new BigNumber(m);\r\n\r\n    // Exponent of MAX_SAFE_INTEGER is 15.\r\n    nIsBig = n.e > 14;\r\n\r\n    // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\r\n    if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\r\n\r\n      // The sign of the result of pow when x is negative depends on the evenness of n.\r\n      // If +n overflows to ±Infinity, the evenness of n would be not be known.\r\n      y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\r\n      return m ? y.mod(m) : y;\r\n    }\r\n\r\n    nIsNeg = n.s < 0;\r\n\r\n    if (m) {\r\n\r\n      // x % m returns NaN if abs(m) is zero, or m is NaN.\r\n      if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\r\n\r\n      isModExp = !nIsNeg && x.isInteger() && m.isInteger();\r\n\r\n      if (isModExp) x = x.mod(m);\r\n\r\n    // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\r\n    // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\r\n    } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0\r\n      // [1, 240000000]\r\n      ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7\r\n      // [80000000000000]  [99999750000000]\r\n      : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\r\n\r\n      // If x is negative and n is odd, k = -0, else k = 0.\r\n      k = x.s < 0 && isOdd(n) ? -0 : 0;\r\n\r\n      // If x >= 1, k = ±Infinity.\r\n      if (x.e > -1) k = 1 / k;\r\n\r\n      // If n is negative return ±0, else return ±Infinity.\r\n      return new BigNumber(nIsNeg ? 1 / k : k);\r\n\r\n    } else if (POW_PRECISION) {\r\n\r\n      // Truncating each coefficient array to a length of k after each multiplication\r\n      // equates to truncating significant digits to POW_PRECISION + [28, 41],\r\n      // i.e. there will be a minimum of 28 guard digits retained.\r\n      k = mathceil(POW_PRECISION / LOG_BASE + 2);\r\n    }\r\n\r\n    if (nIsBig) {\r\n      half = new BigNumber(0.5);\r\n      if (nIsNeg) n.s = 1;\r\n      nIsOdd = isOdd(n);\r\n    } else {\r\n      i = Math.abs(+valueOf(n));\r\n      nIsOdd = i % 2;\r\n    }\r\n\r\n    y = new BigNumber(ONE);\r\n\r\n    // Performs 54 loop iterations for n of 9007199254740991.\r\n    for (; ;) {\r\n\r\n      if (nIsOdd) {\r\n        y = y.times(x);\r\n        if (!y.c) break;\r\n\r\n        if (k) {\r\n          if (y.c.length > k) y.c.length = k;\r\n        } else if (isModExp) {\r\n          y = y.mod(m);    //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\r\n        }\r\n      }\r\n\r\n      if (i) {\r\n        i = mathfloor(i / 2);\r\n        if (i === 0) break;\r\n        nIsOdd = i % 2;\r\n      } else {\r\n        n = n.times(half);\r\n        round(n, n.e + 1, 1);\r\n\r\n        if (n.e > 14) {\r\n          nIsOdd = isOdd(n);\r\n        } else {\r\n          i = +valueOf(n);\r\n          if (i === 0) break;\r\n          nIsOdd = i % 2;\r\n        }\r\n      }\r\n\r\n      x = x.times(x);\r\n\r\n      if (k) {\r\n        if (x.c && x.c.length > k) x.c.length = k;\r\n      } else if (isModExp) {\r\n        x = x.mod(m);    //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\r\n      }\r\n    }\r\n\r\n    if (isModExp) return y;\r\n    if (nIsNeg) y = ONE.div(y);\r\n\r\n    return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n   * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n   */\r\n  P.integerValue = function (rm) {\r\n    var n = new BigNumber(this);\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n    return round(n, n.e + 1, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isEqualTo = P.eq = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n   */\r\n  P.isFinite = function () {\r\n    return !!this.c;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isGreaterThan = P.gt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isGreaterThanOrEqualTo = P.gte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\r\n\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n   */\r\n  P.isInteger = function () {\r\n    return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isLessThan = P.lt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isLessThanOrEqualTo = P.lte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n   */\r\n  P.isNaN = function () {\r\n    return !this.s;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is negative, otherwise return false.\r\n   */\r\n  P.isNegative = function () {\r\n    return this.s < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is positive, otherwise return false.\r\n   */\r\n  P.isPositive = function () {\r\n    return this.s > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n   */\r\n  P.isZero = function () {\r\n    return !!this.c && this.c[0] == 0;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n - 0 = n\r\n   *  n - N = N\r\n   *  n - I = -I\r\n   *  0 - n = -n\r\n   *  0 - 0 = 0\r\n   *  0 - N = N\r\n   *  0 - I = -I\r\n   *  N - n = N\r\n   *  N - 0 = N\r\n   *  N - N = N\r\n   *  N - I = N\r\n   *  I - n = I\r\n   *  I - 0 = I\r\n   *  I - N = N\r\n   *  I - I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.minus = function (y, b) {\r\n    var i, j, t, xLTy,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n    if (a != b) {\r\n      y.s = -b;\r\n      return x.plus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Either Infinity?\r\n      if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\r\n\r\n      // Either zero?\r\n      if (!xc[0] || !yc[0]) {\r\n\r\n        // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n        return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x :\r\n\r\n         // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\r\n         ROUNDING_MODE == 3 ? -0 : 0);\r\n      }\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Determine which is the bigger number.\r\n    if (a = xe - ye) {\r\n\r\n      if (xLTy = a < 0) {\r\n        a = -a;\r\n        t = xc;\r\n      } else {\r\n        ye = xe;\r\n        t = yc;\r\n      }\r\n\r\n      t.reverse();\r\n\r\n      // Prepend zeros to equalise exponents.\r\n      for (b = a; b--; t.push(0));\r\n      t.reverse();\r\n    } else {\r\n\r\n      // Exponents equal. Check digit by digit.\r\n      j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\r\n\r\n      for (a = b = 0; b < j; b++) {\r\n\r\n        if (xc[b] != yc[b]) {\r\n          xLTy = xc[b] < yc[b];\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // x < y? Point xc to the array of the bigger number.\r\n    if (xLTy) {\r\n      t = xc;\r\n      xc = yc;\r\n      yc = t;\r\n      y.s = -y.s;\r\n    }\r\n\r\n    b = (j = yc.length) - (i = xc.length);\r\n\r\n    // Append zeros to xc if shorter.\r\n    // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\r\n    if (b > 0) for (; b--; xc[i++] = 0);\r\n    b = BASE - 1;\r\n\r\n    // Subtract yc from xc.\r\n    for (; j > a;) {\r\n\r\n      if (xc[--j] < yc[j]) {\r\n        for (i = j; i && !xc[--i]; xc[i] = b);\r\n        --xc[i];\r\n        xc[j] += BASE;\r\n      }\r\n\r\n      xc[j] -= yc[j];\r\n    }\r\n\r\n    // Remove leading zeros and adjust exponent accordingly.\r\n    for (; xc[0] == 0; xc.splice(0, 1), --ye);\r\n\r\n    // Zero?\r\n    if (!xc[0]) {\r\n\r\n      // Following IEEE 754 (2008) 6.3,\r\n      // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\r\n      y.s = ROUNDING_MODE == 3 ? -1 : 1;\r\n      y.c = [y.e = 0];\r\n      return y;\r\n    }\r\n\r\n    // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\r\n    // for finite x and y.\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   *   n % 0 =  N\r\n   *   n % N =  N\r\n   *   n % I =  n\r\n   *   0 % n =  0\r\n   *  -0 % n = -0\r\n   *   0 % 0 =  N\r\n   *   0 % N =  N\r\n   *   0 % I =  0\r\n   *   N % n =  N\r\n   *   N % 0 =  N\r\n   *   N % N =  N\r\n   *   N % I =  N\r\n   *   I % n =  N\r\n   *   I % 0 =  N\r\n   *   I % N =  N\r\n   *   I % I =  N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n   * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n   */\r\n  P.modulo = P.mod = function (y, b) {\r\n    var q, s,\r\n      x = this;\r\n\r\n    y = new BigNumber(y, b);\r\n\r\n    // Return NaN if x is Infinity or NaN, or y is NaN or zero.\r\n    if (!x.c || !y.s || y.c && !y.c[0]) {\r\n      return new BigNumber(NaN);\r\n\r\n    // Return x if y is Infinity or x is zero.\r\n    } else if (!y.c || x.c && !x.c[0]) {\r\n      return new BigNumber(x);\r\n    }\r\n\r\n    if (MODULO_MODE == 9) {\r\n\r\n      // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n      // r = x - qy    where  0 <= r < abs(y)\r\n      s = y.s;\r\n      y.s = 1;\r\n      q = div(x, y, 0, 3);\r\n      y.s = s;\r\n      q.s *= s;\r\n    } else {\r\n      q = div(x, y, 0, MODULO_MODE);\r\n    }\r\n\r\n    y = x.minus(q.times(y));\r\n\r\n    // To match JavaScript %, ensure sign of zero is sign of dividend.\r\n    if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\r\n\r\n    return y;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n * 0 = 0\r\n   *  n * N = N\r\n   *  n * I = I\r\n   *  0 * n = 0\r\n   *  0 * 0 = 0\r\n   *  0 * N = N\r\n   *  0 * I = N\r\n   *  N * n = N\r\n   *  N * 0 = N\r\n   *  N * N = N\r\n   *  N * I = N\r\n   *  I * n = I\r\n   *  I * 0 = N\r\n   *  I * N = N\r\n   *  I * I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n   * of BigNumber(y, b).\r\n   */\r\n  P.multipliedBy = P.times = function (y, b) {\r\n    var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc,\r\n      base, sqrtBase,\r\n      x = this,\r\n      xc = x.c,\r\n      yc = (y = new BigNumber(y, b)).c;\r\n\r\n    // Either NaN, ±Infinity or ±0?\r\n    if (!xc || !yc || !xc[0] || !yc[0]) {\r\n\r\n      // Return NaN if either is NaN, or one is 0 and the other is Infinity.\r\n      if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\r\n        y.c = y.e = y.s = null;\r\n      } else {\r\n        y.s *= x.s;\r\n\r\n        // Return ±Infinity if either is ±Infinity.\r\n        if (!xc || !yc) {\r\n          y.c = y.e = null;\r\n\r\n        // Return ±0 if either is ±0.\r\n        } else {\r\n          y.c = [0];\r\n          y.e = 0;\r\n        }\r\n      }\r\n\r\n      return y;\r\n    }\r\n\r\n    e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\r\n    y.s *= x.s;\r\n    xcL = xc.length;\r\n    ycL = yc.length;\r\n\r\n    // Ensure xc points to longer array and xcL to its length.\r\n    if (xcL < ycL) {\r\n      zc = xc;\r\n      xc = yc;\r\n      yc = zc;\r\n      i = xcL;\r\n      xcL = ycL;\r\n      ycL = i;\r\n    }\r\n\r\n    // Initialise the result array with zeros.\r\n    for (i = xcL + ycL, zc = []; i--; zc.push(0));\r\n\r\n    base = BASE;\r\n    sqrtBase = SQRT_BASE;\r\n\r\n    for (i = ycL; --i >= 0;) {\r\n      c = 0;\r\n      ylo = yc[i] % sqrtBase;\r\n      yhi = yc[i] / sqrtBase | 0;\r\n\r\n      for (k = xcL, j = i + k; j > i;) {\r\n        xlo = xc[--k] % sqrtBase;\r\n        xhi = xc[k] / sqrtBase | 0;\r\n        m = yhi * xlo + xhi * ylo;\r\n        xlo = ylo * xlo + ((m % sqrtBase) * sqrtBase) + zc[j] + c;\r\n        c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\r\n        zc[j--] = xlo % base;\r\n      }\r\n\r\n      zc[j] = c;\r\n    }\r\n\r\n    if (c) {\r\n      ++e;\r\n    } else {\r\n      zc.splice(0, 1);\r\n    }\r\n\r\n    return normalise(y, zc, e);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n   * i.e. multiplied by -1.\r\n   */\r\n  P.negated = function () {\r\n    var x = new BigNumber(this);\r\n    x.s = -x.s || null;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n + 0 = n\r\n   *  n + N = N\r\n   *  n + I = I\r\n   *  0 + n = n\r\n   *  0 + 0 = 0\r\n   *  0 + N = N\r\n   *  0 + I = I\r\n   *  N + n = N\r\n   *  N + 0 = N\r\n   *  N + N = N\r\n   *  N + I = N\r\n   *  I + n = I\r\n   *  I + 0 = I\r\n   *  I + N = N\r\n   *  I + I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.plus = function (y, b) {\r\n    var t,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n     if (a != b) {\r\n      y.s = -b;\r\n      return x.minus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Return ±Infinity if either ±Infinity.\r\n      if (!xc || !yc) return new BigNumber(a / 0);\r\n\r\n      // Either zero?\r\n      // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n      if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\r\n    if (a = xe - ye) {\r\n      if (a > 0) {\r\n        ye = xe;\r\n        t = yc;\r\n      } else {\r\n        a = -a;\r\n        t = xc;\r\n      }\r\n\r\n      t.reverse();\r\n      for (; a--; t.push(0));\r\n      t.reverse();\r\n    }\r\n\r\n    a = xc.length;\r\n    b = yc.length;\r\n\r\n    // Point xc to the longer array, and b to the shorter length.\r\n    if (a - b < 0) {\r\n      t = yc;\r\n      yc = xc;\r\n      xc = t;\r\n      b = a;\r\n    }\r\n\r\n    // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\r\n    for (a = 0; b;) {\r\n      a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\r\n      xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\r\n    }\r\n\r\n    if (a) {\r\n      xc = [a].concat(xc);\r\n      ++ye;\r\n    }\r\n\r\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n    // ye = MAX_EXP + 1 possible\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   * If sd is undefined or null or true or false, return the number of significant digits of\r\n   * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   * If sd is true include integer-part trailing zeros in the count.\r\n   *\r\n   * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n   *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.precision = P.sd = function (sd, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (sd != null && sd !== !!sd) {\r\n      intCheck(sd, 1, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), sd, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    v = c.length - 1;\r\n    n = v * LOG_BASE + 1;\r\n\r\n    if (v = c[v]) {\r\n\r\n      // Subtract the number of trailing zeros of the last element.\r\n      for (; v % 10 == 0; v /= 10, n--);\r\n\r\n      // Add the number of digits of the first element.\r\n      for (v = c[0]; v >= 10; v /= 10, n++);\r\n    }\r\n\r\n    if (sd && x.e + 1 > n) n = x.e + 1;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n   * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n   *\r\n   * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n   */\r\n  P.shiftedBy = function (k) {\r\n    intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\r\n    return this.times('1e' + k);\r\n  };\r\n\r\n\r\n  /*\r\n   *  sqrt(-n) =  N\r\n   *  sqrt(N) =  N\r\n   *  sqrt(-I) =  N\r\n   *  sqrt(I) =  I\r\n   *  sqrt(0) =  0\r\n   *  sqrt(-0) = -0\r\n   *\r\n   * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n   * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.squareRoot = P.sqrt = function () {\r\n    var m, n, r, rep, t,\r\n      x = this,\r\n      c = x.c,\r\n      s = x.s,\r\n      e = x.e,\r\n      dp = DECIMAL_PLACES + 4,\r\n      half = new BigNumber('0.5');\r\n\r\n    // Negative/NaN/Infinity/zero?\r\n    if (s !== 1 || !c || !c[0]) {\r\n      return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\r\n    }\r\n\r\n    // Initial estimate.\r\n    s = Math.sqrt(+valueOf(x));\r\n\r\n    // Math.sqrt underflow/overflow?\r\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n    if (s == 0 || s == 1 / 0) {\r\n      n = coeffToString(c);\r\n      if ((n.length + e) % 2 == 0) n += '0';\r\n      s = Math.sqrt(+n);\r\n      e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n      if (s == 1 / 0) {\r\n        n = '5e' + e;\r\n      } else {\r\n        n = s.toExponential();\r\n        n = n.slice(0, n.indexOf('e') + 1) + e;\r\n      }\r\n\r\n      r = new BigNumber(n);\r\n    } else {\r\n      r = new BigNumber(s + '');\r\n    }\r\n\r\n    // Check for zero.\r\n    // r could be zero if MIN_EXP is changed after the this value was created.\r\n    // This would cause a division by zero (x/t) and hence Infinity below, which would cause\r\n    // coeffToString to throw.\r\n    if (r.c[0]) {\r\n      e = r.e;\r\n      s = e + dp;\r\n      if (s < 3) s = 0;\r\n\r\n      // Newton-Raphson iteration.\r\n      for (; ;) {\r\n        t = r;\r\n        r = half.times(t.plus(div(x, t, dp, 1)));\r\n\r\n        if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\r\n\r\n          // The exponent of r may here be one less than the final result exponent,\r\n          // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\r\n          // are indexed correctly.\r\n          if (r.e < e) --s;\r\n          n = n.slice(s - 3, s + 1);\r\n\r\n          // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\r\n          // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\r\n          // iteration.\r\n          if (n == '9999' || !rep && n == '4999') {\r\n\r\n            // On the first iteration only, check to see if rounding up gives the\r\n            // exact result as the nines may infinitely repeat.\r\n            if (!rep) {\r\n              round(t, t.e + DECIMAL_PLACES + 2, 0);\r\n\r\n              if (t.times(t).eq(x)) {\r\n                r = t;\r\n                break;\r\n              }\r\n            }\r\n\r\n            dp += 4;\r\n            s += 4;\r\n            rep = 1;\r\n          } else {\r\n\r\n            // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\r\n            // result. If not, then there are further digits and m will be truthy.\r\n            if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n              // Truncate to the first rounding digit.\r\n              round(r, r.e + DECIMAL_PLACES + 2, 1);\r\n              m = !r.times(r).eq(x);\r\n            }\r\n\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in exponential notation and\r\n   * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toExponential = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp++;\r\n    }\r\n    return format(this, dp, rm, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n   * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n   * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toFixed = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp = dp + this.e + 1;\r\n    }\r\n    return format(this, dp, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n   * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n   * of the format or FORMAT object (see BigNumber.set).\r\n   *\r\n   * The formatting object may contain some or all of the properties shown below.\r\n   *\r\n   * FORMAT = {\r\n   *   prefix: '',\r\n   *   groupSize: 3,\r\n   *   secondaryGroupSize: 0,\r\n   *   groupSeparator: ',',\r\n   *   decimalSeparator: '.',\r\n   *   fractionGroupSize: 0,\r\n   *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n   *   suffix: ''\r\n   * };\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   * [format] {object} Formatting options. See FORMAT pbject above.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   * '[BigNumber Error] Argument not an object: {format}'\r\n   */\r\n  P.toFormat = function (dp, rm, format) {\r\n    var str,\r\n      x = this;\r\n\r\n    if (format == null) {\r\n      if (dp != null && rm && typeof rm == 'object') {\r\n        format = rm;\r\n        rm = null;\r\n      } else if (dp && typeof dp == 'object') {\r\n        format = dp;\r\n        dp = rm = null;\r\n      } else {\r\n        format = FORMAT;\r\n      }\r\n    } else if (typeof format != 'object') {\r\n      throw Error\r\n        (bignumberError + 'Argument not an object: ' + format);\r\n    }\r\n\r\n    str = x.toFixed(dp, rm);\r\n\r\n    if (x.c) {\r\n      var i,\r\n        arr = str.split('.'),\r\n        g1 = +format.groupSize,\r\n        g2 = +format.secondaryGroupSize,\r\n        groupSeparator = format.groupSeparator || '',\r\n        intPart = arr[0],\r\n        fractionPart = arr[1],\r\n        isNeg = x.s < 0,\r\n        intDigits = isNeg ? intPart.slice(1) : intPart,\r\n        len = intDigits.length;\r\n\r\n      if (g2) {\r\n        i = g1;\r\n        g1 = g2;\r\n        g2 = i;\r\n        len -= i;\r\n      }\r\n\r\n      if (g1 > 0 && len > 0) {\r\n        i = len % g1 || g1;\r\n        intPart = intDigits.substr(0, i);\r\n        for (; i < len; i += g1) intPart += groupSeparator + intDigits.substr(i, g1);\r\n        if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\r\n        if (isNeg) intPart = '-' + intPart;\r\n      }\r\n\r\n      str = fractionPart\r\n       ? intPart + (format.decimalSeparator || '') + ((g2 = +format.fractionGroupSize)\r\n        ? fractionPart.replace(new RegExp('\\\\d{' + g2 + '}\\\\B', 'g'),\r\n         '$&' + (format.fractionGroupSeparator || ''))\r\n        : fractionPart)\r\n       : intPart;\r\n    }\r\n\r\n    return (format.prefix || '') + str + (format.suffix || '');\r\n  };\r\n\r\n\r\n  /*\r\n   * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n   * fraction with an integer numerator and an integer denominator.\r\n   * The denominator will be a positive non-zero value less than or equal to the specified\r\n   * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n   * the lowest value necessary to represent the number exactly.\r\n   *\r\n   * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n   *\r\n   * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n   */\r\n  P.toFraction = function (md) {\r\n    var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s,\r\n      x = this,\r\n      xc = x.c;\r\n\r\n    if (md != null) {\r\n      n = new BigNumber(md);\r\n\r\n      // Throw if md is less than one or is not an integer, unless it is Infinity.\r\n      if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\r\n        throw Error\r\n          (bignumberError + 'Argument ' +\r\n            (n.isInteger() ? 'out of range: ' : 'not an integer: ') + valueOf(n));\r\n      }\r\n    }\r\n\r\n    if (!xc) return new BigNumber(x);\r\n\r\n    d = new BigNumber(ONE);\r\n    n1 = d0 = new BigNumber(ONE);\r\n    d1 = n0 = new BigNumber(ONE);\r\n    s = coeffToString(xc);\r\n\r\n    // Determine initial denominator.\r\n    // d is a power of 10 and the minimum max denominator that specifies the value exactly.\r\n    e = d.e = s.length - x.e - 1;\r\n    d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\r\n    md = !md || n.comparedTo(d) > 0 ? (e > 0 ? d : n1) : n;\r\n\r\n    exp = MAX_EXP;\r\n    MAX_EXP = 1 / 0;\r\n    n = new BigNumber(s);\r\n\r\n    // n0 = d1 = 0\r\n    n0.c[0] = 0;\r\n\r\n    for (; ;)  {\r\n      q = div(n, d, 0, 1);\r\n      d2 = d0.plus(q.times(d1));\r\n      if (d2.comparedTo(md) == 1) break;\r\n      d0 = d1;\r\n      d1 = d2;\r\n      n1 = n0.plus(q.times(d2 = n1));\r\n      n0 = d2;\r\n      d = n.minus(q.times(d2 = d));\r\n      n = d2;\r\n    }\r\n\r\n    d2 = div(md.minus(d0), d1, 0, 1);\r\n    n0 = n0.plus(d2.times(n1));\r\n    d0 = d0.plus(d2.times(d1));\r\n    n0.s = n1.s = x.s;\r\n    e = e * 2;\r\n\r\n    // Determine which fraction is closer to x, n0/d0 or n1/d1\r\n    r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(\r\n        div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [n1, d1] : [n0, d0];\r\n\r\n    MAX_EXP = exp;\r\n\r\n    return r;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the value of this BigNumber converted to a number primitive.\r\n   */\r\n  P.toNumber = function () {\r\n    return +valueOf(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n   * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n   * necessary to represent the integer part of the value in fixed-point notation, then use\r\n   * exponential notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.toPrecision = function (sd, rm) {\r\n    if (sd != null) intCheck(sd, 1, MAX);\r\n    return format(this, sd, rm, 2);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n   * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n   * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n   * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n   * TO_EXP_NEG, return exponential notation.\r\n   *\r\n   * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n   *\r\n   * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n   */\r\n  P.toString = function (b) {\r\n    var str,\r\n      n = this,\r\n      s = n.s,\r\n      e = n.e;\r\n\r\n    // Infinity or NaN?\r\n    if (e === null) {\r\n      if (s) {\r\n        str = 'Infinity';\r\n        if (s < 0) str = '-' + str;\r\n      } else {\r\n        str = 'NaN';\r\n      }\r\n    } else {\r\n      if (b == null) {\r\n        str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n         ? toExponential(coeffToString(n.c), e)\r\n         : toFixedPoint(coeffToString(n.c), e, '0');\r\n      } else if (b === 10 && alphabetHasNormalDecimalDigits) {\r\n        n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\r\n        str = toFixedPoint(coeffToString(n.c), n.e, '0');\r\n      } else {\r\n        intCheck(b, 2, ALPHABET.length, 'Base');\r\n        str = convertBase(toFixedPoint(coeffToString(n.c), e, '0'), 10, b, s, true);\r\n      }\r\n\r\n      if (s < 0 && n.c[0]) str = '-' + str;\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return as toString, but do not accept a base argument, and include the minus sign for\r\n   * negative zero.\r\n   */\r\n  P.valueOf = P.toJSON = function () {\r\n    return valueOf(this);\r\n  };\r\n\r\n\r\n  P._isBigNumber = true;\r\n\r\n  P[Symbol.toStringTag] = 'BigNumber';\r\n\r\n  // Node.js v10.12.0+\r\n  P[Symbol.for('nodejs.util.inspect.custom')] = P.valueOf;\r\n\r\n  if (configObject != null) BigNumber.set(configObject);\r\n\r\n  return BigNumber;\r\n}\r\n\r\n\r\n// PRIVATE HELPER FUNCTIONS\r\n\r\n// These functions don't need access to variables,\r\n// e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\r\n\r\n\r\nfunction bitFloor(n) {\r\n  var i = n | 0;\r\n  return n > 0 || n === i ? i : i - 1;\r\n}\r\n\r\n\r\n// Return a coefficient array as a string of base 10 digits.\r\nfunction coeffToString(a) {\r\n  var s, z,\r\n    i = 1,\r\n    j = a.length,\r\n    r = a[0] + '';\r\n\r\n  for (; i < j;) {\r\n    s = a[i++] + '';\r\n    z = LOG_BASE - s.length;\r\n    for (; z--; s = '0' + s);\r\n    r += s;\r\n  }\r\n\r\n  // Determine trailing zeros.\r\n  for (j = r.length; r.charCodeAt(--j) === 48;);\r\n\r\n  return r.slice(0, j + 1 || 1);\r\n}\r\n\r\n\r\n// Compare the value of BigNumbers x and y.\r\nfunction compare(x, y) {\r\n  var a, b,\r\n    xc = x.c,\r\n    yc = y.c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either NaN?\r\n  if (!i || !j) return null;\r\n\r\n  a = xc && !xc[0];\r\n  b = yc && !yc[0];\r\n\r\n  // Either zero?\r\n  if (a || b) return a ? b ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  a = i < 0;\r\n  b = k == l;\r\n\r\n  // Either Infinity?\r\n  if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\r\n\r\n  // Compare exponents.\r\n  if (!b) return k > l ^ a ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0; i < j; i++) if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ a ? 1 : -1;\r\n}\r\n\r\n\r\n/*\r\n * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n */\r\nfunction intCheck(n, min, max, name) {\r\n  if (n < min || n > max || n !== mathfloor(n)) {\r\n    throw Error\r\n     (bignumberError + (name || 'Argument') + (typeof n == 'number'\r\n       ? n < min || n > max ? ' out of range: ' : ' not an integer: '\r\n       : ' not a primitive number: ') + String(n));\r\n  }\r\n}\r\n\r\n\r\n// Assumes finite n.\r\nfunction isOdd(n) {\r\n  var k = n.c.length - 1;\r\n  return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\r\n}\r\n\r\n\r\nfunction toExponential(str, e) {\r\n  return (str.length > 1 ? str.charAt(0) + '.' + str.slice(1) : str) +\r\n   (e < 0 ? 'e' : 'e+') + e;\r\n}\r\n\r\n\r\nfunction toFixedPoint(str, e, z) {\r\n  var len, zs;\r\n\r\n  // Negative exponent?\r\n  if (e < 0) {\r\n\r\n    // Prepend zeros.\r\n    for (zs = z + '.'; ++e; zs += z);\r\n    str = zs + str;\r\n\r\n  // Positive exponent\r\n  } else {\r\n    len = str.length;\r\n\r\n    // Append zeros.\r\n    if (++e > len) {\r\n      for (zs = z, e -= len; --e; zs += z);\r\n      str += zs;\r\n    } else if (e < len) {\r\n      str = str.slice(0, e) + '.' + str.slice(e);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// EXPORT\r\n\r\n\r\nvar BigNumber = clone();\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BigNumber);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmlnbnVtYmVyLmpzL2JpZ251bWJlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsdURBQXVEO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLHlCQUF5QjtBQUNqQyxVQUFVLFFBQVE7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsU0FBUztBQUN0QztBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0Esa0NBQWtDLG1EQUFtRCxHQUFHLEVBQUU7QUFDMUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUZBQXFGLEVBQUU7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixTQUFTO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsMEJBQTBCO0FBQzFDO0FBQ0E7QUFDQSwyQkFBMkIsNkJBQTZCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUZBQW1GLEVBQUU7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsUUFBUTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxlQUFlLEtBQUs7QUFDcEI7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsa0JBQWtCO0FBQzNDLHlCQUF5QixrQkFBa0I7QUFDM0MseUJBQXlCLGtCQUFrQjtBQUMzQyx5QkFBeUIsa0JBQWtCO0FBQzNDLHlCQUF5QixrQkFBa0I7QUFDM0MseUJBQXlCLGtCQUFrQjtBQUMzQyw0QkFBNEIsa0JBQWtCO0FBQzlDLHlCQUF5QixrQkFBa0I7QUFDM0M7QUFDQSx5QkFBeUIsa0JBQWtCO0FBQzNDLGlDQUFpQztBQUNqQyxpQ0FBaUM7QUFDakMsaUNBQWlDO0FBQ2pDLGlDQUFpQztBQUNqQyxpQ0FBaUM7QUFDakMsaUNBQWlDO0FBQ2pDLGlDQUFpQztBQUNqQyxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isd0NBQXdDO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixRQUFRO0FBQ25DLDhDQUE4QyxtREFBbUQsR0FBRyxFQUFFO0FBQ3RHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixRQUFRO0FBQ2xDLDZDQUE2QyxtREFBbUQsR0FBRyxFQUFFO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0EsOENBQThDLG1EQUFtRCxHQUFHLEVBQUU7QUFDdEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQSxxQ0FBcUMsa0VBQWtFLEdBQUcsRUFBRTtBQUM1RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLFNBQVM7QUFDNUIseURBQXlELEVBQUU7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsUUFBUTtBQUNoQywyQ0FBMkMsbURBQW1ELEdBQUcsRUFBRTtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsUUFBUTtBQUNsQyw2Q0FBNkMsbURBQW1ELEdBQUcsRUFBRTtBQUNyRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIscURBQXFELEVBQUU7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsaURBQWlELEVBQUU7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsZ0RBQWdELEVBQUU7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsNENBQTRDLEVBQUU7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixjQUFjO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxrQ0FBa0MsbURBQW1ELEdBQUcsR0FBRztBQUMzRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixNQUFNO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsTUFBTTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsTUFBTTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLHNCQUFzQixZQUFZO0FBQ2xDO0FBQ0E7QUFDQSw4QkFBOEIsU0FBUztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxnQkFBZ0I7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixnQ0FBZ0MsUUFBUTtBQUN4QztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLG9CQUFvQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixTQUFTO0FBQ3JDO0FBQ0E7QUFDQSw4QkFBOEIsUUFBUTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLElBQUk7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsMEJBQTBCLFFBQVE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxLQUFLO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsdUJBQXVCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1QkFBdUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFdBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLFNBQVM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxLQUFLO0FBQzdDLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsS0FBSztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsaUJBQWlCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0E7QUFDQSxtQkFBbUIsU0FBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLEVBQUU7QUFDL0MsMENBQTBDLEdBQUcsU0FBUyxFQUFFO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixTQUFTO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGlCQUFpQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixTQUFTO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsU0FBUztBQUM5QztBQUNBLDBCQUEwQixTQUFTO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGVBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGtDQUFrQyxtREFBbUQsR0FBRyxNQUFNO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsYUFBYTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5QkFBeUI7QUFDakMsVUFBVSx5QkFBeUI7QUFDbkM7QUFDQSxrREFBa0QsRUFBRTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1IseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxrQ0FBa0MsbURBQW1ELEdBQUcsR0FBRztBQUMzRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsS0FBSztBQUN2QjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixPQUFPO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsS0FBSztBQUMzQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQTtBQUNBLG9CQUFvQixlQUFlO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFlBQVk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsS0FBSztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixTQUFTO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLE1BQU07QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsS0FBSztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsRUFBRTtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLGdCQUFnQjtBQUN6QjtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGtDQUFrQyxtREFBbUQsR0FBRyxNQUFNO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBLHFCQUFxQixTQUFTO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsUUFBUTtBQUNoQjtBQUNBLGtDQUFrQyxtREFBbUQsR0FBRyxFQUFFO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBLDhDQUE4QyxLQUFLLE1BQU0sSUFBSTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDQSxrQ0FBa0MsbURBQW1ELEdBQUcsTUFBTTtBQUM5RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGtDQUFrQyxtREFBbUQsR0FBRyxNQUFNO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixlQUFlLFFBQVE7QUFDdkI7QUFDQSxrQ0FBa0MsbURBQW1ELEdBQUcsTUFBTTtBQUM5RixpREFBaUQsT0FBTztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxTQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxXQUFXO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx5QkFBeUI7QUFDcEM7QUFDQSxrQ0FBa0MsNkJBQTZCLEdBQUcsR0FBRztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGtDQUFrQyxtREFBbUQsR0FBRyxNQUFNO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLFFBQVE7QUFDbEI7QUFDQSw4QkFBOEIsbURBQW1ELEdBQUcsRUFBRTtBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxNQUFNO0FBQ2Y7QUFDQTtBQUNBLFdBQVcsS0FBSztBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix5QkFBeUI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxPQUFPO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixLQUFLO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixLQUFLO0FBQ2xDO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsaUVBQWUsU0FBUyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxiaWdudW1iZXIuanNcXGJpZ251bWJlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcclxuICogICAgICBiaWdudW1iZXIuanMgdjkuMy4wXHJcbiAqICAgICAgQSBKYXZhU2NyaXB0IGxpYnJhcnkgZm9yIGFyYml0cmFyeS1wcmVjaXNpb24gYXJpdGhtZXRpYy5cclxuICogICAgICBodHRwczovL2dpdGh1Yi5jb20vTWlrZU1jbC9iaWdudW1iZXIuanNcclxuICogICAgICBDb3B5cmlnaHQgKGMpIDIwMjUgTWljaGFlbCBNY2xhdWdobGluIDxNOGNoODhsQGdtYWlsLmNvbT5cclxuICogICAgICBNSVQgTGljZW5zZWQuXHJcbiAqXHJcbiAqICAgICAgQmlnTnVtYmVyLnByb3RvdHlwZSBtZXRob2RzICAgICB8ICBCaWdOdW1iZXIgbWV0aG9kc1xyXG4gKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIGFic29sdXRlVmFsdWUgICAgICAgICAgICBhYnMgICAgfCAgY2xvbmVcclxuICogICAgICBjb21wYXJlZFRvICAgICAgICAgICAgICAgICAgICAgIHwgIGNvbmZpZyAgICAgICAgICAgICAgIHNldFxyXG4gKiAgICAgIGRlY2ltYWxQbGFjZXMgICAgICAgICAgICBkcCAgICAgfCAgICAgIERFQ0lNQUxfUExBQ0VTXHJcbiAqICAgICAgZGl2aWRlZEJ5ICAgICAgICAgICAgICAgIGRpdiAgICB8ICAgICAgUk9VTkRJTkdfTU9ERVxyXG4gKiAgICAgIGRpdmlkZWRUb0ludGVnZXJCeSAgICAgICBpZGl2ICAgfCAgICAgIEVYUE9ORU5USUFMX0FUXHJcbiAqICAgICAgZXhwb25lbnRpYXRlZEJ5ICAgICAgICAgIHBvdyAgICB8ICAgICAgUkFOR0VcclxuICogICAgICBpbnRlZ2VyVmFsdWUgICAgICAgICAgICAgICAgICAgIHwgICAgICBDUllQVE9cclxuICogICAgICBpc0VxdWFsVG8gICAgICAgICAgICAgICAgZXEgICAgIHwgICAgICBNT0RVTE9fTU9ERVxyXG4gKiAgICAgIGlzRmluaXRlICAgICAgICAgICAgICAgICAgICAgICAgfCAgICAgIFBPV19QUkVDSVNJT05cclxuICogICAgICBpc0dyZWF0ZXJUaGFuICAgICAgICAgICAgZ3QgICAgIHwgICAgICBGT1JNQVRcclxuICogICAgICBpc0dyZWF0ZXJUaGFuT3JFcXVhbFRvICAgZ3RlICAgIHwgICAgICBBTFBIQUJFVFxyXG4gKiAgICAgIGlzSW50ZWdlciAgICAgICAgICAgICAgICAgICAgICAgfCAgaXNCaWdOdW1iZXJcclxuICogICAgICBpc0xlc3NUaGFuICAgICAgICAgICAgICAgbHQgICAgIHwgIG1heGltdW0gICAgICAgICAgICAgIG1heFxyXG4gKiAgICAgIGlzTGVzc1RoYW5PckVxdWFsVG8gICAgICBsdGUgICAgfCAgbWluaW11bSAgICAgICAgICAgICAgbWluXHJcbiAqICAgICAgaXNOYU4gICAgICAgICAgICAgICAgICAgICAgICAgICB8ICByYW5kb21cclxuICogICAgICBpc05lZ2F0aXZlICAgICAgICAgICAgICAgICAgICAgIHwgIHN1bVxyXG4gKiAgICAgIGlzUG9zaXRpdmUgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIGlzWmVybyAgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIG1pbnVzICAgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIG1vZHVsbyAgICAgICAgICAgICAgICAgICBtb2QgICAgfFxyXG4gKiAgICAgIG11bHRpcGxpZWRCeSAgICAgICAgICAgICB0aW1lcyAgfFxyXG4gKiAgICAgIG5lZ2F0ZWQgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHBsdXMgICAgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHByZWNpc2lvbiAgICAgICAgICAgICAgICBzZCAgICAgfFxyXG4gKiAgICAgIHNoaWZ0ZWRCeSAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHNxdWFyZVJvb3QgICAgICAgICAgICAgICBzcXJ0ICAgfFxyXG4gKiAgICAgIHRvRXhwb25lbnRpYWwgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvRml4ZWQgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvRm9ybWF0ICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvRnJhY3Rpb24gICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvSlNPTiAgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvTnVtYmVyICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvUHJlY2lzaW9uICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHRvU3RyaW5nICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKiAgICAgIHZhbHVlT2YgICAgICAgICAgICAgICAgICAgICAgICAgfFxyXG4gKlxyXG4gKi9cclxuXHJcblxyXG52YXJcclxuICBpc051bWVyaWMgPSAvXi0/KD86XFxkKyg/OlxcLlxcZCopP3xcXC5cXGQrKSg/OmVbKy1dP1xcZCspPyQvaSxcclxuICBtYXRoY2VpbCA9IE1hdGguY2VpbCxcclxuICBtYXRoZmxvb3IgPSBNYXRoLmZsb29yLFxyXG5cclxuICBiaWdudW1iZXJFcnJvciA9ICdbQmlnTnVtYmVyIEVycm9yXSAnLFxyXG4gIHRvb01hbnlEaWdpdHMgPSBiaWdudW1iZXJFcnJvciArICdOdW1iZXIgcHJpbWl0aXZlIGhhcyBtb3JlIHRoYW4gMTUgc2lnbmlmaWNhbnQgZGlnaXRzOiAnLFxyXG5cclxuICBCQVNFID0gMWUxNCxcclxuICBMT0dfQkFTRSA9IDE0LFxyXG4gIE1BWF9TQUZFX0lOVEVHRVIgPSAweDFmZmZmZmZmZmZmZmZmLCAgICAgICAgIC8vIDJeNTMgLSAxXHJcbiAgLy8gTUFYX0lOVDMyID0gMHg3ZmZmZmZmZiwgICAgICAgICAgICAgICAgICAgLy8gMl4zMSAtIDFcclxuICBQT1dTX1RFTiA9IFsxLCAxMCwgMTAwLCAxZTMsIDFlNCwgMWU1LCAxZTYsIDFlNywgMWU4LCAxZTksIDFlMTAsIDFlMTEsIDFlMTIsIDFlMTNdLFxyXG4gIFNRUlRfQkFTRSA9IDFlNyxcclxuXHJcbiAgLy8gRURJVEFCTEVcclxuICAvLyBUaGUgbGltaXQgb24gdGhlIHZhbHVlIG9mIERFQ0lNQUxfUExBQ0VTLCBUT19FWFBfTkVHLCBUT19FWFBfUE9TLCBNSU5fRVhQLCBNQVhfRVhQLCBhbmRcclxuICAvLyB0aGUgYXJndW1lbnRzIHRvIHRvRXhwb25lbnRpYWwsIHRvRml4ZWQsIHRvRm9ybWF0LCBhbmQgdG9QcmVjaXNpb24uXHJcbiAgTUFYID0gMUU5OyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMCB0byBNQVhfSU5UMzJcclxuXHJcblxyXG4vKlxyXG4gKiBDcmVhdGUgYW5kIHJldHVybiBhIEJpZ051bWJlciBjb25zdHJ1Y3Rvci5cclxuICovXHJcbmZ1bmN0aW9uIGNsb25lKGNvbmZpZ09iamVjdCkge1xyXG4gIHZhciBkaXYsIGNvbnZlcnRCYXNlLCBwYXJzZU51bWVyaWMsXHJcbiAgICBQID0gQmlnTnVtYmVyLnByb3RvdHlwZSA9IHsgY29uc3RydWN0b3I6IEJpZ051bWJlciwgdG9TdHJpbmc6IG51bGwsIHZhbHVlT2Y6IG51bGwgfSxcclxuICAgIE9ORSA9IG5ldyBCaWdOdW1iZXIoMSksXHJcblxyXG5cclxuICAgIC8vLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gRURJVEFCTEUgQ09ORklHIERFRkFVTFRTIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcblxyXG4gICAgLy8gVGhlIGRlZmF1bHQgdmFsdWVzIGJlbG93IG11c3QgYmUgaW50ZWdlcnMgd2l0aGluIHRoZSBpbmNsdXNpdmUgcmFuZ2VzIHN0YXRlZC5cclxuICAgIC8vIFRoZSB2YWx1ZXMgY2FuIGFsc28gYmUgY2hhbmdlZCBhdCBydW4tdGltZSB1c2luZyBCaWdOdW1iZXIuc2V0LlxyXG5cclxuICAgIC8vIFRoZSBtYXhpbXVtIG51bWJlciBvZiBkZWNpbWFsIHBsYWNlcyBmb3Igb3BlcmF0aW9ucyBpbnZvbHZpbmcgZGl2aXNpb24uXHJcbiAgICBERUNJTUFMX1BMQUNFUyA9IDIwLCAgICAgICAgICAgICAgICAgICAgIC8vIDAgdG8gTUFYXHJcblxyXG4gICAgLy8gVGhlIHJvdW5kaW5nIG1vZGUgdXNlZCB3aGVuIHJvdW5kaW5nIHRvIHRoZSBhYm92ZSBkZWNpbWFsIHBsYWNlcywgYW5kIHdoZW4gdXNpbmdcclxuICAgIC8vIHRvRXhwb25lbnRpYWwsIHRvRml4ZWQsIHRvRm9ybWF0IGFuZCB0b1ByZWNpc2lvbiwgYW5kIHJvdW5kIChkZWZhdWx0IHZhbHVlKS5cclxuICAgIC8vIFVQICAgICAgICAgMCBBd2F5IGZyb20gemVyby5cclxuICAgIC8vIERPV04gICAgICAgMSBUb3dhcmRzIHplcm8uXHJcbiAgICAvLyBDRUlMICAgICAgIDIgVG93YXJkcyArSW5maW5pdHkuXHJcbiAgICAvLyBGTE9PUiAgICAgIDMgVG93YXJkcyAtSW5maW5pdHkuXHJcbiAgICAvLyBIQUxGX1VQICAgIDQgVG93YXJkcyBuZWFyZXN0IG5laWdoYm91ci4gSWYgZXF1aWRpc3RhbnQsIHVwLlxyXG4gICAgLy8gSEFMRl9ET1dOICA1IFRvd2FyZHMgbmVhcmVzdCBuZWlnaGJvdXIuIElmIGVxdWlkaXN0YW50LCBkb3duLlxyXG4gICAgLy8gSEFMRl9FVkVOICA2IFRvd2FyZHMgbmVhcmVzdCBuZWlnaGJvdXIuIElmIGVxdWlkaXN0YW50LCB0b3dhcmRzIGV2ZW4gbmVpZ2hib3VyLlxyXG4gICAgLy8gSEFMRl9DRUlMICA3IFRvd2FyZHMgbmVhcmVzdCBuZWlnaGJvdXIuIElmIGVxdWlkaXN0YW50LCB0b3dhcmRzICtJbmZpbml0eS5cclxuICAgIC8vIEhBTEZfRkxPT1IgOCBUb3dhcmRzIG5lYXJlc3QgbmVpZ2hib3VyLiBJZiBlcXVpZGlzdGFudCwgdG93YXJkcyAtSW5maW5pdHkuXHJcbiAgICBST1VORElOR19NT0RFID0gNCwgICAgICAgICAgICAgICAgICAgICAgIC8vIDAgdG8gOFxyXG5cclxuICAgIC8vIEVYUE9ORU5USUFMX0FUIDogW1RPX0VYUF9ORUcgLCBUT19FWFBfUE9TXVxyXG5cclxuICAgIC8vIFRoZSBleHBvbmVudCB2YWx1ZSBhdCBhbmQgYmVuZWF0aCB3aGljaCB0b1N0cmluZyByZXR1cm5zIGV4cG9uZW50aWFsIG5vdGF0aW9uLlxyXG4gICAgLy8gTnVtYmVyIHR5cGU6IC03XHJcbiAgICBUT19FWFBfTkVHID0gLTcsICAgICAgICAgICAgICAgICAgICAgICAgIC8vIDAgdG8gLU1BWFxyXG5cclxuICAgIC8vIFRoZSBleHBvbmVudCB2YWx1ZSBhdCBhbmQgYWJvdmUgd2hpY2ggdG9TdHJpbmcgcmV0dXJucyBleHBvbmVudGlhbCBub3RhdGlvbi5cclxuICAgIC8vIE51bWJlciB0eXBlOiAyMVxyXG4gICAgVE9fRVhQX1BPUyA9IDIxLCAgICAgICAgICAgICAgICAgICAgICAgICAvLyAwIHRvIE1BWFxyXG5cclxuICAgIC8vIFJBTkdFIDogW01JTl9FWFAsIE1BWF9FWFBdXHJcblxyXG4gICAgLy8gVGhlIG1pbmltdW0gZXhwb25lbnQgdmFsdWUsIGJlbmVhdGggd2hpY2ggdW5kZXJmbG93IHRvIHplcm8gb2NjdXJzLlxyXG4gICAgLy8gTnVtYmVyIHR5cGU6IC0zMjQgICg1ZS0zMjQpXHJcbiAgICBNSU5fRVhQID0gLTFlNywgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIC0xIHRvIC1NQVhcclxuXHJcbiAgICAvLyBUaGUgbWF4aW11bSBleHBvbmVudCB2YWx1ZSwgYWJvdmUgd2hpY2ggb3ZlcmZsb3cgdG8gSW5maW5pdHkgb2NjdXJzLlxyXG4gICAgLy8gTnVtYmVyIHR5cGU6ICAzMDggICgxLjc5NzY5MzEzNDg2MjMxNTdlKzMwOClcclxuICAgIC8vIEZvciBNQVhfRVhQID4gMWU3LCBlLmcuIG5ldyBCaWdOdW1iZXIoJzFlMTAwMDAwMDAwJykucGx1cygxKSBtYXkgYmUgc2xvdy5cclxuICAgIE1BWF9FWFAgPSAxZTcsICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMSB0byBNQVhcclxuXHJcbiAgICAvLyBXaGV0aGVyIHRvIHVzZSBjcnlwdG9ncmFwaGljYWxseS1zZWN1cmUgcmFuZG9tIG51bWJlciBnZW5lcmF0aW9uLCBpZiBhdmFpbGFibGUuXHJcbiAgICBDUllQVE8gPSBmYWxzZSwgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIHRydWUgb3IgZmFsc2VcclxuXHJcbiAgICAvLyBUaGUgbW9kdWxvIG1vZGUgdXNlZCB3aGVuIGNhbGN1bGF0aW5nIHRoZSBtb2R1bHVzOiBhIG1vZCBuLlxyXG4gICAgLy8gVGhlIHF1b3RpZW50IChxID0gYSAvIG4pIGlzIGNhbGN1bGF0ZWQgYWNjb3JkaW5nIHRvIHRoZSBjb3JyZXNwb25kaW5nIHJvdW5kaW5nIG1vZGUuXHJcbiAgICAvLyBUaGUgcmVtYWluZGVyIChyKSBpcyBjYWxjdWxhdGVkIGFzOiByID0gYSAtIG4gKiBxLlxyXG4gICAgLy9cclxuICAgIC8vIFVQICAgICAgICAwIFRoZSByZW1haW5kZXIgaXMgcG9zaXRpdmUgaWYgdGhlIGRpdmlkZW5kIGlzIG5lZ2F0aXZlLCBlbHNlIGlzIG5lZ2F0aXZlLlxyXG4gICAgLy8gRE9XTiAgICAgIDEgVGhlIHJlbWFpbmRlciBoYXMgdGhlIHNhbWUgc2lnbiBhcyB0aGUgZGl2aWRlbmQuXHJcbiAgICAvLyAgICAgICAgICAgICBUaGlzIG1vZHVsbyBtb2RlIGlzIGNvbW1vbmx5IGtub3duIGFzICd0cnVuY2F0ZWQgZGl2aXNpb24nIGFuZCBpc1xyXG4gICAgLy8gICAgICAgICAgICAgZXF1aXZhbGVudCB0byAoYSAlIG4pIGluIEphdmFTY3JpcHQuXHJcbiAgICAvLyBGTE9PUiAgICAgMyBUaGUgcmVtYWluZGVyIGhhcyB0aGUgc2FtZSBzaWduIGFzIHRoZSBkaXZpc29yIChQeXRob24gJSkuXHJcbiAgICAvLyBIQUxGX0VWRU4gNiBUaGlzIG1vZHVsbyBtb2RlIGltcGxlbWVudHMgdGhlIElFRUUgNzU0IHJlbWFpbmRlciBmdW5jdGlvbi5cclxuICAgIC8vIEVVQ0xJRCAgICA5IEV1Y2xpZGlhbiBkaXZpc2lvbi4gcSA9IHNpZ24obikgKiBmbG9vcihhIC8gYWJzKG4pKS5cclxuICAgIC8vICAgICAgICAgICAgIFRoZSByZW1haW5kZXIgaXMgYWx3YXlzIHBvc2l0aXZlLlxyXG4gICAgLy9cclxuICAgIC8vIFRoZSB0cnVuY2F0ZWQgZGl2aXNpb24sIGZsb29yZWQgZGl2aXNpb24sIEV1Y2xpZGlhbiBkaXZpc2lvbiBhbmQgSUVFRSA3NTQgcmVtYWluZGVyXHJcbiAgICAvLyBtb2RlcyBhcmUgY29tbW9ubHkgdXNlZCBmb3IgdGhlIG1vZHVsdXMgb3BlcmF0aW9uLlxyXG4gICAgLy8gQWx0aG91Z2ggdGhlIG90aGVyIHJvdW5kaW5nIG1vZGVzIGNhbiBhbHNvIGJlIHVzZWQsIHRoZXkgbWF5IG5vdCBnaXZlIHVzZWZ1bCByZXN1bHRzLlxyXG4gICAgTU9EVUxPX01PREUgPSAxLCAgICAgICAgICAgICAgICAgICAgICAgICAvLyAwIHRvIDlcclxuXHJcbiAgICAvLyBUaGUgbWF4aW11bSBudW1iZXIgb2Ygc2lnbmlmaWNhbnQgZGlnaXRzIG9mIHRoZSByZXN1bHQgb2YgdGhlIGV4cG9uZW50aWF0ZWRCeSBvcGVyYXRpb24uXHJcbiAgICAvLyBJZiBQT1dfUFJFQ0lTSU9OIGlzIDAsIHRoZXJlIHdpbGwgYmUgdW5saW1pdGVkIHNpZ25pZmljYW50IGRpZ2l0cy5cclxuICAgIFBPV19QUkVDSVNJT04gPSAwLCAgICAgICAgICAgICAgICAgICAgICAgLy8gMCB0byBNQVhcclxuXHJcbiAgICAvLyBUaGUgZm9ybWF0IHNwZWNpZmljYXRpb24gdXNlZCBieSB0aGUgQmlnTnVtYmVyLnByb3RvdHlwZS50b0Zvcm1hdCBtZXRob2QuXHJcbiAgICBGT1JNQVQgPSB7XHJcbiAgICAgIHByZWZpeDogJycsXHJcbiAgICAgIGdyb3VwU2l6ZTogMyxcclxuICAgICAgc2Vjb25kYXJ5R3JvdXBTaXplOiAwLFxyXG4gICAgICBncm91cFNlcGFyYXRvcjogJywnLFxyXG4gICAgICBkZWNpbWFsU2VwYXJhdG9yOiAnLicsXHJcbiAgICAgIGZyYWN0aW9uR3JvdXBTaXplOiAwLFxyXG4gICAgICBmcmFjdGlvbkdyb3VwU2VwYXJhdG9yOiAnXFx4QTAnLCAgICAgICAgLy8gbm9uLWJyZWFraW5nIHNwYWNlXHJcbiAgICAgIHN1ZmZpeDogJydcclxuICAgIH0sXHJcblxyXG4gICAgLy8gVGhlIGFscGhhYmV0IHVzZWQgZm9yIGJhc2UgY29udmVyc2lvbi4gSXQgbXVzdCBiZSBhdCBsZWFzdCAyIGNoYXJhY3RlcnMgbG9uZywgd2l0aCBubyAnKycsXHJcbiAgICAvLyAnLScsICcuJywgd2hpdGVzcGFjZSwgb3IgcmVwZWF0ZWQgY2hhcmFjdGVyLlxyXG4gICAgLy8gJzAxMjM0NTY3ODlhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaJF8nXHJcbiAgICBBTFBIQUJFVCA9ICcwMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXonLFxyXG4gICAgYWxwaGFiZXRIYXNOb3JtYWxEZWNpbWFsRGlnaXRzID0gdHJ1ZTtcclxuXHJcblxyXG4gIC8vLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5cclxuICAvLyBDT05TVFJVQ1RPUlxyXG5cclxuXHJcbiAgLypcclxuICAgKiBUaGUgQmlnTnVtYmVyIGNvbnN0cnVjdG9yIGFuZCBleHBvcnRlZCBmdW5jdGlvbi5cclxuICAgKiBDcmVhdGUgYW5kIHJldHVybiBhIG5ldyBpbnN0YW5jZSBvZiBhIEJpZ051bWJlciBvYmplY3QuXHJcbiAgICpcclxuICAgKiB2IHtudW1iZXJ8c3RyaW5nfEJpZ051bWJlcn0gQSBudW1lcmljIHZhbHVlLlxyXG4gICAqIFtiXSB7bnVtYmVyfSBUaGUgYmFzZSBvZiB2LiBJbnRlZ2VyLCAyIHRvIEFMUEhBQkVULmxlbmd0aCBpbmNsdXNpdmUuXHJcbiAgICovXHJcbiAgZnVuY3Rpb24gQmlnTnVtYmVyKHYsIGIpIHtcclxuICAgIHZhciBhbHBoYWJldCwgYywgY2FzZUNoYW5nZWQsIGUsIGksIGlzTnVtLCBsZW4sIHN0cixcclxuICAgICAgeCA9IHRoaXM7XHJcblxyXG4gICAgLy8gRW5hYmxlIGNvbnN0cnVjdG9yIGNhbGwgd2l0aG91dCBgbmV3YC5cclxuICAgIGlmICghKHggaW5zdGFuY2VvZiBCaWdOdW1iZXIpKSByZXR1cm4gbmV3IEJpZ051bWJlcih2LCBiKTtcclxuXHJcbiAgICBpZiAoYiA9PSBudWxsKSB7XHJcblxyXG4gICAgICBpZiAodiAmJiB2Ll9pc0JpZ051bWJlciA9PT0gdHJ1ZSkge1xyXG4gICAgICAgIHgucyA9IHYucztcclxuXHJcbiAgICAgICAgaWYgKCF2LmMgfHwgdi5lID4gTUFYX0VYUCkge1xyXG4gICAgICAgICAgeC5jID0geC5lID0gbnVsbDtcclxuICAgICAgICB9IGVsc2UgaWYgKHYuZSA8IE1JTl9FWFApIHtcclxuICAgICAgICAgIHguYyA9IFt4LmUgPSAwXTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgeC5lID0gdi5lO1xyXG4gICAgICAgICAgeC5jID0gdi5jLnNsaWNlKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICgoaXNOdW0gPSB0eXBlb2YgdiA9PSAnbnVtYmVyJykgJiYgdiAqIDAgPT0gMCkge1xyXG5cclxuICAgICAgICAvLyBVc2UgYDEgLyBuYCB0byBoYW5kbGUgbWludXMgemVybyBhbHNvLlxyXG4gICAgICAgIHgucyA9IDEgLyB2IDwgMCA/ICh2ID0gLXYsIC0xKSA6IDE7XHJcblxyXG4gICAgICAgIC8vIEZhc3QgcGF0aCBmb3IgaW50ZWdlcnMsIHdoZXJlIG4gPCAyMTQ3NDgzNjQ4ICgyKiozMSkuXHJcbiAgICAgICAgaWYgKHYgPT09IH5+dikge1xyXG4gICAgICAgICAgZm9yIChlID0gMCwgaSA9IHY7IGkgPj0gMTA7IGkgLz0gMTAsIGUrKyk7XHJcblxyXG4gICAgICAgICAgaWYgKGUgPiBNQVhfRVhQKSB7XHJcbiAgICAgICAgICAgIHguYyA9IHguZSA9IG51bGw7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB4LmUgPSBlO1xyXG4gICAgICAgICAgICB4LmMgPSBbdl07XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc3RyID0gU3RyaW5nKHYpO1xyXG4gICAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgICBpZiAoIWlzTnVtZXJpYy50ZXN0KHN0ciA9IFN0cmluZyh2KSkpIHJldHVybiBwYXJzZU51bWVyaWMoeCwgc3RyLCBpc051bSk7XHJcblxyXG4gICAgICAgIHgucyA9IHN0ci5jaGFyQ29kZUF0KDApID09IDQ1ID8gKHN0ciA9IHN0ci5zbGljZSgxKSwgLTEpIDogMTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gRGVjaW1hbCBwb2ludD9cclxuICAgICAgaWYgKChlID0gc3RyLmluZGV4T2YoJy4nKSkgPiAtMSkgc3RyID0gc3RyLnJlcGxhY2UoJy4nLCAnJyk7XHJcblxyXG4gICAgICAvLyBFeHBvbmVudGlhbCBmb3JtP1xyXG4gICAgICBpZiAoKGkgPSBzdHIuc2VhcmNoKC9lL2kpKSA+IDApIHtcclxuXHJcbiAgICAgICAgLy8gRGV0ZXJtaW5lIGV4cG9uZW50LlxyXG4gICAgICAgIGlmIChlIDwgMCkgZSA9IGk7XHJcbiAgICAgICAgZSArPSArc3RyLnNsaWNlKGkgKyAxKTtcclxuICAgICAgICBzdHIgPSBzdHIuc3Vic3RyaW5nKDAsIGkpO1xyXG4gICAgICB9IGVsc2UgaWYgKGUgPCAwKSB7XHJcblxyXG4gICAgICAgIC8vIEludGVnZXIuXHJcbiAgICAgICAgZSA9IHN0ci5sZW5ndGg7XHJcbiAgICAgIH1cclxuXHJcbiAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgLy8gJ1tCaWdOdW1iZXIgRXJyb3JdIEJhc2Uge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge2J9J1xyXG4gICAgICBpbnRDaGVjayhiLCAyLCBBTFBIQUJFVC5sZW5ndGgsICdCYXNlJyk7XHJcblxyXG4gICAgICAvLyBBbGxvdyBleHBvbmVudGlhbCBub3RhdGlvbiB0byBiZSB1c2VkIHdpdGggYmFzZSAxMCBhcmd1bWVudCwgd2hpbGVcclxuICAgICAgLy8gYWxzbyByb3VuZGluZyB0byBERUNJTUFMX1BMQUNFUyBhcyB3aXRoIG90aGVyIGJhc2VzLlxyXG4gICAgICBpZiAoYiA9PSAxMCAmJiBhbHBoYWJldEhhc05vcm1hbERlY2ltYWxEaWdpdHMpIHtcclxuICAgICAgICB4ID0gbmV3IEJpZ051bWJlcih2KTtcclxuICAgICAgICByZXR1cm4gcm91bmQoeCwgREVDSU1BTF9QTEFDRVMgKyB4LmUgKyAxLCBST1VORElOR19NT0RFKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc3RyID0gU3RyaW5nKHYpO1xyXG5cclxuICAgICAgaWYgKGlzTnVtID0gdHlwZW9mIHYgPT0gJ251bWJlcicpIHtcclxuXHJcbiAgICAgICAgLy8gQXZvaWQgcG90ZW50aWFsIGludGVycHJldGF0aW9uIG9mIEluZmluaXR5IGFuZCBOYU4gYXMgYmFzZSA0NCsgdmFsdWVzLlxyXG4gICAgICAgIGlmICh2ICogMCAhPSAwKSByZXR1cm4gcGFyc2VOdW1lcmljKHgsIHN0ciwgaXNOdW0sIGIpO1xyXG5cclxuICAgICAgICB4LnMgPSAxIC8gdiA8IDAgPyAoc3RyID0gc3RyLnNsaWNlKDEpLCAtMSkgOiAxO1xyXG5cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gTnVtYmVyIHByaW1pdGl2ZSBoYXMgbW9yZSB0aGFuIDE1IHNpZ25pZmljYW50IGRpZ2l0czoge259J1xyXG4gICAgICAgIGlmIChCaWdOdW1iZXIuREVCVUcgJiYgc3RyLnJlcGxhY2UoL14wXFwuMCp8XFwuLywgJycpLmxlbmd0aCA+IDE1KSB7XHJcbiAgICAgICAgICB0aHJvdyBFcnJvclxyXG4gICAgICAgICAgICh0b29NYW55RGlnaXRzICsgdik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHgucyA9IHN0ci5jaGFyQ29kZUF0KDApID09PSA0NSA/IChzdHIgPSBzdHIuc2xpY2UoMSksIC0xKSA6IDE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGFscGhhYmV0ID0gQUxQSEFCRVQuc2xpY2UoMCwgYik7XHJcbiAgICAgIGUgPSBpID0gMDtcclxuXHJcbiAgICAgIC8vIENoZWNrIHRoYXQgc3RyIGlzIGEgdmFsaWQgYmFzZSBiIG51bWJlci5cclxuICAgICAgLy8gRG9uJ3QgdXNlIFJlZ0V4cCwgc28gYWxwaGFiZXQgY2FuIGNvbnRhaW4gc3BlY2lhbCBjaGFyYWN0ZXJzLlxyXG4gICAgICBmb3IgKGxlbiA9IHN0ci5sZW5ndGg7IGkgPCBsZW47IGkrKykge1xyXG4gICAgICAgIGlmIChhbHBoYWJldC5pbmRleE9mKGMgPSBzdHIuY2hhckF0KGkpKSA8IDApIHtcclxuICAgICAgICAgIGlmIChjID09ICcuJykge1xyXG5cclxuICAgICAgICAgICAgLy8gSWYgJy4nIGlzIG5vdCB0aGUgZmlyc3QgY2hhcmFjdGVyIGFuZCBpdCBoYXMgbm90IGJlIGZvdW5kIGJlZm9yZS5cclxuICAgICAgICAgICAgaWYgKGkgPiBlKSB7XHJcbiAgICAgICAgICAgICAgZSA9IGxlbjtcclxuICAgICAgICAgICAgICBjb250aW51ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBlbHNlIGlmICghY2FzZUNoYW5nZWQpIHtcclxuXHJcbiAgICAgICAgICAgIC8vIEFsbG93IGUuZy4gaGV4YWRlY2ltYWwgJ0ZGJyBhcyB3ZWxsIGFzICdmZicuXHJcbiAgICAgICAgICAgIGlmIChzdHIgPT0gc3RyLnRvVXBwZXJDYXNlKCkgJiYgKHN0ciA9IHN0ci50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgICAgICAgICAgc3RyID09IHN0ci50b0xvd2VyQ2FzZSgpICYmIChzdHIgPSBzdHIudG9VcHBlckNhc2UoKSkpIHtcclxuICAgICAgICAgICAgICBjYXNlQ2hhbmdlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgaSA9IC0xO1xyXG4gICAgICAgICAgICAgIGUgPSAwO1xyXG4gICAgICAgICAgICAgIGNvbnRpbnVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHBhcnNlTnVtZXJpYyh4LCBTdHJpbmcodiksIGlzTnVtLCBiKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFByZXZlbnQgbGF0ZXIgY2hlY2sgZm9yIGxlbmd0aCBvbiBjb252ZXJ0ZWQgbnVtYmVyLlxyXG4gICAgICBpc051bSA9IGZhbHNlO1xyXG4gICAgICBzdHIgPSBjb252ZXJ0QmFzZShzdHIsIGIsIDEwLCB4LnMpO1xyXG5cclxuICAgICAgLy8gRGVjaW1hbCBwb2ludD9cclxuICAgICAgaWYgKChlID0gc3RyLmluZGV4T2YoJy4nKSkgPiAtMSkgc3RyID0gc3RyLnJlcGxhY2UoJy4nLCAnJyk7XHJcbiAgICAgIGVsc2UgZSA9IHN0ci5sZW5ndGg7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRGV0ZXJtaW5lIGxlYWRpbmcgemVyb3MuXHJcbiAgICBmb3IgKGkgPSAwOyBzdHIuY2hhckNvZGVBdChpKSA9PT0gNDg7IGkrKyk7XHJcblxyXG4gICAgLy8gRGV0ZXJtaW5lIHRyYWlsaW5nIHplcm9zLlxyXG4gICAgZm9yIChsZW4gPSBzdHIubGVuZ3RoOyBzdHIuY2hhckNvZGVBdCgtLWxlbikgPT09IDQ4Oyk7XHJcblxyXG4gICAgaWYgKHN0ciA9IHN0ci5zbGljZShpLCArK2xlbikpIHtcclxuICAgICAgbGVuIC09IGk7XHJcblxyXG4gICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gTnVtYmVyIHByaW1pdGl2ZSBoYXMgbW9yZSB0aGFuIDE1IHNpZ25pZmljYW50IGRpZ2l0czoge259J1xyXG4gICAgICBpZiAoaXNOdW0gJiYgQmlnTnVtYmVyLkRFQlVHICYmXHJcbiAgICAgICAgbGVuID4gMTUgJiYgKHYgPiBNQVhfU0FGRV9JTlRFR0VSIHx8IHYgIT09IG1hdGhmbG9vcih2KSkpIHtcclxuICAgICAgICAgIHRocm93IEVycm9yXHJcbiAgICAgICAgICAgKHRvb01hbnlEaWdpdHMgKyAoeC5zICogdikpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAgLy8gT3ZlcmZsb3c/XHJcbiAgICAgIGlmICgoZSA9IGUgLSBpIC0gMSkgPiBNQVhfRVhQKSB7XHJcblxyXG4gICAgICAgIC8vIEluZmluaXR5LlxyXG4gICAgICAgIHguYyA9IHguZSA9IG51bGw7XHJcblxyXG4gICAgICAvLyBVbmRlcmZsb3c/XHJcbiAgICAgIH0gZWxzZSBpZiAoZSA8IE1JTl9FWFApIHtcclxuXHJcbiAgICAgICAgLy8gWmVyby5cclxuICAgICAgICB4LmMgPSBbeC5lID0gMF07XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgeC5lID0gZTtcclxuICAgICAgICB4LmMgPSBbXTtcclxuXHJcbiAgICAgICAgLy8gVHJhbnNmb3JtIGJhc2VcclxuXHJcbiAgICAgICAgLy8gZSBpcyB0aGUgYmFzZSAxMCBleHBvbmVudC5cclxuICAgICAgICAvLyBpIGlzIHdoZXJlIHRvIHNsaWNlIHN0ciB0byBnZXQgdGhlIGZpcnN0IGVsZW1lbnQgb2YgdGhlIGNvZWZmaWNpZW50IGFycmF5LlxyXG4gICAgICAgIGkgPSAoZSArIDEpICUgTE9HX0JBU0U7XHJcbiAgICAgICAgaWYgKGUgPCAwKSBpICs9IExPR19CQVNFOyAgLy8gaSA8IDFcclxuXHJcbiAgICAgICAgaWYgKGkgPCBsZW4pIHtcclxuICAgICAgICAgIGlmIChpKSB4LmMucHVzaCgrc3RyLnNsaWNlKDAsIGkpKTtcclxuXHJcbiAgICAgICAgICBmb3IgKGxlbiAtPSBMT0dfQkFTRTsgaSA8IGxlbjspIHtcclxuICAgICAgICAgICAgeC5jLnB1c2goK3N0ci5zbGljZShpLCBpICs9IExPR19CQVNFKSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaSA9IExPR19CQVNFIC0gKHN0ciA9IHN0ci5zbGljZShpKSkubGVuZ3RoO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBpIC09IGxlbjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGZvciAoOyBpLS07IHN0ciArPSAnMCcpO1xyXG4gICAgICAgIHguYy5wdXNoKCtzdHIpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgLy8gWmVyby5cclxuICAgICAgeC5jID0gW3guZSA9IDBdO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcblxyXG4gIC8vIENPTlNUUlVDVE9SIFBST1BFUlRJRVNcclxuXHJcblxyXG4gIEJpZ051bWJlci5jbG9uZSA9IGNsb25lO1xyXG5cclxuICBCaWdOdW1iZXIuUk9VTkRfVVAgPSAwO1xyXG4gIEJpZ051bWJlci5ST1VORF9ET1dOID0gMTtcclxuICBCaWdOdW1iZXIuUk9VTkRfQ0VJTCA9IDI7XHJcbiAgQmlnTnVtYmVyLlJPVU5EX0ZMT09SID0gMztcclxuICBCaWdOdW1iZXIuUk9VTkRfSEFMRl9VUCA9IDQ7XHJcbiAgQmlnTnVtYmVyLlJPVU5EX0hBTEZfRE9XTiA9IDU7XHJcbiAgQmlnTnVtYmVyLlJPVU5EX0hBTEZfRVZFTiA9IDY7XHJcbiAgQmlnTnVtYmVyLlJPVU5EX0hBTEZfQ0VJTCA9IDc7XHJcbiAgQmlnTnVtYmVyLlJPVU5EX0hBTEZfRkxPT1IgPSA4O1xyXG4gIEJpZ051bWJlci5FVUNMSUQgPSA5O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBDb25maWd1cmUgaW5mcmVxdWVudGx5LWNoYW5naW5nIGxpYnJhcnktd2lkZSBzZXR0aW5ncy5cclxuICAgKlxyXG4gICAqIEFjY2VwdCBhbiBvYmplY3Qgd2l0aCB0aGUgZm9sbG93aW5nIG9wdGlvbmFsIHByb3BlcnRpZXMgKGlmIHRoZSB2YWx1ZSBvZiBhIHByb3BlcnR5IGlzXHJcbiAgICogYSBudW1iZXIsIGl0IG11c3QgYmUgYW4gaW50ZWdlciB3aXRoaW4gdGhlIGluY2x1c2l2ZSByYW5nZSBzdGF0ZWQpOlxyXG4gICAqXHJcbiAgICogICBERUNJTUFMX1BMQUNFUyAgIHtudW1iZXJ9ICAgICAgICAgICAwIHRvIE1BWFxyXG4gICAqICAgUk9VTkRJTkdfTU9ERSAgICB7bnVtYmVyfSAgICAgICAgICAgMCB0byA4XHJcbiAgICogICBFWFBPTkVOVElBTF9BVCAgIHtudW1iZXJ8bnVtYmVyW119ICAtTUFYIHRvIE1BWCAgb3IgIFstTUFYIHRvIDAsIDAgdG8gTUFYXVxyXG4gICAqICAgUkFOR0UgICAgICAgICAgICB7bnVtYmVyfG51bWJlcltdfSAgLU1BWCB0byBNQVggKG5vdCB6ZXJvKSAgb3IgIFstTUFYIHRvIC0xLCAxIHRvIE1BWF1cclxuICAgKiAgIENSWVBUTyAgICAgICAgICAge2Jvb2xlYW59ICAgICAgICAgIHRydWUgb3IgZmFsc2VcclxuICAgKiAgIE1PRFVMT19NT0RFICAgICAge251bWJlcn0gICAgICAgICAgIDAgdG8gOVxyXG4gICAqICAgUE9XX1BSRUNJU0lPTiAgICAgICB7bnVtYmVyfSAgICAgICAgICAgMCB0byBNQVhcclxuICAgKiAgIEFMUEhBQkVUICAgICAgICAge3N0cmluZ30gICAgICAgICAgIEEgc3RyaW5nIG9mIHR3byBvciBtb3JlIHVuaXF1ZSBjaGFyYWN0ZXJzIHdoaWNoIGRvZXNcclxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5vdCBjb250YWluICcuJy5cclxuICAgKiAgIEZPUk1BVCAgICAgICAgICAge29iamVjdH0gICAgICAgICAgIEFuIG9iamVjdCB3aXRoIHNvbWUgb2YgdGhlIGZvbGxvd2luZyBwcm9wZXJ0aWVzOlxyXG4gICAqICAgICBwcmVmaXggICAgICAgICAgICAgICAgIHtzdHJpbmd9XHJcbiAgICogICAgIGdyb3VwU2l6ZSAgICAgICAgICAgICAge251bWJlcn1cclxuICAgKiAgICAgc2Vjb25kYXJ5R3JvdXBTaXplICAgICB7bnVtYmVyfVxyXG4gICAqICAgICBncm91cFNlcGFyYXRvciAgICAgICAgIHtzdHJpbmd9XHJcbiAgICogICAgIGRlY2ltYWxTZXBhcmF0b3IgICAgICAge3N0cmluZ31cclxuICAgKiAgICAgZnJhY3Rpb25Hcm91cFNpemUgICAgICB7bnVtYmVyfVxyXG4gICAqICAgICBmcmFjdGlvbkdyb3VwU2VwYXJhdG9yIHtzdHJpbmd9XHJcbiAgICogICAgIHN1ZmZpeCAgICAgICAgICAgICAgICAge3N0cmluZ31cclxuICAgKlxyXG4gICAqIChUaGUgdmFsdWVzIGFzc2lnbmVkIHRvIHRoZSBhYm92ZSBGT1JNQVQgb2JqZWN0IHByb3BlcnRpZXMgYXJlIG5vdCBjaGVja2VkIGZvciB2YWxpZGl0eS4pXHJcbiAgICpcclxuICAgKiBFLmcuXHJcbiAgICogQmlnTnVtYmVyLmNvbmZpZyh7IERFQ0lNQUxfUExBQ0VTIDogMjAsIFJPVU5ESU5HX01PREUgOiA0IH0pXHJcbiAgICpcclxuICAgKiBJZ25vcmUgcHJvcGVydGllcy9wYXJhbWV0ZXJzIHNldCB0byBudWxsIG9yIHVuZGVmaW5lZCwgZXhjZXB0IGZvciBBTFBIQUJFVC5cclxuICAgKlxyXG4gICAqIFJldHVybiBhbiBvYmplY3Qgd2l0aCB0aGUgcHJvcGVydGllcyBjdXJyZW50IHZhbHVlcy5cclxuICAgKi9cclxuICBCaWdOdW1iZXIuY29uZmlnID0gQmlnTnVtYmVyLnNldCA9IGZ1bmN0aW9uIChvYmopIHtcclxuICAgIHZhciBwLCB2O1xyXG5cclxuICAgIGlmIChvYmogIT0gbnVsbCkge1xyXG5cclxuICAgICAgaWYgKHR5cGVvZiBvYmogPT0gJ29iamVjdCcpIHtcclxuXHJcbiAgICAgICAgLy8gREVDSU1BTF9QTEFDRVMge251bWJlcn0gSW50ZWdlciwgMCB0byBNQVggaW5jbHVzaXZlLlxyXG4gICAgICAgIC8vICdbQmlnTnVtYmVyIEVycm9yXSBERUNJTUFMX1BMQUNFUyB7bm90IGEgcHJpbWl0aXZlIG51bWJlcnxub3QgYW4gaW50ZWdlcnxvdXQgb2YgcmFuZ2V9OiB7dn0nXHJcbiAgICAgICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShwID0gJ0RFQ0lNQUxfUExBQ0VTJykpIHtcclxuICAgICAgICAgIHYgPSBvYmpbcF07XHJcbiAgICAgICAgICBpbnRDaGVjayh2LCAwLCBNQVgsIHApO1xyXG4gICAgICAgICAgREVDSU1BTF9QTEFDRVMgPSB2O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUk9VTkRJTkdfTU9ERSB7bnVtYmVyfSBJbnRlZ2VyLCAwIHRvIDggaW5jbHVzaXZlLlxyXG4gICAgICAgIC8vICdbQmlnTnVtYmVyIEVycm9yXSBST1VORElOR19NT0RFIHtub3QgYSBwcmltaXRpdmUgbnVtYmVyfG5vdCBhbiBpbnRlZ2VyfG91dCBvZiByYW5nZX06IHt2fSdcclxuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KHAgPSAnUk9VTkRJTkdfTU9ERScpKSB7XHJcbiAgICAgICAgICB2ID0gb2JqW3BdO1xyXG4gICAgICAgICAgaW50Q2hlY2sodiwgMCwgOCwgcCk7XHJcbiAgICAgICAgICBST1VORElOR19NT0RFID0gdjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEVYUE9ORU5USUFMX0FUIHtudW1iZXJ8bnVtYmVyW119XHJcbiAgICAgICAgLy8gSW50ZWdlciwgLU1BWCB0byBNQVggaW5jbHVzaXZlIG9yXHJcbiAgICAgICAgLy8gW2ludGVnZXIgLU1BWCB0byAwIGluY2x1c2l2ZSwgMCB0byBNQVggaW5jbHVzaXZlXS5cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gRVhQT05FTlRJQUxfQVQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge3Z9J1xyXG4gICAgICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkocCA9ICdFWFBPTkVOVElBTF9BVCcpKSB7XHJcbiAgICAgICAgICB2ID0gb2JqW3BdO1xyXG4gICAgICAgICAgaWYgKHYgJiYgdi5wb3ApIHtcclxuICAgICAgICAgICAgaW50Q2hlY2sodlswXSwgLU1BWCwgMCwgcCk7XHJcbiAgICAgICAgICAgIGludENoZWNrKHZbMV0sIDAsIE1BWCwgcCk7XHJcbiAgICAgICAgICAgIFRPX0VYUF9ORUcgPSB2WzBdO1xyXG4gICAgICAgICAgICBUT19FWFBfUE9TID0gdlsxXTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGludENoZWNrKHYsIC1NQVgsIE1BWCwgcCk7XHJcbiAgICAgICAgICAgIFRPX0VYUF9ORUcgPSAtKFRPX0VYUF9QT1MgPSB2IDwgMCA/IC12IDogdik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBSQU5HRSB7bnVtYmVyfG51bWJlcltdfSBOb24temVybyBpbnRlZ2VyLCAtTUFYIHRvIE1BWCBpbmNsdXNpdmUgb3JcclxuICAgICAgICAvLyBbaW50ZWdlciAtTUFYIHRvIC0xIGluY2x1c2l2ZSwgaW50ZWdlciAxIHRvIE1BWCBpbmNsdXNpdmVdLlxyXG4gICAgICAgIC8vICdbQmlnTnVtYmVyIEVycm9yXSBSQU5HRSB7bm90IGEgcHJpbWl0aXZlIG51bWJlcnxub3QgYW4gaW50ZWdlcnxvdXQgb2YgcmFuZ2V8Y2Fubm90IGJlIHplcm99OiB7dn0nXHJcbiAgICAgICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShwID0gJ1JBTkdFJykpIHtcclxuICAgICAgICAgIHYgPSBvYmpbcF07XHJcbiAgICAgICAgICBpZiAodiAmJiB2LnBvcCkge1xyXG4gICAgICAgICAgICBpbnRDaGVjayh2WzBdLCAtTUFYLCAtMSwgcCk7XHJcbiAgICAgICAgICAgIGludENoZWNrKHZbMV0sIDEsIE1BWCwgcCk7XHJcbiAgICAgICAgICAgIE1JTl9FWFAgPSB2WzBdO1xyXG4gICAgICAgICAgICBNQVhfRVhQID0gdlsxXTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGludENoZWNrKHYsIC1NQVgsIE1BWCwgcCk7XHJcbiAgICAgICAgICAgIGlmICh2KSB7XHJcbiAgICAgICAgICAgICAgTUlOX0VYUCA9IC0oTUFYX0VYUCA9IHYgPCAwID8gLXYgOiB2KTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICB0aHJvdyBFcnJvclxyXG4gICAgICAgICAgICAgICAoYmlnbnVtYmVyRXJyb3IgKyBwICsgJyBjYW5ub3QgYmUgemVybzogJyArIHYpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDUllQVE8ge2Jvb2xlYW59IHRydWUgb3IgZmFsc2UuXHJcbiAgICAgICAgLy8gJ1tCaWdOdW1iZXIgRXJyb3JdIENSWVBUTyBub3QgdHJ1ZSBvciBmYWxzZToge3Z9J1xyXG4gICAgICAgIC8vICdbQmlnTnVtYmVyIEVycm9yXSBjcnlwdG8gdW5hdmFpbGFibGUnXHJcbiAgICAgICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShwID0gJ0NSWVBUTycpKSB7XHJcbiAgICAgICAgICB2ID0gb2JqW3BdO1xyXG4gICAgICAgICAgaWYgKHYgPT09ICEhdikge1xyXG4gICAgICAgICAgICBpZiAodikge1xyXG4gICAgICAgICAgICAgIGlmICh0eXBlb2YgY3J5cHRvICE9ICd1bmRlZmluZWQnICYmIGNyeXB0byAmJlxyXG4gICAgICAgICAgICAgICAoY3J5cHRvLmdldFJhbmRvbVZhbHVlcyB8fCBjcnlwdG8ucmFuZG9tQnl0ZXMpKSB7XHJcbiAgICAgICAgICAgICAgICBDUllQVE8gPSB2O1xyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBDUllQVE8gPSAhdjtcclxuICAgICAgICAgICAgICAgIHRocm93IEVycm9yXHJcbiAgICAgICAgICAgICAgICAgKGJpZ251bWJlckVycm9yICsgJ2NyeXB0byB1bmF2YWlsYWJsZScpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBDUllQVE8gPSB2O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aHJvdyBFcnJvclxyXG4gICAgICAgICAgICAgKGJpZ251bWJlckVycm9yICsgcCArICcgbm90IHRydWUgb3IgZmFsc2U6ICcgKyB2KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIE1PRFVMT19NT0RFIHtudW1iZXJ9IEludGVnZXIsIDAgdG8gOSBpbmNsdXNpdmUuXHJcbiAgICAgICAgLy8gJ1tCaWdOdW1iZXIgRXJyb3JdIE1PRFVMT19NT0RFIHtub3QgYSBwcmltaXRpdmUgbnVtYmVyfG5vdCBhbiBpbnRlZ2VyfG91dCBvZiByYW5nZX06IHt2fSdcclxuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KHAgPSAnTU9EVUxPX01PREUnKSkge1xyXG4gICAgICAgICAgdiA9IG9ialtwXTtcclxuICAgICAgICAgIGludENoZWNrKHYsIDAsIDksIHApO1xyXG4gICAgICAgICAgTU9EVUxPX01PREUgPSB2O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUE9XX1BSRUNJU0lPTiB7bnVtYmVyfSBJbnRlZ2VyLCAwIHRvIE1BWCBpbmNsdXNpdmUuXHJcbiAgICAgICAgLy8gJ1tCaWdOdW1iZXIgRXJyb3JdIFBPV19QUkVDSVNJT04ge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge3Z9J1xyXG4gICAgICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkocCA9ICdQT1dfUFJFQ0lTSU9OJykpIHtcclxuICAgICAgICAgIHYgPSBvYmpbcF07XHJcbiAgICAgICAgICBpbnRDaGVjayh2LCAwLCBNQVgsIHApO1xyXG4gICAgICAgICAgUE9XX1BSRUNJU0lPTiA9IHY7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBGT1JNQVQge29iamVjdH1cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gRk9STUFUIG5vdCBhbiBvYmplY3Q6IHt2fSdcclxuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KHAgPSAnRk9STUFUJykpIHtcclxuICAgICAgICAgIHYgPSBvYmpbcF07XHJcbiAgICAgICAgICBpZiAodHlwZW9mIHYgPT0gJ29iamVjdCcpIEZPUk1BVCA9IHY7XHJcbiAgICAgICAgICBlbHNlIHRocm93IEVycm9yXHJcbiAgICAgICAgICAgKGJpZ251bWJlckVycm9yICsgcCArICcgbm90IGFuIG9iamVjdDogJyArIHYpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQUxQSEFCRVQge3N0cmluZ31cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gQUxQSEFCRVQgaW52YWxpZDoge3Z9J1xyXG4gICAgICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkocCA9ICdBTFBIQUJFVCcpKSB7XHJcbiAgICAgICAgICB2ID0gb2JqW3BdO1xyXG5cclxuICAgICAgICAgIC8vIERpc2FsbG93IGlmIGxlc3MgdGhhbiB0d28gY2hhcmFjdGVycyxcclxuICAgICAgICAgIC8vIG9yIGlmIGl0IGNvbnRhaW5zICcrJywgJy0nLCAnLicsIHdoaXRlc3BhY2UsIG9yIGEgcmVwZWF0ZWQgY2hhcmFjdGVyLlxyXG4gICAgICAgICAgaWYgKHR5cGVvZiB2ID09ICdzdHJpbmcnICYmICEvXi4/JHxbK1xcLS5cXHNdfCguKS4qXFwxLy50ZXN0KHYpKSB7XHJcbiAgICAgICAgICAgIGFscGhhYmV0SGFzTm9ybWFsRGVjaW1hbERpZ2l0cyA9IHYuc2xpY2UoMCwgMTApID09ICcwMTIzNDU2Nzg5JztcclxuICAgICAgICAgICAgQUxQSEFCRVQgPSB2O1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhyb3cgRXJyb3JcclxuICAgICAgICAgICAgIChiaWdudW1iZXJFcnJvciArIHAgKyAnIGludmFsaWQ6ICcgKyB2KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gT2JqZWN0IGV4cGVjdGVkOiB7dn0nXHJcbiAgICAgICAgdGhyb3cgRXJyb3JcclxuICAgICAgICAgKGJpZ251bWJlckVycm9yICsgJ09iamVjdCBleHBlY3RlZDogJyArIG9iaik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBERUNJTUFMX1BMQUNFUzogREVDSU1BTF9QTEFDRVMsXHJcbiAgICAgIFJPVU5ESU5HX01PREU6IFJPVU5ESU5HX01PREUsXHJcbiAgICAgIEVYUE9ORU5USUFMX0FUOiBbVE9fRVhQX05FRywgVE9fRVhQX1BPU10sXHJcbiAgICAgIFJBTkdFOiBbTUlOX0VYUCwgTUFYX0VYUF0sXHJcbiAgICAgIENSWVBUTzogQ1JZUFRPLFxyXG4gICAgICBNT0RVTE9fTU9ERTogTU9EVUxPX01PREUsXHJcbiAgICAgIFBPV19QUkVDSVNJT046IFBPV19QUkVDSVNJT04sXHJcbiAgICAgIEZPUk1BVDogRk9STUFULFxyXG4gICAgICBBTFBIQUJFVDogQUxQSEFCRVRcclxuICAgIH07XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdiBpcyBhIEJpZ051bWJlciBpbnN0YW5jZSwgb3RoZXJ3aXNlIHJldHVybiBmYWxzZS5cclxuICAgKlxyXG4gICAqIElmIEJpZ051bWJlci5ERUJVRyBpcyB0cnVlLCB0aHJvdyBpZiBhIEJpZ051bWJlciBpbnN0YW5jZSBpcyBub3Qgd2VsbC1mb3JtZWQuXHJcbiAgICpcclxuICAgKiB2IHthbnl9XHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gSW52YWxpZCBCaWdOdW1iZXI6IHt2fSdcclxuICAgKi9cclxuICBCaWdOdW1iZXIuaXNCaWdOdW1iZXIgPSBmdW5jdGlvbiAodikge1xyXG4gICAgaWYgKCF2IHx8IHYuX2lzQmlnTnVtYmVyICE9PSB0cnVlKSByZXR1cm4gZmFsc2U7XHJcbiAgICBpZiAoIUJpZ051bWJlci5ERUJVRykgcmV0dXJuIHRydWU7XHJcblxyXG4gICAgdmFyIGksIG4sXHJcbiAgICAgIGMgPSB2LmMsXHJcbiAgICAgIGUgPSB2LmUsXHJcbiAgICAgIHMgPSB2LnM7XHJcblxyXG4gICAgb3V0OiBpZiAoe30udG9TdHJpbmcuY2FsbChjKSA9PSAnW29iamVjdCBBcnJheV0nKSB7XHJcblxyXG4gICAgICBpZiAoKHMgPT09IDEgfHwgcyA9PT0gLTEpICYmIGUgPj0gLU1BWCAmJiBlIDw9IE1BWCAmJiBlID09PSBtYXRoZmxvb3IoZSkpIHtcclxuXHJcbiAgICAgICAgLy8gSWYgdGhlIGZpcnN0IGVsZW1lbnQgaXMgemVybywgdGhlIEJpZ051bWJlciB2YWx1ZSBtdXN0IGJlIHplcm8uXHJcbiAgICAgICAgaWYgKGNbMF0gPT09IDApIHtcclxuICAgICAgICAgIGlmIChlID09PSAwICYmIGMubGVuZ3RoID09PSAxKSByZXR1cm4gdHJ1ZTtcclxuICAgICAgICAgIGJyZWFrIG91dDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIENhbGN1bGF0ZSBudW1iZXIgb2YgZGlnaXRzIHRoYXQgY1swXSBzaG91bGQgaGF2ZSwgYmFzZWQgb24gdGhlIGV4cG9uZW50LlxyXG4gICAgICAgIGkgPSAoZSArIDEpICUgTE9HX0JBU0U7XHJcbiAgICAgICAgaWYgKGkgPCAxKSBpICs9IExPR19CQVNFO1xyXG5cclxuICAgICAgICAvLyBDYWxjdWxhdGUgbnVtYmVyIG9mIGRpZ2l0cyBvZiBjWzBdLlxyXG4gICAgICAgIC8vaWYgKE1hdGguY2VpbChNYXRoLmxvZyhjWzBdICsgMSkgLyBNYXRoLkxOMTApID09IGkpIHtcclxuICAgICAgICBpZiAoU3RyaW5nKGNbMF0pLmxlbmd0aCA9PSBpKSB7XHJcblxyXG4gICAgICAgICAgZm9yIChpID0gMDsgaSA8IGMubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICAgICAgbiA9IGNbaV07XHJcbiAgICAgICAgICAgIGlmIChuIDwgMCB8fCBuID49IEJBU0UgfHwgbiAhPT0gbWF0aGZsb29yKG4pKSBicmVhayBvdXQ7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLy8gTGFzdCBlbGVtZW50IGNhbm5vdCBiZSB6ZXJvLCB1bmxlc3MgaXQgaXMgdGhlIG9ubHkgZWxlbWVudC5cclxuICAgICAgICAgIGlmIChuICE9PSAwKSByZXR1cm4gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAvLyBJbmZpbml0eS9OYU5cclxuICAgIH0gZWxzZSBpZiAoYyA9PT0gbnVsbCAmJiBlID09PSBudWxsICYmIChzID09PSBudWxsIHx8IHMgPT09IDEgfHwgcyA9PT0gLTEpKSB7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfVxyXG5cclxuICAgIHRocm93IEVycm9yXHJcbiAgICAgIChiaWdudW1iZXJFcnJvciArICdJbnZhbGlkIEJpZ051bWJlcjogJyArIHYpO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIG1heGltdW0gb2YgdGhlIGFyZ3VtZW50cy5cclxuICAgKlxyXG4gICAqIGFyZ3VtZW50cyB7bnVtYmVyfHN0cmluZ3xCaWdOdW1iZXJ9XHJcbiAgICovXHJcbiAgQmlnTnVtYmVyLm1heGltdW0gPSBCaWdOdW1iZXIubWF4ID0gZnVuY3Rpb24gKCkge1xyXG4gICAgcmV0dXJuIG1heE9yTWluKGFyZ3VtZW50cywgLTEpO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIG1pbmltdW0gb2YgdGhlIGFyZ3VtZW50cy5cclxuICAgKlxyXG4gICAqIGFyZ3VtZW50cyB7bnVtYmVyfHN0cmluZ3xCaWdOdW1iZXJ9XHJcbiAgICovXHJcbiAgQmlnTnVtYmVyLm1pbmltdW0gPSBCaWdOdW1iZXIubWluID0gZnVuY3Rpb24gKCkge1xyXG4gICAgcmV0dXJuIG1heE9yTWluKGFyZ3VtZW50cywgMSk7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIGEgbmV3IEJpZ051bWJlciB3aXRoIGEgcmFuZG9tIHZhbHVlIGVxdWFsIHRvIG9yIGdyZWF0ZXIgdGhhbiAwIGFuZCBsZXNzIHRoYW4gMSxcclxuICAgKiBhbmQgd2l0aCBkcCwgb3IgREVDSU1BTF9QTEFDRVMgaWYgZHAgaXMgb21pdHRlZCwgZGVjaW1hbCBwbGFjZXMgKG9yIGxlc3MgaWYgdHJhaWxpbmdcclxuICAgKiB6ZXJvcyBhcmUgcHJvZHVjZWQpLlxyXG4gICAqXHJcbiAgICogW2RwXSB7bnVtYmVyfSBEZWNpbWFsIHBsYWNlcy4gSW50ZWdlciwgMCB0byBNQVggaW5jbHVzaXZlLlxyXG4gICAqXHJcbiAgICogJ1tCaWdOdW1iZXIgRXJyb3JdIEFyZ3VtZW50IHtub3QgYSBwcmltaXRpdmUgbnVtYmVyfG5vdCBhbiBpbnRlZ2VyfG91dCBvZiByYW5nZX06IHtkcH0nXHJcbiAgICogJ1tCaWdOdW1iZXIgRXJyb3JdIGNyeXB0byB1bmF2YWlsYWJsZSdcclxuICAgKi9cclxuICBCaWdOdW1iZXIucmFuZG9tID0gKGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciBwb3cyXzUzID0gMHgyMDAwMDAwMDAwMDAwMDtcclxuXHJcbiAgICAvLyBSZXR1cm4gYSA1MyBiaXQgaW50ZWdlciBuLCB3aGVyZSAwIDw9IG4gPCA5MDA3MTk5MjU0NzQwOTkyLlxyXG4gICAgLy8gQ2hlY2sgaWYgTWF0aC5yYW5kb20oKSBwcm9kdWNlcyBtb3JlIHRoYW4gMzIgYml0cyBvZiByYW5kb21uZXNzLlxyXG4gICAgLy8gSWYgaXQgZG9lcywgYXNzdW1lIGF0IGxlYXN0IDUzIGJpdHMgYXJlIHByb2R1Y2VkLCBvdGhlcndpc2UgYXNzdW1lIGF0IGxlYXN0IDMwIGJpdHMuXHJcbiAgICAvLyAweDQwMDAwMDAwIGlzIDJeMzAsIDB4ODAwMDAwIGlzIDJeMjMsIDB4MWZmZmZmIGlzIDJeMjEgLSAxLlxyXG4gICAgdmFyIHJhbmRvbTUzYml0SW50ID0gKE1hdGgucmFuZG9tKCkgKiBwb3cyXzUzKSAmIDB4MWZmZmZmXHJcbiAgICAgPyBmdW5jdGlvbiAoKSB7IHJldHVybiBtYXRoZmxvb3IoTWF0aC5yYW5kb20oKSAqIHBvdzJfNTMpOyB9XHJcbiAgICAgOiBmdW5jdGlvbiAoKSB7IHJldHVybiAoKE1hdGgucmFuZG9tKCkgKiAweDQwMDAwMDAwIHwgMCkgKiAweDgwMDAwMCkgK1xyXG4gICAgICAgKE1hdGgucmFuZG9tKCkgKiAweDgwMDAwMCB8IDApOyB9O1xyXG5cclxuICAgIHJldHVybiBmdW5jdGlvbiAoZHApIHtcclxuICAgICAgdmFyIGEsIGIsIGUsIGssIHYsXHJcbiAgICAgICAgaSA9IDAsXHJcbiAgICAgICAgYyA9IFtdLFxyXG4gICAgICAgIHJhbmQgPSBuZXcgQmlnTnVtYmVyKE9ORSk7XHJcblxyXG4gICAgICBpZiAoZHAgPT0gbnVsbCkgZHAgPSBERUNJTUFMX1BMQUNFUztcclxuICAgICAgZWxzZSBpbnRDaGVjayhkcCwgMCwgTUFYKTtcclxuXHJcbiAgICAgIGsgPSBtYXRoY2VpbChkcCAvIExPR19CQVNFKTtcclxuXHJcbiAgICAgIGlmIChDUllQVE8pIHtcclxuXHJcbiAgICAgICAgLy8gQnJvd3NlcnMgc3VwcG9ydGluZyBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzLlxyXG4gICAgICAgIGlmIChjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKSB7XHJcblxyXG4gICAgICAgICAgYSA9IGNyeXB0by5nZXRSYW5kb21WYWx1ZXMobmV3IFVpbnQzMkFycmF5KGsgKj0gMikpO1xyXG5cclxuICAgICAgICAgIGZvciAoOyBpIDwgazspIHtcclxuXHJcbiAgICAgICAgICAgIC8vIDUzIGJpdHM6XHJcbiAgICAgICAgICAgIC8vICgoTWF0aC5wb3coMiwgMzIpIC0gMSkgKiBNYXRoLnBvdygyLCAyMSkpLnRvU3RyaW5nKDIpXHJcbiAgICAgICAgICAgIC8vIDExMTExIDExMTExMTExIDExMTExMTExIDExMTExMTExIDExMTAwMDAwIDAwMDAwMDAwIDAwMDAwMDAwXHJcbiAgICAgICAgICAgIC8vICgoTWF0aC5wb3coMiwgMzIpIC0gMSkgPj4+IDExKS50b1N0cmluZygyKVxyXG4gICAgICAgICAgICAvLyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMTExMSAxMTExMTExMSAxMTExMTExMVxyXG4gICAgICAgICAgICAvLyAweDIwMDAwIGlzIDJeMjEuXHJcbiAgICAgICAgICAgIHYgPSBhW2ldICogMHgyMDAwMCArIChhW2kgKyAxXSA+Pj4gMTEpO1xyXG5cclxuICAgICAgICAgICAgLy8gUmVqZWN0aW9uIHNhbXBsaW5nOlxyXG4gICAgICAgICAgICAvLyAwIDw9IHYgPCA5MDA3MTk5MjU0NzQwOTkyXHJcbiAgICAgICAgICAgIC8vIFByb2JhYmlsaXR5IHRoYXQgdiA+PSA5ZTE1LCBpc1xyXG4gICAgICAgICAgICAvLyA3MTk5MjU0NzQwOTkyIC8gOTAwNzE5OTI1NDc0MDk5MiB+PSAwLjAwMDgsIGkuZS4gMSBpbiAxMjUxXHJcbiAgICAgICAgICAgIGlmICh2ID49IDllMTUpIHtcclxuICAgICAgICAgICAgICBiID0gY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhuZXcgVWludDMyQXJyYXkoMikpO1xyXG4gICAgICAgICAgICAgIGFbaV0gPSBiWzBdO1xyXG4gICAgICAgICAgICAgIGFbaSArIDFdID0gYlsxXTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgICAgICAgLy8gMCA8PSB2IDw9IDg5OTk5OTk5OTk5OTk5OTlcclxuICAgICAgICAgICAgICAvLyAwIDw9ICh2ICUgMWUxNCkgPD0gOTk5OTk5OTk5OTk5OTlcclxuICAgICAgICAgICAgICBjLnB1c2godiAlIDFlMTQpO1xyXG4gICAgICAgICAgICAgIGkgKz0gMjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaSA9IGsgLyAyO1xyXG5cclxuICAgICAgICAvLyBOb2RlLmpzIHN1cHBvcnRpbmcgY3J5cHRvLnJhbmRvbUJ5dGVzLlxyXG4gICAgICAgIH0gZWxzZSBpZiAoY3J5cHRvLnJhbmRvbUJ5dGVzKSB7XHJcblxyXG4gICAgICAgICAgLy8gYnVmZmVyXHJcbiAgICAgICAgICBhID0gY3J5cHRvLnJhbmRvbUJ5dGVzKGsgKj0gNyk7XHJcblxyXG4gICAgICAgICAgZm9yICg7IGkgPCBrOykge1xyXG5cclxuICAgICAgICAgICAgLy8gMHgxMDAwMDAwMDAwMDAwIGlzIDJeNDgsIDB4MTAwMDAwMDAwMDAgaXMgMl40MFxyXG4gICAgICAgICAgICAvLyAweDEwMDAwMDAwMCBpcyAyXjMyLCAweDEwMDAwMDAgaXMgMl4yNFxyXG4gICAgICAgICAgICAvLyAxMTExMSAxMTExMTExMSAxMTExMTExMSAxMTExMTExMSAxMTExMTExMSAxMTExMTExMSAxMTExMTExMVxyXG4gICAgICAgICAgICAvLyAwIDw9IHYgPCA5MDA3MTk5MjU0NzQwOTkyXHJcbiAgICAgICAgICAgIHYgPSAoKGFbaV0gJiAzMSkgKiAweDEwMDAwMDAwMDAwMDApICsgKGFbaSArIDFdICogMHgxMDAwMDAwMDAwMCkgK1xyXG4gICAgICAgICAgICAgICAoYVtpICsgMl0gKiAweDEwMDAwMDAwMCkgKyAoYVtpICsgM10gKiAweDEwMDAwMDApICtcclxuICAgICAgICAgICAgICAgKGFbaSArIDRdIDw8IDE2KSArIChhW2kgKyA1XSA8PCA4KSArIGFbaSArIDZdO1xyXG5cclxuICAgICAgICAgICAgaWYgKHYgPj0gOWUxNSkge1xyXG4gICAgICAgICAgICAgIGNyeXB0by5yYW5kb21CeXRlcyg3KS5jb3B5KGEsIGkpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgICAgICAgICAvLyAwIDw9ICh2ICUgMWUxNCkgPD0gOTk5OTk5OTk5OTk5OTlcclxuICAgICAgICAgICAgICBjLnB1c2godiAlIDFlMTQpO1xyXG4gICAgICAgICAgICAgIGkgKz0gNztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaSA9IGsgLyA3O1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBDUllQVE8gPSBmYWxzZTtcclxuICAgICAgICAgIHRocm93IEVycm9yXHJcbiAgICAgICAgICAgKGJpZ251bWJlckVycm9yICsgJ2NyeXB0byB1bmF2YWlsYWJsZScpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVXNlIE1hdGgucmFuZG9tLlxyXG4gICAgICBpZiAoIUNSWVBUTykge1xyXG5cclxuICAgICAgICBmb3IgKDsgaSA8IGs7KSB7XHJcbiAgICAgICAgICB2ID0gcmFuZG9tNTNiaXRJbnQoKTtcclxuICAgICAgICAgIGlmICh2IDwgOWUxNSkgY1tpKytdID0gdiAlIDFlMTQ7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBrID0gY1stLWldO1xyXG4gICAgICBkcCAlPSBMT0dfQkFTRTtcclxuXHJcbiAgICAgIC8vIENvbnZlcnQgdHJhaWxpbmcgZGlnaXRzIHRvIHplcm9zIGFjY29yZGluZyB0byBkcC5cclxuICAgICAgaWYgKGsgJiYgZHApIHtcclxuICAgICAgICB2ID0gUE9XU19URU5bTE9HX0JBU0UgLSBkcF07XHJcbiAgICAgICAgY1tpXSA9IG1hdGhmbG9vcihrIC8gdikgKiB2O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZW1vdmUgdHJhaWxpbmcgZWxlbWVudHMgd2hpY2ggYXJlIHplcm8uXHJcbiAgICAgIGZvciAoOyBjW2ldID09PSAwOyBjLnBvcCgpLCBpLS0pO1xyXG5cclxuICAgICAgLy8gWmVybz9cclxuICAgICAgaWYgKGkgPCAwKSB7XHJcbiAgICAgICAgYyA9IFtlID0gMF07XHJcbiAgICAgIH0gZWxzZSB7XHJcblxyXG4gICAgICAgIC8vIFJlbW92ZSBsZWFkaW5nIGVsZW1lbnRzIHdoaWNoIGFyZSB6ZXJvIGFuZCBhZGp1c3QgZXhwb25lbnQgYWNjb3JkaW5nbHkuXHJcbiAgICAgICAgZm9yIChlID0gLTEgOyBjWzBdID09PSAwOyBjLnNwbGljZSgwLCAxKSwgZSAtPSBMT0dfQkFTRSk7XHJcblxyXG4gICAgICAgIC8vIENvdW50IHRoZSBkaWdpdHMgb2YgdGhlIGZpcnN0IGVsZW1lbnQgb2YgYyB0byBkZXRlcm1pbmUgbGVhZGluZyB6ZXJvcywgYW5kLi4uXHJcbiAgICAgICAgZm9yIChpID0gMSwgdiA9IGNbMF07IHYgPj0gMTA7IHYgLz0gMTAsIGkrKyk7XHJcblxyXG4gICAgICAgIC8vIGFkanVzdCB0aGUgZXhwb25lbnQgYWNjb3JkaW5nbHkuXHJcbiAgICAgICAgaWYgKGkgPCBMT0dfQkFTRSkgZSAtPSBMT0dfQkFTRSAtIGk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJhbmQuZSA9IGU7XHJcbiAgICAgIHJhbmQuYyA9IGM7XHJcbiAgICAgIHJldHVybiByYW5kO1xyXG4gICAgfTtcclxuICB9KSgpO1xyXG5cclxuXHJcbiAgIC8qXHJcbiAgICogUmV0dXJuIGEgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSBzdW0gb2YgdGhlIGFyZ3VtZW50cy5cclxuICAgKlxyXG4gICAqIGFyZ3VtZW50cyB7bnVtYmVyfHN0cmluZ3xCaWdOdW1iZXJ9XHJcbiAgICovXHJcbiAgQmlnTnVtYmVyLnN1bSA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciBpID0gMSxcclxuICAgICAgYXJncyA9IGFyZ3VtZW50cyxcclxuICAgICAgc3VtID0gbmV3IEJpZ051bWJlcihhcmdzWzBdKTtcclxuICAgIGZvciAoOyBpIDwgYXJncy5sZW5ndGg7KSBzdW0gPSBzdW0ucGx1cyhhcmdzW2krK10pO1xyXG4gICAgcmV0dXJuIHN1bTtcclxuICB9O1xyXG5cclxuXHJcbiAgLy8gUFJJVkFURSBGVU5DVElPTlNcclxuXHJcblxyXG4gIC8vIENhbGxlZCBieSBCaWdOdW1iZXIgYW5kIEJpZ051bWJlci5wcm90b3R5cGUudG9TdHJpbmcuXHJcbiAgY29udmVydEJhc2UgPSAoZnVuY3Rpb24gKCkge1xyXG4gICAgdmFyIGRlY2ltYWwgPSAnMDEyMzQ1Njc4OSc7XHJcblxyXG4gICAgLypcclxuICAgICAqIENvbnZlcnQgc3RyaW5nIG9mIGJhc2VJbiB0byBhbiBhcnJheSBvZiBudW1iZXJzIG9mIGJhc2VPdXQuXHJcbiAgICAgKiBFZy4gdG9CYXNlT3V0KCcyNTUnLCAxMCwgMTYpIHJldHVybnMgWzE1LCAxNV0uXHJcbiAgICAgKiBFZy4gdG9CYXNlT3V0KCdmZicsIDE2LCAxMCkgcmV0dXJucyBbMiwgNSwgNV0uXHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIHRvQmFzZU91dChzdHIsIGJhc2VJbiwgYmFzZU91dCwgYWxwaGFiZXQpIHtcclxuICAgICAgdmFyIGosXHJcbiAgICAgICAgYXJyID0gWzBdLFxyXG4gICAgICAgIGFyckwsXHJcbiAgICAgICAgaSA9IDAsXHJcbiAgICAgICAgbGVuID0gc3RyLmxlbmd0aDtcclxuXHJcbiAgICAgIGZvciAoOyBpIDwgbGVuOykge1xyXG4gICAgICAgIGZvciAoYXJyTCA9IGFyci5sZW5ndGg7IGFyckwtLTsgYXJyW2FyckxdICo9IGJhc2VJbik7XHJcblxyXG4gICAgICAgIGFyclswXSArPSBhbHBoYWJldC5pbmRleE9mKHN0ci5jaGFyQXQoaSsrKSk7XHJcblxyXG4gICAgICAgIGZvciAoaiA9IDA7IGogPCBhcnIubGVuZ3RoOyBqKyspIHtcclxuXHJcbiAgICAgICAgICBpZiAoYXJyW2pdID4gYmFzZU91dCAtIDEpIHtcclxuICAgICAgICAgICAgaWYgKGFycltqICsgMV0gPT0gbnVsbCkgYXJyW2ogKyAxXSA9IDA7XHJcbiAgICAgICAgICAgIGFycltqICsgMV0gKz0gYXJyW2pdIC8gYmFzZU91dCB8IDA7XHJcbiAgICAgICAgICAgIGFycltqXSAlPSBiYXNlT3V0O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIGFyci5yZXZlcnNlKCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ29udmVydCBhIG51bWVyaWMgc3RyaW5nIG9mIGJhc2VJbiB0byBhIG51bWVyaWMgc3RyaW5nIG9mIGJhc2VPdXQuXHJcbiAgICAvLyBJZiB0aGUgY2FsbGVyIGlzIHRvU3RyaW5nLCB3ZSBhcmUgY29udmVydGluZyBmcm9tIGJhc2UgMTAgdG8gYmFzZU91dC5cclxuICAgIC8vIElmIHRoZSBjYWxsZXIgaXMgQmlnTnVtYmVyLCB3ZSBhcmUgY29udmVydGluZyBmcm9tIGJhc2VJbiB0byBiYXNlIDEwLlxyXG4gICAgcmV0dXJuIGZ1bmN0aW9uIChzdHIsIGJhc2VJbiwgYmFzZU91dCwgc2lnbiwgY2FsbGVySXNUb1N0cmluZykge1xyXG4gICAgICB2YXIgYWxwaGFiZXQsIGQsIGUsIGssIHIsIHgsIHhjLCB5LFxyXG4gICAgICAgIGkgPSBzdHIuaW5kZXhPZignLicpLFxyXG4gICAgICAgIGRwID0gREVDSU1BTF9QTEFDRVMsXHJcbiAgICAgICAgcm0gPSBST1VORElOR19NT0RFO1xyXG5cclxuICAgICAgLy8gTm9uLWludGVnZXIuXHJcbiAgICAgIGlmIChpID49IDApIHtcclxuICAgICAgICBrID0gUE9XX1BSRUNJU0lPTjtcclxuXHJcbiAgICAgICAgLy8gVW5saW1pdGVkIHByZWNpc2lvbi5cclxuICAgICAgICBQT1dfUFJFQ0lTSU9OID0gMDtcclxuICAgICAgICBzdHIgPSBzdHIucmVwbGFjZSgnLicsICcnKTtcclxuICAgICAgICB5ID0gbmV3IEJpZ051bWJlcihiYXNlSW4pO1xyXG4gICAgICAgIHggPSB5LnBvdyhzdHIubGVuZ3RoIC0gaSk7XHJcbiAgICAgICAgUE9XX1BSRUNJU0lPTiA9IGs7XHJcblxyXG4gICAgICAgIC8vIENvbnZlcnQgc3RyIGFzIGlmIGFuIGludGVnZXIsIHRoZW4gcmVzdG9yZSB0aGUgZnJhY3Rpb24gcGFydCBieSBkaXZpZGluZyB0aGVcclxuICAgICAgICAvLyByZXN1bHQgYnkgaXRzIGJhc2UgcmFpc2VkIHRvIGEgcG93ZXIuXHJcblxyXG4gICAgICAgIHkuYyA9IHRvQmFzZU91dCh0b0ZpeGVkUG9pbnQoY29lZmZUb1N0cmluZyh4LmMpLCB4LmUsICcwJyksXHJcbiAgICAgICAgIDEwLCBiYXNlT3V0LCBkZWNpbWFsKTtcclxuICAgICAgICB5LmUgPSB5LmMubGVuZ3RoO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDb252ZXJ0IHRoZSBudW1iZXIgYXMgaW50ZWdlci5cclxuXHJcbiAgICAgIHhjID0gdG9CYXNlT3V0KHN0ciwgYmFzZUluLCBiYXNlT3V0LCBjYWxsZXJJc1RvU3RyaW5nXHJcbiAgICAgICA/IChhbHBoYWJldCA9IEFMUEhBQkVULCBkZWNpbWFsKVxyXG4gICAgICAgOiAoYWxwaGFiZXQgPSBkZWNpbWFsLCBBTFBIQUJFVCkpO1xyXG5cclxuICAgICAgLy8geGMgbm93IHJlcHJlc2VudHMgc3RyIGFzIGFuIGludGVnZXIgYW5kIGNvbnZlcnRlZCB0byBiYXNlT3V0LiBlIGlzIHRoZSBleHBvbmVudC5cclxuICAgICAgZSA9IGsgPSB4Yy5sZW5ndGg7XHJcblxyXG4gICAgICAvLyBSZW1vdmUgdHJhaWxpbmcgemVyb3MuXHJcbiAgICAgIGZvciAoOyB4Y1stLWtdID09IDA7IHhjLnBvcCgpKTtcclxuXHJcbiAgICAgIC8vIFplcm8/XHJcbiAgICAgIGlmICgheGNbMF0pIHJldHVybiBhbHBoYWJldC5jaGFyQXQoMCk7XHJcblxyXG4gICAgICAvLyBEb2VzIHN0ciByZXByZXNlbnQgYW4gaW50ZWdlcj8gSWYgc28sIG5vIG5lZWQgZm9yIHRoZSBkaXZpc2lvbi5cclxuICAgICAgaWYgKGkgPCAwKSB7XHJcbiAgICAgICAgLS1lO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHguYyA9IHhjO1xyXG4gICAgICAgIHguZSA9IGU7XHJcblxyXG4gICAgICAgIC8vIFRoZSBzaWduIGlzIG5lZWRlZCBmb3IgY29ycmVjdCByb3VuZGluZy5cclxuICAgICAgICB4LnMgPSBzaWduO1xyXG4gICAgICAgIHggPSBkaXYoeCwgeSwgZHAsIHJtLCBiYXNlT3V0KTtcclxuICAgICAgICB4YyA9IHguYztcclxuICAgICAgICByID0geC5yO1xyXG4gICAgICAgIGUgPSB4LmU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIHhjIG5vdyByZXByZXNlbnRzIHN0ciBjb252ZXJ0ZWQgdG8gYmFzZU91dC5cclxuXHJcbiAgICAgIC8vIFRoZSBpbmRleCBvZiB0aGUgcm91bmRpbmcgZGlnaXQuXHJcbiAgICAgIGQgPSBlICsgZHAgKyAxO1xyXG5cclxuICAgICAgLy8gVGhlIHJvdW5kaW5nIGRpZ2l0OiB0aGUgZGlnaXQgdG8gdGhlIHJpZ2h0IG9mIHRoZSBkaWdpdCB0aGF0IG1heSBiZSByb3VuZGVkIHVwLlxyXG4gICAgICBpID0geGNbZF07XHJcblxyXG4gICAgICAvLyBMb29rIGF0IHRoZSByb3VuZGluZyBkaWdpdHMgYW5kIG1vZGUgdG8gZGV0ZXJtaW5lIHdoZXRoZXIgdG8gcm91bmQgdXAuXHJcblxyXG4gICAgICBrID0gYmFzZU91dCAvIDI7XHJcbiAgICAgIHIgPSByIHx8IGQgPCAwIHx8IHhjW2QgKyAxXSAhPSBudWxsO1xyXG5cclxuICAgICAgciA9IHJtIDwgNCA/IChpICE9IG51bGwgfHwgcikgJiYgKHJtID09IDAgfHwgcm0gPT0gKHgucyA8IDAgPyAzIDogMikpXHJcbiAgICAgICAgICAgIDogaSA+IGsgfHwgaSA9PSBrICYmKHJtID09IDQgfHwgciB8fCBybSA9PSA2ICYmIHhjW2QgLSAxXSAmIDEgfHxcclxuICAgICAgICAgICAgIHJtID09ICh4LnMgPCAwID8gOCA6IDcpKTtcclxuXHJcbiAgICAgIC8vIElmIHRoZSBpbmRleCBvZiB0aGUgcm91bmRpbmcgZGlnaXQgaXMgbm90IGdyZWF0ZXIgdGhhbiB6ZXJvLCBvciB4YyByZXByZXNlbnRzXHJcbiAgICAgIC8vIHplcm8sIHRoZW4gdGhlIHJlc3VsdCBvZiB0aGUgYmFzZSBjb252ZXJzaW9uIGlzIHplcm8gb3IsIGlmIHJvdW5kaW5nIHVwLCBhIHZhbHVlXHJcbiAgICAgIC8vIHN1Y2ggYXMgMC4wMDAwMS5cclxuICAgICAgaWYgKGQgPCAxIHx8ICF4Y1swXSkge1xyXG5cclxuICAgICAgICAvLyAxXi1kcCBvciAwXHJcbiAgICAgICAgc3RyID0gciA/IHRvRml4ZWRQb2ludChhbHBoYWJldC5jaGFyQXQoMSksIC1kcCwgYWxwaGFiZXQuY2hhckF0KDApKSA6IGFscGhhYmV0LmNoYXJBdCgwKTtcclxuICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgLy8gVHJ1bmNhdGUgeGMgdG8gdGhlIHJlcXVpcmVkIG51bWJlciBvZiBkZWNpbWFsIHBsYWNlcy5cclxuICAgICAgICB4Yy5sZW5ndGggPSBkO1xyXG5cclxuICAgICAgICAvLyBSb3VuZCB1cD9cclxuICAgICAgICBpZiAocikge1xyXG5cclxuICAgICAgICAgIC8vIFJvdW5kaW5nIHVwIG1heSBtZWFuIHRoZSBwcmV2aW91cyBkaWdpdCBoYXMgdG8gYmUgcm91bmRlZCB1cCBhbmQgc28gb24uXHJcbiAgICAgICAgICBmb3IgKC0tYmFzZU91dDsgKyt4Y1stLWRdID4gYmFzZU91dDspIHtcclxuICAgICAgICAgICAgeGNbZF0gPSAwO1xyXG5cclxuICAgICAgICAgICAgaWYgKCFkKSB7XHJcbiAgICAgICAgICAgICAgKytlO1xyXG4gICAgICAgICAgICAgIHhjID0gWzFdLmNvbmNhdCh4Yyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIERldGVybWluZSB0cmFpbGluZyB6ZXJvcy5cclxuICAgICAgICBmb3IgKGsgPSB4Yy5sZW5ndGg7ICF4Y1stLWtdOyk7XHJcblxyXG4gICAgICAgIC8vIEUuZy4gWzQsIDExLCAxNV0gYmVjb21lcyA0YmYuXHJcbiAgICAgICAgZm9yIChpID0gMCwgc3RyID0gJyc7IGkgPD0gazsgc3RyICs9IGFscGhhYmV0LmNoYXJBdCh4Y1tpKytdKSk7XHJcblxyXG4gICAgICAgIC8vIEFkZCBsZWFkaW5nIHplcm9zLCBkZWNpbWFsIHBvaW50IGFuZCB0cmFpbGluZyB6ZXJvcyBhcyByZXF1aXJlZC5cclxuICAgICAgICBzdHIgPSB0b0ZpeGVkUG9pbnQoc3RyLCBlLCBhbHBoYWJldC5jaGFyQXQoMCkpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBUaGUgY2FsbGVyIHdpbGwgYWRkIHRoZSBzaWduLlxyXG4gICAgICByZXR1cm4gc3RyO1xyXG4gICAgfTtcclxuICB9KSgpO1xyXG5cclxuXHJcbiAgLy8gUGVyZm9ybSBkaXZpc2lvbiBpbiB0aGUgc3BlY2lmaWVkIGJhc2UuIENhbGxlZCBieSBkaXYgYW5kIGNvbnZlcnRCYXNlLlxyXG4gIGRpdiA9IChmdW5jdGlvbiAoKSB7XHJcblxyXG4gICAgLy8gQXNzdW1lIG5vbi16ZXJvIHggYW5kIGsuXHJcbiAgICBmdW5jdGlvbiBtdWx0aXBseSh4LCBrLCBiYXNlKSB7XHJcbiAgICAgIHZhciBtLCB0ZW1wLCB4bG8sIHhoaSxcclxuICAgICAgICBjYXJyeSA9IDAsXHJcbiAgICAgICAgaSA9IHgubGVuZ3RoLFxyXG4gICAgICAgIGtsbyA9IGsgJSBTUVJUX0JBU0UsXHJcbiAgICAgICAga2hpID0gayAvIFNRUlRfQkFTRSB8IDA7XHJcblxyXG4gICAgICBmb3IgKHggPSB4LnNsaWNlKCk7IGktLTspIHtcclxuICAgICAgICB4bG8gPSB4W2ldICUgU1FSVF9CQVNFO1xyXG4gICAgICAgIHhoaSA9IHhbaV0gLyBTUVJUX0JBU0UgfCAwO1xyXG4gICAgICAgIG0gPSBraGkgKiB4bG8gKyB4aGkgKiBrbG87XHJcbiAgICAgICAgdGVtcCA9IGtsbyAqIHhsbyArICgobSAlIFNRUlRfQkFTRSkgKiBTUVJUX0JBU0UpICsgY2Fycnk7XHJcbiAgICAgICAgY2FycnkgPSAodGVtcCAvIGJhc2UgfCAwKSArIChtIC8gU1FSVF9CQVNFIHwgMCkgKyBraGkgKiB4aGk7XHJcbiAgICAgICAgeFtpXSA9IHRlbXAgJSBiYXNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoY2FycnkpIHggPSBbY2FycnldLmNvbmNhdCh4KTtcclxuXHJcbiAgICAgIHJldHVybiB4O1xyXG4gICAgfVxyXG5cclxuICAgIGZ1bmN0aW9uIGNvbXBhcmUoYSwgYiwgYUwsIGJMKSB7XHJcbiAgICAgIHZhciBpLCBjbXA7XHJcblxyXG4gICAgICBpZiAoYUwgIT0gYkwpIHtcclxuICAgICAgICBjbXAgPSBhTCA+IGJMID8gMSA6IC0xO1xyXG4gICAgICB9IGVsc2Uge1xyXG5cclxuICAgICAgICBmb3IgKGkgPSBjbXAgPSAwOyBpIDwgYUw7IGkrKykge1xyXG5cclxuICAgICAgICAgIGlmIChhW2ldICE9IGJbaV0pIHtcclxuICAgICAgICAgICAgY21wID0gYVtpXSA+IGJbaV0gPyAxIDogLTE7XHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIGNtcDtcclxuICAgIH1cclxuXHJcbiAgICBmdW5jdGlvbiBzdWJ0cmFjdChhLCBiLCBhTCwgYmFzZSkge1xyXG4gICAgICB2YXIgaSA9IDA7XHJcblxyXG4gICAgICAvLyBTdWJ0cmFjdCBiIGZyb20gYS5cclxuICAgICAgZm9yICg7IGFMLS07KSB7XHJcbiAgICAgICAgYVthTF0gLT0gaTtcclxuICAgICAgICBpID0gYVthTF0gPCBiW2FMXSA/IDEgOiAwO1xyXG4gICAgICAgIGFbYUxdID0gaSAqIGJhc2UgKyBhW2FMXSAtIGJbYUxdO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZW1vdmUgbGVhZGluZyB6ZXJvcy5cclxuICAgICAgZm9yICg7ICFhWzBdICYmIGEubGVuZ3RoID4gMTsgYS5zcGxpY2UoMCwgMSkpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIHg6IGRpdmlkZW5kLCB5OiBkaXZpc29yLlxyXG4gICAgcmV0dXJuIGZ1bmN0aW9uICh4LCB5LCBkcCwgcm0sIGJhc2UpIHtcclxuICAgICAgdmFyIGNtcCwgZSwgaSwgbW9yZSwgbiwgcHJvZCwgcHJvZEwsIHEsIHFjLCByZW0sIHJlbUwsIHJlbTAsIHhpLCB4TCwgeWMwLFxyXG4gICAgICAgIHlMLCB5eixcclxuICAgICAgICBzID0geC5zID09IHkucyA/IDEgOiAtMSxcclxuICAgICAgICB4YyA9IHguYyxcclxuICAgICAgICB5YyA9IHkuYztcclxuXHJcbiAgICAgIC8vIEVpdGhlciBOYU4sIEluZmluaXR5IG9yIDA/XHJcbiAgICAgIGlmICgheGMgfHwgIXhjWzBdIHx8ICF5YyB8fCAheWNbMF0pIHtcclxuXHJcbiAgICAgICAgcmV0dXJuIG5ldyBCaWdOdW1iZXIoXHJcblxyXG4gICAgICAgICAvLyBSZXR1cm4gTmFOIGlmIGVpdGhlciBOYU4sIG9yIGJvdGggSW5maW5pdHkgb3IgMC5cclxuICAgICAgICAgIXgucyB8fCAheS5zIHx8ICh4YyA/IHljICYmIHhjWzBdID09IHljWzBdIDogIXljKSA/IE5hTiA6XHJcblxyXG4gICAgICAgICAgLy8gUmV0dXJuIMKxMCBpZiB4IGlzIMKxMCBvciB5IGlzIMKxSW5maW5pdHksIG9yIHJldHVybiDCsUluZmluaXR5IGFzIHkgaXMgwrEwLlxyXG4gICAgICAgICAgeGMgJiYgeGNbMF0gPT0gMCB8fCAheWMgPyBzICogMCA6IHMgLyAwXHJcbiAgICAgICApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBxID0gbmV3IEJpZ051bWJlcihzKTtcclxuICAgICAgcWMgPSBxLmMgPSBbXTtcclxuICAgICAgZSA9IHguZSAtIHkuZTtcclxuICAgICAgcyA9IGRwICsgZSArIDE7XHJcblxyXG4gICAgICBpZiAoIWJhc2UpIHtcclxuICAgICAgICBiYXNlID0gQkFTRTtcclxuICAgICAgICBlID0gYml0Rmxvb3IoeC5lIC8gTE9HX0JBU0UpIC0gYml0Rmxvb3IoeS5lIC8gTE9HX0JBU0UpO1xyXG4gICAgICAgIHMgPSBzIC8gTE9HX0JBU0UgfCAwO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZXN1bHQgZXhwb25lbnQgbWF5IGJlIG9uZSBsZXNzIHRoZW4gdGhlIGN1cnJlbnQgdmFsdWUgb2YgZS5cclxuICAgICAgLy8gVGhlIGNvZWZmaWNpZW50cyBvZiB0aGUgQmlnTnVtYmVycyBmcm9tIGNvbnZlcnRCYXNlIG1heSBoYXZlIHRyYWlsaW5nIHplcm9zLlxyXG4gICAgICBmb3IgKGkgPSAwOyB5Y1tpXSA9PSAoeGNbaV0gfHwgMCk7IGkrKyk7XHJcblxyXG4gICAgICBpZiAoeWNbaV0gPiAoeGNbaV0gfHwgMCkpIGUtLTtcclxuXHJcbiAgICAgIGlmIChzIDwgMCkge1xyXG4gICAgICAgIHFjLnB1c2goMSk7XHJcbiAgICAgICAgbW9yZSA9IHRydWU7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgeEwgPSB4Yy5sZW5ndGg7XHJcbiAgICAgICAgeUwgPSB5Yy5sZW5ndGg7XHJcbiAgICAgICAgaSA9IDA7XHJcbiAgICAgICAgcyArPSAyO1xyXG5cclxuICAgICAgICAvLyBOb3JtYWxpc2UgeGMgYW5kIHljIHNvIGhpZ2hlc3Qgb3JkZXIgZGlnaXQgb2YgeWMgaXMgPj0gYmFzZSAvIDIuXHJcblxyXG4gICAgICAgIG4gPSBtYXRoZmxvb3IoYmFzZSAvICh5Y1swXSArIDEpKTtcclxuXHJcbiAgICAgICAgLy8gTm90IG5lY2Vzc2FyeSwgYnV0IHRvIGhhbmRsZSBvZGQgYmFzZXMgd2hlcmUgeWNbMF0gPT0gKGJhc2UgLyAyKSAtIDEuXHJcbiAgICAgICAgLy8gaWYgKG4gPiAxIHx8IG4rKyA9PSAxICYmIHljWzBdIDwgYmFzZSAvIDIpIHtcclxuICAgICAgICBpZiAobiA+IDEpIHtcclxuICAgICAgICAgIHljID0gbXVsdGlwbHkoeWMsIG4sIGJhc2UpO1xyXG4gICAgICAgICAgeGMgPSBtdWx0aXBseSh4YywgbiwgYmFzZSk7XHJcbiAgICAgICAgICB5TCA9IHljLmxlbmd0aDtcclxuICAgICAgICAgIHhMID0geGMubGVuZ3RoO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgeGkgPSB5TDtcclxuICAgICAgICByZW0gPSB4Yy5zbGljZSgwLCB5TCk7XHJcbiAgICAgICAgcmVtTCA9IHJlbS5sZW5ndGg7XHJcblxyXG4gICAgICAgIC8vIEFkZCB6ZXJvcyB0byBtYWtlIHJlbWFpbmRlciBhcyBsb25nIGFzIGRpdmlzb3IuXHJcbiAgICAgICAgZm9yICg7IHJlbUwgPCB5TDsgcmVtW3JlbUwrK10gPSAwKTtcclxuICAgICAgICB5eiA9IHljLnNsaWNlKCk7XHJcbiAgICAgICAgeXogPSBbMF0uY29uY2F0KHl6KTtcclxuICAgICAgICB5YzAgPSB5Y1swXTtcclxuICAgICAgICBpZiAoeWNbMV0gPj0gYmFzZSAvIDIpIHljMCsrO1xyXG4gICAgICAgIC8vIE5vdCBuZWNlc3NhcnksIGJ1dCB0byBwcmV2ZW50IHRyaWFsIGRpZ2l0IG4gPiBiYXNlLCB3aGVuIHVzaW5nIGJhc2UgMy5cclxuICAgICAgICAvLyBlbHNlIGlmIChiYXNlID09IDMgJiYgeWMwID09IDEpIHljMCA9IDEgKyAxZS0xNTtcclxuXHJcbiAgICAgICAgZG8ge1xyXG4gICAgICAgICAgbiA9IDA7XHJcblxyXG4gICAgICAgICAgLy8gQ29tcGFyZSBkaXZpc29yIGFuZCByZW1haW5kZXIuXHJcbiAgICAgICAgICBjbXAgPSBjb21wYXJlKHljLCByZW0sIHlMLCByZW1MKTtcclxuXHJcbiAgICAgICAgICAvLyBJZiBkaXZpc29yIDwgcmVtYWluZGVyLlxyXG4gICAgICAgICAgaWYgKGNtcCA8IDApIHtcclxuXHJcbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSB0cmlhbCBkaWdpdCwgbi5cclxuXHJcbiAgICAgICAgICAgIHJlbTAgPSByZW1bMF07XHJcbiAgICAgICAgICAgIGlmICh5TCAhPSByZW1MKSByZW0wID0gcmVtMCAqIGJhc2UgKyAocmVtWzFdIHx8IDApO1xyXG5cclxuICAgICAgICAgICAgLy8gbiBpcyBob3cgbWFueSB0aW1lcyB0aGUgZGl2aXNvciBnb2VzIGludG8gdGhlIGN1cnJlbnQgcmVtYWluZGVyLlxyXG4gICAgICAgICAgICBuID0gbWF0aGZsb29yKHJlbTAgLyB5YzApO1xyXG5cclxuICAgICAgICAgICAgLy8gIEFsZ29yaXRobTpcclxuICAgICAgICAgICAgLy8gIHByb2R1Y3QgPSBkaXZpc29yIG11bHRpcGxpZWQgYnkgdHJpYWwgZGlnaXQgKG4pLlxyXG4gICAgICAgICAgICAvLyAgQ29tcGFyZSBwcm9kdWN0IGFuZCByZW1haW5kZXIuXHJcbiAgICAgICAgICAgIC8vICBJZiBwcm9kdWN0IGlzIGdyZWF0ZXIgdGhhbiByZW1haW5kZXI6XHJcbiAgICAgICAgICAgIC8vICAgIFN1YnRyYWN0IGRpdmlzb3IgZnJvbSBwcm9kdWN0LCBkZWNyZW1lbnQgdHJpYWwgZGlnaXQuXHJcbiAgICAgICAgICAgIC8vICBTdWJ0cmFjdCBwcm9kdWN0IGZyb20gcmVtYWluZGVyLlxyXG4gICAgICAgICAgICAvLyAgSWYgcHJvZHVjdCB3YXMgbGVzcyB0aGFuIHJlbWFpbmRlciBhdCB0aGUgbGFzdCBjb21wYXJlOlxyXG4gICAgICAgICAgICAvLyAgICBDb21wYXJlIG5ldyByZW1haW5kZXIgYW5kIGRpdmlzb3IuXHJcbiAgICAgICAgICAgIC8vICAgIElmIHJlbWFpbmRlciBpcyBncmVhdGVyIHRoYW4gZGl2aXNvcjpcclxuICAgICAgICAgICAgLy8gICAgICBTdWJ0cmFjdCBkaXZpc29yIGZyb20gcmVtYWluZGVyLCBpbmNyZW1lbnQgdHJpYWwgZGlnaXQuXHJcblxyXG4gICAgICAgICAgICBpZiAobiA+IDEpIHtcclxuXHJcbiAgICAgICAgICAgICAgLy8gbiBtYXkgYmUgPiBiYXNlIG9ubHkgd2hlbiBiYXNlIGlzIDMuXHJcbiAgICAgICAgICAgICAgaWYgKG4gPj0gYmFzZSkgbiA9IGJhc2UgLSAxO1xyXG5cclxuICAgICAgICAgICAgICAvLyBwcm9kdWN0ID0gZGl2aXNvciAqIHRyaWFsIGRpZ2l0LlxyXG4gICAgICAgICAgICAgIHByb2QgPSBtdWx0aXBseSh5YywgbiwgYmFzZSk7XHJcbiAgICAgICAgICAgICAgcHJvZEwgPSBwcm9kLmxlbmd0aDtcclxuICAgICAgICAgICAgICByZW1MID0gcmVtLmxlbmd0aDtcclxuXHJcbiAgICAgICAgICAgICAgLy8gQ29tcGFyZSBwcm9kdWN0IGFuZCByZW1haW5kZXIuXHJcbiAgICAgICAgICAgICAgLy8gSWYgcHJvZHVjdCA+IHJlbWFpbmRlciB0aGVuIHRyaWFsIGRpZ2l0IG4gdG9vIGhpZ2guXHJcbiAgICAgICAgICAgICAgLy8gbiBpcyAxIHRvbyBoaWdoIGFib3V0IDUlIG9mIHRoZSB0aW1lLCBhbmQgaXMgbm90IGtub3duIHRvIGhhdmVcclxuICAgICAgICAgICAgICAvLyBldmVyIGJlZW4gbW9yZSB0aGFuIDEgdG9vIGhpZ2guXHJcbiAgICAgICAgICAgICAgd2hpbGUgKGNvbXBhcmUocHJvZCwgcmVtLCBwcm9kTCwgcmVtTCkgPT0gMSkge1xyXG4gICAgICAgICAgICAgICAgbi0tO1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIFN1YnRyYWN0IGRpdmlzb3IgZnJvbSBwcm9kdWN0LlxyXG4gICAgICAgICAgICAgICAgc3VidHJhY3QocHJvZCwgeUwgPCBwcm9kTCA/IHl6IDogeWMsIHByb2RMLCBiYXNlKTtcclxuICAgICAgICAgICAgICAgIHByb2RMID0gcHJvZC5sZW5ndGg7XHJcbiAgICAgICAgICAgICAgICBjbXAgPSAxO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgICAgICAgLy8gbiBpcyAwIG9yIDEsIGNtcCBpcyAtMS5cclxuICAgICAgICAgICAgICAvLyBJZiBuIGlzIDAsIHRoZXJlIGlzIG5vIG5lZWQgdG8gY29tcGFyZSB5YyBhbmQgcmVtIGFnYWluIGJlbG93LFxyXG4gICAgICAgICAgICAgIC8vIHNvIGNoYW5nZSBjbXAgdG8gMSB0byBhdm9pZCBpdC5cclxuICAgICAgICAgICAgICAvLyBJZiBuIGlzIDEsIGxlYXZlIGNtcCBhcyAtMSwgc28geWMgYW5kIHJlbSBhcmUgY29tcGFyZWQgYWdhaW4uXHJcbiAgICAgICAgICAgICAgaWYgKG4gPT0gMCkge1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIGRpdmlzb3IgPCByZW1haW5kZXIsIHNvIG4gbXVzdCBiZSBhdCBsZWFzdCAxLlxyXG4gICAgICAgICAgICAgICAgY21wID0gbiA9IDE7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAvLyBwcm9kdWN0ID0gZGl2aXNvclxyXG4gICAgICAgICAgICAgIHByb2QgPSB5Yy5zbGljZSgpO1xyXG4gICAgICAgICAgICAgIHByb2RMID0gcHJvZC5sZW5ndGg7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmIChwcm9kTCA8IHJlbUwpIHByb2QgPSBbMF0uY29uY2F0KHByb2QpO1xyXG5cclxuICAgICAgICAgICAgLy8gU3VidHJhY3QgcHJvZHVjdCBmcm9tIHJlbWFpbmRlci5cclxuICAgICAgICAgICAgc3VidHJhY3QocmVtLCBwcm9kLCByZW1MLCBiYXNlKTtcclxuICAgICAgICAgICAgcmVtTCA9IHJlbS5sZW5ndGg7XHJcblxyXG4gICAgICAgICAgICAgLy8gSWYgcHJvZHVjdCB3YXMgPCByZW1haW5kZXIuXHJcbiAgICAgICAgICAgIGlmIChjbXAgPT0gLTEpIHtcclxuXHJcbiAgICAgICAgICAgICAgLy8gQ29tcGFyZSBkaXZpc29yIGFuZCBuZXcgcmVtYWluZGVyLlxyXG4gICAgICAgICAgICAgIC8vIElmIGRpdmlzb3IgPCBuZXcgcmVtYWluZGVyLCBzdWJ0cmFjdCBkaXZpc29yIGZyb20gcmVtYWluZGVyLlxyXG4gICAgICAgICAgICAgIC8vIFRyaWFsIGRpZ2l0IG4gdG9vIGxvdy5cclxuICAgICAgICAgICAgICAvLyBuIGlzIDEgdG9vIGxvdyBhYm91dCA1JSBvZiB0aGUgdGltZSwgYW5kIHZlcnkgcmFyZWx5IDIgdG9vIGxvdy5cclxuICAgICAgICAgICAgICB3aGlsZSAoY29tcGFyZSh5YywgcmVtLCB5TCwgcmVtTCkgPCAxKSB7XHJcbiAgICAgICAgICAgICAgICBuKys7XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gU3VidHJhY3QgZGl2aXNvciBmcm9tIHJlbWFpbmRlci5cclxuICAgICAgICAgICAgICAgIHN1YnRyYWN0KHJlbSwgeUwgPCByZW1MID8geXogOiB5YywgcmVtTCwgYmFzZSk7XHJcbiAgICAgICAgICAgICAgICByZW1MID0gcmVtLmxlbmd0aDtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gZWxzZSBpZiAoY21wID09PSAwKSB7XHJcbiAgICAgICAgICAgIG4rKztcclxuICAgICAgICAgICAgcmVtID0gWzBdO1xyXG4gICAgICAgICAgfSAvLyBlbHNlIGNtcCA9PT0gMSBhbmQgbiB3aWxsIGJlIDBcclxuXHJcbiAgICAgICAgICAvLyBBZGQgdGhlIG5leHQgZGlnaXQsIG4sIHRvIHRoZSByZXN1bHQgYXJyYXkuXHJcbiAgICAgICAgICBxY1tpKytdID0gbjtcclxuXHJcbiAgICAgICAgICAvLyBVcGRhdGUgdGhlIHJlbWFpbmRlci5cclxuICAgICAgICAgIGlmIChyZW1bMF0pIHtcclxuICAgICAgICAgICAgcmVtW3JlbUwrK10gPSB4Y1t4aV0gfHwgMDtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJlbSA9IFt4Y1t4aV1dO1xyXG4gICAgICAgICAgICByZW1MID0gMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IHdoaWxlICgoeGkrKyA8IHhMIHx8IHJlbVswXSAhPSBudWxsKSAmJiBzLS0pO1xyXG5cclxuICAgICAgICBtb3JlID0gcmVtWzBdICE9IG51bGw7XHJcblxyXG4gICAgICAgIC8vIExlYWRpbmcgemVybz9cclxuICAgICAgICBpZiAoIXFjWzBdKSBxYy5zcGxpY2UoMCwgMSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChiYXNlID09IEJBU0UpIHtcclxuXHJcbiAgICAgICAgLy8gVG8gY2FsY3VsYXRlIHEuZSwgZmlyc3QgZ2V0IHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIHFjWzBdLlxyXG4gICAgICAgIGZvciAoaSA9IDEsIHMgPSBxY1swXTsgcyA+PSAxMDsgcyAvPSAxMCwgaSsrKTtcclxuXHJcbiAgICAgICAgcm91bmQocSwgZHAgKyAocS5lID0gaSArIGUgKiBMT0dfQkFTRSAtIDEpICsgMSwgcm0sIG1vcmUpO1xyXG5cclxuICAgICAgLy8gQ2FsbGVyIGlzIGNvbnZlcnRCYXNlLlxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHEuZSA9IGU7XHJcbiAgICAgICAgcS5yID0gK21vcmU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBxO1xyXG4gICAgfTtcclxuICB9KSgpO1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBzdHJpbmcgcmVwcmVzZW50aW5nIHRoZSB2YWx1ZSBvZiBCaWdOdW1iZXIgbiBpbiBmaXhlZC1wb2ludCBvciBleHBvbmVudGlhbFxyXG4gICAqIG5vdGF0aW9uIHJvdW5kZWQgdG8gdGhlIHNwZWNpZmllZCBkZWNpbWFsIHBsYWNlcyBvciBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAgICpcclxuICAgKiBuOiBhIEJpZ051bWJlci5cclxuICAgKiBpOiB0aGUgaW5kZXggb2YgdGhlIGxhc3QgZGlnaXQgcmVxdWlyZWQgKGkuZS4gdGhlIGRpZ2l0IHRoYXQgbWF5IGJlIHJvdW5kZWQgdXApLlxyXG4gICAqIHJtOiB0aGUgcm91bmRpbmcgbW9kZS5cclxuICAgKiBpZDogMSAodG9FeHBvbmVudGlhbCkgb3IgMiAodG9QcmVjaXNpb24pLlxyXG4gICAqL1xyXG4gIGZ1bmN0aW9uIGZvcm1hdChuLCBpLCBybSwgaWQpIHtcclxuICAgIHZhciBjMCwgZSwgbmUsIGxlbiwgc3RyO1xyXG5cclxuICAgIGlmIChybSA9PSBudWxsKSBybSA9IFJPVU5ESU5HX01PREU7XHJcbiAgICBlbHNlIGludENoZWNrKHJtLCAwLCA4KTtcclxuXHJcbiAgICBpZiAoIW4uYykgcmV0dXJuIG4udG9TdHJpbmcoKTtcclxuXHJcbiAgICBjMCA9IG4uY1swXTtcclxuICAgIG5lID0gbi5lO1xyXG5cclxuICAgIGlmIChpID09IG51bGwpIHtcclxuICAgICAgc3RyID0gY29lZmZUb1N0cmluZyhuLmMpO1xyXG4gICAgICBzdHIgPSBpZCA9PSAxIHx8IGlkID09IDIgJiYgKG5lIDw9IFRPX0VYUF9ORUcgfHwgbmUgPj0gVE9fRVhQX1BPUylcclxuICAgICAgID8gdG9FeHBvbmVudGlhbChzdHIsIG5lKVxyXG4gICAgICAgOiB0b0ZpeGVkUG9pbnQoc3RyLCBuZSwgJzAnKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIG4gPSByb3VuZChuZXcgQmlnTnVtYmVyKG4pLCBpLCBybSk7XHJcblxyXG4gICAgICAvLyBuLmUgbWF5IGhhdmUgY2hhbmdlZCBpZiB0aGUgdmFsdWUgd2FzIHJvdW5kZWQgdXAuXHJcbiAgICAgIGUgPSBuLmU7XHJcblxyXG4gICAgICBzdHIgPSBjb2VmZlRvU3RyaW5nKG4uYyk7XHJcbiAgICAgIGxlbiA9IHN0ci5sZW5ndGg7XHJcblxyXG4gICAgICAvLyB0b1ByZWNpc2lvbiByZXR1cm5zIGV4cG9uZW50aWFsIG5vdGF0aW9uIGlmIHRoZSBudW1iZXIgb2Ygc2lnbmlmaWNhbnQgZGlnaXRzXHJcbiAgICAgIC8vIHNwZWNpZmllZCBpcyBsZXNzIHRoYW4gdGhlIG51bWJlciBvZiBkaWdpdHMgbmVjZXNzYXJ5IHRvIHJlcHJlc2VudCB0aGUgaW50ZWdlclxyXG4gICAgICAvLyBwYXJ0IG9mIHRoZSB2YWx1ZSBpbiBmaXhlZC1wb2ludCBub3RhdGlvbi5cclxuXHJcbiAgICAgIC8vIEV4cG9uZW50aWFsIG5vdGF0aW9uLlxyXG4gICAgICBpZiAoaWQgPT0gMSB8fCBpZCA9PSAyICYmIChpIDw9IGUgfHwgZSA8PSBUT19FWFBfTkVHKSkge1xyXG5cclxuICAgICAgICAvLyBBcHBlbmQgemVyb3M/XHJcbiAgICAgICAgZm9yICg7IGxlbiA8IGk7IHN0ciArPSAnMCcsIGxlbisrKTtcclxuICAgICAgICBzdHIgPSB0b0V4cG9uZW50aWFsKHN0ciwgZSk7XHJcblxyXG4gICAgICAvLyBGaXhlZC1wb2ludCBub3RhdGlvbi5cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBpIC09IG5lO1xyXG4gICAgICAgIHN0ciA9IHRvRml4ZWRQb2ludChzdHIsIGUsICcwJyk7XHJcblxyXG4gICAgICAgIC8vIEFwcGVuZCB6ZXJvcz9cclxuICAgICAgICBpZiAoZSArIDEgPiBsZW4pIHtcclxuICAgICAgICAgIGlmICgtLWkgPiAwKSBmb3IgKHN0ciArPSAnLic7IGktLTsgc3RyICs9ICcwJyk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGkgKz0gZSAtIGxlbjtcclxuICAgICAgICAgIGlmIChpID4gMCkge1xyXG4gICAgICAgICAgICBpZiAoZSArIDEgPT0gbGVuKSBzdHIgKz0gJy4nO1xyXG4gICAgICAgICAgICBmb3IgKDsgaS0tOyBzdHIgKz0gJzAnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbi5zIDwgMCAmJiBjMCA/ICctJyArIHN0ciA6IHN0cjtcclxuICB9XHJcblxyXG5cclxuICAvLyBIYW5kbGUgQmlnTnVtYmVyLm1heCBhbmQgQmlnTnVtYmVyLm1pbi5cclxuICAvLyBJZiBhbnkgbnVtYmVyIGlzIE5hTiwgcmV0dXJuIE5hTi5cclxuICBmdW5jdGlvbiBtYXhPck1pbihhcmdzLCBuKSB7XHJcbiAgICB2YXIgaywgeSxcclxuICAgICAgaSA9IDEsXHJcbiAgICAgIHggPSBuZXcgQmlnTnVtYmVyKGFyZ3NbMF0pO1xyXG5cclxuICAgIGZvciAoOyBpIDwgYXJncy5sZW5ndGg7IGkrKykge1xyXG4gICAgICB5ID0gbmV3IEJpZ051bWJlcihhcmdzW2ldKTtcclxuICAgICAgaWYgKCF5LnMgfHwgKGsgPSBjb21wYXJlKHgsIHkpKSA9PT0gbiB8fCBrID09PSAwICYmIHgucyA9PT0gbikge1xyXG4gICAgICAgIHggPSB5O1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHg7XHJcbiAgfVxyXG5cclxuXHJcbiAgLypcclxuICAgKiBTdHJpcCB0cmFpbGluZyB6ZXJvcywgY2FsY3VsYXRlIGJhc2UgMTAgZXhwb25lbnQgYW5kIGNoZWNrIGFnYWluc3QgTUlOX0VYUCBhbmQgTUFYX0VYUC5cclxuICAgKiBDYWxsZWQgYnkgbWludXMsIHBsdXMgYW5kIHRpbWVzLlxyXG4gICAqL1xyXG4gIGZ1bmN0aW9uIG5vcm1hbGlzZShuLCBjLCBlKSB7XHJcbiAgICB2YXIgaSA9IDEsXHJcbiAgICAgIGogPSBjLmxlbmd0aDtcclxuXHJcbiAgICAgLy8gUmVtb3ZlIHRyYWlsaW5nIHplcm9zLlxyXG4gICAgZm9yICg7ICFjWy0tal07IGMucG9wKCkpO1xyXG5cclxuICAgIC8vIENhbGN1bGF0ZSB0aGUgYmFzZSAxMCBleHBvbmVudC4gRmlyc3QgZ2V0IHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIGNbMF0uXHJcbiAgICBmb3IgKGogPSBjWzBdOyBqID49IDEwOyBqIC89IDEwLCBpKyspO1xyXG5cclxuICAgIC8vIE92ZXJmbG93P1xyXG4gICAgaWYgKChlID0gaSArIGUgKiBMT0dfQkFTRSAtIDEpID4gTUFYX0VYUCkge1xyXG5cclxuICAgICAgLy8gSW5maW5pdHkuXHJcbiAgICAgIG4uYyA9IG4uZSA9IG51bGw7XHJcblxyXG4gICAgLy8gVW5kZXJmbG93P1xyXG4gICAgfSBlbHNlIGlmIChlIDwgTUlOX0VYUCkge1xyXG5cclxuICAgICAgLy8gWmVyby5cclxuICAgICAgbi5jID0gW24uZSA9IDBdO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgbi5lID0gZTtcclxuICAgICAgbi5jID0gYztcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbjtcclxuICB9XHJcblxyXG5cclxuICAvLyBIYW5kbGUgdmFsdWVzIHRoYXQgZmFpbCB0aGUgdmFsaWRpdHkgdGVzdCBpbiBCaWdOdW1iZXIuXHJcbiAgcGFyc2VOdW1lcmljID0gKGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciBiYXNlUHJlZml4ID0gL14oLT8pMChbeGJvXSkoPz1cXHdbXFx3Ll0qJCkvaSxcclxuICAgICAgZG90QWZ0ZXIgPSAvXihbXi5dKylcXC4kLyxcclxuICAgICAgZG90QmVmb3JlID0gL15cXC4oW14uXSspJC8sXHJcbiAgICAgIGlzSW5maW5pdHlPck5hTiA9IC9eLT8oSW5maW5pdHl8TmFOKSQvLFxyXG4gICAgICB3aGl0ZXNwYWNlT3JQbHVzID0gL15cXHMqXFwrKD89W1xcdy5dKXxeXFxzK3xcXHMrJC9nO1xyXG5cclxuICAgIHJldHVybiBmdW5jdGlvbiAoeCwgc3RyLCBpc051bSwgYikge1xyXG4gICAgICB2YXIgYmFzZSxcclxuICAgICAgICBzID0gaXNOdW0gPyBzdHIgOiBzdHIucmVwbGFjZSh3aGl0ZXNwYWNlT3JQbHVzLCAnJyk7XHJcblxyXG4gICAgICAvLyBObyBleGNlcHRpb24gb24gwrFJbmZpbml0eSBvciBOYU4uXHJcbiAgICAgIGlmIChpc0luZmluaXR5T3JOYU4udGVzdChzKSkge1xyXG4gICAgICAgIHgucyA9IGlzTmFOKHMpID8gbnVsbCA6IHMgPCAwID8gLTEgOiAxO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGlmICghaXNOdW0pIHtcclxuXHJcbiAgICAgICAgICAvLyBiYXNlUHJlZml4ID0gL14oLT8pMChbeGJvXSkoPz1cXHdbXFx3Ll0qJCkvaVxyXG4gICAgICAgICAgcyA9IHMucmVwbGFjZShiYXNlUHJlZml4LCBmdW5jdGlvbiAobSwgcDEsIHAyKSB7XHJcbiAgICAgICAgICAgIGJhc2UgPSAocDIgPSBwMi50b0xvd2VyQ2FzZSgpKSA9PSAneCcgPyAxNiA6IHAyID09ICdiJyA/IDIgOiA4O1xyXG4gICAgICAgICAgICByZXR1cm4gIWIgfHwgYiA9PSBiYXNlID8gcDEgOiBtO1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgaWYgKGIpIHtcclxuICAgICAgICAgICAgYmFzZSA9IGI7XHJcblxyXG4gICAgICAgICAgICAvLyBFLmcuICcxLicgdG8gJzEnLCAnLjEnIHRvICcwLjEnXHJcbiAgICAgICAgICAgIHMgPSBzLnJlcGxhY2UoZG90QWZ0ZXIsICckMScpLnJlcGxhY2UoZG90QmVmb3JlLCAnMC4kMScpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGlmIChzdHIgIT0gcykgcmV0dXJuIG5ldyBCaWdOdW1iZXIocywgYmFzZSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyAnW0JpZ051bWJlciBFcnJvcl0gTm90IGEgbnVtYmVyOiB7bn0nXHJcbiAgICAgICAgLy8gJ1tCaWdOdW1iZXIgRXJyb3JdIE5vdCBhIGJhc2Uge2J9IG51bWJlcjoge259J1xyXG4gICAgICAgIGlmIChCaWdOdW1iZXIuREVCVUcpIHtcclxuICAgICAgICAgIHRocm93IEVycm9yXHJcbiAgICAgICAgICAgIChiaWdudW1iZXJFcnJvciArICdOb3QgYScgKyAoYiA/ICcgYmFzZSAnICsgYiA6ICcnKSArICcgbnVtYmVyOiAnICsgc3RyKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIE5hTlxyXG4gICAgICAgIHgucyA9IG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHguYyA9IHguZSA9IG51bGw7XHJcbiAgICB9XHJcbiAgfSkoKTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUm91bmQgeCB0byBzZCBzaWduaWZpY2FudCBkaWdpdHMgdXNpbmcgcm91bmRpbmcgbW9kZSBybS4gQ2hlY2sgZm9yIG92ZXIvdW5kZXItZmxvdy5cclxuICAgKiBJZiByIGlzIHRydXRoeSwgaXQgaXMga25vd24gdGhhdCB0aGVyZSBhcmUgbW9yZSBkaWdpdHMgYWZ0ZXIgdGhlIHJvdW5kaW5nIGRpZ2l0LlxyXG4gICAqL1xyXG4gIGZ1bmN0aW9uIHJvdW5kKHgsIHNkLCBybSwgcikge1xyXG4gICAgdmFyIGQsIGksIGosIGssIG4sIG5pLCByZCxcclxuICAgICAgeGMgPSB4LmMsXHJcbiAgICAgIHBvd3MxMCA9IFBPV1NfVEVOO1xyXG5cclxuICAgIC8vIGlmIHggaXMgbm90IEluZmluaXR5IG9yIE5hTi4uLlxyXG4gICAgaWYgKHhjKSB7XHJcblxyXG4gICAgICAvLyByZCBpcyB0aGUgcm91bmRpbmcgZGlnaXQsIGkuZS4gdGhlIGRpZ2l0IGFmdGVyIHRoZSBkaWdpdCB0aGF0IG1heSBiZSByb3VuZGVkIHVwLlxyXG4gICAgICAvLyBuIGlzIGEgYmFzZSAxZTE0IG51bWJlciwgdGhlIHZhbHVlIG9mIHRoZSBlbGVtZW50IG9mIGFycmF5IHguYyBjb250YWluaW5nIHJkLlxyXG4gICAgICAvLyBuaSBpcyB0aGUgaW5kZXggb2YgbiB3aXRoaW4geC5jLlxyXG4gICAgICAvLyBkIGlzIHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIG4uXHJcbiAgICAgIC8vIGkgaXMgdGhlIGluZGV4IG9mIHJkIHdpdGhpbiBuIGluY2x1ZGluZyBsZWFkaW5nIHplcm9zLlxyXG4gICAgICAvLyBqIGlzIHRoZSBhY3R1YWwgaW5kZXggb2YgcmQgd2l0aGluIG4gKGlmIDwgMCwgcmQgaXMgYSBsZWFkaW5nIHplcm8pLlxyXG4gICAgICBvdXQ6IHtcclxuXHJcbiAgICAgICAgLy8gR2V0IHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIHRoZSBmaXJzdCBlbGVtZW50IG9mIHhjLlxyXG4gICAgICAgIGZvciAoZCA9IDEsIGsgPSB4Y1swXTsgayA+PSAxMDsgayAvPSAxMCwgZCsrKTtcclxuICAgICAgICBpID0gc2QgLSBkO1xyXG5cclxuICAgICAgICAvLyBJZiB0aGUgcm91bmRpbmcgZGlnaXQgaXMgaW4gdGhlIGZpcnN0IGVsZW1lbnQgb2YgeGMuLi5cclxuICAgICAgICBpZiAoaSA8IDApIHtcclxuICAgICAgICAgIGkgKz0gTE9HX0JBU0U7XHJcbiAgICAgICAgICBqID0gc2Q7XHJcbiAgICAgICAgICBuID0geGNbbmkgPSAwXTtcclxuXHJcbiAgICAgICAgICAvLyBHZXQgdGhlIHJvdW5kaW5nIGRpZ2l0IGF0IGluZGV4IGogb2Ygbi5cclxuICAgICAgICAgIHJkID0gbWF0aGZsb29yKG4gLyBwb3dzMTBbZCAtIGogLSAxXSAlIDEwKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgbmkgPSBtYXRoY2VpbCgoaSArIDEpIC8gTE9HX0JBU0UpO1xyXG5cclxuICAgICAgICAgIGlmIChuaSA+PSB4Yy5sZW5ndGgpIHtcclxuXHJcbiAgICAgICAgICAgIGlmIChyKSB7XHJcblxyXG4gICAgICAgICAgICAgIC8vIE5lZWRlZCBieSBzcXJ0LlxyXG4gICAgICAgICAgICAgIGZvciAoOyB4Yy5sZW5ndGggPD0gbmk7IHhjLnB1c2goMCkpO1xyXG4gICAgICAgICAgICAgIG4gPSByZCA9IDA7XHJcbiAgICAgICAgICAgICAgZCA9IDE7XHJcbiAgICAgICAgICAgICAgaSAlPSBMT0dfQkFTRTtcclxuICAgICAgICAgICAgICBqID0gaSAtIExPR19CQVNFICsgMTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBicmVhayBvdXQ7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIG4gPSBrID0geGNbbmldO1xyXG5cclxuICAgICAgICAgICAgLy8gR2V0IHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIG4uXHJcbiAgICAgICAgICAgIGZvciAoZCA9IDE7IGsgPj0gMTA7IGsgLz0gMTAsIGQrKyk7XHJcblxyXG4gICAgICAgICAgICAvLyBHZXQgdGhlIGluZGV4IG9mIHJkIHdpdGhpbiBuLlxyXG4gICAgICAgICAgICBpICU9IExPR19CQVNFO1xyXG5cclxuICAgICAgICAgICAgLy8gR2V0IHRoZSBpbmRleCBvZiByZCB3aXRoaW4gbiwgYWRqdXN0ZWQgZm9yIGxlYWRpbmcgemVyb3MuXHJcbiAgICAgICAgICAgIC8vIFRoZSBudW1iZXIgb2YgbGVhZGluZyB6ZXJvcyBvZiBuIGlzIGdpdmVuIGJ5IExPR19CQVNFIC0gZC5cclxuICAgICAgICAgICAgaiA9IGkgLSBMT0dfQkFTRSArIGQ7XHJcblxyXG4gICAgICAgICAgICAvLyBHZXQgdGhlIHJvdW5kaW5nIGRpZ2l0IGF0IGluZGV4IGogb2Ygbi5cclxuICAgICAgICAgICAgcmQgPSBqIDwgMCA/IDAgOiBtYXRoZmxvb3IobiAvIHBvd3MxMFtkIC0gaiAtIDFdICUgMTApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgciA9IHIgfHwgc2QgPCAwIHx8XHJcblxyXG4gICAgICAgIC8vIEFyZSB0aGVyZSBhbnkgbm9uLXplcm8gZGlnaXRzIGFmdGVyIHRoZSByb3VuZGluZyBkaWdpdD9cclxuICAgICAgICAvLyBUaGUgZXhwcmVzc2lvbiAgbiAlIHBvd3MxMFtkIC0gaiAtIDFdICByZXR1cm5zIGFsbCBkaWdpdHMgb2YgbiB0byB0aGUgcmlnaHRcclxuICAgICAgICAvLyBvZiB0aGUgZGlnaXQgYXQgaiwgZS5nLiBpZiBuIGlzIDkwODcxNCBhbmQgaiBpcyAyLCB0aGUgZXhwcmVzc2lvbiBnaXZlcyA3MTQuXHJcbiAgICAgICAgIHhjW25pICsgMV0gIT0gbnVsbCB8fCAoaiA8IDAgPyBuIDogbiAlIHBvd3MxMFtkIC0gaiAtIDFdKTtcclxuXHJcbiAgICAgICAgciA9IHJtIDwgNFxyXG4gICAgICAgICA/IChyZCB8fCByKSAmJiAocm0gPT0gMCB8fCBybSA9PSAoeC5zIDwgMCA/IDMgOiAyKSlcclxuICAgICAgICAgOiByZCA+IDUgfHwgcmQgPT0gNSAmJiAocm0gPT0gNCB8fCByIHx8IHJtID09IDYgJiZcclxuXHJcbiAgICAgICAgICAvLyBDaGVjayB3aGV0aGVyIHRoZSBkaWdpdCB0byB0aGUgbGVmdCBvZiB0aGUgcm91bmRpbmcgZGlnaXQgaXMgb2RkLlxyXG4gICAgICAgICAgKChpID4gMCA/IGogPiAwID8gbiAvIHBvd3MxMFtkIC0gal0gOiAwIDogeGNbbmkgLSAxXSkgJSAxMCkgJiAxIHx8XHJcbiAgICAgICAgICAgcm0gPT0gKHgucyA8IDAgPyA4IDogNykpO1xyXG5cclxuICAgICAgICBpZiAoc2QgPCAxIHx8ICF4Y1swXSkge1xyXG4gICAgICAgICAgeGMubGVuZ3RoID0gMDtcclxuXHJcbiAgICAgICAgICBpZiAocikge1xyXG5cclxuICAgICAgICAgICAgLy8gQ29udmVydCBzZCB0byBkZWNpbWFsIHBsYWNlcy5cclxuICAgICAgICAgICAgc2QgLT0geC5lICsgMTtcclxuXHJcbiAgICAgICAgICAgIC8vIDEsIDAuMSwgMC4wMSwgMC4wMDEsIDAuMDAwMSBldGMuXHJcbiAgICAgICAgICAgIHhjWzBdID0gcG93czEwWyhMT0dfQkFTRSAtIHNkICUgTE9HX0JBU0UpICUgTE9HX0JBU0VdO1xyXG4gICAgICAgICAgICB4LmUgPSAtc2QgfHwgMDtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcblxyXG4gICAgICAgICAgICAvLyBaZXJvLlxyXG4gICAgICAgICAgICB4Y1swXSA9IHguZSA9IDA7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBSZW1vdmUgZXhjZXNzIGRpZ2l0cy5cclxuICAgICAgICBpZiAoaSA9PSAwKSB7XHJcbiAgICAgICAgICB4Yy5sZW5ndGggPSBuaTtcclxuICAgICAgICAgIGsgPSAxO1xyXG4gICAgICAgICAgbmktLTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgeGMubGVuZ3RoID0gbmkgKyAxO1xyXG4gICAgICAgICAgayA9IHBvd3MxMFtMT0dfQkFTRSAtIGldO1xyXG5cclxuICAgICAgICAgIC8vIEUuZy4gNTY3MDAgYmVjb21lcyA1NjAwMCBpZiA3IGlzIHRoZSByb3VuZGluZyBkaWdpdC5cclxuICAgICAgICAgIC8vIGogPiAwIG1lYW5zIGkgPiBudW1iZXIgb2YgbGVhZGluZyB6ZXJvcyBvZiBuLlxyXG4gICAgICAgICAgeGNbbmldID0gaiA+IDAgPyBtYXRoZmxvb3IobiAvIHBvd3MxMFtkIC0gal0gJSBwb3dzMTBbal0pICogayA6IDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBSb3VuZCB1cD9cclxuICAgICAgICBpZiAocikge1xyXG5cclxuICAgICAgICAgIGZvciAoOyA7KSB7XHJcblxyXG4gICAgICAgICAgICAvLyBJZiB0aGUgZGlnaXQgdG8gYmUgcm91bmRlZCB1cCBpcyBpbiB0aGUgZmlyc3QgZWxlbWVudCBvZiB4Yy4uLlxyXG4gICAgICAgICAgICBpZiAobmkgPT0gMCkge1xyXG5cclxuICAgICAgICAgICAgICAvLyBpIHdpbGwgYmUgdGhlIGxlbmd0aCBvZiB4Y1swXSBiZWZvcmUgayBpcyBhZGRlZC5cclxuICAgICAgICAgICAgICBmb3IgKGkgPSAxLCBqID0geGNbMF07IGogPj0gMTA7IGogLz0gMTAsIGkrKyk7XHJcbiAgICAgICAgICAgICAgaiA9IHhjWzBdICs9IGs7XHJcbiAgICAgICAgICAgICAgZm9yIChrID0gMTsgaiA+PSAxMDsgaiAvPSAxMCwgaysrKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gaWYgaSAhPSBrIHRoZSBsZW5ndGggaGFzIGluY3JlYXNlZC5cclxuICAgICAgICAgICAgICBpZiAoaSAhPSBrKSB7XHJcbiAgICAgICAgICAgICAgICB4LmUrKztcclxuICAgICAgICAgICAgICAgIGlmICh4Y1swXSA9PSBCQVNFKSB4Y1swXSA9IDE7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICB4Y1tuaV0gKz0gaztcclxuICAgICAgICAgICAgICBpZiAoeGNbbmldICE9IEJBU0UpIGJyZWFrO1xyXG4gICAgICAgICAgICAgIHhjW25pLS1dID0gMDtcclxuICAgICAgICAgICAgICBrID0gMTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUmVtb3ZlIHRyYWlsaW5nIHplcm9zLlxyXG4gICAgICAgIGZvciAoaSA9IHhjLmxlbmd0aDsgeGNbLS1pXSA9PT0gMDsgeGMucG9wKCkpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBPdmVyZmxvdz8gSW5maW5pdHkuXHJcbiAgICAgIGlmICh4LmUgPiBNQVhfRVhQKSB7XHJcbiAgICAgICAgeC5jID0geC5lID0gbnVsbDtcclxuXHJcbiAgICAgIC8vIFVuZGVyZmxvdz8gWmVyby5cclxuICAgICAgfSBlbHNlIGlmICh4LmUgPCBNSU5fRVhQKSB7XHJcbiAgICAgICAgeC5jID0gW3guZSA9IDBdO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHg7XHJcbiAgfVxyXG5cclxuXHJcbiAgZnVuY3Rpb24gdmFsdWVPZihuKSB7XHJcbiAgICB2YXIgc3RyLFxyXG4gICAgICBlID0gbi5lO1xyXG5cclxuICAgIGlmIChlID09PSBudWxsKSByZXR1cm4gbi50b1N0cmluZygpO1xyXG5cclxuICAgIHN0ciA9IGNvZWZmVG9TdHJpbmcobi5jKTtcclxuXHJcbiAgICBzdHIgPSBlIDw9IFRPX0VYUF9ORUcgfHwgZSA+PSBUT19FWFBfUE9TXHJcbiAgICAgID8gdG9FeHBvbmVudGlhbChzdHIsIGUpXHJcbiAgICAgIDogdG9GaXhlZFBvaW50KHN0ciwgZSwgJzAnKTtcclxuXHJcbiAgICByZXR1cm4gbi5zIDwgMCA/ICctJyArIHN0ciA6IHN0cjtcclxuICB9XHJcblxyXG5cclxuICAvLyBQUk9UT1RZUEUvSU5TVEFOQ0UgTUVUSE9EU1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBuZXcgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSBhYnNvbHV0ZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlci5cclxuICAgKi9cclxuICBQLmFic29sdXRlVmFsdWUgPSBQLmFicyA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciB4ID0gbmV3IEJpZ051bWJlcih0aGlzKTtcclxuICAgIGlmICh4LnMgPCAwKSB4LnMgPSAxO1xyXG4gICAgcmV0dXJuIHg7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuXHJcbiAgICogICAxIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBpcyBncmVhdGVyIHRoYW4gdGhlIHZhbHVlIG9mIEJpZ051bWJlcih5LCBiKSxcclxuICAgKiAgIC0xIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBpcyBsZXNzIHRoYW4gdGhlIHZhbHVlIG9mIEJpZ051bWJlcih5LCBiKSxcclxuICAgKiAgIDAgaWYgdGhleSBoYXZlIHRoZSBzYW1lIHZhbHVlLFxyXG4gICAqICAgb3IgbnVsbCBpZiB0aGUgdmFsdWUgb2YgZWl0aGVyIGlzIE5hTi5cclxuICAgKi9cclxuICBQLmNvbXBhcmVkVG8gPSBmdW5jdGlvbiAoeSwgYikge1xyXG4gICAgcmV0dXJuIGNvbXBhcmUodGhpcywgbmV3IEJpZ051bWJlcih5LCBiKSk7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogSWYgZHAgaXMgdW5kZWZpbmVkIG9yIG51bGwgb3IgdHJ1ZSBvciBmYWxzZSwgcmV0dXJuIHRoZSBudW1iZXIgb2YgZGVjaW1hbCBwbGFjZXMgb2YgdGhlXHJcbiAgICogdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIsIG9yIG51bGwgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIMKxSW5maW5pdHkgb3IgTmFOLlxyXG4gICAqXHJcbiAgICogT3RoZXJ3aXNlLCBpZiBkcCBpcyBhIG51bWJlciwgcmV0dXJuIGEgbmV3IEJpZ051bWJlciB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpc1xyXG4gICAqIEJpZ051bWJlciByb3VuZGVkIHRvIGEgbWF4aW11bSBvZiBkcCBkZWNpbWFsIHBsYWNlcyB1c2luZyByb3VuZGluZyBtb2RlIHJtLCBvclxyXG4gICAqIFJPVU5ESU5HX01PREUgaWYgcm0gaXMgb21pdHRlZC5cclxuICAgKlxyXG4gICAqIFtkcF0ge251bWJlcn0gRGVjaW1hbCBwbGFjZXM6IGludGVnZXIsIDAgdG8gTUFYIGluY2x1c2l2ZS5cclxuICAgKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge2RwfHJtfSdcclxuICAgKi9cclxuICBQLmRlY2ltYWxQbGFjZXMgPSBQLmRwID0gZnVuY3Rpb24gKGRwLCBybSkge1xyXG4gICAgdmFyIGMsIG4sIHYsXHJcbiAgICAgIHggPSB0aGlzO1xyXG5cclxuICAgIGlmIChkcCAhPSBudWxsKSB7XHJcbiAgICAgIGludENoZWNrKGRwLCAwLCBNQVgpO1xyXG4gICAgICBpZiAocm0gPT0gbnVsbCkgcm0gPSBST1VORElOR19NT0RFO1xyXG4gICAgICBlbHNlIGludENoZWNrKHJtLCAwLCA4KTtcclxuXHJcbiAgICAgIHJldHVybiByb3VuZChuZXcgQmlnTnVtYmVyKHgpLCBkcCArIHguZSArIDEsIHJtKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIShjID0geC5jKSkgcmV0dXJuIG51bGw7XHJcbiAgICBuID0gKCh2ID0gYy5sZW5ndGggLSAxKSAtIGJpdEZsb29yKHRoaXMuZSAvIExPR19CQVNFKSkgKiBMT0dfQkFTRTtcclxuXHJcbiAgICAvLyBTdWJ0cmFjdCB0aGUgbnVtYmVyIG9mIHRyYWlsaW5nIHplcm9zIG9mIHRoZSBsYXN0IG51bWJlci5cclxuICAgIGlmICh2ID0gY1t2XSkgZm9yICg7IHYgJSAxMCA9PSAwOyB2IC89IDEwLCBuLS0pO1xyXG4gICAgaWYgKG4gPCAwKSBuID0gMDtcclxuXHJcbiAgICByZXR1cm4gbjtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiAgbiAvIDAgPSBJXHJcbiAgICogIG4gLyBOID0gTlxyXG4gICAqICBuIC8gSSA9IDBcclxuICAgKiAgMCAvIG4gPSAwXHJcbiAgICogIDAgLyAwID0gTlxyXG4gICAqICAwIC8gTiA9IE5cclxuICAgKiAgMCAvIEkgPSAwXHJcbiAgICogIE4gLyBuID0gTlxyXG4gICAqICBOIC8gMCA9IE5cclxuICAgKiAgTiAvIE4gPSBOXHJcbiAgICogIE4gLyBJID0gTlxyXG4gICAqICBJIC8gbiA9IElcclxuICAgKiAgSSAvIDAgPSBJXHJcbiAgICogIEkgLyBOID0gTlxyXG4gICAqICBJIC8gSSA9IE5cclxuICAgKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGRpdmlkZWQgYnkgdGhlIHZhbHVlIG9mXHJcbiAgICogQmlnTnVtYmVyKHksIGIpLCByb3VuZGVkIGFjY29yZGluZyB0byBERUNJTUFMX1BMQUNFUyBhbmQgUk9VTkRJTkdfTU9ERS5cclxuICAgKi9cclxuICBQLmRpdmlkZWRCeSA9IFAuZGl2ID0gZnVuY3Rpb24gKHksIGIpIHtcclxuICAgIHJldHVybiBkaXYodGhpcywgbmV3IEJpZ051bWJlcih5LCBiKSwgREVDSU1BTF9QTEFDRVMsIFJPVU5ESU5HX01PREUpO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIGludGVnZXIgcGFydCBvZiBkaXZpZGluZyB0aGUgdmFsdWUgb2YgdGhpc1xyXG4gICAqIEJpZ051bWJlciBieSB0aGUgdmFsdWUgb2YgQmlnTnVtYmVyKHksIGIpLlxyXG4gICAqL1xyXG4gIFAuZGl2aWRlZFRvSW50ZWdlckJ5ID0gUC5pZGl2ID0gZnVuY3Rpb24gKHksIGIpIHtcclxuICAgIHJldHVybiBkaXYodGhpcywgbmV3IEJpZ051bWJlcih5LCBiKSwgMCwgMSk7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIGEgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBleHBvbmVudGlhdGVkIGJ5IG4uXHJcbiAgICpcclxuICAgKiBJZiBtIGlzIHByZXNlbnQsIHJldHVybiB0aGUgcmVzdWx0IG1vZHVsbyBtLlxyXG4gICAqIElmIG4gaXMgbmVnYXRpdmUgcm91bmQgYWNjb3JkaW5nIHRvIERFQ0lNQUxfUExBQ0VTIGFuZCBST1VORElOR19NT0RFLlxyXG4gICAqIElmIFBPV19QUkVDSVNJT04gaXMgbm9uLXplcm8gYW5kIG0gaXMgbm90IHByZXNlbnQsIHJvdW5kIHRvIFBPV19QUkVDSVNJT04gdXNpbmcgUk9VTkRJTkdfTU9ERS5cclxuICAgKlxyXG4gICAqIFRoZSBtb2R1bGFyIHBvd2VyIG9wZXJhdGlvbiB3b3JrcyBlZmZpY2llbnRseSB3aGVuIHgsIG4sIGFuZCBtIGFyZSBpbnRlZ2Vycywgb3RoZXJ3aXNlIGl0XHJcbiAgICogaXMgZXF1aXZhbGVudCB0byBjYWxjdWxhdGluZyB4LmV4cG9uZW50aWF0ZWRCeShuKS5tb2R1bG8obSkgd2l0aCBhIFBPV19QUkVDSVNJT04gb2YgMC5cclxuICAgKlxyXG4gICAqIG4ge251bWJlcnxzdHJpbmd8QmlnTnVtYmVyfSBUaGUgZXhwb25lbnQuIEFuIGludGVnZXIuXHJcbiAgICogW21dIHtudW1iZXJ8c3RyaW5nfEJpZ051bWJlcn0gVGhlIG1vZHVsdXMuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gRXhwb25lbnQgbm90IGFuIGludGVnZXI6IHtufSdcclxuICAgKi9cclxuICBQLmV4cG9uZW50aWF0ZWRCeSA9IFAucG93ID0gZnVuY3Rpb24gKG4sIG0pIHtcclxuICAgIHZhciBoYWxmLCBpc01vZEV4cCwgaSwgaywgbW9yZSwgbklzQmlnLCBuSXNOZWcsIG5Jc09kZCwgeSxcclxuICAgICAgeCA9IHRoaXM7XHJcblxyXG4gICAgbiA9IG5ldyBCaWdOdW1iZXIobik7XHJcblxyXG4gICAgLy8gQWxsb3cgTmFOIGFuZCDCsUluZmluaXR5LCBidXQgbm90IG90aGVyIG5vbi1pbnRlZ2Vycy5cclxuICAgIGlmIChuLmMgJiYgIW4uaXNJbnRlZ2VyKCkpIHtcclxuICAgICAgdGhyb3cgRXJyb3JcclxuICAgICAgICAoYmlnbnVtYmVyRXJyb3IgKyAnRXhwb25lbnQgbm90IGFuIGludGVnZXI6ICcgKyB2YWx1ZU9mKG4pKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAobSAhPSBudWxsKSBtID0gbmV3IEJpZ051bWJlcihtKTtcclxuXHJcbiAgICAvLyBFeHBvbmVudCBvZiBNQVhfU0FGRV9JTlRFR0VSIGlzIDE1LlxyXG4gICAgbklzQmlnID0gbi5lID4gMTQ7XHJcblxyXG4gICAgLy8gSWYgeCBpcyBOYU4sIMKxSW5maW5pdHksIMKxMCBvciDCsTEsIG9yIG4gaXMgwrFJbmZpbml0eSwgTmFOIG9yIMKxMC5cclxuICAgIGlmICgheC5jIHx8ICF4LmNbMF0gfHwgeC5jWzBdID09IDEgJiYgIXguZSAmJiB4LmMubGVuZ3RoID09IDEgfHwgIW4uYyB8fCAhbi5jWzBdKSB7XHJcblxyXG4gICAgICAvLyBUaGUgc2lnbiBvZiB0aGUgcmVzdWx0IG9mIHBvdyB3aGVuIHggaXMgbmVnYXRpdmUgZGVwZW5kcyBvbiB0aGUgZXZlbm5lc3Mgb2Ygbi5cclxuICAgICAgLy8gSWYgK24gb3ZlcmZsb3dzIHRvIMKxSW5maW5pdHksIHRoZSBldmVubmVzcyBvZiBuIHdvdWxkIGJlIG5vdCBiZSBrbm93bi5cclxuICAgICAgeSA9IG5ldyBCaWdOdW1iZXIoTWF0aC5wb3coK3ZhbHVlT2YoeCksIG5Jc0JpZyA/IG4ucyAqICgyIC0gaXNPZGQobikpIDogK3ZhbHVlT2YobikpKTtcclxuICAgICAgcmV0dXJuIG0gPyB5Lm1vZChtKSA6IHk7XHJcbiAgICB9XHJcblxyXG4gICAgbklzTmVnID0gbi5zIDwgMDtcclxuXHJcbiAgICBpZiAobSkge1xyXG5cclxuICAgICAgLy8geCAlIG0gcmV0dXJucyBOYU4gaWYgYWJzKG0pIGlzIHplcm8sIG9yIG0gaXMgTmFOLlxyXG4gICAgICBpZiAobS5jID8gIW0uY1swXSA6ICFtLnMpIHJldHVybiBuZXcgQmlnTnVtYmVyKE5hTik7XHJcblxyXG4gICAgICBpc01vZEV4cCA9ICFuSXNOZWcgJiYgeC5pc0ludGVnZXIoKSAmJiBtLmlzSW50ZWdlcigpO1xyXG5cclxuICAgICAgaWYgKGlzTW9kRXhwKSB4ID0geC5tb2QobSk7XHJcblxyXG4gICAgLy8gT3ZlcmZsb3cgdG8gwrFJbmZpbml0eTogPj0yKioxZTEwIG9yID49MS4wMDAwMDI0KioxZTE1LlxyXG4gICAgLy8gVW5kZXJmbG93IHRvIMKxMDogPD0wLjc5KioxZTEwIG9yIDw9MC45OTk5OTc1KioxZTE1LlxyXG4gICAgfSBlbHNlIGlmIChuLmUgPiA5ICYmICh4LmUgPiAwIHx8IHguZSA8IC0xIHx8ICh4LmUgPT0gMFxyXG4gICAgICAvLyBbMSwgMjQwMDAwMDAwXVxyXG4gICAgICA/IHguY1swXSA+IDEgfHwgbklzQmlnICYmIHguY1sxXSA+PSAyNGU3XHJcbiAgICAgIC8vIFs4MDAwMDAwMDAwMDAwMF0gIFs5OTk5OTc1MDAwMDAwMF1cclxuICAgICAgOiB4LmNbMF0gPCA4ZTEzIHx8IG5Jc0JpZyAmJiB4LmNbMF0gPD0gOTk5OTk3NWU3KSkpIHtcclxuXHJcbiAgICAgIC8vIElmIHggaXMgbmVnYXRpdmUgYW5kIG4gaXMgb2RkLCBrID0gLTAsIGVsc2UgayA9IDAuXHJcbiAgICAgIGsgPSB4LnMgPCAwICYmIGlzT2RkKG4pID8gLTAgOiAwO1xyXG5cclxuICAgICAgLy8gSWYgeCA+PSAxLCBrID0gwrFJbmZpbml0eS5cclxuICAgICAgaWYgKHguZSA+IC0xKSBrID0gMSAvIGs7XHJcblxyXG4gICAgICAvLyBJZiBuIGlzIG5lZ2F0aXZlIHJldHVybiDCsTAsIGVsc2UgcmV0dXJuIMKxSW5maW5pdHkuXHJcbiAgICAgIHJldHVybiBuZXcgQmlnTnVtYmVyKG5Jc05lZyA/IDEgLyBrIDogayk7XHJcblxyXG4gICAgfSBlbHNlIGlmIChQT1dfUFJFQ0lTSU9OKSB7XHJcblxyXG4gICAgICAvLyBUcnVuY2F0aW5nIGVhY2ggY29lZmZpY2llbnQgYXJyYXkgdG8gYSBsZW5ndGggb2YgayBhZnRlciBlYWNoIG11bHRpcGxpY2F0aW9uXHJcbiAgICAgIC8vIGVxdWF0ZXMgdG8gdHJ1bmNhdGluZyBzaWduaWZpY2FudCBkaWdpdHMgdG8gUE9XX1BSRUNJU0lPTiArIFsyOCwgNDFdLFxyXG4gICAgICAvLyBpLmUuIHRoZXJlIHdpbGwgYmUgYSBtaW5pbXVtIG9mIDI4IGd1YXJkIGRpZ2l0cyByZXRhaW5lZC5cclxuICAgICAgayA9IG1hdGhjZWlsKFBPV19QUkVDSVNJT04gLyBMT0dfQkFTRSArIDIpO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChuSXNCaWcpIHtcclxuICAgICAgaGFsZiA9IG5ldyBCaWdOdW1iZXIoMC41KTtcclxuICAgICAgaWYgKG5Jc05lZykgbi5zID0gMTtcclxuICAgICAgbklzT2RkID0gaXNPZGQobik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpID0gTWF0aC5hYnMoK3ZhbHVlT2YobikpO1xyXG4gICAgICBuSXNPZGQgPSBpICUgMjtcclxuICAgIH1cclxuXHJcbiAgICB5ID0gbmV3IEJpZ051bWJlcihPTkUpO1xyXG5cclxuICAgIC8vIFBlcmZvcm1zIDU0IGxvb3AgaXRlcmF0aW9ucyBmb3IgbiBvZiA5MDA3MTk5MjU0NzQwOTkxLlxyXG4gICAgZm9yICg7IDspIHtcclxuXHJcbiAgICAgIGlmIChuSXNPZGQpIHtcclxuICAgICAgICB5ID0geS50aW1lcyh4KTtcclxuICAgICAgICBpZiAoIXkuYykgYnJlYWs7XHJcblxyXG4gICAgICAgIGlmIChrKSB7XHJcbiAgICAgICAgICBpZiAoeS5jLmxlbmd0aCA+IGspIHkuYy5sZW5ndGggPSBrO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoaXNNb2RFeHApIHtcclxuICAgICAgICAgIHkgPSB5Lm1vZChtKTsgICAgLy95ID0geS5taW51cyhkaXYoeSwgbSwgMCwgTU9EVUxPX01PREUpLnRpbWVzKG0pKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChpKSB7XHJcbiAgICAgICAgaSA9IG1hdGhmbG9vcihpIC8gMik7XHJcbiAgICAgICAgaWYgKGkgPT09IDApIGJyZWFrO1xyXG4gICAgICAgIG5Jc09kZCA9IGkgJSAyO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIG4gPSBuLnRpbWVzKGhhbGYpO1xyXG4gICAgICAgIHJvdW5kKG4sIG4uZSArIDEsIDEpO1xyXG5cclxuICAgICAgICBpZiAobi5lID4gMTQpIHtcclxuICAgICAgICAgIG5Jc09kZCA9IGlzT2RkKG4pO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBpID0gK3ZhbHVlT2Yobik7XHJcbiAgICAgICAgICBpZiAoaSA9PT0gMCkgYnJlYWs7XHJcbiAgICAgICAgICBuSXNPZGQgPSBpICUgMjtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHggPSB4LnRpbWVzKHgpO1xyXG5cclxuICAgICAgaWYgKGspIHtcclxuICAgICAgICBpZiAoeC5jICYmIHguYy5sZW5ndGggPiBrKSB4LmMubGVuZ3RoID0gaztcclxuICAgICAgfSBlbHNlIGlmIChpc01vZEV4cCkge1xyXG4gICAgICAgIHggPSB4Lm1vZChtKTsgICAgLy94ID0geC5taW51cyhkaXYoeCwgbSwgMCwgTU9EVUxPX01PREUpLnRpbWVzKG0pKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChpc01vZEV4cCkgcmV0dXJuIHk7XHJcbiAgICBpZiAobklzTmVnKSB5ID0gT05FLmRpdih5KTtcclxuXHJcbiAgICByZXR1cm4gbSA/IHkubW9kKG0pIDogayA/IHJvdW5kKHksIFBPV19QUkVDSVNJT04sIFJPVU5ESU5HX01PREUsIG1vcmUpIDogeTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBuZXcgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciByb3VuZGVkIHRvIGFuIGludGVnZXJcclxuICAgKiB1c2luZyByb3VuZGluZyBtb2RlIHJtLCBvciBST1VORElOR19NT0RFIGlmIHJtIGlzIG9taXR0ZWQuXHJcbiAgICpcclxuICAgKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge3JtfSdcclxuICAgKi9cclxuICBQLmludGVnZXJWYWx1ZSA9IGZ1bmN0aW9uIChybSkge1xyXG4gICAgdmFyIG4gPSBuZXcgQmlnTnVtYmVyKHRoaXMpO1xyXG4gICAgaWYgKHJtID09IG51bGwpIHJtID0gUk9VTkRJTkdfTU9ERTtcclxuICAgIGVsc2UgaW50Q2hlY2socm0sIDAsIDgpO1xyXG4gICAgcmV0dXJuIHJvdW5kKG4sIG4uZSArIDEsIHJtKTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gdHJ1ZSBpZiB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIgaXMgZXF1YWwgdG8gdGhlIHZhbHVlIG9mIEJpZ051bWJlcih5LCBiKSxcclxuICAgKiBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gICAqL1xyXG4gIFAuaXNFcXVhbFRvID0gUC5lcSA9IGZ1bmN0aW9uICh5LCBiKSB7XHJcbiAgICByZXR1cm4gY29tcGFyZSh0aGlzLCBuZXcgQmlnTnVtYmVyKHksIGIpKSA9PT0gMDtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gdHJ1ZSBpZiB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIgaXMgYSBmaW5pdGUgbnVtYmVyLCBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gICAqL1xyXG4gIFAuaXNGaW5pdGUgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICByZXR1cm4gISF0aGlzLmM7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIGdyZWF0ZXIgdGhhbiB0aGUgdmFsdWUgb2YgQmlnTnVtYmVyKHksIGIpLFxyXG4gICAqIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAgICovXHJcbiAgUC5pc0dyZWF0ZXJUaGFuID0gUC5ndCA9IGZ1bmN0aW9uICh5LCBiKSB7XHJcbiAgICByZXR1cm4gY29tcGFyZSh0aGlzLCBuZXcgQmlnTnVtYmVyKHksIGIpKSA+IDA7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbCB0byB0aGUgdmFsdWUgb2ZcclxuICAgKiBCaWdOdW1iZXIoeSwgYiksIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAgICovXHJcbiAgUC5pc0dyZWF0ZXJUaGFuT3JFcXVhbFRvID0gUC5ndGUgPSBmdW5jdGlvbiAoeSwgYikge1xyXG4gICAgcmV0dXJuIChiID0gY29tcGFyZSh0aGlzLCBuZXcgQmlnTnVtYmVyKHksIGIpKSkgPT09IDEgfHwgYiA9PT0gMDtcclxuXHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIGFuIGludGVnZXIsIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAgICovXHJcbiAgUC5pc0ludGVnZXIgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICByZXR1cm4gISF0aGlzLmMgJiYgYml0Rmxvb3IodGhpcy5lIC8gTE9HX0JBU0UpID4gdGhpcy5jLmxlbmd0aCAtIDI7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIGxlc3MgdGhhbiB0aGUgdmFsdWUgb2YgQmlnTnVtYmVyKHksIGIpLFxyXG4gICAqIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAgICovXHJcbiAgUC5pc0xlc3NUaGFuID0gUC5sdCA9IGZ1bmN0aW9uICh5LCBiKSB7XHJcbiAgICByZXR1cm4gY29tcGFyZSh0aGlzLCBuZXcgQmlnTnVtYmVyKHksIGIpKSA8IDA7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIGxlc3MgdGhhbiBvciBlcXVhbCB0byB0aGUgdmFsdWUgb2ZcclxuICAgKiBCaWdOdW1iZXIoeSwgYiksIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAgICovXHJcbiAgUC5pc0xlc3NUaGFuT3JFcXVhbFRvID0gUC5sdGUgPSBmdW5jdGlvbiAoeSwgYikge1xyXG4gICAgcmV0dXJuIChiID0gY29tcGFyZSh0aGlzLCBuZXcgQmlnTnVtYmVyKHksIGIpKSkgPT09IC0xIHx8IGIgPT09IDA7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIE5hTiwgb3RoZXJ3aXNlIHJldHVybiBmYWxzZS5cclxuICAgKi9cclxuICBQLmlzTmFOID0gZnVuY3Rpb24gKCkge1xyXG4gICAgcmV0dXJuICF0aGlzLnM7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIG5lZ2F0aXZlLCBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gICAqL1xyXG4gIFAuaXNOZWdhdGl2ZSA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHJldHVybiB0aGlzLnMgPCAwO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiB0cnVlIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBpcyBwb3NpdGl2ZSwgb3RoZXJ3aXNlIHJldHVybiBmYWxzZS5cclxuICAgKi9cclxuICBQLmlzUG9zaXRpdmUgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5zID4gMDtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gdHJ1ZSBpZiB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIgaXMgMCBvciAtMCwgb3RoZXJ3aXNlIHJldHVybiBmYWxzZS5cclxuICAgKi9cclxuICBQLmlzWmVybyA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHJldHVybiAhIXRoaXMuYyAmJiB0aGlzLmNbMF0gPT0gMDtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiAgbiAtIDAgPSBuXHJcbiAgICogIG4gLSBOID0gTlxyXG4gICAqICBuIC0gSSA9IC1JXHJcbiAgICogIDAgLSBuID0gLW5cclxuICAgKiAgMCAtIDAgPSAwXHJcbiAgICogIDAgLSBOID0gTlxyXG4gICAqICAwIC0gSSA9IC1JXHJcbiAgICogIE4gLSBuID0gTlxyXG4gICAqICBOIC0gMCA9IE5cclxuICAgKiAgTiAtIE4gPSBOXHJcbiAgICogIE4gLSBJID0gTlxyXG4gICAqICBJIC0gbiA9IElcclxuICAgKiAgSSAtIDAgPSBJXHJcbiAgICogIEkgLSBOID0gTlxyXG4gICAqICBJIC0gSSA9IE5cclxuICAgKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIG1pbnVzIHRoZSB2YWx1ZSBvZlxyXG4gICAqIEJpZ051bWJlcih5LCBiKS5cclxuICAgKi9cclxuICBQLm1pbnVzID0gZnVuY3Rpb24gKHksIGIpIHtcclxuICAgIHZhciBpLCBqLCB0LCB4TFR5LFxyXG4gICAgICB4ID0gdGhpcyxcclxuICAgICAgYSA9IHgucztcclxuXHJcbiAgICB5ID0gbmV3IEJpZ051bWJlcih5LCBiKTtcclxuICAgIGIgPSB5LnM7XHJcblxyXG4gICAgLy8gRWl0aGVyIE5hTj9cclxuICAgIGlmICghYSB8fCAhYikgcmV0dXJuIG5ldyBCaWdOdW1iZXIoTmFOKTtcclxuXHJcbiAgICAvLyBTaWducyBkaWZmZXI/XHJcbiAgICBpZiAoYSAhPSBiKSB7XHJcbiAgICAgIHkucyA9IC1iO1xyXG4gICAgICByZXR1cm4geC5wbHVzKHkpO1xyXG4gICAgfVxyXG5cclxuICAgIHZhciB4ZSA9IHguZSAvIExPR19CQVNFLFxyXG4gICAgICB5ZSA9IHkuZSAvIExPR19CQVNFLFxyXG4gICAgICB4YyA9IHguYyxcclxuICAgICAgeWMgPSB5LmM7XHJcblxyXG4gICAgaWYgKCF4ZSB8fCAheWUpIHtcclxuXHJcbiAgICAgIC8vIEVpdGhlciBJbmZpbml0eT9cclxuICAgICAgaWYgKCF4YyB8fCAheWMpIHJldHVybiB4YyA/ICh5LnMgPSAtYiwgeSkgOiBuZXcgQmlnTnVtYmVyKHljID8geCA6IE5hTik7XHJcblxyXG4gICAgICAvLyBFaXRoZXIgemVybz9cclxuICAgICAgaWYgKCF4Y1swXSB8fCAheWNbMF0pIHtcclxuXHJcbiAgICAgICAgLy8gUmV0dXJuIHkgaWYgeSBpcyBub24temVybywgeCBpZiB4IGlzIG5vbi16ZXJvLCBvciB6ZXJvIGlmIGJvdGggYXJlIHplcm8uXHJcbiAgICAgICAgcmV0dXJuIHljWzBdID8gKHkucyA9IC1iLCB5KSA6IG5ldyBCaWdOdW1iZXIoeGNbMF0gPyB4IDpcclxuXHJcbiAgICAgICAgIC8vIElFRUUgNzU0ICgyMDA4KSA2LjM6IG4gLSBuID0gLTAgd2hlbiByb3VuZGluZyB0byAtSW5maW5pdHlcclxuICAgICAgICAgUk9VTkRJTkdfTU9ERSA9PSAzID8gLTAgOiAwKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHhlID0gYml0Rmxvb3IoeGUpO1xyXG4gICAgeWUgPSBiaXRGbG9vcih5ZSk7XHJcbiAgICB4YyA9IHhjLnNsaWNlKCk7XHJcblxyXG4gICAgLy8gRGV0ZXJtaW5lIHdoaWNoIGlzIHRoZSBiaWdnZXIgbnVtYmVyLlxyXG4gICAgaWYgKGEgPSB4ZSAtIHllKSB7XHJcblxyXG4gICAgICBpZiAoeExUeSA9IGEgPCAwKSB7XHJcbiAgICAgICAgYSA9IC1hO1xyXG4gICAgICAgIHQgPSB4YztcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB5ZSA9IHhlO1xyXG4gICAgICAgIHQgPSB5YztcclxuICAgICAgfVxyXG5cclxuICAgICAgdC5yZXZlcnNlKCk7XHJcblxyXG4gICAgICAvLyBQcmVwZW5kIHplcm9zIHRvIGVxdWFsaXNlIGV4cG9uZW50cy5cclxuICAgICAgZm9yIChiID0gYTsgYi0tOyB0LnB1c2goMCkpO1xyXG4gICAgICB0LnJldmVyc2UoKTtcclxuICAgIH0gZWxzZSB7XHJcblxyXG4gICAgICAvLyBFeHBvbmVudHMgZXF1YWwuIENoZWNrIGRpZ2l0IGJ5IGRpZ2l0LlxyXG4gICAgICBqID0gKHhMVHkgPSAoYSA9IHhjLmxlbmd0aCkgPCAoYiA9IHljLmxlbmd0aCkpID8gYSA6IGI7XHJcblxyXG4gICAgICBmb3IgKGEgPSBiID0gMDsgYiA8IGo7IGIrKykge1xyXG5cclxuICAgICAgICBpZiAoeGNbYl0gIT0geWNbYl0pIHtcclxuICAgICAgICAgIHhMVHkgPSB4Y1tiXSA8IHljW2JdO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8geCA8IHk/IFBvaW50IHhjIHRvIHRoZSBhcnJheSBvZiB0aGUgYmlnZ2VyIG51bWJlci5cclxuICAgIGlmICh4TFR5KSB7XHJcbiAgICAgIHQgPSB4YztcclxuICAgICAgeGMgPSB5YztcclxuICAgICAgeWMgPSB0O1xyXG4gICAgICB5LnMgPSAteS5zO1xyXG4gICAgfVxyXG5cclxuICAgIGIgPSAoaiA9IHljLmxlbmd0aCkgLSAoaSA9IHhjLmxlbmd0aCk7XHJcblxyXG4gICAgLy8gQXBwZW5kIHplcm9zIHRvIHhjIGlmIHNob3J0ZXIuXHJcbiAgICAvLyBObyBuZWVkIHRvIGFkZCB6ZXJvcyB0byB5YyBpZiBzaG9ydGVyIGFzIHN1YnRyYWN0IG9ubHkgbmVlZHMgdG8gc3RhcnQgYXQgeWMubGVuZ3RoLlxyXG4gICAgaWYgKGIgPiAwKSBmb3IgKDsgYi0tOyB4Y1tpKytdID0gMCk7XHJcbiAgICBiID0gQkFTRSAtIDE7XHJcblxyXG4gICAgLy8gU3VidHJhY3QgeWMgZnJvbSB4Yy5cclxuICAgIGZvciAoOyBqID4gYTspIHtcclxuXHJcbiAgICAgIGlmICh4Y1stLWpdIDwgeWNbal0pIHtcclxuICAgICAgICBmb3IgKGkgPSBqOyBpICYmICF4Y1stLWldOyB4Y1tpXSA9IGIpO1xyXG4gICAgICAgIC0teGNbaV07XHJcbiAgICAgICAgeGNbal0gKz0gQkFTRTtcclxuICAgICAgfVxyXG5cclxuICAgICAgeGNbal0gLT0geWNbal07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmVtb3ZlIGxlYWRpbmcgemVyb3MgYW5kIGFkanVzdCBleHBvbmVudCBhY2NvcmRpbmdseS5cclxuICAgIGZvciAoOyB4Y1swXSA9PSAwOyB4Yy5zcGxpY2UoMCwgMSksIC0teWUpO1xyXG5cclxuICAgIC8vIFplcm8/XHJcbiAgICBpZiAoIXhjWzBdKSB7XHJcblxyXG4gICAgICAvLyBGb2xsb3dpbmcgSUVFRSA3NTQgKDIwMDgpIDYuMyxcclxuICAgICAgLy8gbiAtIG4gPSArMCAgYnV0ICBuIC0gbiA9IC0wICB3aGVuIHJvdW5kaW5nIHRvd2FyZHMgLUluZmluaXR5LlxyXG4gICAgICB5LnMgPSBST1VORElOR19NT0RFID09IDMgPyAtMSA6IDE7XHJcbiAgICAgIHkuYyA9IFt5LmUgPSAwXTtcclxuICAgICAgcmV0dXJuIHk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm8gbmVlZCB0byBjaGVjayBmb3IgSW5maW5pdHkgYXMgK3ggLSAreSAhPSBJbmZpbml0eSAmJiAteCAtIC15ICE9IEluZmluaXR5XHJcbiAgICAvLyBmb3IgZmluaXRlIHggYW5kIHkuXHJcbiAgICByZXR1cm4gbm9ybWFsaXNlKHksIHhjLCB5ZSk7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogICBuICUgMCA9ICBOXHJcbiAgICogICBuICUgTiA9ICBOXHJcbiAgICogICBuICUgSSA9ICBuXHJcbiAgICogICAwICUgbiA9ICAwXHJcbiAgICogIC0wICUgbiA9IC0wXHJcbiAgICogICAwICUgMCA9ICBOXHJcbiAgICogICAwICUgTiA9ICBOXHJcbiAgICogICAwICUgSSA9ICAwXHJcbiAgICogICBOICUgbiA9ICBOXHJcbiAgICogICBOICUgMCA9ICBOXHJcbiAgICogICBOICUgTiA9ICBOXHJcbiAgICogICBOICUgSSA9ICBOXHJcbiAgICogICBJICUgbiA9ICBOXHJcbiAgICogICBJICUgMCA9ICBOXHJcbiAgICogICBJICUgTiA9ICBOXHJcbiAgICogICBJICUgSSA9ICBOXHJcbiAgICpcclxuICAgKiBSZXR1cm4gYSBuZXcgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBtb2R1bG8gdGhlIHZhbHVlIG9mXHJcbiAgICogQmlnTnVtYmVyKHksIGIpLiBUaGUgcmVzdWx0IGRlcGVuZHMgb24gdGhlIHZhbHVlIG9mIE1PRFVMT19NT0RFLlxyXG4gICAqL1xyXG4gIFAubW9kdWxvID0gUC5tb2QgPSBmdW5jdGlvbiAoeSwgYikge1xyXG4gICAgdmFyIHEsIHMsXHJcbiAgICAgIHggPSB0aGlzO1xyXG5cclxuICAgIHkgPSBuZXcgQmlnTnVtYmVyKHksIGIpO1xyXG5cclxuICAgIC8vIFJldHVybiBOYU4gaWYgeCBpcyBJbmZpbml0eSBvciBOYU4sIG9yIHkgaXMgTmFOIG9yIHplcm8uXHJcbiAgICBpZiAoIXguYyB8fCAheS5zIHx8IHkuYyAmJiAheS5jWzBdKSB7XHJcbiAgICAgIHJldHVybiBuZXcgQmlnTnVtYmVyKE5hTik7XHJcblxyXG4gICAgLy8gUmV0dXJuIHggaWYgeSBpcyBJbmZpbml0eSBvciB4IGlzIHplcm8uXHJcbiAgICB9IGVsc2UgaWYgKCF5LmMgfHwgeC5jICYmICF4LmNbMF0pIHtcclxuICAgICAgcmV0dXJuIG5ldyBCaWdOdW1iZXIoeCk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKE1PRFVMT19NT0RFID09IDkpIHtcclxuXHJcbiAgICAgIC8vIEV1Y2xpZGlhbiBkaXZpc2lvbjogcSA9IHNpZ24oeSkgKiBmbG9vcih4IC8gYWJzKHkpKVxyXG4gICAgICAvLyByID0geCAtIHF5ICAgIHdoZXJlICAwIDw9IHIgPCBhYnMoeSlcclxuICAgICAgcyA9IHkucztcclxuICAgICAgeS5zID0gMTtcclxuICAgICAgcSA9IGRpdih4LCB5LCAwLCAzKTtcclxuICAgICAgeS5zID0gcztcclxuICAgICAgcS5zICo9IHM7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBxID0gZGl2KHgsIHksIDAsIE1PRFVMT19NT0RFKTtcclxuICAgIH1cclxuXHJcbiAgICB5ID0geC5taW51cyhxLnRpbWVzKHkpKTtcclxuXHJcbiAgICAvLyBUbyBtYXRjaCBKYXZhU2NyaXB0ICUsIGVuc3VyZSBzaWduIG9mIHplcm8gaXMgc2lnbiBvZiBkaXZpZGVuZC5cclxuICAgIGlmICgheS5jWzBdICYmIE1PRFVMT19NT0RFID09IDEpIHkucyA9IHgucztcclxuXHJcbiAgICByZXR1cm4geTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiAgbiAqIDAgPSAwXHJcbiAgICogIG4gKiBOID0gTlxyXG4gICAqICBuICogSSA9IElcclxuICAgKiAgMCAqIG4gPSAwXHJcbiAgICogIDAgKiAwID0gMFxyXG4gICAqICAwICogTiA9IE5cclxuICAgKiAgMCAqIEkgPSBOXHJcbiAgICogIE4gKiBuID0gTlxyXG4gICAqICBOICogMCA9IE5cclxuICAgKiAgTiAqIE4gPSBOXHJcbiAgICogIE4gKiBJID0gTlxyXG4gICAqICBJICogbiA9IElcclxuICAgKiAgSSAqIDAgPSBOXHJcbiAgICogIEkgKiBOID0gTlxyXG4gICAqICBJICogSSA9IElcclxuICAgKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIG11bHRpcGxpZWQgYnkgdGhlIHZhbHVlXHJcbiAgICogb2YgQmlnTnVtYmVyKHksIGIpLlxyXG4gICAqL1xyXG4gIFAubXVsdGlwbGllZEJ5ID0gUC50aW1lcyA9IGZ1bmN0aW9uICh5LCBiKSB7XHJcbiAgICB2YXIgYywgZSwgaSwgaiwgaywgbSwgeGNMLCB4bG8sIHhoaSwgeWNMLCB5bG8sIHloaSwgemMsXHJcbiAgICAgIGJhc2UsIHNxcnRCYXNlLFxyXG4gICAgICB4ID0gdGhpcyxcclxuICAgICAgeGMgPSB4LmMsXHJcbiAgICAgIHljID0gKHkgPSBuZXcgQmlnTnVtYmVyKHksIGIpKS5jO1xyXG5cclxuICAgIC8vIEVpdGhlciBOYU4sIMKxSW5maW5pdHkgb3IgwrEwP1xyXG4gICAgaWYgKCF4YyB8fCAheWMgfHwgIXhjWzBdIHx8ICF5Y1swXSkge1xyXG5cclxuICAgICAgLy8gUmV0dXJuIE5hTiBpZiBlaXRoZXIgaXMgTmFOLCBvciBvbmUgaXMgMCBhbmQgdGhlIG90aGVyIGlzIEluZmluaXR5LlxyXG4gICAgICBpZiAoIXgucyB8fCAheS5zIHx8IHhjICYmICF4Y1swXSAmJiAheWMgfHwgeWMgJiYgIXljWzBdICYmICF4Yykge1xyXG4gICAgICAgIHkuYyA9IHkuZSA9IHkucyA9IG51bGw7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgeS5zICo9IHgucztcclxuXHJcbiAgICAgICAgLy8gUmV0dXJuIMKxSW5maW5pdHkgaWYgZWl0aGVyIGlzIMKxSW5maW5pdHkuXHJcbiAgICAgICAgaWYgKCF4YyB8fCAheWMpIHtcclxuICAgICAgICAgIHkuYyA9IHkuZSA9IG51bGw7XHJcblxyXG4gICAgICAgIC8vIFJldHVybiDCsTAgaWYgZWl0aGVyIGlzIMKxMC5cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgeS5jID0gWzBdO1xyXG4gICAgICAgICAgeS5lID0gMDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiB5O1xyXG4gICAgfVxyXG5cclxuICAgIGUgPSBiaXRGbG9vcih4LmUgLyBMT0dfQkFTRSkgKyBiaXRGbG9vcih5LmUgLyBMT0dfQkFTRSk7XHJcbiAgICB5LnMgKj0geC5zO1xyXG4gICAgeGNMID0geGMubGVuZ3RoO1xyXG4gICAgeWNMID0geWMubGVuZ3RoO1xyXG5cclxuICAgIC8vIEVuc3VyZSB4YyBwb2ludHMgdG8gbG9uZ2VyIGFycmF5IGFuZCB4Y0wgdG8gaXRzIGxlbmd0aC5cclxuICAgIGlmICh4Y0wgPCB5Y0wpIHtcclxuICAgICAgemMgPSB4YztcclxuICAgICAgeGMgPSB5YztcclxuICAgICAgeWMgPSB6YztcclxuICAgICAgaSA9IHhjTDtcclxuICAgICAgeGNMID0geWNMO1xyXG4gICAgICB5Y0wgPSBpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEluaXRpYWxpc2UgdGhlIHJlc3VsdCBhcnJheSB3aXRoIHplcm9zLlxyXG4gICAgZm9yIChpID0geGNMICsgeWNMLCB6YyA9IFtdOyBpLS07IHpjLnB1c2goMCkpO1xyXG5cclxuICAgIGJhc2UgPSBCQVNFO1xyXG4gICAgc3FydEJhc2UgPSBTUVJUX0JBU0U7XHJcblxyXG4gICAgZm9yIChpID0geWNMOyAtLWkgPj0gMDspIHtcclxuICAgICAgYyA9IDA7XHJcbiAgICAgIHlsbyA9IHljW2ldICUgc3FydEJhc2U7XHJcbiAgICAgIHloaSA9IHljW2ldIC8gc3FydEJhc2UgfCAwO1xyXG5cclxuICAgICAgZm9yIChrID0geGNMLCBqID0gaSArIGs7IGogPiBpOykge1xyXG4gICAgICAgIHhsbyA9IHhjWy0ta10gJSBzcXJ0QmFzZTtcclxuICAgICAgICB4aGkgPSB4Y1trXSAvIHNxcnRCYXNlIHwgMDtcclxuICAgICAgICBtID0geWhpICogeGxvICsgeGhpICogeWxvO1xyXG4gICAgICAgIHhsbyA9IHlsbyAqIHhsbyArICgobSAlIHNxcnRCYXNlKSAqIHNxcnRCYXNlKSArIHpjW2pdICsgYztcclxuICAgICAgICBjID0gKHhsbyAvIGJhc2UgfCAwKSArIChtIC8gc3FydEJhc2UgfCAwKSArIHloaSAqIHhoaTtcclxuICAgICAgICB6Y1tqLS1dID0geGxvICUgYmFzZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgemNbal0gPSBjO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChjKSB7XHJcbiAgICAgICsrZTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHpjLnNwbGljZSgwLCAxKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbm9ybWFsaXNlKHksIHpjLCBlKTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBuZXcgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBuZWdhdGVkLFxyXG4gICAqIGkuZS4gbXVsdGlwbGllZCBieSAtMS5cclxuICAgKi9cclxuICBQLm5lZ2F0ZWQgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICB2YXIgeCA9IG5ldyBCaWdOdW1iZXIodGhpcyk7XHJcbiAgICB4LnMgPSAteC5zIHx8IG51bGw7XHJcbiAgICByZXR1cm4geDtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiAgbiArIDAgPSBuXHJcbiAgICogIG4gKyBOID0gTlxyXG4gICAqICBuICsgSSA9IElcclxuICAgKiAgMCArIG4gPSBuXHJcbiAgICogIDAgKyAwID0gMFxyXG4gICAqICAwICsgTiA9IE5cclxuICAgKiAgMCArIEkgPSBJXHJcbiAgICogIE4gKyBuID0gTlxyXG4gICAqICBOICsgMCA9IE5cclxuICAgKiAgTiArIE4gPSBOXHJcbiAgICogIE4gKyBJID0gTlxyXG4gICAqICBJICsgbiA9IElcclxuICAgKiAgSSArIDAgPSBJXHJcbiAgICogIEkgKyBOID0gTlxyXG4gICAqICBJICsgSSA9IElcclxuICAgKlxyXG4gICAqIFJldHVybiBhIG5ldyBCaWdOdW1iZXIgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIHBsdXMgdGhlIHZhbHVlIG9mXHJcbiAgICogQmlnTnVtYmVyKHksIGIpLlxyXG4gICAqL1xyXG4gIFAucGx1cyA9IGZ1bmN0aW9uICh5LCBiKSB7XHJcbiAgICB2YXIgdCxcclxuICAgICAgeCA9IHRoaXMsXHJcbiAgICAgIGEgPSB4LnM7XHJcblxyXG4gICAgeSA9IG5ldyBCaWdOdW1iZXIoeSwgYik7XHJcbiAgICBiID0geS5zO1xyXG5cclxuICAgIC8vIEVpdGhlciBOYU4/XHJcbiAgICBpZiAoIWEgfHwgIWIpIHJldHVybiBuZXcgQmlnTnVtYmVyKE5hTik7XHJcblxyXG4gICAgLy8gU2lnbnMgZGlmZmVyP1xyXG4gICAgIGlmIChhICE9IGIpIHtcclxuICAgICAgeS5zID0gLWI7XHJcbiAgICAgIHJldHVybiB4Lm1pbnVzKHkpO1xyXG4gICAgfVxyXG5cclxuICAgIHZhciB4ZSA9IHguZSAvIExPR19CQVNFLFxyXG4gICAgICB5ZSA9IHkuZSAvIExPR19CQVNFLFxyXG4gICAgICB4YyA9IHguYyxcclxuICAgICAgeWMgPSB5LmM7XHJcblxyXG4gICAgaWYgKCF4ZSB8fCAheWUpIHtcclxuXHJcbiAgICAgIC8vIFJldHVybiDCsUluZmluaXR5IGlmIGVpdGhlciDCsUluZmluaXR5LlxyXG4gICAgICBpZiAoIXhjIHx8ICF5YykgcmV0dXJuIG5ldyBCaWdOdW1iZXIoYSAvIDApO1xyXG5cclxuICAgICAgLy8gRWl0aGVyIHplcm8/XHJcbiAgICAgIC8vIFJldHVybiB5IGlmIHkgaXMgbm9uLXplcm8sIHggaWYgeCBpcyBub24temVybywgb3IgemVybyBpZiBib3RoIGFyZSB6ZXJvLlxyXG4gICAgICBpZiAoIXhjWzBdIHx8ICF5Y1swXSkgcmV0dXJuIHljWzBdID8geSA6IG5ldyBCaWdOdW1iZXIoeGNbMF0gPyB4IDogYSAqIDApO1xyXG4gICAgfVxyXG5cclxuICAgIHhlID0gYml0Rmxvb3IoeGUpO1xyXG4gICAgeWUgPSBiaXRGbG9vcih5ZSk7XHJcbiAgICB4YyA9IHhjLnNsaWNlKCk7XHJcblxyXG4gICAgLy8gUHJlcGVuZCB6ZXJvcyB0byBlcXVhbGlzZSBleHBvbmVudHMuIEZhc3RlciB0byB1c2UgcmV2ZXJzZSB0aGVuIGRvIHVuc2hpZnRzLlxyXG4gICAgaWYgKGEgPSB4ZSAtIHllKSB7XHJcbiAgICAgIGlmIChhID4gMCkge1xyXG4gICAgICAgIHllID0geGU7XHJcbiAgICAgICAgdCA9IHljO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGEgPSAtYTtcclxuICAgICAgICB0ID0geGM7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHQucmV2ZXJzZSgpO1xyXG4gICAgICBmb3IgKDsgYS0tOyB0LnB1c2goMCkpO1xyXG4gICAgICB0LnJldmVyc2UoKTtcclxuICAgIH1cclxuXHJcbiAgICBhID0geGMubGVuZ3RoO1xyXG4gICAgYiA9IHljLmxlbmd0aDtcclxuXHJcbiAgICAvLyBQb2ludCB4YyB0byB0aGUgbG9uZ2VyIGFycmF5LCBhbmQgYiB0byB0aGUgc2hvcnRlciBsZW5ndGguXHJcbiAgICBpZiAoYSAtIGIgPCAwKSB7XHJcbiAgICAgIHQgPSB5YztcclxuICAgICAgeWMgPSB4YztcclxuICAgICAgeGMgPSB0O1xyXG4gICAgICBiID0gYTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBPbmx5IHN0YXJ0IGFkZGluZyBhdCB5Yy5sZW5ndGggLSAxIGFzIHRoZSBmdXJ0aGVyIGRpZ2l0cyBvZiB4YyBjYW4gYmUgaWdub3JlZC5cclxuICAgIGZvciAoYSA9IDA7IGI7KSB7XHJcbiAgICAgIGEgPSAoeGNbLS1iXSA9IHhjW2JdICsgeWNbYl0gKyBhKSAvIEJBU0UgfCAwO1xyXG4gICAgICB4Y1tiXSA9IEJBU0UgPT09IHhjW2JdID8gMCA6IHhjW2JdICUgQkFTRTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoYSkge1xyXG4gICAgICB4YyA9IFthXS5jb25jYXQoeGMpO1xyXG4gICAgICArK3llO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vIG5lZWQgdG8gY2hlY2sgZm9yIHplcm8sIGFzICt4ICsgK3kgIT0gMCAmJiAteCArIC15ICE9IDBcclxuICAgIC8vIHllID0gTUFYX0VYUCArIDEgcG9zc2libGVcclxuICAgIHJldHVybiBub3JtYWxpc2UoeSwgeGMsIHllKTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBJZiBzZCBpcyB1bmRlZmluZWQgb3IgbnVsbCBvciB0cnVlIG9yIGZhbHNlLCByZXR1cm4gdGhlIG51bWJlciBvZiBzaWduaWZpY2FudCBkaWdpdHMgb2ZcclxuICAgKiB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIsIG9yIG51bGwgaWYgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGlzIMKxSW5maW5pdHkgb3IgTmFOLlxyXG4gICAqIElmIHNkIGlzIHRydWUgaW5jbHVkZSBpbnRlZ2VyLXBhcnQgdHJhaWxpbmcgemVyb3MgaW4gdGhlIGNvdW50LlxyXG4gICAqXHJcbiAgICogT3RoZXJ3aXNlLCBpZiBzZCBpcyBhIG51bWJlciwgcmV0dXJuIGEgbmV3IEJpZ051bWJlciB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpc1xyXG4gICAqIEJpZ051bWJlciByb3VuZGVkIHRvIGEgbWF4aW11bSBvZiBzZCBzaWduaWZpY2FudCBkaWdpdHMgdXNpbmcgcm91bmRpbmcgbW9kZSBybSwgb3JcclxuICAgKiBST1VORElOR19NT0RFIGlmIHJtIGlzIG9taXR0ZWQuXHJcbiAgICpcclxuICAgKiBzZCB7bnVtYmVyfGJvb2xlYW59IG51bWJlcjogc2lnbmlmaWNhbnQgZGlnaXRzOiBpbnRlZ2VyLCAxIHRvIE1BWCBpbmNsdXNpdmUuXHJcbiAgICogICAgICAgICAgICAgICAgICAgICBib29sZWFuOiB3aGV0aGVyIHRvIGNvdW50IGludGVnZXItcGFydCB0cmFpbGluZyB6ZXJvczogdHJ1ZSBvciBmYWxzZS5cclxuICAgKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge3NkfHJtfSdcclxuICAgKi9cclxuICBQLnByZWNpc2lvbiA9IFAuc2QgPSBmdW5jdGlvbiAoc2QsIHJtKSB7XHJcbiAgICB2YXIgYywgbiwgdixcclxuICAgICAgeCA9IHRoaXM7XHJcblxyXG4gICAgaWYgKHNkICE9IG51bGwgJiYgc2QgIT09ICEhc2QpIHtcclxuICAgICAgaW50Q2hlY2soc2QsIDEsIE1BWCk7XHJcbiAgICAgIGlmIChybSA9PSBudWxsKSBybSA9IFJPVU5ESU5HX01PREU7XHJcbiAgICAgIGVsc2UgaW50Q2hlY2socm0sIDAsIDgpO1xyXG5cclxuICAgICAgcmV0dXJuIHJvdW5kKG5ldyBCaWdOdW1iZXIoeCksIHNkLCBybSk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKCEoYyA9IHguYykpIHJldHVybiBudWxsO1xyXG4gICAgdiA9IGMubGVuZ3RoIC0gMTtcclxuICAgIG4gPSB2ICogTE9HX0JBU0UgKyAxO1xyXG5cclxuICAgIGlmICh2ID0gY1t2XSkge1xyXG5cclxuICAgICAgLy8gU3VidHJhY3QgdGhlIG51bWJlciBvZiB0cmFpbGluZyB6ZXJvcyBvZiB0aGUgbGFzdCBlbGVtZW50LlxyXG4gICAgICBmb3IgKDsgdiAlIDEwID09IDA7IHYgLz0gMTAsIG4tLSk7XHJcblxyXG4gICAgICAvLyBBZGQgdGhlIG51bWJlciBvZiBkaWdpdHMgb2YgdGhlIGZpcnN0IGVsZW1lbnQuXHJcbiAgICAgIGZvciAodiA9IGNbMF07IHYgPj0gMTA7IHYgLz0gMTAsIG4rKyk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHNkICYmIHguZSArIDEgPiBuKSBuID0geC5lICsgMTtcclxuXHJcbiAgICByZXR1cm4gbjtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBuZXcgQmlnTnVtYmVyIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBzaGlmdGVkIGJ5IGsgcGxhY2VzXHJcbiAgICogKHBvd2VycyBvZiAxMCkuIFNoaWZ0IHRvIHRoZSByaWdodCBpZiBuID4gMCwgYW5kIHRvIHRoZSBsZWZ0IGlmIG4gPCAwLlxyXG4gICAqXHJcbiAgICogayB7bnVtYmVyfSBJbnRlZ2VyLCAtTUFYX1NBRkVfSU5URUdFUiB0byBNQVhfU0FGRV9JTlRFR0VSIGluY2x1c2l2ZS5cclxuICAgKlxyXG4gICAqICdbQmlnTnVtYmVyIEVycm9yXSBBcmd1bWVudCB7bm90IGEgcHJpbWl0aXZlIG51bWJlcnxub3QgYW4gaW50ZWdlcnxvdXQgb2YgcmFuZ2V9OiB7a30nXHJcbiAgICovXHJcbiAgUC5zaGlmdGVkQnkgPSBmdW5jdGlvbiAoaykge1xyXG4gICAgaW50Q2hlY2soaywgLU1BWF9TQUZFX0lOVEVHRVIsIE1BWF9TQUZFX0lOVEVHRVIpO1xyXG4gICAgcmV0dXJuIHRoaXMudGltZXMoJzFlJyArIGspO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqICBzcXJ0KC1uKSA9ICBOXHJcbiAgICogIHNxcnQoTikgPSAgTlxyXG4gICAqICBzcXJ0KC1JKSA9ICBOXHJcbiAgICogIHNxcnQoSSkgPSAgSVxyXG4gICAqICBzcXJ0KDApID0gIDBcclxuICAgKiAgc3FydCgtMCkgPSAtMFxyXG4gICAqXHJcbiAgICogUmV0dXJuIGEgbmV3IEJpZ051bWJlciB3aG9zZSB2YWx1ZSBpcyB0aGUgc3F1YXJlIHJvb3Qgb2YgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyLFxyXG4gICAqIHJvdW5kZWQgYWNjb3JkaW5nIHRvIERFQ0lNQUxfUExBQ0VTIGFuZCBST1VORElOR19NT0RFLlxyXG4gICAqL1xyXG4gIFAuc3F1YXJlUm9vdCA9IFAuc3FydCA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciBtLCBuLCByLCByZXAsIHQsXHJcbiAgICAgIHggPSB0aGlzLFxyXG4gICAgICBjID0geC5jLFxyXG4gICAgICBzID0geC5zLFxyXG4gICAgICBlID0geC5lLFxyXG4gICAgICBkcCA9IERFQ0lNQUxfUExBQ0VTICsgNCxcclxuICAgICAgaGFsZiA9IG5ldyBCaWdOdW1iZXIoJzAuNScpO1xyXG5cclxuICAgIC8vIE5lZ2F0aXZlL05hTi9JbmZpbml0eS96ZXJvP1xyXG4gICAgaWYgKHMgIT09IDEgfHwgIWMgfHwgIWNbMF0pIHtcclxuICAgICAgcmV0dXJuIG5ldyBCaWdOdW1iZXIoIXMgfHwgcyA8IDAgJiYgKCFjIHx8IGNbMF0pID8gTmFOIDogYyA/IHggOiAxIC8gMCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSW5pdGlhbCBlc3RpbWF0ZS5cclxuICAgIHMgPSBNYXRoLnNxcnQoK3ZhbHVlT2YoeCkpO1xyXG5cclxuICAgIC8vIE1hdGguc3FydCB1bmRlcmZsb3cvb3ZlcmZsb3c/XHJcbiAgICAvLyBQYXNzIHggdG8gTWF0aC5zcXJ0IGFzIGludGVnZXIsIHRoZW4gYWRqdXN0IHRoZSBleHBvbmVudCBvZiB0aGUgcmVzdWx0LlxyXG4gICAgaWYgKHMgPT0gMCB8fCBzID09IDEgLyAwKSB7XHJcbiAgICAgIG4gPSBjb2VmZlRvU3RyaW5nKGMpO1xyXG4gICAgICBpZiAoKG4ubGVuZ3RoICsgZSkgJSAyID09IDApIG4gKz0gJzAnO1xyXG4gICAgICBzID0gTWF0aC5zcXJ0KCtuKTtcclxuICAgICAgZSA9IGJpdEZsb29yKChlICsgMSkgLyAyKSAtIChlIDwgMCB8fCBlICUgMik7XHJcblxyXG4gICAgICBpZiAocyA9PSAxIC8gMCkge1xyXG4gICAgICAgIG4gPSAnNWUnICsgZTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBuID0gcy50b0V4cG9uZW50aWFsKCk7XHJcbiAgICAgICAgbiA9IG4uc2xpY2UoMCwgbi5pbmRleE9mKCdlJykgKyAxKSArIGU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHIgPSBuZXcgQmlnTnVtYmVyKG4pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgciA9IG5ldyBCaWdOdW1iZXIocyArICcnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBmb3IgemVyby5cclxuICAgIC8vIHIgY291bGQgYmUgemVybyBpZiBNSU5fRVhQIGlzIGNoYW5nZWQgYWZ0ZXIgdGhlIHRoaXMgdmFsdWUgd2FzIGNyZWF0ZWQuXHJcbiAgICAvLyBUaGlzIHdvdWxkIGNhdXNlIGEgZGl2aXNpb24gYnkgemVybyAoeC90KSBhbmQgaGVuY2UgSW5maW5pdHkgYmVsb3csIHdoaWNoIHdvdWxkIGNhdXNlXHJcbiAgICAvLyBjb2VmZlRvU3RyaW5nIHRvIHRocm93LlxyXG4gICAgaWYgKHIuY1swXSkge1xyXG4gICAgICBlID0gci5lO1xyXG4gICAgICBzID0gZSArIGRwO1xyXG4gICAgICBpZiAocyA8IDMpIHMgPSAwO1xyXG5cclxuICAgICAgLy8gTmV3dG9uLVJhcGhzb24gaXRlcmF0aW9uLlxyXG4gICAgICBmb3IgKDsgOykge1xyXG4gICAgICAgIHQgPSByO1xyXG4gICAgICAgIHIgPSBoYWxmLnRpbWVzKHQucGx1cyhkaXYoeCwgdCwgZHAsIDEpKSk7XHJcblxyXG4gICAgICAgIGlmIChjb2VmZlRvU3RyaW5nKHQuYykuc2xpY2UoMCwgcykgPT09IChuID0gY29lZmZUb1N0cmluZyhyLmMpKS5zbGljZSgwLCBzKSkge1xyXG5cclxuICAgICAgICAgIC8vIFRoZSBleHBvbmVudCBvZiByIG1heSBoZXJlIGJlIG9uZSBsZXNzIHRoYW4gdGhlIGZpbmFsIHJlc3VsdCBleHBvbmVudCxcclxuICAgICAgICAgIC8vIGUuZyAwLjAwMDk5OTkgKGUtNCkgLS0+IDAuMDAxIChlLTMpLCBzbyBhZGp1c3QgcyBzbyB0aGUgcm91bmRpbmcgZGlnaXRzXHJcbiAgICAgICAgICAvLyBhcmUgaW5kZXhlZCBjb3JyZWN0bHkuXHJcbiAgICAgICAgICBpZiAoci5lIDwgZSkgLS1zO1xyXG4gICAgICAgICAgbiA9IG4uc2xpY2UocyAtIDMsIHMgKyAxKTtcclxuXHJcbiAgICAgICAgICAvLyBUaGUgNHRoIHJvdW5kaW5nIGRpZ2l0IG1heSBiZSBpbiBlcnJvciBieSAtMSBzbyBpZiB0aGUgNCByb3VuZGluZyBkaWdpdHNcclxuICAgICAgICAgIC8vIGFyZSA5OTk5IG9yIDQ5OTkgKGkuZS4gYXBwcm9hY2hpbmcgYSByb3VuZGluZyBib3VuZGFyeSkgY29udGludWUgdGhlXHJcbiAgICAgICAgICAvLyBpdGVyYXRpb24uXHJcbiAgICAgICAgICBpZiAobiA9PSAnOTk5OScgfHwgIXJlcCAmJiBuID09ICc0OTk5Jykge1xyXG5cclxuICAgICAgICAgICAgLy8gT24gdGhlIGZpcnN0IGl0ZXJhdGlvbiBvbmx5LCBjaGVjayB0byBzZWUgaWYgcm91bmRpbmcgdXAgZ2l2ZXMgdGhlXHJcbiAgICAgICAgICAgIC8vIGV4YWN0IHJlc3VsdCBhcyB0aGUgbmluZXMgbWF5IGluZmluaXRlbHkgcmVwZWF0LlxyXG4gICAgICAgICAgICBpZiAoIXJlcCkge1xyXG4gICAgICAgICAgICAgIHJvdW5kKHQsIHQuZSArIERFQ0lNQUxfUExBQ0VTICsgMiwgMCk7XHJcblxyXG4gICAgICAgICAgICAgIGlmICh0LnRpbWVzKHQpLmVxKHgpKSB7XHJcbiAgICAgICAgICAgICAgICByID0gdDtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgZHAgKz0gNDtcclxuICAgICAgICAgICAgcyArPSA0O1xyXG4gICAgICAgICAgICByZXAgPSAxO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuXHJcbiAgICAgICAgICAgIC8vIElmIHJvdW5kaW5nIGRpZ2l0cyBhcmUgbnVsbCwgMHswLDR9IG9yIDUwezAsM30sIGNoZWNrIGZvciBleGFjdFxyXG4gICAgICAgICAgICAvLyByZXN1bHQuIElmIG5vdCwgdGhlbiB0aGVyZSBhcmUgZnVydGhlciBkaWdpdHMgYW5kIG0gd2lsbCBiZSB0cnV0aHkuXHJcbiAgICAgICAgICAgIGlmICghK24gfHwgIStuLnNsaWNlKDEpICYmIG4uY2hhckF0KDApID09ICc1Jykge1xyXG5cclxuICAgICAgICAgICAgICAvLyBUcnVuY2F0ZSB0byB0aGUgZmlyc3Qgcm91bmRpbmcgZGlnaXQuXHJcbiAgICAgICAgICAgICAgcm91bmQociwgci5lICsgREVDSU1BTF9QTEFDRVMgKyAyLCAxKTtcclxuICAgICAgICAgICAgICBtID0gIXIudGltZXMocikuZXEoeCk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiByb3VuZChyLCByLmUgKyBERUNJTUFMX1BMQUNFUyArIDEsIFJPVU5ESU5HX01PREUsIG0pO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGluIGV4cG9uZW50aWFsIG5vdGF0aW9uIGFuZFxyXG4gICAqIHJvdW5kZWQgdXNpbmcgUk9VTkRJTkdfTU9ERSB0byBkcCBmaXhlZCBkZWNpbWFsIHBsYWNlcy5cclxuICAgKlxyXG4gICAqIFtkcF0ge251bWJlcn0gRGVjaW1hbCBwbGFjZXMuIEludGVnZXIsIDAgdG8gTUFYIGluY2x1c2l2ZS5cclxuICAgKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge2RwfHJtfSdcclxuICAgKi9cclxuICBQLnRvRXhwb25lbnRpYWwgPSBmdW5jdGlvbiAoZHAsIHJtKSB7XHJcbiAgICBpZiAoZHAgIT0gbnVsbCkge1xyXG4gICAgICBpbnRDaGVjayhkcCwgMCwgTUFYKTtcclxuICAgICAgZHArKztcclxuICAgIH1cclxuICAgIHJldHVybiBmb3JtYXQodGhpcywgZHAsIHJtLCAxKTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBzdHJpbmcgcmVwcmVzZW50aW5nIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBpbiBmaXhlZC1wb2ludCBub3RhdGlvbiByb3VuZGluZ1xyXG4gICAqIHRvIGRwIGZpeGVkIGRlY2ltYWwgcGxhY2VzIHVzaW5nIHJvdW5kaW5nIG1vZGUgcm0sIG9yIFJPVU5ESU5HX01PREUgaWYgcm0gaXMgb21pdHRlZC5cclxuICAgKlxyXG4gICAqIE5vdGU6IGFzIHdpdGggSmF2YVNjcmlwdCdzIG51bWJlciB0eXBlLCAoLTApLnRvRml4ZWQoMCkgaXMgJzAnLFxyXG4gICAqIGJ1dCBlLmcuICgtMC4wMDAwMSkudG9GaXhlZCgwKSBpcyAnLTAnLlxyXG4gICAqXHJcbiAgICogW2RwXSB7bnVtYmVyfSBEZWNpbWFsIHBsYWNlcy4gSW50ZWdlciwgMCB0byBNQVggaW5jbHVzaXZlLlxyXG4gICAqIFtybV0ge251bWJlcn0gUm91bmRpbmcgbW9kZS4gSW50ZWdlciwgMCB0byA4IGluY2x1c2l2ZS5cclxuICAgKlxyXG4gICAqICdbQmlnTnVtYmVyIEVycm9yXSBBcmd1bWVudCB7bm90IGEgcHJpbWl0aXZlIG51bWJlcnxub3QgYW4gaW50ZWdlcnxvdXQgb2YgcmFuZ2V9OiB7ZHB8cm19J1xyXG4gICAqL1xyXG4gIFAudG9GaXhlZCA9IGZ1bmN0aW9uIChkcCwgcm0pIHtcclxuICAgIGlmIChkcCAhPSBudWxsKSB7XHJcbiAgICAgIGludENoZWNrKGRwLCAwLCBNQVgpO1xyXG4gICAgICBkcCA9IGRwICsgdGhpcy5lICsgMTtcclxuICAgIH1cclxuICAgIHJldHVybiBmb3JtYXQodGhpcywgZHAsIHJtKTtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYSBzdHJpbmcgcmVwcmVzZW50aW5nIHRoZSB2YWx1ZSBvZiB0aGlzIEJpZ051bWJlciBpbiBmaXhlZC1wb2ludCBub3RhdGlvbiByb3VuZGVkXHJcbiAgICogdXNpbmcgcm0gb3IgUk9VTkRJTkdfTU9ERSB0byBkcCBkZWNpbWFsIHBsYWNlcywgYW5kIGZvcm1hdHRlZCBhY2NvcmRpbmcgdG8gdGhlIHByb3BlcnRpZXNcclxuICAgKiBvZiB0aGUgZm9ybWF0IG9yIEZPUk1BVCBvYmplY3QgKHNlZSBCaWdOdW1iZXIuc2V0KS5cclxuICAgKlxyXG4gICAqIFRoZSBmb3JtYXR0aW5nIG9iamVjdCBtYXkgY29udGFpbiBzb21lIG9yIGFsbCBvZiB0aGUgcHJvcGVydGllcyBzaG93biBiZWxvdy5cclxuICAgKlxyXG4gICAqIEZPUk1BVCA9IHtcclxuICAgKiAgIHByZWZpeDogJycsXHJcbiAgICogICBncm91cFNpemU6IDMsXHJcbiAgICogICBzZWNvbmRhcnlHcm91cFNpemU6IDAsXHJcbiAgICogICBncm91cFNlcGFyYXRvcjogJywnLFxyXG4gICAqICAgZGVjaW1hbFNlcGFyYXRvcjogJy4nLFxyXG4gICAqICAgZnJhY3Rpb25Hcm91cFNpemU6IDAsXHJcbiAgICogICBmcmFjdGlvbkdyb3VwU2VwYXJhdG9yOiAnXFx4QTAnLCAgICAgIC8vIG5vbi1icmVha2luZyBzcGFjZVxyXG4gICAqICAgc3VmZml4OiAnJ1xyXG4gICAqIH07XHJcbiAgICpcclxuICAgKiBbZHBdIHtudW1iZXJ9IERlY2ltYWwgcGxhY2VzLiBJbnRlZ2VyLCAwIHRvIE1BWCBpbmNsdXNpdmUuXHJcbiAgICogW3JtXSB7bnVtYmVyfSBSb3VuZGluZyBtb2RlLiBJbnRlZ2VyLCAwIHRvIDggaW5jbHVzaXZlLlxyXG4gICAqIFtmb3JtYXRdIHtvYmplY3R9IEZvcm1hdHRpbmcgb3B0aW9ucy4gU2VlIEZPUk1BVCBwYmplY3QgYWJvdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge2RwfHJtfSdcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQgbm90IGFuIG9iamVjdDoge2Zvcm1hdH0nXHJcbiAgICovXHJcbiAgUC50b0Zvcm1hdCA9IGZ1bmN0aW9uIChkcCwgcm0sIGZvcm1hdCkge1xyXG4gICAgdmFyIHN0cixcclxuICAgICAgeCA9IHRoaXM7XHJcblxyXG4gICAgaWYgKGZvcm1hdCA9PSBudWxsKSB7XHJcbiAgICAgIGlmIChkcCAhPSBudWxsICYmIHJtICYmIHR5cGVvZiBybSA9PSAnb2JqZWN0Jykge1xyXG4gICAgICAgIGZvcm1hdCA9IHJtO1xyXG4gICAgICAgIHJtID0gbnVsbDtcclxuICAgICAgfSBlbHNlIGlmIChkcCAmJiB0eXBlb2YgZHAgPT0gJ29iamVjdCcpIHtcclxuICAgICAgICBmb3JtYXQgPSBkcDtcclxuICAgICAgICBkcCA9IHJtID0gbnVsbDtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBmb3JtYXQgPSBGT1JNQVQ7XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSBpZiAodHlwZW9mIGZvcm1hdCAhPSAnb2JqZWN0Jykge1xyXG4gICAgICB0aHJvdyBFcnJvclxyXG4gICAgICAgIChiaWdudW1iZXJFcnJvciArICdBcmd1bWVudCBub3QgYW4gb2JqZWN0OiAnICsgZm9ybWF0KTtcclxuICAgIH1cclxuXHJcbiAgICBzdHIgPSB4LnRvRml4ZWQoZHAsIHJtKTtcclxuXHJcbiAgICBpZiAoeC5jKSB7XHJcbiAgICAgIHZhciBpLFxyXG4gICAgICAgIGFyciA9IHN0ci5zcGxpdCgnLicpLFxyXG4gICAgICAgIGcxID0gK2Zvcm1hdC5ncm91cFNpemUsXHJcbiAgICAgICAgZzIgPSArZm9ybWF0LnNlY29uZGFyeUdyb3VwU2l6ZSxcclxuICAgICAgICBncm91cFNlcGFyYXRvciA9IGZvcm1hdC5ncm91cFNlcGFyYXRvciB8fCAnJyxcclxuICAgICAgICBpbnRQYXJ0ID0gYXJyWzBdLFxyXG4gICAgICAgIGZyYWN0aW9uUGFydCA9IGFyclsxXSxcclxuICAgICAgICBpc05lZyA9IHgucyA8IDAsXHJcbiAgICAgICAgaW50RGlnaXRzID0gaXNOZWcgPyBpbnRQYXJ0LnNsaWNlKDEpIDogaW50UGFydCxcclxuICAgICAgICBsZW4gPSBpbnREaWdpdHMubGVuZ3RoO1xyXG5cclxuICAgICAgaWYgKGcyKSB7XHJcbiAgICAgICAgaSA9IGcxO1xyXG4gICAgICAgIGcxID0gZzI7XHJcbiAgICAgICAgZzIgPSBpO1xyXG4gICAgICAgIGxlbiAtPSBpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoZzEgPiAwICYmIGxlbiA+IDApIHtcclxuICAgICAgICBpID0gbGVuICUgZzEgfHwgZzE7XHJcbiAgICAgICAgaW50UGFydCA9IGludERpZ2l0cy5zdWJzdHIoMCwgaSk7XHJcbiAgICAgICAgZm9yICg7IGkgPCBsZW47IGkgKz0gZzEpIGludFBhcnQgKz0gZ3JvdXBTZXBhcmF0b3IgKyBpbnREaWdpdHMuc3Vic3RyKGksIGcxKTtcclxuICAgICAgICBpZiAoZzIgPiAwKSBpbnRQYXJ0ICs9IGdyb3VwU2VwYXJhdG9yICsgaW50RGlnaXRzLnNsaWNlKGkpO1xyXG4gICAgICAgIGlmIChpc05lZykgaW50UGFydCA9ICctJyArIGludFBhcnQ7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHN0ciA9IGZyYWN0aW9uUGFydFxyXG4gICAgICAgPyBpbnRQYXJ0ICsgKGZvcm1hdC5kZWNpbWFsU2VwYXJhdG9yIHx8ICcnKSArICgoZzIgPSArZm9ybWF0LmZyYWN0aW9uR3JvdXBTaXplKVxyXG4gICAgICAgID8gZnJhY3Rpb25QYXJ0LnJlcGxhY2UobmV3IFJlZ0V4cCgnXFxcXGR7JyArIGcyICsgJ31cXFxcQicsICdnJyksXHJcbiAgICAgICAgICckJicgKyAoZm9ybWF0LmZyYWN0aW9uR3JvdXBTZXBhcmF0b3IgfHwgJycpKVxyXG4gICAgICAgIDogZnJhY3Rpb25QYXJ0KVxyXG4gICAgICAgOiBpbnRQYXJ0O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoZm9ybWF0LnByZWZpeCB8fCAnJykgKyBzdHIgKyAoZm9ybWF0LnN1ZmZpeCB8fCAnJyk7XHJcbiAgfTtcclxuXHJcblxyXG4gIC8qXHJcbiAgICogUmV0dXJuIGFuIGFycmF5IG9mIHR3byBCaWdOdW1iZXJzIHJlcHJlc2VudGluZyB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIgYXMgYSBzaW1wbGVcclxuICAgKiBmcmFjdGlvbiB3aXRoIGFuIGludGVnZXIgbnVtZXJhdG9yIGFuZCBhbiBpbnRlZ2VyIGRlbm9taW5hdG9yLlxyXG4gICAqIFRoZSBkZW5vbWluYXRvciB3aWxsIGJlIGEgcG9zaXRpdmUgbm9uLXplcm8gdmFsdWUgbGVzcyB0aGFuIG9yIGVxdWFsIHRvIHRoZSBzcGVjaWZpZWRcclxuICAgKiBtYXhpbXVtIGRlbm9taW5hdG9yLiBJZiBhIG1heGltdW0gZGVub21pbmF0b3IgaXMgbm90IHNwZWNpZmllZCwgdGhlIGRlbm9taW5hdG9yIHdpbGwgYmVcclxuICAgKiB0aGUgbG93ZXN0IHZhbHVlIG5lY2Vzc2FyeSB0byByZXByZXNlbnQgdGhlIG51bWJlciBleGFjdGx5LlxyXG4gICAqXHJcbiAgICogW21kXSB7bnVtYmVyfHN0cmluZ3xCaWdOdW1iZXJ9IEludGVnZXIgPj0gMSwgb3IgSW5maW5pdHkuIFRoZSBtYXhpbXVtIGRlbm9taW5hdG9yLlxyXG4gICAqXHJcbiAgICogJ1tCaWdOdW1iZXIgRXJyb3JdIEFyZ3VtZW50IHtub3QgYW4gaW50ZWdlcnxvdXQgb2YgcmFuZ2V9IDoge21kfSdcclxuICAgKi9cclxuICBQLnRvRnJhY3Rpb24gPSBmdW5jdGlvbiAobWQpIHtcclxuICAgIHZhciBkLCBkMCwgZDEsIGQyLCBlLCBleHAsIG4sIG4wLCBuMSwgcSwgciwgcyxcclxuICAgICAgeCA9IHRoaXMsXHJcbiAgICAgIHhjID0geC5jO1xyXG5cclxuICAgIGlmIChtZCAhPSBudWxsKSB7XHJcbiAgICAgIG4gPSBuZXcgQmlnTnVtYmVyKG1kKTtcclxuXHJcbiAgICAgIC8vIFRocm93IGlmIG1kIGlzIGxlc3MgdGhhbiBvbmUgb3IgaXMgbm90IGFuIGludGVnZXIsIHVubGVzcyBpdCBpcyBJbmZpbml0eS5cclxuICAgICAgaWYgKCFuLmlzSW50ZWdlcigpICYmIChuLmMgfHwgbi5zICE9PSAxKSB8fCBuLmx0KE9ORSkpIHtcclxuICAgICAgICB0aHJvdyBFcnJvclxyXG4gICAgICAgICAgKGJpZ251bWJlckVycm9yICsgJ0FyZ3VtZW50ICcgK1xyXG4gICAgICAgICAgICAobi5pc0ludGVnZXIoKSA/ICdvdXQgb2YgcmFuZ2U6ICcgOiAnbm90IGFuIGludGVnZXI6ICcpICsgdmFsdWVPZihuKSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoIXhjKSByZXR1cm4gbmV3IEJpZ051bWJlcih4KTtcclxuXHJcbiAgICBkID0gbmV3IEJpZ051bWJlcihPTkUpO1xyXG4gICAgbjEgPSBkMCA9IG5ldyBCaWdOdW1iZXIoT05FKTtcclxuICAgIGQxID0gbjAgPSBuZXcgQmlnTnVtYmVyKE9ORSk7XHJcbiAgICBzID0gY29lZmZUb1N0cmluZyh4Yyk7XHJcblxyXG4gICAgLy8gRGV0ZXJtaW5lIGluaXRpYWwgZGVub21pbmF0b3IuXHJcbiAgICAvLyBkIGlzIGEgcG93ZXIgb2YgMTAgYW5kIHRoZSBtaW5pbXVtIG1heCBkZW5vbWluYXRvciB0aGF0IHNwZWNpZmllcyB0aGUgdmFsdWUgZXhhY3RseS5cclxuICAgIGUgPSBkLmUgPSBzLmxlbmd0aCAtIHguZSAtIDE7XHJcbiAgICBkLmNbMF0gPSBQT1dTX1RFTlsoZXhwID0gZSAlIExPR19CQVNFKSA8IDAgPyBMT0dfQkFTRSArIGV4cCA6IGV4cF07XHJcbiAgICBtZCA9ICFtZCB8fCBuLmNvbXBhcmVkVG8oZCkgPiAwID8gKGUgPiAwID8gZCA6IG4xKSA6IG47XHJcblxyXG4gICAgZXhwID0gTUFYX0VYUDtcclxuICAgIE1BWF9FWFAgPSAxIC8gMDtcclxuICAgIG4gPSBuZXcgQmlnTnVtYmVyKHMpO1xyXG5cclxuICAgIC8vIG4wID0gZDEgPSAwXHJcbiAgICBuMC5jWzBdID0gMDtcclxuXHJcbiAgICBmb3IgKDsgOykgIHtcclxuICAgICAgcSA9IGRpdihuLCBkLCAwLCAxKTtcclxuICAgICAgZDIgPSBkMC5wbHVzKHEudGltZXMoZDEpKTtcclxuICAgICAgaWYgKGQyLmNvbXBhcmVkVG8obWQpID09IDEpIGJyZWFrO1xyXG4gICAgICBkMCA9IGQxO1xyXG4gICAgICBkMSA9IGQyO1xyXG4gICAgICBuMSA9IG4wLnBsdXMocS50aW1lcyhkMiA9IG4xKSk7XHJcbiAgICAgIG4wID0gZDI7XHJcbiAgICAgIGQgPSBuLm1pbnVzKHEudGltZXMoZDIgPSBkKSk7XHJcbiAgICAgIG4gPSBkMjtcclxuICAgIH1cclxuXHJcbiAgICBkMiA9IGRpdihtZC5taW51cyhkMCksIGQxLCAwLCAxKTtcclxuICAgIG4wID0gbjAucGx1cyhkMi50aW1lcyhuMSkpO1xyXG4gICAgZDAgPSBkMC5wbHVzKGQyLnRpbWVzKGQxKSk7XHJcbiAgICBuMC5zID0gbjEucyA9IHgucztcclxuICAgIGUgPSBlICogMjtcclxuXHJcbiAgICAvLyBEZXRlcm1pbmUgd2hpY2ggZnJhY3Rpb24gaXMgY2xvc2VyIHRvIHgsIG4wL2QwIG9yIG4xL2QxXHJcbiAgICByID0gZGl2KG4xLCBkMSwgZSwgUk9VTkRJTkdfTU9ERSkubWludXMoeCkuYWJzKCkuY29tcGFyZWRUbyhcclxuICAgICAgICBkaXYobjAsIGQwLCBlLCBST1VORElOR19NT0RFKS5taW51cyh4KS5hYnMoKSkgPCAxID8gW24xLCBkMV0gOiBbbjAsIGQwXTtcclxuXHJcbiAgICBNQVhfRVhQID0gZXhwO1xyXG5cclxuICAgIHJldHVybiByO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiB0aGUgdmFsdWUgb2YgdGhpcyBCaWdOdW1iZXIgY29udmVydGVkIHRvIGEgbnVtYmVyIHByaW1pdGl2ZS5cclxuICAgKi9cclxuICBQLnRvTnVtYmVyID0gZnVuY3Rpb24gKCkge1xyXG4gICAgcmV0dXJuICt2YWx1ZU9mKHRoaXMpO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIHJvdW5kZWQgdG8gc2Qgc2lnbmlmaWNhbnQgZGlnaXRzXHJcbiAgICogdXNpbmcgcm91bmRpbmcgbW9kZSBybSBvciBST1VORElOR19NT0RFLiBJZiBzZCBpcyBsZXNzIHRoYW4gdGhlIG51bWJlciBvZiBkaWdpdHNcclxuICAgKiBuZWNlc3NhcnkgdG8gcmVwcmVzZW50IHRoZSBpbnRlZ2VyIHBhcnQgb2YgdGhlIHZhbHVlIGluIGZpeGVkLXBvaW50IG5vdGF0aW9uLCB0aGVuIHVzZVxyXG4gICAqIGV4cG9uZW50aWFsIG5vdGF0aW9uLlxyXG4gICAqXHJcbiAgICogW3NkXSB7bnVtYmVyfSBTaWduaWZpY2FudCBkaWdpdHMuIEludGVnZXIsIDEgdG8gTUFYIGluY2x1c2l2ZS5cclxuICAgKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAgICpcclxuICAgKiAnW0JpZ051bWJlciBFcnJvcl0gQXJndW1lbnQge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge3NkfHJtfSdcclxuICAgKi9cclxuICBQLnRvUHJlY2lzaW9uID0gZnVuY3Rpb24gKHNkLCBybSkge1xyXG4gICAgaWYgKHNkICE9IG51bGwpIGludENoZWNrKHNkLCAxLCBNQVgpO1xyXG4gICAgcmV0dXJuIGZvcm1hdCh0aGlzLCBzZCwgcm0sIDIpO1xyXG4gIH07XHJcblxyXG5cclxuICAvKlxyXG4gICAqIFJldHVybiBhIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIHZhbHVlIG9mIHRoaXMgQmlnTnVtYmVyIGluIGJhc2UgYiwgb3IgYmFzZSAxMCBpZiBiIGlzXHJcbiAgICogb21pdHRlZC4gSWYgYSBiYXNlIGlzIHNwZWNpZmllZCwgaW5jbHVkaW5nIGJhc2UgMTAsIHJvdW5kIGFjY29yZGluZyB0byBERUNJTUFMX1BMQUNFUyBhbmRcclxuICAgKiBST1VORElOR19NT0RFLiBJZiBhIGJhc2UgaXMgbm90IHNwZWNpZmllZCwgYW5kIHRoaXMgQmlnTnVtYmVyIGhhcyBhIHBvc2l0aXZlIGV4cG9uZW50XHJcbiAgICogdGhhdCBpcyBlcXVhbCB0byBvciBncmVhdGVyIHRoYW4gVE9fRVhQX1BPUywgb3IgYSBuZWdhdGl2ZSBleHBvbmVudCBlcXVhbCB0byBvciBsZXNzIHRoYW5cclxuICAgKiBUT19FWFBfTkVHLCByZXR1cm4gZXhwb25lbnRpYWwgbm90YXRpb24uXHJcbiAgICpcclxuICAgKiBbYl0ge251bWJlcn0gSW50ZWdlciwgMiB0byBBTFBIQUJFVC5sZW5ndGggaW5jbHVzaXZlLlxyXG4gICAqXHJcbiAgICogJ1tCaWdOdW1iZXIgRXJyb3JdIEJhc2Uge25vdCBhIHByaW1pdGl2ZSBudW1iZXJ8bm90IGFuIGludGVnZXJ8b3V0IG9mIHJhbmdlfToge2J9J1xyXG4gICAqL1xyXG4gIFAudG9TdHJpbmcgPSBmdW5jdGlvbiAoYikge1xyXG4gICAgdmFyIHN0cixcclxuICAgICAgbiA9IHRoaXMsXHJcbiAgICAgIHMgPSBuLnMsXHJcbiAgICAgIGUgPSBuLmU7XHJcblxyXG4gICAgLy8gSW5maW5pdHkgb3IgTmFOP1xyXG4gICAgaWYgKGUgPT09IG51bGwpIHtcclxuICAgICAgaWYgKHMpIHtcclxuICAgICAgICBzdHIgPSAnSW5maW5pdHknO1xyXG4gICAgICAgIGlmIChzIDwgMCkgc3RyID0gJy0nICsgc3RyO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHN0ciA9ICdOYU4nO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpZiAoYiA9PSBudWxsKSB7XHJcbiAgICAgICAgc3RyID0gZSA8PSBUT19FWFBfTkVHIHx8IGUgPj0gVE9fRVhQX1BPU1xyXG4gICAgICAgICA/IHRvRXhwb25lbnRpYWwoY29lZmZUb1N0cmluZyhuLmMpLCBlKVxyXG4gICAgICAgICA6IHRvRml4ZWRQb2ludChjb2VmZlRvU3RyaW5nKG4uYyksIGUsICcwJyk7XHJcbiAgICAgIH0gZWxzZSBpZiAoYiA9PT0gMTAgJiYgYWxwaGFiZXRIYXNOb3JtYWxEZWNpbWFsRGlnaXRzKSB7XHJcbiAgICAgICAgbiA9IHJvdW5kKG5ldyBCaWdOdW1iZXIobiksIERFQ0lNQUxfUExBQ0VTICsgZSArIDEsIFJPVU5ESU5HX01PREUpO1xyXG4gICAgICAgIHN0ciA9IHRvRml4ZWRQb2ludChjb2VmZlRvU3RyaW5nKG4uYyksIG4uZSwgJzAnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBpbnRDaGVjayhiLCAyLCBBTFBIQUJFVC5sZW5ndGgsICdCYXNlJyk7XHJcbiAgICAgICAgc3RyID0gY29udmVydEJhc2UodG9GaXhlZFBvaW50KGNvZWZmVG9TdHJpbmcobi5jKSwgZSwgJzAnKSwgMTAsIGIsIHMsIHRydWUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAocyA8IDAgJiYgbi5jWzBdKSBzdHIgPSAnLScgKyBzdHI7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHN0cjtcclxuICB9O1xyXG5cclxuXHJcbiAgLypcclxuICAgKiBSZXR1cm4gYXMgdG9TdHJpbmcsIGJ1dCBkbyBub3QgYWNjZXB0IGEgYmFzZSBhcmd1bWVudCwgYW5kIGluY2x1ZGUgdGhlIG1pbnVzIHNpZ24gZm9yXHJcbiAgICogbmVnYXRpdmUgemVyby5cclxuICAgKi9cclxuICBQLnZhbHVlT2YgPSBQLnRvSlNPTiA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHJldHVybiB2YWx1ZU9mKHRoaXMpO1xyXG4gIH07XHJcblxyXG5cclxuICBQLl9pc0JpZ051bWJlciA9IHRydWU7XHJcblxyXG4gIFBbU3ltYm9sLnRvU3RyaW5nVGFnXSA9ICdCaWdOdW1iZXInO1xyXG5cclxuICAvLyBOb2RlLmpzIHYxMC4xMi4wK1xyXG4gIFBbU3ltYm9sLmZvcignbm9kZWpzLnV0aWwuaW5zcGVjdC5jdXN0b20nKV0gPSBQLnZhbHVlT2Y7XHJcblxyXG4gIGlmIChjb25maWdPYmplY3QgIT0gbnVsbCkgQmlnTnVtYmVyLnNldChjb25maWdPYmplY3QpO1xyXG5cclxuICByZXR1cm4gQmlnTnVtYmVyO1xyXG59XHJcblxyXG5cclxuLy8gUFJJVkFURSBIRUxQRVIgRlVOQ1RJT05TXHJcblxyXG4vLyBUaGVzZSBmdW5jdGlvbnMgZG9uJ3QgbmVlZCBhY2Nlc3MgdG8gdmFyaWFibGVzLFxyXG4vLyBlLmcuIERFQ0lNQUxfUExBQ0VTLCBpbiB0aGUgc2NvcGUgb2YgdGhlIGBjbG9uZWAgZnVuY3Rpb24gYWJvdmUuXHJcblxyXG5cclxuZnVuY3Rpb24gYml0Rmxvb3Iobikge1xyXG4gIHZhciBpID0gbiB8IDA7XHJcbiAgcmV0dXJuIG4gPiAwIHx8IG4gPT09IGkgPyBpIDogaSAtIDE7XHJcbn1cclxuXHJcblxyXG4vLyBSZXR1cm4gYSBjb2VmZmljaWVudCBhcnJheSBhcyBhIHN0cmluZyBvZiBiYXNlIDEwIGRpZ2l0cy5cclxuZnVuY3Rpb24gY29lZmZUb1N0cmluZyhhKSB7XHJcbiAgdmFyIHMsIHosXHJcbiAgICBpID0gMSxcclxuICAgIGogPSBhLmxlbmd0aCxcclxuICAgIHIgPSBhWzBdICsgJyc7XHJcblxyXG4gIGZvciAoOyBpIDwgajspIHtcclxuICAgIHMgPSBhW2krK10gKyAnJztcclxuICAgIHogPSBMT0dfQkFTRSAtIHMubGVuZ3RoO1xyXG4gICAgZm9yICg7IHotLTsgcyA9ICcwJyArIHMpO1xyXG4gICAgciArPSBzO1xyXG4gIH1cclxuXHJcbiAgLy8gRGV0ZXJtaW5lIHRyYWlsaW5nIHplcm9zLlxyXG4gIGZvciAoaiA9IHIubGVuZ3RoOyByLmNoYXJDb2RlQXQoLS1qKSA9PT0gNDg7KTtcclxuXHJcbiAgcmV0dXJuIHIuc2xpY2UoMCwgaiArIDEgfHwgMSk7XHJcbn1cclxuXHJcblxyXG4vLyBDb21wYXJlIHRoZSB2YWx1ZSBvZiBCaWdOdW1iZXJzIHggYW5kIHkuXHJcbmZ1bmN0aW9uIGNvbXBhcmUoeCwgeSkge1xyXG4gIHZhciBhLCBiLFxyXG4gICAgeGMgPSB4LmMsXHJcbiAgICB5YyA9IHkuYyxcclxuICAgIGkgPSB4LnMsXHJcbiAgICBqID0geS5zLFxyXG4gICAgayA9IHguZSxcclxuICAgIGwgPSB5LmU7XHJcblxyXG4gIC8vIEVpdGhlciBOYU4/XHJcbiAgaWYgKCFpIHx8ICFqKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgYSA9IHhjICYmICF4Y1swXTtcclxuICBiID0geWMgJiYgIXljWzBdO1xyXG5cclxuICAvLyBFaXRoZXIgemVybz9cclxuICBpZiAoYSB8fCBiKSByZXR1cm4gYSA/IGIgPyAwIDogLWogOiBpO1xyXG5cclxuICAvLyBTaWducyBkaWZmZXI/XHJcbiAgaWYgKGkgIT0gaikgcmV0dXJuIGk7XHJcblxyXG4gIGEgPSBpIDwgMDtcclxuICBiID0gayA9PSBsO1xyXG5cclxuICAvLyBFaXRoZXIgSW5maW5pdHk/XHJcbiAgaWYgKCF4YyB8fCAheWMpIHJldHVybiBiID8gMCA6ICF4YyBeIGEgPyAxIDogLTE7XHJcblxyXG4gIC8vIENvbXBhcmUgZXhwb25lbnRzLlxyXG4gIGlmICghYikgcmV0dXJuIGsgPiBsIF4gYSA/IDEgOiAtMTtcclxuXHJcbiAgaiA9IChrID0geGMubGVuZ3RoKSA8IChsID0geWMubGVuZ3RoKSA/IGsgOiBsO1xyXG5cclxuICAvLyBDb21wYXJlIGRpZ2l0IGJ5IGRpZ2l0LlxyXG4gIGZvciAoaSA9IDA7IGkgPCBqOyBpKyspIGlmICh4Y1tpXSAhPSB5Y1tpXSkgcmV0dXJuIHhjW2ldID4geWNbaV0gXiBhID8gMSA6IC0xO1xyXG5cclxuICAvLyBDb21wYXJlIGxlbmd0aHMuXHJcbiAgcmV0dXJuIGsgPT0gbCA/IDAgOiBrID4gbCBeIGEgPyAxIDogLTE7XHJcbn1cclxuXHJcblxyXG4vKlxyXG4gKiBDaGVjayB0aGF0IG4gaXMgYSBwcmltaXRpdmUgbnVtYmVyLCBhbiBpbnRlZ2VyLCBhbmQgaW4gcmFuZ2UsIG90aGVyd2lzZSB0aHJvdy5cclxuICovXHJcbmZ1bmN0aW9uIGludENoZWNrKG4sIG1pbiwgbWF4LCBuYW1lKSB7XHJcbiAgaWYgKG4gPCBtaW4gfHwgbiA+IG1heCB8fCBuICE9PSBtYXRoZmxvb3IobikpIHtcclxuICAgIHRocm93IEVycm9yXHJcbiAgICAgKGJpZ251bWJlckVycm9yICsgKG5hbWUgfHwgJ0FyZ3VtZW50JykgKyAodHlwZW9mIG4gPT0gJ251bWJlcidcclxuICAgICAgID8gbiA8IG1pbiB8fCBuID4gbWF4ID8gJyBvdXQgb2YgcmFuZ2U6ICcgOiAnIG5vdCBhbiBpbnRlZ2VyOiAnXHJcbiAgICAgICA6ICcgbm90IGEgcHJpbWl0aXZlIG51bWJlcjogJykgKyBTdHJpbmcobikpO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbi8vIEFzc3VtZXMgZmluaXRlIG4uXHJcbmZ1bmN0aW9uIGlzT2RkKG4pIHtcclxuICB2YXIgayA9IG4uYy5sZW5ndGggLSAxO1xyXG4gIHJldHVybiBiaXRGbG9vcihuLmUgLyBMT0dfQkFTRSkgPT0gayAmJiBuLmNba10gJSAyICE9IDA7XHJcbn1cclxuXHJcblxyXG5mdW5jdGlvbiB0b0V4cG9uZW50aWFsKHN0ciwgZSkge1xyXG4gIHJldHVybiAoc3RyLmxlbmd0aCA+IDEgPyBzdHIuY2hhckF0KDApICsgJy4nICsgc3RyLnNsaWNlKDEpIDogc3RyKSArXHJcbiAgIChlIDwgMCA/ICdlJyA6ICdlKycpICsgZTtcclxufVxyXG5cclxuXHJcbmZ1bmN0aW9uIHRvRml4ZWRQb2ludChzdHIsIGUsIHopIHtcclxuICB2YXIgbGVuLCB6cztcclxuXHJcbiAgLy8gTmVnYXRpdmUgZXhwb25lbnQ/XHJcbiAgaWYgKGUgPCAwKSB7XHJcblxyXG4gICAgLy8gUHJlcGVuZCB6ZXJvcy5cclxuICAgIGZvciAoenMgPSB6ICsgJy4nOyArK2U7IHpzICs9IHopO1xyXG4gICAgc3RyID0genMgKyBzdHI7XHJcblxyXG4gIC8vIFBvc2l0aXZlIGV4cG9uZW50XHJcbiAgfSBlbHNlIHtcclxuICAgIGxlbiA9IHN0ci5sZW5ndGg7XHJcblxyXG4gICAgLy8gQXBwZW5kIHplcm9zLlxyXG4gICAgaWYgKCsrZSA+IGxlbikge1xyXG4gICAgICBmb3IgKHpzID0geiwgZSAtPSBsZW47IC0tZTsgenMgKz0geik7XHJcbiAgICAgIHN0ciArPSB6cztcclxuICAgIH0gZWxzZSBpZiAoZSA8IGxlbikge1xyXG4gICAgICBzdHIgPSBzdHIuc2xpY2UoMCwgZSkgKyAnLicgKyBzdHIuc2xpY2UoZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gc3RyO1xyXG59XHJcblxyXG5cclxuLy8gRVhQT1JUXHJcblxyXG5cclxuZXhwb3J0IHZhciBCaWdOdW1iZXIgPSBjbG9uZSgpO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQmlnTnVtYmVyO1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bignumber.js/bignumber.mjs\n");

/***/ })

};
;