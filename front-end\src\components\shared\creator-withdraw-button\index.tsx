import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Wallet, InfoIcon } from 'lucide-react';
import { useTranslation } from '../../../hooks/useTranslation';

interface CreatorWithdrawButtonProps {
  tokenData: {
    creatorWallet: string;
    [key: string]: any;
  };
  currentUserWallet?: string;
  onWithdraw?: () => void;
}

const CreatorWithdrawButton: React.FC<CreatorWithdrawButtonProps> = ({
  tokenData,
  currentUserWallet,
  onWithdraw,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const { t } = useTranslation();

  const isCreator =
    currentUserWallet &&
    tokenData.creatorWallet &&
    currentUserWallet.toLowerCase() === tokenData.creatorWallet.toLowerCase();

  if (!isCreator) {
    return null;
  }

  const handleWithdraw = () => {
    if (onWithdraw) {
      onWithdraw();
    }
  };

  return (
    <div className="relative">
      <motion.button
        onClick={handleWithdraw}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6600] to-[#e55a00] text-white font-medium rounded-lg hover:from-[#e55a00] hover:to-[#cc5200] transition-all duration-200 shadow-sm hover:shadow-md border border-transparent"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      >
        <Wallet size={18} />
        <span>{t('creatorWithdraw.button')}</span>
        <InfoIcon size={16} className="opacity-70" />
      </motion.button>

      {/* Tooltip */}
      {showTooltip && (
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 10, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
        >
          <div className="bg-gray-900 text-white text-sm rounded-lg px-4 py-3 max-w-xs shadow-lg">
            <div className="text-center">
              <p className="font-medium mb-1">{t('creatorWithdraw.tooltipTitle')}</p>
              <p className="text-gray-300 text-xs leading-relaxed">
                {t('creatorWithdraw.tooltipDescription')}
              </p>
            </div>
            {/* Tooltip arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900"></div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CreatorWithdrawButton;
