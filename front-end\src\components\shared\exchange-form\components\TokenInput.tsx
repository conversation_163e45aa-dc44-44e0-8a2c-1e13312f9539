"use client";

import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import React, { useState, useRef, useEffect } from "react";

import { PLACEHOLDER_IMAGES } from "@/components/shared/images";
import { fadeInUp } from "@/lib/animations";
import type { TokenInputProps, TokenOption } from "../types";
import { useTranslation } from '../../../../hooks/useTranslation';

const solPrice = 156;
const coinPrice = 0.0000045515;

const TokenInput: React.FC<TokenInputProps> = ({
  label,
  token,
  amount,
  setAmount,
  onTokenChange,
  tokenOptions,
  badge,
  showSymbolInAvailable = false,
  loading = false,
}) => {
  const { t } = useTranslation();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleTokenSelect = (selectedToken: TokenOption) => {
    onTokenChange(selectedToken);
    setDropdownOpen(false);
  };

  return (
    <motion.div
      className="w-full h-[160px] bg-[#FFF7F1] rounded-[20px] p-[18px]"
      {...fadeInUp}
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className="flex items-center mb-3"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1 }}
      >
        <p className="font-['IBM_Plex_Sans'] font-semibold text-[14px] leading-[24px] text-[#090A0B]">
          {label}
        </p>
        {label === "From" ? (
          <motion.div
            className="ml-2 border-2 border-[#8BB7A2] rounded-[10px] px-3 py-[2px] bg-white/50"
            whileHover={{ scale: 1.05 }}
          >
            <span className="font-['IBM_Plex_Sans'] font-medium text-xs leading-none text-[#8BB7A2]">
              {token ? token.symbol : "SYMBOL"}
            </span>
          </motion.div>
        ) : (
          <motion.div
            className="ml-2 border-2 border-[#E7D4A3] rounded-[10px] px-3 py-[2px] bg-white/50"
            whileHover={{ scale: 1.05 }}
          >
            <span className="font-['IBM_Plex_Sans'] font-medium text-sm leading-none text-[#A68952]">
              {token ? token.symbol : "SYMBOL"}
            </span>
          </motion.div>
        )}
        {badge && badge !== "Fun hi" && (
          <motion.span
            className="ml-2 px-2 py-1 bg-[#F2F4F7] text-xs rounded-full text-[#667085]"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            {badge}
          </motion.span>
        )}
      </motion.div>

      <motion.div
        className="flex items-center justify-between mb-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex items-center relative" ref={dropdownRef}>
          <motion.div
            className="flex items-center cursor-pointer border border-transparent hover:border-gray-300 px-2 py-1 rounded-md transition-all duration-300"
            onClick={() => !loading && setDropdownOpen(!dropdownOpen)}
            whileHover={!loading ? { scale: 1.02 } : {}}
            whileTap={!loading ? { scale: 0.98 } : {}}
          >
            <motion.div
              className="w-8 h-8 rounded-full bg-gray-200 mr-2 overflow-hidden flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
            >
              <Image
                src={token.icon || PLACEHOLDER_IMAGES.tokenIcon}
                alt={token.name}
                width={40}
                height={40}
              />
            </motion.div>
            <span className="font-['IBM_Plex_Sans'] font-semibold text-[18px] leading-[28px] text-[#090A0B]">
              {token.symbol}
            </span>
            <motion.div
              animate={{ rotate: dropdownOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown size={20} className="ml-1 text-gray-500" />
            </motion.div>
          </motion.div>

          <AnimatePresence>
            {dropdownOpen && (
              <motion.div
                className="absolute top-full left-0 mt-1 w-56 max-h-[300px] overflow-y-auto bg-white rounded-lg shadow-xl z-50 border border-gray-200"
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                {tokenOptions.map((option, index) => (
                  <motion.div
                    key={index}
                    className={`flex items-center p-3 hover:bg-gray-100 cursor-pointer transition-colors ${
                      option.symbol === token.symbol ? "bg-gray-50" : ""
                    }`}
                    onClick={() => handleTokenSelect(option)}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.02, backgroundColor: "#f3f4f6" }}
                  >
                    <div className="w-6 h-6 rounded-full bg-gray-200 mr-2 overflow-hidden flex items-center justify-center">
                      <Image
                        src={option.icon || PLACEHOLDER_IMAGES.tokenIcon}
                        alt={option.name}
                        width={24}
                        height={24}
                      />
                    </div>
                    <div className="flex flex-col">
                      <span className="font-['IBM_Plex_Sans'] font-semibold text-sm text-[#090A0B]">
                        {option.symbol}
                      </span>
                      <span className="font-['IBM_Plex_Sans'] text-xs text-gray-500">
                        {option.name}
                      </span>
                    </div>
                    <div className="ml-auto text-xs text-gray-500">
                      {option.balance}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <motion.input
          type="text"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          className="bg-transparent text-right font-['IBM_Plex_Sans'] font-semibold text-[24px] focus:outline-none w-full text-[#090A0B] focus:ring-2 focus:ring-[#F58A38] rounded px-2 transition-all duration-300"
          placeholder="0.00"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={loading}
          whileFocus={{ scale: 1.02 }}
        />
      </motion.div>

      <motion.div
        className="flex justify-between"
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <span
          className="font-['IBM_Plex_Sans'] font-semibold text-[14px] leading-[24px] text-[#788691] cursor-pointer"
          onClick={() => !loading && setAmount(token.balance.toString())}
        >
          {t('exchangeForm.available')} {" "}
          <span className="text-[#090A0B]">
            {loading ? (
              <motion.span
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                {t('exchangeForm.loading')}
              </motion.span>
            ) : (
              `${token.balance} ${
                showSymbolInAvailable ? token.symbol : token.symbol
              }`
            )}
          </span>
        </span>
        <span className="font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[24px] text-[#788691]">
          ≈$
          {(() => {
            const amt = parseFloat(amount || "0");
            if (isNaN(amt)) return "0.00";
            const rate = token.symbol === "SOL" ? solPrice : coinPrice;
            return (amt * rate).toFixed(4);
          })()}
        </span>
      </motion.div>
    </motion.div>
  );
};

export default TokenInput; 