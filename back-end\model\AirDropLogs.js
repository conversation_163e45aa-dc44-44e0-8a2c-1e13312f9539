const { Model, DataTypes } = require("sequelize");

class AirDropLogs extends Model {
  static initModel(sequelize) {
    return AirDropLogs.init(
      {
        id: {
          type: DataTypes.BIGINT,
          autoIncrement: true,
          primaryKey: true,
        },
        airdropID: {
          type: DataTypes.BIGINT,
          allowNull: false,
        },
        wallet: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        signature: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        tokensPerLink: {
          type: DataTypes.BIGINT,
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          defaultValue: DataTypes.NOW,
        },
      },
      {
        sequelize,
        tableName: "airdrop_logs",
        timestamps: false,
      }
    );
  }
}

module.exports = AirDropLogs;
