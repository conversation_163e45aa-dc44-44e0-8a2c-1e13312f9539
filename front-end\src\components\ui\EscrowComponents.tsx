"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, AlertCircle, CheckCircle, Clock, RefreshCw, X } from 'lucide-react';
import { EscrowOperationType, EscrowOperationStatus } from '@/utils/escrow';

// Loading Button Component
interface EscrowLoadingButtonProps {
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  loadingText?: string;
  icon?: React.ReactNode;
}

export const EscrowLoadingButton: React.FC<EscrowLoadingButtonProps> = ({
  onClick,
  disabled = false,
  loading = false,
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  loadingText,
  icon
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      whileHover={!disabled && !loading ? { scale: 1.02 } : {}}
      whileTap={!disabled && !loading ? { scale: 0.98 } : {}}
    >
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>{loadingText || 'Processing...'}</span>
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            {icon && <span className="w-4 h-4">{icon}</span>}
            <span>{children}</span>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.button>
  );
};

// Operation Status Indicator
interface EscrowOperationStatusProps {
  operation: EscrowOperationType;
  status: EscrowOperationStatus;
  error?: Error | null;
  txId?: string | null;
  className?: string;
  showDetails?: boolean;
}

export const EscrowOperationStatus: React.FC<EscrowOperationStatusProps> = ({
  operation,
  status,
  error,
  txId,
  className = '',
  showDetails = true
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          label: 'Processing'
        };
      case 'success':
        return {
          icon: <CheckCircle className="w-4 h-4" />,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          label: 'Completed'
        };
      case 'error':
        return {
          icon: <AlertCircle className="w-4 h-4" />,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          label: 'Failed'
        };
      default:
        return {
          icon: <Clock className="w-4 h-4" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          label: 'Idle'
        };
    }
  };

  const config = getStatusConfig();
  const operationLabels = {
    buy: 'Purchase',
    sell: 'Release',
    refund: 'Refund',
    dispute: 'Dispute'
  };

  if (status === 'idle') return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`p-3 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}
    >
      <div className="flex items-center gap-2">
        <span className={config.color}>{config.icon}</span>
        <span className={`font-medium ${config.color}`}>
          {operationLabels[operation]} {config.label}
        </span>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-sm text-gray-600">
          {status === 'success' && txId && (
            <div className="flex items-center gap-1">
              <span>Transaction ID:</span>
              <code className="bg-gray-100 px-1 rounded text-xs font-mono">
                {txId.length > 20 ? `${txId.slice(0, 8)}...${txId.slice(-8)}` : txId}
              </code>
            </div>
          )}
          
          {status === 'error' && error && (
            <div className="text-red-600">
              <span className="font-medium">Error:</span> {error.message}
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

// Error Display Component
interface EscrowErrorDisplayProps {
  error: Error;
  operation: EscrowOperationType;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export const EscrowErrorDisplay: React.FC<EscrowErrorDisplayProps> = ({
  error,
  operation,
  onRetry,
  onDismiss,
  className = ''
}) => {
  const operationLabels = {
    buy: 'Purchase',
    sell: 'Release',
    refund: 'Refund',
    dispute: 'Dispute'
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="font-medium text-red-800">
            {operationLabels[operation]} Failed
          </h4>
          <p className="text-red-700 text-sm mt-1">{error.message}</p>
        </div>
        
        <div className="flex items-center gap-2">
          {onRetry && (
            <button
              onClick={onRetry}
              className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
              title="Retry operation"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          )}
          
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
              title="Dismiss error"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Operation Progress Indicator
interface EscrowOperationProgressProps {
  operations: {
    operation: EscrowOperationType;
    status: EscrowOperationStatus;
    label: string;
  }[];
  className?: string;
}

export const EscrowOperationProgress: React.FC<EscrowOperationProgressProps> = ({
  operations,
  className = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {operations.map(({ operation, status, label }) => (
        <div key={operation} className="flex items-center gap-3">
          <div className="flex-shrink-0">
            {status === 'pending' && <Loader2 className="w-4 h-4 animate-spin text-blue-600" />}
            {status === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
            {status === 'error' && <AlertCircle className="w-4 h-4 text-red-600" />}
            {status === 'idle' && <Clock className="w-4 h-4 text-gray-400" />}
          </div>
          
          <span className={`text-sm ${
            status === 'success' ? 'text-green-700' :
            status === 'error' ? 'text-red-700' :
            status === 'pending' ? 'text-blue-700' :
            'text-gray-500'
          }`}>
            {label}
          </span>
        </div>
      ))}
    </div>
  );
};

// Escrow Action Card Component
interface EscrowActionCardProps {
  title: string;
  description: string;
  action: React.ReactNode;
  status?: EscrowOperationStatus;
  error?: Error | null;
  className?: string;
  disabled?: boolean;
}

export const EscrowActionCard: React.FC<EscrowActionCardProps> = ({
  title,
  description,
  action,
  status = 'idle',
  error,
  className = '',
  disabled = false
}) => {
  return (
    <motion.div
      className={`bg-white border border-gray-200 rounded-lg p-4 ${disabled ? 'opacity-50' : ''} ${className}`}
      whileHover={!disabled ? { y: -2 } : {}}
      transition={{ duration: 0.2 }}
    >
      <div className="space-y-3">
        <div>
          <h3 className="font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        </div>
        
        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
            {error.message}
          </div>
        )}
        
        <div className="flex items-center justify-between">
          {action}
          
          {status === 'pending' && (
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Processing...</span>
            </div>
          )}
          
          {status === 'success' && (
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Completed</span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};
