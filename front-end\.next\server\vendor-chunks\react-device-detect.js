"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-device-detect";
exports.ids = ["vendor-chunks/react-device-detect"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-device-detect/dist/lib.js":
/*!******************************************************!*\
  !*** ./node_modules/react-device-detect/dist/lib.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar React__default = _interopDefault(React);\n\nvar UAParser = __webpack_require__(/*! ua-parser-js/dist/ua-parser.min */ \"(ssr)/./node_modules/ua-parser-js/dist/ua-parser.min.js\");\n\nvar ClientUAInstance = new UAParser();\nvar browser = ClientUAInstance.getBrowser();\nvar cpu = ClientUAInstance.getCPU();\nvar device = ClientUAInstance.getDevice();\nvar engine = ClientUAInstance.getEngine();\nvar os = ClientUAInstance.getOS();\nvar ua = ClientUAInstance.getUA();\nvar setUa = function setUa(userAgentString) {\n  return ClientUAInstance.setUA(userAgentString);\n};\nvar parseUserAgent = function parseUserAgent(userAgent) {\n  if (!userAgent) {\n    console.error('No userAgent string was provided');\n    return;\n  }\n\n  var UserAgentInstance = new UAParser(userAgent);\n  return {\n    UA: UserAgentInstance,\n    browser: UserAgentInstance.getBrowser(),\n    cpu: UserAgentInstance.getCPU(),\n    device: UserAgentInstance.getDevice(),\n    engine: UserAgentInstance.getEngine(),\n    os: UserAgentInstance.getOS(),\n    ua: UserAgentInstance.getUA(),\n    setUserAgent: function setUserAgent(userAgentString) {\n      return UserAgentInstance.setUA(userAgentString);\n    }\n  };\n};\n\nvar UAHelper = /*#__PURE__*/Object.freeze({\n  ClientUAInstance: ClientUAInstance,\n  browser: browser,\n  cpu: cpu,\n  device: device,\n  engine: engine,\n  os: os,\n  ua: ua,\n  setUa: setUa,\n  parseUserAgent: parseUserAgent\n});\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar DeviceTypes = {\n  Mobile: 'mobile',\n  Tablet: 'tablet',\n  SmartTv: 'smarttv',\n  Console: 'console',\n  Wearable: 'wearable',\n  Embedded: 'embedded',\n  Browser: undefined\n};\nvar BrowserTypes = {\n  Chrome: 'Chrome',\n  Firefox: 'Firefox',\n  Opera: 'Opera',\n  Yandex: 'Yandex',\n  Safari: 'Safari',\n  InternetExplorer: 'Internet Explorer',\n  Edge: 'Edge',\n  Chromium: 'Chromium',\n  Ie: 'IE',\n  MobileSafari: 'Mobile Safari',\n  EdgeChromium: 'Edge Chromium',\n  MIUI: 'MIUI Browser',\n  SamsungBrowser: 'Samsung Browser'\n};\nvar OsTypes = {\n  IOS: 'iOS',\n  Android: 'Android',\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MAC_OS: 'Mac OS'\n};\nvar InitialDeviceTypes = {\n  isMobile: false,\n  isTablet: false,\n  isBrowser: false,\n  isSmartTV: false,\n  isConsole: false,\n  isWearable: false\n};\n\nvar checkDeviceType = function checkDeviceType(type) {\n  switch (type) {\n    case DeviceTypes.Mobile:\n      return {\n        isMobile: true\n      };\n\n    case DeviceTypes.Tablet:\n      return {\n        isTablet: true\n      };\n\n    case DeviceTypes.SmartTv:\n      return {\n        isSmartTV: true\n      };\n\n    case DeviceTypes.Console:\n      return {\n        isConsole: true\n      };\n\n    case DeviceTypes.Wearable:\n      return {\n        isWearable: true\n      };\n\n    case DeviceTypes.Browser:\n      return {\n        isBrowser: true\n      };\n\n    case DeviceTypes.Embedded:\n      return {\n        isEmbedded: true\n      };\n\n    default:\n      return InitialDeviceTypes;\n  }\n};\nvar setUserAgent = function setUserAgent(userAgent) {\n  return setUa(userAgent);\n};\nvar setDefaults = function setDefaults(p) {\n  var d = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';\n  return p ? p : d;\n};\nvar getNavigatorInstance = function getNavigatorInstance() {\n  if (typeof window !== 'undefined') {\n    if (window.navigator || navigator) {\n      return window.navigator || navigator;\n    }\n  }\n\n  return false;\n};\nvar isIOS13Check = function isIOS13Check(type) {\n  var nav = getNavigatorInstance();\n  return nav && nav.platform && (nav.platform.indexOf(type) !== -1 || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1 && !window.MSStream);\n};\n\nvar browserPayload = function browserPayload(isBrowser, browser, engine, os, ua) {\n  return {\n    isBrowser: isBrowser,\n    browserMajorVersion: setDefaults(browser.major),\n    browserFullVersion: setDefaults(browser.version),\n    browserName: setDefaults(browser.name),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar mobilePayload = function mobilePayload(type, device, os, ua) {\n  return _objectSpread2({}, type, {\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    os: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    ua: setDefaults(ua)\n  });\n};\nvar smartTvPayload = function smartTvPayload(isSmartTV, engine, os, ua) {\n  return {\n    isSmartTV: isSmartTV,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar consolePayload = function consolePayload(isConsole, engine, os, ua) {\n  return {\n    isConsole: isConsole,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar wearablePayload = function wearablePayload(isWearable, engine, os, ua) {\n  return {\n    isWearable: isWearable,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar embeddedPayload = function embeddedPayload(isEmbedded, device, engine, os, ua) {\n  return {\n    isEmbedded: isEmbedded,\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\n\nfunction deviceDetect(userAgent) {\n  var _ref = userAgent ? parseUserAgent(userAgent) : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      engine = _ref.engine,\n      os = _ref.os,\n      ua = _ref.ua;\n\n  var type = checkDeviceType(device.type);\n  var isBrowser = type.isBrowser,\n      isMobile = type.isMobile,\n      isTablet = type.isTablet,\n      isSmartTV = type.isSmartTV,\n      isConsole = type.isConsole,\n      isWearable = type.isWearable,\n      isEmbedded = type.isEmbedded;\n\n  if (isBrowser) {\n    return browserPayload(isBrowser, browser, engine, os, ua);\n  }\n\n  if (isSmartTV) {\n    return smartTvPayload(isSmartTV, engine, os, ua);\n  }\n\n  if (isConsole) {\n    return consolePayload(isConsole, engine, os, ua);\n  }\n\n  if (isMobile) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isTablet) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isWearable) {\n    return wearablePayload(isWearable, engine, os, ua);\n  }\n\n  if (isEmbedded) {\n    return embeddedPayload(isEmbedded, device, engine, os, ua);\n  }\n}\n\nvar isMobileType = function isMobileType(_ref) {\n  var type = _ref.type;\n  return type === DeviceTypes.Mobile;\n};\nvar isTabletType = function isTabletType(_ref2) {\n  var type = _ref2.type;\n  return type === DeviceTypes.Tablet;\n};\nvar isMobileAndTabletType = function isMobileAndTabletType(_ref3) {\n  var type = _ref3.type;\n  return type === DeviceTypes.Mobile || type === DeviceTypes.Tablet;\n};\nvar isSmartTVType = function isSmartTVType(_ref4) {\n  var type = _ref4.type;\n  return type === DeviceTypes.SmartTv;\n};\nvar isBrowserType = function isBrowserType(_ref5) {\n  var type = _ref5.type;\n  return type === DeviceTypes.Browser;\n};\nvar isWearableType = function isWearableType(_ref6) {\n  var type = _ref6.type;\n  return type === DeviceTypes.Wearable;\n};\nvar isConsoleType = function isConsoleType(_ref7) {\n  var type = _ref7.type;\n  return type === DeviceTypes.Console;\n};\nvar isEmbeddedType = function isEmbeddedType(_ref8) {\n  var type = _ref8.type;\n  return type === DeviceTypes.Embedded;\n};\nvar getMobileVendor = function getMobileVendor(_ref9) {\n  var vendor = _ref9.vendor;\n  return setDefaults(vendor);\n};\nvar getMobileModel = function getMobileModel(_ref10) {\n  var model = _ref10.model;\n  return setDefaults(model);\n};\nvar getDeviceType = function getDeviceType(_ref11) {\n  var type = _ref11.type;\n  return setDefaults(type, 'browser');\n}; // os types\n\nvar isAndroidType = function isAndroidType(_ref12) {\n  var name = _ref12.name;\n  return name === OsTypes.Android;\n};\nvar isWindowsType = function isWindowsType(_ref13) {\n  var name = _ref13.name;\n  return name === OsTypes.Windows;\n};\nvar isMacOsType = function isMacOsType(_ref14) {\n  var name = _ref14.name;\n  return name === OsTypes.MAC_OS;\n};\nvar isWinPhoneType = function isWinPhoneType(_ref15) {\n  var name = _ref15.name;\n  return name === OsTypes.WindowsPhone;\n};\nvar isIOSType = function isIOSType(_ref16) {\n  var name = _ref16.name;\n  return name === OsTypes.IOS;\n};\nvar getOsVersion = function getOsVersion(_ref17) {\n  var version = _ref17.version;\n  return setDefaults(version);\n};\nvar getOsName = function getOsName(_ref18) {\n  var name = _ref18.name;\n  return setDefaults(name);\n}; // browser types\n\nvar isChromeType = function isChromeType(_ref19) {\n  var name = _ref19.name;\n  return name === BrowserTypes.Chrome;\n};\nvar isFirefoxType = function isFirefoxType(_ref20) {\n  var name = _ref20.name;\n  return name === BrowserTypes.Firefox;\n};\nvar isChromiumType = function isChromiumType(_ref21) {\n  var name = _ref21.name;\n  return name === BrowserTypes.Chromium;\n};\nvar isEdgeType = function isEdgeType(_ref22) {\n  var name = _ref22.name;\n  return name === BrowserTypes.Edge;\n};\nvar isYandexType = function isYandexType(_ref23) {\n  var name = _ref23.name;\n  return name === BrowserTypes.Yandex;\n};\nvar isSafariType = function isSafariType(_ref24) {\n  var name = _ref24.name;\n  return name === BrowserTypes.Safari || name === BrowserTypes.MobileSafari;\n};\nvar isMobileSafariType = function isMobileSafariType(_ref25) {\n  var name = _ref25.name;\n  return name === BrowserTypes.MobileSafari;\n};\nvar isOperaType = function isOperaType(_ref26) {\n  var name = _ref26.name;\n  return name === BrowserTypes.Opera;\n};\nvar isIEType = function isIEType(_ref27) {\n  var name = _ref27.name;\n  return name === BrowserTypes.InternetExplorer || name === BrowserTypes.Ie;\n};\nvar isMIUIType = function isMIUIType(_ref28) {\n  var name = _ref28.name;\n  return name === BrowserTypes.MIUI;\n};\nvar isSamsungBrowserType = function isSamsungBrowserType(_ref29) {\n  var name = _ref29.name;\n  return name === BrowserTypes.SamsungBrowser;\n};\nvar getBrowserFullVersion = function getBrowserFullVersion(_ref30) {\n  var version = _ref30.version;\n  return setDefaults(version);\n};\nvar getBrowserVersion = function getBrowserVersion(_ref31) {\n  var major = _ref31.major;\n  return setDefaults(major);\n};\nvar getBrowserName = function getBrowserName(_ref32) {\n  var name = _ref32.name;\n  return setDefaults(name);\n}; // engine types\n\nvar getEngineName = function getEngineName(_ref33) {\n  var name = _ref33.name;\n  return setDefaults(name);\n};\nvar getEngineVersion = function getEngineVersion(_ref34) {\n  var version = _ref34.version;\n  return setDefaults(version);\n};\nvar isElectronType = function isElectronType() {\n  var nav = getNavigatorInstance();\n  var ua = nav && nav.userAgent && nav.userAgent.toLowerCase();\n  return typeof ua === 'string' ? /electron/.test(ua) : false;\n};\nvar isEdgeChromiumType = function isEdgeChromiumType(ua) {\n  return typeof ua === 'string' && ua.indexOf('Edg/') !== -1;\n};\nvar getIOS13 = function getIOS13() {\n  var nav = getNavigatorInstance();\n  return nav && (/iPad|iPhone|iPod/.test(nav.platform) || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1) && !window.MSStream;\n};\nvar getIPad13 = function getIPad13() {\n  return isIOS13Check('iPad');\n};\nvar getIphone13 = function getIphone13() {\n  return isIOS13Check('iPhone');\n};\nvar getIPod13 = function getIPod13() {\n  return isIOS13Check('iPod');\n};\nvar getUseragent = function getUseragent(userAg) {\n  return setDefaults(userAg);\n};\n\nfunction buildSelectorsObject(options) {\n  var _ref = options ? options : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      os = _ref.os,\n      engine = _ref.engine,\n      ua = _ref.ua;\n\n  return {\n    isSmartTV: isSmartTVType(device),\n    isConsole: isConsoleType(device),\n    isWearable: isWearableType(device),\n    isEmbedded: isEmbeddedType(device),\n    isMobileSafari: isMobileSafariType(browser) || getIPad13(),\n    isChromium: isChromiumType(browser),\n    isMobile: isMobileAndTabletType(device) || getIPad13(),\n    isMobileOnly: isMobileType(device),\n    isTablet: isTabletType(device) || getIPad13(),\n    isBrowser: isBrowserType(device),\n    isDesktop: isBrowserType(device),\n    isAndroid: isAndroidType(os),\n    isWinPhone: isWinPhoneType(os),\n    isIOS: isIOSType(os) || getIPad13(),\n    isChrome: isChromeType(browser),\n    isFirefox: isFirefoxType(browser),\n    isSafari: isSafariType(browser),\n    isOpera: isOperaType(browser),\n    isIE: isIEType(browser),\n    osVersion: getOsVersion(os),\n    osName: getOsName(os),\n    fullBrowserVersion: getBrowserFullVersion(browser),\n    browserVersion: getBrowserVersion(browser),\n    browserName: getBrowserName(browser),\n    mobileVendor: getMobileVendor(device),\n    mobileModel: getMobileModel(device),\n    engineName: getEngineName(engine),\n    engineVersion: getEngineVersion(engine),\n    getUA: getUseragent(ua),\n    isEdge: isEdgeType(browser) || isEdgeChromiumType(ua),\n    isYandex: isYandexType(browser),\n    deviceType: getDeviceType(device),\n    isIOS13: getIOS13(),\n    isIPad13: getIPad13(),\n    isIPhone13: getIphone13(),\n    isIPod13: getIPod13(),\n    isElectron: isElectronType(),\n    isEdgeChromium: isEdgeChromiumType(ua),\n    isLegacyEdge: isEdgeType(browser) && !isEdgeChromiumType(ua),\n    isWindows: isWindowsType(os),\n    isMacOs: isMacOsType(os),\n    isMIUI: isMIUIType(browser),\n    isSamsungBrowser: isSamsungBrowserType(browser)\n  };\n}\n\nvar isSmartTV = isSmartTVType(device);\nvar isConsole = isConsoleType(device);\nvar isWearable = isWearableType(device);\nvar isEmbedded = isEmbeddedType(device);\nvar isMobileSafari = isMobileSafariType(browser) || getIPad13();\nvar isChromium = isChromiumType(browser);\nvar isMobile = isMobileAndTabletType(device) || getIPad13();\nvar isMobileOnly = isMobileType(device);\nvar isTablet = isTabletType(device) || getIPad13();\nvar isBrowser = isBrowserType(device);\nvar isDesktop = isBrowserType(device);\nvar isAndroid = isAndroidType(os);\nvar isWinPhone = isWinPhoneType(os);\nvar isIOS = isIOSType(os) || getIPad13();\nvar isChrome = isChromeType(browser);\nvar isFirefox = isFirefoxType(browser);\nvar isSafari = isSafariType(browser);\nvar isOpera = isOperaType(browser);\nvar isIE = isIEType(browser);\nvar osVersion = getOsVersion(os);\nvar osName = getOsName(os);\nvar fullBrowserVersion = getBrowserFullVersion(browser);\nvar browserVersion = getBrowserVersion(browser);\nvar browserName = getBrowserName(browser);\nvar mobileVendor = getMobileVendor(device);\nvar mobileModel = getMobileModel(device);\nvar engineName = getEngineName(engine);\nvar engineVersion = getEngineVersion(engine);\nvar getUA = getUseragent(ua);\nvar isEdge = isEdgeType(browser) || isEdgeChromiumType(ua);\nvar isYandex = isYandexType(browser);\nvar deviceType = getDeviceType(device);\nvar isIOS13 = getIOS13();\nvar isIPad13 = getIPad13();\nvar isIPhone13 = getIphone13();\nvar isIPod13 = getIPod13();\nvar isElectron = isElectronType();\nvar isEdgeChromium = isEdgeChromiumType(ua);\nvar isLegacyEdge = isEdgeType(browser) && !isEdgeChromiumType(ua);\nvar isWindows = isWindowsType(os);\nvar isMacOs = isMacOsType(os);\nvar isMIUI = isMIUIType(browser);\nvar isSamsungBrowser = isSamsungBrowserType(browser);\nvar getSelectorsByUserAgent = function getSelectorsByUserAgent(userAgent) {\n  if (!userAgent || typeof userAgent !== 'string') {\n    console.error('No valid user agent string was provided');\n    return;\n  }\n\n  var _UAHelper$parseUserAg = parseUserAgent(userAgent),\n      device = _UAHelper$parseUserAg.device,\n      browser = _UAHelper$parseUserAg.browser,\n      os = _UAHelper$parseUserAg.os,\n      engine = _UAHelper$parseUserAg.engine,\n      ua = _UAHelper$parseUserAg.ua;\n\n  return buildSelectorsObject({\n    device: device,\n    browser: browser,\n    os: os,\n    engine: engine,\n    ua: ua\n  });\n};\n\nvar AndroidView = function AndroidView(_ref) {\n  var renderWithFragment = _ref.renderWithFragment,\n      children = _ref.children,\n      props = _objectWithoutProperties(_ref, [\"renderWithFragment\", \"children\"]);\n\n  return isAndroid ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar BrowserView = function BrowserView(_ref2) {\n  var renderWithFragment = _ref2.renderWithFragment,\n      children = _ref2.children,\n      props = _objectWithoutProperties(_ref2, [\"renderWithFragment\", \"children\"]);\n\n  return isBrowser ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IEView = function IEView(_ref3) {\n  var renderWithFragment = _ref3.renderWithFragment,\n      children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, [\"renderWithFragment\", \"children\"]);\n\n  return isIE ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IOSView = function IOSView(_ref4) {\n  var renderWithFragment = _ref4.renderWithFragment,\n      children = _ref4.children,\n      props = _objectWithoutProperties(_ref4, [\"renderWithFragment\", \"children\"]);\n\n  return isIOS ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileView = function MobileView(_ref5) {\n  var renderWithFragment = _ref5.renderWithFragment,\n      children = _ref5.children,\n      props = _objectWithoutProperties(_ref5, [\"renderWithFragment\", \"children\"]);\n\n  return isMobile ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar TabletView = function TabletView(_ref6) {\n  var renderWithFragment = _ref6.renderWithFragment,\n      children = _ref6.children,\n      props = _objectWithoutProperties(_ref6, [\"renderWithFragment\", \"children\"]);\n\n  return isTablet ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WinPhoneView = function WinPhoneView(_ref7) {\n  var renderWithFragment = _ref7.renderWithFragment,\n      children = _ref7.children,\n      props = _objectWithoutProperties(_ref7, [\"renderWithFragment\", \"children\"]);\n\n  return isWinPhone ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileOnlyView = function MobileOnlyView(_ref8) {\n  var renderWithFragment = _ref8.renderWithFragment,\n      children = _ref8.children,\n      viewClassName = _ref8.viewClassName,\n      style = _ref8.style,\n      props = _objectWithoutProperties(_ref8, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\"]);\n\n  return isMobileOnly ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar SmartTVView = function SmartTVView(_ref9) {\n  var renderWithFragment = _ref9.renderWithFragment,\n      children = _ref9.children,\n      props = _objectWithoutProperties(_ref9, [\"renderWithFragment\", \"children\"]);\n\n  return isSmartTV ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar ConsoleView = function ConsoleView(_ref10) {\n  var renderWithFragment = _ref10.renderWithFragment,\n      children = _ref10.children,\n      props = _objectWithoutProperties(_ref10, [\"renderWithFragment\", \"children\"]);\n\n  return isConsole ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WearableView = function WearableView(_ref11) {\n  var renderWithFragment = _ref11.renderWithFragment,\n      children = _ref11.children,\n      props = _objectWithoutProperties(_ref11, [\"renderWithFragment\", \"children\"]);\n\n  return isWearable ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar CustomView = function CustomView(_ref12) {\n  var renderWithFragment = _ref12.renderWithFragment,\n      children = _ref12.children,\n      viewClassName = _ref12.viewClassName,\n      style = _ref12.style,\n      condition = _ref12.condition,\n      props = _objectWithoutProperties(_ref12, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\", \"condition\"]);\n\n  return condition ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\n\nfunction withOrientationChange(WrappedComponent) {\n  return /*#__PURE__*/function (_React$Component) {\n    _inherits(_class, _React$Component);\n\n    function _class(props) {\n      var _this;\n\n      _classCallCheck(this, _class);\n\n      _this = _possibleConstructorReturn(this, _getPrototypeOf(_class).call(this, props));\n      _this.isEventListenerAdded = false;\n      _this.handleOrientationChange = _this.handleOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onOrientationChange = _this.onOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onPageLoad = _this.onPageLoad.bind(_assertThisInitialized(_this));\n      _this.state = {\n        isLandscape: false,\n        isPortrait: false\n      };\n      return _this;\n    }\n\n    _createClass(_class, [{\n      key: \"handleOrientationChange\",\n      value: function handleOrientationChange() {\n        if (!this.isEventListenerAdded) {\n          this.isEventListenerAdded = true;\n        }\n\n        var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n        this.setState({\n          isPortrait: orientation === 0,\n          isLandscape: orientation === 90\n        });\n      }\n    }, {\n      key: \"onOrientationChange\",\n      value: function onOrientationChange() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"onPageLoad\",\n      value: function onPageLoad() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n          if (!this.isEventListenerAdded) {\n            this.handleOrientationChange();\n            window.addEventListener(\"load\", this.onPageLoad, false);\n          } else {\n            window.removeEventListener(\"load\", this.onPageLoad, false);\n          }\n\n          window.addEventListener(\"resize\", this.onOrientationChange, false);\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        window.removeEventListener(\"resize\", this.onOrientationChange, false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return React__default.createElement(WrappedComponent, _extends({}, this.props, {\n          isLandscape: this.state.isLandscape,\n          isPortrait: this.state.isPortrait\n        }));\n      }\n    }]);\n\n    return _class;\n  }(React__default.Component);\n}\n\nfunction useMobileOrientation() {\n  var _useState = React.useState(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    return {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n\n  var handleOrientationChange = React.useCallback(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    var next = {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n    state.orientation !== next.orientation && setState(next);\n  }, [state.orientation]);\n  React.useEffect(function () {\n    if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n      handleOrientationChange();\n      window.addEventListener(\"load\", handleOrientationChange, false);\n      window.addEventListener(\"resize\", handleOrientationChange, false);\n    }\n\n    return function () {\n      window.removeEventListener(\"resize\", handleOrientationChange, false);\n      window.removeEventListener(\"load\", handleOrientationChange, false);\n    };\n  }, [handleOrientationChange]);\n  return state;\n}\n\nfunction useDeviceData(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  return parseUserAgent(hookUserAgent);\n}\n\nfunction useDeviceSelectors(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  var deviceData = useDeviceData(hookUserAgent);\n  var selectors = buildSelectorsObject(deviceData);\n  return [selectors, deviceData];\n}\n\nexports.AndroidView = AndroidView;\nexports.BrowserTypes = BrowserTypes;\nexports.BrowserView = BrowserView;\nexports.ConsoleView = ConsoleView;\nexports.CustomView = CustomView;\nexports.IEView = IEView;\nexports.IOSView = IOSView;\nexports.MobileOnlyView = MobileOnlyView;\nexports.MobileView = MobileView;\nexports.OsTypes = OsTypes;\nexports.SmartTVView = SmartTVView;\nexports.TabletView = TabletView;\nexports.WearableView = WearableView;\nexports.WinPhoneView = WinPhoneView;\nexports.browserName = browserName;\nexports.browserVersion = browserVersion;\nexports.deviceDetect = deviceDetect;\nexports.deviceType = deviceType;\nexports.engineName = engineName;\nexports.engineVersion = engineVersion;\nexports.fullBrowserVersion = fullBrowserVersion;\nexports.getSelectorsByUserAgent = getSelectorsByUserAgent;\nexports.getUA = getUA;\nexports.isAndroid = isAndroid;\nexports.isBrowser = isBrowser;\nexports.isChrome = isChrome;\nexports.isChromium = isChromium;\nexports.isConsole = isConsole;\nexports.isDesktop = isDesktop;\nexports.isEdge = isEdge;\nexports.isEdgeChromium = isEdgeChromium;\nexports.isElectron = isElectron;\nexports.isEmbedded = isEmbedded;\nexports.isFirefox = isFirefox;\nexports.isIE = isIE;\nexports.isIOS = isIOS;\nexports.isIOS13 = isIOS13;\nexports.isIPad13 = isIPad13;\nexports.isIPhone13 = isIPhone13;\nexports.isIPod13 = isIPod13;\nexports.isLegacyEdge = isLegacyEdge;\nexports.isMIUI = isMIUI;\nexports.isMacOs = isMacOs;\nexports.isMobile = isMobile;\nexports.isMobileOnly = isMobileOnly;\nexports.isMobileSafari = isMobileSafari;\nexports.isOpera = isOpera;\nexports.isSafari = isSafari;\nexports.isSamsungBrowser = isSamsungBrowser;\nexports.isSmartTV = isSmartTV;\nexports.isTablet = isTablet;\nexports.isWearable = isWearable;\nexports.isWinPhone = isWinPhone;\nexports.isWindows = isWindows;\nexports.isYandex = isYandex;\nexports.mobileModel = mobileModel;\nexports.mobileVendor = mobileVendor;\nexports.osName = osName;\nexports.osVersion = osVersion;\nexports.parseUserAgent = parseUserAgent;\nexports.setUserAgent = setUserAgent;\nexports.useDeviceData = useDeviceData;\nexports.useDeviceSelectors = useDeviceSelectors;\nexports.useMobileOrientation = useMobileOrientation;\nexports.withOrientationChange = withOrientationChange;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-device-detect/dist/lib.js\n");

/***/ })

};
;