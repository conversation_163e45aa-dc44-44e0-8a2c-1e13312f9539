"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ChatModalStateContext */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { updateModalProps, getChatModalState } = (0,_contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2, _notification_data3;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        const hasRequiredData = !!(((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || ((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n        const shouldOpen = chatTypes.includes(notification.type) && hasRequiredData;\n        console.log('🔍 [NotificationBell] shouldOpenChatModal check:', {\n            type: notification.type,\n            isValidType: chatTypes.includes(notification.type),\n            hasRequiredData,\n            chatRoomId: (_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.chatRoomId,\n            tradeId: (_notification_data3 = notification.data) === null || _notification_data3 === void 0 ? void 0 : _notification_data3.tradeId,\n            shouldOpen\n        });\n        return shouldOpen;\n    };\n    // Unified function to normalize notification data and determine user roles\n    const normalizeNotificationData = async (notification, currentUserId)=>{\n        console.log('🔍 [NotificationBell] Normalizing notification data:', {\n            type: notification.type,\n            data: notification.data,\n            currentUserId,\n            currentUserIdType: typeof currentUserId\n        });\n        if (!notification.data) {\n            throw new Error('Notification data is missing');\n        }\n        if (!currentUserId || isNaN(currentUserId) || currentUserId <= 0) {\n            throw new Error(\"Invalid currentUserId: \".concat(currentUserId));\n        }\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        let tradeId = notification.data.tradeId;\n        let chatRoomId = notification.data.chatRoomId;\n        let perkId = notification.data.perkId;\n        // Handle different notification types with unified logic\n        switch(notification.type){\n            case 'perk_purchased':\n            case 'escrow_created':\n                // Current user is buyer, notification contains seller info\n                buyerId = currentUserId;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n                break;\n            case 'perk_sold':\n            case 'escrow_pending_acceptance':\n            case 'escrow_accepted':\n                // Current user is seller, notification contains buyer info\n                sellerId = currentUserId;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n                break;\n            case 'escrow_released':\n            case 'trade_completed':\n                // Could be either buyer or seller, determine from notification data\n                if (notification.data.buyerId === currentUserId) {\n                    buyerId = currentUserId;\n                    sellerId = notification.data.sellerId;\n                } else {\n                    sellerId = currentUserId;\n                    buyerId = notification.data.buyerId;\n                }\n                break;\n            case 'chat_message':\n                // For chat messages, use the buyerId and sellerId directly from notification data\n                // These are set when the notification is created in the backend\n                buyerId = notification.data.buyerId;\n                sellerId = notification.data.sellerId;\n                console.log('🔍 [NotificationBell] Chat message notification data:', {\n                    senderId: notification.data.senderId,\n                    receiverId: notification.data.receiverId,\n                    buyerId: notification.data.buyerId,\n                    sellerId: notification.data.sellerId,\n                    currentUserId,\n                    chatRoomId: notification.data.chatRoomId\n                });\n                // Fallback if buyerId/sellerId not available\n                if (!buyerId || !sellerId) {\n                    if (notification.data.senderId === currentUserId) {\n                        // I sent the message, I'm talking to the receiver\n                        buyerId = currentUserId;\n                        sellerId = notification.data.receiverId;\n                    } else {\n                        // I received the message, sender is talking to me\n                        buyerId = notification.data.senderId;\n                        sellerId = currentUserId;\n                    }\n                }\n                break;\n            default:\n                // Fallback logic for other types\n                if (!buyerId || !sellerId) {\n                    if (notification.data.buyerId === currentUserId) {\n                        buyerId = currentUserId;\n                        sellerId = notification.data.sellerId || notification.data.receiverId;\n                    } else {\n                        sellerId = currentUserId;\n                        buyerId = notification.data.buyerId || notification.data.senderId;\n                    }\n                }\n        }\n        // Generate chatRoomId if not provided\n        if (!chatRoomId && buyerId && sellerId && perkId) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId:', chatRoomId);\n        }\n        // Try to find tradeId if not provided (only if we have a valid currentUserId)\n        // Skip this lookup for now to avoid API errors - we can work without tradeId\n        if (!tradeId && chatRoomId && currentUserId && currentUserId > 0) {\n            console.log('🔍 [NotificationBell] TradeId not provided, but we can proceed with chatRoomId:', chatRoomId);\n        // We'll skip the getUserTrades call for now since it's causing 422 errors\n        // The chat modal can work without a specific tradeId\n        }\n        return {\n            buyerId,\n            sellerId,\n            tradeId,\n            chatRoomId,\n            perkId: perkId || notification.data.perkId\n        };\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        console.log('🔍 [NotificationBell] Opening chat modal for notification:', notification);\n        if (!(user === null || user === void 0 ? void 0 : user.id)) {\n            console.error('❌ [NotificationBell] User not authenticated');\n            return;\n        }\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        const userIdNumber = parseInt(user.id);\n        console.log('🔍 [NotificationBell] User validation:', {\n            userId: user.id,\n            userIdNumber,\n            myUserId,\n            userBo: userBo ? {\n                id: userBo.id\n            } : null\n        });\n        if (!myUserId || !userIdNumber || isNaN(userIdNumber) || userIdNumber <= 0) {\n            console.error('❌ [NotificationBell] Invalid user ID:', {\n                myUserId,\n                userIdNumber,\n                originalUserId: user.id\n            });\n            return;\n        }\n        // Use the unified normalization function\n        let normalizedData;\n        try {\n            normalizedData = await normalizeNotificationData(notification, userIdNumber);\n        } catch (error) {\n            var _notification_data, _notification_data1, _notification_data2, _notification_data3, _notification_data4;\n            console.error('❌ [NotificationBell] Failed to normalize notification data:', error);\n            // Fallback to basic notification data if normalization fails\n            console.log('🔄 [NotificationBell] Using fallback notification handling');\n            // Determine user roles based on notification type\n            let buyerId = (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId;\n            let sellerId = (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId;\n            if (!buyerId || !sellerId) {\n                // Use simple logic based on notification type\n                switch(notification.type){\n                    case 'perk_purchased':\n                    case 'escrow_created':\n                        var _notification_data5, _notification_data6;\n                        buyerId = userIdNumber;\n                        sellerId = ((_notification_data5 = notification.data) === null || _notification_data5 === void 0 ? void 0 : _notification_data5.sellerId) || ((_notification_data6 = notification.data) === null || _notification_data6 === void 0 ? void 0 : _notification_data6.receiverId);\n                        break;\n                    case 'perk_sold':\n                    case 'escrow_pending_acceptance':\n                    case 'escrow_accepted':\n                        var _notification_data7, _notification_data8;\n                        sellerId = userIdNumber;\n                        buyerId = ((_notification_data7 = notification.data) === null || _notification_data7 === void 0 ? void 0 : _notification_data7.buyerId) || ((_notification_data8 = notification.data) === null || _notification_data8 === void 0 ? void 0 : _notification_data8.senderId);\n                        break;\n                    case 'chat_message':\n                        var _notification_data9, _notification_data10;\n                        // For chat messages, use the data directly or determine from sender/receiver\n                        buyerId = (_notification_data9 = notification.data) === null || _notification_data9 === void 0 ? void 0 : _notification_data9.buyerId;\n                        sellerId = (_notification_data10 = notification.data) === null || _notification_data10 === void 0 ? void 0 : _notification_data10.sellerId;\n                        if (!buyerId || !sellerId) {\n                            var _notification_data11;\n                            if (((_notification_data11 = notification.data) === null || _notification_data11 === void 0 ? void 0 : _notification_data11.senderId) === userIdNumber) {\n                                var _notification_data12;\n                                buyerId = userIdNumber;\n                                sellerId = (_notification_data12 = notification.data) === null || _notification_data12 === void 0 ? void 0 : _notification_data12.receiverId;\n                            } else {\n                                var _notification_data13;\n                                buyerId = (_notification_data13 = notification.data) === null || _notification_data13 === void 0 ? void 0 : _notification_data13.senderId;\n                                sellerId = userIdNumber;\n                            }\n                        }\n                        break;\n                    default:\n                        // Default fallback\n                        buyerId = buyerId || userIdNumber;\n                        sellerId = sellerId || userIdNumber;\n                }\n            }\n            normalizedData = {\n                buyerId,\n                sellerId,\n                tradeId: (_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.tradeId,\n                chatRoomId: (_notification_data3 = notification.data) === null || _notification_data3 === void 0 ? void 0 : _notification_data3.chatRoomId,\n                perkId: (_notification_data4 = notification.data) === null || _notification_data4 === void 0 ? void 0 : _notification_data4.perkId\n            };\n            // Generate chatRoomId if missing\n            if (!normalizedData.chatRoomId && normalizedData.buyerId && normalizedData.sellerId && normalizedData.perkId) {\n                normalizedData.chatRoomId = generateChatRoomId(normalizedData.buyerId, normalizedData.sellerId, normalizedData.perkId);\n                console.log('🔄 [NotificationBell] Generated fallback chatRoomId:', normalizedData.chatRoomId);\n            }\n            // If we still don't have a chatRoomId, we can't proceed\n            if (!normalizedData.chatRoomId) {\n                console.error('❌ [NotificationBell] Cannot determine chatRoomId even with fallback');\n                return;\n            }\n        }\n        const { buyerId, sellerId, tradeId, chatRoomId, perkId } = normalizedData;\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with normalized data:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            tradeId,\n            perkId,\n            myUserId,\n            userIdNumber\n        });\n        // Fetch real trade details if tradeId exists\n        let activeTrade = undefined;\n        let tradeIdToUse = tradeId;\n        if (tradeIdToUse) {\n            try {\n                var _tradeResponse_data;\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data,\n                    tradeDataId: (_tradeResponse_data = tradeResponse.data) === null || _tradeResponse_data === void 0 ? void 0 : _tradeResponse_data.id,\n                    tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    // Ensure we have a valid ID - use tradeIdToUse as fallback\n                    const validId = tradeResponse.data.id || tradeIdToUse;\n                    activeTrade = {\n                        id: validId,\n                        status: tradeResponse.data.status,\n                        tradeId: validId,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    var _notification_data14, _notification_data15;\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to normalized data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: ((_notification_data14 = notification.data) === null || _notification_data14 === void 0 ? void 0 : _notification_data14.status) || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: perkId,\n                        escrowId: (_notification_data15 = notification.data) === null || _notification_data15 === void 0 ? void 0 : _notification_data15.escrowId\n                    };\n                }\n            } catch (error) {\n                var _notification_data16, _notification_data17;\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to normalized data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: ((_notification_data16 = notification.data) === null || _notification_data16 === void 0 ? void 0 : _notification_data16.status) || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: perkId,\n                    escrowId: (_notification_data17 = notification.data) === null || _notification_data17 === void 0 ? void 0 : _notification_data17.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, update it instead of creating new one\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, updating with new state');\n            // Update the existing modal with new props\n            const newProps = {\n                activeTrade,\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onRelease: tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n                    alert('Cannot release escrow: Trade information not available');\n                },\n                onRefund: tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n                    alert('Cannot refund escrow: Trade information not available');\n                },\n                onAccept: tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n                    alert('Cannot accept escrow: Trade information not available');\n                }\n            };\n            // Update modal props through the state context\n            const updated = updateModalProps(chatRoomId, newProps);\n            if (updated) {\n                console.log('✅ [NotificationBell] Successfully updated existing chat modal');\n                existingModal.scrollIntoView({\n                    behavior: 'smooth'\n                });\n                setIsOpen(false);\n                return;\n            } else {\n                console.log('⚠️ [NotificationBell] Failed to update modal, will create new one');\n            }\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            walletConnected: isConnected,\n            hasWallet: !!(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address)\n        });\n        // Ensure we always have functional action functions\n        const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n            alert('Cannot release escrow: Trade information not available');\n        };\n        const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n            alert('Cannot accept escrow: Trade information not available');\n        };\n        const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n            alert('Cannot refund escrow: Trade information not available');\n        };\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: safeOnRelease,\n                onRefund: safeOnRefund,\n                onReport: ()=>{\n                    console.log('🔍 [NotificationBell] Report function called');\n                // TODO: Implement report functionality\n                },\n                onAccept: safeOnAccept,\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data, _notification_data1;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n            actionUrl: notification.actionUrl,\n            notificationData: notification.data\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 896,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 895,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 904,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 903,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 921,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 920,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 929,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 928,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 936,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 945,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 944,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 954,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 953,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 952,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 963,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 962,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 970,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 989,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 987,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 995,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1006,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1014,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 1012,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1021,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 1020,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1051,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1058,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1064,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1054,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1123,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1139,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1137,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1147,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1166,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1176,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1180,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1186,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1196,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1183,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1179,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1156,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1135,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1212,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1072,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 1049,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"QdiDVwBej9/cSe8ZkWZlFhrudmc=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"529c7582a75f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTI5Yzc1ODJhNzVmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});