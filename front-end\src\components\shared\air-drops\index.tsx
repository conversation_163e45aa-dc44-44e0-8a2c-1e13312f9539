'use client';

import { formatDistanceToNow } from 'date-fns';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
  
import {
  fetchTokenOptions,
  transferSPLTokenWithSigner,
} from "@/utils/helpers";
import dynamic from 'next/dynamic';
import { addAirDrop } from "../../../axios/requests";
import { useWallet } from "../../../hooks/useWallet";
import { useTranslation } from "../../../hooks/useTranslation";
import { WALLET_CONFIG } from '@/config/environment';
import { showErrorToast, showSuccessToast, TOAST_MESSAGES } from '@/utils/errorHandling';

const Table = dynamic(() => import('../table').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const TextBox = dynamic(() => import('../textBox').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const CreateAirdropForm = dynamic(() => import('../avelable-balance').then(mod => ({ default: mod.CreateAirdropForm })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const AirDrops: React.FC<{ myAirDrop: any[]; myTokens: any[] }> = ({
  myAirDrop,
  myTokens,
}) => {
  const { t } = useTranslation();
  const [availableBalance, setAvailableBalance] = useState(0);
  const [airdrops, setAirdrops] = useState(myAirDrop || []);
  const { solanaWallet, isConnected, getWalletAddress, wallets } = useWallet();

  // Memoize wallet address to avoid unnecessary re-fetches
  const walletAddress = useMemo(() => getWalletAddress(), [getWalletAddress]);

  // Memoize token address to avoid unnecessary lookups
  const tokenToCheck = useMemo(() => myTokens[0]?.tokenAddress, [myTokens]);

  const handleAirdropSubmit = useCallback(async (formData: any) => {
    if (!isConnected || !solanaWallet) {
      showErrorToast(TOAST_MESSAGES.WALLET.CONNECT_REQUIRED);
      return;
    }

    const recipientPubkeyHere = WALLET_CONFIG.AIRDROP_WALLET || '';

    const privyWallet = wallets.find((w) => w.type === "solana");

    if (!privyWallet || !privyWallet.signTransaction) {
      showErrorToast(TOAST_MESSAGES.WALLET.CONNECTION_FAILED);
      throw new Error(
        t('airDrops.wrongWallet')
      );
    }

    const result = await transferSPLTokenWithSigner(
      solanaWallet,
      recipientPubkeyHere,
      myTokens[0]?.tokenAddress,
      formData.totalTokens,
      6
    );

    if (result.success) {
      const dataIs = {
        totalTokens: formData.totalTokens,
        tokensPerLink: formData.tokensPerLink,
        isPrivate: formData.isPrivate,
        UserID: myTokens[0].userId,
        TokenID: myTokens[0].tokenId,
        signature: result.signature,
        tokenAddress: myTokens[0].tokenAddress,
      };

      const data = await addAirDrop(dataIs);
      myAirDrop = data.allAirdrops;
      setAirdrops(data.allAirdrops); // 🔁 update local state
      //setAvailableBalance((prev) => prev - parseInt(formData.totalTokens));
      showSuccessToast(TOAST_MESSAGES.AIRDROP.CREATE_SUCCESS);
    } else {
      showErrorToast(TOAST_MESSAGES.AIRDROP.CREATE_FAILED);
    }
    return 0;
  }, [isConnected, solanaWallet, wallets, myTokens]);

  // Memoize the balance checking function
  const checkAndGetBalance = useCallback(async () => {
    if (!walletAddress) return;

    const updatedTokens = await fetchTokenOptions(walletAddress);

    if (!tokenToCheck) return;

    const match = updatedTokens.find(
      (token) => token.tokenAddress === tokenToCheck
    );

    if (match) {
      setAvailableBalance(match.balance);
    } else {
      console.log('Token not found in updated list.');
    }
  }, [walletAddress, tokenToCheck]);

  useEffect(() => {
    checkAndGetBalance();
  }, [checkAndGetBalance]); // ✅ safer dependency

  // Memoize columns configuration
  const columns = useMemo(() => [
  { header: t('airDrops.colLink'), key: 'link', width: 'flex-1' },
    { header: t('airDrops.colTotalTokens'), key: 'totalTokens', width: 'flex-1' },
    { header: t('airDrops.colPerLink'), key: 'remaining', width: 'w-24' },
  ], []);

  // Memoize data transformation
  const data = useMemo(() => {
    return airdrops?.map((drop) => ({
      id: drop.id.toString(),
      link: `${window.location.origin}/#${drop.link}<br/>${formatDistanceToNow(
        new Date(drop.updatedAt)
      )} ago`,
      totalTokens: drop.totalTokens.toLocaleString(),
      remaining: drop.tokensPerLink.toString(),
    }));
  }, [airdrops]);

  // Memoize pagination calculation
  const paginationData = useMemo(() => {
    const rowsPerPage = 4;
    const actualTotalPages = Math.ceil((data?.length || 0) / rowsPerPage);
    return { rowsPerPage, actualTotalPages };
  }, [data]);

  // Memoize page change handler
  const handlePageChange = useCallback((page: number) => {
    console.log(`Navigate to page ${page}`);
  }, []);

  return (
    <div className="flex flex-col mt-18">
      <div className="flex flex-col lg:flex-row justify-between gap-8 mb-12">
        <div className="lg:flex-1 lg:block lg:justify-start">
          <CreateAirdropForm
            title={t('airDrops.formTitle')}
            availableBalance={availableBalance}
            buttonText={t('airDrops.formButton')}
            onSubmit={handleAirdropSubmit}
          />
        </div>
        <div className="flex-1 flex justify-center lg:justify-end">
          <TextBox
            title={t('airDrops.aboutTitle')}
            description={t('airDrops.aboutDescription')}
          />
        </div>
      </div>
      <div className="">
        <Table
          title={t('airDrops.tableTitle')}
          columns={columns}
          data={data || []}
          currentPage={1}
          totalPages={paginationData.actualTotalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default AirDrops;
