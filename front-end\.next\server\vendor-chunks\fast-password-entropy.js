"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-password-entropy";
exports.ids = ["vendor-chunks/fast-password-entropy"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-password-entropy/src/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/fast-password-entropy/src/index.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\n/**\n * Calculate the entropy of a string based on the size of the charset used and\n * the length of the string.\n *\n * Based on:\n * http://resources.infosecinstitute.com/password-security-complexity-vs-length/\n *\n * @param   {number} charset is the size of the string charset.\n * @param   {number} length  is the length of the string.\n * @returns {number}         the calculated entropy.\n */\nconst calcEntropy = (charset, length) =>\n  Math.round(length * Math.log(charset) / Math.LN2)\n\n/**\n * Standard character sets list.\n *\n * It assumes the `uppercase` and `lowercase` charsets to have 26 chars as in\n * the English alphabet. Numbers are 10 characters long. Symbols are the rest\n * of the 33 remaining chars in the 7-bit ASCII table.\n *\n * @type {Array}\n */\nconst stdCharsets = [{\n  name: 'lowercase',\n  re: /[a-z]/, // abcdefghijklmnopqrstuvwxyz\n  length: 26\n}, {\n  name: 'uppercase',\n  re: /[A-Z]/, // ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  length: 26\n}, {\n  name: 'numbers',\n  re: /[0-9]/, // 1234567890\n  length: 10\n}, {\n  name: 'symbols',\n  re: /[^a-zA-Z0-9]/, //  !\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~ (and any other)\n  length: 33\n}]\n\n/**\n * Creates a function to calculate the total charset length of a string based on\n * the given charsets.\n *\n * @param  {Object[]} charsets are description of each charset. Shall contain a\n *                             regular expression `re` to identify each\n *                             character and a `length` with the total possible\n *                             characters in the set.\n * @returns {Function}         a function that will receive a string and return\n *                             the total charset length.\n */\nconst calcCharsetLengthWith = charsets =>\n  string =>\n    charsets.reduce((length, charset) =>\n      length + (charset.re.test(string) ? charset.length : 0), 0)\n\n/**\n * Helper function to calculate the total charset lengths of a given string\n * using the standard character sets.\n *\n * @type {Function}\n */\nconst calcCharsetLength = calcCharsetLengthWith(stdCharsets)\n\n/**\n * Calculate the given password entropy.\n *\n * @param   {string} string is the password string.\n * @returns {number}        [the calculated entropy.\n */\nconst passwordEntropy = string =>\n  string ? calcEntropy(calcCharsetLength(string), string.length) : 0\n\nmodule.exports = passwordEntropy\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-password-entropy/src/index.js\n");

/***/ })

};
;