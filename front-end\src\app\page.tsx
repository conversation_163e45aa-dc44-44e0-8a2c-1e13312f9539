'use client';

import { motion, AnimatePresence } from 'framer-motion';
import React, { useEffect, useCallback, useMemo } from 'react';
import Card from '@/components/shared/card';
import CardSkeleton from '@/components/shared/card-skeleton';
import { useAppContext } from '@/contexts/AppContext';
import { useCoins, Coin } from '@/hooks/useCoins';
import { useFilters, FilterState } from '@/hooks/useFilters';
import { staggerContainer } from '@/lib/animations';
import { fetchNotifications } from '@/lib/api';
import { useAirdrop } from '@/hooks/useCollectAirdrop';
import { useTranslation } from '@/hooks/useTranslation';
import dynamic from 'next/dynamic';

const Filters = dynamic(() => import('@/components/shared/filters').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const StaggeredGrid = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredGrid })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const StaggeredItem = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredItem })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const PageTransition = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.PageTransition })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const ErrorState = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.ErrorState })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const NoCoinsEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoCoinsEmpty })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const SearchEmptyState = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.SearchEmptyState })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


export default function Home() {
  const { filters, setCategory, setSearch, setSortBy, setFilterVerified } =
    useFilters();
  const { setNotificationCount } = useAppContext();
  const { coins, loading, error } = useCoins(filters);
  const { t } = useTranslation();

  useAirdrop();

  // Memoize the notification loading effect
  useEffect(() => {
    const loadNotifications = async () => {
      try {
        const notificationData = await fetchNotifications();
        setNotificationCount(notificationData.length);
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        // Silently fail for notifications as they are not critical
      }
    };

    loadNotifications();
  }, [setNotificationCount]);

  const handleFilterChange = useCallback(
    (newFilters: FilterState) => {
      setCategory(newFilters.category);
      setSearch(newFilters.search);
      setSortBy(newFilters.sortBy);
      setFilterVerified(newFilters.filterVerified);
    },
    [setCategory, setSearch, setSortBy, setFilterVerified]
  );

  const handleBuyCoin = useCallback((coin: Coin) => {
    console.log("click on buyy");
  }, []);

  const handleRetry = useCallback(() => {
    window.location.reload();
  }, []);

  // Memoize expensive calculations
  const hasSearchTerm = useMemo(() => filters.search.trim().length > 0, [filters.search]);
  const showEmptyState = useMemo(() => !loading && coins.length === 0, [loading, coins.length]);
  const showErrorState = useMemo(() => error && !loading, [error, loading]);

  // Memoize skeleton array to prevent recreation
  const skeletonArray = useMemo(() => Array.from({ length: 10 }), []);

  // Memoize coin cards to prevent unnecessary re-renders
  const coinCards = useMemo(() =>
    coins.map((coin, i) => (
      <StaggeredItem key={`coin-${coin.id || i}`}>
        <Card
          id={coin.id}
          isLive={coin.isLive}
          imageUrl={coin.imageUrl}
          name={coin.name}
          price={coin.price}
          username={coin.username}
          handle={coin.handle}
          buttonType="view"
          soldCount={coin.soldCount}
          remainingCount={coin.remainingCount}
          timeLeft={coin.timeLeft}
          isVerified={coin.isVerified}
          onBuyClick={() => handleBuyCoin(coin)}
          href={`/coins/${coin.id}`}
        />
      </StaggeredItem>
    )), [coins, handleBuyCoin]);

  // Memoize skeleton cards
  const skeletonCards = useMemo(() =>
    skeletonArray.map((_, index) => (
      <motion.div
        key={index}
        variants={{
          initial: { opacity: 0, y: 20 },
          animate: {
            opacity: 1,
            y: 0,
            transition: { delay: index * 0.1 },
          },
          exit: { opacity: 0, y: -20 },
        }}
      >
        <CardSkeleton buttonType="view" />
      </motion.div>
    )), [skeletonArray]);

  return (
    <PageTransition>
      <div className="px-0 md:px-6 pb-6 mx-auto mt-2 bg-[#FFF7F1] min-h-screen">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Filters onFilterChange={handleFilterChange} title={t('ui.trendingCoins')} />
        </motion.div>

        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mt-4"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              {skeletonCards}
            </motion.div>
          ) : showErrorState ? (
            <motion.div
              key="error"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <ErrorState onRetry={handleRetry} />
            </motion.div>
          ) : showEmptyState ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              {hasSearchTerm ? (
                <SearchEmptyState searchTerm={filters.search} />
              ) : (
                <NoCoinsEmpty />
              )}
            </motion.div>
          ) : (
            <StaggeredGrid
              key="coins"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mt-4"
              staggerDelay={0.05}
            >
              {coinCards}
            </StaggeredGrid>
          )}
        </AnimatePresence>

        {!loading && !showEmptyState && !showErrorState && (
          <motion.div
            className="flex justify-center mt-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <motion.div
              className="text-gray-600 font-medium"
              whileHover={{ scale: 1.05 }}
            >
              {t('ui.showingCoins', { count: coins.length })}
            </motion.div>
          </motion.div>
        )}

        {/* Airdrop Modal is now handled by GlobalModalContext */}
      </div>
    </PageTransition>
  );
}
