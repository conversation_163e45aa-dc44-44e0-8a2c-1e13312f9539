import {
  validateName,
  validateNameWithMinLength,
  validatePrice,
  validateUrl,
  validateStockAmount,
  validateTicker,
  validateSocialHandle,
  validateImage,
  validateUserLogin,
  validateUserSession,
  hasErrors,
  isFormValid,
} from '../formValidation'

// Mock the config import
jest.mock('@/config/environment', () => ({
  UPLOAD_CONFIG: {
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },
}))

describe('Form Validation Utilities', () => {
  describe('validateName', () => {
    it('should return error for empty name', () => {
      expect(validateName('')).toBe('Name is required')
    })

    it('should return error for whitespace-only name', () => {
      expect(validateName('   ')).toBe('Name is required')
    })

    it('should return empty string for valid name', () => {
      expect(validateName('Valid Name')).toBe('')
    })

    it('should handle special characters in name', () => {
      expect(validateName('Name-123_$')).toBe('')
    })

    it('should handle very long names', () => {
      const longName = 'a'.repeat(1000)
      expect(validateName(longName)).toBe('')
    })
  })

  describe('validateNameWithMinLength', () => {
    it('should return error for name shorter than default minimum (3)', () => {
      expect(validateNameWithMinLength('ab')).toBe('Name must be at least 3 characters')
    })

    it('should return error for name shorter than custom minimum', () => {
      expect(validateNameWithMinLength('abc', 5)).toBe('Name must be at least 5 characters')
    })

    it('should accept name with exact minimum length', () => {
      expect(validateNameWithMinLength('abc', 3)).toBe('')
    })

    it('should handle Unicode characters', () => {
      expect(validateNameWithMinLength('测试名')).toBe('')
    })

    it('should handle emojis in name', () => {
      expect(validateNameWithMinLength('Test 😀🎉')).toBe('')
    })
  })

  describe('validatePrice', () => {
    it('should return error for empty price', () => {
      expect(validatePrice('')).toBe('Price is required')
    })

    it('should return error for whitespace-only price', () => {
      expect(validatePrice('   ')).toBe('Price is required')
    })

    it('should return error for negative price', () => {
      expect(validatePrice('-10')).toBe('Please enter a valid price')
    })

    it('should return error for non-numeric price', () => {
      expect(validatePrice('abc')).toBe('Please enter a valid price')
    })

    it('should accept zero price', () => {
      expect(validatePrice('0')).toBe('')
    })

    it('should accept valid decimal price', () => {
      expect(validatePrice('19.99')).toBe('')
    })

    it('should accept large price values', () => {
      expect(validatePrice('999999.99')).toBe('')
    })

    it('should handle scientific notation', () => {
      expect(validatePrice('1e10')).toBe('')
    })

    it('should reject Infinity', () => {
      expect(validatePrice('Infinity')).toBe('Please enter a valid price')
    })

    it('should reject NaN string', () => {
      expect(validatePrice('NaN')).toBe('Please enter a valid price')
    })
  })

  describe('validateUrl', () => {
    it('should allow empty URL (optional field)', () => {
      expect(validateUrl('')).toBe('')
    })

    it('should accept valid HTTP URL', () => {
      expect(validateUrl('http://example.com')).toBe('')
    })

    it('should accept valid HTTPS URL', () => {
      expect(validateUrl('https://example.com')).toBe('')
    })

    it('should accept URL without protocol', () => {
      expect(validateUrl('example.com')).toBe('')
    })

    it('should accept URL with www', () => {
      expect(validateUrl('www.example.com')).toBe('')
    })

    it('should accept URL with path', () => {
      expect(validateUrl('https://example.com/path/to/page')).toBe('')
    })

    it('should accept URL with query parameters', () => {
      expect(validateUrl('https://example.com/page?param=value&other=test')).toBe('')
    })

    it('should reject invalid URL format', () => {
      expect(validateUrl('not-a-url')).toBe('Please enter a valid URL')
    })

    it('should reject URL with spaces', () => {
      expect(validateUrl('example .com')).toBe('Please enter a valid URL')
    })

    it('should use custom field name in error message', () => {
      expect(validateUrl('invalid', 'website')).toBe('Please enter a valid website')
    })

    it('should handle very long URLs', () => {
      const longUrl = 'https://example.com/' + 'a'.repeat(2000)
      expect(validateUrl(longUrl)).toBe('')
    })
  })

  describe('validateStockAmount', () => {
    it('should return empty string when limited stock is disabled', () => {
      expect(validateStockAmount('', false)).toBe('')
      expect(validateStockAmount('invalid', false)).toBe('')
    })

    it('should return error for empty amount when limited stock is enabled', () => {
      expect(validateStockAmount('', true)).toBe('Stock amount is required')
    })

    it('should return error for negative amount', () => {
      expect(validateStockAmount('-5', true)).toBe('Please enter a valid stock amount')
    })

    it('should return error for zero amount', () => {
      expect(validateStockAmount('0', true)).toBe('Please enter a valid stock amount')
    })

    it('should return error for non-integer amount', () => {
      expect(validateStockAmount('5.5', true)).toBe('Please enter a valid stock amount')
    })

    it('should accept valid positive integer', () => {
      expect(validateStockAmount('100', true)).toBe('')
    })

    it('should accept very large stock amounts', () => {
      expect(validateStockAmount('999999999', true)).toBe('')
    })

    it('should reject non-numeric input', () => {
      expect(validateStockAmount('abc', true)).toBe('Please enter a valid stock amount')
    })
  })

  describe('validateTicker', () => {
    it('should return error for empty ticker', () => {
      expect(validateTicker('')).toBe('Ticker is required')
    })

    it('should return error for ticker too short', () => {
      expect(validateTicker('A')).toBe('Ticker must be 2-5 characters')
    })

    it('should return error for ticker too long', () => {
      expect(validateTicker('TOOLONG')).toBe('Ticker must be 2-5 characters')
    })

    it('should return error for lowercase letters', () => {
      expect(validateTicker('abc')).toBe('Ticker must contain only uppercase letters and numbers')
    })

    it('should return error for special characters', () => {
      expect(validateTicker('AB$')).toBe('Ticker must contain only uppercase letters and numbers')
    })

    it('should accept valid uppercase ticker', () => {
      expect(validateTicker('BTC')).toBe('')
    })

    it('should accept ticker with numbers', () => {
      expect(validateTicker('ABC123')).toBe('Ticker must be 2-5 characters') // Too long
      expect(validateTicker('AB1')).toBe('')
    })

    it('should accept minimum length ticker', () => {
      expect(validateTicker('AB')).toBe('')
    })

    it('should accept maximum length ticker', () => {
      expect(validateTicker('ABCDE')).toBe('')
    })
  })

  describe('validateSocialHandle', () => {
    describe('Telegram validation', () => {
      it('should allow empty handle', () => {
        expect(validateSocialHandle('', 'telegram')).toBe('')
      })

      it('should accept valid telegram handle without @', () => {
        expect(validateSocialHandle('username123', 'telegram')).toBe('')
      })

      it('should accept valid telegram handle with @', () => {
        expect(validateSocialHandle('@username123', 'telegram')).toBe('')
      })

      it('should reject telegram handle too short', () => {
        expect(validateSocialHandle('abc', 'telegram')).toBe('Please enter a valid Telegram username')
      })

      it('should reject telegram handle with spaces', () => {
        expect(validateSocialHandle('user name', 'telegram')).toBe('Please enter a valid Telegram username')
      })

      it('should reject telegram handle with special characters', () => {
        expect(validateSocialHandle('user@name', 'telegram')).toBe('Please enter a valid Telegram username')
      })
    })

    describe('Twitter validation', () => {
      it('should allow empty handle', () => {
        expect(validateSocialHandle('', 'twitter')).toBe('')
      })

      it('should accept valid twitter handle without @', () => {
        expect(validateSocialHandle('username', 'twitter')).toBe('')
      })

      it('should accept valid twitter handle with @', () => {
        expect(validateSocialHandle('@username', 'twitter')).toBe('')
      })

      it('should reject twitter handle too long', () => {
        expect(validateSocialHandle('verylongusernamehere', 'twitter')).toBe('Please enter a valid Twitter/X username')
      })

      it('should accept maximum length twitter handle', () => {
        expect(validateSocialHandle('username1234567', 'twitter')).toBe('')
      })

      it('should reject twitter handle with spaces', () => {
        expect(validateSocialHandle('user name', 'twitter')).toBe('Please enter a valid Twitter/X username')
      })
    })

    it('should handle unknown platforms gracefully', () => {
      expect(validateSocialHandle('anything', 'unknown')).toBe('')
    })
  })

  describe('validateImage', () => {
    const createMockFile = (size: number, type: string, name: string = 'test.jpg'): File => {
      const file = new File([''], name, { type })
      Object.defineProperty(file, 'size', { value: size })
      return file
    }

    it('should return error for null file', () => {
      expect(validateImage(null)).toBe('Please upload an image')
    })

    it('should accept valid image file', () => {
      const file = createMockFile(1024 * 1024, 'image/jpeg') // 1MB
      expect(validateImage(file)).toBe('')
    })

    it('should reject file larger than default limit', () => {
      const file = createMockFile(6 * 1024 * 1024, 'image/jpeg') // 6MB
      expect(validateImage(file)).toBe('Image must be less than 5MB')
    })

    it('should reject file larger than custom limit', () => {
      const file = createMockFile(2 * 1024 * 1024, 'image/jpeg') // 2MB
      expect(validateImage(file, 1)).toBe('Image must be less than 1MB')
    })

    it('should reject unsupported file type', () => {
      const file = createMockFile(1024, 'application/pdf')
      expect(validateImage(file)).toBe('Only JPEG, PNG, GIF, WEBP images are allowed')
    })

    it('should accept all supported file types', () => {
      const supportedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      supportedTypes.forEach(type => {
        const file = createMockFile(1024, type)
        expect(validateImage(file)).toBe('')
      })
    })

    it('should handle edge case of exact size limit', () => {
      const file = createMockFile(5 * 1024 * 1024, 'image/jpeg') // Exactly 5MB
      expect(validateImage(file)).toBe('')
    })
  })

  describe('validateUserLogin', () => {
    it('should return error for user ID 0', () => {
      expect(validateUserLogin(0)).toBe('Please log in first before Creating a token.')
    })

    it('should return empty string for valid user ID', () => {
      expect(validateUserLogin(123)).toBe('')
    })

    it('should handle negative user ID as valid', () => {
      expect(validateUserLogin(-1)).toBe('')
    })
  })

  describe('validateUserSession', () => {
    const mockLocalStorage = localStorage as any

    beforeEach(() => {
      // Clear localStorage mock
      mockLocalStorage.getItem.mockClear()
    })

    it('should return error when no token in localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      expect(validateUserSession()).toBe('Please log in first before proceeding.')
    })

    it('should return empty string when token exists', () => {
      mockLocalStorage.getItem.mockReturnValue('valid-token')
      expect(validateUserSession()).toBe('')
    })

    it('should return error for empty token', () => {
      mockLocalStorage.getItem.mockReturnValue('')
      expect(validateUserSession()).toBe('Please log in first before proceeding.')
    })

    it('should handle server-side rendering', () => {
      // Mock window as undefined
      const originalWindow = global.window
      // @ts-expect-error - Testing edge case
      delete global.window
      
      expect(validateUserSession()).toBe('Please log in first before proceeding.')
      
      // Restore window
      global.window = originalWindow
    })
  })

  describe('hasErrors', () => {
    it('should return false for object with no errors', () => {
      const errors = { name: '', email: '', price: '' }
      expect(hasErrors(errors)).toBe(false)
    })

    it('should return true for object with errors', () => {
      const errors = { name: 'Required', email: '', price: '' }
      expect(hasErrors(errors)).toBe(true)
    })

    it('should handle empty object', () => {
      expect(hasErrors({})).toBe(false)
    })

    it('should handle object with null values', () => {
      const errors = { name: null, email: '', price: undefined }
      expect(hasErrors(errors)).toBe(false)
    })

    it('should handle mixed error types', () => {
      const errors = { name: 'Error', email: 0, price: false, description: '' }
      expect(hasErrors(errors)).toBe(true)
    })
  })

  describe('isFormValid', () => {
    it('should return true for object with no errors', () => {
      const errors = { name: '', email: '', price: '' }
      expect(isFormValid(errors)).toBe(true)
    })

    it('should return false for object with errors', () => {
      const errors = { name: 'Required', email: '', price: '' }
      expect(isFormValid(errors)).toBe(false)
    })

    it('should be inverse of hasErrors', () => {
      const errors1 = { name: '', email: '' }
      const errors2 = { name: 'Error', email: '' }
      
      expect(isFormValid(errors1)).toBe(!hasErrors(errors1))
      expect(isFormValid(errors2)).toBe(!hasErrors(errors2))
    })
  })
}) 