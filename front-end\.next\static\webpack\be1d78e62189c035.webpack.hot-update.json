{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Cconstants%5C%5C%5C%5Clayout.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22100%5C%22%2C%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22IbmFont%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cchat%5C%5CChatNotificationListener.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cfooter%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5CI18nInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CrunningLine%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22AppProvider%22%2C%22UnreadChatMessagesProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CGlobalModalContext.tsx%22%2C%22ids%22%3A%5B%22GlobalModalProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CPrivyContext.tsx%22%2C%22ids%22%3A%5B%22PrivyContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CSocketProvider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}