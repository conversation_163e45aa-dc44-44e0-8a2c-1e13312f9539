"use client";

import { PrivyProvider } from "@privy-io/react-auth";
import { toSolanaWalletConnectors } from "@privy-io/react-auth/solana";
import { AUTH_CONFIG } from "@/config/environment";

type Props = {
  children: React.ReactNode;
};
export const PrivyContextProvider = ({ children }: Props) => {
  return (
    <PrivyProvider
      appId={AUTH_CONFIG.PRIVY_APP_ID!}
      config={{
        embeddedWallets: {
          solana: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          theme: "light",
          landingHeader: "FunHi Login",
          walletChainType: "solana-only", // or your preferred chain
        },
        externalWallets: {
          solana: { connectors: toSolanaWalletConnectors() },
        },
      }}
    >
      {children}
    </PrivyProvider>
  );
};
