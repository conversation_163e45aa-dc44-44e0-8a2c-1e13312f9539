'use client';
import React, { useEffect, useRef, useState } from 'react';
import { AgCharts } from 'ag-charts-react';
import 'ag-charts-enterprise';
import { ChartData, generateMockCandles, getChartOptions } from './utils/chartUtils';

interface ChartProps {
  windowWidth: number;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ChartErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean }> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-full p-4 text-red-500">
          <p>Failed to load chart data. Please refresh the page and try again.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

const Chart: React.FC<ChartProps> = ({ windowWidth }) => {
  const [data, setData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadChartData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const currentDate = Date.now();
        const newData = generateMockCandles(100, currentDate, 0.0033, 60000);
        setData(newData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load chart data');
        console.error('Chart data loading error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadChartData();
  }, []);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full p-4 text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="w-full h-full p-4">
        <div className="space-y-3 h-full">
          <div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div>
          <div className="animate-pulse bg-gray-200 h-full w-full rounded"></div>
        </div>
      </div>
    );
  }

  if (!windowWidth || data.length === 0 || !data) {
    return (
      <div className="flex items-center justify-center h-full p-4 text-gray-500">
        <p>No data available</p>
      </div>
    );
  }

  return (
    <ChartErrorBoundary>
      <div className="w-full h-full">
        <AgCharts options={getChartOptions(data, windowWidth)} />
      </div>
    </ChartErrorBoundary>
  );
};

export default function TradeChart() {
  const [windowWidth, setWindowWidth] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleResize = () => {
      try {
        setWindowWidth(window.innerWidth);
      } catch (err) {
        setError('Failed to update chart dimensions');
        console.error('Resize error:', err);
      }
    };

    try {
      handleResize();
      window.addEventListener('resize', handleResize);
    } catch (err) {
      setError('Failed to initialize chart');
      console.error('Initialization error:', err);
    }

    return () => {
      try {
        window.removeEventListener('resize', handleResize);
      } catch (err) {
        console.error('Cleanup error:', err);
      }
    };
  }, [ref]);

  if (error) {
    return (
      <div className="h-max relative rounded-[12px] overflow-hidden shadow-md flex-1">
        <div className="flex items-center justify-center h-full p-4 text-red-500">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="h-max relative rounded-[12px] overflow-hidden shadow-md flex-1"
      ref={ref}
    >
      <Chart windowWidth={(ref?.current as any)?.offsetWidth || windowWidth} />
    </div>
  );
}
