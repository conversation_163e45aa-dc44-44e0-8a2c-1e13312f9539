"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-charts-core";
exports.ids = ["vendor-chunks/ag-charts-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-charts-core/dist/package/main.esm.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/ag-charts-core/dist/package/main.esm.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventEmitter: () => (/* binding */ EventEmitter),\n/* harmony export */   Logger: () => (/* binding */ logger_exports),\n/* harmony export */   ModuleRegistry: () => (/* binding */ moduleRegistry_exports),\n/* harmony export */   ModuleType: () => (/* binding */ ModuleType),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   and: () => (/* binding */ and),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   arrayLength: () => (/* binding */ arrayLength),\n/* harmony export */   arrayOf: () => (/* binding */ arrayOf),\n/* harmony export */   arrayOfDefs: () => (/* binding */ arrayOfDefs),\n/* harmony export */   arraysEqual: () => (/* binding */ arraysEqual),\n/* harmony export */   attachDescription: () => (/* binding */ attachDescription),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   callback: () => (/* binding */ callback),\n/* harmony export */   circularSliceArray: () => (/* binding */ circularSliceArray),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   color: () => (/* binding */ color),\n/* harmony export */   colorUnion: () => (/* binding */ colorUnion),\n/* harmony export */   constant: () => (/* binding */ constant),\n/* harmony export */   countFractionDigits: () => (/* binding */ countFractionDigits),\n/* harmony export */   countLines: () => (/* binding */ countLines),\n/* harmony export */   createElement: () => (/* binding */ createElement),\n/* harmony export */   createNumberFormatter: () => (/* binding */ createNumberFormatter),\n/* harmony export */   createSvgElement: () => (/* binding */ createSvgElement),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   defined: () => (/* binding */ defined),\n/* harmony export */   diffArrays: () => (/* binding */ diffArrays),\n/* harmony export */   downloadUrl: () => (/* binding */ downloadUrl),\n/* harmony export */   entries: () => (/* binding */ entries),\n/* harmony export */   fillGradientDefaults: () => (/* binding */ fillGradientDefaults),\n/* harmony export */   fillOptionsDef: () => (/* binding */ fillOptionsDef),\n/* harmony export */   fillPatternDefaults: () => (/* binding */ fillPatternDefaults),\n/* harmony export */   findMaxIndex: () => (/* binding */ findMaxIndex),\n/* harmony export */   findMaxValue: () => (/* binding */ findMaxValue),\n/* harmony export */   findMinIndex: () => (/* binding */ findMinIndex),\n/* harmony export */   findMinValue: () => (/* binding */ findMinValue),\n/* harmony export */   first: () => (/* binding */ first),\n/* harmony export */   fontOptionsDef: () => (/* binding */ fontOptionsDef),\n/* harmony export */   getDocument: () => (/* binding */ getDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   gradientColorStops: () => (/* binding */ gradientColorStops),\n/* harmony export */   gradientStrict: () => (/* binding */ gradientStrict),\n/* harmony export */   greaterThan: () => (/* binding */ greaterThan),\n/* harmony export */   groupBy: () => (/* binding */ groupBy),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   instanceOf: () => (/* binding */ instanceOf),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isBoolean: () => (/* binding */ isBoolean),\n/* harmony export */   isColor: () => (/* binding */ isColor),\n/* harmony export */   isDate: () => (/* binding */ isDate),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isEnumKey: () => (/* binding */ isEnumKey),\n/* harmony export */   isEnumValue: () => (/* binding */ isEnumValue),\n/* harmony export */   isFiniteNumber: () => (/* binding */ isFiniteNumber),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isHtmlElement: () => (/* binding */ isHtmlElement),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNegative: () => (/* binding */ isNegative),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isNumberEqual: () => (/* binding */ isNumberEqual),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isRegExp: () => (/* binding */ isRegExp),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol),\n/* harmony export */   isValidDate: () => (/* binding */ isValidDate),\n/* harmony export */   isValidNumberFormat: () => (/* binding */ isValidNumberFormat),\n/* harmony export */   iterate: () => (/* binding */ iterate),\n/* harmony export */   joinFormatted: () => (/* binding */ joinFormatted),\n/* harmony export */   lessThan: () => (/* binding */ lessThan),\n/* harmony export */   levenshteinDistance: () => (/* binding */ levenshteinDistance),\n/* harmony export */   lineDashOptionsDef: () => (/* binding */ lineDashOptionsDef),\n/* harmony export */   modulus: () => (/* binding */ modulus),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   numberMin: () => (/* binding */ numberMin),\n/* harmony export */   numberRange: () => (/* binding */ numberRange),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   optionsDefs: () => (/* binding */ optionsDefs),\n/* harmony export */   or: () => (/* binding */ or),\n/* harmony export */   parseColor: () => (/* binding */ parseColor),\n/* harmony export */   parseNumberFormat: () => (/* binding */ parseNumberFormat),\n/* harmony export */   partialDefs: () => (/* binding */ partialDefs),\n/* harmony export */   positiveNumber: () => (/* binding */ positiveNumber),\n/* harmony export */   positiveNumberNonZero: () => (/* binding */ positiveNumberNonZero),\n/* harmony export */   ratio: () => (/* binding */ ratio),\n/* harmony export */   required: () => (/* binding */ required),\n/* harmony export */   roundTo: () => (/* binding */ roundTo),\n/* harmony export */   setDocument: () => (/* binding */ setDocument),\n/* harmony export */   setWindow: () => (/* binding */ setWindow),\n/* harmony export */   sortBasedOnArray: () => (/* binding */ sortBasedOnArray),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   stringifyValue: () => (/* binding */ stringifyValue),\n/* harmony export */   strokeOptionsDef: () => (/* binding */ strokeOptionsDef),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toIterable: () => (/* binding */ toIterable),\n/* harmony export */   typeUnion: () => (/* binding */ typeUnion),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unique: () => (/* binding */ unique),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// packages/ag-charts-core/src/globals/logger.ts\nvar logger_exports = {};\n__export(logger_exports, {\n  error: () => error,\n  errorOnce: () => errorOnce,\n  log: () => log,\n  logGroup: () => logGroup,\n  reset: () => reset,\n  table: () => table,\n  warn: () => warn,\n  warnOnce: () => warnOnce\n});\nvar doOnceCache = /* @__PURE__ */ new Set();\nfunction log(...logContent) {\n  console.log(...logContent);\n}\nfunction warn(message, ...logContent) {\n  console.warn(`AG Charts - ${message}`, ...logContent);\n}\nfunction error(message, ...logContent) {\n  if (typeof message === \"object\") {\n    console.error(`AG Charts error`, message, ...logContent);\n  } else {\n    console.error(`AG Charts - ${message}`, ...logContent);\n  }\n}\nfunction table(...logContent) {\n  console.table(...logContent);\n}\nfunction warnOnce(message, ...logContent) {\n  const cacheKey = `Logger.warn: ${message}`;\n  if (doOnceCache.has(cacheKey))\n    return;\n  warn(message, ...logContent);\n  doOnceCache.add(cacheKey);\n}\nfunction errorOnce(message, ...logContent) {\n  const cacheKey = `Logger.error: ${message}`;\n  if (doOnceCache.has(cacheKey))\n    return;\n  error(message, ...logContent);\n  doOnceCache.add(cacheKey);\n}\nfunction reset() {\n  doOnceCache.clear();\n}\nfunction logGroup(name, cb) {\n  console.groupCollapsed(name);\n  try {\n    return cb();\n  } finally {\n    console.groupEnd();\n  }\n}\n\n// packages/ag-charts-core/src/globals/moduleRegistry.ts\nvar moduleRegistry_exports = {};\n__export(moduleRegistry_exports, {\n  detectChartDefinition: () => detectChartDefinition,\n  getAxisModule: () => getAxisModule,\n  getSeriesModule: () => getSeriesModule,\n  hasModule: () => hasModule,\n  listModulesByType: () => listModulesByType,\n  register: () => register,\n  registerMany: () => registerMany,\n  reset: () => reset2\n});\n\n// packages/ag-charts-core/src/interfaces/moduleDefinition.ts\nvar ModuleType = /* @__PURE__ */ ((ModuleType2) => {\n  ModuleType2[\"Axis\"] = \"axis\";\n  ModuleType2[\"Chart\"] = \"chart\";\n  ModuleType2[\"Preset\"] = \"preset\";\n  ModuleType2[\"Plugin\"] = \"plugin\";\n  ModuleType2[\"Series\"] = \"series\";\n  return ModuleType2;\n})(ModuleType || {});\n\n// packages/ag-charts-core/src/globals/moduleRegistry.ts\nvar registeredModules = /* @__PURE__ */ new Map();\nfunction register(definition) {\n  const existingDefinition = registeredModules.get(definition.name);\n  if (existingDefinition && (existingDefinition.enterprise || !definition.enterprise)) {\n    throw new Error(`AG Charts - Module '${definition.name}' already registered`);\n  }\n  registeredModules.set(definition.name, definition);\n}\nfunction registerMany(definitions) {\n  for (const definition of definitions) {\n    register(definition);\n  }\n}\nfunction reset2() {\n  registeredModules.clear();\n}\nfunction hasModule(moduleName) {\n  return registeredModules.has(moduleName);\n}\nfunction* listModulesByType(moduleType) {\n  for (const definition of registeredModules.values()) {\n    if (definition.type === moduleType) {\n      yield definition;\n    }\n  }\n}\nfunction detectChartDefinition(options) {\n  for (const definition of registeredModules.values()) {\n    if (isChartModule(definition) && definition.detect(options)) {\n      return definition;\n    }\n  }\n  throw new Error(\n    `AG Charts - Unknown chart type; Check options are correctly structured and series types are specified`\n  );\n}\nfunction getSeriesModule(moduleName) {\n  const definition = registeredModules.get(moduleName);\n  if (isSeriesModule(definition)) {\n    return definition;\n  }\n}\nfunction getAxisModule(moduleName) {\n  const definition = registeredModules.get(moduleName);\n  if (isAxisModule(definition)) {\n    return definition;\n  }\n}\nfunction isChartModule(definition) {\n  return definition?.type === \"chart\" /* Chart */;\n}\nfunction isAxisModule(definition) {\n  return definition?.type === \"axis\" /* Axis */;\n}\nfunction isSeriesModule(definition) {\n  return definition?.type === \"series\" /* Series */;\n}\n\n// packages/ag-charts-core/src/classes/eventEmitter.ts\nvar EventEmitter = class {\n  constructor() {\n    this.events = /* @__PURE__ */ new Map();\n  }\n  /**\n   * Registers an event listener.\n   * @param eventName The event name to listen for.\n   * @param listener The callback to be invoked on the event.\n   * @returns A function to unregister the listener.\n   */\n  on(eventName, listener) {\n    if (!this.events.has(eventName)) {\n      this.events.set(eventName, /* @__PURE__ */ new Set());\n    }\n    this.events.get(eventName)?.add(listener);\n    return () => this.off(eventName, listener);\n  }\n  /**\n   * Unregisters an event listener.\n   * @param eventName The event name to stop listening for.\n   * @param listener The callback to be removed.\n   */\n  off(eventName, listener) {\n    const eventListeners = this.events.get(eventName);\n    if (eventListeners) {\n      eventListeners.delete(listener);\n      if (eventListeners.size === 0) {\n        this.events.delete(eventName);\n      }\n    }\n  }\n  /**\n   * Emits an event to all registered listeners.\n   * @param eventName The name of the event to emit.\n   * @param event The event payload.\n   */\n  emit(eventName, event) {\n    this.events.get(eventName)?.forEach((callback2) => callback2(event));\n  }\n  /**\n   * Clears all listeners for a specific event or all events if no event name is provided.\n   * @param eventName (Optional) The name of the event to clear listeners for. If not provided, all listeners for all events are cleared.\n   */\n  clear(eventName) {\n    if (eventName) {\n      this.events.delete(eventName);\n    } else {\n      this.events.clear();\n    }\n  }\n};\n\n// packages/ag-charts-core/src/utils/strings.ts\nfunction joinFormatted(values, conjunction = \"and\", format = String, maxItems = Infinity) {\n  if (values.length === 1) {\n    return format(values[0]);\n  }\n  values = values.map(format);\n  const lastValue = values.pop();\n  if (values.length >= maxItems) {\n    const remainingCount = values.length - (maxItems - 1);\n    return `${values.slice(0, maxItems - 1).join(\", \")}, and ${remainingCount} more ${conjunction} ${lastValue}`;\n  }\n  return `${values.join(\", \")} ${conjunction} ${lastValue}`;\n}\nfunction stringifyValue(value, maxLength = Infinity) {\n  if (typeof value === \"number\") {\n    if (isNaN(value)) {\n      return \"NaN\";\n    } else if (value === Infinity) {\n      return \"Infinity\";\n    } else if (value === -Infinity) {\n      return \"-Infinity\";\n    }\n  }\n  const strValue = JSON.stringify(value) ?? typeof value;\n  if (strValue.length > maxLength) {\n    return `${strValue.slice(0, maxLength)}... (+${strValue.length - maxLength} characters)`;\n  }\n  return strValue;\n}\nfunction countLines(text) {\n  let count = 1;\n  for (let i = 0; i < text.length; i++) {\n    if (text.charCodeAt(i) === 10) {\n      count++;\n    }\n  }\n  return count;\n}\nfunction levenshteinDistance(a, b) {\n  if (a === b)\n    return 0;\n  const [shorter, longer] = a.length < b.length ? [a, b] : [b, a];\n  const m = shorter.length;\n  const n = longer.length;\n  let prevRow = new Array(m + 1).fill(0).map((_, i) => i);\n  let currRow = new Array(m + 1);\n  for (let i = 1; i <= n; i++) {\n    currRow[0] = i;\n    for (let j = 1; j <= m; j++) {\n      const cost = longer[i - 1] === shorter[j - 1] ? 0 : 1;\n      currRow[j] = Math.min(\n        prevRow[j] + 1,\n        // Deletion\n        currRow[j - 1] + 1,\n        // Insertion\n        prevRow[j - 1] + cost\n        // Substitution\n      );\n    }\n    [prevRow, currRow] = [currRow, prevRow];\n  }\n  return prevRow[m];\n}\n\n// packages/ag-charts-core/src/utils/dom/globalsProxy.ts\nvar verifiedGlobals = {};\nif (typeof window !== \"undefined\") {\n  verifiedGlobals.window = window;\n} else if (typeof global !== \"undefined\") {\n  verifiedGlobals.window = global.window;\n}\nif (typeof document !== \"undefined\") {\n  verifiedGlobals.document = document;\n} else if (typeof global !== \"undefined\") {\n  verifiedGlobals.document = global.document;\n}\nfunction getDocument(propertyName) {\n  return propertyName ? verifiedGlobals.document?.[propertyName] : verifiedGlobals.document;\n}\nfunction getWindow(propertyName) {\n  return propertyName ? verifiedGlobals.window?.[propertyName] : verifiedGlobals.window;\n}\nfunction setDocument(document2) {\n  verifiedGlobals.document = document2;\n}\nfunction setWindow(window2) {\n  verifiedGlobals.window = window2;\n}\n\n// packages/ag-charts-core/src/utils/dom/domElements.ts\nfunction createElement(tagName, className, style) {\n  const element = getDocument().createElement(tagName);\n  if (typeof className === \"object\") {\n    style = className;\n    className = void 0;\n  }\n  if (className) {\n    for (const name of className.split(\" \")) {\n      element.classList.add(name);\n    }\n  }\n  if (style) {\n    Object.assign(element.style, style);\n  }\n  return element;\n}\nfunction createSvgElement(elementName) {\n  return getDocument().createElementNS(\"http://www.w3.org/2000/svg\", elementName);\n}\n\n// packages/ag-charts-core/src/utils/dom/domDownload.ts\nfunction downloadUrl(dataUrl, fileName) {\n  const body = getDocument(\"body\");\n  const element = createElement(\"a\", { display: \"none\" });\n  element.href = dataUrl;\n  element.download = fileName;\n  body.appendChild(element);\n  element.click();\n  setTimeout(() => body.removeChild(element));\n}\n\n// packages/ag-charts-core/src/utils/dom/domUtils.ts\nfunction parseColor(color2) {\n  const OptionConstructor = getWindow(\"Option\");\n  const { style } = new OptionConstructor();\n  style.color = color2;\n  return style.color || null;\n}\n\n// packages/ag-charts-core/src/utils/typeGuards.ts\nfunction isDefined(val) {\n  return val != null;\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isBoolean(value) {\n  return typeof value === \"boolean\";\n}\nfunction isDate(value) {\n  return value instanceof Date;\n}\nfunction isValidDate(value) {\n  return isDate(value) && !isNaN(Number(value));\n}\nfunction isRegExp(value) {\n  return value instanceof RegExp;\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction isObject(value) {\n  return typeof value === \"object\" && value !== null && !isArray(value);\n}\nfunction isObjectLike(value) {\n  return isArray(value) || isPlainObject(value);\n}\nfunction isPlainObject(value) {\n  return typeof value === \"object\" && value !== null && value.constructor?.name === \"Object\";\n}\nfunction isString(value) {\n  return typeof value === \"string\";\n}\nfunction isNumber(value) {\n  return typeof value === \"number\";\n}\nfunction isFiniteNumber(value) {\n  return Number.isFinite(value);\n}\nfunction isHtmlElement(value) {\n  return typeof window !== \"undefined\" && value instanceof HTMLElement;\n}\nfunction isEnumKey(enumObject, enumKey) {\n  return isString(enumKey) && Object.keys(enumObject).includes(enumKey);\n}\nfunction isEnumValue(enumObject, enumValue) {\n  return Object.values(enumObject).includes(enumValue);\n}\nfunction isSymbol(value) {\n  return typeof value === \"symbol\";\n}\nfunction isColor(value) {\n  return isString(value) && parseColor(value) != null;\n}\n\n// packages/ag-charts-core/src/utils/validation.ts\nvar descriptionSymbol = Symbol(\"description\");\nvar requiredSymbol = Symbol(\"required\");\nvar ValidationError = class {\n  constructor(message, path, required2, unknown) {\n    this.message = message;\n    this.path = path;\n    this.required = required2;\n    this.unknown = unknown;\n  }\n  toString() {\n    return this.message;\n  }\n};\nfunction validate(options, optionsDefs2, path = \"\") {\n  if (!isObject(options)) {\n    const message = validateMessage(path, options, \"an object\", true);\n    return { valid: null, errors: [new ValidationError(message, path, true)] };\n  }\n  const unusedKeys = [];\n  const optionsKeys = new Set(Object.keys(options));\n  const errors = [];\n  const valid = {};\n  function extendPath(key) {\n    if (isArray(optionsDefs2)) {\n      return `${path}[${key}]`;\n    }\n    return path ? `${path}.${key}` : key;\n  }\n  for (const key of Object.keys(optionsDefs2)) {\n    const validatorOrDefs = optionsDefs2[key];\n    const value = options[key];\n    const required2 = validatorOrDefs[requiredSymbol];\n    optionsKeys.delete(key);\n    if (typeof value === \"undefined\") {\n      unusedKeys.push(key);\n      if (!required2)\n        continue;\n    }\n    const keyPath = extendPath(key);\n    if (isFunction(validatorOrDefs)) {\n      const context = { options, path: keyPath };\n      if (validatorOrDefs(value, context)) {\n        valid[key] = context.result?.valid ?? value;\n      } else if (!context.result) {\n        const message = validateMessage(keyPath, value, validatorOrDefs, required2);\n        errors.push(new ValidationError(message, path, required2));\n      }\n      if (context.result) {\n        errors.push(...context.result.errors);\n      }\n    } else {\n      const nestedResult = validate(value, validatorOrDefs, keyPath);\n      if (nestedResult.valid != null) {\n        valid[key] = nestedResult.valid;\n      }\n      errors.push(...nestedResult.errors);\n    }\n  }\n  for (const key of optionsKeys) {\n    const value = options[key];\n    if (typeof value === \"undefined\")\n      continue;\n    const message = unknownMessage(key, extendPath(key), unusedKeys);\n    errors.push(new ValidationError(message, path, void 0, true));\n  }\n  return { valid, errors };\n}\nfunction validateMessage(path, value, validatorOrDefs, required2) {\n  const description = isString(validatorOrDefs) ? validatorOrDefs : validatorOrDefs[descriptionSymbol];\n  const expecting = description ? `; expecting ${description}` : \"\";\n  const prefix = path ? `Option \\`${path}\\`` : \"Value\";\n  return required2 && value == null ? `${prefix} is required and has not been provided${expecting}, ignoring.` : `${prefix} cannot be set to \\`${stringifyValue(value, 50)}\\`${expecting}, ignoring.`;\n}\nfunction unknownMessage(key, keyPath, unusedKeys) {\n  const match = findSuggestion(key, unusedKeys);\n  const postfix = match ? `; Did you mean \\`${match}\\`? Ignoring.` : \", ignoring.\";\n  return `Unknown option \\`${keyPath}\\`${postfix}`;\n}\nfunction findSuggestion(value, suggestions, maxDistance = 2) {\n  let smallestDistance = Infinity;\n  const lowerCaseValue = value.toLowerCase();\n  return suggestions.reduce((res, item) => {\n    const d = levenshteinDistance(lowerCaseValue, item.toLowerCase());\n    if (smallestDistance > d && d <= maxDistance) {\n      smallestDistance = d;\n      return item;\n    }\n    return res;\n  }, null);\n}\nfunction attachDescription(validatorOrDefs, description) {\n  return Object.assign(\n    isFunction(validatorOrDefs) ? (value, context) => validatorOrDefs(value, context) : { ...validatorOrDefs },\n    { [descriptionSymbol]: description }\n  );\n}\nfunction required(validatorOrDefs) {\n  return Object.assign(\n    isFunction(validatorOrDefs) ? (value, context) => validatorOrDefs(value, context) : optionsDefs(validatorOrDefs),\n    { [requiredSymbol]: true, [descriptionSymbol]: validatorOrDefs[descriptionSymbol] }\n  );\n}\nvar optionsDefs = (defs, description = \"an object\") => attachDescription((value, context) => {\n  context.result = validate(value, defs, context.path);\n  return !context.result.errors.some((error2) => error2.required && error2.path === context.path);\n}, description);\nvar partialDefs = (defs, description = \"an object\") => attachDescription((value, context) => {\n  context.result = validate(value, defs, context.path);\n  context.result.errors = context.result.errors.filter((error2) => !error2.unknown);\n  return !context.result.errors.some((error2) => error2.required && error2.path === context.path);\n}, description);\nvar and = (...validators) => attachDescription(\n  (value, context) => validators.every((validator) => {\n    const result = validator(value, context);\n    if (context.result && !result) {\n      delete context.result;\n    }\n    return result;\n  }),\n  validators.map((v) => v[descriptionSymbol]).filter(Boolean).join(\" and \")\n);\nvar or = (...validators) => attachDescription(\n  (value, context) => validators.some((validator) => {\n    const result = validator(value, context);\n    if (context.result && !result) {\n      delete context.result;\n    }\n    return result;\n  }),\n  validators.map((v) => v[descriptionSymbol]).filter(Boolean).join(\" or \")\n);\nvar isComparable = (value) => isFiniteNumber(value) || isValidDate(value);\nvar isValidDateValue = (value) => isDate(value) || (isFiniteNumber(value) || isString(value)) && isValidDate(new Date(value));\nvar array = attachDescription(isArray, \"an array\");\nvar boolean = attachDescription(isBoolean, \"a boolean\");\nvar callback = attachDescription(isFunction, \"a function\");\nvar color = attachDescription(isColor, \"a color string\");\nvar date = attachDescription(isValidDateValue, \"a date\");\nvar defined = attachDescription(isDefined, \"a defined value\");\nvar number = attachDescription(isFiniteNumber, \"a number\");\nvar object = attachDescription(isObject, \"an object\");\nvar string = attachDescription(isString, \"a string\");\nvar arrayLength = (minLength, maxLength = Infinity) => {\n  let message;\n  if (maxLength === Infinity) {\n    message = `an array of at least ${minLength} items`;\n  } else if (minLength === maxLength) {\n    message = `an array of exactly ${minLength} items`;\n  } else if (minLength === 0) {\n    message = `an array of no more than ${maxLength} items`;\n  } else {\n    message = `an array of at least ${minLength} and no more than ${maxLength} items`;\n  }\n  return attachDescription(\n    (value) => isArray(value) && value.length >= minLength && value.length <= maxLength,\n    message\n  );\n};\nvar numberMin = (min, inclusive = true) => attachDescription(\n  (value) => isFiniteNumber(value) && (value > min || inclusive && value === min),\n  `a number greater than ${inclusive ? \"or equal to \" : \"\"}${min}`\n);\nvar numberRange = (min, max) => attachDescription(\n  (value) => isFiniteNumber(value) && value >= min && value <= max,\n  `a number between ${min} and ${max} inclusive`\n);\nvar positiveNumber = numberMin(0);\nvar positiveNumberNonZero = numberMin(0, false);\nvar ratio = numberRange(0, 1);\nvar lessThan = (otherField) => attachDescription(\n  (value, { options }) => !isComparable(value) || !isComparable(options[otherField]) || value < options[otherField],\n  `the value to be less than \\`${otherField}\\``\n);\nvar greaterThan = (otherField) => attachDescription(\n  (value, { options }) => !isComparable(value) || !isComparable(options[otherField]) || value > options[otherField],\n  `the value to be greater than \\`${otherField}\\``\n);\nfunction union(...allowed) {\n  if (isObject(allowed[0])) {\n    allowed = Object.values(allowed[0]);\n  }\n  const keywords = joinFormatted(allowed, \"or\", (value) => `'${value}'`);\n  return attachDescription((value) => allowed.includes(value), `a keyword such as ${keywords}`);\n}\nvar constant = (allowed) => attachDescription((value) => allowed === value, `the value ${JSON.stringify(allowed)}`);\nvar instanceOf = (instanceType, description) => attachDescription((value) => value instanceof instanceType, description ?? `an instance of ${instanceType.name}`);\nvar arrayOf = (validator, description) => attachDescription(\n  (value, context) => isArray(value) && value.every((v) => {\n    const result = validator(v, context);\n    delete context.result;\n    return result;\n  }),\n  description ?? `${validator[descriptionSymbol]} array`\n);\nvar arrayOfDefs = (defs, description = \"an object array\") => attachDescription((value, context) => {\n  if (!isArray(value))\n    return false;\n  const valid = [];\n  const errors = [];\n  for (let i = 0; i < value.length; i++) {\n    const indexPath = `${context.path}[${i}]`;\n    const result = validate(value[i], defs, indexPath);\n    errors.push(...result.errors);\n    if (!result.errors.some((error2) => error2.required && error2.path === indexPath)) {\n      valid.push(result.valid);\n    }\n  }\n  context.result = { valid, errors };\n  return true;\n}, description);\nvar typeUnion = (defs, description = \"an object\") => {\n  const typeValidator = partialDefs({ type: required(union(...Object.keys(defs))) });\n  return attachDescription((value, context) => {\n    if (typeValidator(value, context)) {\n      const type = value.type;\n      const typeDefs = { type: required(constant(type)), ...defs[type] };\n      const result = optionsDefs(typeDefs)(value, context);\n      if (context.result) {\n        for (const error2 of context.result.errors) {\n          error2.message += ` (type=\"${type}\")`;\n        }\n      }\n      return result;\n    }\n    return false;\n  }, description);\n};\n\n// packages/ag-charts-core/src/options/commonOptionsDefs.ts\nvar colorStop = optionsDefs({ color, stop: ratio }, \"\");\nvar colorStopsOrderValidator = attachDescription((value) => {\n  let lastStop = -Infinity;\n  for (const item of value) {\n    if (item?.stop != null) {\n      if (item.stop < lastStop) {\n        return false;\n      }\n      lastStop = item.stop;\n    }\n  }\n  return true;\n}, \"color stops to be defined in ascending order\");\nvar gradientColorStops = and(arrayLength(2), and(arrayOf(colorStop), colorStopsOrderValidator));\nvar gradientBounds = union(\"axis\", \"item\", \"series\");\nvar gradientStrict = typeUnion(\n  {\n    gradient: {\n      // @ts-expect-error undocumented options\n      gradient: union(\"linear\", \"radial\", \"conic\"),\n      bounds: gradientBounds,\n      colorStops: required(gradientColorStops),\n      rotation: number,\n      reverse: boolean\n    }\n  },\n  \"a gradient object with color stops\"\n);\nvar strokeOptionsDef = {\n  stroke: color,\n  strokeWidth: positiveNumber,\n  strokeOpacity: ratio\n};\nvar fillGradientDefaults = optionsDefs({\n  type: required(constant(\"gradient\")),\n  gradient: required(union(\"linear\", \"radial\", \"conic\")),\n  bounds: required(gradientBounds),\n  colorStops: required(or(gradientColorStops, and(arrayLength(2), arrayOf(color)))),\n  rotation: required(number),\n  reverse: required(boolean)\n});\nvar fillPatternDefaults = optionsDefs({\n  type: required(constant(\"pattern\")),\n  pattern: required(\n    union(\n      \"vertical-lines\",\n      \"horizontal-lines\",\n      \"forward-slanted-lines\",\n      \"backward-slanted-lines\",\n      \"circles\",\n      \"squares\",\n      \"triangles\",\n      \"diamonds\",\n      \"stars\",\n      \"hearts\",\n      \"crosses\"\n    )\n  ),\n  width: required(positiveNumber),\n  height: required(positiveNumber),\n  fill: required(color),\n  fillOpacity: required(ratio),\n  backgroundFill: required(color),\n  backgroundFillOpacity: required(ratio),\n  padding: required(positiveNumber),\n  rotation: required(number),\n  stroke: required(color),\n  strokeWidth: required(positiveNumber),\n  strokeOpacity: required(ratio)\n});\nvar gradientUndocumentedOpts = {\n  // @ts-expect-error undocumented option\n  gradient: union(\"linear\", \"radial\", \"conic\"),\n  bounds: gradientBounds,\n  reverse: boolean\n};\nvar patternUndocumentedOpts = {\n  // @ts-expect-error undocumented option\n  rotation: number,\n  padding: positiveNumber\n};\nvar colorObject = typeUnion(\n  {\n    gradient: {\n      ...gradientUndocumentedOpts,\n      colorStops: gradientColorStops,\n      rotation: number\n    },\n    pattern: {\n      ...patternUndocumentedOpts,\n      pattern: union(\n        \"vertical-lines\",\n        \"horizontal-lines\",\n        \"forward-slanted-lines\",\n        \"backward-slanted-lines\",\n        \"circles\",\n        \"squares\",\n        \"triangles\",\n        \"diamonds\",\n        \"stars\",\n        \"hearts\",\n        \"crosses\"\n      ),\n      width: positiveNumber,\n      height: positiveNumber,\n      fill: color,\n      fillOpacity: ratio,\n      backgroundFill: color,\n      backgroundFillOpacity: ratio,\n      ...strokeOptionsDef\n    }\n  },\n  \"a color object\"\n);\nvar colorUnion = or(color, colorObject);\nvar fillOptionsDef = {\n  fill: colorUnion,\n  fillOpacity: ratio\n};\nfillOptionsDef.fillGradientDefaults = fillGradientDefaults;\nfillOptionsDef.fillPatternDefaults = fillPatternDefaults;\nvar lineDashOptionsDef = {\n  lineDash: arrayOf(positiveNumber),\n  lineDashOffset: number\n};\nvar fontOptionsDef = {\n  color,\n  fontFamily: string,\n  fontSize: positiveNumber,\n  fontStyle: union(\"normal\", \"italic\", \"oblique\"),\n  fontWeight: or(positiveNumber, union(\"normal\", \"bold\", \"bolder\", \"lighter\"))\n};\n\n// packages/ag-charts-core/src/utils/arrays.ts\nfunction toArray(value) {\n  if (typeof value === \"undefined\") {\n    return [];\n  }\n  return Array.isArray(value) ? value : [value];\n}\nfunction unique(array2) {\n  return Array.from(new Set(array2));\n}\nfunction groupBy(array2, iteratee) {\n  return array2.reduce((result, item) => {\n    const groupKey = iteratee(item);\n    result[groupKey] ?? (result[groupKey] = []);\n    result[groupKey].push(item);\n    return result;\n  }, {});\n}\nfunction arraysEqual(a, b) {\n  if (a == null || b == null || a.length !== b.length) {\n    return false;\n  }\n  for (let i = 0; i < a.length; i++) {\n    if (Array.isArray(a[i]) && Array.isArray(b[i])) {\n      if (!arraysEqual(a[i], b[i])) {\n        return false;\n      }\n    } else if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction circularSliceArray(data, size, offset = 0) {\n  if (data.length === 0) {\n    return [];\n  }\n  const result = [];\n  for (let i = 0; i < size; i++) {\n    result.push(data.at((i + offset) % data.length));\n  }\n  return result;\n}\nfunction sortBasedOnArray(baseArray, orderArray) {\n  const orderMap = /* @__PURE__ */ new Map();\n  orderArray.forEach((item, index) => {\n    orderMap.set(item, index);\n  });\n  return baseArray.sort((a, b) => {\n    const indexA = orderMap.get(a) ?? Infinity;\n    const indexB = orderMap.get(b) ?? Infinity;\n    return indexA - indexB;\n  });\n}\n\n// packages/ag-charts-core/src/utils/binarySearch.ts\nfunction findMaxIndex(min, max, iteratee) {\n  if (min > max)\n    return;\n  let found;\n  while (max >= min) {\n    const index = Math.floor((max + min) / 2);\n    const value = iteratee(index);\n    if (value) {\n      found = index;\n      min = index + 1;\n    } else {\n      max = index - 1;\n    }\n  }\n  return found;\n}\nfunction findMinIndex(min, max, iteratee) {\n  if (min > max)\n    return;\n  let found;\n  while (max >= min) {\n    const index = Math.floor((max + min) / 2);\n    const value = iteratee(index);\n    if (value) {\n      found = index;\n      max = index - 1;\n    } else {\n      min = index + 1;\n    }\n  }\n  return found;\n}\nfunction findMaxValue(min, max, iteratee) {\n  if (min > max)\n    return;\n  let found;\n  while (max >= min) {\n    const index = Math.floor((max + min) / 2);\n    const value = iteratee(index);\n    if (value == null) {\n      max = index - 1;\n    } else {\n      found = value;\n      min = index + 1;\n    }\n  }\n  return found;\n}\nfunction findMinValue(min, max, iteratee) {\n  if (min > max)\n    return;\n  let found;\n  while (max >= min) {\n    const index = Math.floor((max + min) / 2);\n    const value = iteratee(index);\n    if (value == null) {\n      min = index + 1;\n    } else {\n      found = value;\n      max = index - 1;\n    }\n  }\n  return found;\n}\n\n// packages/ag-charts-core/src/utils/diff.ts\nfunction diffArrays(previous, current) {\n  const size = Math.max(previous.length, current.length);\n  const added = /* @__PURE__ */ new Set();\n  const removed = /* @__PURE__ */ new Set();\n  for (let i = 0; i < size; i++) {\n    const prev = previous[i];\n    const curr = current[i];\n    if (prev === curr)\n      continue;\n    if (removed.has(curr)) {\n      removed.delete(curr);\n    } else if (curr) {\n      added.add(curr);\n    }\n    if (added.has(prev)) {\n      added.delete(prev);\n    } else if (prev) {\n      removed.add(prev);\n    }\n  }\n  return { changed: added.size > 0 || removed.size > 0, added, removed };\n}\n\n// packages/ag-charts-core/src/utils/functions.ts\nfunction debounce(callback2, waitMs = 0, options) {\n  const { leading = false, trailing = true, maxWait = Infinity } = options ?? {};\n  let timerId;\n  let startTime;\n  if (maxWait < waitMs) {\n    throw new Error(\"Value of maxWait cannot be lower than waitMs.\");\n  }\n  function debounceCallback(...args) {\n    if (leading && !startTime) {\n      startTime = Date.now();\n      timerId = setTimeout(() => startTime = null, waitMs);\n      callback2(...args);\n      return;\n    }\n    let adjustedWaitMs = waitMs;\n    if (maxWait !== Infinity && startTime) {\n      const elapsedTime = Date.now() - startTime;\n      if (waitMs > maxWait - elapsedTime) {\n        adjustedWaitMs = maxWait - elapsedTime;\n      }\n    }\n    clearTimeout(timerId);\n    startTime ?? (startTime = Date.now());\n    timerId = setTimeout(() => {\n      startTime = null;\n      if (trailing) {\n        callback2(...args);\n      }\n    }, adjustedWaitMs);\n  }\n  return Object.assign(debounceCallback, {\n    cancel() {\n      clearTimeout(timerId);\n      startTime = null;\n    }\n  });\n}\nfunction throttle(callback2, waitMs, options) {\n  const { leading = true, trailing = true } = options ?? {};\n  let timerId;\n  let lastArgs;\n  let shouldWait = false;\n  function timeoutHandler() {\n    if (trailing && lastArgs) {\n      timerId = setTimeout(timeoutHandler, waitMs);\n      callback2(...lastArgs);\n    } else {\n      shouldWait = false;\n    }\n    lastArgs = null;\n  }\n  function throttleCallback(...args) {\n    if (shouldWait) {\n      lastArgs = args;\n    } else {\n      shouldWait = true;\n      timerId = setTimeout(timeoutHandler, waitMs);\n      if (leading) {\n        callback2(...args);\n      } else {\n        lastArgs = args;\n      }\n    }\n  }\n  return Object.assign(throttleCallback, {\n    cancel() {\n      clearTimeout(timerId);\n      shouldWait = false;\n      lastArgs = null;\n    }\n  });\n}\n\n// packages/ag-charts-core/src/utils/iterators.ts\nfunction* iterate(...iterators) {\n  for (const iterator of iterators) {\n    yield* iterator;\n  }\n}\nfunction toIterable(value) {\n  return value != null && typeof value === \"object\" && Symbol.iterator in value ? value : [value];\n}\nfunction first(iterable) {\n  for (const value of iterable) {\n    return value;\n  }\n  throw new Error(\"AG Charts - no first() value found\");\n}\nfunction* entries(obj) {\n  const resultTuple = [void 0, void 0];\n  for (const key of Object.keys(obj)) {\n    resultTuple[0] = key;\n    resultTuple[1] = obj[key];\n    yield resultTuple;\n  }\n}\n\n// packages/ag-charts-core/src/utils/numbers.ts\nfunction clamp(min, value, max) {\n  return Math.min(max, Math.max(min, value));\n}\nfunction inRange(value, range, epsilon = 1e-10) {\n  return value >= range[0] - epsilon && value <= range[1] + epsilon;\n}\nfunction isNumberEqual(a, b, epsilon = 1e-10) {\n  return a === b || Math.abs(a - b) < epsilon;\n}\nfunction isNegative(value) {\n  return Math.sign(value) === -1 || Object.is(value, -0);\n}\nfunction isInteger(value) {\n  return value % 1 === 0;\n}\nfunction roundTo(value, decimals = 2) {\n  const base = 10 ** decimals;\n  return Math.round(value * base) / base;\n}\nfunction modulus(n, m) {\n  return Math.floor(n % m + (n < 0 ? Math.abs(m) : 0));\n}\nfunction countFractionDigits(value) {\n  if (Math.floor(value) === value) {\n    return 0;\n  }\n  let valueString = String(value);\n  let exponent = 0;\n  if (value < 1e-6 || value >= 1e21) {\n    let exponentString;\n    [valueString, exponentString] = valueString.split(\"e\");\n    if (exponentString != null) {\n      exponent = Number(exponentString);\n    }\n  }\n  const decimalPlaces = valueString.split(\".\")[1]?.length ?? 0;\n  return Math.max(decimalPlaces - exponent, 0);\n}\n\n// packages/ag-charts-core/src/utils/numberFormat.ts\nvar formatRegEx = /^(?:(.)?([<>=^]))?([+\\-( ])?([$€£¥₣₹#])?(0)?(\\d+)?(,)?(?:\\.(\\d+))?(~)?([%a-z])?$/i;\nvar surroundedRegEx = /^((?:[^#]|#[^{])*)#{([^}]+)}(.*)$/;\nfunction isValidNumberFormat(value) {\n  if (!isString(value))\n    return false;\n  const match = surroundedRegEx.exec(value);\n  return formatRegEx.test(match ? match[2] : value);\n}\nfunction parseNumberFormat(format) {\n  let prefix;\n  let suffix;\n  const surrounded = surroundedRegEx.exec(format);\n  if (surrounded) {\n    [, prefix, format, suffix] = surrounded;\n  }\n  const match = formatRegEx.exec(format);\n  if (!match) {\n    throw new Error(`The number formatter is invalid: ${format}`);\n  }\n  const [, fill, align, sign, symbol, zero, width, comma, precision, trim, type] = match;\n  return {\n    fill,\n    align,\n    sign,\n    symbol,\n    zero,\n    width: parseInt(width),\n    comma,\n    precision: parseInt(precision),\n    trim: Boolean(trim),\n    type,\n    prefix,\n    suffix\n  };\n}\nfunction createNumberFormatter(format) {\n  const options = typeof format === \"string\" ? parseNumberFormat(format) : format;\n  const { fill, align, sign = \"-\", symbol, zero, width, comma, type, prefix = \"\", suffix = \"\", precision } = options;\n  let { trim } = options;\n  const precisionIsNaN = precision == null || isNaN(precision);\n  let formatBody;\n  if (!type) {\n    formatBody = decimalTypes[\"g\"];\n    trim = true;\n  } else if (type in decimalTypes && type in integerTypes) {\n    formatBody = precisionIsNaN ? integerTypes[type] : decimalTypes[type];\n  } else if (type in decimalTypes) {\n    formatBody = decimalTypes[type];\n  } else if (type in integerTypes) {\n    formatBody = integerTypes[type];\n  } else {\n    throw new Error(`The number formatter type is invalid: ${type}`);\n  }\n  let formatterPrecision;\n  if (precisionIsNaN) {\n    formatterPrecision = type ? 6 : 12;\n  } else {\n    formatterPrecision = precision;\n  }\n  return (n) => {\n    let result = formatBody(n, formatterPrecision);\n    if (trim) {\n      result = removeTrailingZeros(result);\n    }\n    if (comma) {\n      result = insertSeparator(result, comma);\n    }\n    result = addSign(n, result, sign);\n    if (symbol && symbol !== \"#\") {\n      result = `${symbol}${result}`;\n    }\n    if (symbol === \"#\" && type === \"x\") {\n      result = `0x${result}`;\n    }\n    if (type === \"s\") {\n      result = `${result}${getSIPrefix(n)}`;\n    }\n    if (type === \"%\" || type === \"p\") {\n      result = `${result}%`;\n    }\n    if (width != null && !isNaN(width)) {\n      result = addPadding(result, width, fill ?? zero, align);\n    }\n    result = `${prefix}${result}${suffix}`;\n    return result;\n  };\n}\nvar integerTypes = {\n  b: (n) => absFloor(n).toString(2),\n  c: (n) => String.fromCharCode(n),\n  d: (n) => Math.round(Math.abs(n)).toFixed(0),\n  o: (n) => absFloor(n).toString(8),\n  x: (n) => absFloor(n).toString(16),\n  X: (n) => integerTypes.x(n).toUpperCase(),\n  n: (n) => integerTypes.d(n),\n  \"%\": (n) => `${absFloor(n * 100).toFixed(0)}`\n};\nvar decimalTypes = {\n  e: (n, f) => Math.abs(n).toExponential(f),\n  E: (n, f) => decimalTypes.e(n, f).toUpperCase(),\n  f: (n, f) => Math.abs(n).toFixed(f),\n  F: (n, f) => decimalTypes.f(n, f).toUpperCase(),\n  g: (n, f) => {\n    if (n === 0) {\n      return \"0\";\n    }\n    const a = Math.abs(n);\n    const p = Math.floor(Math.log10(a));\n    if (p >= -4 && p < f) {\n      return a.toFixed(f - 1 - p);\n    }\n    return a.toExponential(f - 1);\n  },\n  G: (n, f) => decimalTypes.g(n, f).toUpperCase(),\n  n: (n, f) => decimalTypes.g(n, f),\n  p: (n, f) => decimalTypes.r(n * 100, f),\n  r: (n, f) => {\n    if (n === 0) {\n      return \"0\";\n    }\n    const a = Math.abs(n);\n    const p = Math.floor(Math.log10(a));\n    const q = p - (f - 1);\n    if (q <= 0) {\n      return a.toFixed(-q);\n    }\n    const x = 10 ** q;\n    return (Math.round(a / x) * x).toFixed();\n  },\n  s: (n, f) => {\n    const p = getSIPrefixPower(n);\n    return decimalTypes.r(n / 10 ** p, f);\n  },\n  \"%\": (n, f) => decimalTypes.f(n * 100, f)\n};\nvar minSIPrefix = -24;\nvar maxSIPrefix = 24;\nvar siPrefixes = {\n  [minSIPrefix]: \"y\",\n  [-21]: \"z\",\n  [-18]: \"a\",\n  [-15]: \"f\",\n  [-12]: \"p\",\n  [-9]: \"n\",\n  [-6]: \"\\xB5\",\n  [-3]: \"m\",\n  [0]: \"\",\n  [3]: \"k\",\n  [6]: \"M\",\n  [9]: \"G\",\n  [12]: \"T\",\n  [15]: \"P\",\n  [18]: \"E\",\n  [21]: \"Z\",\n  [maxSIPrefix]: \"Y\"\n};\nvar minusSign = \"\\u2212\";\nfunction absFloor(n) {\n  return Math.floor(Math.abs(n));\n}\nfunction removeTrailingZeros(numString) {\n  return numString.replace(/\\.0+$/, \"\").replace(/(\\.[1-9])0+$/, \"$1\");\n}\nfunction insertSeparator(numString, separator) {\n  let dotIndex = numString.indexOf(\".\");\n  if (dotIndex < 0) {\n    dotIndex = numString.length;\n  }\n  const integerChars = numString.substring(0, dotIndex).split(\"\");\n  const fractionalPart = numString.substring(dotIndex);\n  for (let i = integerChars.length - 3; i > 0; i -= 3) {\n    integerChars.splice(i, 0, separator);\n  }\n  return `${integerChars.join(\"\")}${fractionalPart}`;\n}\nfunction getSIPrefix(n) {\n  return siPrefixes[getSIPrefixPower(n)];\n}\nfunction getSIPrefixPower(n) {\n  return clamp(minSIPrefix, n ? Math.floor(Math.log10(Math.abs(n)) / 3) * 3 : 0, maxSIPrefix);\n}\nfunction addSign(num, numString, signType = \"\") {\n  if (signType === \"(\") {\n    return num >= 0 ? numString : `(${numString})`;\n  }\n  const plusSign = signType === \"+\" ? \"+\" : \"\";\n  return `${num >= 0 ? plusSign : minusSign}${numString}`;\n}\nfunction addPadding(numString, width, fill = \" \", align = \">\") {\n  let result = numString;\n  if (align === \">\" || !align) {\n    result = result.padStart(width, fill);\n  } else if (align === \"<\") {\n    result = result.padEnd(width, fill);\n  } else if (align === \"^\") {\n    const padWidth = Math.max(0, width - result.length);\n    const padLeft = Math.ceil(padWidth / 2);\n    const padRight = Math.floor(padWidth / 2);\n    result = result.padStart(padLeft + result.length, fill);\n    result = result.padEnd(padRight + result.length, fill);\n  }\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-charts-core/dist/package/main.esm.mjs\n");

/***/ })

};
;