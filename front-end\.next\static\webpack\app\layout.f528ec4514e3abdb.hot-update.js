"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/GlobalModalContext.tsx":
/*!*********************************************!*\
  !*** ./src/contexts/GlobalModalContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalModalProvider: () => (/* binding */ GlobalModalProvider),\n/* harmony export */   useGlobalModal: () => (/* binding */ useGlobalModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! body-scroll-lock */ \"(app-pages-browser)/./node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js\");\n/* __next_internal_client_entry_do_not_use__ GlobalModalProvider,useGlobalModal auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst GlobalModalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GlobalModalProvider(param) {\n    let { children, defaultZIndex = 1000 } = param;\n    _s();\n    const [modals, setModals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const nextZIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultZIndex);\n    const modalRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // Generate unique modal ID\n    const generateModalId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[generateModalId]\": ()=>{\n            return \"global-modal-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n        }\n    }[\"GlobalModalProvider.useCallback[generateModalId]\"], []);\n    // Open modal\n    const openModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[openModal]\": (config)=>{\n            var _config_id;\n            const id = (_config_id = config.id) !== null && _config_id !== void 0 ? _config_id : generateModalId();\n            const zIndex = config.zIndex || nextZIndex.current;\n            var _config_closeOnBackdropClick, _config_closeOnEscape, _config_preventClose, _config_disableScroll;\n            const modalConfig = {\n                ...config,\n                id,\n                zIndex,\n                closeOnBackdropClick: (_config_closeOnBackdropClick = config.closeOnBackdropClick) !== null && _config_closeOnBackdropClick !== void 0 ? _config_closeOnBackdropClick : true,\n                closeOnEscape: (_config_closeOnEscape = config.closeOnEscape) !== null && _config_closeOnEscape !== void 0 ? _config_closeOnEscape : true,\n                preventClose: (_config_preventClose = config.preventClose) !== null && _config_preventClose !== void 0 ? _config_preventClose : false,\n                disableScroll: (_config_disableScroll = config.disableScroll) !== null && _config_disableScroll !== void 0 ? _config_disableScroll : true\n            };\n            setModals({\n                \"GlobalModalProvider.useCallback[openModal]\": (prev)=>[\n                        ...prev,\n                        modalConfig\n                    ]\n            }[\"GlobalModalProvider.useCallback[openModal]\"]);\n            nextZIndex.current = zIndex + 1;\n            return id;\n        }\n    }[\"GlobalModalProvider.useCallback[openModal]\"], [\n        generateModalId\n    ]);\n    // Close modal\n    const closeModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[closeModal]\": (id)=>{\n            setModals({\n                \"GlobalModalProvider.useCallback[closeModal]\": (prev)=>{\n                    const newModals = prev.filter({\n                        \"GlobalModalProvider.useCallback[closeModal].newModals\": (modal)=>modal.id !== id\n                    }[\"GlobalModalProvider.useCallback[closeModal].newModals\"]);\n                    // Enable body scroll if no modals are open\n                    if (newModals.length === 0) {\n                        (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.enableBodyScroll)(document.body);\n                    }\n                    return newModals;\n                }\n            }[\"GlobalModalProvider.useCallback[closeModal]\"]);\n            // Clean up modal ref\n            modalRefs.current.delete(id);\n        }\n    }[\"GlobalModalProvider.useCallback[closeModal]\"], []);\n    // Close all modals\n    const closeAllModals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[closeAllModals]\": ()=>{\n            setModals([]);\n            (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.clearAllBodyScrollLocks)();\n            modalRefs.current.clear();\n        }\n    }[\"GlobalModalProvider.useCallback[closeAllModals]\"], []);\n    // Check if modal is open\n    const isModalOpen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[isModalOpen]\": (id)=>{\n            return modals.some({\n                \"GlobalModalProvider.useCallback[isModalOpen]\": (modal)=>modal.id === id\n            }[\"GlobalModalProvider.useCallback[isModalOpen]\"]);\n        }\n    }[\"GlobalModalProvider.useCallback[isModalOpen]\"], [\n        modals\n    ]);\n    // Get modal by ID\n    const getModalById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[getModalById]\": (id)=>{\n            return modals.find({\n                \"GlobalModalProvider.useCallback[getModalById]\": (modal)=>modal.id === id\n            }[\"GlobalModalProvider.useCallback[getModalById]\"]);\n        }\n    }[\"GlobalModalProvider.useCallback[getModalById]\"], [\n        modals\n    ]);\n    // Handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalModalProvider.useEffect\": ()=>{\n            const handleEscape = {\n                \"GlobalModalProvider.useEffect.handleEscape\": (event)=>{\n                    if (event.key === 'Escape' && modals.length > 0) {\n                        const topModal = modals[modals.length - 1];\n                        if (topModal.closeOnEscape && !topModal.preventClose) {\n                            var _topModal_onClose;\n                            var _topModal_id;\n                            closeModal((_topModal_id = topModal.id) !== null && _topModal_id !== void 0 ? _topModal_id : \"\");\n                            (_topModal_onClose = topModal.onClose) === null || _topModal_onClose === void 0 ? void 0 : _topModal_onClose.call(topModal);\n                        }\n                    }\n                }\n            }[\"GlobalModalProvider.useEffect.handleEscape\"];\n            if (modals.length > 0) {\n                document.addEventListener('keydown', handleEscape);\n            }\n            return ({\n                \"GlobalModalProvider.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                }\n            })[\"GlobalModalProvider.useEffect\"];\n        }\n    }[\"GlobalModalProvider.useEffect\"], [\n        modals,\n        closeModal\n    ]);\n    // Handle backdrop click\n    const handleBackdropClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[handleBackdropClick]\": (modal, event)=>{\n            console.log(\"modal\", modal);\n            if (event.target === event.currentTarget && modal.closeOnBackdropClick && !modal.preventClose) {\n                var _modal_onClose;\n                var _modal_id;\n                closeModal((_modal_id = modal.id) !== null && _modal_id !== void 0 ? _modal_id : \"\");\n                (_modal_onClose = modal.onClose) === null || _modal_onClose === void 0 ? void 0 : _modal_onClose.call(modal);\n            }\n        }\n    }[\"GlobalModalProvider.useCallback[handleBackdropClick]\"], [\n        closeModal\n    ]);\n    // Handle modal ref for scroll lock\n    const handleModalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[handleModalRef]\": (modalId, element)=>{\n            if (element) {\n                modalRefs.current.set(modalId, element);\n                const modal = getModalById(modalId);\n                if (modal === null || modal === void 0 ? void 0 : modal.disableScroll) {\n                    (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.disableBodyScroll)(element);\n                }\n            }\n        }\n    }[\"GlobalModalProvider.useCallback[handleModalRef]\"], [\n        getModalById\n    ]);\n    const value = {\n        modals,\n        openModal,\n        closeModal,\n        closeAllModals,\n        isModalOpen,\n        getModalById\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlobalModalContext.Provider, {\n        value: value,\n        children: [\n            children,\n            modals.length > 0 && \"object\" !== 'undefined' && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"global-modal-portal\",\n                children: modals.map((modal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 flex items-center justify-center \".concat(modal.backdropClassName || 'bg-black/50 backdrop-blur-sm'),\n                        style: {\n                            zIndex: modal.zIndex\n                        },\n                        onClick: (e)=>handleBackdropClick(modal, e),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: (el)=>{\n                                var _modal_id;\n                                return handleModalRef((_modal_id = modal.id) !== null && _modal_id !== void 0 ? _modal_id : \"\", el);\n                            },\n                            className: modal.modalClassName || '',\n                            onClick: (e)=>e.stopPropagation(),\n                            children: modal.component\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, this)\n                    }, modal.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this), document.body)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(GlobalModalProvider, \"5G+h1NVLQqe+Vs6oZJtRTz0dRIM=\");\n_c = GlobalModalProvider;\nfunction useGlobalModal() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GlobalModalContext);\n    if (context === undefined) {\n        throw new Error('useGlobalModal must be used within a GlobalModalProvider');\n    }\n    return context;\n}\n_s1(useGlobalModal, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"GlobalModalProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0fb0c040a0dc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGZiMGMwNDBhMGRjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});