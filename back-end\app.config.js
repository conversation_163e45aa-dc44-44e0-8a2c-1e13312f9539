/** @format */

const dotenv = require('dotenv');
dotenv.config()
const ADMIN_PK =  process.env.ADMIN_PK;


const config = {
  theRpcIndexToUse : {
    bnb: 0,
    eth: 0,
    pls: 0,
    tbnb: 0,
  },
  networks: {
    eth: {
      urls: [
        "https://mainnet.infura.io/v3/********************************",
        "https://mainnet.infura.io/v3/********************************",
        "https://mainnet.infura.io/v3/********************************",
        "https://mainnet.infura.io/v3/********************************",
        "https://mainnet.infura.io/v3/********************************",
        "https://mainnet.infura.io/v3/********************************",
      ],
      wssUrl: "wss://mainnet.infura.io/v3/********************************",
      network_id: 1,
      chainId: 1,
      maxBlockPerQuery: 500,
      nativeToken: "eth",
      otherToken: {
        "******************************************": "usdt",
        "******************************************": "usdc",
      },
    },
  },

  errorFile: {
    default: "error.js",
    apiError: "errorApi.error",
    dbError: "errorDb.error",
    modelError: "errorModel.error",
    controllerError: "errorController.error",
    servicesError: "errorServices.error",
    utilitiesError: "errorUtilities.error",
    blockSkippedError: "errorBlockSkipped.error",
  },
  solTokenToUpdatePrice: [
    { name: "sol", token: "SOL" },
    { name: "usdc", token: "USDC" },
  ],
  serviceErrorLimit: 3,
  serviceTimeoutWhenError: 5000,
  serviceTimeout: 5_000, // 1 minutes,
  sacrificeStart: 1723012930925, //The timestamp in seconds of when the sacrifice should start
  bonus: [
    {
      name: "Bronze",
      thresold: 0,
      multiplier: 0.03,
    },
    {
      name: "Silver",
      thresold: 1000,
      multiplier: 0.05,
    },
    {
      name: "Gold",
      thresold: 5000,
      multiplier: 0.07,
    },
  ],
  initialTokenPrice: 100,
  referralBonusQualifier: 100,
  referralBonusMultiplier: 1.1,
  referrerBonusMultiplier: 0.1,
  totalSupply: 800_000_000,
  adminPrivateKey: ADMIN_PK,
};

module.exports = config;
