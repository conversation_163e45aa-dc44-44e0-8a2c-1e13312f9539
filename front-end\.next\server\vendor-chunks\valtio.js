"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/valtio";
exports.ids = ["vendor-chunks/valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/valtio/esm/vanilla.mjs":
/*!*********************************************!*\
  !*** ./node_modules/valtio/esm/vanilla.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   proxy: () => (/* binding */ proxy),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   snapshot: () => (/* binding */ snapshot),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unstable_buildProxyFunction: () => (/* binding */ unstable_buildProxyFunction)\n/* harmony export */ });\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! proxy-compare */ \"(ssr)/./node_modules/proxy-compare/dist/index.modern.js\");\n\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const { enumerable } = Reflect.getOwnPropertyDescriptor(\n      target,\n      key\n    );\n    const desc = {\n      value,\n      enumerable,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if (( false ? 0 : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if (( false ? 0 : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.getUntracked)(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs":
/*!***************************************************!*\
  !*** ./node_modules/valtio/esm/vanilla/utils.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addComputed: () => (/* binding */ addComputed_DEPRECATED),\n/* harmony export */   derive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   proxyMap: () => (/* binding */ proxyMap),\n/* harmony export */   proxySet: () => (/* binding */ proxySet),\n/* harmony export */   proxyWithComputed: () => (/* binding */ proxyWithComputed_DEPRECATED),\n/* harmony export */   proxyWithHistory: () => (/* binding */ proxyWithHistory_DEPRECATED),\n/* harmony export */   subscribeKey: () => (/* binding */ subscribeKey),\n/* harmony export */   underive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.underive),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.unstable_deriveSubscriptions),\n/* harmony export */   watch: () => (/* binding */ watch)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\n/* harmony import */ var derive_valtio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! derive-valtio */ \"(ssr)/./node_modules/derive-valtio/dist/index.modern.js\");\n\n\n\n\nfunction subscribeKey(proxyObject, key, callback, notifyInSync) {\n  let prevValue = proxyObject[key];\n  return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(\n    proxyObject,\n    () => {\n      const nextValue = proxyObject[key];\n      if (!Object.is(prevValue, nextValue)) {\n        callback(prevValue = nextValue);\n      }\n    },\n    notifyInSync\n  );\n}\n\nlet currentCleanups;\nfunction watch(callback, options) {\n  let alive = true;\n  const cleanups = /* @__PURE__ */ new Set();\n  const subscriptions = /* @__PURE__ */ new Map();\n  const cleanup = () => {\n    if (alive) {\n      alive = false;\n      cleanups.forEach((clean) => clean());\n      cleanups.clear();\n      subscriptions.forEach((unsubscribe) => unsubscribe());\n      subscriptions.clear();\n    }\n  };\n  const revalidate = async () => {\n    if (!alive) {\n      return;\n    }\n    cleanups.forEach((clean) => clean());\n    cleanups.clear();\n    const proxiesToSubscribe = /* @__PURE__ */ new Set();\n    const parent = currentCleanups;\n    currentCleanups = cleanups;\n    try {\n      const promiseOrPossibleCleanup = callback((proxyObject) => {\n        proxiesToSubscribe.add(proxyObject);\n        if (alive && !subscriptions.has(proxyObject)) {\n          const unsubscribe = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, revalidate, options == null ? void 0 : options.sync);\n          subscriptions.set(proxyObject, unsubscribe);\n        }\n        return proxyObject;\n      });\n      const couldBeCleanup = promiseOrPossibleCleanup && promiseOrPossibleCleanup instanceof Promise ? await promiseOrPossibleCleanup : promiseOrPossibleCleanup;\n      if (couldBeCleanup) {\n        if (alive) {\n          cleanups.add(couldBeCleanup);\n        } else {\n          cleanup();\n        }\n      }\n    } finally {\n      currentCleanups = parent;\n    }\n    subscriptions.forEach((unsubscribe, proxyObject) => {\n      if (!proxiesToSubscribe.has(proxyObject)) {\n        subscriptions.delete(proxyObject);\n        unsubscribe();\n      }\n    });\n  };\n  if (currentCleanups) {\n    currentCleanups.add(cleanup);\n  }\n  revalidate();\n  return cleanup;\n}\n\nconst DEVTOOLS = Symbol();\nfunction devtools(proxyObject, options) {\n  if (typeof options === \"string\") {\n    console.warn(\n      \"string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400\"\n    );\n    options = { name: options };\n  }\n  const { enabled, name = \"\", ...rest } = options || {};\n  let extension;\n  try {\n    extension = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extension) {\n    if (( false ? 0 : void 0) !== \"production\" && enabled) {\n      console.warn(\"[Warning] Please install/enable Redux devtools extension\");\n    }\n    return;\n  }\n  let isTimeTraveling = false;\n  const devtools2 = extension.connect({ name, ...rest });\n  const unsub1 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n    const action = ops.filter(([_, path]) => path[0] !== DEVTOOLS).map(([op, path]) => `${op}:${path.map(String).join(\".\")}`).join(\", \");\n    if (!action) {\n      return;\n    }\n    if (isTimeTraveling) {\n      isTimeTraveling = false;\n    } else {\n      const snapWithoutDevtools = Object.assign({}, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n      delete snapWithoutDevtools[DEVTOOLS];\n      devtools2.send(\n        {\n          type: action,\n          updatedAt: (/* @__PURE__ */ new Date()).toLocaleString()\n        },\n        snapWithoutDevtools\n      );\n    }\n  });\n  const unsub2 = devtools2.subscribe((message) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (message.type === \"ACTION\" && message.payload) {\n      try {\n        Object.assign(proxyObject, JSON.parse(message.payload));\n      } catch (e) {\n        console.error(\n          \"please dispatch a serializable value that JSON.parse() and proxy() support\\n\",\n          e\n        );\n      }\n    }\n    if (message.type === \"DISPATCH\" && message.state) {\n      if (((_a = message.payload) == null ? void 0 : _a.type) === \"JUMP_TO_ACTION\" || ((_b = message.payload) == null ? void 0 : _b.type) === \"JUMP_TO_STATE\") {\n        isTimeTraveling = true;\n        const state = JSON.parse(message.state);\n        Object.assign(proxyObject, state);\n      }\n      proxyObject[DEVTOOLS] = message;\n    } else if (message.type === \"DISPATCH\" && ((_c = message.payload) == null ? void 0 : _c.type) === \"COMMIT\") {\n      devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    } else if (message.type === \"DISPATCH\" && ((_d = message.payload) == null ? void 0 : _d.type) === \"IMPORT_STATE\") {\n      const actions = (_e = message.payload.nextLiftedState) == null ? void 0 : _e.actionsById;\n      const computedStates = ((_f = message.payload.nextLiftedState) == null ? void 0 : _f.computedStates) || [];\n      isTimeTraveling = true;\n      computedStates.forEach(({ state }, index) => {\n        const action = actions[index] || \"No action found\";\n        Object.assign(proxyObject, state);\n        if (index === 0) {\n          devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        } else {\n          devtools2.send(action, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        }\n      });\n    }\n  });\n  devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n  return () => {\n    unsub1();\n    unsub2 == null ? void 0 : unsub2();\n  };\n}\n\nfunction addComputed_DEPRECATED(proxyObject, computedFns_FAKE, targetObject = proxyObject) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"addComputed is deprecated. Please consider using `derive`. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201\"\n    );\n  }\n  const derivedFns = {};\n  Object.keys(computedFns_FAKE).forEach((key) => {\n    derivedFns[key] = (get) => computedFns_FAKE[key](get(proxyObject));\n  });\n  return (0,derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive)(derivedFns, { proxy: targetObject });\n}\n\nfunction proxyWithComputed_DEPRECATED(initialObject, computedFns) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithComputed is deprecated. Please follow \"Computed Properties\" guide in docs.'\n    );\n  }\n  Object.keys(computedFns).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(initialObject, key)) {\n      throw new Error(\"object property already defined\");\n    }\n    const computedFn = computedFns[key];\n    const { get, set } = typeof computedFn === \"function\" ? { get: computedFn } : computedFn;\n    const desc = {};\n    desc.get = () => get((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    if (set) {\n      desc.set = (newValue) => set(proxyObject, newValue);\n    }\n    Object.defineProperty(initialObject, key, desc);\n  });\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(initialObject);\n  return proxyObject;\n}\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nlet refSet;\nconst deepClone = (obj) => {\n  if (!refSet) {\n    refSet = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.unstable_buildProxyFunction)()[2];\n  }\n  if (!isObject(obj) || refSet.has(obj)) {\n    return obj;\n  }\n  const baseObject = Array.isArray(obj) ? [] : Object.create(Object.getPrototypeOf(obj));\n  Reflect.ownKeys(obj).forEach((key) => {\n    baseObject[key] = deepClone(obj[key]);\n  });\n  return baseObject;\n};\nfunction proxyWithHistory_DEPRECATED(initialValue, skipSubscribe = false) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithHistory is deprecated. Please use the \"valtio-history\" package; refer to the docs'\n    );\n  }\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    value: initialValue,\n    history: (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.ref)({\n      wip: void 0,\n      // to avoid infinite loop\n      snapshots: [],\n      index: -1\n    }),\n    clone: deepClone,\n    canUndo: () => proxyObject.history.index > 0,\n    undo: () => {\n      if (proxyObject.canUndo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[--proxyObject.history.index]\n        );\n      }\n    },\n    canRedo: () => proxyObject.history.index < proxyObject.history.snapshots.length - 1,\n    redo: () => {\n      if (proxyObject.canRedo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[++proxyObject.history.index]\n        );\n      }\n    },\n    saveHistory: () => {\n      proxyObject.history.snapshots.splice(proxyObject.history.index + 1);\n      proxyObject.history.snapshots.push((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject).value);\n      ++proxyObject.history.index;\n    },\n    subscribe: () => (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n      if (ops.every(\n        (op) => op[1][0] === \"value\" && (op[0] !== \"set\" || op[2] !== proxyObject.history.wip)\n      )) {\n        proxyObject.saveHistory();\n      }\n    })\n  });\n  proxyObject.saveHistory();\n  if (!skipSubscribe) {\n    proxyObject.subscribe();\n  }\n  return proxyObject;\n}\n\nfunction proxySet(initialValues) {\n  const set = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(new Set(initialValues)),\n    has(value) {\n      return this.data.indexOf(value) !== -1;\n    },\n    add(value) {\n      let hasProxy = false;\n      if (typeof value === \"object\" && value !== null) {\n        hasProxy = this.data.indexOf((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(value)) !== -1;\n      }\n      if (this.data.indexOf(value) === -1 && !hasProxy) {\n        this.data.push(value);\n      }\n      return this;\n    },\n    delete(value) {\n      const index = this.data.indexOf(value);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    forEach(cb) {\n      this.data.forEach((value) => {\n        cb(value, value, this);\n      });\n    },\n    get [Symbol.toStringTag]() {\n      return \"Set\";\n    },\n    toJSON() {\n      return new Set(this.data);\n    },\n    [Symbol.iterator]() {\n      return this.data[Symbol.iterator]();\n    },\n    values() {\n      return this.data.values();\n    },\n    keys() {\n      return this.data.values();\n    },\n    entries() {\n      return new Set(this.data).entries();\n    }\n  });\n  Object.defineProperties(set, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(set);\n  return set;\n}\n\nfunction proxyMap(entries) {\n  const map = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(entries || []),\n    has(key) {\n      return this.data.some((p) => p[0] === key);\n    },\n    set(key, value) {\n      const record = this.data.find((p) => p[0] === key);\n      if (record) {\n        record[1] = value;\n      } else {\n        this.data.push([key, value]);\n      }\n      return this;\n    },\n    get(key) {\n      var _a;\n      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];\n    },\n    delete(key) {\n      const index = this.data.findIndex((p) => p[0] === key);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    toJSON() {\n      return new Map(this.data);\n    },\n    forEach(cb) {\n      this.data.forEach((p) => {\n        cb(p[1], p[0], this);\n      });\n    },\n    keys() {\n      return this.data.map((p) => p[0]).values();\n    },\n    values() {\n      return this.data.map((p) => p[1]).values();\n    },\n    entries() {\n      return new Map(this.data).entries();\n    },\n    get [Symbol.toStringTag]() {\n      return \"Map\";\n    },\n    [Symbol.iterator]() {\n      return this.entries();\n    }\n  });\n  Object.defineProperties(map, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(map);\n  return map;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs\n");

/***/ })

};
;