"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { useTranslation } from '../../../hooks/useTranslation';

interface DetailProps {
  productName?: string;
  category?: string;
  imageUrl?: string;
  backgroundColor?: string;
}

const Detail: React.FC<DetailProps> = ({
  productName = "Water bottle",
  category = "Perks",
  imageUrl,
  backgroundColor = "#E5E5E5",
}) => {
  const { t } = useTranslation();
  return (
    <div className="w-full">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center space-x-2 mb-4 text-sm font-medium">
        <Link href="/perks-shop" className="text-gray-600 hover:text-gray-900 rounded-full  px-5 py-2 text-[16px] border font-['Inter']">
          {category}
        </Link>
        <span className="text-gray-400 ">/</span>
        <Link href={`/perks-shop/${productName.toLowerCase().replace(/\s+/g, '-')}`} className="text-gray-600 hover:text-gray-900 rounded-full  px-5 py-2 text-[16px] border font-['Inter']">
          {productName}
        </Link>
        <span className="text-gray-400">/</span>
        <span className="bg-gray-100 px-5 py-2 rounded-full text-gray-900 font-[600] text-[16px] leading-[120%] tracking-[-0.01em] border border-[#1111111A] font-['Inter']">
          {t('detail.breadcrumb')}
        </span>
      </div>

      {/* Product Image Container */}
      <div 
        className="w-full h-[600px] rounded-lg overflow-hidden"
        style={{ backgroundColor }}
      >
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={productName}
            width={975}
            height={600}
            className="w-full h-full object-contain py-6"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            {t('detail.productImage')}
          </div>
        )}
      </div>
    </div>
  );
};

export default Detail;
