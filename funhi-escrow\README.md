# Funhi Protocol
The protocol's core feature is an automated bonding curve that governs a token's price based on its circulating supply. This eliminates the need for manual liquidity pool creation and prevents common "rug pull" scams.

A key differentiator of the Funhi protocol is its built-in **linear vesting schedule for creator tokens**, ensuring long-term alignment between the project creator and the community.

## Table of Contents

- [Core Features](#core-features)
- [How It Works: The Lifecycle of a Token](#how-it-works-the-lifecycle-of-a-token)
- [Smart Contract Architecture](#smart-contract-architecture)
  - [State Accounts](#state-accounts)
  - [Key Instructions](#key-instructions)
- [Getting Started (Local Development)](#getting-started-local-development)
  - [Prerequisites](#prerequisites)
  - [Installation & Setup](#installation--setup)
- [Future Work](#future-work)

## Core Features

-   **Instant Token Creation:** Deploy a new SPL token with metadata for a minimal fee.
-   **Automated Bonding Curve:** The token's price is managed by a constant product formula (`x * y = k`), providing instant liquidity and fair price discovery.
-   **Creator Vesting Schedule:** A portion of the total supply is locked in a vault for the token creator. These tokens unlock linearly at a rate of **1% per day over 100 days**, promoting long-term project commitment.
-   **Integrated Fee Mechanism:** A small, configurable fee is applied to buys and sells to support the platform.
-   **Graduation Ready:** Includes a mechanism to "complete" the bonding curve once a certain liquidity threshold is met, preparing it for a future migration to a full-fledged DEX like Raydium.

## How It Works: The Lifecycle of a Token

#### Phase 1: Creation & Bonding Curve Trading

1.  **Creation:** A user (the "creator") calls the `create` instruction, providing a name, symbol, and image URI. The program deploys a new SPL Mint and distributes the total supply between two accounts:
    -   The majority of tokens go to the `BondingCurve`'s token account to serve as the market's inventory.
    -   A smaller, fixed portion goes into a `CreatorVault`, which is locked.
2.  **Trading:** Users can now `buy` and `sell` the token directly against the bonding curve.
    -   **Buying:** Users send SOL and receive tokens. This increases the SOL in the curve's treasury and pushes the token price up.
    -   **Selling:** Users send tokens and receive SOL. This decreases the SOL in the curve's treasury and moves the token price down.
3.  **Vesting:** The creator can call the `withdraw_unlocked` instruction at any time. The contract calculates how much time has passed since creation and allows them to withdraw their prorated share of vested tokens.

#### Phase 2: Graduation (Future Work)

-   Once the amount of SOL in the bonding curve's treasury (`real_sol_reserves`) reaches the `graduation_threshold`, the `complete` flag on the `BondingCurve` account is set to `true`.
-   This freezes the `buy` and `sell` instructions, effectively locking the market.
-   The next step (to be implemented) would be to use the collected SOL and the remaining tokens in the bonding curve to create and seed a liquidity pool on a DEX.

## Smart Contract Architecture

The program is built using the Anchor framework. The source code is located in `programs/funhi/src/`.

### State Accounts

The on-chain state is managed by three primary accounts defined in `programs/funhi/src/account.rs`:

1.  **`Global`**
    -   A singleton account that holds program-wide configuration.
    -   `authority`: The admin key that can change settings.
    -   `fee_recipient`: The address that receives trading fees.
    -   `fee_basis_points`: The trading fee (e.g., 100 = 1%).
    -   `graduation_threshold`: The SOL amount required to complete the curve.
    -   And other initial reserve parameters.

2.  **`BondingCurve`**
    -   An account created for each new token, acting as its dedicated market.
    -   `mint`: The Pubkey of the token this curve manages.
    -   `virtual_sol_reserves` & `virtual_token_reserves`: The parameters for the `x*y=k` pricing formula.
    -   `real_sol_reserves` & `real_token_reserves`: The actual inventory of SOL and tokens held by the curve.
    -   `complete`: A boolean flag that freezes the market upon graduation.

3.  **`CreatorVault`**
    -   An account created for each new token to manage the creator's vested tokens.
    -   `owner`: The creator's Pubkey.
    -   `deposited_amount`: The total number of tokens locked at creation.
    -   `withdrawn_amount`: A running total of tokens the creator has withdrawn.
    -   `creation_ts`: A Unix timestamp marking the moment the vesting schedule begins.

### Key Instructions

The core logic is exposed through the following instructions in `programs/funhi/src/lib.rs`:

-   `initialize(...)`: Called once by the program authority to set up the `Global` state.
-   `create(name, symbol, uri)`: Deploys a new token, `BondingCurve`, and `CreatorVault`.
-   `buy(sol_amount, min_token_output)`: A user sends SOL to buy tokens from the curve.
-   `sell(token_amount, min_sol_output)`: A user sells tokens back to the curve to receive SOL.
-   `withdraw_unlocked()`: Called by the token creator to withdraw their vested tokens.

## Getting Started (Local Development)

Follow these steps to set up the project locally for development and testing.

### Prerequisites

-   **Rust & Cargo:** [https://www.rust-lang.org/tools/install](https://www.rust-lang.org/tools/install)
-   **Solana Tool Suite:** [https://docs.anza.xyz/cli/install#use-solanas-install-tool](https://docs.anza.xyz/cli/install#use-solanas-install-tool)
-   **Anchor Framework (v0.31.1 or compatible):** [https://www.anchor-lang.com/docs/installation](https://www.anchor-lang.com/docs/installation)
-   **Node.js & Yarn:** [https://nodejs.org/](https://nodejs.org/)

### Installation & Setup

1.  **Clone the repository:**

2.  **Install client-side dependencies:**
    ```bash
    yarn install
    ```

3.  **Build the smart contract:**
    ```bash
    anchor build
    ```

4.  **Run the tests:**
    Start local validator
    ```bash
    solana-test-validator --reset \
      --clone metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s \
      --clone-upgradeable-program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s \
      --url mainnet-beta
    ```
    The test suite in `tests/funhi.ts` covers most instructions.
    ```bash
    anchor test --skip-local-validator
    ```

## Future Work

-   **Graduation Logic:** Implement an instruction to take a `complete` bonding curve and use its assets to create a liquidity pool on a DEX like Raydium.
