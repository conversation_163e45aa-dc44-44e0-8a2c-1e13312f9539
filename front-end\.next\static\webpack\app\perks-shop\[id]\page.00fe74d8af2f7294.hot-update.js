"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000, wallet = arguments.length > 4 ? arguments[4] : void 0, originalTransaction = arguments.length > 5 ? arguments[5] : void 0;\n    let lastError;\n    let currentSignedTx = signedTransaction;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(currentSignedTx.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Handle blockhash not found error by refreshing blockhash and re-signing\n            if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Blockhash not found')) && wallet && originalTransaction && attempt < maxRetries) {\n                console.log(\"\\uD83D\\uDD04 Blockhash expired, getting fresh blockhash and re-signing...\");\n                try {\n                    // Get fresh blockhash\n                    const { blockhash } = await connection.getLatestBlockhash('finalized');\n                    originalTransaction.recentBlockhash = blockhash;\n                    // Re-sign the transaction with fresh blockhash\n                    currentSignedTx = await wallet.signTransaction(originalTransaction);\n                    console.log(\"✅ Transaction re-signed with fresh blockhash\");\n                    continue;\n                } catch (resignError) {\n                    console.error(\"❌ Failed to re-sign transaction:\", resignError);\n                // Fall through to normal retry logic\n                }\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(escrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(escrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_tokenDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1, _tradeData_tokenDetails1, _tradeData_perkDetails_token1, _tradeData_perkDetails2;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price,\n                    perkDetails: tradeData.perkDetails,\n                    perkTokenMint: tradeData.perkTokenMint\n                });\n                // Use trade data to get the numeric escrowId\n                // Priority: 1. tradeData.escrowId, 2. extra field, 3. original escrowId\n                let numericEscrowId = null;\n                // First try the direct escrowId field from trade data\n                if (tradeData.escrowId) {\n                    // Validate that it's numeric before using it\n                    const escrowIdStr = String(tradeData.escrowId);\n                    if (/^\\d+$/.test(escrowIdStr)) {\n                        numericEscrowId = tradeData.escrowId;\n                    } else {\n                        console.warn('⚠️ Trade escrowId is not numeric:', tradeData.escrowId);\n                    }\n                } else {\n                    // Fall back to extra field\n                    try {\n                        if (tradeData.extra) {\n                            const extraData = JSON.parse(tradeData.extra);\n                            if (extraData.escrowId) {\n                                const escrowIdStr = String(extraData.escrowId);\n                                if (/^\\d+$/.test(escrowIdStr)) {\n                                    numericEscrowId = extraData.escrowId;\n                                } else {\n                                    console.warn('⚠️ Extra escrowId is not numeric:', extraData.escrowId);\n                                }\n                            }\n                        }\n                    } catch (e) {\n                        console.log('Could not parse extra data:', e);\n                    }\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                console.log('🔍 [EscrowAccept] EscrowId resolution:', {\n                    originalEscrowId: escrowId,\n                    tradeDataEscrowId: tradeData.escrowId,\n                    numericEscrowId,\n                    finalCalculatedEscrowId: calculatedEscrowId\n                });\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                // Try multiple sources for perk token mint\n                calculatedPerkMint = ((_tradeData_tokenDetails = tradeData.tokenDetails) === null || _tradeData_tokenDetails === void 0 ? void 0 : _tradeData_tokenDetails.tokenAddress) || ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || tradeData.perkTokenMint || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Perk token mint resolution:', {\n                    'tokenDetails.tokenAddress': (_tradeData_tokenDetails1 = tradeData.tokenDetails) === null || _tradeData_tokenDetails1 === void 0 ? void 0 : _tradeData_tokenDetails1.tokenAddress,\n                    'perkDetails.token.tokenAddress': (_tradeData_perkDetails2 = tradeData.perkDetails) === null || _tradeData_perkDetails2 === void 0 ? void 0 : (_tradeData_perkDetails_token1 = _tradeData_perkDetails2.token) === null || _tradeData_perkDetails_token1 === void 0 ? void 0 : _tradeData_perkDetails_token1.tokenAddress,\n                    'tradeData.perkTokenMint': tradeData.perkTokenMint,\n                    'original perkTokenMint': perkTokenMint,\n                    'final calculatedPerkMint': calculatedPerkMint\n                });\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required but not found. Please ensure the trade has a valid escrowId. TradeId: \".concat(tradeId), \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        if (!calculatedPerkMint) {\n            throw new EscrowError(\"Perk token mint address is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            perkTokenAta: sellerPerkTokenAta,\n            purchase: purchasePda,\n            vault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey; // Buyer pays the fees and is the signer\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});