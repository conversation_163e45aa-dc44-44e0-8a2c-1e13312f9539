import React from 'react';
import { useTranslation } from '../../../hooks/useTranslation';


const ProfileDetailsSection = ({
  profileData,
  setProfileData
}: {
  profileData: { value: { name: string; email: string; bio: string }; errors: { name: string; email: string; bio: string } };
  setProfileData: React.Dispatch<React.SetStateAction<{ value: { name: string; email: string; bio: string }; errors: { name: string; email: string; bio: string } }>>;
}) => {
  const { t } = useTranslation();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateName = (name: string) => {
    const nameRegex = /^[a-zA-Zà-žÀ-Ž''\- ]{2,50}$/;
    return nameRegex.test(name);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, value: { ...prev.value, [name]: value } })); // 🔥 Update parent state

    // Clear errors when typing
    if (profileData.errors[name as keyof typeof profileData.errors]) {
      setProfileData(prev => ({ ...prev, errors: { ...prev.errors, [name]: '' } }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'name') {
      if (value.trim() && !validateName(value)) {
        setProfileData(prev => ({ ...prev, errors: { ...prev.errors, name: t('profileDetails.validationName') } }));
      }
    } else if (name === 'email') {
      if (value.trim() && !validateEmail(value)) {
        setProfileData(prev => ({ ...prev, errors: { ...prev.errors, email: t('profileDetails.validationEmail') } }));
      }
    }
  };

  return (
    <div>
      <div className="flex flex-wrap gap-6">
        {/* Name Field */}
        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-black mb-2">
            {t('profileDetails.name')}
          </label>
          <input
            name="name"
            type="text"
            value={profileData.value.name} // use prop
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full border ${profileData.errors.name ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 py-4 focus:outline-none focus:ring-2 focus:ring-blue-500 font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8]`}
            placeholder={t('profileDetails.inputPlaceholder')}
          />
          {profileData.errors.name && <p className="mt-1 text-red-500 text-sm">{profileData.errors.name}</p>}
        </div>

        {/* Email Field */}
        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-black mb-2">
            {t('profileDetails.email')}
          </label>
          <input
            name="email"
            type="email"
            value={profileData.value.email} // use prop
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full border ${profileData.errors.email ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 py-4 focus:outline-none focus:ring-2 focus:ring-blue-500 font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8]`}
            placeholder={t('profileDetails.inputPlaceholder')}
          />
          {profileData.errors.email && <p className="mt-1 text-red-500 text-sm">{profileData.errors.email}</p>}
        </div>
      </div>

      {/* Bio Field */}
      <div className="flex flex-wrap gap-6">
        <div className="mt-6 flex-1">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-black mb-2">
            {t('profileDetails.bio')}
          </label>
          <textarea
            name="bio"
            value={profileData.value.bio} // use prop
            onChange={handleChange}
            className="w-full border border-[#D9E1E7] rounded-[10px] px-4 py-4 focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px] font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8]"
            placeholder={t('profileDetails.inputPlaceholder')}
          />
        </div>
        <div className="hidden lg:flex flex-1 w-full" />
      </div>
    </div>
  );
};

export default ProfileDetailsSection;
