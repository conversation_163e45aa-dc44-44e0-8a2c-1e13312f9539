// src/components/shared/perks-card/index.tsx
'use client';

import { motion } from 'framer-motion';
import { Heart, CheckCircle } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

import { fadeInUp } from '@/lib/animations';

interface PerksCardProps {
  id: string | number;
  name: string;
  price: string;
  timeLeft?: string;
  username: string;
  handle: string;
  isLive?: boolean;
  soldCount?: number;
  remainingCount?: number;
  imageUrl?: string;
  isVerified?: boolean;
  onBuyClick?: () => void;
  compact?: boolean; // New prop for compact version
}

const PerksCard: React.FC<PerksCardProps> = ({
  id,
  name = 'ITEM NAME',
  price = '2.33 USD',
  timeLeft = '2h : 4m : 32s',
  username = 'User',
  handle = '@user',
  isLive = true,
  soldCount = 0,
  remainingCount = 0,
  imageUrl = '/images/placeholder.png',
  isVerified = false,
  onBuyClick,
  compact = false,
}) => {
  const handleBuyClick = (e: React.MouseEvent) => {
    if (onBuyClick) {
      e.preventDefault();
      onBuyClick();
    }
  };

  if (compact) {
    // Compact version for Available Perks section
    return (
      <motion.div
        className="bg-white rounded-xl border border-[#F58A38]/20 overflow-hidden hover:border-[#F58A38] transition-all duration-300 hover:shadow-md group h-full flex flex-col"
        whileHover={{ y: -2, scale: 1.01 }}
        transition={{ duration: 0.2 }}
        {...fadeInUp}
      >
        {/* Image Section - Fixed height */}
        <div className="relative h-40 overflow-hidden flex-shrink-0">
          <Image
            src={imageUrl}
            alt={name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />

          {/* Overlay with name */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/10 to-transparent" />
          <div className="absolute bottom-2 left-3 right-3">
            <h3 className="text-white font-semibold text-sm leading-tight line-clamp-2">
              {name}
            </h3>
          </div>

          {/* Top right badges */}
          <div className="absolute top-2 right-2 flex items-center gap-1.5">
            {timeLeft && (
              <div className="bg-black/50 backdrop-blur-sm rounded-md px-2 py-0.5">
                <span className="text-white text-xs font-medium">
                  {timeLeft}
                </span>
              </div>
            )}
            {isLive && (
              <motion.button
                className="w-7 h-7 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Heart size={12} className="text-white" />
              </motion.button>
            )}
          </div>
        </div>

        {/* Content Section - Flexible height */}
        <div className="p-3 flex flex-col flex-1">
          {/* User Info and Buy Button */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <div className="w-7 h-7 bg-[#F58A38]/10 rounded-full flex items-center justify-center flex-shrink-0">
                <Image
                  src={imageUrl}
                  alt={username}
                  width={28}
                  height={28}
                  className="w-full h-full rounded-full object-cover"
                />
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-1">
                  <span className="font-medium text-gray-900 text-xs truncate">
                    {username}
                  </span>
                  {isVerified && (
                    <CheckCircle
                      size={10}
                      className="text-[#F58A38] flex-shrink-0"
                    />
                  )}
                </div>
                <span className="text-xs text-gray-500 truncate block">
                  {handle}
                </span>
              </div>
            </div>

            <motion.button
              onClick={handleBuyClick}
              className="bg-[#F58A38] text-white px-2.5 py-1 rounded-md text-xs font-medium hover:bg-[#FF6600] transition-colors flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Buy
            </motion.button>
          </div>

          {/* Price and Stats - Pushed to bottom */}
          <div className="flex justify-between items-end mt-auto">
            <div>
              <p className="text-xs text-gray-500 mb-0.5">Price</p>
              <p className="font-semibold text-gray-900 text-sm">{price}</p>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 mb-0.5">
                {remainingCount > 0 ? 'Available' : 'Made'}
              </p>
              <p className="font-semibold text-gray-900 text-xs">
                {remainingCount > 0 ? `${remainingCount}` : `${soldCount} Min`}
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Regular version (existing Card component logic with theme colors)
  return (
    <motion.div
      className="relative rounded-xl border border-[#F58A38] transition-all duration-300 ease-in-out cursor-pointer overflow-hidden group bg-white hover:shadow-lg"
      whileHover={{ y: -2, scale: 1.01 }}
      variants={fadeInUp}
    >
      <div className="p-4 flex flex-col gap-5">
        <motion.div
          className="w-full aspect-[1.55/1] rounded-lg overflow-hidden relative"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <div className="w-full aspect-[1.55/1] relative bg-gray-200">
            <Image
              src={imageUrl}
              alt={name}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <motion.div
              className="absolute bottom-4 left-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-white text-2xl font-semibold leading-none drop-shadow-lg">
                {name}
              </div>
            </motion.div>
          </div>

          <motion.div
            className="w-auto flex absolute right-[28px] top-[24px] items-center justify-center gap-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            {timeLeft && (
              <motion.div
                className="h-7 bg-white/30 rounded-lg backdrop-blur-[20px] flex items-center justify-center px-3"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-white text-xs font-semibold leading-normal">
                  {timeLeft}
                </div>
              </motion.div>
            )}
            {isLive && (
              <motion.div
                className="w-7 h-7 bg-white/30 rounded-[50px] backdrop-blur-[1.50px] flex items-center justify-center"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Heart size={16} className="text-white" />
              </motion.div>
            )}
          </motion.div>
        </motion.div>

        <div className="w-full h-[120px] flex flex-col gap-4">
          <div className="w-full h-14">
            <div className="w-full h-14 flex justify-between items-center">
              <div className="flex items-center gap-4">
                <motion.div
                  className="w-14 h-14 bg-gray-200 rounded-full relative overflow-hidden"
                  whileHover={{ scale: 1.05 }}
                >
                  <Image
                    src={imageUrl}
                    alt={username}
                    fill
                    className="rounded-full object-cover"
                  />
                </motion.div>
                <div className="flex flex-col">
                  <div className="text-neutral-900 text-base font-medium font-['Poppins'] capitalize flex items-center gap-2">
                    <span className="block overflow-hidden text-ellipsis whitespace-nowrap max-w-[100px] sm:max-w-[70px] md:max-w-[90px] lg:max-w-[40px] xl:max-w-[50px]">
                      {username}
                    </span>
                    {isVerified && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4, type: 'spring' }}
                      >
                        <CheckCircle size={16} className="text-[#F58A38]" />
                      </motion.div>
                    )}
                  </div>
                  <div className="text-neutral-400 text-sm font-semibold">
                    {handle}
                  </div>
                </div>
              </div>
              <div>
                <motion.button
                  onClick={handleBuyClick}
                  className="h-7 rounded-[40px] bg-[#F58A38] text-white transition-all duration-300 ease-in-out hover:bg-[#FF6600] px-6"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="text-white text-xs font-medium font-['Poppins'] capitalize cursor-pointer whitespace-nowrap text-center">
                    Buy
                  </div>
                </motion.button>
              </div>
            </div>
          </div>

          <motion.div
            className="flex justify-between items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div>
              <div className="text-neutral-400 text-sm font-semibold">
                Price
              </div>
              <div className="text-black text-base font-medium font-['Poppins']">
                {price}
              </div>
            </div>

            <div>
              <div className="text-right text-neutral-400 text-sm font-semibold">
                {remainingCount > 0 ? 'Remaining' : 'Made before'}
              </div>
              <div className="text-right text-black text-base font-medium font-['Poppins']">
                {remainingCount > 0
                  ? `${remainingCount} Items`
                  : `${soldCount} Min`}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default PerksCard;
