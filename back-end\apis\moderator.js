const { Router } = require("express");
const {
  addModerator,
  getModerators,
  getModeratorDashboard,
  updateModeratorStatus
} = require("../controller/moderator");
const { body, param, query } = require('express-validator');
const { validate } = require("../middleware/validator");
const { authenticate } = require("../middleware/authenticate");
const { requireModerator, requireAdmin } = require("../middleware/roleAuth");

const router = Router();

// Get moderator dashboard (for logged-in moderator)
router.get(
  "/dashboard",
  authenticate,
  requireModerator,
  getModeratorDashboard
);

// Get all moderators
router.get(
  "/",
  authenticate,
  [
    query("isActive")
      .optional()
      .isBoolean()
      .withMessage("isActive must be a boolean"),
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("pageSize")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Page size must be between 1 and 100"),
  ],
  validate,
  getModerators
);

// Add a new moderator (admin only)
router.post(
  "/add",
  authenticate,
  requireAdmin,
  [
    body("userId")
      .isInt({ min: 1 })
      .withMessage("User ID must be a positive integer"),
    body("specializations")
      .optional()
      .isArray()
      .withMessage("Specializations must be an array"),
    body("maxConcurrentDisputes")
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage("Max concurrent disputes must be between 1 and 50"),
  ],
  validate,
  addModerator
);

// Update moderator status
router.patch(
  "/:moderatorId/status",
  authenticate,
  requireAdmin,
  [
    param("moderatorId")
      .isInt({ min: 1 })
      .withMessage("Moderator ID must be a positive integer"),
    body("isActive")
      .isBoolean()
      .withMessage("isActive must be a boolean"),
  ],
  validate,
  updateModeratorStatus
);

module.exports = router;
