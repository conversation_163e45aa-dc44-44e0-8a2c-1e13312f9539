"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle, 
  Wifi, 
  Wallet, 
  DollarSign, 
  Clock, 
  RefreshCw, 
  X, 
  ChevronDown, 
  ChevronUp,
  ExternalLink,
  HelpCircle,
  Copy,
  CheckCircle
} from 'lucide-react';
import { EscrowOperationType } from '@/utils/escrow';

// Error Categories
export type ErrorCategory = 
  | 'INSUFFICIENT_FUNDS'
  | 'WALLET_CONNECTION'
  | 'NETWORK_ERROR'
  | 'TRANSACTION_TIMEOUT'
  | 'SMART_CONTRACT_ERROR'
  | 'USER_REJECTED'
  | 'DUPLICATE_TRANSACTION'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

// Error Configuration
const getErrorConfig = (category: ErrorCategory) => {
  const configs = {
    INSUFFICIENT_FUNDS: {
      icon: <DollarSign className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      title: 'Insufficient Funds',
      severity: 'high' as const
    },
    WALLET_CONNECTION: {
      icon: <Wallet className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      title: 'Wallet Connection Issue',
      severity: 'medium' as const
    },
    NETWORK_ERROR: {
      icon: <Wifi className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      title: 'Network Connection Error',
      severity: 'medium' as const
    },
    TRANSACTION_TIMEOUT: {
      icon: <Clock className="w-6 h-6" />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      title: 'Transaction Timeout',
      severity: 'medium' as const
    },
    SMART_CONTRACT_ERROR: {
      icon: <AlertTriangle className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      title: 'Smart Contract Error',
      severity: 'high' as const
    },
    USER_REJECTED: {
      icon: <X className="w-6 h-6" />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      title: 'Transaction Cancelled',
      severity: 'low' as const
    },
    DUPLICATE_TRANSACTION: {
      icon: <RefreshCw className="w-6 h-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      title: 'Duplicate Transaction',
      severity: 'medium' as const
    },
    VALIDATION_ERROR: {
      icon: <AlertTriangle className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      title: 'Validation Error',
      severity: 'medium' as const
    },
    UNKNOWN_ERROR: {
      icon: <HelpCircle className="w-6 h-6" />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      title: 'Unexpected Error',
      severity: 'medium' as const
    }
  };

  return configs[category];
};

// Error Modal Props
interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  category: ErrorCategory;
  operation: EscrowOperationType;
  message: string;
  details?: string;
  errorCode?: string;
  onRetry?: () => void;
  onHelp?: () => void;
  onProceedAnyway?: () => void;
  canRetry?: boolean;
  canProceedAnyway?: boolean;
  additionalInfo?: {
    currentBalance?: number;
    requiredAmount?: number;
    walletAddress?: string;
    transactionId?: string;
  };
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  category,
  operation,
  message,
  details,
  errorCode,
  onRetry,
  onHelp,
  onProceedAnyway,
  canRetry = true,
  canProceedAnyway = false,
  additionalInfo
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);
  
  const config = getErrorConfig(category);
  const operationLabels = {
    buy: 'Purchase',
    sell: 'Release',
    refund: 'Refund',
    dispute: 'Dispute'
  };

  const copyErrorCode = async () => {
    if (errorCode) {
      await navigator.clipboard.writeText(errorCode);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    }
  };

  const getContextualHelp = () => {
    const helpTexts = {
      INSUFFICIENT_FUNDS: {
        buy: "You need more SOL in your wallet to complete this purchase. Please add funds to your wallet and try again.",
        sell: "You need perk tokens to complete this release. Please ensure you have the required tokens.",
        refund: "There may be insufficient funds in the escrow account for the refund.",
        dispute: "You need a small amount of SOL to pay for the dispute transaction fee."
      },
      WALLET_CONNECTION: {
        buy: "Please reconnect your wallet and ensure it's unlocked before attempting the purchase.",
        sell: "Your wallet connection was lost. Please reconnect and try releasing the funds again.",
        refund: "Reconnect your wallet to process the refund.",
        dispute: "Please ensure your wallet is connected to submit the dispute."
      },
      NETWORK_ERROR: {
        buy: "Network connection is unstable. Please check your internet connection and try again.",
        sell: "Unable to connect to the blockchain. Please wait a moment and retry the release.",
        refund: "Network issues prevented the refund. Please try again when connection is stable.",
        dispute: "Network error occurred while submitting dispute. Please retry when connection improves."
      },
      TRANSACTION_TIMEOUT: {
        buy: "The purchase transaction took too long to process. It may still complete, or you can try again.",
        sell: "The release transaction timed out. Please check if it completed before retrying.",
        refund: "Refund transaction timed out. Please verify the status before attempting again.",
        dispute: "Dispute submission timed out. Please check if it was recorded before retrying."
      }
    };

    return helpTexts[category]?.[operation] || "Please try the operation again or contact support if the issue persists.";
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-start space-x-4">
              <div className={`p-3 rounded-full ${config.bgColor}`}>
                <div className={config.color}>
                  {config.icon}
                </div>
              </div>
              
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">
                  {config.title}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {operationLabels[operation]} operation failed
                </p>
              </div>
              
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Main Error Message */}
            <div className="mb-4">
              <p className="text-gray-800 font-medium mb-2">{message}</p>
              <p className="text-gray-600 text-sm">{getContextualHelp()}</p>
            </div>

            {/* Additional Information */}
            {additionalInfo && (
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Additional Information</h4>
                <div className="space-y-2 text-sm">
                  {additionalInfo.currentBalance !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Current Balance:</span>
                      <span className="font-medium">{additionalInfo.currentBalance.toFixed(4)} SOL</span>
                    </div>
                  )}
                  {additionalInfo.requiredAmount !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Required Amount:</span>
                      <span className="font-medium">{additionalInfo.requiredAmount.toFixed(4)} SOL</span>
                    </div>
                  )}
                  {additionalInfo.walletAddress && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Wallet:</span>
                      <span className="font-mono text-xs">
                        {additionalInfo.walletAddress.slice(0, 8)}...{additionalInfo.walletAddress.slice(-8)}
                      </span>
                    </div>
                  )}
                  {additionalInfo.transactionId && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Transaction ID:</span>
                      <span className="font-mono text-xs">
                        {additionalInfo.transactionId.slice(0, 8)}...{additionalInfo.transactionId.slice(-8)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Error Details Toggle */}
            {(details || errorCode) && (
              <div className="mb-4">
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <span className="text-sm font-medium">Technical Details</span>
                  {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                </button>
                
                <AnimatePresence>
                  {showDetails && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="mt-3 p-3 bg-gray-100 rounded-lg overflow-hidden"
                    >
                      {details && (
                        <div className="mb-3">
                          <p className="text-xs font-medium text-gray-700 mb-1">Error Details:</p>
                          <p className="text-xs text-gray-600 font-mono">{details}</p>
                        </div>
                      )}
                      
                      {errorCode && (
                        <div>
                          <p className="text-xs font-medium text-gray-700 mb-1">Error Code:</p>
                          <div className="flex items-center space-x-2">
                            <code className="text-xs bg-white px-2 py-1 rounded border font-mono">
                              {errorCode}
                            </code>
                            <button
                              onClick={copyErrorCode}
                              className="text-gray-500 hover:text-gray-700 transition-colors"
                              title="Copy error code"
                            >
                              {copiedCode ? <CheckCircle className="w-4 h-4 text-green-500" /> : <Copy className="w-4 h-4" />}
                            </button>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Primary Actions */}
              <div className="flex gap-3 flex-1">
                {canRetry && onRetry && (
                  <button
                    onClick={onRetry}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>Try Again</span>
                  </button>
                )}

                {canProceedAnyway && onProceedAnyway && (
                  <button
                    onClick={onProceedAnyway}
                    className="flex-1 bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors font-medium flex items-center justify-center space-x-2"
                  >
                    <span>Proceed Anyway</span>
                  </button>
                )}

                <button
                  onClick={onClose}
                  className="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                >
                  Close
                </button>
              </div>
              
              {/* Help Action */}
              {onHelp && (
                <button
                  onClick={onHelp}
                  className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium flex items-center space-x-2"
                >
                  <HelpCircle className="w-4 h-4" />
                  <span>Get Help</span>
                </button>
              )}
            </div>
            
            {/* Progressive Error Disclosure - Contextual Help */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <details className="group">
                <summary className="cursor-pointer text-xs text-gray-600 hover:text-gray-800 transition-colors flex items-center space-x-1">
                  <span>Need help resolving this issue?</span>
                  <ChevronDown className="w-3 h-3 group-open:rotate-180 transition-transform" />
                </summary>

                <div className="mt-2 text-xs text-gray-600 space-y-2">
                  {category === 'INSUFFICIENT_FUNDS' && (
                    <div className="space-y-1">
                      <p><strong>Common solutions:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Add more SOL to your wallet</li>
                        <li>Check if you have enough for transaction fees (~0.01 SOL)</li>
                        <li>Wait for pending transactions to complete</li>
                      </ul>
                    </div>
                  )}

                  {category === 'WALLET_CONNECTION' && (
                    <div className="space-y-1">
                      <p><strong>Troubleshooting steps:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Refresh the page and reconnect your wallet</li>
                        <li>Check if your wallet is unlocked</li>
                        <li>Try switching to a different wallet</li>
                        <li>Clear browser cache and cookies</li>
                      </ul>
                    </div>
                  )}

                  {category === 'NETWORK_ERROR' && (
                    <div className="space-y-1">
                      <p><strong>Network troubleshooting:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Check your internet connection</li>
                        <li>Try refreshing the page</li>
                        <li>Wait a few minutes and try again</li>
                        <li>Switch to a different network if available</li>
                      </ul>
                    </div>
                  )}

                  {category === 'TRANSACTION_TIMEOUT' && (
                    <div className="space-y-1">
                      <p><strong>What to do:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Check if the transaction completed successfully</li>
                        <li>Wait 1-2 minutes before retrying</li>
                        <li>Ensure stable internet connection</li>
                        <li>Try during off-peak hours for faster processing</li>
                      </ul>
                    </div>
                  )}

                  {category === 'SMART_CONTRACT_ERROR' && (
                    <div className="space-y-1">
                      <p><strong>Advanced troubleshooting:</strong></p>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Verify all transaction parameters</li>
                        <li>Check if the smart contract is operational</li>
                        <li>Contact support with the error code</li>
                        <li>Try again in a few minutes</li>
                      </ul>
                    </div>
                  )}
                </div>
              </details>
            </div>

            {/* Quick Actions for specific error types */}
            {category === 'WALLET_CONNECTION' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600 mb-2">Quick Actions:</p>
                <div className="flex gap-2">
                  <button className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors">
                    Reconnect Wallet
                  </button>
                  <button className="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors">
                    Switch Wallet
                  </button>
                </div>
              </div>
            )}

            {category === 'NETWORK_ERROR' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600 mb-2">Network Status:</p>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-600">Connection unstable - retrying automatically</span>
                </div>
              </div>
            )}

            {category === 'INSUFFICIENT_FUNDS' && additionalInfo?.currentBalance !== undefined && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600 mb-2">Balance Information:</p>
                <div className="bg-yellow-50 p-2 rounded text-xs">
                  <div className="flex justify-between">
                    <span>Current Balance:</span>
                    <span className="font-medium">{additionalInfo.currentBalance.toFixed(4)} SOL</span>
                  </div>
                  {additionalInfo.requiredAmount && (
                    <div className="flex justify-between">
                      <span>Required Amount:</span>
                      <span className="font-medium">{additionalInfo.requiredAmount.toFixed(4)} SOL</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
