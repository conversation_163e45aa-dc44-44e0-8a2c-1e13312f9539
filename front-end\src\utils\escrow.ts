import { PublicKey, Transaction } from '@solana/web3.js';
import { getAssociatedTokenAddressSync, ASSOCIATED_TOKEN_PROGRAM_ID, TOKEN_PROGRAM_ID, createAssociatedTokenAccountInstruction, createSyncNativeInstruction } from '@solana/spl-token';
import * as anchor from '@coral-xyz/anchor';
import { program } from './sol/setup';
import { initiateDispute } from '@/axios/requests';
import { AppError } from './errorHandling';

// Enhanced error types for escrow operations
export class EscrowError extends AppError {
  constructor(message: string, code: string = 'ESCROW_ERROR', status: number = 400) {
    super(message, code, status);
    this.name = 'EscrowError';
  }
}

export class TransactionError extends EscrowError {
  constructor(message: string, public txId?: string) {
    super(message, 'TRANSACTION_ERROR', 400);
    this.name = 'TransactionError';
  }
}

export class InsufficientFundsError extends EscrowError {
  constructor(message: string = 'Insufficient funds to complete the transaction') {
    super(message, 'INSUFFICIENT_FUNDS', 400);
    this.name = 'InsufficientFundsError';
  }
}

export class DuplicateTransactionError extends EscrowError {
  constructor(message: string = 'Transaction already in progress or completed') {
    super(message, 'DUPLICATE_TRANSACTION', 409);
    this.name = 'DuplicateTransactionError';
  }
}

export class BlockchainError extends EscrowError {
  constructor(message: string, public originalError?: any) {
    super(message, 'BLOCKCHAIN_ERROR', 500);
    this.name = 'BlockchainError';
  }
}

// Operation status types
export type EscrowOperationType = 'buy' | 'sell' | 'refund' | 'dispute' | 'accept';
export type EscrowOperationStatus = 'idle' | 'pending' | 'success' | 'error';

export interface EscrowOperationState {
  status: EscrowOperationStatus;
  error: Error | null;
  txId: string | null;
  timestamp: number;
}

export interface EscrowOperationsState {
  buy: EscrowOperationState;
  sell: EscrowOperationState;
  refund: EscrowOperationState;
  dispute: EscrowOperationState;
  accept: EscrowOperationState;
}

// Transaction state management to prevent duplicates
const transactionStates = new Map<string, {
  status: 'pending' | 'completed' | 'failed';
  timestamp: number;
  txId?: string;
}>();

// Last transaction attempt timestamps to prevent rapid duplicates
const lastTransactionAttempts = new Map<string, number>();

// Clean up old transaction states (older than 5 minutes)
const cleanupOldTransactions = () => {
  const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
  for (const [key, state] of transactionStates.entries()) {
    if (state.timestamp < fiveMinutesAgo) {
      transactionStates.delete(key);
    }
  }
};

// Utility function to send transaction with retry logic
const sendTransactionWithRetry = async (
  connection: any,
  signedTransaction: any,
  maxRetries: number = 3,
  retryDelay: number = 1000,
  wallet?: any,
  originalTransaction?: any
): Promise<string> => {
  let lastError: any;
  let currentSignedTx = signedTransaction;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Transaction attempt ${attempt}/${maxRetries}`);

      const txId = await connection.sendRawTransaction(currentSignedTx.serialize(), {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
        maxRetries: 0 // Prevent automatic retries that could cause duplicates
      });

      console.log(`✅ Transaction sent successfully on attempt ${attempt}:`, txId);
      return txId;

    } catch (error: any) {
      lastError = error;
      console.error(`❌ Transaction attempt ${attempt} failed:`, error.message);

      // Don't retry for certain errors - throw immediately
      if (error.message?.includes('This transaction has already been processed') ||
        error.message?.includes('Transaction already processed') ||
        error.message?.includes('already been processed') ||
        error.message?.includes('User rejected') ||
        error.message?.includes('user rejected') ||
        error.code === 4001 ||
        error.code === -32003) { // RPC error for already processed
        console.log(`🚫 Not retrying - error type should not be retried`);
        throw error;
      }

      // Handle blockhash not found error by refreshing blockhash and re-signing
      if (error.message?.includes('Blockhash not found') && wallet && originalTransaction && attempt < maxRetries) {
        console.log(`🔄 Blockhash expired, getting fresh blockhash and re-signing...`);
        try {
          // Get fresh blockhash
          const { blockhash } = await connection.getLatestBlockhash('finalized');
          originalTransaction.recentBlockhash = blockhash;

          // Re-sign the transaction with fresh blockhash
          currentSignedTx = await wallet.signTransaction(originalTransaction);
          console.log(`✅ Transaction re-signed with fresh blockhash`);

          // Continue to next attempt without waiting
          continue;
        } catch (resignError) {
          console.error(`❌ Failed to re-sign transaction:`, resignError);
          // Fall through to normal retry logic
        }
      }

      // Don't retry on last attempt
      if (attempt >= maxRetries) {
        console.log(`🚫 Max retries reached, throwing error`);
        break;
      }

      // Wait before retrying
      console.log(`⏳ Waiting ${retryDelay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      retryDelay *= 1.5; // Exponential backoff
    }
  }

  throw lastError;
};

// Enhanced transaction status checking with detailed error information
const checkTransactionSuccess = async (
  connection: any,
  signature: string,
  maxAttempts: number = 5,
  retryDelay: number = 2000
): Promise<{ success: boolean; error?: string; confirmationStatus?: string }> => {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const status = await connection.getSignatureStatus(signature);

      if (status?.value) {
        const confirmationStatus = status.value.confirmationStatus;

        if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {
          if (status.value.err) {
            return {
              success: false,
              error: `Transaction failed: ${JSON.stringify(status.value.err)}`,
              confirmationStatus
            };
          }
          return { success: true, confirmationStatus };
        }

        // Transaction is still processing
        if (i < maxAttempts - 1) {
          console.log(`Transaction ${signature} still processing, attempt ${i + 1}/${maxAttempts}`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }
      }

      // No status available yet
      if (i < maxAttempts - 1) {
        console.log(`No status available for transaction ${signature}, attempt ${i + 1}/${maxAttempts}`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

    } catch (error) {
      console.log(`Attempt ${i + 1} to check transaction status failed:`, error);
      if (i === maxAttempts - 1) {
        return { success: false, error: `Failed to check transaction status: ${error}` };
      }
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  return { success: false, error: 'Transaction status check timed out' };
};

// Enhanced connection health check
const checkConnectionHealth = async (connection: any): Promise<{ healthy: boolean; error?: string }> => {
  try {
    const startTime = Date.now();
    const slot = await connection.getSlot();
    const responseTime = Date.now() - startTime;

    if (responseTime > 10000) { // 10 seconds
      return { healthy: false, error: 'Connection response time too slow' };
    }

    if (typeof slot !== 'number' || slot <= 0) {
      return { healthy: false, error: 'Invalid slot response from connection' };
    }

    return { healthy: true };
  } catch (error) {
    return { healthy: false, error: `Connection health check failed: ${error}` };
  }
};

// Enhanced wallet validation
const validateWalletConnection = (wallet: any): { valid: boolean; error?: string } => {
  if (!wallet) {
    return { valid: false, error: 'Wallet not connected. Please connect your wallet and try again.' };
  }

  if (typeof wallet.signTransaction !== 'function') {
    return { valid: false, error: 'Wallet does not support transaction signing. Please use a compatible wallet.' };
  }

  if (!wallet.address) {
    return { valid: false, error: 'Wallet address not available. Please reconnect your wallet.' };
  }

  // Check if wallet address is valid Solana address
  try {
    new PublicKey(wallet.address);
  } catch (error) {
    return { valid: false, error: 'Invalid wallet address format.' };
  }

  return { valid: true };
};

export interface EscrowTxData {
  escrowId: string;
  solAmount: string;
  perkTokenAmount: string;
  purchasePda: string;
  vaultPda: string;
  purchaseTokenMint: string;
  perkTokenMint: string;
  buyerWallet: string;
  sellerWallet: string;
}

/**
 * Create and sign escrow buy transaction
 */
export async function createEscrowBuyTransaction(
  escrowTxData: EscrowTxData,
  wallet: any
): Promise<string> {
  try {
    // Enhanced wallet validation
    const walletValidation = validateWalletConnection(wallet);
    if (!walletValidation.valid) {
      throw new EscrowError(walletValidation.error!, "WALLET_ERROR");
    }

    // Validate escrow transaction data
    if (!escrowTxData) {
      throw new EscrowError("Escrow transaction data is required", "INVALID_DATA");
    }

    if (!escrowTxData.escrowId) {
      throw new EscrowError("Escrow ID is required", "INVALID_DATA");
    }

    if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {
      throw new EscrowError("Valid SOL amount is required", "INVALID_DATA");
    }

    // Check connection health before proceeding
    const connection = program.provider.connection;
    const connectionHealth = await checkConnectionHealth(connection);
    if (!connectionHealth.healthy) {
      throw new EscrowError(`Network connection issue: ${connectionHealth.error}`, "NETWORK_ERROR");
    }

    // Create unique transaction key to prevent duplicates
    const transactionKey = `${escrowTxData.escrowId}-${wallet.address}-buy`;

    // Check for rapid duplicate attempts (within 3 seconds)
    const lastAttempt = lastTransactionAttempts.get(transactionKey);
    const now = Date.now();
    if (lastAttempt && (now - lastAttempt) < 3000) {
      throw new DuplicateTransactionError("Please wait a moment before trying again. Transaction attempt too soon.");
    }
    lastTransactionAttempts.set(transactionKey, now);

    // Clean up old transactions periodically
    if (Math.random() < 0.1) { // 10% chance to cleanup on each call
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      for (const [key, state] of transactionStates.entries()) {
        if (state.timestamp < fiveMinutesAgo) {
          transactionStates.delete(key);
        }
      }
      // Also cleanup last attempts
      for (const [key, timestamp] of lastTransactionAttempts.entries()) {
        if (timestamp < fiveMinutesAgo) {
          lastTransactionAttempts.delete(key);
        }
      }
    }

    // Check if transaction is already in progress or completed
    const existingState = transactionStates.get(transactionKey);
    if (existingState) {
      if (existingState.status === 'pending') {
        throw new Error("Transaction already in progress. Please wait for the current transaction to complete.");
      }
      if (existingState.status === 'completed' && existingState.txId) {
        // If completed recently (within 5 minutes), return existing txId
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        if (existingState.timestamp > fiveMinutesAgo) {
          console.log("Returning existing transaction ID:", existingState.txId);
          return existingState.txId;
        }
      }
    }

    // Check if there's already a successful transaction for this escrow on the blockchain
    try {
      console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);
      const connection = program.provider.connection;
      const buyerPubkey = new PublicKey(wallet.address);

      // Calculate the purchase PDA to check if it already exists
      const escrowIdBN = new anchor.BN(escrowTxData.escrowId);
      const [purchasePda] = PublicKey.findProgramAddressSync(
        [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
        program.programId
      );

      // Check if the purchase account already exists
      const purchaseAccount = await connection.getAccountInfo(purchasePda);
      if (purchaseAccount) {
        console.log('✅ Purchase account already exists, escrow transaction was successful');

        // Try to find the transaction signature in recent history
        const signatures = await connection.getSignaturesForAddress(buyerPubkey, { limit: 20 });
        for (const sig of signatures) {
          // Check if this signature is recent (within last 10 minutes)
          const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
          if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {
            // Mark as completed in our state
            transactionStates.set(transactionKey, {
              status: 'completed',
              timestamp: Date.now(),
              txId: sig.signature
            });
            return sig.signature;
          }
        }

        // If we can't find the exact signature, create a placeholder
        const placeholderTxId = `existing-${escrowTxData.escrowId}`;
        transactionStates.set(transactionKey, {
          status: 'completed',
          timestamp: Date.now(),
          txId: placeholderTxId
        });
        return placeholderTxId;
      }
    } catch (checkError) {
      console.log('Could not check for existing purchase account:', checkError);
      // Continue with normal flow if check fails
    }

    // Mark transaction as pending
    transactionStates.set(transactionKey, {
      status: 'pending',
      timestamp: Date.now()
    });

    const {
      escrowId,
      solAmount,
      perkTokenAmount,
      purchasePda,
      vaultPda,
      purchaseTokenMint,
      perkTokenMint,
      buyerWallet
    } = escrowTxData;

    console.log('Creating escrow buy transaction:', escrowTxData);

    // Validate that escrowId is numeric
    const escrowIdStr = String(escrowId);
    if (!/^\d+$/.test(escrowIdStr)) {
      throw new EscrowError(`Invalid escrow ID format: ${escrowId}. Expected numeric value.`, "INVALID_DATA");
    }

    // Check buyer's native SOL balance first
    const buyerBalance = await connection.getBalance(new PublicKey(buyerWallet));
    console.log(`Buyer native SOL balance: ${buyerBalance / 1_000_000_000} SOL (${buyerBalance} lamports)`);
    console.log(`Required amount: ${Number(solAmount) / 1_000_000_000} SOL (${solAmount} lamports)`);

    // Convert values
    const escrowIdBN = new anchor.BN(escrowIdStr);
    const solAmountBN = new anchor.BN(solAmount);
    const perkTokenAmountBN = new anchor.BN(perkTokenAmount);

    // Create public keys
    const buyerPubkey = new PublicKey(buyerWallet);
    const purchaseTokenMintPubkey = new PublicKey(purchaseTokenMint);
    const perkTokenMintPubkey = new PublicKey(perkTokenMint);
    const purchasePdaPubkey = new PublicKey(purchasePda);
    const vaultPdaPubkey = new PublicKey(vaultPda);

    // Get buyer's token account
    const buyerTokenAccount = getAssociatedTokenAddressSync(
      purchaseTokenMintPubkey,
      buyerPubkey
    );

    // Check if buyer's ATA exists and create if needed
    const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);

    let preInstructions = [];

    // Check if this is wrapped SOL
    const isWrappedSOL = purchaseTokenMintPubkey.equals(new PublicKey('So11111111111111111111111111111111111111112'));

    if (!buyerAtaInfo) {
      console.log('Creating ATA for buyer:', buyerTokenAccount.toString());

      // Create associated token account instruction
      const createAtaIx = createAssociatedTokenAccountInstruction(
        buyerPubkey, // payer
        buyerTokenAccount, // ata
        buyerPubkey, // owner
        purchaseTokenMintPubkey // mint
      );

      preInstructions.push(createAtaIx);
    }

    // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL
    if (isWrappedSOL) {
      console.log('Handling wrapped SOL transaction');

      // For wrapped SOL, we always need to fund the account with the exact amount
      // Plus some extra for rent and fees
      const requiredAmount = Number(solAmount);
      const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size
      const totalAmountNeeded = requiredAmount + rentExemption;

      console.log(`Required wrapped SOL: ${requiredAmount} lamports`);
      console.log(`Rent exemption: ${rentExemption} lamports`);
      console.log(`Total amount needed: ${totalAmountNeeded} lamports`);

      // Check if we have enough native SOL
      if (buyerBalance < totalAmountNeeded + 5000) { // Extra 5000 lamports for transaction fees
        throw new Error(`Insufficient SOL balance. Required: ${(totalAmountNeeded + 5000) / 1_000_000_000} SOL, Available: ${buyerBalance / 1_000_000_000} SOL`);
      }

      // Transfer native SOL to the wrapped SOL account to fund it
      const transferIx = anchor.web3.SystemProgram.transfer({
        fromPubkey: buyerPubkey,
        toPubkey: buyerTokenAccount,
        lamports: totalAmountNeeded,
      });

      preInstructions.push(transferIx);

      // Sync native instruction to convert the transferred SOL to wrapped SOL
      const syncIx = createSyncNativeInstruction(buyerTokenAccount);
      preInstructions.push(syncIx);

      console.log('Added instructions to wrap SOL');
    }

    // Create the transaction
    const tx = await program.methods
      .escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN)
      .accounts({
        signer: buyerPubkey,
        purchaseTokenMint: purchaseTokenMintPubkey,
        perkTokenMint: perkTokenMintPubkey,
        purchaseTokenAta: buyerTokenAccount,
        purchase: purchasePdaPubkey,
        vault: vaultPdaPubkey,
        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
        tokenProgram: TOKEN_PROGRAM_ID,
        systemProgram: anchor.web3.SystemProgram.programId,
      })
      .preInstructions(preInstructions)
      .transaction();

    // Get fresh blockhash to ensure transaction uniqueness
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
    tx.recentBlockhash = blockhash;
    tx.feePayer = buyerPubkey;

    // Add a unique memo instruction to prevent duplicate transactions
    const memoInstruction = new anchor.web3.TransactionInstruction({
      keys: [],
      programId: new PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"),
      data: Buffer.from(`escrow-buy-${escrowTxData.escrowId}-${Date.now()}`, 'utf-8')
    });
    tx.add(memoInstruction);

    console.log('🔍 Transaction details:', {
      escrowId: escrowTxData.escrowId,
      blockhash: blockhash.slice(0, 8) + '...',
      lastValidBlockHeight
    });

    // Sign the transaction
    const signedTx = await wallet.signTransaction(tx);

    console.log('🔍 Sending transaction...');

    // Send the transaction with retry logic (with blockhash refresh capability)
    const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);

    console.log('✅ Transaction sent:', txId);

    // Confirm the transaction with timeout
    const confirmation = await connection.confirmTransaction({
      signature: txId,
      blockhash: tx.recentBlockhash!,
      lastValidBlockHeight: lastValidBlockHeight
    }, 'confirmed');

    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err}`);
    }

    console.log('✅ Escrow buy transaction confirmed:', txId);

    // Mark transaction as completed
    transactionStates.set(transactionKey, {
      status: 'completed',
      timestamp: Date.now(),
      txId
    });

    // Small delay to ensure transaction is fully processed
    await new Promise(resolve => setTimeout(resolve, 1000));

    return txId;

  } catch (error: any) {
    console.error('Escrow buy transaction failed:', error);

    // Only mark as failed if it's not an "already processed" error
    // (which might actually be successful)
    if (!error.message?.includes('This transaction has already been processed')) {
      const transactionKey = `${escrowTxData.escrowId}-${wallet.address}-buy`;
      transactionStates.set(transactionKey, {
        status: 'failed',
        timestamp: Date.now()
      });
    }

    // Enhanced error handling for Solana transactions
    if (error.logs) {
      console.error('Transaction logs:', error.logs);
    }

    // If it's already an EscrowError, re-throw it
    if (error instanceof EscrowError) {
      throw error;
    }

    // Handle specific error types with enhanced error messages
    if (error.message?.includes('This transaction has already been processed')) {
      const transactionKey = `${escrowTxData.escrowId}-${wallet.address}-buy`;

      console.log('🔍 Transaction already processed error - checking if escrow was actually created...');

      // Check if the purchase account was actually created (meaning transaction was successful)
      try {
        const connection = program.provider.connection;
        const escrowIdBN = new anchor.BN(escrowTxData.escrowId);
        const [purchasePda] = PublicKey.findProgramAddressSync(
          [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
          program.programId
        );

        const purchaseAccount = await connection.getAccountInfo(purchasePda);
        if (purchaseAccount) {
          console.log('✅ Purchase account exists - transaction was actually successful!');

          // Try to find the transaction signature in recent history
          const buyerPubkey = new PublicKey(wallet.address);
          const signatures = await connection.getSignaturesForAddress(buyerPubkey, { limit: 10 });

          let foundTxId = null;
          for (const sig of signatures) {
            // Check if this signature is recent (within last 5 minutes)
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
            if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {
              foundTxId = sig.signature;
              break;
            }
          }

          if (!foundTxId) {
            foundTxId = `successful-${escrowTxData.escrowId}-${Date.now()}`;
          }

          // Mark as completed in our state
          transactionStates.set(transactionKey, {
            status: 'completed',
            timestamp: Date.now(),
            txId: foundTxId
          });

          console.log('🎉 Returning successful transaction ID:', foundTxId);
          return foundTxId;
        } else {
          console.log('❌ Purchase account does not exist - transaction actually failed');
        }
      } catch (checkError) {
        console.log('Could not verify purchase account:', checkError);
      }

      throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');
    }

    // Handle specific Solana/blockchain errors
    if (error.message?.includes('AccountNotInitialized')) {
      throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');
    }

    if (error.message?.includes('InsufficientFunds') || error.message?.includes('insufficient funds')) {
      throw new InsufficientFundsError('Insufficient funds to complete the transaction.');
    }

    if (error.message?.includes('blockhash not found') || error.message?.includes('Transaction expired')) {
      throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');
    }

    if (error.message?.includes('User rejected') || error.message?.includes('user rejected') || error.code === 4001) {
      throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');
    }

    if (error.message?.includes('Network Error') || error.message?.includes('Failed to fetch')) {
      throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');
    }

    if (error.message?.includes('Simulation failed')) {
      throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');
    }

    // Handle RPC errors
    if (error.code && typeof error.code === 'number') {
      if (error.code === -32002) {
        throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');
      }
      if (error.code === -32003) {
        throw new DuplicateTransactionError('Transaction already processed.');
      }
    }

    // Generic blockchain error
    if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {
      throw new BlockchainError(`Blockchain transaction failed: ${error.message}`, error);
    }

    // Fallback for unknown errors
    throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');
  }
}

/**
 * Create and sign escrow accept transaction (for sellers)
 */
export async function createEscrowAcceptTransaction(
  escrowId: string,
  purchaseTokenAmount: string,
  perkTokenAmount: string,
  purchaseTokenMint: string,
  perkTokenMint: string,
  sellerWallet: string,
  wallet: any,
  tradeId?: string | number
): Promise<string> {
  try {
    // Enhanced wallet validation
    const walletValidation = validateWalletConnection(wallet);
    if (!walletValidation.valid) {
      throw new EscrowError(walletValidation.error!, "WALLET_ERROR");
    }

    // If tradeId is provided, fetch trade details and calculate amounts
    let calculatedPurchaseAmount = purchaseTokenAmount;
    let calculatedPerkAmount = perkTokenAmount;
    let calculatedPurchaseMint = purchaseTokenMint;
    let calculatedPerkMint = perkTokenMint;
    let calculatedEscrowId = escrowId;

    if (tradeId) {
      try {
        console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);
        const { getTradeDetails } = await import('../axios/requests');
        const tradeResponse = await getTradeDetails(Number(tradeId));
        const tradeData = tradeResponse.data;

        console.log('✅ [EscrowAccept] Trade data fetched successfully:', {
          tradeId: tradeData.id,
          status: tradeData.status,
          escrowId: tradeData.escrowId,
          escrowTxId: tradeData.escrowTxId,
          amount: tradeData.amount,
          price: tradeData.price,
          perkDetails: tradeData.perkDetails,
          perkTokenMint: tradeData.perkTokenMint
        });

        // Use trade data to get the numeric escrowId
        // Priority: 1. tradeData.escrowId, 2. extra field, 3. original escrowId
        let numericEscrowId = null;

        // First try the direct escrowId field from trade data
        if (tradeData.escrowId) {
          // Validate that it's numeric before using it
          const escrowIdStr = String(tradeData.escrowId);
          if (/^\d+$/.test(escrowIdStr)) {
            numericEscrowId = tradeData.escrowId;
          } else {
            console.warn('⚠️ Trade escrowId is not numeric:', tradeData.escrowId);
          }
        } else {
          // Fall back to extra field
          try {
            if (tradeData.extra) {
              const extraData = JSON.parse(tradeData.extra);
              if (extraData.escrowId) {
                const escrowIdStr = String(extraData.escrowId);
                if (/^\d+$/.test(escrowIdStr)) {
                  numericEscrowId = extraData.escrowId;
                } else {
                  console.warn('⚠️ Extra escrowId is not numeric:', extraData.escrowId);
                }
              }
            }
          } catch (e) {
            console.log('Could not parse extra data:', e);
          }
        }

        // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId
        // Never use escrowTxId as it's a transaction signature, not a numeric ID
        calculatedEscrowId = numericEscrowId || escrowId;

        console.log('🔍 [EscrowAccept] EscrowId resolution:', {
          originalEscrowId: escrowId,
          tradeDataEscrowId: tradeData.escrowId,
          numericEscrowId,
          finalCalculatedEscrowId: calculatedEscrowId
        });

        // Calculate SOL amount in lamports
        const solAmount = tradeData.amount || 0;
        calculatedPurchaseAmount = (solAmount * 1e9).toString();

        // Calculate perk token amount based on SOL amount and perk price
        const perkPrice = tradeData.perkDetails?.price || 0.002;
        const perkTokensToReceive = solAmount / perkPrice;
        calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();

        // Set token mints
        calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint

        // Try multiple sources for perk token mint
        calculatedPerkMint = tradeData.tokenDetails?.tokenAddress ||
                           tradeData.perkDetails?.token?.tokenAddress ||
                           tradeData.perkTokenMint ||
                           perkTokenMint;

        console.log('🔍 [EscrowAccept] Perk token mint resolution:', {
          'tokenDetails.tokenAddress': tradeData.tokenDetails?.tokenAddress,
          'perkDetails.token.tokenAddress': tradeData.perkDetails?.token?.tokenAddress,
          'tradeData.perkTokenMint': tradeData.perkTokenMint,
          'original perkTokenMint': perkTokenMint,
          'final calculatedPerkMint': calculatedPerkMint
        });

        console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {
          escrowId: calculatedEscrowId,
          purchaseAmount: calculatedPurchaseAmount,
          perkAmount: calculatedPerkAmount,
          purchaseMint: calculatedPurchaseMint,
          perkMint: calculatedPerkMint
        });

      } catch (error: any) {
        console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);
        throw new EscrowError(`Failed to fetch trade data: ${error.message}`, "TRADE_DATA_ERROR");
      }
    }

    // Validate escrow accept data
    if (!calculatedEscrowId) {
      throw new EscrowError(
        `Escrow ID is required but not found. Please ensure the trade has a valid escrowId. TradeId: ${tradeId}`,
        "INVALID_DATA"
      );
    }

    // Validate that escrowId is numeric
    const escrowIdStr = String(calculatedEscrowId);
    if (!/^\d+$/.test(escrowIdStr)) {
      throw new EscrowError(`Invalid escrow ID format: ${calculatedEscrowId}. Expected numeric value.`, "INVALID_DATA");
    }

    if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {
      throw new EscrowError("Valid perk token amount is required", "INVALID_DATA");
    }

    if (!calculatedPerkMint) {
      throw new EscrowError("Perk token mint address is required", "INVALID_DATA");
    }

    // Check connection health before proceeding
    const connection = program.provider.connection;
    const connectionHealth = await checkConnectionHealth(connection);
    if (!connectionHealth.healthy) {
      throw new EscrowError(`Network connection issue: ${connectionHealth.error}`, "NETWORK_ERROR");
    }

    // Create unique transaction key to prevent duplicates
    const transactionKey = `${escrowIdStr}-${wallet.address}-accept`;

    // Check for rapid duplicate attempts (within 3 seconds)
    const lastAttempt = lastTransactionAttempts.get(transactionKey);
    const now = Date.now();
    if (lastAttempt && (now - lastAttempt) < 3000) {
      throw new DuplicateTransactionError("Please wait a moment before trying again. Transaction attempt too soon.");
    }
    lastTransactionAttempts.set(transactionKey, now);

    // Clean up old transactions periodically
    cleanupOldTransactions();

    // Check if transaction is already in progress or completed
    const existingState = transactionStates.get(transactionKey);
    if (existingState) {
      if (existingState.status === 'pending') {
        throw new Error("Escrow accept transaction already in progress. Please wait for the current transaction to complete.");
      }
      if (existingState.status === 'completed' && existingState.txId) {
        // If completed recently (within 5 minutes), return existing txId
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        if (existingState.timestamp > fiveMinutesAgo) {
          console.log("Returning existing escrow accept transaction ID:", existingState.txId);
          return existingState.txId;
        }
      }
    }

    // Check if the escrow has already been accepted on the blockchain
    try {
      console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);
      const escrowIdBN = new anchor.BN(escrowIdStr);
      const [purchasePda] = PublicKey.findProgramAddressSync(
        [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
        program.programId
      );

      // Check if the purchase account exists and is already accepted
      const purchaseAccount = await program.account.purchase.fetch(purchasePda);
      if (purchaseAccount.accepted) {
        console.log('✅ Escrow already accepted');

        // Try to find the transaction signature in recent history
        const sellerPubkey = new PublicKey(wallet.address);
        const signatures = await connection.getSignaturesForAddress(sellerPubkey, { limit: 20 });
        for (const sig of signatures) {
          // Check if this signature is recent (within last 10 minutes)
          const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
          if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {
            // Mark as completed in our state
            transactionStates.set(transactionKey, {
              status: 'completed',
              timestamp: Date.now(),
              txId: sig.signature
            });
            return sig.signature;
          }
        }

        // If we can't find the exact signature, create a placeholder
        const placeholderTxId = `accepted-${escrowIdStr}`;
        transactionStates.set(transactionKey, {
          status: 'completed',
          timestamp: Date.now(),
          txId: placeholderTxId
        });
        return placeholderTxId;
      }
    } catch (checkError) {
      console.log('Could not check for existing acceptance:', checkError);
      // Continue with normal flow if check fails
    }

    // Mark transaction as pending
    transactionStates.set(transactionKey, {
      status: 'pending',
      timestamp: Date.now()
    });

    console.log('Creating escrow accept transaction:', {
      escrowId: escrowIdStr,
      purchaseTokenAmount: calculatedPurchaseAmount,
      perkTokenAmount: calculatedPerkAmount,
      sellerWallet
    });

    // Convert values
    const escrowIdBN = new anchor.BN(escrowIdStr);
    const purchaseTokenAmountBN = new anchor.BN(calculatedPurchaseAmount);
    const perkTokenAmountBN = new anchor.BN(calculatedPerkAmount);

    // Create public keys
    const sellerPubkey = new PublicKey(sellerWallet);
    const purchaseTokenMintPubkey = new PublicKey(calculatedPurchaseMint);
    const perkTokenMintPubkey = new PublicKey(calculatedPerkMint);

    // Calculate PDAs
    const [purchasePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
      program.programId
    );

    // Get seller's perk token account
    const sellerPerkTokenAta = getAssociatedTokenAddressSync(
      perkTokenMintPubkey,
      sellerPubkey
    );

    // Calculate the perk vault PDA (where seller's perk tokens will be stored)
    const [perkVaultPda] = PublicKey.findProgramAddressSync(
      [purchasePda.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), perkTokenMintPubkey.toBuffer()],
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    // Check if seller's perk ATA exists and create if needed
    const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);
    let preInstructions = [];

    if (!sellerPerkAtaInfo) {
      console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());

      // Create associated token account instruction
      const createAtaIx = createAssociatedTokenAccountInstruction(
        sellerPubkey, // payer
        sellerPerkTokenAta, // ata
        sellerPubkey, // owner
        perkTokenMintPubkey // mint
      );

      preInstructions.push(createAtaIx);
    }

    // Create the transaction
    const tx = await program.methods
      .escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN)
      .accounts({
        signer: sellerPubkey,
        purchaseTokenMint: purchaseTokenMintPubkey,
        perkTokenMint: perkTokenMintPubkey,
        perkTokenAta: sellerPerkTokenAta,
        purchase: purchasePda,
        vault: perkVaultPda,
        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
        tokenProgram: TOKEN_PROGRAM_ID,
        systemProgram: anchor.web3.SystemProgram.programId,
      })
      .preInstructions(preInstructions)
      .transaction();

    // Get fresh blockhash to ensure transaction uniqueness
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
    tx.recentBlockhash = blockhash;
    tx.feePayer = sellerPubkey;

    // Add a unique memo instruction to prevent duplicate transactions
    const memoInstruction = new anchor.web3.TransactionInstruction({
      keys: [],
      programId: new PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"),
      data: Buffer.from(`escrow-accept-${escrowIdStr}-${Date.now()}`, 'utf-8')
    });
    tx.add(memoInstruction);

    console.log('🔍 Accept transaction details:', {
      escrowId: escrowIdStr,
      blockhash: blockhash.slice(0, 8) + '...',
      lastValidBlockHeight
    });

    // Sign the transaction
    const signedTx = await wallet.signTransaction(tx);

    console.log('🔍 Sending accept transaction...');

    // Send the transaction with retry logic (with blockhash refresh capability)
    const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);

    console.log('✅ Transaction sent:', txId);

    // Confirm the transaction with timeout
    const confirmation = await connection.confirmTransaction({
      signature: txId,
      blockhash: tx.recentBlockhash!,
      lastValidBlockHeight: lastValidBlockHeight
    }, 'confirmed');

    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err}`);
    }

    console.log('✅ Escrow accept transaction confirmed:', txId);

    // Mark transaction as completed
    transactionStates.set(transactionKey, {
      status: 'completed',
      timestamp: Date.now(),
      txId
    });

    // Small delay to ensure transaction is fully processed
    await new Promise(resolve => setTimeout(resolve, 1000));

    return txId;

  } catch (error: any) {
    console.error('Escrow accept transaction failed:', error);

    // Only mark as failed if it's not an "already processed" error
    if (!error.message?.includes('This transaction has already been processed')) {
      const transactionKey = `${escrowId}-${wallet.address}-accept`;
      transactionStates.set(transactionKey, {
        status: 'failed',
        timestamp: Date.now()
      });
    }

    // Enhanced error handling for Solana transactions
    if (error.logs) {
      console.error('Transaction logs:', error.logs);
    }

    // If it's already an EscrowError, re-throw it
    if (error instanceof EscrowError) {
      throw error;
    }

    // Handle specific error types with enhanced error messages
    if (error.message?.includes('This transaction has already been processed')) {
      const transactionKey = `${escrowId}-${wallet.address}-accept`;

      console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');

      // Check if the purchase account was actually accepted (meaning transaction was successful)
      try {
        const escrowIdBN = new anchor.BN(escrowId);
        const [purchasePda] = PublicKey.findProgramAddressSync(
          [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
          program.programId
        );

        const purchaseAccount = await program.account.purchase.fetch(purchasePda);
        if (purchaseAccount.accepted) {
          console.log('✅ Purchase account is accepted - transaction was actually successful!');

          // Try to find the transaction signature in recent history
          const sellerPubkey = new PublicKey(wallet.address);
          const connection = program.provider.connection;
          const signatures = await connection.getSignaturesForAddress(sellerPubkey, { limit: 10 });

          let foundTxId = null;
          for (const sig of signatures) {
            // Check if this signature is recent (within last 5 minutes)
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
            if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {
              foundTxId = sig.signature;
              break;
            }
          }

          if (!foundTxId) {
            foundTxId = `successful-accept-${escrowId}-${Date.now()}`;
          }

          // Mark as completed in our state
          transactionStates.set(transactionKey, {
            status: 'completed',
            timestamp: Date.now(),
            txId: foundTxId
          });

          console.log('🎉 Returning successful accept transaction ID:', foundTxId);
          return foundTxId;
        } else {
          console.log('❌ Purchase account is not accepted - transaction actually failed');
        }
      } catch (checkError) {
        console.log('Could not verify accept status:', checkError);
      }

      throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');
    }

    // Handle specific Solana/blockchain errors
    if (error.message?.includes('AccountNotInitialized')) {
      throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');
    }

    if (error.message?.includes('InsufficientFunds') || error.message?.includes('insufficient funds')) {
      throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');
    }

    if (error.message?.includes('blockhash not found') || error.message?.includes('Transaction expired')) {
      throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');
    }

    if (error.message?.includes('User rejected') || error.message?.includes('user rejected') || error.code === 4001) {
      throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');
    }

    if (error.message?.includes('Network Error') || error.message?.includes('Failed to fetch')) {
      throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');
    }

    if (error.message?.includes('Simulation failed')) {
      throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');
    }

    // Handle RPC errors
    if (error.code && typeof error.code === 'number') {
      if (error.code === -32002) {
        throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');
      }
      if (error.code === -32003) {
        throw new DuplicateTransactionError('Transaction already processed.');
      }
    }

    // Generic blockchain error
    if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {
      throw new BlockchainError(`Blockchain transaction failed: ${error.message}`, error);
    }

    // Fallback for unknown errors
    throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');
  }
}

/**
 * Create and sign escrow sell transaction (for sellers)
 */
export async function createEscrowSellTransaction(
  escrowId: string,
  purchaseTokenMint: string,
  perkTokenMint: string,
  sellerWallet: string,
  buyerWallet: string,
  wallet: any,
  tradeId?: string | number
): Promise<string> {
  try {
    // Validate wallet
    if (!wallet || typeof wallet.signTransaction !== "function" || !wallet.address) {
      throw new Error("Wallet not connected or signTransaction missing");
    }

    // Step 1: Fetch trade data first if tradeId is provided
    let tradeData = null;
    if (tradeId) {
      try {
        console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);
        const { getTradeDetails } = await import('../axios/requests');
        const tradeResponse = await getTradeDetails(Number(tradeId));
        tradeData = tradeResponse.data;
        console.log('✅ [EscrowSell] Trade data fetched successfully:', {
          tradeId: tradeData.id,
          status: tradeData.status,
          escrowId: tradeData.escrowId,
          perkId: tradeData.perkId,
          price: tradeData.price
        });

        // Validate that the trade data matches the escrow parameters
        if (tradeData.escrowId && tradeData.escrowId !== escrowId) {
          console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {
            provided: escrowId,
            fromTrade: tradeData.escrowId
          });
        }

        // Ensure trade is in correct status for release
        if (tradeData.status !== 'escrowed') {
          throw new Error(`Trade is not in escrowed status. Current status: ${tradeData.status}`);
        }

        // Additional validation: check if seller wallet matches
        if (tradeData.to && tradeData.to !== sellerWallet) {
          console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {
            provided: sellerWallet,
            fromTrade: tradeData.to
          });
        }

        // Additional validation: check if buyer wallet matches
        if (tradeData.from && tradeData.from !== buyerWallet) {
          console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {
            provided: buyerWallet,
            fromTrade: tradeData.from
          });
        }

      } catch (error: any) {
        console.error('❌ [EscrowSell] Failed to fetch trade data:', error);
        throw new Error(`Failed to fetch trade data: ${error.message}`);
      }
    }

    // Create unique transaction key to prevent duplicates
    const transactionKey = `${escrowId}-${wallet.address}-sell`;

    // Check for rapid duplicate attempts (within 3 seconds)
    const lastAttempt = lastTransactionAttempts.get(transactionKey);
    const now = Date.now();
    if (lastAttempt && (now - lastAttempt) < 3000) {
      throw new Error("Please wait a moment before trying to release again. Transaction attempt too soon.");
    }
    lastTransactionAttempts.set(transactionKey, now);

    // Clean up old transactions periodically
    if (Math.random() < 0.1) { // 10% chance to cleanup on each call
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      for (const [key, state] of transactionStates.entries()) {
        if (state.timestamp < fiveMinutesAgo) {
          transactionStates.delete(key);
        }
      }
      // Also cleanup last attempts
      for (const [key, timestamp] of lastTransactionAttempts.entries()) {
        if (timestamp < fiveMinutesAgo) {
          lastTransactionAttempts.delete(key);
        }
      }
    }

    // Check if transaction is already in progress or completed
    const existingState = transactionStates.get(transactionKey);
    if (existingState) {
      if (existingState.status === 'pending') {
        throw new Error("Release transaction already in progress. Please wait for the current transaction to complete.");
      }
      if (existingState.status === 'completed' && existingState.txId) {
        // If completed recently (within 5 minutes), return existing txId
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        if (existingState.timestamp > fiveMinutesAgo) {
          console.log("Returning existing release transaction ID:", existingState.txId);
          return existingState.txId;
        }
      }
    }

    // Check if the escrow has already been released on the blockchain
    try {
      console.log('🔍 Checking if escrow has already been released:', escrowId);
      const connection = program.provider.connection;
      const escrowIdBN = new anchor.BN(escrowId);
      const [purchasePda] = PublicKey.findProgramAddressSync(
        [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
        program.programId
      );

      // Check if the purchase account still exists (if released, it should be closed)
      const purchaseAccount = await connection.getAccountInfo(purchasePda);
      if (!purchaseAccount) {
        console.log('✅ Purchase account no longer exists, escrow was already released');

        // Try to find the release transaction signature in recent history
        const sellerPubkey = new PublicKey(wallet.address);
        const signatures = await connection.getSignaturesForAddress(sellerPubkey, { limit: 20 });
        for (const sig of signatures) {
          // Check if this signature is recent (within last 10 minutes)
          const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
          if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {
            // Mark as completed in our state
            transactionStates.set(transactionKey, {
              status: 'completed',
              timestamp: Date.now(),
              txId: sig.signature
            });
            return sig.signature;
          }
        }

        // If we can't find the exact signature, create a placeholder
        const placeholderTxId = `released-${escrowId}`;
        transactionStates.set(transactionKey, {
          status: 'completed',
          timestamp: Date.now(),
          txId: placeholderTxId
        });
        return placeholderTxId;
      }
    } catch (checkError) {
      console.log('Could not check release status:', checkError);
      // Continue with normal flow if check fails
    }

    // Mark transaction as pending
    transactionStates.set(transactionKey, {
      status: 'pending',
      timestamp: Date.now()
    });

    console.log('Creating escrow sell transaction:', {
      escrowId,
      sellerWallet,
      buyerWallet,
      tradeData: tradeData ? {
        id: tradeData.id,
        status: tradeData.status,
        perkId: tradeData.perkId,
        price: tradeData.price,
        escrowTxId: tradeData.escrowTxId
      } : 'No trade data provided'
    });

    // Calculate PDAs
    const escrowIdBN = new anchor.BN(escrowId);
    const [purchasePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
      program.programId
    );

    const [vaultPda] = PublicKey.findProgramAddressSync(
      [purchasePda.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), new PublicKey(purchaseTokenMint).toBuffer()],
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    // Create public keys
    const sellerPubkey = new PublicKey(sellerWallet);
    const buyerPubkey = new PublicKey(buyerWallet);
    const purchaseTokenMintPubkey = new PublicKey(purchaseTokenMint);
    const perkTokenMintPubkey = new PublicKey(perkTokenMint);

    // Get token accounts
    const sellerPurchaseTokenAta = getAssociatedTokenAddressSync(purchaseTokenMintPubkey, sellerPubkey);
    const buyerPerkTokenAta = getAssociatedTokenAddressSync(perkTokenMintPubkey, buyerPubkey);

    // Calculate the perk vault PDA (where seller's perk tokens are stored)
    const [perkVaultPda] = PublicKey.findProgramAddressSync(
      [purchasePda.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), perkTokenMintPubkey.toBuffer()],
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    console.log('🔍 Account debugging:', {
      sellerWallet,
      buyerWallet,
      buyerPerkTokenAta: buyerPerkTokenAta.toString(),
      sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),
      perkVaultPda: perkVaultPda.toString(),
      whoOwnsWhat: {
        purchaseTokenGoesTo: 'seller', // Should be seller
        perkTokenGoesTo: 'buyer'       // Should be buyer
      }
    });

    // Create the transaction
    const tx = await program.methods
      .escrowSell(escrowIdBN)
      .accounts({
        buyer: buyerPubkey,
        seller: sellerPubkey,
        purchaseTokenMint: purchaseTokenMintPubkey,
        perkTokenMint: perkTokenMintPubkey,
        purchaseTokenAta: sellerPurchaseTokenAta,
        perkTokenDestinationAta: buyerPerkTokenAta,
        purchase: purchasePda,
        vault: vaultPda,
        perkVault: perkVaultPda,
        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
        tokenProgram: TOKEN_PROGRAM_ID,
        systemProgram: anchor.web3.SystemProgram.programId,
      })
      .transaction();

    // Get fresh blockhash to ensure transaction uniqueness
    const connection = program.provider.connection;
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
    tx.recentBlockhash = blockhash;
    tx.feePayer = buyerPubkey; // Buyer pays the fees and is the signer

    // Add a unique memo instruction to prevent duplicate transactions
    const memoInstruction = new anchor.web3.TransactionInstruction({
      keys: [],
      programId: new PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"),
      data: Buffer.from(`escrow-sell-${escrowId}-${Date.now()}`, 'utf-8')
    });
    tx.add(memoInstruction);

    console.log('🔍 Sell transaction details:', {
      escrowId,
      blockhash: blockhash.slice(0, 8) + '...',
      lastValidBlockHeight
    });

    // Sign the transaction
    const signedTx = await wallet.signTransaction(tx);

    console.log('🔍 Sending sell transaction...');

    // Send the transaction with retry logic (with blockhash refresh capability)
    const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);

    console.log('✅ Transaction sent:', txId);

    // Confirm the transaction with timeout
    const confirmation = await connection.confirmTransaction({
      signature: txId,
      blockhash: tx.recentBlockhash!,
      lastValidBlockHeight: lastValidBlockHeight
    }, 'confirmed');

    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err}`);
    }

    console.log('✅ Escrow sell transaction confirmed:', txId);

    // Mark transaction as completed
    transactionStates.set(transactionKey, {
      status: 'completed',
      timestamp: Date.now(),
      txId
    });

    // Small delay to ensure transaction is fully processed
    await new Promise(resolve => setTimeout(resolve, 1000));

    return txId;

  } catch (error: any) {
    console.error('Escrow sell transaction failed:', error);

    const transactionKey = `${escrowId}-${wallet.address}-sell`;

    // Only mark as failed if it's not an "already processed" error
    // (which might actually be successful)
    if (!error.message?.includes('This transaction has already been processed')) {
      transactionStates.set(transactionKey, {
        status: 'failed',
        timestamp: Date.now()
      });
    }

    // Enhanced error handling for Solana transactions
    if (error.logs) {
      console.error('Transaction logs:', error.logs);
    }

    // Handle specific duplicate transaction error
    if (error.message?.includes('This transaction has already been processed')) {
      console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');

      // Check if the purchase account was actually closed (meaning release was successful)
      try {
        const connection = program.provider.connection;
        const escrowIdBN = new anchor.BN(escrowId);
        const [purchasePda] = PublicKey.findProgramAddressSync(
          [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
          program.programId
        );

        const purchaseAccount = await connection.getAccountInfo(purchasePda);
        if (!purchaseAccount) {
          console.log('✅ Purchase account no longer exists - release was actually successful!');

          // Try to find the transaction signature in recent history
          const sellerPubkey = new PublicKey(wallet.address);
          const signatures = await connection.getSignaturesForAddress(sellerPubkey, { limit: 10 });

          let foundTxId = null;
          for (const sig of signatures) {
            // Check if this signature is recent (within last 5 minutes)
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
            if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {
              foundTxId = sig.signature;
              break;
            }
          }

          if (!foundTxId) {
            foundTxId = `successful-release-${escrowId}-${Date.now()}`;
          }

          // Mark as completed in our state
          transactionStates.set(transactionKey, {
            status: 'completed',
            timestamp: Date.now(),
            txId: foundTxId
          });

          console.log('🎉 Returning successful release transaction ID:', foundTxId);
          return foundTxId;
        } else {
          console.log('❌ Purchase account still exists - release actually failed');
        }
      } catch (checkError) {
        console.log('Could not verify release status:', checkError);
      }

      throw new Error('Release transaction already processed. Please refresh the page and try again.');
    }

    if (error.message?.includes('AccountNotInitialized')) {
      throw new Error('Token account not initialized. Please ensure you have the required tokens.');
    }

    if (error.message?.includes('InsufficientFunds')) {
      throw new Error('Insufficient funds to complete the release transaction.');
    }

    // Handle transaction timeout or blockhash expiry
    if (error.message?.includes('blockhash not found') || error.message?.includes('Transaction expired')) {
      throw new Error('Release transaction expired. Please try again with a fresh transaction.');
    }

    throw error;
  }
}

/**
 * Create and sign escrow refund transaction (for buyers)
 */
export async function createEscrowRefundTransaction(
  escrowId: string,
  purchaseTokenMint: string,
  buyerWallet: string,
  wallet: any
): Promise<string> {
  try {
    // Validate wallet - same pattern as in helpers.ts
    if (!wallet || typeof wallet.signTransaction !== "function" || !wallet.address) {
      throw new Error("Wallet not connected or signTransaction missing");
    }

    // Create unique transaction key to prevent duplicates
    const transactionKey = `${escrowId}-${wallet.address}-refund`;

    // Check for rapid duplicate attempts (within 3 seconds)
    const lastAttempt = lastTransactionAttempts.get(transactionKey);
    const now = Date.now();
    if (lastAttempt && (now - lastAttempt) < 3000) {
      throw new Error("Please wait a moment before trying to refund again. Transaction attempt too soon.");
    }
    lastTransactionAttempts.set(transactionKey, now);

    // Check if transaction is already in progress or completed
    const existingState = transactionStates.get(transactionKey);
    if (existingState) {
      if (existingState.status === 'pending') {
        throw new Error("Refund transaction already in progress. Please wait for the current transaction to complete.");
      }
      if (existingState.status === 'completed' && existingState.txId) {
        // If completed recently (within 5 minutes), return existing txId
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        if (existingState.timestamp > fiveMinutesAgo) {
          console.log("Returning existing refund transaction ID:", existingState.txId);
          return existingState.txId;
        }
      }
    }

    // Check if the escrow has already been refunded on the blockchain
    try {
      console.log('🔍 Checking if escrow has already been refunded:', escrowId);
      const connection = program.provider.connection;
      const escrowIdBN = new anchor.BN(escrowId);
      const [purchasePda] = PublicKey.findProgramAddressSync(
        [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
        program.programId
      );

      // Check if the purchase account still exists (if refunded, it should be closed)
      const purchaseAccount = await connection.getAccountInfo(purchasePda);
      if (!purchaseAccount) {
        console.log('✅ Purchase account no longer exists, escrow was already refunded');

        // Try to find the refund transaction signature in recent history
        const buyerPubkey = new PublicKey(wallet.address);
        const signatures = await connection.getSignaturesForAddress(buyerPubkey, { limit: 20 });
        for (const sig of signatures) {
          // Check if this signature is recent (within last 10 minutes)
          const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
          if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {
            // Mark as completed in our state
            transactionStates.set(transactionKey, {
              status: 'completed',
              timestamp: Date.now(),
              txId: sig.signature
            });
            return sig.signature;
          }
        }

        // If we can't find the exact signature, create a placeholder
        const placeholderTxId = `refunded-${escrowId}`;
        transactionStates.set(transactionKey, {
          status: 'completed',
          timestamp: Date.now(),
          txId: placeholderTxId
        });
        return placeholderTxId;
      }
    } catch (checkError) {
      console.log('Could not check refund status:', checkError);
      // Continue with normal flow if check fails
    }

    // Mark transaction as pending
    transactionStates.set(transactionKey, {
      status: 'pending',
      timestamp: Date.now()
    });

    console.log('Creating escrow refund transaction:', {
      escrowId,
      buyerWallet
    });

    // Calculate PDAs
    const escrowIdBN = new anchor.BN(escrowId);
    const [purchasePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
      program.programId
    );

    const [vaultPda] = PublicKey.findProgramAddressSync(
      [purchasePda.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), new PublicKey(purchaseTokenMint).toBuffer()],
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    // Create public keys
    const buyerPubkey = new PublicKey(buyerWallet);
    const purchaseTokenMintPubkey = new PublicKey(purchaseTokenMint);

    // Get buyer's token account
    const buyerTokenAccount = getAssociatedTokenAddressSync(purchaseTokenMintPubkey, buyerPubkey);

    // Create the transaction
    const tx = await program.methods
      .escrowRefund(escrowIdBN)
      .accounts({
        buyer: buyerPubkey,
        purchaseTokenMint: purchaseTokenMintPubkey,
        buyerTokenAta: buyerTokenAccount,
        purchase: purchasePda,
        vault: vaultPda,
        tokenProgram: TOKEN_PROGRAM_ID,
        systemProgram: anchor.web3.SystemProgram.programId,
      })
      .transaction();

    // Get fresh blockhash to ensure transaction uniqueness
    const connection = program.provider.connection;
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
    tx.recentBlockhash = blockhash;
    tx.feePayer = buyerPubkey;

    // Add a unique memo instruction to prevent duplicate transactions
    const memoInstruction = new anchor.web3.TransactionInstruction({
      keys: [],
      programId: new PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"),
      data: Buffer.from(`escrow-refund-${escrowId}-${Date.now()}`, 'utf-8')
    });
    tx.add(memoInstruction);

    console.log('🔍 Refund transaction details:', {
      escrowId,
      blockhash: blockhash.slice(0, 8) + '...',
      lastValidBlockHeight
    });

    // Sign the transaction
    const signedTx = await wallet.signTransaction(tx);

    console.log('🔍 Sending refund transaction...');

    // Send the transaction with retry logic (with blockhash refresh capability)
    const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);

    console.log('✅ Transaction sent:', txId);

    // Confirm the transaction with timeout
    const confirmation = await connection.confirmTransaction({
      signature: txId,
      blockhash: tx.recentBlockhash!,
      lastValidBlockHeight: lastValidBlockHeight
    }, 'confirmed');

    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err}`);
    }

    console.log('✅ Escrow refund transaction confirmed:', txId);

    // Mark transaction as completed
    transactionStates.set(transactionKey, {
      status: 'completed',
      timestamp: Date.now(),
      txId
    });

    // Small delay to ensure transaction is fully processed
    await new Promise(resolve => setTimeout(resolve, 1000));

    return txId;

  } catch (error: any) {
    console.error('Escrow refund transaction failed:', error);

    const transactionKey = `${escrowId}-${wallet.address}-refund`;

    // Only mark as failed if it's not an "already processed" error
    // (which might actually be successful)
    if (!error.message?.includes('This transaction has already been processed')) {
      transactionStates.set(transactionKey, {
        status: 'failed',
        timestamp: Date.now()
      });
    }

    // Enhanced error handling for Solana transactions
    if (error.logs) {
      console.error('Transaction logs:', error.logs);
    }

    // Handle specific duplicate transaction error
    if (error.message?.includes('This transaction has already been processed')) {
      console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');

      // Check if the purchase account was actually closed (meaning refund was successful)
      try {
        const connection = program.provider.connection;
        const escrowIdBN = new anchor.BN(escrowId);
        const [purchasePda] = PublicKey.findProgramAddressSync(
          [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
          program.programId
        );

        const purchaseAccount = await connection.getAccountInfo(purchasePda);
        if (!purchaseAccount) {
          console.log('✅ Purchase account no longer exists - refund was actually successful!');

          // Try to find the transaction signature in recent history
          const buyerPubkey = new PublicKey(wallet.address);
          const signatures = await connection.getSignaturesForAddress(buyerPubkey, { limit: 10 });

          let foundTxId = null;
          for (const sig of signatures) {
            // Check if this signature is recent (within last 5 minutes)
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
            if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {
              foundTxId = sig.signature;
              break;
            }
          }

          if (!foundTxId) {
            foundTxId = `successful-refund-${escrowId}-${Date.now()}`;
          }

          // Mark as completed in our state
          transactionStates.set(transactionKey, {
            status: 'completed',
            timestamp: Date.now(),
            txId: foundTxId
          });

          console.log('🎉 Returning successful refund transaction ID:', foundTxId);
          return foundTxId;
        } else {
          console.log('❌ Purchase account still exists - refund actually failed');
        }
      } catch (checkError) {
        console.log('Could not verify refund status:', checkError);
      }

      throw new Error('Refund transaction already processed. Please refresh the page and try again.');
    }

    if (error.message?.includes('AccountNotInitialized')) {
      throw new Error('Token account not initialized. Please ensure you have the required tokens.');
    }

    if (error.message?.includes('InsufficientFunds')) {
      throw new Error('Insufficient funds to complete the refund transaction.');
    }

    // Handle transaction timeout or blockhash expiry
    if (error.message?.includes('blockhash not found') || error.message?.includes('Transaction expired')) {
      throw new Error('Refund transaction expired. Please try again with a fresh transaction.');
    }

    throw error;
  }
}

/**
 * Initiate a dispute for an escrow transaction
 * Can be called by buyer or seller when there's an issue with the trade
 */
export async function initiateEscrowDispute(
  tradeId: number,
  reason: string,
  initiatorRole: 'buyer' | 'seller'
): Promise<{ disputeId: number; status: string }> {
  try {
    console.log('Initiating escrow dispute:', { tradeId, reason, initiatorRole });

    const response = await initiateDispute({
      tradeId,
      reason,
      initiatorRole
    });

    if (response.status === 201) {
      console.log('Dispute initiated successfully:', response.data);
      return response.data;
    } else {
      throw new Error(response.message || 'Failed to initiate dispute');
    }

  } catch (error) {
    console.error('Dispute initiation failed:', error);
    throw error;
  }
}

/**
 * Check if a trade is eligible for dispute
 * Disputes can only be initiated for escrowed trades within the deadline
 */
export function canInitiateDispute(
  tradeStatus: string,
  tradeCreatedAt: string,
  disputeDeadlineDays: number = 2,
  disputeStatus?: string
): { canDispute: boolean; reason?: string } {
  // Check if trade is in escrowed status
  if (tradeStatus !== 'escrowed') {
    return {
      canDispute: false,
      reason: 'Dispute can only be initiated for escrowed trades'
    };
  }

  // Check if dispute already exists
  if (disputeStatus && disputeStatus !== 'none') {
    return {
      canDispute: false,
      reason: 'A dispute already exists for this trade'
    };
  }

  // Check if dispute deadline has passed
  const createdDate = new Date(tradeCreatedAt);
  const deadlineDate = new Date(createdDate);
  deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);

  if (new Date() > deadlineDate) {
    return {
      canDispute: false,
      reason: 'Dispute deadline has passed'
    };
  }

  return { canDispute: true };
}

/**
 * Get dispute status information for display
 */
export function getDisputeStatusInfo(disputeStatus: string) {
  switch (disputeStatus) {
    case 'none':
      return {
        label: 'No Dispute',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100'
      };
    case 'open':
      return {
        label: 'Dispute Open',
        color: 'text-yellow-800',
        bgColor: 'bg-yellow-100'
      };
    case 'assigned':
      return {
        label: 'Under Review',
        color: 'text-blue-800',
        bgColor: 'bg-blue-100'
      };
    case 'resolved':
      return {
        label: 'Dispute Resolved',
        color: 'text-green-800',
        bgColor: 'bg-green-100'
      };
    default:
      return {
        label: 'Unknown Status',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100'
      };
  }
}
