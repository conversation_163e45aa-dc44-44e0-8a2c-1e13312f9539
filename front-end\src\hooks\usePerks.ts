import { addDays, differenceInSeconds } from "date-fns";
import { useState, useEffect } from "react";
import { fetchPerks } from "../axios/requests";
import { formatSecondsToHMS } from "../utils/helpers";
import { API_CONFIG } from "@/config/environment";



// Define types for our coin data
export type Coin = {
  timeLeft: string | undefined;
  remainingCount: number | undefined;
  soldCount: number | undefined;
  id: string | number;
  name: string;
  description: string;
  price: number | string;
  address: number;
  fulfillmentLink: string;
  imageUrl: string;
  isLimited: boolean;
  stockAmount: number;
  category:string,
  
  username: string;
  handle: string;
  
  isVerified?: boolean;
  user?: { id: number; username?: string; email?: string };
};

interface UseCoinOptions {
  category?: string;
  search?: string;
  sortBy?: "marketCap" | "price" | "newest";
  filterVerified?: boolean | null; // true = verified only, false = unverified only, null = all
}

/**
 * Hook for fetching and filtering coin data
 *
 * This hook handles:
 * - Data fetching from API (mock for now)
 * - Filtering by category
 * - Searching by name
 * - Sorting by different criteria
 * - Filter by verification status
 */
export function usePerks({
  category = "All",
  search = "",
  sortBy = "newest",
  filterVerified = null,
}: UseCoinOptions = {}) {
  const [perks, setPerks] = useState<Coin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Simulate API call with setTimeout
    const fetchData = async () => {
      try {
        setLoading(true);

        // if connect with server
        if (API_CONFIG.BASE_URL != undefined) {
          const result = await fetchPerks({
            search: search,
            sort: sortBy,
            order: "desc",
            page: 0,
            pageSize: 10,
          });

          // Debug: log raw API perks
          console.log('Raw perks from API:', result.data.perks);

          // Map API data to Coin[] format
          const apiData: Coin[] = result.data.perks.map(
            (token: any) => {
              // Debug: log each token and its user
              console.log('Mapped perk:', token, 'user:', token.user);
              const targetTime = token.time
                ? addDays(new Date(token.time), 1)
                : null;
              const timeLeftInSeconds = targetTime
                ? Math.max(differenceInSeconds(targetTime, new Date()), 0)
                : 0;
              return {
                id: String(token.perkId),
                name: token.name,
                description: token.description,
                price: `${Number(token.price).toFixed(2)} USD`,
                address: token.address,
                fulfillmentLink: token.fulfillmentLink,
                imageUrl: token.image || "/images/placeholder.png",
                isLimited: token.isLimited,
                isVerified: token.isVerified,
                username: token.user?.username || '',
                handle: `@ ${token.name.toLowerCase()}`,
                soldCount: Math.floor(Math.random() * 100), // Placeholder until real data
                remainingCount: token.stockAmount, // Placeholder until real data
                timeLeft: token.time
                  ? formatSecondsToHMS(timeLeftInSeconds)
                  : "--",
                category: token.category || "Uncategorized",
                user: token.user ? { id: token.user.id, username: token.user.username, email: token.user.email } : undefined,
              };
            }
          );

          // Debug: log mapped perks
          console.log('Mapped perks array:', apiData);

          setPerks(apiData);
        } else {
          // // In a real app, this would be an API call
          // // For now, we'll simulate a delay and return mock data
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Mock data generation - in a real app this would come from an API
          const mockData: any = Array.from({ length: 10 }, (_, i) => ({
            id: `coin-${i + 1}`,
            name: `Coin ${i + 1}`,
            imageUrl: "/images/placeholder.png",
            isLive: i % 3 === 0,
            isVerified: i % 2 === 0, // Alternate between verified and unverified
            price: `${(Math.random() * 10).toFixed(2)} USD`,
            username: `User ${i + 1}`,
            handle: `@user${i + 1}`,
            soldCount: Math.floor(Math.random() * 100),
            remainingCount: Math.floor(Math.random() * 10),
            timeLeft: `${Math.floor(Math.random() * 5)}h : ${Math.floor(
              Math.random() * 60
            )}m`,
            category: ["Art", "Music", "Photography", "Videography", "Utility"][
              i % 5
            ],
          }));
           setPerks(mockData);
        }

        
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error("Unknown error occurred")
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [category, search, sortBy, filterVerified]);

  // Apply filters to the data (in a real app, these might be API parameters)
  const filteredPerks = perks.filter((perk) => {
    // Filter by category
    if (category !== "All" && perk.category !== category) {
      return false;
    }

    // Filter by search
    if (search && !perk.name.toLowerCase().includes(search.toLowerCase())) {
      return false;
    }
    // Filter by verification status
    if (filterVerified == true && !perk.isVerified) {
      return false;
    }
    if (filterVerified == false && perk.isVerified) {
      return false;
    }

    return true;
  });

  // Sort the data
  const sortedPerks = [...filteredPerks].sort((a, b) => {
    if (sortBy === "price") {
      return parseFloat(String(b.price)) - parseFloat(String(a.price));
    }
    if (sortBy === "newest") {
      return parseInt(String(b.id).split("-")[1]) - parseInt(String(a.id).split("-")[1]);
    }
    // Default to marketCap (using soldCount as a proxy for now)
    return b.stockAmount - a.stockAmount;
  });

  return {
    perks: sortedPerks,
    loading,
    error,
    totalCount: perks.length,
    filteredCount: sortedPerks.length,
  };
}
