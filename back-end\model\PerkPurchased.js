const { Model, DataTypes } = require("sequelize");

class PerkPurchased extends Model {
    static initModel(sequelize) {
        return PerkPurchased.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                perkId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'perks',
                        key: 'perkId',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                amount: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 1,
                },
                price: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                },
                extra: {
                    type: DataTypes.JSON,
                    allowNull: true,
                },
                signature: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'perk_purchased',
                timestamps: true,
                indexes: [
                    {
                        unique: true,
                        fields: ['userId', 'perkId'], // Ensure a user can only purchase a perk once
                    },
                ],
            }
        );
    }

    // ✅ Define associations here
    static associate(models) {
        // PerkPurchased belongs to User
        PerkPurchased.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });

        // PerkPurchased belongs to Perk
        PerkPurchased.belongsTo(models.Perks, { foreignKey: 'perkId', as: 'perkDetails' });
    }
}

module.exports = PerkPurchased;
