import axios from 'axios'
import {
  loginPrivyUser,
  signupUser,
  addPerk,
  postTokenComment,
  updateProfile,
  getUserPurchasesSummary,
  fetchToken,
  buyPerk,
  buyToken,
  addAirDrop,
  claimAirDrop,
  setup2FA,
  verify2FALogin,
} from '../requests'
import {
  AppError,
  ValidationError,
  AuthenticationError,
  NetworkError,
} from '../../utils/errorHandling'

// Mock axios
jest.mock('axios')
const mockedAxios = axios as jest.Mocked<typeof axios>

// Mock environment config
jest.mock('@/config/environment', () => ({
  API_CONFIG: {
    BASE_URL: 'https://api.test.com',
    TIMEOUT: 10000,
  },
}))

describe('API Requests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset axios defaults
    mockedAxios.post.mockReset()
    mockedAxios.get.mockReset()
    mockedAxios.put.mockReset()
  })

  describe('loginPrivyUser', () => {
    const mockLoginData = {
      privyId: 'privy123',
      email: '<EMAIL>',
      privywallet: 'wallet123',
      referralBy: 'ref123',
    }

    it('should login user successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          data: { id: 1, username: 'testuser' },
          token: 'jwt-token',
        },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await loginPrivyUser(mockLoginData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/users/loginPrivy',
        mockLoginData
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle 400 validation error', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Invalid email format' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow(ValidationError)
      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow('Invalid email format')
    })

    it('should handle 401 authentication error', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Invalid credentials' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow(AuthenticationError)
      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow('Invalid credentials')
    })

    it('should handle network error', async () => {
      const mockError = { request: {} }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow(NetworkError)
      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow('No response from server')
    })

    it('should handle unknown error', async () => {
      const mockError = new Error('Unknown error')
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow(AppError)
      await expect(loginPrivyUser(mockLoginData)).rejects.toThrow('Unknown error')
    })

    it('should handle missing required fields', async () => {
      const incompleteData = { privyId: 'privy123' }

      const mockError = {
        response: {
          status: 400,
          data: { message: 'Email is required' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(loginPrivyUser(incompleteData)).rejects.toThrow(ValidationError)
    })
  })

  describe('signupUser', () => {
    const mockSignupData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
    }

    it('should signup user successfully', async () => {
      const mockResponse = {
        data: { status: 201, data: { id: 1, username: 'testuser' } },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await signupUser(mockSignupData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/users/addUser',
        mockSignupData
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle 409 user exists error', async () => {
      const mockError = {
        response: {
          status: 409,
          data: { message: 'User already exists' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(signupUser(mockSignupData)).rejects.toThrow(AppError)
      await expect(signupUser(mockSignupData)).rejects.toThrow('User already exists')
    })

    it('should handle weak password validation', async () => {
      const weakPasswordData = { ...mockSignupData, password: '123' }
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Password too weak' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(signupUser(weakPasswordData)).rejects.toThrow(ValidationError)
    })
  })

  describe('addPerk', () => {
    const mockPerkData = {
      name: 'Test Perk',
      description: 'Test description',
      price: '10.99',
      image: 'https://example.com/image.jpg',
      userId: 1,
    }

    it('should create perk successfully', async () => {
      const mockResponse = {
        data: { status: 201, data: { id: 1, ...mockPerkData } },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await addPerk(mockPerkData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/perks/addPerk',
        mockPerkData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle invalid price format', async () => {
      const invalidPerkData = { ...mockPerkData, price: 'invalid' }
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Invalid price format' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(addPerk(invalidPerkData)).rejects.toThrow(ValidationError)
    })

    it('should handle authorization error', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(addPerk(mockPerkData)).rejects.toThrow(AuthenticationError)
    })

    it('should handle server error', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(addPerk(mockPerkData)).rejects.toThrow(AppError)
      expect.objectContaining({ status: 500 })
    })
  })

  describe('postTokenComment', () => {
    const mockCommentData = {
      userId: 1,
      tokenId: 123,
      username: 'testuser',
      comment: 'Great token!',
      avatar: '/images/avatar.png',
    }

    beforeEach(() => {
      // Mock localStorage.getItem to return a token
      const mockLocalStorage = localStorage as jest.Mocked<typeof localStorage>
      mockLocalStorage.getItem.mockReturnValue('valid-token')
    })

    it('should post comment successfully', async () => {
      const mockResponse = {
        data: {
          status: 201,
          data: [
            { id: 1, comment: 'Great token!', username: 'testuser' },
          ],
        },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await postTokenComment(mockCommentData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/tokens/postComments',
        mockCommentData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle empty comment validation', async () => {
      const emptyCommentData = { ...mockCommentData, comment: '' }
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Comment cannot be empty' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(postTokenComment(emptyCommentData)).rejects.toThrow(ValidationError)
    })

    it('should handle rate limiting', async () => {
      const mockError = {
        response: {
          status: 429,
          data: { message: 'Too many comments' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(postTokenComment(mockCommentData)).rejects.toThrow(AppError)
    })
  })

  describe('updateProfile', () => {
    const mockProfileData = {
      userId: 1,
      name: 'Updated Name',
      bio: 'Updated bio',
      website: 'https://example.com',
    }

    it('should update profile successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          data: { id: 1, ...mockProfileData },
          message: 'Profile updated successfully',
        },
      }
      mockedAxios.put.mockResolvedValue(mockResponse)

      const result = await updateProfile(mockProfileData)

      expect(mockedAxios.put).toHaveBeenCalledWith(
        'https://api.test.com/users/updateUser',
        mockProfileData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle invalid website URL', async () => {
      const invalidProfileData = { ...mockProfileData, website: 'invalid-url' }
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Invalid website URL' },
        },
      }
      mockedAxios.put.mockRejectedValue(mockError)

      await expect(updateProfile(invalidProfileData)).rejects.toThrow(ValidationError)
    })

    it('should handle profile not found', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { message: 'Profile not found' },
        },
      }
      mockedAxios.put.mockRejectedValue(mockError)

      await expect(updateProfile(mockProfileData)).rejects.toThrow(AppError)
    })
  })

  describe('getUserPurchasesSummary', () => {
    const mockUserData = { userId: 1 }

    it('should fetch purchases successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          data: {
            PurchaseToken: [],
            Transactions: [],
            PurchasedPerks: [],
            MyToken: [],
          },
        },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await getUserPurchasesSummary(mockUserData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/users/getUserPurchasesSummary',
        mockUserData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle user not found', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { message: 'User not found' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(getUserPurchasesSummary(mockUserData)).rejects.toThrow(AppError)
    })

    it('should handle invalid user ID', async () => {
      const invalidUserData = { userId: -1 }
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Invalid user ID' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(getUserPurchasesSummary(invalidUserData)).rejects.toThrow(ValidationError)
    })
  })

  describe('buyPerk', () => {
    const mockPurchaseData = {
      userId: '1',
      perkId: '123',
      amount: 1,
      price: 10.99,
      signature: 'blockchain-signature',
    }

    it('should purchase perk successfully', async () => {
      const mockResponse = {
        data: { status: 200, message: 'Perk purchased successfully' },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await buyPerk(mockPurchaseData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/perks/buy',
        mockPurchaseData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle insufficient funds', async () => {
      const mockError = {
        response: {
          status: 402,
          data: { message: 'Insufficient funds' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(buyPerk(mockPurchaseData)).rejects.toThrow(AppError)
    })

    it('should handle out of stock', async () => {
      const mockError = {
        response: {
          status: 409,
          data: { message: 'Perk out of stock' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(buyPerk(mockPurchaseData)).rejects.toThrow(AppError)
    })
  })

  describe('claimAirDrop', () => {
    const mockAirdropData = {
      code: 'AIRDROP123',
      wallet: 'wallet-address',
      userId: 1,
    }

    it('should claim airdrop successfully', async () => {
      const mockResponse = {
        data: { status: 201, message: 'Airdrop claimed successfully' },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await claimAirDrop(mockAirdropData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/tokens/claimAirDrop',
        mockAirdropData
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle already claimed airdrop', async () => {
      const mockError = {
        response: {
          status: 409,
          data: { message: 'Airdrop already claimed' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(claimAirDrop(mockAirdropData)).rejects.toThrow(AppError)
    })

    it('should handle expired airdrop', async () => {
      const mockError = {
        response: {
          status: 410,
          data: { message: 'Airdrop expired' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(claimAirDrop(mockAirdropData)).rejects.toThrow(AppError)
    })

    it('should handle invalid airdrop code', async () => {
      const invalidData = { ...mockAirdropData, code: 'INVALID' }
      const mockError = {
        response: {
          status: 404,
          data: { message: 'Invalid airdrop code' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(claimAirDrop(invalidData)).rejects.toThrow(AppError)
    })
  })

  describe('setup2FA', () => {
    const mockSetupData = { userId: 1 }

    it('should setup 2FA successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          data: {
            qrCodeUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgA...',
            secret: 'JBSWY3DPEHPK3PXP',
          },
        },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await setup2FA(mockSetupData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/users/2fa/setup',
        mockSetupData,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer')
          })
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle 2FA already enabled', async () => {
      const mockError = {
        response: {
          status: 409,
          data: { message: '2FA already enabled' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(setup2FA(mockSetupData)).rejects.toThrow(AppError)
    })
  })

  describe('verify2FALogin', () => {
    const mockVerifyData = {
      userId: 1,
      token: '123456',
    }

    it('should verify 2FA successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          data: { id: 1, username: 'testuser' },
          token: 'jwt-token',
        },
      }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await verify2FALogin(mockVerifyData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.test.com/users/2fa/login',
        mockVerifyData
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle invalid 2FA token', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { message: 'Invalid 2FA token' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(verify2FALogin(mockVerifyData)).rejects.toThrow(ValidationError)
    })

    it('should handle expired 2FA token', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { message: '2FA token expired' },
        },
      }
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(verify2FALogin(mockVerifyData)).rejects.toThrow(AuthenticationError)
    })
  })

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle very large payloads', async () => {
      const largeData = {
        description: 'x'.repeat(100000), // 100KB string
      }
      const mockResponse = { data: { status: 200 } }
      mockedAxios.post.mockResolvedValue(mockResponse)

      await expect(addPerk(largeData)).resolves.not.toThrow()
    })

    it('should handle special characters in data', async () => {
      const specialData = {
        name: 'Test 中文 🚀 @#$%^&*()',
        email: '<EMAIL>',
      }
      const mockResponse = { data: { status: 200 } }
      mockedAxios.post.mockResolvedValue(mockResponse)

      await expect(signupUser(specialData)).resolves.not.toThrow()
    })

    it('should handle concurrent requests', async () => {
      const mockResponse = { data: { status: 200 } }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const promises = Array(10).fill(null).map(() => 
        loginPrivyUser({ privyId: 'test', email: '<EMAIL>' })
      )

      await expect(Promise.all(promises)).resolves.not.toThrow()
      expect(mockedAxios.post).toHaveBeenCalledTimes(10)
    })

    it('should handle timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 10000ms exceeded',
      }
      mockedAxios.post.mockRejectedValue(timeoutError)

      await expect(loginPrivyUser({ privyId: 'test' })).rejects.toThrow(AppError)
    })

    it('should handle malformed response data', async () => {
      const malformedResponse = { data: null }
      mockedAxios.post.mockResolvedValue(malformedResponse)

      const result = await loginPrivyUser({ privyId: 'test' })
      expect(result).toBeNull()
    })

    it('should handle empty response body', async () => {
      const emptyResponse = { data: '' }
      mockedAxios.post.mockResolvedValue(emptyResponse)

      const result = await loginPrivyUser({ privyId: 'test' })
      expect(result).toBe('')
    })
  })
}) 