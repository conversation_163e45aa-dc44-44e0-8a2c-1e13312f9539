import { addPerk } from '../../../axios/requests';
import { uploadToPinata } from '@/utils/helpers';
import { showErrorToast, showSuccessToast, TOAST_MESSAGES, createFormError, createAPIError } from '@/utils/errorHandling';

export interface FormData {
  name: string;
  description: string;
  price: string;
  fulfillmentLink: string;
  picture: File | null;
  limitedStock: boolean;
  stockAmount: string;
  tokenAmount: string;
  category: string;
}

export interface FormErrors {
  name: string;
  description: string;
  price: string;
  fulfillmentLink: string;
  picture: string;
  stockAmount: string;
  tokenAmount: string;
  category: string;
  api: string;
}

export const useFormSubmission = (
  formData: FormData,
  userID: number,
  onClose: () => void,
  setSubmitted: (value: boolean) => void,
  setIsSubmitting: (value: boolean) => void,
  setFormData: (data: FormData) => void,
  validateForm: (formData: FormData, userId: number) => FormErrors,
  hasErrors: (errors: FormErrors) => boolean,
  t: (key: string) => string
) => {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors = validateForm(formData, userID);
    if (!hasErrors(newErrors)) {
      setIsSubmitting(true);

      let imageUrl = '';
      if (formData.picture) {
        try {
          imageUrl = await uploadToPinata(formData.picture);
        } catch (error) {
          console.error('Image upload failed:', error);
          showErrorToast(createFormError(TOAST_MESSAGES.FORM.IMAGE_UPLOAD_FAILED));
          setIsSubmitting(false);
          return newErrors;
        }
      }

      // Get userId from localStorage if available
      let submissionUserId = userID;
      if (typeof window !== 'undefined' && !submissionUserId) {
        const storedUserBo = localStorage.getItem('userBo');
        if (storedUserBo) {
          try {
            const parsedUserBo = JSON.parse(storedUserBo);
            submissionUserId = parsedUserBo.id;
          } catch (error) {
            console.error('Error parsing stored userBo:', error);
            showErrorToast(createFormError(TOAST_MESSAGES.AUTH.LOGIN_REQUIRED));
            setIsSubmitting(false);
            return newErrors;
          }
        }
      }

      const newPerk = {
        name: formData.name,
        description: formData.description,
        price: formData.price,
        fulfillmentLink: formData.fulfillmentLink,
        image: imageUrl, // You may want to handle image upload separately
        isLimited: formData.limitedStock,
        stockAmount: parseInt(formData.stockAmount),
        tokenAmount: parseInt(formData.tokenAmount) || 1, // Default to 1 if not specified
        category: formData.category,
        userId: submissionUserId,
      };

      try {
        const response = await addPerk(newPerk);

        if (response.status === 201) {
          setSubmitted(true);
          showSuccessToast(TOAST_MESSAGES.PERK.CREATE_SUCCESS);

          // Reset form after 2 seconds
          setTimeout(() => {
            setFormData({
              name: '',
              description: '',
              price: '',
              fulfillmentLink: '',
              picture: null,
              limitedStock: false,
              stockAmount: '',
              tokenAmount: '1', // Default to 1 token
              category: '',
            });
            setSubmitted(false);
            onClose();
          }, 2000);
        } else {
          const errorMessage = response.data?.message || TOAST_MESSAGES.PERK.CREATE_FAILED;
          showErrorToast(createAPIError(errorMessage, response.status));
        }
      } catch (error: any) {
        console.error('Perk creation failed:', error);
        
        // Handle different types of errors with specific messages
        if (error.response?.status === 401) {
          showErrorToast(createFormError(TOAST_MESSAGES.AUTH.LOGIN_REQUIRED));
        } else if (error.response?.status === 400) {
          const errorMessage = error.response?.data?.message || TOAST_MESSAGES.FORM.VALIDATION_FAILED;
          showErrorToast(createFormError(errorMessage));
        } else if (error.response?.status >= 500) {
          showErrorToast(createAPIError(TOAST_MESSAGES.NETWORK.SERVER_ERROR, error.response?.status));
        } else {
          const errorMessage = error.response?.data?.message || error.message || TOAST_MESSAGES.PERK.CREATE_FAILED;
          showErrorToast(createAPIError(errorMessage));
        }
      } finally {
        setIsSubmitting(false);
      }
    }
    return newErrors;
  };

  return { handleSubmit };
};
