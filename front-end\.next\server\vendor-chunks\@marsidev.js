"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@marsidev";
exports.ids = ["vendor-chunks/@marsidev"];
exports.modules = {

/***/ "(ssr)/./node_modules/@marsidev/react-turnstile/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@marsidev/react-turnstile/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CONTAINER_ID: () => (/* binding */ DEFAULT_CONTAINER_ID),\n/* harmony export */   DEFAULT_ONLOAD_NAME: () => (/* binding */ DEFAULT_ONLOAD_NAME),\n/* harmony export */   DEFAULT_SCRIPT_ID: () => (/* binding */ DEFAULT_SCRIPT_ID),\n/* harmony export */   SCRIPT_URL: () => (/* binding */ SCRIPT_URL),\n/* harmony export */   Turnstile: () => (/* binding */ Turnstile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ DEFAULT_CONTAINER_ID,DEFAULT_ONLOAD_NAME,DEFAULT_SCRIPT_ID,SCRIPT_URL,Turnstile auto */ \n\nconst Component = ({ as: Element = \"div\", ...props }, ref)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Element, {\n        ...props,\n        ref\n    });\n};\nconst Container = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Component);\nconst SCRIPT_URL = \"https://challenges.cloudflare.com/turnstile/v0/api.js\";\nconst DEFAULT_SCRIPT_ID = \"cf-turnstile-script\";\nconst DEFAULT_CONTAINER_ID = \"cf-turnstile\";\nconst DEFAULT_ONLOAD_NAME = \"onloadTurnstileCallback\";\nconst checkElementExistence = (id)=>!!document.getElementById(id);\nconst injectTurnstileScript = ({ render = \"explicit\", onLoadCallbackName = DEFAULT_ONLOAD_NAME, scriptOptions: { nonce = \"\", defer = true, async = true, id = \"\", appendTo, onError, crossOrigin = \"\" } = {} })=>{\n    const scriptId = id || DEFAULT_SCRIPT_ID;\n    if (checkElementExistence(scriptId)) {\n        return;\n    }\n    const script = document.createElement(\"script\");\n    script.id = scriptId;\n    script.src = `${SCRIPT_URL}?onload=${onLoadCallbackName}&render=${render}`;\n    if (document.querySelector(`script[src=\"${script.src}\"]`)) {\n        return;\n    }\n    script.defer = !!defer;\n    script.async = !!async;\n    if (nonce) {\n        script.nonce = nonce;\n    }\n    if (crossOrigin) {\n        script.crossOrigin = crossOrigin;\n    }\n    if (onError) {\n        script.onerror = onError;\n    }\n    const parentEl = appendTo === \"body\" ? document.body : document.getElementsByTagName(\"head\")[0];\n    parentEl.appendChild(script);\n};\nconst CONTAINER_STYLE_SET = {\n    normal: {\n        width: 300,\n        height: 65\n    },\n    compact: {\n        width: 130,\n        height: 120\n    },\n    invisible: {\n        width: 0,\n        height: 0,\n        overflow: \"hidden\"\n    },\n    interactionOnly: {\n        width: \"fit-content\",\n        height: \"auto\"\n    }\n};\nfunction getTurnstileSizeOpts(size) {\n    let result;\n    if (size !== \"invisible\") {\n        result = size;\n    }\n    return result;\n}\nfunction useObserveScript(scriptId = DEFAULT_SCRIPT_ID) {\n    const [scriptLoaded, setScriptLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useObserveScript.useEffect\": ()=>{\n            const checkScriptExists = {\n                \"useObserveScript.useEffect.checkScriptExists\": ()=>{\n                    if (checkElementExistence(scriptId)) {\n                        setScriptLoaded(true);\n                    }\n                }\n            }[\"useObserveScript.useEffect.checkScriptExists\"];\n            const observer = new MutationObserver(checkScriptExists);\n            observer.observe(document, {\n                childList: true,\n                subtree: true\n            });\n            checkScriptExists();\n            return ({\n                \"useObserveScript.useEffect\": ()=>{\n                    observer.disconnect();\n                }\n            })[\"useObserveScript.useEffect\"];\n        }\n    }[\"useObserveScript.useEffect\"], [\n        scriptId\n    ]);\n    return scriptLoaded;\n}\nconst Turnstile = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n    const { scriptOptions, options = {}, siteKey, onWidgetLoad, onSuccess, onExpire, onError, onBeforeInteractive, onAfterInteractive, onUnsupported, onLoadScript, id, style, as = \"div\", injectScript = true, ...divProps } = props;\n    const widgetSize = options.size ?? \"normal\";\n    const [containerStyle, setContainerStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(options.execution === \"execute\" ? CONTAINER_STYLE_SET.invisible : options.appearance === \"interaction-only\" ? CONTAINER_STYLE_SET.interactionOnly : CONTAINER_STYLE_SET[widgetSize]);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const firstRendered = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [widgetId, setWidgetId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [turnstileLoaded, setTurnstileLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerId = id ?? DEFAULT_CONTAINER_ID;\n    const scriptId = injectScript ? scriptOptions?.id || `${DEFAULT_SCRIPT_ID}__${containerId}` : scriptOptions?.id || DEFAULT_SCRIPT_ID;\n    const scriptLoaded = useObserveScript(scriptId);\n    const onLoadCallbackName = scriptOptions?.onLoadCallbackName ? `${scriptOptions.onLoadCallbackName}__${containerId}` : `${DEFAULT_ONLOAD_NAME}__${containerId}`;\n    const renderConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Turnstile.useMemo[renderConfig]\": ()=>({\n                sitekey: siteKey,\n                action: options.action,\n                cData: options.cData,\n                callback: onSuccess,\n                \"error-callback\": onError,\n                \"expired-callback\": onExpire,\n                \"before-interactive-callback\": onBeforeInteractive,\n                \"after-interactive-callback\": onAfterInteractive,\n                \"unsupported-callback\": onUnsupported,\n                theme: options.theme ?? \"auto\",\n                language: options.language ?? \"auto\",\n                tabindex: options.tabIndex,\n                \"response-field\": options.responseField,\n                \"response-field-name\": options.responseFieldName,\n                size: getTurnstileSizeOpts(widgetSize),\n                retry: options.retry ?? \"auto\",\n                \"retry-interval\": options.retryInterval ?? 8e3,\n                \"refresh-expired\": options.refreshExpired ?? \"auto\",\n                execution: options.execution ?? \"render\",\n                appearance: options.appearance ?? \"always\"\n            })\n    }[\"Turnstile.useMemo[renderConfig]\"], [\n        siteKey,\n        options,\n        onSuccess,\n        onError,\n        onExpire,\n        widgetSize,\n        onBeforeInteractive,\n        onAfterInteractive,\n        onUnsupported\n    ]);\n    const renderConfigStringified = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Turnstile.useMemo[renderConfigStringified]\": ()=>JSON.stringify(renderConfig)\n    }[\"Turnstile.useMemo[renderConfigStringified]\"], [\n        renderConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"Turnstile.useImperativeHandle\": ()=>{\n            if (true) {\n                return;\n            }\n            const { turnstile } = window;\n            return {\n                getResponse () {\n                    if (!turnstile?.getResponse || !widgetId) {\n                        console.warn(\"Turnstile has not been loaded\");\n                        return;\n                    }\n                    return turnstile.getResponse(widgetId);\n                },\n                reset () {\n                    if (!turnstile?.reset || !widgetId) {\n                        console.warn(\"Turnstile has not been loaded\");\n                        return;\n                    }\n                    if (options.execution === \"execute\") {\n                        setContainerStyle(CONTAINER_STYLE_SET.invisible);\n                    }\n                    try {\n                        turnstile.reset(widgetId);\n                    } catch (error) {\n                        console.warn(`Failed to reset Turnstile widget ${widgetId}`, error);\n                    }\n                },\n                remove () {\n                    if (!turnstile?.remove || !widgetId) {\n                        console.warn(\"Turnstile has not been loaded\");\n                        return;\n                    }\n                    setWidgetId(\"\");\n                    setContainerStyle(CONTAINER_STYLE_SET.invisible);\n                    turnstile.remove(widgetId);\n                },\n                render () {\n                    if (!turnstile?.render || !containerRef.current || widgetId) {\n                        console.warn(\"Turnstile has not been loaded or widget already rendered\");\n                        return;\n                    }\n                    const id2 = turnstile.render(containerRef.current, renderConfig);\n                    setWidgetId(id2);\n                    if (options.execution !== \"execute\") {\n                        setContainerStyle(CONTAINER_STYLE_SET[widgetSize]);\n                    }\n                    return id2;\n                },\n                execute () {\n                    if (options.execution !== \"execute\") {\n                        return;\n                    }\n                    if (!turnstile?.execute || !containerRef.current || !widgetId) {\n                        console.warn(\"Turnstile has not been loaded or widget has not been rendered\");\n                        return;\n                    }\n                    turnstile.execute(containerRef.current, renderConfig);\n                    setContainerStyle(CONTAINER_STYLE_SET[widgetSize]);\n                },\n                isExpired () {\n                    if (!turnstile?.isExpired || !widgetId) {\n                        console.warn(\"Turnstile has not been loaded\");\n                        return;\n                    }\n                    return turnstile.isExpired(widgetId);\n                }\n            };\n        }\n    }[\"Turnstile.useImperativeHandle\"], [\n        scriptLoaded,\n        widgetId,\n        options.execution,\n        widgetSize,\n        renderConfig,\n        containerRef\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            window[onLoadCallbackName] = ({\n                \"Turnstile.useEffect\": ()=>setTurnstileLoaded(true)\n            })[\"Turnstile.useEffect\"];\n            return ({\n                \"Turnstile.useEffect\": ()=>{\n                    delete window[onLoadCallbackName];\n                }\n            })[\"Turnstile.useEffect\"];\n        }\n    }[\"Turnstile.useEffect\"], [\n        onLoadCallbackName\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (injectScript && !turnstileLoaded) {\n                injectTurnstileScript({\n                    onLoadCallbackName,\n                    scriptOptions: {\n                        ...scriptOptions,\n                        id: scriptId\n                    }\n                });\n            }\n        }\n    }[\"Turnstile.useEffect\"], [\n        injectScript,\n        turnstileLoaded,\n        onLoadCallbackName,\n        scriptOptions,\n        scriptId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (scriptLoaded && !turnstileLoaded && window.turnstile) {\n                setTurnstileLoaded(true);\n            }\n        }\n    }[\"Turnstile.useEffect\"], [\n        turnstileLoaded,\n        scriptLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (!siteKey) {\n                console.warn(\"sitekey was not provided\");\n                return;\n            }\n            if (!scriptLoaded || !containerRef.current || !turnstileLoaded || firstRendered.current) {\n                return;\n            }\n            const id2 = window.turnstile.render(containerRef.current, renderConfig);\n            setWidgetId(id2);\n            firstRendered.current = true;\n        }\n    }[\"Turnstile.useEffect\"], [\n        scriptLoaded,\n        siteKey,\n        renderConfig,\n        firstRendered,\n        turnstileLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (!window.turnstile) return;\n            if (containerRef.current && widgetId) {\n                if (checkElementExistence(widgetId)) {\n                    window.turnstile.remove(widgetId);\n                }\n                const newWidgetId = window.turnstile.render(containerRef.current, renderConfig);\n                setWidgetId(newWidgetId);\n                firstRendered.current = true;\n            }\n        }\n    }[\"Turnstile.useEffect\"], [\n        renderConfigStringified,\n        siteKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (!window.turnstile) return;\n            if (!widgetId) return;\n            if (!checkElementExistence(widgetId)) return;\n            onWidgetLoad?.(widgetId);\n            return ({\n                \"Turnstile.useEffect\": ()=>{\n                    window.turnstile.remove(widgetId);\n                }\n            })[\"Turnstile.useEffect\"];\n        }\n    }[\"Turnstile.useEffect\"], [\n        widgetId,\n        onWidgetLoad\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            setContainerStyle(options.execution === \"execute\" ? CONTAINER_STYLE_SET.invisible : renderConfig.appearance === \"interaction-only\" ? CONTAINER_STYLE_SET.interactionOnly : CONTAINER_STYLE_SET[widgetSize]);\n        }\n    }[\"Turnstile.useEffect\"], [\n        options.execution,\n        widgetSize,\n        renderConfig.appearance\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Turnstile.useEffect\": ()=>{\n            if (!scriptLoaded || typeof onLoadScript !== \"function\") return;\n            onLoadScript();\n        }\n    }[\"Turnstile.useEffect\"], [\n        scriptLoaded,\n        onLoadScript\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Container, {\n        ref: containerRef,\n        as,\n        id: containerId,\n        style: {\n            ...containerStyle,\n            ...style\n        },\n        ...divProps\n    });\n});\nTurnstile.displayName = \"Turnstile\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@marsidev/react-turnstile/dist/index.mjs\n");

/***/ })

};
;