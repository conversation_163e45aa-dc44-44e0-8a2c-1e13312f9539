'use client';

import { motion } from 'framer-motion';

import { fadeInUp } from '@/lib/animations';

interface EmptyStateProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

const EmptyState = ({
  title,
  description,
  icon,
  action,
  className = '',
}: EmptyStateProps) => (
  <motion.div
    className={`flex flex-col items-center justify-center py-16 px-8 text-center ${className}`}
    {...fadeInUp}
  >
    <motion.div
      className="mb-6 p-4 bg-gray-100 rounded-full"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
    >
      {icon}
    </motion.div>
    <motion.h3
      className="text-xl font-semibold text-gray-900 mb-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.3 }}
    >
      {title}
    </motion.h3>
    <motion.p
      className="text-gray-600 mb-6 max-w-md"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.4 }}
    >
      {description}
    </motion.p>
    {action && (
      <motion.button
        className="bg-[#FF6600] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a00] transition-colors"
        onClick={action.onClick}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {action.label}
      </motion.button>
    )}
  </motion.div>
);

export default EmptyState; 