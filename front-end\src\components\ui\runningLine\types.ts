export interface Message {
    id: string;
    text: string;
    type: 'buy' | 'sell' | 'airdrop' | 'burn' | 'general';
    icon: string;
    timestamp?: Date;
    tradeId?: number | string; // Added for chat opening
    receiverId?: number | string; // Added for chat opening
    chatRoomId?: string; // Added for chat notifications
}

export interface RunningLineProps {
    messages?: Message[];
    speed?: 'slow' | 'normal' | 'fast';
    pauseOnHover?: boolean;
    showControls?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
}