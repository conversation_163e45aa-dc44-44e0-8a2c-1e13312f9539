"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_reviews_index_tsx";
exports.ids = ["_ssr_src_components_shared_reviews_index_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/reviews/index.tsx":
/*!*************************************************!*\
  !*** ./src/components/shared/reviews/index.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../axios/requests */ \"(ssr)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contexts/AppContext */ \"(ssr)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/errorHandling */ \"(ssr)/./src/utils/errorHandling.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst defaultReviews = [\n    {\n        id: 1,\n        name: \"User\",\n        date: \"Apr 18, 2024\",\n        review: \"I think this coin has good projects\",\n        avatar: \"/images/placeholder.png\"\n    },\n    {\n        id: 2,\n        name: \"User\",\n        date: \"Apr 18, 2024\",\n        review: \"No, they are selling bad products\",\n        avatar: \"/images/placeholder.png\"\n    },\n    {\n        id: 3,\n        name: \"User\",\n        date: \"Apr 17, 2024\",\n        review: \"I lost money with this investment\",\n        avatar: \"/images/placeholder.png\"\n    }\n];\nconst Reviews = ({ perkId, reviews = defaultReviews, canPost = false, onSort, onShowDetails })=>{\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [reviewsShow, setReviewsShow] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { state, logout } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__.useAppContext)();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Reviews.useEffect\": ()=>{\n            setReviewsShow(reviews);\n        }\n    }[\"Reviews.useEffect\"], [\n        reviews\n    ]); // Run the effect when `id` changes\n    // Transform incoming raw comments to internal format\n    const displayedReviews = reviewsShow.map((review, index)=>{\n        const dateObj = new Date(review.updatedAt);\n        const formattedDate = dateObj.toLocaleString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true,\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n        return {\n            id: review.id ?? index,\n            name: review.UserName || \"Anonymous\",\n            date: formattedDate,\n            review: review.Review || \"\",\n            avatar: review.Avatar || \"/images/placeholder.png\"\n        };\n    });\n    const handlePostComment = async ()=>{\n        if (!newComment.trim()) {\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_6__.showErrorToast)(\"Review cannot be empty.\");\n            return;\n        }\n        const userId = state?.userBo?.id;\n        const username = state?.userBo?.username;\n        const avatar = \"/images/placeholder.png\";\n        if (!userId) {\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_6__.showErrorToast)(\"Please login!\");\n            return;\n        }\n        try {\n            const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.postPerkReviews)({\n                userId,\n                perkId: perkId,\n                username,\n                review: newComment,\n                avatar\n            });\n            setReviewsShow(response.data);\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_6__.showSuccessToast)(\"Thanks for posting your comment.\");\n            setNewComment(\"\"); // Clear the input field after posting\n        } catch (error) {\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_6__.showErrorToast)(\"Failed to post comment. Please try again.\");\n            console.error(\"Post comment error:\", error);\n        }\n    };\n    // Function to determine margin bottom based on review index\n    const getMarginBottom = (index)=>{\n        if (index === 0) return \"mb-[28px]\";\n        if (index === 1) return \"mb-[31px]\";\n        if (index === 2) return \"mb-[30px]\";\n        return \"mb-6\"; // Default margin for any additional reviews\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex flex-col border border-[#F58A38] rounded-[10px] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[76px] px-[99px] py-4 bg-[#F58A38] flex justify-center items-center gap-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center justify-center text-white text-2xl font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                    children: t('reviews.title', 'Reviews')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-98 bg-[#FFF7F1] rounded-bl-[10px] rounded-br-[10px] p-0 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: displayedReviews.map((review, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center pl-6 ${getMarginBottom(index)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-14 h-14 bg-stone-300 rounded-[100px] relative mr-4\",\n                                            children: review.avatar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: review.avatar,\n                                                alt: review.name,\n                                                fill: true,\n                                                className: \"object-cover rounded-[100px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-start items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-black text-base font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                                                        children: review.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-black text-xs font-light font-['IBM_Plex_Sans'] leading-normal\",\n                                                        children: review.review\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500 text-[11px] mt-1 font-['IBM_Plex_Sans']\",\n                                                        children: review.date\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-white/30 rounded-[50px] backdrop-blur-[1.50px] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/icons/hurt.svg\",\n                                                alt: \"Heart icon\",\n                                                width: 16,\n                                                height: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, review.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-5\",\n                            children: !canPost ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-[67px] bg-[#C4C4C4] rounded-lg flex items-center justify-center text-white text-xl font-semibold font-['IBM_Plex_Sans'] leading-normal mb-2 hover:bg-black hover:text-white transition-colors duration-300 cursor-pointer text-center\",\n                                children: t('reviews.mustHavePerk', 'You should have this perk to Review')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: newComment,\n                                        onChange: (e)=>setNewComment(e.target.value),\n                                        placeholder: t('reviews.writeAReview', 'Write a review...'),\n                                        className: \"flex-1 h-12 px-4 rounded-lg border border-gray-300 focus:outline-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: handlePostComment,\n                                        className: \"w-24 h-12 bg-[#C4C4C4] rounded-lg flex items-center justify-center text-white text-lg font-semibold font-['IBM_Plex_Sans'] hover:bg-black hover:text-white transition-colors duration-300 cursor-pointer\",\n                                        children: t('reviews.post', 'Post')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\reviews\\\\index.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Reviews);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/reviews/index.tsx\n");

/***/ })

};
;