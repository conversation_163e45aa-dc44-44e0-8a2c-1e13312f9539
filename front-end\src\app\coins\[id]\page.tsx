"use client";
import { usePrivy } from "@privy-io/react-auth";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useAppContext } from "@/contexts/AppContext";
import { fetchTokenDetails } from "../../../axios/requests";
import dynamic from "next/dynamic";

const ErrorState = dynamic(() => import('./error-state').then(mod => ({ default: mod.ErrorState })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const PageTransition = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.PageTransition })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const LoadingSkeleton = dynamic(() => import('./skeletons').then(mod => ({ default: mod.LoadingSkeleton })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const TokenContent = dynamic(() => import('./token-content').then(mod => ({ default: mod.TokenContent })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

export default function TokenPage() {
  const params = useParams();
  const idParam = params?.id;
  const id = Array.isArray(idParam) ? idParam[0] : idParam ?? "0";
  const state = useAppContext();
  const { login } = usePrivy();

  const [tokenData, setTokenData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTokenData = async () => {
      if (id) {
        setLoading(true);
        setError(null);
        try {
          const response = await fetchTokenDetails(id);
          if (response.status === 200) {
            setTokenData(response.data);
          } else {
            setError("Failed to fetch token details");
            console.error("Failed to fetch token:", response);
          }
        } catch (error) {
          setError("Error fetching token data");
          console.error("Error fetching token data:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadTokenData();
  }, [id]);

  return (
    <PageTransition>
      {loading ? (
        <LoadingSkeleton />
      ) : error || !tokenData ? (
        <ErrorState error={error} />
      ) : (
        <TokenContent
          tokenData={tokenData}
          isLoggedIn={state.state.isLoggedIn}
          onLogin={login}
        />
      )}
    </PageTransition>
  );
}
