"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_header_NavigationItems_tsx";
exports.ids = ["_ssr_src_components_shared_header_NavigationItems_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/header/NavigationItems.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shared/header/NavigationItems.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n\n\n\n\n\n\nconst NavigationItems = ({ isActiveRoute, handleNavItemClick, hideCreate, handleCreateTokenClick, handleCreatePerkClick })=>{\n    const { t, isReady, i18n, currentLanguage } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const navigationItems = [\n        {\n            label: t('navigation.dashboard'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.DASHBOARD,\n            icon: \"/icons/dashboard.svg\",\n            activeIcon: \"/icons/dashboard.svg\",\n            requiresAuth: true\n        },\n        {\n            label: t('navigation.perks'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.PERKS,\n            icon: \"/icons/perks.svg\",\n            activeIcon: \"/icons/perks.svg\",\n            requiresAuth: false\n        },\n        {\n            label: t('navigation.coins'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.HOME,\n            icon: \"/icons/coin.svg\",\n            activeIcon: \"/icons/coin.svg\",\n            requiresAuth: false\n        }\n    ];\n    return !isReady ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        className: \"hidden lg:flex items-center gap-2 xl:gap-3 2xl:gap-4\",\n        variants: {\n            hidden: {\n                opacity: 0,\n                x: 20\n            },\n            visible: {\n                opacity: 1,\n                x: 0,\n                transition: {\n                    staggerChildren: 0.1,\n                    delayChildren: 0.2\n                }\n            }\n        },\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: {\n                        hidden: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        visible: {\n                            opacity: 1,\n                            x: 0\n                        }\n                    },\n                    className: `group h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center gap-2 rounded-full text-sm lg:text-base transition-all duration-300 text-white cursor-pointer ${isActiveRoute(item.route) ? \"bg-black/20 scale-105\" : \"hover:bg-black/10\"}`,\n                    onClick: ()=>handleNavItemClick(item),\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: isActiveRoute(item.route) ? item.activeIcon : item.icon,\n                            alt: item.label,\n                            width: 20,\n                            height: 20,\n                            className: \"invert group-hover:brightness-0 group-hover:invert filter transition-filter duration-300 ease-in-out h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden xl:inline\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, item.label, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, undefined)),\n            !hideCreate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: {\n                            hidden: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            visible: {\n                                opacity: 1,\n                                x: 0\n                            }\n                        },\n                        className: \"bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30\",\n                        onClick: handleCreateTokenClick,\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:inline\",\n                                children: t('coins.createCoin')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"lg:hidden\",\n                                children: t('coins.createCoin')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: {\n                            hidden: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            visible: {\n                                opacity: 1,\n                                x: 0\n                            }\n                        },\n                        className: \"bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30 ml-1\",\n                        onClick: handleCreatePerkClick,\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:inline\",\n                                children: t('perks.createPerk')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"lg:hidden\",\n                                children: t('perks.perk')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavigationItems);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/header/NavigationItems.tsx\n");

/***/ })

};
;