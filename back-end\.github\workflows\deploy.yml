name: Deploy to AWS VPS

on:
  push:
    branches:
      - bleeding-edge

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: install dependencies
      run: yarn install

    - name: Copy files to AWS VPS
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_SSH_KEY }}
        port: 22
        source: "apis/**,i18n.js,scripts/**,tsconfig.json,app.config.js,locales/**,package.json,server.js,utilities/**,controller/**,middleware/**,package-lock.json,services/**,db/**,model/**,setupDatabase.js"
        target: "/home/<USER>/back-end/"
        debug: true

    - name: Run remote commands on AWS VPS
      uses: appleboy/ssh-action@v1
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_SSH_KEY }}
        port: 22
        script: |
          cd /home/<USER>/back-end/
          yarn install
          ~/deploy.sh
          npm run db:migrate
          pm2 restart funhi-backend || pm2 start server.js --name funhi-backend
        debug: true
