"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId,\n            hasTradeId: !!notification.data.tradeId,\n            hasChatRoomId: !!notification.data.chatRoomId,\n            hasPerkId: !!notification.data.perkId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Use getUserTrades to find trades for current user and filter by perkId\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(myUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        // Find active trade for this perk\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(perkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            // Get the most recent trade\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeIdToUse = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    activeTrade = {\n                        id: tradeResponse.data.id,\n                        status: tradeResponse.data.status,\n                        tradeId: tradeResponse.data.id,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to notification data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: notification.data.status || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: notification.data.perkId,\n                        escrowId: notification.data.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, don't close it, just focus it\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, focusing existing modal');\n            existingModal.scrollIntoView({\n                behavior: 'smooth'\n            });\n            return;\n        }\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: createReleaseFunction(notification, tradeIdToUse),\n                onRefund: createRefundFunction(notification, tradeIdToUse),\n                onReport: ()=>{},\n                onAccept: createAcceptFunction(notification, tradeIdToUse),\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 726,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 742,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 752,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 760,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 769,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 768,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 778,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 777,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 785,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 804,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 802,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 811,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 841,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 864,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 966,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 925,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1002,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1001,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 862,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 839,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYXJlZC9ub3RpZmljYXRpb25zL05vdGlmaWNhdGlvbkJlbGwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJEO0FBQ1g7QUFDNkc7QUFDOUY7QUFDakI7QUFDb0I7QUFDUDtBQUNlO0FBQ3hCO0FBc0JuQyxTQUFTaUI7O0lBQ3RCLE1BQU0sQ0FBQ0MscUJBQXFCQyx1QkFBdUIsR0FBR2xCLCtDQUFRQSxDQUFpQixFQUFFO0lBQ2pGLE1BQU0sQ0FBQ21CLG9CQUFvQkMsc0JBQXNCLEdBQUdwQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUMvRSxNQUFNLENBQUNxQixtQkFBbUJDLHFCQUFxQixHQUFHdEIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDdUIsa0JBQWtCQyxvQkFBb0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3lCLFdBQVdDLGFBQWEsR0FBRzFCLCtDQUFRQSxDQUFVO0lBQ3BELE1BQU0sQ0FBQzJCLFFBQVFDLFVBQVUsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQzZCLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0rQixjQUFjN0IsNkNBQU1BLENBQWlCO0lBQzNDLE1BQU0sRUFBRThCLElBQUksRUFBRSxHQUFHN0IsdURBQVFBO0lBQ3pCLE1BQU0sRUFBRThCLFNBQVMsRUFBRUMsVUFBVSxFQUFFLEdBQUd4Qiw0RUFBY0E7SUFDaEQsTUFBTSxFQUFFeUIsWUFBWSxFQUFFQyxXQUFXLEVBQUUsR0FBR3pCLDJEQUFTQTtJQUMvQyxNQUFNMEIsbUJBQW1CekIsK0VBQW1CQTtJQUU1QywrQ0FBK0M7SUFDL0MsTUFBTTBCLG9CQUFvQjtRQUN4QixJQUFJLENBQUNOLE1BQU07UUFFWCxJQUFJO1lBQ0ZGLFdBQVc7WUFDWCxNQUFNLENBQUNTLHVCQUF1QkMsZUFBZSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDaEV0QyxpRUFBZ0JBLENBQUM7b0JBQUV1QyxNQUFNO29CQUFHQyxVQUFVO2dCQUFHO2dCQUN6Q3ZDLDJFQUEwQkE7YUFDM0I7WUFFRCxJQUFJa0Msc0JBQXNCTSxNQUFNLEtBQUssS0FBSztnQkFDeEMsTUFBTUMsbUJBQW1CUCxzQkFBc0JRLElBQUksQ0FBQ0MsYUFBYTtnQkFFakUsMERBQTBEO2dCQUMxRCxNQUFNQyxhQUE2QixFQUFFO2dCQUNyQyxNQUFNQyxZQUE0QixFQUFFO2dCQUVwQ0osaUJBQWlCSyxPQUFPLENBQUMsQ0FBQ0M7b0JBQ3hCLElBQUlDLG9CQUFvQkQsZUFBZTt3QkFDckNGLFVBQVVJLElBQUksQ0FBQ0Y7b0JBQ2pCLE9BQU87d0JBQ0xILFdBQVdLLElBQUksQ0FBQ0Y7b0JBQ2xCO2dCQUNGO2dCQUVBRyxRQUFRQyxHQUFHLENBQUMsc0RBQXNEO29CQUNoRUMsb0JBQW9CWCxpQkFBaUJZLE1BQU07b0JBQzNDekMscUJBQXFCZ0MsV0FBV1MsTUFBTTtvQkFDdEN2QyxvQkFBb0IrQixVQUFVUSxNQUFNO29CQUNwQ0MsYUFBYVYsV0FBV1csR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO29CQUN2Q0MsWUFBWWIsVUFBVVUsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO2dCQUN2QztnQkFFQTVDLHVCQUF1QitCO2dCQUN2QjdCLHNCQUFzQjhCO2dCQUV0Qiw0Q0FBNEM7Z0JBQzVDLE1BQU1jLGVBQWVmLFdBQVdnQixNQUFNLENBQUNKLENBQUFBLElBQUssQ0FBQ0EsRUFBRUssTUFBTSxFQUFFUixNQUFNO2dCQUM3RCxNQUFNUyxjQUFjakIsVUFBVWUsTUFBTSxDQUFDSixDQUFBQSxJQUFLLENBQUNBLEVBQUVLLE1BQU0sRUFBRVIsTUFBTTtnQkFFM0RwQyxxQkFBcUIwQztnQkFDckJ4QyxvQkFBb0IyQztZQUN0QjtRQUVBLG1FQUFtRTtRQUNuRSxtRUFBbUU7UUFDckUsRUFBRSxPQUFPQyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pELFNBQVU7WUFDUnRDLFdBQVc7UUFDYjtJQUNGO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU11QixzQkFBc0IsQ0FBQ0Q7UUFDM0IsNERBQTREO1FBQzVELE1BQU1PLGNBQWM7WUFDbEI7WUFDQTtZQUNBO1lBQ0E7WUFDQSwrREFBK0Q7WUFDL0Q7WUFDQTtTQUNEO1FBRUQsdURBQXVEO1FBQ3ZELElBQUlBLFlBQVlVLFFBQVEsQ0FBQ2pCLGFBQWFVLElBQUksR0FBRztZQUMzQyxPQUFPO1FBQ1Q7UUFFQSxvRUFBb0U7UUFDcEUsTUFBTUMsYUFBYTtZQUNqQixpQ0FBaUM7WUFDakM7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0EsNkNBQTZDO1lBQzdDO1lBQ0EscUNBQXFDO1lBQ3JDO1NBQ0Q7UUFFRCxtQ0FBbUM7UUFDbkMsT0FBT0EsV0FBV00sUUFBUSxDQUFDakIsYUFBYVUsSUFBSTtJQUM5QztJQUVBLHFDQUFxQztJQUNyQyxNQUFNUSxxQkFBcUIsQ0FBQ2xCO1lBQ3JCQSxvQkFBK0JBO1FBQXBDLElBQUksR0FBQ0EscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUJtQixPQUFPLEtBQUksR0FBQ25CLHNCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSywwQ0FBQUEsb0JBQW1Cb0IsUUFBUSxHQUFFLE9BQU87UUFFeEUsTUFBTUMsU0FBU3pDLGlCQUFBQSwyQkFBQUEsS0FBTTBDLEVBQUU7UUFDdkIsTUFBTUMsZUFBZSxPQUFPRixXQUFXLFdBQVdHLFNBQVNILFVBQVVBO1FBRXJFLElBQUlyQixhQUFhTCxJQUFJLENBQUN3QixPQUFPLEtBQUtJLGNBQWMsT0FBTztRQUN2RCxJQUFJdkIsYUFBYUwsSUFBSSxDQUFDeUIsUUFBUSxLQUFLRyxjQUFjLE9BQU87UUFDeEQsT0FBTztJQUNUO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU1FLHFCQUFxQixDQUFDTixTQUFpQkMsVUFBa0JNO1FBQzdELE9BQU8sR0FBY04sT0FBWEQsU0FBUSxLQUFlTyxPQUFaTixVQUFTLEtBQVUsT0FBUE07SUFDbkM7SUFFQSxzRUFBc0U7SUFDdEUsTUFBTUMsc0JBQXNCLENBQUMzQjtZQU93QkEsb0JBQW1DQTtRQU50RixNQUFNNEIsWUFBWTtZQUNoQjtZQUFhO1lBQWtCO1lBQW1CO1lBQ2xEO1lBQWtCO1lBQW9CO1lBQWtCO1lBQ3hEO1lBQWtCO1lBQTZCO1lBQy9DO1lBQTJCO1NBQzVCO1FBQ0QsT0FBT0EsVUFBVVgsUUFBUSxDQUFDakIsYUFBYVUsSUFBSSxLQUFNLEVBQUMsR0FBQ1YscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI2QixVQUFVLEtBQUksQ0FBQyxHQUFDN0Isc0JBQUFBLGFBQWFMLElBQUksY0FBakJLLDBDQUFBQSxvQkFBbUI4QixPQUFPLENBQUQ7SUFDakg7SUFFQSxtRkFBbUY7SUFDbkYsTUFBTUMsd0JBQXdCLENBQUMvQixjQUE0QmdDO1FBQ3pELE9BQU87Z0JBQ21DaEM7WUFBeEMsTUFBTWlDLGVBQWVELHFCQUFtQmhDLHFCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSyx5Q0FBQUEsbUJBQW1COEIsT0FBTztZQUNsRSxJQUFJLENBQUNHLGdCQUFnQixFQUFDbEQseUJBQUFBLG1DQUFBQSxhQUFjbUQsT0FBTyxLQUFJLENBQUNsRCxhQUFhO2dCQUMzRG1CLFFBQVFhLEtBQUssQ0FBQyxrREFBa0Q7b0JBQzlEYyxTQUFTRztvQkFDVEUsTUFBTSxFQUFFcEQseUJBQUFBLG1DQUFBQSxhQUFjbUQsT0FBTztvQkFDN0JFLFdBQVdwRDtnQkFDYjtnQkFDQTtZQUNGO1lBRUEsSUFBSTtvQkE2QkFxRDtnQkE1QkZsQyxRQUFRQyxHQUFHLENBQUM7Z0JBRVoseUNBQXlDO2dCQUN6QyxNQUFNa0MsZ0JBQWdCLE1BQU1DLE1BQU0sR0FBc0NOLE9BQW5DdEUsMkRBQVVBLENBQUM2RSxRQUFRLEVBQUMsaUJBQTRCLE9BQWJQLGNBQWEsaUJBQWU7b0JBQ2xHUSxTQUFTO3dCQUNQLGlCQUFpQixVQUF3QyxPQUE5QkMsYUFBYUMsT0FBTyxDQUFDO29CQUNsRDtnQkFDRjtnQkFFQSxJQUFJLENBQUNMLGNBQWNNLEVBQUUsRUFBRTtvQkFDckIsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNQyxZQUFZLE1BQU1SLGNBQWNTLElBQUk7Z0JBQzFDLE1BQU1WLFFBQVFTLFVBQVVuRCxJQUFJO2dCQUU1QlEsUUFBUUMsR0FBRyxDQUFDLGlEQUFpRDtvQkFDM0Q0QyxVQUFVWCxNQUFNVyxRQUFRO29CQUN4QkMsY0FBY1osTUFBTVksWUFBWTtvQkFDaENDLGVBQWViLE1BQU1hLGFBQWE7b0JBQ2xDQyxJQUFJZCxNQUFNYyxFQUFFO29CQUNaQyxNQUFNZixNQUFNZSxJQUFJO2dCQUNsQjtnQkFFQSxpREFBaUQ7Z0JBQ2pELE1BQU1uRSxpQkFBaUJvRSxXQUFXLENBQ2hDaEIsTUFBTVcsUUFBUSxFQUNkLCtDQUNBWCxFQUFBQSxzQkFBQUEsTUFBTVksWUFBWSxjQUFsQlosMENBQUFBLG9CQUFvQmlCLFlBQVksS0FBSWpCLE1BQU1hLGFBQWEsRUFDdkRiLE1BQU1jLEVBQUUsRUFDUmQsTUFBTWUsSUFBSSxFQUNWckUsY0FDQSxNQUFNLGlCQUFpQjs7Z0JBR3pCLDhCQUE4QjtnQkFDOUIsTUFBTTNCLDREQUFXQSxDQUFDO29CQUNoQjBFLFNBQVNHLGFBQWFzQixRQUFRO29CQUM5QkMsY0FBY3pFLGFBQWFtRCxPQUFPO2dCQUNwQztnQkFFQS9CLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiwrQ0FBK0M7Z0JBQy9DLE1BQU1sQjtZQUVSLEVBQUUsT0FBTzhCLE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQywrQ0FBK0NBO2dCQUM3RCxtQ0FBbUM7Z0JBQ25DeUMsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLGtGQUFrRjtJQUNsRixNQUFNQyx1QkFBdUIsQ0FBQzFELGNBQTRCZ0M7UUFDeEQsT0FBTztnQkFDbUNoQztZQUF4QyxNQUFNaUMsZUFBZUQscUJBQW1CaEMscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI4QixPQUFPO1lBQ2xFLElBQUksQ0FBQ0csZ0JBQWdCLEVBQUNsRCx5QkFBQUEsbUNBQUFBLGFBQWNtRCxPQUFPLEtBQUksQ0FBQ2xELGFBQWE7Z0JBQzNEbUIsUUFBUWEsS0FBSyxDQUFDLGlEQUFpRDtvQkFDN0RjLFNBQVNHO29CQUNURSxNQUFNLEVBQUVwRCx5QkFBQUEsbUNBQUFBLGFBQWNtRCxPQUFPO29CQUM3QkUsV0FBV3BEO2dCQUNiO2dCQUNBO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbUIsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLHlDQUF5QztnQkFDekMsTUFBTWtDLGdCQUFnQixNQUFNQyxNQUFNLEdBQXNDTixPQUFuQ3RFLDJEQUFVQSxDQUFDNkUsUUFBUSxFQUFDLGlCQUE0QixPQUFiUCxjQUFhLGlCQUFlO29CQUNsR1EsU0FBUzt3QkFDUCxpQkFBaUIsVUFBd0MsT0FBOUJDLGFBQWFDLE9BQU8sQ0FBQztvQkFDbEQ7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDTCxjQUFjTSxFQUFFLEVBQUU7b0JBQ3JCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTUMsWUFBWSxNQUFNUixjQUFjUyxJQUFJO2dCQUMxQyxNQUFNVixRQUFRUyxVQUFVbkQsSUFBSTtnQkFFNUIsbURBQW1EO2dCQUNuRCxNQUFNVixpQkFBaUIwRSxhQUFhLENBQ2xDdEIsTUFBTVcsUUFBUSxFQUNkLCtDQUNBakUsYUFBYW1ELE9BQU8sRUFDcEJuRDtnQkFHRiw2QkFBNkI7Z0JBQzdCLE1BQU0xQiwyREFBVUEsQ0FBQztvQkFDZnlFLFNBQVNHLGFBQWFzQixRQUFRO29CQUM5QkssYUFBYTdFLGFBQWFtRCxPQUFPO2dCQUNuQztnQkFFQS9CLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiwrQ0FBK0M7Z0JBQy9DLE1BQU1sQjtZQUVSLEVBQUUsT0FBTzhCLE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQyw4Q0FBOENBO2dCQUM1RCxtQ0FBbUM7Z0JBQ25DeUMsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLGtGQUFrRjtJQUNsRixNQUFNSSx1QkFBdUIsQ0FBQzdELGNBQTRCZ0M7UUFDeEQsT0FBTztnQkFDbUNoQztZQUF4QyxNQUFNaUMsZUFBZUQscUJBQW1CaEMscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI4QixPQUFPO1lBQ2xFLElBQUksQ0FBQ0csZ0JBQWdCLEVBQUNsRCx5QkFBQUEsbUNBQUFBLGFBQWNtRCxPQUFPLEtBQUksQ0FBQ2xELGFBQWE7Z0JBQzNEbUIsUUFBUWEsS0FBSyxDQUFDLGlEQUFpRDtvQkFDN0RjLFNBQVNHO29CQUNURSxNQUFNLEVBQUVwRCx5QkFBQUEsbUNBQUFBLGFBQWNtRCxPQUFPO29CQUM3QkUsV0FBV3BEO2dCQUNiO2dCQUNBO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbUIsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLDRFQUE0RTtnQkFDNUUsNEVBQTRFO2dCQUM1RSxNQUFNLEVBQUUwRCw2QkFBNkIsRUFBRSxHQUFHLE1BQU0sOEpBQStCO2dCQUMvRSxNQUFNQyxPQUFPLE1BQU1ELDhCQUNqQixJQUNBLEtBQ0EsS0FDQSxJQUNBLElBQ0EvRSxhQUFhbUQsT0FBTyxFQUNwQm5ELGNBQ0FrRCxhQUFhLG9EQUFvRDs7Z0JBR25FLGlDQUFpQztnQkFDakMsTUFBTStCLGlCQUFpQixNQUFNekIsTUFBTSxHQUF1Q04sT0FBcEN0RSwyREFBVUEsQ0FBQzZFLFFBQVEsRUFBQyxrQkFBNkIsT0FBYlAsY0FBYSxZQUFVO29CQUMvRmdDLFFBQVE7b0JBQ1J4QixTQUFTO3dCQUNQLGdCQUFnQjt3QkFDaEIsaUJBQWlCLFVBQXdDLE9BQTlCQyxhQUFhQyxPQUFPLENBQUM7b0JBQ2xEO29CQUNBdUIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQkw7d0JBQ0FNLFlBQVksSUFBSUMsT0FBT0MsV0FBVztvQkFDcEM7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDUCxlQUFlcEIsRUFBRSxFQUFFO29CQUN0QixNQUFNLElBQUlDLE1BQU07Z0JBQ2xCO2dCQUVBMUMsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLCtDQUErQztnQkFDL0MsTUFBTWxCO2dCQUVOLHVCQUF1QjtnQkFDdkJ1RSxNQUFNO1lBRVIsRUFBRSxPQUFPekMsT0FBTztnQkFDZGIsUUFBUWEsS0FBSyxDQUFDLDhDQUE4Q0E7Z0JBQzVELG1DQUFtQztnQkFDbkN5QyxNQUFNO1lBQ1I7UUFDRjtJQUNGO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1lLGdCQUFnQixPQUFPeEU7WUFFdEJBLG9CQUFrQ0EscUJBbUNHQTtRQXBDMUMsaUVBQWlFO1FBQ2pFLElBQUksR0FBQ0EscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI2QixVQUFVLEtBQUksR0FBQzdCLHNCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSywwQ0FBQUEsb0JBQW1COEIsT0FBTyxHQUFFO1lBQ2pFM0IsUUFBUWEsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxNQUFNeUQsWUFBWSxLQUE2QixHQUFHL0IsYUFBYUMsT0FBTyxDQUFDLFlBQVksQ0FBSTtRQUN2RixNQUFNK0IsU0FBU0QsWUFBWU4sS0FBS1EsS0FBSyxDQUFDRixhQUFhO1FBQ25ELE1BQU1HLFdBQVdGLG1CQUFBQSw2QkFBQUEsT0FBUXBELEVBQUU7UUFFM0IsOENBQThDO1FBQzlDLE1BQU1ELFNBQVN6QyxpQkFBQUEsMkJBQUFBLEtBQU0wQyxFQUFFO1FBQ3ZCLE1BQU1DLGVBQWUsT0FBT0YsV0FBVyxXQUFXRyxTQUFTSCxVQUFVQTtRQUVyRSxJQUFJRixVQUFVbkIsYUFBYUwsSUFBSSxDQUFDd0IsT0FBTztRQUN2QyxJQUFJQyxXQUFXcEIsYUFBYUwsSUFBSSxDQUFDeUIsUUFBUTtRQUV6Qyw0RUFBNEU7UUFDNUUsSUFBSXBCLGFBQWFVLElBQUksS0FBSywyQkFBMkI7WUFDbkRVLFdBQVdHLGNBQWMsNkJBQTZCO1lBQ3RESixVQUFVbkIsYUFBYUwsSUFBSSxDQUFDd0IsT0FBTyxFQUFFLCtCQUErQjtRQUN0RSxPQUVLLElBQUksQ0FBQ0EsV0FBVyxDQUFDQyxVQUFVO1lBQzlCLElBQUlwQixhQUFhTCxJQUFJLENBQUN3QixPQUFPLEtBQUtJLGNBQWM7Z0JBQzlDSixVQUFVSTtnQkFDVkgsV0FBV3BCLGFBQWFMLElBQUksQ0FBQ3lCLFFBQVEsSUFBSXBCLGFBQWFMLElBQUksQ0FBQ2tGLFVBQVU7WUFDdkUsT0FBTztnQkFDTHpELFdBQVdHO2dCQUNYSixVQUFVbkIsYUFBYUwsSUFBSSxDQUFDd0IsT0FBTyxJQUFJbkIsYUFBYUwsSUFBSSxDQUFDbUYsUUFBUTtZQUNuRTtRQUNGO1FBRUEsbURBQW1EO1FBQ25ELElBQUlqRCxhQUFhN0IsYUFBYUwsSUFBSSxDQUFDa0MsVUFBVTtRQUM3QyxJQUFJLENBQUNBLGNBQWNWLFdBQVdDLGNBQVlwQixzQkFBQUEsYUFBYUwsSUFBSSxjQUFqQkssMENBQUFBLG9CQUFtQjBCLE1BQU0sR0FBRTtZQUNuRUcsYUFBYUosbUJBQW1CTixTQUFTQyxVQUFVcEIsYUFBYUwsSUFBSSxDQUFDK0IsTUFBTTtZQUMzRXZCLFFBQVFDLEdBQUcsQ0FBQywrREFBK0R5QjtRQUM3RTtRQUVBLElBQUksQ0FBQ0EsWUFBWTtZQUNmMUIsUUFBUWEsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBYixRQUFRQyxHQUFHLENBQUMsa0RBQWtEO1lBQzVEeUI7WUFDQVY7WUFDQUM7WUFDQXdEO1lBQ0FyRDtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCcEIsUUFBUUMsR0FBRyxDQUFDLHVEQUF1RDtZQUNqRTJFLGtCQUFrQi9FLGFBQWFVLElBQUk7WUFDbkNzRSxrQkFBa0JoRixhQUFhTCxJQUFJO1lBQ25Dd0IsU0FBU0EsV0FBV3lEO1lBQ3BCeEQsVUFBVUEsWUFBWXdEO1lBQ3RCQTtZQUNBSyxXQUFXLEVBQUVyRyxpQkFBQUEsMkJBQUFBLEtBQU0wQyxFQUFFO1lBQ3JCUSxTQUFTOUIsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTztZQUNsQ29ELFlBQVksQ0FBQyxDQUFDbEYsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTztZQUN2Q3FELGVBQWUsQ0FBQyxDQUFDbkYsYUFBYUwsSUFBSSxDQUFDa0MsVUFBVTtZQUM3Q3VELFdBQVcsQ0FBQyxDQUFDcEYsYUFBYUwsSUFBSSxDQUFDK0IsTUFBTTtRQUN2QztRQUVBLGdGQUFnRjtRQUNoRixJQUFJMkQsY0FBY0M7UUFDbEIsSUFBSXJELGVBQWVqQyxhQUFhTCxJQUFJLENBQUNtQyxPQUFPO1FBRTVDLDhEQUE4RDtRQUM5RCxJQUFJLENBQUNHLGdCQUFnQmpDLGFBQWFMLElBQUksQ0FBQ2tDLFVBQVUsRUFBRTtZQUNqRCxJQUFJO2dCQUNGMUIsUUFBUUMsR0FBRyxDQUFDLG9FQUFvRUosYUFBYUwsSUFBSSxDQUFDa0MsVUFBVTtnQkFFNUcsaUVBQWlFO2dCQUNqRSxNQUFNMEQsZ0JBQWdCdkYsYUFBYUwsSUFBSSxDQUFDa0MsVUFBVSxDQUFDMkQsS0FBSyxDQUFDO2dCQUN6RCxJQUFJRCxjQUFjakYsTUFBTSxLQUFLLEdBQUc7b0JBQzlCLE1BQU1vQixTQUFTNkQsYUFBYSxDQUFDLEVBQUU7b0JBQy9CcEYsUUFBUUMsR0FBRyxDQUFDLDJEQUEyRHNCO29CQUV2RSx5RUFBeUU7b0JBQ3pFLE1BQU0sRUFBRStELGFBQWEsRUFBRSxHQUFHLE1BQU0sa0tBQWlDO29CQUNqRSxNQUFNQyxpQkFBaUIsTUFBTUQsY0FBY0UsT0FBT2Y7b0JBRWxELElBQUljLGVBQWVqRyxNQUFNLEtBQUssT0FBT2lHLGVBQWUvRixJQUFJLEVBQUU7d0JBQ3hELGtDQUFrQzt3QkFDbEMsTUFBTWlHLGVBQWVGLGVBQWUvRixJQUFJLENBQUNrQixNQUFNLENBQUMsQ0FBQ3dCLFFBQy9DQSxNQUFNWCxNQUFNLEtBQUttRSxPQUFPbkUsV0FDeEI7Z0NBQUM7Z0NBQXNCO2dDQUFZO2dDQUFhOzZCQUFXLENBQUNULFFBQVEsQ0FBQ29CLE1BQU01QyxNQUFNO3dCQUduRixJQUFJbUcsYUFBYXRGLE1BQU0sR0FBRyxHQUFHOzRCQUMzQiw0QkFBNEI7NEJBQzVCLE1BQU13RixlQUFlRixhQUFhRyxJQUFJLENBQUMsQ0FBQ0MsR0FBUUMsSUFDOUMsSUFBSTNCLEtBQUsyQixFQUFFQyxTQUFTLEVBQUVDLE9BQU8sS0FBSyxJQUFJN0IsS0FBSzBCLEVBQUVFLFNBQVMsRUFBRUMsT0FBTzs0QkFFakVsRSxlQUFlNkQsWUFBWSxDQUFDLEVBQUUsQ0FBQ3hFLEVBQUU7NEJBQ2pDbkIsUUFBUUMsR0FBRyxDQUFDLDREQUE0RDZCO3dCQUMxRTtvQkFDRjtnQkFDRjtZQUNGLEVBQUUsT0FBT2pCLE9BQU87Z0JBQ2RiLFFBQVFDLEdBQUcsQ0FBQywrREFBK0RZO1lBQzdFO1FBQ0Y7UUFFQSxJQUFJaUIsY0FBYztZQUNoQixJQUFJO2dCQUNGOUIsUUFBUUMsR0FBRyxDQUFDLDZEQUE2RDZCO2dCQUN6RSxNQUFNLEVBQUVtRSxlQUFlLEVBQUUsR0FBRyxNQUFNLGtLQUFpQztnQkFDbkUsTUFBTTlELGdCQUFnQixNQUFNOEQsZ0JBQWdCUCxPQUFPNUQ7Z0JBRW5EOUIsUUFBUUMsR0FBRyxDQUFDLGlEQUFpRDtvQkFDM0RYLFFBQVE2QyxjQUFjN0MsTUFBTTtvQkFDNUI0RyxTQUFTLENBQUMsQ0FBQy9ELGNBQWMzQyxJQUFJO29CQUM3Qm1ELFdBQVdSLGNBQWMzQyxJQUFJO2dCQUMvQjtnQkFFQSxJQUFJMkMsY0FBYzdDLE1BQU0sS0FBSyxPQUFPNkMsY0FBYzNDLElBQUksRUFBRTt3QkFNaEQyQyxpQ0FFV0E7b0JBUGpCK0MsY0FBYzt3QkFDWi9ELElBQUlnQixjQUFjM0MsSUFBSSxDQUFDMkIsRUFBRTt3QkFDekI3QixRQUFRNkMsY0FBYzNDLElBQUksQ0FBQ0YsTUFBTTt3QkFDakNxQyxTQUFTUSxjQUFjM0MsSUFBSSxDQUFDMkIsRUFBRTt3QkFDOUI4QixNQUFNZCxjQUFjM0MsSUFBSSxDQUFDMEIsTUFBTTt3QkFDL0I4QixFQUFFLEdBQUViLGtDQUFBQSxjQUFjM0MsSUFBSSxDQUFDMkcsV0FBVyxjQUE5QmhFLHNEQUFBQSxnQ0FBZ0NqQixNQUFNO3dCQUMxQzZFLFdBQVc1RCxjQUFjM0MsSUFBSSxDQUFDdUcsU0FBUzt3QkFDdkNLLGVBQWVqRSxFQUFBQSw4QkFBQUEsY0FBYzNDLElBQUksQ0FBQzZHLE9BQU8sY0FBMUJsRSxrREFBQUEsNEJBQTRCN0MsTUFBTSxLQUFJO3dCQUNyRHVELFVBQVVWLGNBQWMzQyxJQUFJLENBQUNxRCxRQUFRO3dCQUNyQ3RCLFFBQVFZLGNBQWMzQyxJQUFJLENBQUMrQixNQUFNO29CQUNuQztvQkFDQXZCLFFBQVFDLEdBQUcsQ0FBQyw0REFBNEQ7d0JBQ3RFMEIsU0FBU3VELFlBQVkvRCxFQUFFO3dCQUN2QjdCLFFBQVE0RixZQUFZNUYsTUFBTTt3QkFDMUJnSCxPQUFPcEIsWUFBWWpDLElBQUk7d0JBQ3ZCc0QsUUFBUXJCLFlBQVlsQyxFQUFFO3dCQUN0QkgsVUFBVXFDLFlBQVlyQyxRQUFRO3dCQUM5QnRCLFFBQVEyRCxZQUFZM0QsTUFBTTtvQkFDNUI7Z0JBQ0YsT0FBTztvQkFDTHZCLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixnQ0FBZ0M7b0JBQ2hDaUYsY0FBYzt3QkFDWi9ELElBQUlXO3dCQUNKeEMsUUFBUU8sYUFBYUwsSUFBSSxDQUFDRixNQUFNLElBQUk7d0JBQ3BDcUMsU0FBU0c7d0JBQ1RtQixNQUFNakM7d0JBQ05nQyxJQUFJL0I7d0JBQ0pNLFFBQVExQixhQUFhTCxJQUFJLENBQUMrQixNQUFNO3dCQUNoQ3NCLFVBQVVoRCxhQUFhTCxJQUFJLENBQUNxRCxRQUFRO29CQUN0QztnQkFDRjtZQUNGLEVBQUUsT0FBT2hDLE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQyx1REFBdURBO2dCQUNyRSxnQ0FBZ0M7Z0JBQ2hDcUUsY0FBYztvQkFDWi9ELElBQUlXO29CQUNKeEMsUUFBUU8sYUFBYUwsSUFBSSxDQUFDRixNQUFNLElBQUk7b0JBQ3BDcUMsU0FBU0c7b0JBQ1RtQixNQUFNakM7b0JBQ05nQyxJQUFJL0I7b0JBQ0pNLFFBQVExQixhQUFhTCxJQUFJLENBQUMrQixNQUFNO29CQUNoQ3NCLFVBQVVoRCxhQUFhTCxJQUFJLENBQUNxRCxRQUFRO2dCQUN0QztnQkFDQTdDLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0RpRjtZQUNsRTtRQUNGLE9BQU87WUFDTGxGLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBRUEseUVBQXlFO1FBQ3pFLE1BQU11RyxVQUFVLGNBQXlCLE9BQVg5RTtRQUU5Qix3RUFBd0U7UUFDeEUsTUFBTStFLGdCQUFnQkMsU0FBU0MsY0FBYyxDQUFDSDtRQUM5QyxJQUFJQyxlQUFlO1lBQ2pCekcsUUFBUUMsR0FBRyxDQUFDO1lBQ1p3RyxjQUFjRyxjQUFjLENBQUM7Z0JBQUVDLFVBQVU7WUFBUztZQUNsRDtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDbkksVUFBVTtZQUNSeUMsSUFBSXFGO1lBQ0pNLHlCQUFXdEssMERBQW1CLENBQUNjLHlFQUFTQSxFQUFFO2dCQUN4Q29FO2dCQUNBVixTQUFTQSxXQUFXeUQ7Z0JBQ3BCeEQsVUFBVUEsWUFBWXdEO2dCQUN0QnVDLFNBQVMsSUFBTXJJLFdBQVc2SDtnQkFDMUJTLFdBQVdyRixzQkFBc0IvQixjQUFjaUM7Z0JBQy9Db0YsVUFBVTNELHFCQUFxQjFELGNBQWNpQztnQkFDN0NxRixVQUFVLEtBQU87Z0JBQ2pCQyxVQUFVMUQscUJBQXFCN0QsY0FBY2lDO2dCQUM3Q29EO1lBQ0Y7WUFDQW1DLHNCQUFzQjtZQUN0QkMsZUFBZTtZQUNmQyxjQUFjO1lBQ2RDLFFBQVE7WUFDUkMsbUJBQW1CO1lBQ25CQyxnQkFBZ0I7WUFDaEJDLGVBQWU7UUFDakI7UUFFQSxrQ0FBa0M7UUFDbEN0SixVQUFVO0lBQ1o7SUFFQSxvREFBb0Q7SUFDcEQzQixnREFBU0E7c0NBQUM7WUFDUnFDO1FBQ0Y7cUNBQUc7UUFBQ047S0FBSztJQUVULHlDQUF5QztJQUN6Qy9CLGdEQUFTQTtzQ0FBQztZQUNSLE1BQU1rTCxXQUFXQyxZQUFZOUksbUJBQW1CO1lBQ2hEOzhDQUFPLElBQU0rSSxjQUFjRjs7UUFDN0I7cUNBQUc7UUFBQ25KO0tBQUs7SUFFVCx1Q0FBdUM7SUFDdkMvQixnREFBU0E7c0NBQUM7WUFDUixNQUFNcUw7aUVBQXFCLENBQUNDO29CQUMxQixJQUFJeEosWUFBWXlKLE9BQU8sSUFBSSxDQUFDekosWUFBWXlKLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7d0JBQzlFOUosVUFBVTtvQkFDWjtnQkFDRjs7WUFFQXFJLFNBQVMwQixnQkFBZ0IsQ0FBQyxhQUFhTDtZQUN2Qzs4Q0FBTyxJQUFNckIsU0FBUzJCLG1CQUFtQixDQUFDLGFBQWFOOztRQUN6RDtxQ0FBRyxFQUFFO0lBRUwsNEJBQTRCO0lBQzVCLE1BQU1PLDBCQUEwQixPQUFPekk7WUFJdkJBO1FBSGRHLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0M7WUFDekRNLE1BQU1WLGFBQWFVLElBQUk7WUFDdkJnSSxnQkFBZ0IvRyxvQkFBb0IzQjtZQUNwQzZCLFVBQVUsR0FBRTdCLHFCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSyx5Q0FBQUEsbUJBQW1CNkIsVUFBVTtZQUN6QzhHLFdBQVczSSxhQUFhMkksU0FBUztRQUNuQztRQUVBLElBQUksQ0FBQzNJLGFBQWFjLE1BQU0sRUFBRTtZQUN4QixJQUFJO2dCQUNGLE1BQU01RCx1RUFBc0JBLENBQUM4QyxhQUFhc0IsRUFBRTtnQkFFNUMsMkNBQTJDO2dCQUMzQyxJQUFJckIsb0JBQW9CRCxlQUFlO29CQUNyQ2hDLHNCQUFzQjRLLENBQUFBLE9BQ3BCQSxLQUFLcEksR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFYSxFQUFFLEtBQUt0QixhQUFhc0IsRUFBRSxHQUFHO2dDQUFFLEdBQUdiLENBQUM7Z0NBQUVLLFFBQVE7NEJBQUssSUFBSUw7b0JBRXBFckMsb0JBQW9Cd0ssQ0FBQUEsT0FBUUMsS0FBS0MsR0FBRyxDQUFDLEdBQUdGLE9BQU87Z0JBQ2pELE9BQU87b0JBQ0w5Syx1QkFBdUI4SyxDQUFBQSxPQUNyQkEsS0FBS3BJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWEsRUFBRSxLQUFLdEIsYUFBYXNCLEVBQUUsR0FBRztnQ0FBRSxHQUFHYixDQUFDO2dDQUFFSyxRQUFROzRCQUFLLElBQUlMO29CQUVwRXZDLHFCQUFxQjBLLENBQUFBLE9BQVFDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRixPQUFPO2dCQUNsRDtZQUNGLEVBQUUsT0FBTzVILE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3hEO1FBQ0Y7UUFFQSxtR0FBbUc7UUFDbkcsSUFBSVcsb0JBQW9CM0IsZUFBZTtZQUNyQ0csUUFBUUMsR0FBRyxDQUFDO1lBQ1pvRSxjQUFjeEU7UUFDaEIsT0FBTyxJQUFJQSxhQUFhMkksU0FBUyxFQUFFO1lBQ2pDLG1GQUFtRjtZQUNuRnhJLFFBQVFDLEdBQUcsQ0FBQyxrREFBa0RKLGFBQWEySSxTQUFTO1lBQ3BGSSxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBR2pKLGFBQWEySSxTQUFTO1FBQy9DLE9BQU87WUFDTCxpRkFBaUY7WUFDakZ4SSxRQUFRQyxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU04SSxzQkFBc0I7UUFDMUIsSUFBSTtZQUNGLE1BQU0vTCwyRUFBMEJBO1lBRWhDLGlDQUFpQztZQUNqQ1csdUJBQXVCOEssQ0FBQUEsT0FBUUEsS0FBS3BJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTt3QkFBRSxHQUFHQSxDQUFDO3dCQUFFSyxRQUFRO29CQUFLO1lBQ25FOUMsc0JBQXNCNEssQ0FBQUEsT0FBUUEsS0FBS3BJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTt3QkFBRSxHQUFHQSxDQUFDO3dCQUFFSyxRQUFRO29CQUFLO1lBRWxFLHNCQUFzQjtZQUN0QjVDLHFCQUFxQjtZQUNyQkUsb0JBQW9CO1FBQ3RCLEVBQUUsT0FBTzRDLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLDZDQUE2Q0E7UUFDN0Q7SUFDRjtJQUVBLG9DQUFvQztJQUNwQyxNQUFNbUksc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUk7WUFDRix1REFBdUQ7WUFDdkQsdURBQXVEO1lBQ3ZELE1BQU14SixnQkFBZ0J3SixZQUFZLFdBQVd2TCxzQkFBc0JFO1lBQ25FLE1BQU1zTCxzQkFBc0J6SixjQUFjaUIsTUFBTSxDQUFDSixDQUFBQSxJQUFLLENBQUNBLEVBQUVLLE1BQU07WUFFL0QsS0FBSyxNQUFNZCxnQkFBZ0JxSixvQkFBcUI7Z0JBQzlDLE1BQU1uTSx1RUFBc0JBLENBQUM4QyxhQUFhc0IsRUFBRTtZQUM5QztZQUVBLDhCQUE4QjtZQUM5QixJQUFJOEgsWUFBWSxVQUFVO2dCQUN4QnRMLHVCQUF1QjhLLENBQUFBLE9BQVFBLEtBQUtwSSxHQUFHLENBQUNDLENBQUFBLElBQU07NEJBQUUsR0FBR0EsQ0FBQzs0QkFBRUssUUFBUTt3QkFBSztnQkFDbkU1QyxxQkFBcUI7WUFDdkIsT0FBTztnQkFDTEYsc0JBQXNCNEssQ0FBQUEsT0FBUUEsS0FBS3BJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTs0QkFBRSxHQUFHQSxDQUFDOzRCQUFFSyxRQUFRO3dCQUFLO2dCQUNsRTFDLG9CQUFvQjtZQUN0QjtRQUNGLEVBQUUsT0FBTzRDLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLGtCQUEwQixPQUFSb0ksU0FBUSw0QkFBMEJwSTtRQUNwRTtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1zSSxzQkFBc0IsQ0FBQzVJO1FBQzNCLE9BQVFBO1lBQ04sS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDNkk7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUEwQkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDakYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXlCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNoRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBdUJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQzlFLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztZQUNMLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBMEJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUEwQkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDakYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQTJCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNsRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBeUJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2hGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF3QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDL0UsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBMEJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF3QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDL0UsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXlCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNoRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQTBCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNqRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBeUJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2hGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztZQUNMLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBd0JFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQy9FLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF1QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDOUUsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RTtnQkFDRSxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF3QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDL0UsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztRQUkvRTtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1DLGdCQUFnQixDQUFDQztRQUNyQixNQUFNQyxPQUFPLElBQUk5RixLQUFLNkY7UUFDdEIsTUFBTUUsTUFBTSxJQUFJL0Y7UUFDaEIsTUFBTWdHLGdCQUFnQnpCLEtBQUswQixLQUFLLENBQUMsQ0FBQ0YsSUFBSWxFLE9BQU8sS0FBS2lFLEtBQUtqRSxPQUFPLEVBQUMsSUFBSztRQUVwRSxJQUFJbUUsZ0JBQWdCLElBQUksT0FBTztRQUMvQixJQUFJQSxnQkFBZ0IsTUFBTSxPQUFPLEdBQWtDLE9BQS9CekIsS0FBSzBCLEtBQUssQ0FBQ0QsZ0JBQWdCLEtBQUk7UUFDbkUsSUFBSUEsZ0JBQWdCLE9BQU8sT0FBTyxHQUFvQyxPQUFqQ3pCLEtBQUswQixLQUFLLENBQUNELGdCQUFnQixPQUFNO1FBQ3RFLE9BQU8sR0FBcUMsT0FBbEN6QixLQUFLMEIsS0FBSyxDQUFDRCxnQkFBZ0IsUUFBTztJQUM5QztJQUVBLDRDQUE0QztJQUM1QyxJQUFJLENBQUMxTCxNQUFNLE9BQU87SUFFbEIsTUFBTTRMLG1CQUFtQnZNLG9CQUFvQkU7SUFDN0MsTUFBTXNNLHVCQUF1QnBNLGNBQWMsV0FBV1Isc0JBQXNCRTtJQUM1RSxNQUFNMk0scUJBQXFCck0sY0FBYyxXQUFXSixvQkFBb0JFO0lBRXhFLHFCQUNFLDhEQUFDb0w7UUFBSUMsV0FBVTtRQUFXbUIsS0FBS2hNOzswQkFFN0IsOERBQUNqQixxRUFBNEJBO2dCQUFDa04sbUJBQW1CMUw7Ozs7OzswQkFHakQsOERBQUMyTDtnQkFDQ0MsU0FBUyxJQUFNdE0sVUFBVSxDQUFDRDtnQkFDMUJpTCxXQUFVOztrQ0FFViw4REFBQ0M7d0JBQUlELFdBQVU7d0JBQVVFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pFLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29CQUl0RU8sbUJBQW1CLG1CQUNsQiw4REFBQ087d0JBQUt2QixXQUFVO2tDQUNiZ0IsbUJBQW1CLEtBQUssUUFBUUE7Ozs7Ozs7Ozs7OztZQU10Q2pNLHdCQUNDLDhEQUFDZ0w7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3dCO3dDQUFHeEIsV0FBVTtrREFBc0M7Ozs7OztvQ0FDbkRnQixtQkFBbUIsbUJBQ2xCLDhEQUFDSzt3Q0FDQ0MsU0FBUzVCO3dDQUNUTSxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7MENBT0wsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3FCO3dDQUNDQyxTQUFTLElBQU14TSxhQUFhO3dDQUM1QmtMLFdBQVcscUVBSVYsT0FIQ25MLGNBQWMsV0FDVixxQ0FDQTs7NENBRVA7NENBRUVKLG9CQUFvQixtQkFDbkIsOERBQUM4TTtnREFBS3ZCLFdBQVU7MERBQ2J2TDs7Ozs7Ozs7Ozs7O2tEQUlQLDhEQUFDNE07d0NBQ0NDLFNBQVMsSUFBTXhNLGFBQWE7d0NBQzVCa0wsV0FBVyxxRUFJVixPQUhDbkwsY0FBYyxVQUNWLHFDQUNBOzs0Q0FFUDs0Q0FFRUYsbUJBQW1CLG1CQUNsQiw4REFBQzRNO2dEQUFLdkIsV0FBVTswREFDYnJMOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT1J1TSxxQkFBcUIsbUJBQ3BCLDhEQUFDbkI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNxQjtvQ0FDQ0MsU0FBUyxJQUFNM0Isb0JBQW9COUs7b0NBQ25DbUwsV0FBVTs7d0NBQ1g7d0NBQ09uTDt3Q0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU94Qiw4REFBQ2tMO3dCQUFJQyxXQUFVO2tDQUNaL0ssd0JBQ0MsOERBQUM4Szs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUN5QjtvQ0FBRXpCLFdBQVU7OENBQU87Ozs7Ozs7Ozs7O21DQUVwQmlCLHFCQUFxQm5LLE1BQU0sS0FBSyxrQkFDbEMsOERBQUNpSjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVO29DQUF1Q0UsTUFBSztvQ0FBT0MsUUFBTztvQ0FBZUMsU0FBUTs4Q0FDOUYsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7OENBRXZFLDhEQUFDZ0I7O3dDQUFFO3dDQUFJNU07d0NBQVU7Ozs7Ozs7OENBQ2pCLDhEQUFDNE07b0NBQUV6QixXQUFVOzhDQUNWbkwsY0FBYyxXQUNYLHNEQUNBOzs7Ozs7Ozs7OzttQ0FLUm9NLHFCQUFxQmpLLEdBQUcsQ0FBQyxDQUFDUjtnQ0F3Q1lBO2lEQXZDcEMsOERBQUN1SjtnQ0FFQ3VCLFNBQVMsSUFBTXJDLHdCQUF3QnpJO2dDQUN2Q3dKLFdBQVcsa0ZBRVYsT0FEQyxDQUFDeEosYUFBYWMsTUFBTSxHQUFHLGVBQWU7MENBR3hDLDRFQUFDeUk7b0NBQUlDLFdBQVU7O3dDQUNaRixvQkFBb0J0SixhQUFhVSxJQUFJO3NEQUN0Qyw4REFBQzZJOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDeUI7NERBQUV6QixXQUFXLHVCQUViLE9BREMsQ0FBQ3hKLGFBQWFjLE1BQU0sR0FBRyxrQkFBa0I7c0VBRXhDZCxhQUFha0wsS0FBSzs7Ozs7O3dEQUVwQixDQUFDbEwsYUFBYWMsTUFBTSxrQkFDbkIsOERBQUN5STs0REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUduQiw4REFBQ3lCO29EQUFFekIsV0FBVTs4REFDVnhKLGFBQWFtTCxPQUFPOzs7Ozs7OERBRXZCLDhEQUFDNUI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDeUI7NERBQUV6QixXQUFVO3NFQUNWVSxjQUFjbEssYUFBYWtHLFNBQVM7Ozs7OztzRUFFdkMsOERBQUNxRDs0REFBSUMsV0FBVTs7Z0VBRVpuTCxjQUFjLFdBQVc2QyxtQkFBbUJsQiwrQkFDM0MsOERBQUMrSztvRUFBS3ZCLFdBQVcsK0JBSWhCLE9BSEN0SSxtQkFBbUJsQixrQkFBa0IsVUFDakMsZ0NBQ0E7OEVBRUhrQixtQkFBbUJsQjs7Ozs7O2dFQUl2QjNCLGNBQWMsYUFBVzJCLHFCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSyx5Q0FBQUEsbUJBQW1COEIsT0FBTyxtQkFDbEQsOERBQUNpSjtvRUFBS3ZCLFdBQVU7O3dFQUF3RDt3RUFDOUR4SixhQUFhTCxJQUFJLENBQUNtQyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQXhDeEM5QixhQUFhc0IsRUFBRTs7Ozs7Ozs7Ozs7b0JBcUQxQnpELENBQUFBLG9CQUFvQnlDLE1BQU0sR0FBRyxLQUFLdkMsbUJBQW1CdUMsTUFBTSxHQUFHLG9CQUM5RCw4REFBQ2lKO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDcUI7NEJBQ0NDLFNBQVM7Z0NBQ1B0TSxVQUFVOzRCQUNWLHNEQUFzRDs0QkFDdEQsMkNBQTJDOzRCQUM3Qzs0QkFDQWdMLFdBQVU7O2dDQUNYO2dDQUNXbkw7Z0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFwQztHQXo5QndCVDs7UUFTTGIsbURBQVFBO1FBQ1NPLHdFQUFjQTtRQUNWQyx1REFBU0E7UUFDdEJDLDJFQUFtQkE7OztLQVp0QkkiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxzcmNcXGNvbXBvbmVudHNcXHNoYXJlZFxcbm90aWZpY2F0aW9uc1xcTm90aWZpY2F0aW9uQmVsbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQcml2eSB9IGZyb20gXCJAcHJpdnktaW8vcmVhY3QtYXV0aFwiO1xuaW1wb3J0IHsgZ2V0Tm90aWZpY2F0aW9ucywgZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQsIG1hcmtOb3RpZmljYXRpb25Bc1JlYWQsIG1hcmtBbGxOb3RpZmljYXRpb25zQXNSZWFkLCByZWxlYXNlUGVyaywgcmVmdW5kUGVyayB9IGZyb20gJ0AvYXhpb3MvcmVxdWVzdHMnO1xuaW1wb3J0IHsgdXNlR2xvYmFsTW9kYWwgfSBmcm9tICdAL2NvbnRleHRzL0dsb2JhbE1vZGFsQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VXYWxsZXQgfSBmcm9tICdAL2hvb2tzL3VzZVdhbGxldCc7XG5pbXBvcnQgeyB1c2VFc2Nyb3dPcGVyYXRpb25zIH0gZnJvbSAnQC9ob29rcy91c2VFc2Nyb3dPcGVyYXRpb25zJztcbmltcG9ydCBDaGF0TW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL3NoYXJlZC9jaGF0L0NoYXRNb2RhbCc7XG5pbXBvcnQgUmVhbFRpbWVOb3RpZmljYXRpb25MaXN0ZW5lciBmcm9tICcuL1JlYWxUaW1lTm90aWZpY2F0aW9uTGlzdGVuZXInO1xuaW1wb3J0IHsgQVBJX0NPTkZJRyB9IGZyb20gJ0AvY29uZmlnL2Vudmlyb25tZW50JztcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBudW1iZXI7XG4gIHR5cGU6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBpc1JlYWQ6IGJvb2xlYW47XG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ3VyZ2VudCc7XG4gIGFjdGlvblVybD86IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGRhdGE/OiB7XG4gICAgdHJhZGVJZD86IG51bWJlcjtcbiAgICBkaXNwdXRlSWQ/OiBudW1iZXI7XG4gICAgYnV5ZXJJZD86IG51bWJlcjtcbiAgICBzZWxsZXJJZD86IG51bWJlcjtcbiAgICBba2V5OiBzdHJpbmddOiBhbnk7XG4gIH07XG59XG5cbnR5cGUgVGFiVHlwZSA9ICdzeXN0ZW0nIHwgJ3RyYWRlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90aWZpY2F0aW9uQmVsbCgpIHtcbiAgY29uc3QgW3N5c3RlbU5vdGlmaWNhdGlvbnMsIHNldFN5c3RlbU5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8Tm90aWZpY2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW3RyYWRlTm90aWZpY2F0aW9ucywgc2V0VHJhZGVOb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXSk7XG4gIGNvbnN0IFtzeXN0ZW1VbnJlYWRDb3VudCwgc2V0U3lzdGVtVW5yZWFkQ291bnRdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFt0cmFkZVVucmVhZENvdW50LCBzZXRUcmFkZVVucmVhZENvdW50XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8VGFiVHlwZT4oJ3N5c3RlbScpO1xuICBjb25zdCBbaXNPcGVuLCBzZXRJc09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGRyb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VQcml2eSgpO1xuICBjb25zdCB7IG9wZW5Nb2RhbCwgY2xvc2VNb2RhbCB9ID0gdXNlR2xvYmFsTW9kYWwoKTtcbiAgY29uc3QgeyBzb2xhbmFXYWxsZXQsIGlzQ29ubmVjdGVkIH0gPSB1c2VXYWxsZXQoKTtcbiAgY29uc3QgZXNjcm93T3BlcmF0aW9ucyA9IHVzZUVzY3Jvd09wZXJhdGlvbnMoKTtcblxuICAvLyBMb2FkIG5vdGlmaWNhdGlvbnMgYW5kIHNlcGFyYXRlIHRoZW0gYnkgdHlwZVxuICBjb25zdCBsb2FkTm90aWZpY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgW25vdGlmaWNhdGlvbnNSZXNwb25zZSwgdW5yZWFkUmVzcG9uc2VdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBnZXROb3RpZmljYXRpb25zKHsgcGFnZTogMSwgcGFnZVNpemU6IDIwIH0pLFxuICAgICAgICBnZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCgpXG4gICAgICBdKTtcblxuICAgICAgaWYgKG5vdGlmaWNhdGlvbnNSZXNwb25zZS5zdGF0dXMgPT09IDIwMCkge1xuICAgICAgICBjb25zdCBhbGxOb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9uc1Jlc3BvbnNlLmRhdGEubm90aWZpY2F0aW9ucztcblxuICAgICAgICAvLyBTZXBhcmF0ZSBub3RpZmljYXRpb25zIGludG8gc3lzdGVtIGFuZCB0cmFkZSBjYXRlZ29yaWVzXG4gICAgICAgIGNvbnN0IHN5c3RlbU5vdHM6IE5vdGlmaWNhdGlvbltdID0gW107XG4gICAgICAgIGNvbnN0IHRyYWRlTm90czogTm90aWZpY2F0aW9uW10gPSBbXTtcblxuICAgICAgICBhbGxOb3RpZmljYXRpb25zLmZvckVhY2goKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgICAgICAgaWYgKGlzVHJhZGVOb3RpZmljYXRpb24obm90aWZpY2F0aW9uKSkge1xuICAgICAgICAgICAgdHJhZGVOb3RzLnB1c2gobm90aWZpY2F0aW9uKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgc3lzdGVtTm90cy5wdXNoKG5vdGlmaWNhdGlvbik7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBbTm90aWZpY2F0aW9uQmVsbF0gTm90aWZpY2F0aW9uIGNhdGVnb3JpemF0aW9uOicsIHtcbiAgICAgICAgICB0b3RhbE5vdGlmaWNhdGlvbnM6IGFsbE5vdGlmaWNhdGlvbnMubGVuZ3RoLFxuICAgICAgICAgIHN5c3RlbU5vdGlmaWNhdGlvbnM6IHN5c3RlbU5vdHMubGVuZ3RoLFxuICAgICAgICAgIHRyYWRlTm90aWZpY2F0aW9uczogdHJhZGVOb3RzLmxlbmd0aCxcbiAgICAgICAgICBzeXN0ZW1UeXBlczogc3lzdGVtTm90cy5tYXAobiA9PiBuLnR5cGUpLFxuICAgICAgICAgIHRyYWRlVHlwZXM6IHRyYWRlTm90cy5tYXAobiA9PiBuLnR5cGUpXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHNldFN5c3RlbU5vdGlmaWNhdGlvbnMoc3lzdGVtTm90cyk7XG4gICAgICAgIHNldFRyYWRlTm90aWZpY2F0aW9ucyh0cmFkZU5vdHMpO1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSB1bnJlYWQgY291bnRzIGZvciBlYWNoIGNhdGVnb3J5XG4gICAgICAgIGNvbnN0IHN5c3RlbVVucmVhZCA9IHN5c3RlbU5vdHMuZmlsdGVyKG4gPT4gIW4uaXNSZWFkKS5sZW5ndGg7XG4gICAgICAgIGNvbnN0IHRyYWRlVW5yZWFkID0gdHJhZGVOb3RzLmZpbHRlcihuID0+ICFuLmlzUmVhZCkubGVuZ3RoO1xuXG4gICAgICAgIHNldFN5c3RlbVVucmVhZENvdW50KHN5c3RlbVVucmVhZCk7XG4gICAgICAgIHNldFRyYWRlVW5yZWFkQ291bnQodHJhZGVVbnJlYWQpO1xuICAgICAgfVxuXG4gICAgICAvLyBOb3RlOiBXZSBjYWxjdWxhdGUgdW5yZWFkIGNvdW50cyBmcm9tIHRoZSBmaWx0ZXJlZCBub3RpZmljYXRpb25zXG4gICAgICAvLyByYXRoZXIgdGhhbiB1c2luZyB0aGUgQVBJIHJlc3BvbnNlIHNpbmNlIHdlIG5lZWQgc2VwYXJhdGUgY291bnRzXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSBpZiBhIG5vdGlmaWNhdGlvbiBpcyB0cmFkZS1yZWxhdGVkXG4gIGNvbnN0IGlzVHJhZGVOb3RpZmljYXRpb24gPSAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24pOiBib29sZWFuID0+IHtcbiAgICAvLyBTeXN0ZW0gbm90aWZpY2F0aW9ucyAobm9uLXRyYWRlKSAtIHRoZXNlIGdvIHRvIFN5c3RlbSB0YWJcbiAgICBjb25zdCBzeXN0ZW1UeXBlcyA9IFtcbiAgICAgICdzeXN0ZW1fYW5ub3VuY2VtZW50JyxcbiAgICAgICdtb2RlcmF0b3JfYWRkZWQnLFxuICAgICAgJ3BlcmtfY3JlYXRlZCcsXG4gICAgICAndG9rZW5fY3JlYXRlZCcsXG4gICAgICAvLyBNb2RlcmF0b3Itc3BlY2lmaWMgbm90aWZpY2F0aW9ucyAoZ28gdG8gbW9kZXJhdG9yIGRhc2hib2FyZClcbiAgICAgICdkaXNwdXRlX2NyZWF0ZWQnLFxuICAgICAgJ2Rpc3B1dGVfYXNzaWduZWQnXG4gICAgXTtcblxuICAgIC8vIElmIGl0J3MgYSBzeXN0ZW0gdHlwZSwgaXQncyBOT1QgYSB0cmFkZSBub3RpZmljYXRpb25cbiAgICBpZiAoc3lzdGVtVHlwZXMuaW5jbHVkZXMobm90aWZpY2F0aW9uLnR5cGUpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLy8gQWxsIGVzY3JvdywgdHJhZGUsIGFuZCB1c2VyIGRpc3B1dGUgbm90aWZpY2F0aW9ucyBnbyB0byBUcmFkZSB0YWJcbiAgICBjb25zdCB0cmFkZVR5cGVzID0gW1xuICAgICAgLy8gVHJhZGUgYW5kIGVzY3JvdyBub3RpZmljYXRpb25zXG4gICAgICAncGVya19wdXJjaGFzZWQnLFxuICAgICAgJ3Blcmtfc29sZCcsXG4gICAgICAnZXNjcm93X2NyZWF0ZWQnLFxuICAgICAgJ2VzY3Jvd19yZWxlYXNlZCcsXG4gICAgICAnZXNjcm93X3JlbGVhc2VfcmVtaW5kZXInLFxuICAgICAgJ3RyYWRlX2NvbXBsZXRlZCcsXG4gICAgICAndHJhZGVfcmVmdW5kZWQnLFxuICAgICAgJ3RyYWRlX3JlcG9ydGVkJyxcbiAgICAgICd0cmFkZV9kaXNwdXRlZCcsXG4gICAgICAvLyBVc2VyIGRpc3B1dGUgbm90aWZpY2F0aW9ucyAodHJhZGUtcmVsYXRlZClcbiAgICAgICdkaXNwdXRlX3Jlc29sdmVkJyxcbiAgICAgIC8vIENoYXQgbm90aWZpY2F0aW9ucyAodHJhZGUtcmVsYXRlZClcbiAgICAgICdjaGF0X21lc3NhZ2UnXG4gICAgXTtcblxuICAgIC8vIFJldHVybiB0cnVlIGlmIGl0J3MgYSB0cmFkZSB0eXBlXG4gICAgcmV0dXJuIHRyYWRlVHlwZXMuaW5jbHVkZXMobm90aWZpY2F0aW9uLnR5cGUpO1xuICB9O1xuXG4gIC8vIEdldCB1c2VyIHJvbGUgaW4gdHJhZGUgZm9yIGRpc3BsYXlcbiAgY29uc3QgZ2V0VXNlclJvbGVJblRyYWRlID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIW5vdGlmaWNhdGlvbi5kYXRhPy5idXllcklkIHx8ICFub3RpZmljYXRpb24uZGF0YT8uc2VsbGVySWQpIHJldHVybiAnJztcblxuICAgIGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkO1xuICAgIGNvbnN0IHVzZXJJZE51bWJlciA9IHR5cGVvZiB1c2VySWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQodXNlcklkKSA6IHVzZXJJZDtcblxuICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5idXllcklkID09PSB1c2VySWROdW1iZXIpIHJldHVybiAnYnV5ZXInO1xuICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZCA9PT0gdXNlcklkTnVtYmVyKSByZXR1cm4gJ3NlbGxlcic7XG4gICAgcmV0dXJuICcnO1xuICB9O1xuXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZW5lcmF0ZSBjb25zaXN0ZW50IGNoYXQgcm9vbSBJRCBmb3IgYSB0cmFkZVxuICBjb25zdCBnZW5lcmF0ZUNoYXRSb29tSWQgPSAoYnV5ZXJJZDogbnVtYmVyLCBzZWxsZXJJZDogbnVtYmVyLCBwZXJrSWQ6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gICAgcmV0dXJuIGAke2J1eWVySWR9LSR7c2VsbGVySWR9LSR7cGVya0lkfWA7XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSBpZiBub3RpZmljYXRpb24gc2hvdWxkIG9wZW4gY2hhdCBtb2RhbFxuICBjb25zdCBzaG91bGRPcGVuQ2hhdE1vZGFsID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgY2hhdFR5cGVzID0gW1xuICAgICAgJ3Blcmtfc29sZCcsICdwZXJrX3B1cmNoYXNlZCcsICdlc2Nyb3dfcmVsZWFzZWQnLCAndHJhZGVfY29tcGxldGVkJyxcbiAgICAgICd0cmFkZV9kaXNwdXRlZCcsICdkaXNwdXRlX3Jlc29sdmVkJywgJ3RyYWRlX3JlZnVuZGVkJywgJ2NoYXRfbWVzc2FnZScsXG4gICAgICAnZXNjcm93X2NyZWF0ZWQnLCAnZXNjcm93X3BlbmRpbmdfYWNjZXB0YW5jZScsICdlc2Nyb3dfYWNjZXB0ZWQnLFxuICAgICAgJ2VzY3Jvd19yZWxlYXNlX3JlbWluZGVyJywgJ3RyYWRlX3JlcG9ydGVkJ1xuICAgIF07XG4gICAgcmV0dXJuIGNoYXRUeXBlcy5pbmNsdWRlcyhub3RpZmljYXRpb24udHlwZSkgJiYgKCEhbm90aWZpY2F0aW9uLmRhdGE/LmNoYXRSb29tSWQgfHwgISFub3RpZmljYXRpb24uZGF0YT8udHJhZGVJZCk7XG4gIH07XG5cbiAgLy8gQ3JlYXRlIHJlbGVhc2UgZnVuY3Rpb24gZm9yIGVzY3JvdyAtIG5vdyB3b3JrcyBnbG9iYWxseSB3aXRob3V0IHJvdXRlIGRlcGVuZGVuY3lcbiAgY29uc3QgY3JlYXRlUmVsZWFzZUZ1bmN0aW9uID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uLCByZXNvbHZlZFRyYWRlSWQ/OiBudW1iZXIgfCBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgdHJhZGVJZFRvVXNlID0gcmVzb2x2ZWRUcmFkZUlkIHx8IG5vdGlmaWNhdGlvbi5kYXRhPy50cmFkZUlkO1xuICAgICAgaWYgKCF0cmFkZUlkVG9Vc2UgfHwgIXNvbGFuYVdhbGxldD8uYWRkcmVzcyB8fCAhaXNDb25uZWN0ZWQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBNaXNzaW5nIGRhdGEgZm9yIHJlbGVhc2U6Jywge1xuICAgICAgICAgIHRyYWRlSWQ6IHRyYWRlSWRUb1VzZSxcbiAgICAgICAgICB3YWxsZXQ6IHNvbGFuYVdhbGxldD8uYWRkcmVzcyxcbiAgICAgICAgICBjb25uZWN0ZWQ6IGlzQ29ubmVjdGVkXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBTdGFydGluZyBnbG9iYWwgZXNjcm93IHJlbGVhc2UgZnJvbSBub3RpZmljYXRpb24nKTtcblxuICAgICAgICAvLyBHZXQgdHJhZGUgZGV0YWlscyBmb3IgZXNjcm93IG9wZXJhdGlvblxuICAgICAgICBjb25zdCB0cmFkZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0NPTkZJRy5CQVNFX1VSTH0vcGVya3MvdHJhZGUvJHt0cmFkZUlkVG9Vc2V9L2VzY3Jvdy1kYXRhYCwge1xuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpfWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghdHJhZGVSZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHRyYWRlIGRldGFpbHMnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHRyYWRlRGF0YSA9IGF3YWl0IHRyYWRlUmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCB0cmFkZSA9IHRyYWRlRGF0YS5kYXRhO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBUcmFkZSBkYXRhIGZvciByZWxlYXNlOicsIHtcbiAgICAgICAgICBlc2Nyb3dJZDogdHJhZGUuZXNjcm93SWQsXG4gICAgICAgICAgdG9rZW5EZXRhaWxzOiB0cmFkZS50b2tlbkRldGFpbHMsXG4gICAgICAgICAgcGVya1Rva2VuTWludDogdHJhZGUucGVya1Rva2VuTWludCxcbiAgICAgICAgICB0bzogdHJhZGUudG8sXG4gICAgICAgICAgZnJvbTogdHJhZGUuZnJvbVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBFeGVjdXRlIGVzY3JvdyBzZWxsIHRyYW5zYWN0aW9uIHVzaW5nIHRoZSBob29rXG4gICAgICAgIGF3YWl0IGVzY3Jvd09wZXJhdGlvbnMuZXhlY3V0ZVNlbGwoXG4gICAgICAgICAgdHJhZGUuZXNjcm93SWQsXG4gICAgICAgICAgJ1NvMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTInLCAvLyBTT0wgbWludFxuICAgICAgICAgIHRyYWRlLnRva2VuRGV0YWlscz8udG9rZW5BZGRyZXNzIHx8IHRyYWRlLnBlcmtUb2tlbk1pbnQsIC8vIFBlcmsgdG9rZW4gbWludFxuICAgICAgICAgIHRyYWRlLnRvLCAvLyBTZWxsZXIgd2FsbGV0IChjdXJyZW50IHVzZXIpXG4gICAgICAgICAgdHJhZGUuZnJvbSwgLy8gQnV5ZXIgd2FsbGV0XG4gICAgICAgICAgc29sYW5hV2FsbGV0LFxuICAgICAgICAgIGZhbHNlIC8vIHNraXBWYWxpZGF0aW9uXG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGJhY2tlbmQgd2l0aCByZWxlYXNlXG4gICAgICAgIGF3YWl0IHJlbGVhc2VQZXJrKHtcbiAgICAgICAgICB0cmFkZUlkOiB0cmFkZUlkVG9Vc2UudG9TdHJpbmcoKSxcbiAgICAgICAgICBzZWxsZXJXYWxsZXQ6IHNvbGFuYVdhbGxldC5hZGRyZXNzXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgW05vdGlmaWNhdGlvbkJlbGxdIEdsb2JhbCBlc2Nyb3cgcmVsZWFzZSBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuXG4gICAgICAgIC8vIFJlZnJlc2ggbm90aWZpY2F0aW9ucyB0byBzaG93IHVwZGF0ZWQgc3RhdHVzXG4gICAgICAgIGF3YWl0IGxvYWROb3RpZmljYXRpb25zKCk7XG5cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTm90aWZpY2F0aW9uQmVsbF0gR2xvYmFsIHJlbGVhc2UgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgICAgLy8gU2hvdyB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICAgICAgYWxlcnQoJ1JlbGVhc2UgZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNvbnRhY3Qgc3VwcG9ydC4nKTtcbiAgICAgIH1cbiAgICB9O1xuICB9O1xuXG4gIC8vIENyZWF0ZSByZWZ1bmQgZnVuY3Rpb24gZm9yIGVzY3JvdyAtIG5vdyB3b3JrcyBnbG9iYWxseSB3aXRob3V0IHJvdXRlIGRlcGVuZGVuY3lcbiAgY29uc3QgY3JlYXRlUmVmdW5kRnVuY3Rpb24gPSAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24sIHJlc29sdmVkVHJhZGVJZD86IG51bWJlciB8IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCB0cmFkZUlkVG9Vc2UgPSByZXNvbHZlZFRyYWRlSWQgfHwgbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQ7XG4gICAgICBpZiAoIXRyYWRlSWRUb1VzZSB8fCAhc29sYW5hV2FsbGV0Py5hZGRyZXNzIHx8ICFpc0Nvbm5lY3RlZCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIE1pc3NpbmcgZGF0YSBmb3IgcmVmdW5kOicsIHtcbiAgICAgICAgICB0cmFkZUlkOiB0cmFkZUlkVG9Vc2UsXG4gICAgICAgICAgd2FsbGV0OiBzb2xhbmFXYWxsZXQ/LmFkZHJlc3MsXG4gICAgICAgICAgY29ubmVjdGVkOiBpc0Nvbm5lY3RlZFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gU3RhcnRpbmcgZ2xvYmFsIGVzY3JvdyByZWZ1bmQgZnJvbSBub3RpZmljYXRpb24nKTtcblxuICAgICAgICAvLyBHZXQgdHJhZGUgZGV0YWlscyBmb3IgZXNjcm93IG9wZXJhdGlvblxuICAgICAgICBjb25zdCB0cmFkZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0NPTkZJRy5CQVNFX1VSTH0vcGVya3MvdHJhZGUvJHt0cmFkZUlkVG9Vc2V9L2VzY3Jvdy1kYXRhYCwge1xuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpfWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghdHJhZGVSZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHRyYWRlIGRldGFpbHMnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHRyYWRlRGF0YSA9IGF3YWl0IHRyYWRlUmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCB0cmFkZSA9IHRyYWRlRGF0YS5kYXRhO1xuXG4gICAgICAgIC8vIEV4ZWN1dGUgZXNjcm93IHJlZnVuZCB0cmFuc2FjdGlvbiB1c2luZyB0aGUgaG9va1xuICAgICAgICBhd2FpdCBlc2Nyb3dPcGVyYXRpb25zLmV4ZWN1dGVSZWZ1bmQoXG4gICAgICAgICAgdHJhZGUuZXNjcm93SWQsXG4gICAgICAgICAgJ1NvMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTInLCAvLyBTT0wgbWludFxuICAgICAgICAgIHNvbGFuYVdhbGxldC5hZGRyZXNzLCAvLyBCdXllciB3YWxsZXRcbiAgICAgICAgICBzb2xhbmFXYWxsZXRcbiAgICAgICAgKTtcblxuICAgICAgICAvLyBVcGRhdGUgYmFja2VuZCB3aXRoIHJlZnVuZFxuICAgICAgICBhd2FpdCByZWZ1bmRQZXJrKHtcbiAgICAgICAgICB0cmFkZUlkOiB0cmFkZUlkVG9Vc2UudG9TdHJpbmcoKSxcbiAgICAgICAgICBidXllcldhbGxldDogc29sYW5hV2FsbGV0LmFkZHJlc3NcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gR2xvYmFsIGVzY3JvdyByZWZ1bmQgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKTtcblxuICAgICAgICAvLyBSZWZyZXNoIG5vdGlmaWNhdGlvbnMgdG8gc2hvdyB1cGRhdGVkIHN0YXR1c1xuICAgICAgICBhd2FpdCBsb2FkTm90aWZpY2F0aW9ucygpO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIEdsb2JhbCByZWZ1bmQgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgICAgLy8gU2hvdyB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICAgICAgYWxlcnQoJ1JlZnVuZCBmYWlsZWQuIFBsZWFzZSB0cnkgYWdhaW4gb3IgY29udGFjdCBzdXBwb3J0LicpO1xuICAgICAgfVxuICAgIH07XG4gIH07XG5cbiAgLy8gQ3JlYXRlIGFjY2VwdCBmdW5jdGlvbiBmb3IgZXNjcm93IC0gbm93IHdvcmtzIGdsb2JhbGx5IHdpdGhvdXQgcm91dGUgZGVwZW5kZW5jeVxuICBjb25zdCBjcmVhdGVBY2NlcHRGdW5jdGlvbiA9IChub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbiwgcmVzb2x2ZWRUcmFkZUlkPzogbnVtYmVyIHwgc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHRyYWRlSWRUb1VzZSA9IHJlc29sdmVkVHJhZGVJZCB8fCBub3RpZmljYXRpb24uZGF0YT8udHJhZGVJZDtcbiAgICAgIGlmICghdHJhZGVJZFRvVXNlIHx8ICFzb2xhbmFXYWxsZXQ/LmFkZHJlc3MgfHwgIWlzQ29ubmVjdGVkKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTm90aWZpY2F0aW9uQmVsbF0gTWlzc2luZyBkYXRhIGZvciBhY2NlcHQ6Jywge1xuICAgICAgICAgIHRyYWRlSWQ6IHRyYWRlSWRUb1VzZSxcbiAgICAgICAgICB3YWxsZXQ6IHNvbGFuYVdhbGxldD8uYWRkcmVzcyxcbiAgICAgICAgICBjb25uZWN0ZWQ6IGlzQ29ubmVjdGVkXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBTdGFydGluZyBnbG9iYWwgZXNjcm93IGFjY2VwdCBmcm9tIG5vdGlmaWNhdGlvbicpO1xuXG4gICAgICAgIC8vIEV4ZWN1dGUgZXNjcm93IGFjY2VwdCB0cmFuc2FjdGlvbiB1c2luZyB0aGUgdXRpbGl0eSBmdW5jdGlvbiB3aXRoIHRyYWRlSWRcbiAgICAgICAgLy8gVGhlIGZ1bmN0aW9uIHdpbGwgYXV0b21hdGljYWxseSBmZXRjaCB0cmFkZSBkZXRhaWxzIGFuZCBjYWxjdWxhdGUgYW1vdW50c1xuICAgICAgICBjb25zdCB7IGNyZWF0ZUVzY3Jvd0FjY2VwdFRyYW5zYWN0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJy4uLy4uLy4uL3V0aWxzL2VzY3JvdycpO1xuICAgICAgICBjb25zdCB0eElkID0gYXdhaXQgY3JlYXRlRXNjcm93QWNjZXB0VHJhbnNhY3Rpb24oXG4gICAgICAgICAgXCJcIiwgLy8gZXNjcm93SWQgLSB3aWxsIGJlIGZldGNoZWQgZnJvbSB0cmFkZSBkYXRhXG4gICAgICAgICAgXCIwXCIsIC8vIHB1cmNoYXNlVG9rZW5BbW91bnQgLSB3aWxsIGJlIGNhbGN1bGF0ZWQgZnJvbSB0cmFkZSBkYXRhXG4gICAgICAgICAgXCIwXCIsIC8vIHBlcmtUb2tlbkFtb3VudCAtIHdpbGwgYmUgY2FsY3VsYXRlZCBmcm9tIHRyYWRlIGRhdGFcbiAgICAgICAgICBcIlwiLCAvLyBwdXJjaGFzZVRva2VuTWludCAtIHdpbGwgYmUgc2V0IGZyb20gdHJhZGUgZGF0YVxuICAgICAgICAgIFwiXCIsIC8vIHBlcmtUb2tlbk1pbnQgLSB3aWxsIGJlIGZldGNoZWQgZnJvbSB0cmFkZSBkYXRhXG4gICAgICAgICAgc29sYW5hV2FsbGV0LmFkZHJlc3MsIC8vIFNlbGxlciB3YWxsZXRcbiAgICAgICAgICBzb2xhbmFXYWxsZXQsXG4gICAgICAgICAgdHJhZGVJZFRvVXNlIC8vIHRyYWRlSWQgLSB0aGlzIHdpbGwgdHJpZ2dlciBhdXRvbWF0aWMgY2FsY3VsYXRpb25cbiAgICAgICAgKTtcblxuICAgICAgICAvLyBVcGRhdGUgYmFja2VuZCB3aXRoIGFjY2VwdGFuY2VcbiAgICAgICAgY29uc3QgYWNjZXB0UmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQ09ORklHLkJBU0VfVVJMfS9wZXJrcy90cmFkZXMvJHt0cmFkZUlkVG9Vc2V9L2FjY2VwdGAsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKX1gXG4gICAgICAgICAgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICB0eElkLFxuICAgICAgICAgICAgYWNjZXB0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSlcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKCFhY2NlcHRSZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBiYWNrZW5kIHdpdGggYWNjZXB0YW5jZScpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gR2xvYmFsIGVzY3JvdyBhY2NlcHQgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKTtcblxuICAgICAgICAvLyBSZWZyZXNoIG5vdGlmaWNhdGlvbnMgdG8gc2hvdyB1cGRhdGVkIHN0YXR1c1xuICAgICAgICBhd2FpdCBsb2FkTm90aWZpY2F0aW9ucygpO1xuXG4gICAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICAgIGFsZXJ0KCdFc2Nyb3cgYWNjZXB0ZWQgc3VjY2Vzc2Z1bGx5ISBUaGUgYnV5ZXIgY2FuIG5vdyByZWxlYXNlIGZ1bmRzLicpO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIEdsb2JhbCBhY2NlcHQgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgICAgLy8gU2hvdyB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICAgICAgYWxlcnQoJ0FjY2VwdCBmYWlsZWQuIFBsZWFzZSB0cnkgYWdhaW4gb3IgY29udGFjdCBzdXBwb3J0LicpO1xuICAgICAgfVxuICAgIH07XG4gIH07XG5cbiAgLy8gRnVuY3Rpb24gdG8gb3BlbiBjaGF0IG1vZGFsXG4gIGNvbnN0IG9wZW5DaGF0TW9kYWwgPSBhc3luYyAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24pID0+IHtcbiAgICAvLyBXZSBjYW4gb3BlbiBjaGF0IG1vZGFsIGlmIHdlIGhhdmUgZWl0aGVyIGNoYXRSb29tSWQgb3IgdHJhZGVJZFxuICAgIGlmICghbm90aWZpY2F0aW9uLmRhdGE/LmNoYXRSb29tSWQgJiYgIW5vdGlmaWNhdGlvbi5kYXRhPy50cmFkZUlkKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIE5vIGNoYXRSb29tSWQgb3IgdHJhZGVJZCBpbiBub3RpZmljYXRpb24gZGF0YScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEdldCBjdXJyZW50IHVzZXIgaWQgZnJvbSBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCB1c2VyQm9TdHIgPSB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ1c2VyQm9cIikgOiBudWxsO1xuICAgIGNvbnN0IHVzZXJCbyA9IHVzZXJCb1N0ciA/IEpTT04ucGFyc2UodXNlckJvU3RyKSA6IG51bGw7XG4gICAgY29uc3QgbXlVc2VySWQgPSB1c2VyQm8/LmlkO1xuXG4gICAgLy8gRGV0ZXJtaW5lIHRoZSBvdGhlciBwYXJ0eSAoYnV5ZXIgb3Igc2VsbGVyKVxuICAgIGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkO1xuICAgIGNvbnN0IHVzZXJJZE51bWJlciA9IHR5cGVvZiB1c2VySWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQodXNlcklkKSA6IHVzZXJJZDtcblxuICAgIGxldCBidXllcklkID0gbm90aWZpY2F0aW9uLmRhdGEuYnV5ZXJJZDtcbiAgICBsZXQgc2VsbGVySWQgPSBub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZDtcblxuICAgIC8vIFNwZWNpYWwgaGFuZGxpbmcgZm9yIGVzY3Jvd19yZWxlYXNlX3JlbWluZGVyIC0gc2VsbGVyIGlzIHRoZSBjdXJyZW50IHVzZXJcbiAgICBpZiAobm90aWZpY2F0aW9uLnR5cGUgPT09ICdlc2Nyb3dfcmVsZWFzZV9yZW1pbmRlcicpIHtcbiAgICAgIHNlbGxlcklkID0gdXNlcklkTnVtYmVyOyAvLyBDdXJyZW50IHVzZXIgaXMgdGhlIHNlbGxlclxuICAgICAgYnV5ZXJJZCA9IG5vdGlmaWNhdGlvbi5kYXRhLmJ1eWVySWQ7IC8vIEJ1eWVyIGZyb20gbm90aWZpY2F0aW9uIGRhdGFcbiAgICB9XG4gICAgLy8gSWYgd2UgZG9uJ3QgaGF2ZSBib3RoIElEcywgdHJ5IHRvIGRldGVybWluZSBmcm9tIHRoZSBub3RpZmljYXRpb25cbiAgICBlbHNlIGlmICghYnV5ZXJJZCB8fCAhc2VsbGVySWQpIHtcbiAgICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5idXllcklkID09PSB1c2VySWROdW1iZXIpIHtcbiAgICAgICAgYnV5ZXJJZCA9IHVzZXJJZE51bWJlcjtcbiAgICAgICAgc2VsbGVySWQgPSBub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZCB8fCBub3RpZmljYXRpb24uZGF0YS5yZWNlaXZlcklkO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2VsbGVySWQgPSB1c2VySWROdW1iZXI7XG4gICAgICAgIGJ1eWVySWQgPSBub3RpZmljYXRpb24uZGF0YS5idXllcklkIHx8IG5vdGlmaWNhdGlvbi5kYXRhLnNlbmRlcklkO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEdlbmVyYXRlIGNvbnNpc3RlbnQgY2hhdCByb29tIElEIGlmIG5vdCBwcm92aWRlZFxuICAgIGxldCBjaGF0Um9vbUlkID0gbm90aWZpY2F0aW9uLmRhdGEuY2hhdFJvb21JZDtcbiAgICBpZiAoIWNoYXRSb29tSWQgJiYgYnV5ZXJJZCAmJiBzZWxsZXJJZCAmJiBub3RpZmljYXRpb24uZGF0YT8ucGVya0lkKSB7XG4gICAgICBjaGF0Um9vbUlkID0gZ2VuZXJhdGVDaGF0Um9vbUlkKGJ1eWVySWQsIHNlbGxlcklkLCBub3RpZmljYXRpb24uZGF0YS5wZXJrSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIEdlbmVyYXRlZCBjaGF0Um9vbUlkIGZyb20gdHJhZGUgZGF0YTonLCBjaGF0Um9vbUlkKTtcbiAgICB9XG5cbiAgICBpZiAoIWNoYXRSb29tSWQpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTm90aWZpY2F0aW9uQmVsbF0gQ291bGQgbm90IGRldGVybWluZSBjaGF0Um9vbUlkJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIE9wZW5pbmcgY2hhdCBtb2RhbCB3aXRoOicsIHtcbiAgICAgIGNoYXRSb29tSWQsXG4gICAgICBidXllcklkLFxuICAgICAgc2VsbGVySWQsXG4gICAgICBteVVzZXJJZCxcbiAgICAgIHVzZXJJZE51bWJlclxuICAgIH0pO1xuXG4gICAgLy8gRGVidWcgbm90aWZpY2F0aW9uIGRhdGFcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gT3BlbmluZyBjaGF0IG1vZGFsIHdpdGggZGF0YTonLCB7XG4gICAgICBub3RpZmljYXRpb25UeXBlOiBub3RpZmljYXRpb24udHlwZSxcbiAgICAgIG5vdGlmaWNhdGlvbkRhdGE6IG5vdGlmaWNhdGlvbi5kYXRhLFxuICAgICAgYnV5ZXJJZDogYnV5ZXJJZCB8fCBteVVzZXJJZCxcbiAgICAgIHNlbGxlcklkOiBzZWxsZXJJZCB8fCBteVVzZXJJZCxcbiAgICAgIG15VXNlcklkLFxuICAgICAgY3VycmVudFVzZXI6IHVzZXI/LmlkLFxuICAgICAgdHJhZGVJZDogbm90aWZpY2F0aW9uLmRhdGEudHJhZGVJZCxcbiAgICAgIGhhc1RyYWRlSWQ6ICEhbm90aWZpY2F0aW9uLmRhdGEudHJhZGVJZCxcbiAgICAgIGhhc0NoYXRSb29tSWQ6ICEhbm90aWZpY2F0aW9uLmRhdGEuY2hhdFJvb21JZCxcbiAgICAgIGhhc1BlcmtJZDogISFub3RpZmljYXRpb24uZGF0YS5wZXJrSWRcbiAgICB9KTtcblxuICAgIC8vIEZldGNoIHJlYWwgdHJhZGUgZGV0YWlscyBpZiB0cmFkZUlkIGV4aXN0cywgb3IgdHJ5IHRvIGZpbmQgaXQgZnJvbSBjaGF0Um9vbUlkXG4gICAgbGV0IGFjdGl2ZVRyYWRlID0gdW5kZWZpbmVkO1xuICAgIGxldCB0cmFkZUlkVG9Vc2UgPSBub3RpZmljYXRpb24uZGF0YS50cmFkZUlkO1xuXG4gICAgLy8gSWYgbm8gdHJhZGVJZCBidXQgd2UgaGF2ZSBjaGF0Um9vbUlkLCB0cnkgdG8gZmluZCB0aGUgdHJhZGVcbiAgICBpZiAoIXRyYWRlSWRUb1VzZSAmJiBub3RpZmljYXRpb24uZGF0YS5jaGF0Um9vbUlkKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gTm8gdHJhZGVJZCBmb3VuZCwgc2VhcmNoaW5nIGJ5IGNoYXRSb29tSWQ6Jywgbm90aWZpY2F0aW9uLmRhdGEuY2hhdFJvb21JZCk7XG5cbiAgICAgICAgLy8gRXh0cmFjdCBwZXJrSWQgZnJvbSBjaGF0Um9vbUlkIGZvcm1hdDogYnV5ZXJJZC1zZWxsZXJJZC1wZXJrSWRcbiAgICAgICAgY29uc3QgY2hhdFJvb21QYXJ0cyA9IG5vdGlmaWNhdGlvbi5kYXRhLmNoYXRSb29tSWQuc3BsaXQoJy0nKTtcbiAgICAgICAgaWYgKGNoYXRSb29tUGFydHMubGVuZ3RoID09PSAzKSB7XG4gICAgICAgICAgY29uc3QgcGVya0lkID0gY2hhdFJvb21QYXJ0c1syXTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gRXh0cmFjdGVkIHBlcmtJZCBmcm9tIGNoYXRSb29tSWQ6JywgcGVya0lkKTtcblxuICAgICAgICAgIC8vIFVzZSBnZXRVc2VyVHJhZGVzIHRvIGZpbmQgdHJhZGVzIGZvciBjdXJyZW50IHVzZXIgYW5kIGZpbHRlciBieSBwZXJrSWRcbiAgICAgICAgICBjb25zdCB7IGdldFVzZXJUcmFkZXMgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vYXhpb3MvcmVxdWVzdHMnKTtcbiAgICAgICAgICBjb25zdCB0cmFkZXNSZXNwb25zZSA9IGF3YWl0IGdldFVzZXJUcmFkZXMoU3RyaW5nKG15VXNlcklkKSk7XG5cbiAgICAgICAgICBpZiAodHJhZGVzUmVzcG9uc2Uuc3RhdHVzID09PSAyMDAgJiYgdHJhZGVzUmVzcG9uc2UuZGF0YSkge1xuICAgICAgICAgICAgLy8gRmluZCBhY3RpdmUgdHJhZGUgZm9yIHRoaXMgcGVya1xuICAgICAgICAgICAgY29uc3QgYWN0aXZlVHJhZGVzID0gdHJhZGVzUmVzcG9uc2UuZGF0YS5maWx0ZXIoKHRyYWRlOiBhbnkpID0+XG4gICAgICAgICAgICAgIHRyYWRlLnBlcmtJZCA9PT0gTnVtYmVyKHBlcmtJZCkgJiZcbiAgICAgICAgICAgICAgWydwZW5kaW5nX2FjY2VwdGFuY2UnLCAnZXNjcm93ZWQnLCAnY29tcGxldGVkJywgJ3JlbGVhc2VkJ10uaW5jbHVkZXModHJhZGUuc3RhdHVzKVxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgaWYgKGFjdGl2ZVRyYWRlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgIC8vIEdldCB0aGUgbW9zdCByZWNlbnQgdHJhZGVcbiAgICAgICAgICAgICAgY29uc3Qgc29ydGVkVHJhZGVzID0gYWN0aXZlVHJhZGVzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PlxuICAgICAgICAgICAgICAgIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkuZ2V0VGltZSgpXG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIHRyYWRlSWRUb1VzZSA9IHNvcnRlZFRyYWRlc1swXS5pZDtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gRm91bmQgYWN0aXZlIHRyYWRlIGZyb20gY2hhdFJvb21JZDonLCB0cmFkZUlkVG9Vc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBbTm90aWZpY2F0aW9uQmVsbF0gQ291bGQgbm90IGZpbmQgdHJhZGUgZnJvbSBjaGF0Um9vbUlkOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAodHJhZGVJZFRvVXNlKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gRmV0Y2hpbmcgdHJhZGUgZGV0YWlscyBmb3IgdHJhZGVJZDonLCB0cmFkZUlkVG9Vc2UpO1xuICAgICAgICBjb25zdCB7IGdldFRyYWRlRGV0YWlscyB9ID0gYXdhaXQgaW1wb3J0KCcuLi8uLi8uLi9heGlvcy9yZXF1ZXN0cycpO1xuICAgICAgICBjb25zdCB0cmFkZVJlc3BvbnNlID0gYXdhaXQgZ2V0VHJhZGVEZXRhaWxzKE51bWJlcih0cmFkZUlkVG9Vc2UpKTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gVHJhZGUgZGV0YWlscyByZXNwb25zZTonLCB7XG4gICAgICAgICAgc3RhdHVzOiB0cmFkZVJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgICBoYXNEYXRhOiAhIXRyYWRlUmVzcG9uc2UuZGF0YSxcbiAgICAgICAgICB0cmFkZURhdGE6IHRyYWRlUmVzcG9uc2UuZGF0YVxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAodHJhZGVSZXNwb25zZS5zdGF0dXMgPT09IDIwMCAmJiB0cmFkZVJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgICBhY3RpdmVUcmFkZSA9IHtcbiAgICAgICAgICAgIGlkOiB0cmFkZVJlc3BvbnNlLmRhdGEuaWQsXG4gICAgICAgICAgICBzdGF0dXM6IHRyYWRlUmVzcG9uc2UuZGF0YS5zdGF0dXMsXG4gICAgICAgICAgICB0cmFkZUlkOiB0cmFkZVJlc3BvbnNlLmRhdGEuaWQsXG4gICAgICAgICAgICBmcm9tOiB0cmFkZVJlc3BvbnNlLmRhdGEudXNlcklkLCAvLyBidXllclxuICAgICAgICAgICAgdG86IHRyYWRlUmVzcG9uc2UuZGF0YS5wZXJrRGV0YWlscz8udXNlcklkLCAvLyBzZWxsZXJcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogdHJhZGVSZXNwb25zZS5kYXRhLmNyZWF0ZWRBdCxcbiAgICAgICAgICAgIGRpc3B1dGVTdGF0dXM6IHRyYWRlUmVzcG9uc2UuZGF0YS5kaXNwdXRlPy5zdGF0dXMgfHwgJ25vbmUnLFxuICAgICAgICAgICAgZXNjcm93SWQ6IHRyYWRlUmVzcG9uc2UuZGF0YS5lc2Nyb3dJZCxcbiAgICAgICAgICAgIHBlcmtJZDogdHJhZGVSZXNwb25zZS5kYXRhLnBlcmtJZFxuICAgICAgICAgIH07XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gVHJhZGUgZGV0YWlscyBmZXRjaGVkIHN1Y2Nlc3NmdWxseTonLCB7XG4gICAgICAgICAgICB0cmFkZUlkOiBhY3RpdmVUcmFkZS5pZCxcbiAgICAgICAgICAgIHN0YXR1czogYWN0aXZlVHJhZGUuc3RhdHVzLFxuICAgICAgICAgICAgYnV5ZXI6IGFjdGl2ZVRyYWRlLmZyb20sXG4gICAgICAgICAgICBzZWxsZXI6IGFjdGl2ZVRyYWRlLnRvLFxuICAgICAgICAgICAgZXNjcm93SWQ6IGFjdGl2ZVRyYWRlLmVzY3Jvd0lkLFxuICAgICAgICAgICAgcGVya0lkOiBhY3RpdmVUcmFkZS5wZXJrSWRcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIFtOb3RpZmljYXRpb25CZWxsXSBUcmFkZSBkZXRhaWxzIHJlc3BvbnNlIG5vdCBzdWNjZXNzZnVsLCB1c2luZyBmYWxsYmFjaycpO1xuICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIG5vdGlmaWNhdGlvbiBkYXRhXG4gICAgICAgICAgYWN0aXZlVHJhZGUgPSB7XG4gICAgICAgICAgICBpZDogdHJhZGVJZFRvVXNlLFxuICAgICAgICAgICAgc3RhdHVzOiBub3RpZmljYXRpb24uZGF0YS5zdGF0dXMgfHwgJ3BlbmRpbmdfYWNjZXB0YW5jZScsIC8vIFVzZSBub3RpZmljYXRpb24gc3RhdHVzIGlmIGF2YWlsYWJsZVxuICAgICAgICAgICAgdHJhZGVJZDogdHJhZGVJZFRvVXNlLFxuICAgICAgICAgICAgZnJvbTogYnV5ZXJJZCxcbiAgICAgICAgICAgIHRvOiBzZWxsZXJJZCxcbiAgICAgICAgICAgIHBlcmtJZDogbm90aWZpY2F0aW9uLmRhdGEucGVya0lkLFxuICAgICAgICAgICAgZXNjcm93SWQ6IG5vdGlmaWNhdGlvbi5kYXRhLmVzY3Jvd0lkXG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBGYWlsZWQgdG8gZmV0Y2ggdHJhZGUgZGV0YWlsczonLCBlcnJvcik7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIG5vdGlmaWNhdGlvbiBkYXRhXG4gICAgICAgIGFjdGl2ZVRyYWRlID0ge1xuICAgICAgICAgIGlkOiB0cmFkZUlkVG9Vc2UsXG4gICAgICAgICAgc3RhdHVzOiBub3RpZmljYXRpb24uZGF0YS5zdGF0dXMgfHwgJ3BlbmRpbmdfYWNjZXB0YW5jZScsIC8vIFVzZSBub3RpZmljYXRpb24gc3RhdHVzIGlmIGF2YWlsYWJsZVxuICAgICAgICAgIHRyYWRlSWQ6IHRyYWRlSWRUb1VzZSxcbiAgICAgICAgICBmcm9tOiBidXllcklkLFxuICAgICAgICAgIHRvOiBzZWxsZXJJZCxcbiAgICAgICAgICBwZXJrSWQ6IG5vdGlmaWNhdGlvbi5kYXRhLnBlcmtJZCxcbiAgICAgICAgICBlc2Nyb3dJZDogbm90aWZpY2F0aW9uLmRhdGEuZXNjcm93SWRcbiAgICAgICAgfTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQgW05vdGlmaWNhdGlvbkJlbGxdIFVzaW5nIGZhbGxiYWNrIHRyYWRlIGRhdGE6JywgYWN0aXZlVHJhZGUpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIFtOb3RpZmljYXRpb25CZWxsXSBObyB0cmFkZUlkIGZvdW5kLCBjaGF0IHdpbGwgb3BlbiB3aXRob3V0IHRyYWRlIGNvbnRleHQnKTtcbiAgICB9XG5cbiAgICAvLyBVc2UgY29uc2lzdGVudCBtb2RhbCBJRCBiYXNlZCBvbiBjaGF0Um9vbUlkIHRvIHByZXZlbnQgbXVsdGlwbGUgbW9kYWxzXG4gICAgY29uc3QgbW9kYWxJZCA9IGBjaGF0LW1vZGFsLSR7Y2hhdFJvb21JZH1gO1xuXG4gICAgLy8gQ2hlY2sgaWYgbW9kYWwgaXMgYWxyZWFkeSBvcGVuIC0gaWYgc28sIGRvbid0IGNsb3NlIGl0LCBqdXN0IGZvY3VzIGl0XG4gICAgY29uc3QgZXhpc3RpbmdNb2RhbCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKG1vZGFsSWQpO1xuICAgIGlmIChleGlzdGluZ01vZGFsKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIFtOb3RpZmljYXRpb25CZWxsXSBDaGF0IG1vZGFsIGFscmVhZHkgb3BlbiwgZm9jdXNpbmcgZXhpc3RpbmcgbW9kYWwnKTtcbiAgICAgIGV4aXN0aW5nTW9kYWwuc2Nyb2xsSW50b1ZpZXcoeyBiZWhhdmlvcjogJ3Ntb290aCcgfSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gT3BlbiBDaGF0TW9kYWwgaW4gZ2xvYmFsIG1vZGFsXG4gICAgb3Blbk1vZGFsKHtcbiAgICAgIGlkOiBtb2RhbElkLFxuICAgICAgY29tcG9uZW50OiBSZWFjdC5jcmVhdGVFbGVtZW50KENoYXRNb2RhbCwge1xuICAgICAgICBjaGF0Um9vbUlkLFxuICAgICAgICBidXllcklkOiBidXllcklkIHx8IG15VXNlcklkLFxuICAgICAgICBzZWxsZXJJZDogc2VsbGVySWQgfHwgbXlVc2VySWQsXG4gICAgICAgIG9uQ2xvc2U6ICgpID0+IGNsb3NlTW9kYWwobW9kYWxJZCksXG4gICAgICAgIG9uUmVsZWFzZTogY3JlYXRlUmVsZWFzZUZ1bmN0aW9uKG5vdGlmaWNhdGlvbiwgdHJhZGVJZFRvVXNlKSxcbiAgICAgICAgb25SZWZ1bmQ6IGNyZWF0ZVJlZnVuZEZ1bmN0aW9uKG5vdGlmaWNhdGlvbiwgdHJhZGVJZFRvVXNlKSxcbiAgICAgICAgb25SZXBvcnQ6ICgpID0+IHt9LFxuICAgICAgICBvbkFjY2VwdDogY3JlYXRlQWNjZXB0RnVuY3Rpb24obm90aWZpY2F0aW9uLCB0cmFkZUlkVG9Vc2UpLFxuICAgICAgICBhY3RpdmVUcmFkZVxuICAgICAgfSksXG4gICAgICBjbG9zZU9uQmFja2Ryb3BDbGljazogZmFsc2UsXG4gICAgICBjbG9zZU9uRXNjYXBlOiB0cnVlLFxuICAgICAgcHJldmVudENsb3NlOiBmYWxzZSxcbiAgICAgIHpJbmRleDogOTk5OSxcbiAgICAgIGJhY2tkcm9wQ2xhc3NOYW1lOiAnYmctYmxhY2svNTAgYmFja2Ryb3AtYmx1ci1zbScsXG4gICAgICBtb2RhbENsYXNzTmFtZTogJycsXG4gICAgICBkaXNhYmxlU2Nyb2xsOiB0cnVlXG4gICAgfSk7XG5cbiAgICAvLyBDbG9zZSB0aGUgbm90aWZpY2F0aW9uIGRyb3Bkb3duXG4gICAgc2V0SXNPcGVuKGZhbHNlKTtcbiAgfTtcblxuICAvLyBMb2FkIG5vdGlmaWNhdGlvbnMgb24gbW91bnQgYW5kIHdoZW4gdXNlciBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZE5vdGlmaWNhdGlvbnMoKTtcbiAgfSwgW3VzZXJdKTtcblxuICAvLyBSZWZyZXNoIG5vdGlmaWNhdGlvbnMgZXZlcnkgMzAgc2Vjb25kc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwobG9hZE5vdGlmaWNhdGlvbnMsIDMwMDAwKTtcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8gQ2xvc2UgZHJvcGRvd24gd2hlbiBjbGlja2luZyBvdXRzaWRlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlQ2xpY2tPdXRzaWRlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XG4gICAgICBpZiAoZHJvcGRvd25SZWYuY3VycmVudCAmJiAhZHJvcGRvd25SZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQgYXMgTm9kZSkpIHtcbiAgICAgICAgc2V0SXNPcGVuKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgfSwgW10pO1xuXG4gIC8vIE1hcmsgbm90aWZpY2F0aW9uIGFzIHJlYWRcbiAgY29uc3QgaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2sgPSBhc3luYyAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24pID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gTm90aWZpY2F0aW9uIGNsaWNrZWQ6Jywge1xuICAgICAgdHlwZTogbm90aWZpY2F0aW9uLnR5cGUsXG4gICAgICBzaG91bGRPcGVuQ2hhdDogc2hvdWxkT3BlbkNoYXRNb2RhbChub3RpZmljYXRpb24pLFxuICAgICAgY2hhdFJvb21JZDogbm90aWZpY2F0aW9uLmRhdGE/LmNoYXRSb29tSWQsXG4gICAgICBhY3Rpb25Vcmw6IG5vdGlmaWNhdGlvbi5hY3Rpb25VcmxcbiAgICB9KTtcblxuICAgIGlmICghbm90aWZpY2F0aW9uLmlzUmVhZCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgbWFya05vdGlmaWNhdGlvbkFzUmVhZChub3RpZmljYXRpb24uaWQpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgYXBwcm9wcmlhdGUgbm90aWZpY2F0aW9uIGxpc3RcbiAgICAgICAgaWYgKGlzVHJhZGVOb3RpZmljYXRpb24obm90aWZpY2F0aW9uKSkge1xuICAgICAgICAgIHNldFRyYWRlTm90aWZpY2F0aW9ucyhwcmV2ID0+XG4gICAgICAgICAgICBwcmV2Lm1hcChuID0+IG4uaWQgPT09IG5vdGlmaWNhdGlvbi5pZCA/IHsgLi4ubiwgaXNSZWFkOiB0cnVlIH0gOiBuKVxuICAgICAgICAgICk7XG4gICAgICAgICAgc2V0VHJhZGVVbnJlYWRDb3VudChwcmV2ID0+IE1hdGgubWF4KDAsIHByZXYgLSAxKSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0U3lzdGVtTm90aWZpY2F0aW9ucyhwcmV2ID0+XG4gICAgICAgICAgICBwcmV2Lm1hcChuID0+IG4uaWQgPT09IG5vdGlmaWNhdGlvbi5pZCA/IHsgLi4ubiwgaXNSZWFkOiB0cnVlIH0gOiBuKVxuICAgICAgICAgICk7XG4gICAgICAgICAgc2V0U3lzdGVtVW5yZWFkQ291bnQocHJldiA9PiBNYXRoLm1heCgwLCBwcmV2IC0gMSkpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbWFyayBub3RpZmljYXRpb24gYXMgcmVhZDonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhpcyBub3RpZmljYXRpb24gc2hvdWxkIG9wZW4gY2hhdCBtb2RhbCAobm93IGFsbCB0cmFkZSBub3RpZmljYXRpb25zIG9wZW4gY2hhdCBtb2RhbHMpXG4gICAgaWYgKHNob3VsZE9wZW5DaGF0TW9kYWwobm90aWZpY2F0aW9uKSkge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gT3BlbmluZyBjaGF0IG1vZGFsIGZvciBub3RpZmljYXRpb24nKTtcbiAgICAgIG9wZW5DaGF0TW9kYWwobm90aWZpY2F0aW9uKTtcbiAgICB9IGVsc2UgaWYgKG5vdGlmaWNhdGlvbi5hY3Rpb25VcmwpIHtcbiAgICAgIC8vIE5hdmlnYXRlIHRvIGFjdGlvbiBVUkwgb25seSBmb3Igc3lzdGVtIG5vdGlmaWNhdGlvbnMgKG1vZGVyYXRvciBkYXNoYm9hcmQsIGV0Yy4pXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gTmF2aWdhdGluZyB0byBhY3Rpb25Vcmw6Jywgbm90aWZpY2F0aW9uLmFjdGlvblVybCk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IG5vdGlmaWNhdGlvbi5hY3Rpb25Vcmw7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEZvciBub3RpZmljYXRpb25zIHdpdGhvdXQgYWN0aW9uVXJsLCBqdXN0IG1hcmsgYXMgcmVhZCAoYWxyZWFkeSBoYW5kbGVkIGFib3ZlKVxuICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gTm90aWZpY2F0aW9uIG1hcmtlZCBhcyByZWFkJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIE1hcmsgYWxsIGFzIHJlYWQgZm9yIGN1cnJlbnQgdGFiXG4gIGNvbnN0IGhhbmRsZU1hcmtBbGxBc1JlYWQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG1hcmtBbGxOb3RpZmljYXRpb25zQXNSZWFkKCk7XG5cbiAgICAgIC8vIFVwZGF0ZSBib3RoIG5vdGlmaWNhdGlvbiBsaXN0c1xuICAgICAgc2V0U3lzdGVtTm90aWZpY2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKG4gPT4gKHsgLi4ubiwgaXNSZWFkOiB0cnVlIH0pKSk7XG4gICAgICBzZXRUcmFkZU5vdGlmaWNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChuID0+ICh7IC4uLm4sIGlzUmVhZDogdHJ1ZSB9KSkpO1xuXG4gICAgICAvLyBSZXNldCB1bnJlYWQgY291bnRzXG4gICAgICBzZXRTeXN0ZW1VbnJlYWRDb3VudCgwKTtcbiAgICAgIHNldFRyYWRlVW5yZWFkQ291bnQoMCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBtYXJrIGFsbCBub3RpZmljYXRpb25zIGFzIHJlYWQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBNYXJrIGFsbCBhcyByZWFkIGZvciBzcGVjaWZpYyB0YWJcbiAgY29uc3QgaGFuZGxlTWFya1RhYkFzUmVhZCA9IGFzeW5jICh0YWJUeXBlOiBUYWJUeXBlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIE5vdGU6IFRoaXMgd291bGQgaWRlYWxseSBiZSBhIG1vcmUgc3BlY2lmaWMgQVBJIGNhbGxcbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIG1hcmsgaW5kaXZpZHVhbCBub3RpZmljYXRpb25zIGFzIHJlYWRcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbnMgPSB0YWJUeXBlID09PSAnc3lzdGVtJyA/IHN5c3RlbU5vdGlmaWNhdGlvbnMgOiB0cmFkZU5vdGlmaWNhdGlvbnM7XG4gICAgICBjb25zdCB1bnJlYWROb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiAhbi5pc1JlYWQpO1xuXG4gICAgICBmb3IgKGNvbnN0IG5vdGlmaWNhdGlvbiBvZiB1bnJlYWROb3RpZmljYXRpb25zKSB7XG4gICAgICAgIGF3YWl0IG1hcmtOb3RpZmljYXRpb25Bc1JlYWQobm90aWZpY2F0aW9uLmlkKTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHRoZSBhcHByb3ByaWF0ZSBsaXN0XG4gICAgICBpZiAodGFiVHlwZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgc2V0U3lzdGVtTm90aWZpY2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKG4gPT4gKHsgLi4ubiwgaXNSZWFkOiB0cnVlIH0pKSk7XG4gICAgICAgIHNldFN5c3RlbVVucmVhZENvdW50KDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VHJhZGVOb3RpZmljYXRpb25zKHByZXYgPT4gcHJldi5tYXAobiA9PiAoeyAuLi5uLCBpc1JlYWQ6IHRydWUgfSkpKTtcbiAgICAgICAgc2V0VHJhZGVVbnJlYWRDb3VudCgwKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIG1hcmsgJHt0YWJUeXBlfSBub3RpZmljYXRpb25zIGFzIHJlYWQ6YCwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBHZXQgbm90aWZpY2F0aW9uIGljb24gYmFzZWQgb24gdHlwZVxuICBjb25zdCBnZXROb3RpZmljYXRpb25JY29uID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnZGlzcHV0ZV9jcmVhdGVkJzpcbiAgICAgIGNhc2UgJ2Rpc3B1dGVfYXNzaWduZWQnOlxuICAgICAgY2FzZSAnZGlzcHV0ZV9yZXNvbHZlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXllbGxvdy0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC15ZWxsb3ctNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA5djJtMCA0aC4wMW0tNi45MzggNGgxMy44NTZjMS41NCAwIDIuNTAyLTEuNjY3IDEuNzMyLTIuNUwxMy43MzIgNGMtLjc3LS44MzMtMS45NjQtLjgzMy0yLjczMiAwTDMuMjY4IDE2LjVjLS43Ny44MzMuMTkyIDIuNSAxLjczMiAyLjV6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAndHJhZGVfY29tcGxldGVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JlZW4tMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk01IDEzbDQgNEwxOSA3XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAndHJhZGVfcmVmdW5kZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1yZWQtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcmVkLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgOGMtMS42NTcgMC0zIC44OTUtMyAyczEuMzQzIDIgMyAyIDMgLjg5NSAzIDItMS4zNDMgMi0zIDJtMC04YzEuMTEgMCAyLjA4LjQwMiAyLjU5OSAxTTEyIDhWN20wIDF2OG0wIDB2MW0wLTFjLTEuMTEgMC0yLjA4LS40MDItMi41OTktMVwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ21vZGVyYXRvcl9hZGRlZCc6XG4gICAgICBjYXNlICdtb2RlcmF0b3JfcmVtb3ZlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXB1cnBsZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1wdXJwbGUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05IDEybDIgMiA0LTRtNS42MTgtNC4wMTZBMTEuOTU1IDExLjk1NSAwIDAxMTIgMi45NDRhMTEuOTU1IDExLjk1NSAwIDAxLTguNjE4IDMuMDRBMTIuMDIgMTIuMDIgMCAwMDMgOWMwIDUuNTkxIDMuODI0IDEwLjI5IDkgMTEuNjIyIDUuMTc2LTEuMzMyIDktNi4wMyA5LTExLjYyMiAwLTEuMDQyLS4xMzMtMi4wNTItLjM4Mi0zLjAxNnpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdzeXN0ZW1fYW5ub3VuY2VtZW50JzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctaW5kaWdvLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWluZGlnby02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTExIDUuODgyVjE5LjI0YTEuNzYgMS43NiAwIDAxLTMuNDE3LjU5MmwtMi4xNDctNi4xNU0xOCAxM2EzIDMgMCAxMDAtNk01LjQzNiAxMy42ODNBNC4wMDEgNC4wMDEgMCAwMTcgNmgxLjgzMmM0LjEgMCA3LjYyNS0xLjIzNCA5LjE2OC0zdjE0Yy0xLjU0My0xLjc2Ni01LjA2Ny0zLTkuMTY4LTNIN2EzLjk4OCAzLjk4OCAwIDAxLTEuNTY0LS4zMTd6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAncGVya19jcmVhdGVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZW1lcmFsZC0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1lbWVyYWxkLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgNnY2bTAgMHY2bTAtNmg2bS02IDBINlwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3Rva2VuX2NyZWF0ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1hbWJlci0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1hbWJlci02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDhjLTEuNjU3IDAtMyAuODk1LTMgMnMxLjM0MyAyIDMgMiAzIC44OTUgMyAyLTEuMzQzIDItMyAybTAtOGMxLjExIDAgMi4wOC40MDIgMi41OTkgMU0xMiA4VjdtMCAxdjhtMCAwdjFtMC0xYy0xLjExIDAtMi4wOC0uNDAyLTIuNTk5LTFcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdjaGF0X21lc3NhZ2UnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk04IDEyaC4wMU0xMiAxMmguMDFNMTYgMTJoLjAxTTIxIDEyYzAgNC40MTgtNC4wMyA4LTkgOGE5Ljg2MyA5Ljg2MyAwIDAxLTQuMjU1LS45NDlMMyAyMGwxLjM5NS0zLjcyQzMuNTEyIDE1LjA0MiAzIDEzLjU3NCAzIDEyYzAtNC40MTggNC4wMy04IDktOHM5IDMuNTgyIDkgOHpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdlc2Nyb3dfY3JlYXRlZCc6XG4gICAgICBjYXNlICdlc2Nyb3dfcmVsZWFzZWQnOlxuICAgICAgY2FzZSAnZXNjcm93X3JlbGVhc2VfcmVtaW5kZXInOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1vcmFuZ2UtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtb3JhbmdlLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgMTV2Mm0tNiA0aDEyYTIgMiAwIDAwMi0ydi02YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnY2YTIgMiAwIDAwMiAyem0xMC0xMFY3YTQgNCAwIDAwLTggMHY0aDh6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnZXNjcm93X3BlbmRpbmdfYWNjZXB0YW5jZSc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDh2NGwzIDNtNi0zYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2VzY3Jvd19hY2NlcHRlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyZWVuLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOSAxMmwyIDIgNC00bTYgMmE5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdkaXNwdXRlX2NyZWF0ZWQnOlxuICAgICAgY2FzZSAnZGlzcHV0ZV9hc3NpZ25lZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXllbGxvdy0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC15ZWxsb3ctNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA5djJtMCA0aC4wMW0tNi45MzggNGgxMy44NTZjMS41NCAwIDIuNTAyLTEuNjY3IDEuNzMyLTIuNUwxMy43MzIgNGMtLjc3LS44MzMtMS45NjQtLjgzMy0yLjczMiAwTDMuMjY4IDE2LjVjLS43Ny44MzMuMTkyIDIuNSAxLjczMiAyLjV6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnZGlzcHV0ZV9yZXNvbHZlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyZWVuLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOSAxMmwyIDIgNC00bTYgMmE5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdwZXJrX3B1cmNoYXNlZCc6XG4gICAgICBjYXNlICdwZXJrX3NvbGQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1jeWFuLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWN5YW4tNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNiAxMVY3YTQgNCAwIDAwLTggMHY0TTUgOWgxNGwxIDEySDRMNSA5elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3RyYWRlX3JlcG9ydGVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctcmVkLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItMi41TDEzLjczMiA0Yy0uNzctLjgzMy0xLjk2NC0uODMzLTIuNzMyIDBMMy4yNjggMTYuNWMtLjc3LjgzMy4xOTIgMi41IDEuNzMyIDIuNXpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxNmgtMXYtNGgtMW0xLTRoLjAxTTIxIDEyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRm9ybWF0IHRpbWUgYWdvXG4gIGNvbnN0IGZvcm1hdFRpbWVBZ28gPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpO1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgZGlmZkluU2Vjb25kcyA9IE1hdGguZmxvb3IoKG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKSkgLyAxMDAwKTtcblxuICAgIGlmIChkaWZmSW5TZWNvbmRzIDwgNjApIHJldHVybiAnSnVzdCBub3cnO1xuICAgIGlmIChkaWZmSW5TZWNvbmRzIDwgMzYwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDYwKX1tIGFnb2A7XG4gICAgaWYgKGRpZmZJblNlY29uZHMgPCA4NjQwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDM2MDApfWggYWdvYDtcbiAgICByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gODY0MDApfWQgYWdvYDtcbiAgfTtcblxuICAvLyBEb24ndCByZW5kZXIgaWYgdXNlciBpcyBub3QgYXV0aGVudGljYXRlZFxuICBpZiAoIXVzZXIpIHJldHVybiBudWxsO1xuXG4gIGNvbnN0IHRvdGFsVW5yZWFkQ291bnQgPSBzeXN0ZW1VbnJlYWRDb3VudCArIHRyYWRlVW5yZWFkQ291bnQ7XG4gIGNvbnN0IGN1cnJlbnROb3RpZmljYXRpb25zID0gYWN0aXZlVGFiID09PSAnc3lzdGVtJyA/IHN5c3RlbU5vdGlmaWNhdGlvbnMgOiB0cmFkZU5vdGlmaWNhdGlvbnM7XG4gIGNvbnN0IGN1cnJlbnRVbnJlYWRDb3VudCA9IGFjdGl2ZVRhYiA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1VbnJlYWRDb3VudCA6IHRyYWRlVW5yZWFkQ291bnQ7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCIgcmVmPXtkcm9wZG93blJlZn0+XG4gICAgICB7LyogUmVhbC10aW1lIG5vdGlmaWNhdGlvbiBsaXN0ZW5lciAqL31cbiAgICAgIDxSZWFsVGltZU5vdGlmaWNhdGlvbkxpc3RlbmVyIG9uTmV3Tm90aWZpY2F0aW9uPXtsb2FkTm90aWZpY2F0aW9uc30gLz5cblxuICAgICAgey8qIE5vdGlmaWNhdGlvbiBCZWxsIEJ1dHRvbiAqL31cbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKCFpc09wZW4pfVxuICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLW9mZnNldC0yIHJvdW5kZWQtZnVsbFwiXG4gICAgICA+XG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNSAxN2g1bC01LTVWOWE2IDYgMCAxMC0xMiAwdjNsLTUgNWg1bTcgMHYxYTMgMyAwIDExLTYgMHYtMW02IDBIOVwiIC8+XG4gICAgICAgIDwvc3ZnPlxuXG4gICAgICAgIHsvKiBDb21iaW5lZCBVbnJlYWQgQ291bnQgQmFkZ2UgKi99XG4gICAgICAgIHt0b3RhbFVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGJnLXJlZC01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBoLTUgdy01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICB7dG90YWxVbnJlYWRDb3VudCA+IDk5ID8gJzk5KycgOiB0b3RhbFVucmVhZENvdW50fVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgKX1cbiAgICAgIDwvYnV0dG9uPlxuXG4gICAgICB7LyogRHJvcGRvd24gKi99XG4gICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIG10LTIgdy05NiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHotNTBcIj5cbiAgICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5Ob3RpZmljYXRpb25zPC9oMz5cbiAgICAgICAgICAgICAge3RvdGFsVW5yZWFkQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVNYXJrQWxsQXNSZWFkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE1hcmsgYWxsIHJlYWRcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgcC0xXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3N5c3RlbScpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdzeXN0ZW0nXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtZ3JheS05MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBTeXN0ZW1cbiAgICAgICAgICAgICAgICB7c3lzdGVtVW5yZWFkQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIHB4LTIgcHktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgIHtzeXN0ZW1VbnJlYWRDb3VudH1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYigndHJhZGUnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAndHJhZGUnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtZ3JheS05MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBUcmFkZXNcbiAgICAgICAgICAgICAgICB7dHJhZGVVbnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiBiZy1yZWQtNTAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkLWZ1bGwgcHgtMiBweS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAge3RyYWRlVW5yZWFkQ291bnR9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFRhYi1zcGVjaWZpYyBNYXJrIGFzIFJlYWQgKi99XG4gICAgICAgICAgICB7Y3VycmVudFVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTWFya1RhYkFzUmVhZChhY3RpdmVUYWIpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE1hcmsge2FjdGl2ZVRhYn0gYXMgcmVhZFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTm90aWZpY2F0aW9ucyBMaXN0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LWgtOTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTYgdy02IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNTAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yXCI+TG9hZGluZyBub3RpZmljYXRpb25zLi4uPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiBjdXJyZW50Tm90aWZpY2F0aW9ucy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTIgdGV4dC1ncmF5LTMwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE1IDE3aDVsLTUtNVY5YTYgNiAwIDEwLTEyIDB2M2wtNSA1aDVtNyAwdjFhMyAzIDAgMTEtNiAwdi0xbTYgMEg5XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8cD5ObyB7YWN0aXZlVGFifSBub3RpZmljYXRpb25zIHlldDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3N5c3RlbSdcbiAgICAgICAgICAgICAgICAgICAgPyAnU3lzdGVtIGFubm91bmNlbWVudHMgYW5kIHVwZGF0ZXMgd2lsbCBhcHBlYXIgaGVyZSdcbiAgICAgICAgICAgICAgICAgICAgOiAnVHJhZGUtcmVsYXRlZCBub3RpZmljYXRpb25zIHdpbGwgYXBwZWFyIGhlcmUnXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIGN1cnJlbnROb3RpZmljYXRpb25zLm1hcCgobm90aWZpY2F0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtub3RpZmljYXRpb24uaWR9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOb3RpZmljYXRpb25DbGljayhub3RpZmljYXRpb24pfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTUwIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICFub3RpZmljYXRpb24uaXNSZWFkID8gJ2JnLWJsdWUtNTAnIDogJydcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAge2dldE5vdGlmaWNhdGlvbkljb24obm90aWZpY2F0aW9uLnR5cGUpfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAhbm90aWZpY2F0aW9uLmlzUmVhZCA/ICd0ZXh0LWdyYXktOTAwJyA6ICd0ZXh0LWdyYXktNzAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgeyFub3RpZmljYXRpb24uaXNSZWFkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBtbC0yIG10LTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTEgbGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZUFnbyhub3RpZmljYXRpb24uY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBTaG93IHVzZXIgcm9sZSBmb3IgdHJhZGUgbm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3RyYWRlJyAmJiBnZXRVc2VyUm9sZUluVHJhZGUobm90aWZpY2F0aW9uKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTAuNSByb3VuZGVkICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRVc2VyUm9sZUluVHJhZGUobm90aWZpY2F0aW9uKSA9PT0gJ2J1eWVyJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHVycGxlLTYwMCBiZy1wdXJwbGUtMTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRVc2VyUm9sZUluVHJhZGUobm90aWZpY2F0aW9uKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBTaG93IHRyYWRlIElEIGZvciB0cmFkZSBub3RpZmljYXRpb25zICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAndHJhZGUnICYmIG5vdGlmaWNhdGlvbi5kYXRhPy50cmFkZUlkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgYmctYmx1ZS0xMDAgcHgtMiBweS0wLjUgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVHJhZGUgI3tub3RpZmljYXRpb24uZGF0YS50cmFkZUlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgICAgeyhzeXN0ZW1Ob3RpZmljYXRpb25zLmxlbmd0aCA+IDAgfHwgdHJhZGVOb3RpZmljYXRpb25zLmxlbmd0aCA+IDApICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0SXNPcGVuKGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgIC8vIE5hdmlnYXRlIHRvIGZ1bGwgbm90aWZpY2F0aW9ucyBwYWdlIGlmIHlvdSBoYXZlIG9uZVxuICAgICAgICAgICAgICAgICAgLy8gd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL25vdGlmaWNhdGlvbnMnO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFZpZXcgYWxsIHthY3RpdmVUYWJ9IG5vdGlmaWNhdGlvbnNcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVByaXZ5IiwiZ2V0Tm90aWZpY2F0aW9ucyIsImdldFVucmVhZE5vdGlmaWNhdGlvbkNvdW50IiwibWFya05vdGlmaWNhdGlvbkFzUmVhZCIsIm1hcmtBbGxOb3RpZmljYXRpb25zQXNSZWFkIiwicmVsZWFzZVBlcmsiLCJyZWZ1bmRQZXJrIiwidXNlR2xvYmFsTW9kYWwiLCJ1c2VXYWxsZXQiLCJ1c2VFc2Nyb3dPcGVyYXRpb25zIiwiQ2hhdE1vZGFsIiwiUmVhbFRpbWVOb3RpZmljYXRpb25MaXN0ZW5lciIsIkFQSV9DT05GSUciLCJOb3RpZmljYXRpb25CZWxsIiwic3lzdGVtTm90aWZpY2F0aW9ucyIsInNldFN5c3RlbU5vdGlmaWNhdGlvbnMiLCJ0cmFkZU5vdGlmaWNhdGlvbnMiLCJzZXRUcmFkZU5vdGlmaWNhdGlvbnMiLCJzeXN0ZW1VbnJlYWRDb3VudCIsInNldFN5c3RlbVVucmVhZENvdW50IiwidHJhZGVVbnJlYWRDb3VudCIsInNldFRyYWRlVW5yZWFkQ291bnQiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImRyb3Bkb3duUmVmIiwidXNlciIsIm9wZW5Nb2RhbCIsImNsb3NlTW9kYWwiLCJzb2xhbmFXYWxsZXQiLCJpc0Nvbm5lY3RlZCIsImVzY3Jvd09wZXJhdGlvbnMiLCJsb2FkTm90aWZpY2F0aW9ucyIsIm5vdGlmaWNhdGlvbnNSZXNwb25zZSIsInVucmVhZFJlc3BvbnNlIiwiUHJvbWlzZSIsImFsbCIsInBhZ2UiLCJwYWdlU2l6ZSIsInN0YXR1cyIsImFsbE5vdGlmaWNhdGlvbnMiLCJkYXRhIiwibm90aWZpY2F0aW9ucyIsInN5c3RlbU5vdHMiLCJ0cmFkZU5vdHMiLCJmb3JFYWNoIiwibm90aWZpY2F0aW9uIiwiaXNUcmFkZU5vdGlmaWNhdGlvbiIsInB1c2giLCJjb25zb2xlIiwibG9nIiwidG90YWxOb3RpZmljYXRpb25zIiwibGVuZ3RoIiwic3lzdGVtVHlwZXMiLCJtYXAiLCJuIiwidHlwZSIsInRyYWRlVHlwZXMiLCJzeXN0ZW1VbnJlYWQiLCJmaWx0ZXIiLCJpc1JlYWQiLCJ0cmFkZVVucmVhZCIsImVycm9yIiwiaW5jbHVkZXMiLCJnZXRVc2VyUm9sZUluVHJhZGUiLCJidXllcklkIiwic2VsbGVySWQiLCJ1c2VySWQiLCJpZCIsInVzZXJJZE51bWJlciIsInBhcnNlSW50IiwiZ2VuZXJhdGVDaGF0Um9vbUlkIiwicGVya0lkIiwic2hvdWxkT3BlbkNoYXRNb2RhbCIsImNoYXRUeXBlcyIsImNoYXRSb29tSWQiLCJ0cmFkZUlkIiwiY3JlYXRlUmVsZWFzZUZ1bmN0aW9uIiwicmVzb2x2ZWRUcmFkZUlkIiwidHJhZGVJZFRvVXNlIiwiYWRkcmVzcyIsIndhbGxldCIsImNvbm5lY3RlZCIsInRyYWRlIiwidHJhZGVSZXNwb25zZSIsImZldGNoIiwiQkFTRV9VUkwiLCJoZWFkZXJzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIm9rIiwiRXJyb3IiLCJ0cmFkZURhdGEiLCJqc29uIiwiZXNjcm93SWQiLCJ0b2tlbkRldGFpbHMiLCJwZXJrVG9rZW5NaW50IiwidG8iLCJmcm9tIiwiZXhlY3V0ZVNlbGwiLCJ0b2tlbkFkZHJlc3MiLCJ0b1N0cmluZyIsInNlbGxlcldhbGxldCIsImFsZXJ0IiwiY3JlYXRlUmVmdW5kRnVuY3Rpb24iLCJleGVjdXRlUmVmdW5kIiwiYnV5ZXJXYWxsZXQiLCJjcmVhdGVBY2NlcHRGdW5jdGlvbiIsImNyZWF0ZUVzY3Jvd0FjY2VwdFRyYW5zYWN0aW9uIiwidHhJZCIsImFjY2VwdFJlc3BvbnNlIiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJhY2NlcHRlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwib3BlbkNoYXRNb2RhbCIsInVzZXJCb1N0ciIsInVzZXJCbyIsInBhcnNlIiwibXlVc2VySWQiLCJyZWNlaXZlcklkIiwic2VuZGVySWQiLCJub3RpZmljYXRpb25UeXBlIiwibm90aWZpY2F0aW9uRGF0YSIsImN1cnJlbnRVc2VyIiwiaGFzVHJhZGVJZCIsImhhc0NoYXRSb29tSWQiLCJoYXNQZXJrSWQiLCJhY3RpdmVUcmFkZSIsInVuZGVmaW5lZCIsImNoYXRSb29tUGFydHMiLCJzcGxpdCIsImdldFVzZXJUcmFkZXMiLCJ0cmFkZXNSZXNwb25zZSIsIlN0cmluZyIsImFjdGl2ZVRyYWRlcyIsIk51bWJlciIsInNvcnRlZFRyYWRlcyIsInNvcnQiLCJhIiwiYiIsImNyZWF0ZWRBdCIsImdldFRpbWUiLCJnZXRUcmFkZURldGFpbHMiLCJoYXNEYXRhIiwicGVya0RldGFpbHMiLCJkaXNwdXRlU3RhdHVzIiwiZGlzcHV0ZSIsImJ1eWVyIiwic2VsbGVyIiwibW9kYWxJZCIsImV4aXN0aW5nTW9kYWwiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImNvbXBvbmVudCIsImNyZWF0ZUVsZW1lbnQiLCJvbkNsb3NlIiwib25SZWxlYXNlIiwib25SZWZ1bmQiLCJvblJlcG9ydCIsIm9uQWNjZXB0IiwiY2xvc2VPbkJhY2tkcm9wQ2xpY2siLCJjbG9zZU9uRXNjYXBlIiwicHJldmVudENsb3NlIiwiekluZGV4IiwiYmFja2Ryb3BDbGFzc05hbWUiLCJtb2RhbENsYXNzTmFtZSIsImRpc2FibGVTY3JvbGwiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVOb3RpZmljYXRpb25DbGljayIsInNob3VsZE9wZW5DaGF0IiwiYWN0aW9uVXJsIiwicHJldiIsIk1hdGgiLCJtYXgiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJoYW5kbGVNYXJrQWxsQXNSZWFkIiwiaGFuZGxlTWFya1RhYkFzUmVhZCIsInRhYlR5cGUiLCJ1bnJlYWROb3RpZmljYXRpb25zIiwiZ2V0Tm90aWZpY2F0aW9uSWNvbiIsImRpdiIsImNsYXNzTmFtZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImZvcm1hdFRpbWVBZ28iLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIm5vdyIsImRpZmZJblNlY29uZHMiLCJmbG9vciIsInRvdGFsVW5yZWFkQ291bnQiLCJjdXJyZW50Tm90aWZpY2F0aW9ucyIsImN1cnJlbnRVbnJlYWRDb3VudCIsInJlZiIsIm9uTmV3Tm90aWZpY2F0aW9uIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iLCJoMyIsInAiLCJ0aXRsZSIsIm1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f30563d41805\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjMwNTYzZDQxODA1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});