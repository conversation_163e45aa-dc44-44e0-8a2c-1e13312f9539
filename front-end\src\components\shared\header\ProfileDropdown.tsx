import { motion, AnimatePresence } from 'framer-motion';
import { Settings, LogOut, User, Wallet, Copy } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

import { ROUTES } from '@/constants';
import { useTranslation } from '@/hooks/useTranslation';

interface ProfileDropdownProps {
  dropdownRef: React.RefObject<HTMLDivElement>;
  dropdownOpen: boolean;
  toggleDropdown: (e: React.MouseEvent) => void;
  authenticated: boolean;
  login: () => void;
  logout: () => void;
  handleNavigation: (route: string) => void;
  userBalance: string;
  walletAddress?: string;
  isAuthenticating?: boolean;
}

const ProfileDropdown: React.FC<ProfileDropdownProps> = ({
  dropdownRef,
  dropdownOpen,
  toggleDropdown,
  authenticated,
  login,
  logout,
  handleNavigation,
  userBalance,
  walletAddress,
  isAuthenticating = false,
}) => {
  const { t } = useTranslation();

  // Helper function to truncate wallet address
  const truncateAddress = (address: string | undefined) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // Copy wallet address to clipboard
  const copyAddress = async () => {
    if (walletAddress) {
      try {
        await navigator.clipboard.writeText(walletAddress);
        // You can add a toast notification here
      } catch (err) {
        console.error('Failed to copy address:', err);
      }
    }
  };

  // If not authenticated, show connect wallet button instead
  if (!authenticated) {
    return (
      <motion.button
        onClick={isAuthenticating ? undefined : login}
        disabled={isAuthenticating}
        className="hidden lg:flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white cursor-pointer font-medium hover:from-black hover:to-gray-800 transition-all duration-200 rounded-full shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
        whileHover={isAuthenticating ? {} : { scale: 1.02 }}
        whileTap={isAuthenticating ? {} : { scale: 0.98 }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      >
        {isAuthenticating ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Connecting...</span>
          </>
        ) : (
          <>
            <User size={18} />
            <span>Connect Wallet</span>
          </>
        )}
      </motion.button>
    );
  }

  return (
    <div className="hidden lg:block relative" ref={dropdownRef} onClick={toggleDropdown}>
      {/* Main Profile Container */}
      <div className="flex items-center gap-3 cursor-pointer bg-gradient-to-r from-[#F8F9FA] to-[#F5F5F5] h-[40px] lg:h-[56px] pr-4 lg:pr-6 pl-2 rounded-full transition-all duration-300 hover:from-gray-100 hover:to-gray-200 shadow-sm hover:shadow-md border border-gray-100">
        {/* Profile Avatar */}
        <motion.button
          className="flex items-center justify-center relative"
          onClick={() => handleNavigation(ROUTES.SETTINGS)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        >
          <div className="bg-gradient-to-br from-[#E0E0E0] to-[#C4C4C4] w-[36px] h-[36px] lg:w-[44px] lg:h-[44px] rounded-full flex items-center justify-center overflow-hidden ring-2 ring-white shadow-sm">
            <Image
              src="/icons/owl.png"
              alt={t('header.profile')}
              width={44}
              height={44}
              className="w-full h-full object-cover h-auto"
            />
          </div>
        </motion.button>

        {/* User Info Section */}
        <div className="flex flex-col justify-center min-w-0 flex-1">
          <span className="font-semibold text-sm lg:text-base text-gray-800 truncate">
            {userBalance}
          </span>
          {walletAddress && (
            <span className="text-xs text-gray-500 font-mono truncate">
              {truncateAddress(walletAddress)}
            </span>
          )}
        </div>

        {/* Dropdown Toggle */}
        <motion.div
          className={`cursor-pointer transition-all duration-300 p-1 rounded-full hover:bg-white/50 ${dropdownOpen ? 'rotate-180 bg-white/50' : ''
            }`}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        >
          <Image
            src="/icons/dropdown.svg"
            alt={t('header.dropdown')}
            width={16}
            height={16}
            className="lg:w-5 lg:h-5 opacity-70 h-auto"
          />
        </motion.div>
      </div>

      {/* Dropdown Content - Only for authenticated users */}
      <AnimatePresence>
        {dropdownOpen && (
          <motion.div
            className="absolute top-full mt-2 w-full bg-white rounded-xl shadow-xl z-50 border border-gray-100 overflow-hidden backdrop-blur-sm"
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 25,
              duration: 0.2,
            }}
          >
            <div className="py-2">
              {/* Wallet Address Section */}
              {walletAddress && (
                <div className="px-4 py-3 bg-gray-50 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Wallet size={16} className="text-gray-600" />
                      <span className="font-mono text-sm text-gray-700">
                        {truncateAddress(walletAddress)}
                      </span>
                    </div>
                    <motion.button
                      onClick={copyAddress}
                      className="p-1 hover:bg-gray-200 rounded transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Copy size={14} className="text-gray-500" />
                    </motion.button>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t('balance.availableBalance', { amount: parseFloat(userBalance).toLocaleString() })}
                  </div>
                </div>
              )}

              {/* Menu Items */}
              <motion.div
                className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors"
                onClick={() => handleNavigation(ROUTES.PROFILE)}
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <User size={18} className="text-gray-600" />
                <span className="font-medium">{t('navigation.profile')}</span>
              </motion.div>

              <motion.div
                className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors"
                onClick={() => handleNavigation(ROUTES.SETTINGS)}
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <Settings size={18} className="text-gray-600" />
                <span className="font-medium">{t('navigation.settings')}</span>
              </motion.div>

              <motion.div
                className="px-4 py-3 hover:bg-red-50 cursor-pointer flex items-center gap-3 text-red-600 transition-colors border-t border-gray-100"
                onClick={logout}
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <LogOut size={18} />
                <span className="font-medium">{t('dashboard.disconnect')}</span>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
};

export default ProfileDropdown;
