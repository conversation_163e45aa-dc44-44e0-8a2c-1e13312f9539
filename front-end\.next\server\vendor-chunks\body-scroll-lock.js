"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/body-scroll-lock";
exports.ids = ["vendor-chunks/body-scroll-lock"];
exports.modules = {

/***/ "(ssr)/./node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js":
/*!*****************************************************************!*\
  !*** ./node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllBodyScrollLocks: () => (/* binding */ clearAllBodyScrollLocks),\n/* harmony export */   disableBodyScroll: () => (/* binding */ disableBodyScroll),\n/* harmony export */   enableBodyScroll: () => (/* binding */ enableBodyScroll)\n/* harmony export */ });\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\n// Older browsers don't support event options, feature detect it.\n\n// Adopted and modified solution from Bohdan Didukh (2017)\n// https://stackoverflow.com/questions/41594997/ios-10-safari-prevent-scrolling-behind-a-fixed-overlay-and-maintain-scroll-posi\n\nvar hasPassiveEvents = false;\nif (typeof window !== 'undefined') {\n  var passiveTestOptions = {\n    get passive() {\n      hasPassiveEvents = true;\n      return undefined;\n    }\n  };\n  window.addEventListener('testPassive', null, passiveTestOptions);\n  window.removeEventListener('testPassive', null, passiveTestOptions);\n}\n\nvar isIosDevice = typeof window !== 'undefined' && window.navigator && window.navigator.platform && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === 'MacIntel' && window.navigator.maxTouchPoints > 1);\n\n\nvar locks = [];\nvar documentListenerAdded = false;\nvar initialClientY = -1;\nvar previousBodyOverflowSetting = void 0;\nvar previousBodyPosition = void 0;\nvar previousBodyPaddingRight = void 0;\n\n// returns true if `el` should be allowed to receive touchmove events.\nvar allowTouchMove = function allowTouchMove(el) {\n  return locks.some(function (lock) {\n    if (lock.options.allowTouchMove && lock.options.allowTouchMove(el)) {\n      return true;\n    }\n\n    return false;\n  });\n};\n\nvar preventDefault = function preventDefault(rawEvent) {\n  var e = rawEvent || window.event;\n\n  // For the case whereby consumers adds a touchmove event listener to document.\n  // Recall that we do document.addEventListener('touchmove', preventDefault, { passive: false })\n  // in disableBodyScroll - so if we provide this opportunity to allowTouchMove, then\n  // the touchmove event on document will break.\n  if (allowTouchMove(e.target)) {\n    return true;\n  }\n\n  // Do not prevent if the event has more than one touch (usually meaning this is a multi touch gesture like pinch to zoom).\n  if (e.touches.length > 1) return true;\n\n  if (e.preventDefault) e.preventDefault();\n\n  return false;\n};\n\nvar setOverflowHidden = function setOverflowHidden(options) {\n  // If previousBodyPaddingRight is already set, don't set it again.\n  if (previousBodyPaddingRight === undefined) {\n    var _reserveScrollBarGap = !!options && options.reserveScrollBarGap === true;\n    var scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n\n    if (_reserveScrollBarGap && scrollBarGap > 0) {\n      var computedBodyPaddingRight = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'), 10);\n      previousBodyPaddingRight = document.body.style.paddingRight;\n      document.body.style.paddingRight = computedBodyPaddingRight + scrollBarGap + 'px';\n    }\n  }\n\n  // If previousBodyOverflowSetting is already set, don't set it again.\n  if (previousBodyOverflowSetting === undefined) {\n    previousBodyOverflowSetting = document.body.style.overflow;\n    document.body.style.overflow = 'hidden';\n  }\n};\n\nvar restoreOverflowSetting = function restoreOverflowSetting() {\n  if (previousBodyPaddingRight !== undefined) {\n    document.body.style.paddingRight = previousBodyPaddingRight;\n\n    // Restore previousBodyPaddingRight to undefined so setOverflowHidden knows it\n    // can be set again.\n    previousBodyPaddingRight = undefined;\n  }\n\n  if (previousBodyOverflowSetting !== undefined) {\n    document.body.style.overflow = previousBodyOverflowSetting;\n\n    // Restore previousBodyOverflowSetting to undefined\n    // so setOverflowHidden knows it can be set again.\n    previousBodyOverflowSetting = undefined;\n  }\n};\n\nvar setPositionFixed = function setPositionFixed() {\n  return window.requestAnimationFrame(function () {\n    // If previousBodyPosition is already set, don't set it again.\n    if (previousBodyPosition === undefined) {\n      previousBodyPosition = {\n        position: document.body.style.position,\n        top: document.body.style.top,\n        left: document.body.style.left\n      };\n\n      // Update the dom inside an animation frame \n      var _window = window,\n          scrollY = _window.scrollY,\n          scrollX = _window.scrollX,\n          innerHeight = _window.innerHeight;\n\n      document.body.style.position = 'fixed';\n      document.body.style.top = -scrollY;\n      document.body.style.left = -scrollX;\n\n      setTimeout(function () {\n        return window.requestAnimationFrame(function () {\n          // Attempt to check if the bottom bar appeared due to the position change\n          var bottomBarHeight = innerHeight - window.innerHeight;\n          if (bottomBarHeight && scrollY >= innerHeight) {\n            // Move the content further up so that the bottom bar doesn't hide it\n            document.body.style.top = -(scrollY + bottomBarHeight);\n          }\n        });\n      }, 300);\n    }\n  });\n};\n\nvar restorePositionSetting = function restorePositionSetting() {\n  if (previousBodyPosition !== undefined) {\n    // Convert the position from \"px\" to Int\n    var y = -parseInt(document.body.style.top, 10);\n    var x = -parseInt(document.body.style.left, 10);\n\n    // Restore styles\n    document.body.style.position = previousBodyPosition.position;\n    document.body.style.top = previousBodyPosition.top;\n    document.body.style.left = previousBodyPosition.left;\n\n    // Restore scroll\n    window.scrollTo(x, y);\n\n    previousBodyPosition = undefined;\n  }\n};\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight#Problems_and_solutions\nvar isTargetElementTotallyScrolled = function isTargetElementTotallyScrolled(targetElement) {\n  return targetElement ? targetElement.scrollHeight - targetElement.scrollTop <= targetElement.clientHeight : false;\n};\n\nvar handleScroll = function handleScroll(event, targetElement) {\n  var clientY = event.targetTouches[0].clientY - initialClientY;\n\n  if (allowTouchMove(event.target)) {\n    return false;\n  }\n\n  if (targetElement && targetElement.scrollTop === 0 && clientY > 0) {\n    // element is at the top of its scroll.\n    return preventDefault(event);\n  }\n\n  if (isTargetElementTotallyScrolled(targetElement) && clientY < 0) {\n    // element is at the bottom of its scroll.\n    return preventDefault(event);\n  }\n\n  event.stopPropagation();\n  return true;\n};\n\nvar disableBodyScroll = function disableBodyScroll(targetElement, options) {\n  // targetElement must be provided\n  if (!targetElement) {\n    // eslint-disable-next-line no-console\n    console.error('disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.');\n    return;\n  }\n\n  // disableBodyScroll must not have been called on this targetElement before\n  if (locks.some(function (lock) {\n    return lock.targetElement === targetElement;\n  })) {\n    return;\n  }\n\n  var lock = {\n    targetElement: targetElement,\n    options: options || {}\n  };\n\n  locks = [].concat(_toConsumableArray(locks), [lock]);\n\n  if (isIosDevice) {\n    setPositionFixed();\n  } else {\n    setOverflowHidden(options);\n  }\n\n  if (isIosDevice) {\n    targetElement.ontouchstart = function (event) {\n      if (event.targetTouches.length === 1) {\n        // detect single touch.\n        initialClientY = event.targetTouches[0].clientY;\n      }\n    };\n    targetElement.ontouchmove = function (event) {\n      if (event.targetTouches.length === 1) {\n        // detect single touch.\n        handleScroll(event, targetElement);\n      }\n    };\n\n    if (!documentListenerAdded) {\n      document.addEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = true;\n    }\n  }\n};\n\nvar clearAllBodyScrollLocks = function clearAllBodyScrollLocks() {\n  if (isIosDevice) {\n    // Clear all locks ontouchstart/ontouchmove handlers, and the references.\n    locks.forEach(function (lock) {\n      lock.targetElement.ontouchstart = null;\n      lock.targetElement.ontouchmove = null;\n    });\n\n    if (documentListenerAdded) {\n      document.removeEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = false;\n    }\n\n    // Reset initial clientY.\n    initialClientY = -1;\n  }\n\n  if (isIosDevice) {\n    restorePositionSetting();\n  } else {\n    restoreOverflowSetting();\n  }\n\n  locks = [];\n};\n\nvar enableBodyScroll = function enableBodyScroll(targetElement) {\n  if (!targetElement) {\n    // eslint-disable-next-line no-console\n    console.error('enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.');\n    return;\n  }\n\n  locks = locks.filter(function (lock) {\n    return lock.targetElement !== targetElement;\n  });\n\n  if (isIosDevice) {\n    targetElement.ontouchstart = null;\n    targetElement.ontouchmove = null;\n\n    if (documentListenerAdded && locks.length === 0) {\n      document.removeEventListener('touchmove', preventDefault, hasPassiveEvents ? { passive: false } : undefined);\n      documentListenerAdded = false;\n    }\n  }\n\n  if (isIosDevice) {\n    restorePositionSetting();\n  } else {\n    restoreOverflowSetting();\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js\n");

/***/ })

};
;