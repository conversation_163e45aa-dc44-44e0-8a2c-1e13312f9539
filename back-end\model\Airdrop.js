const { Model, DataTypes } = require("sequelize");

class Airdrop extends Model {
    static initModel(sequelize) {
        return Airdrop.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                UserID: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                TokenID: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                totalTokens: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                tokensPerLink: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                remainingToken: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                isPrivate: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                link: {
                    type: DataTypes.STRING,
                    defaultValue: "",
                },
                tokenAddress: {
                    type: DataTypes.STRING,
                    defaultValue: "",
                },
                signature: {
                    type: DataTypes.STRING,
                    defaultValue: "",
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'airdrops',
                timestamps: true,
            }
        );
    }
}

module.exports = Airdrop;
