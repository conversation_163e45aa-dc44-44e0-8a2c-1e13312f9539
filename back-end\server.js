/** @format */

// const cluster = require("cluster");
require("ts-node/register");

// if (cluster.isMaster) {
  const express = require("express");
  const app = express();
  const cors = require("cors");
  
  const http = require("http");
  const morgan = require("morgan");
  const moment = require("moment-timezone");
  const config = require("./config/security");
  const { initializeDatabase, initializeContract, updateGlobalConfig, cleanupExistingAccount } = require("./setupDatabase");
  const errorHandler = require("./middleware/errorHandler");
  const bodyParser = require('body-parser')
  const colors = require('colors')
  const server = http.createServer(app);
  const { Server } = require("socket.io");
  const dataContext = require('./db');
  const { Op } = require('sequelize');
  const { PublicKey } = require('@solana/web3.js');
  require('ts-node/register');
  // const { withdrawUnlocked } = require('./utilities/funhi-program/withdrawUnlocked');
  app.use(express.static(__dirname + "/static", { dotfiles: "allow" }));
  app.use(bodyParser.json())
  
  // Setup CORS using config
  const allowedOrigins = config.CORS_ORIGINS.map(origin => origin.trim());
  const corsOptions = {
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin)) callback(null, true);
      else callback(new Error(`CORS blocked: ${origin}`));
    },
    credentials: true,
  };
  app.use(cors(corsOptions));
  
  app.use(morgan(function (tokens, req, res) {
    return colors.yellow(moment().tz("America/Los_Angeles").format('ddd, DD MMM YYYY HH:mm:ss.SSS Z z'))
      + ' ' + colors.blue(tokens['remote-addr'](req, res))
      + ' ' + colors.cyan(tokens.method(req, res))
      + ' ' + colors.green(tokens.url(req, res))
      + ' ' + colors.magenta(tokens.status(req, res))
      + ' ' + colors.red(tokens['response-time'](req, res))
  }))
  
  // --------- Api Routes ------------------- //
  
  const userRouter = require("./apis/user");
  const tokenRouter = require("./apis/token");
  const perkRouter = require("./apis/perk");
  const messageRouter = require("./apis/message");
  const disputeRouter = require("./apis/dispute");
  const moderatorRouter = require("./apis/moderator");
  const notificationRouter = require("./apis/notification");
  // const contributionRouter = require('./apis/contribution')
  // const campaignRouter = require('./apis/campaign');
  // const referralRouter = require('./apis/refer');
  // const utilsRouter = require('./apis/utils');
  
  
  app.use("/users", userRouter);
  app.use('/tokens', tokenRouter)
  app.use('/perks', perkRouter)
  app.use('/messages', messageRouter)
  app.use('/disputes', disputeRouter)
  app.use('/moderators', moderatorRouter)
  app.use('/notifications', notificationRouter)
  app.use(errorHandler);
  
  // }
  
  
  
  const io = new Server(server, {
    cors: {
      origin: "*", // Adjust for production!
      methods: ["GET", "POST"]
    }
  });
  
  io.on("connection", (socket) => {
  let userWallet = null;

  // Authentication
  socket.on("authenticate", async ({ wallet }) => {
    if (!wallet) {
      socket.emit("error", { message: "Wallet address required" });
      socket.disconnect();
      return;
    }
    userWallet = wallet;
    socket.emit("authenticated");
  });

  // Room join with access control
  socket.on("joinRoom", async ({ tradeId, chatRoomId }) => {
    if (!userWallet) {
      socket.emit("error", { message: "Not authenticated" });
      return;
    }
    if (chatRoomId) {
      // Join by chatRoomId
      const chatRoom = await dataContext.ChatRoom.findOne({ where: { chatRoomId } });
      if (!chatRoom) {
        socket.emit("error", { message: "Chat room not found" });
        return;
      }
      // Find user by wallet
      const user = await dataContext.User.findOne({
        where: { privywallet: userWallet },
      });
      if (!user) {
        socket.emit("error", { message: "User not found for this wallet" });
        return;
      }
      // Check if user is buyer or seller in this chat room
      if (chatRoom.buyerId !== user.id && chatRoom.sellerId !== user.id) {
        socket.emit("error", { message: "Not authorized for this chat room" });
        return;
      }
      socket.join(`chatRoom-${chatRoom.id}`);
      console.log('🔗 [Socket] User joined room:', `chatRoom-${chatRoom.id}`, 'for chatRoomId:', chatRoomId);
      socket.emit("joinedRoom", { chatRoomId });
      return;
    }
    // Old tradeId logic
    if (tradeId) {
      const trade = await dataContext.TokenPurchased.findOne({ where: { id: tradeId } });
      if (!trade) {
        socket.emit("error", { message: "Trade not found" });
        return;
      }
      if (trade.from !== userWallet && trade.to !== userWallet) {
        socket.emit("error", { message: "Not authorized for this trade" });
        return;
      }
      socket.join(`trade-${tradeId}`);
      socket.emit("joinedRoom", { tradeId });
      return;
    }
    socket.emit("error", { message: "No room identifier provided" });
  });



  // Handle messages
  socket.on("message", async (data) => {
    console.log('📨 [Socket] Received message:', data, 'from wallet:', userWallet);

    if (!userWallet) return socket.emit("error", "Not authenticated");

    try {
      const user = await dataContext.User.findOne({
        where: { privywallet: userWallet }
      });
      if (!user) {
        console.error('❌ [Socket] User not found for wallet:', userWallet);
        return socket.emit("error", "User not found");
      }

      // Find the chat room to get the correct room ID for broadcasting
      const chatRoom = await dataContext.ChatRoom.findOne({
        where: { chatRoomId: data.chatRoomId }
      });
      if (!chatRoom) {
        console.error('❌ [Socket] Chat room not found:', data.chatRoomId);
        return socket.emit("error", "Chat room not found");
      }

      console.log('✅ [Socket] Found chat room:', { id: chatRoom.id, chatRoomId: chatRoom.chatRoomId });

      const message = await dataContext.Message.create({
        chatRoomId: chatRoom.id, // Use the database ID, not the chatRoomId string
        senderId: user.id,
        receiverId: data.receiverId,
        message: data.message,
        type: 'user'
      });

      console.log('💾 [Socket] Message saved to database:', message.id);

      // Prepare message object to broadcast (include all necessary fields)
      const messageToSend = {
        id: message.id,
        chatRoomId: data.chatRoomId, // Use the original chatRoomId for frontend
        senderId: user.id,
        receiverId: data.receiverId,
        message: data.message,
        type: 'user',
        createdAt: message.createdAt,
      };

      // Create real-time chat message notification for the receiver
      try {
        const receiver = await dataContext.User.findByPk(data.receiverId);
        const sender = await dataContext.User.findByPk(user.id);

        // Find the active trade for this chat room to include tradeId
        let tradeId = null;
        try {
          const activeTrade = await dataContext.TokenPurchased.findOne({
            where: {
              perkId: chatRoom.perkId,
              [dataContext.Sequelize.Op.or]: [
                { userId: chatRoom.buyerId, to: chatRoom.sellerId },
                { userId: chatRoom.sellerId, to: chatRoom.buyerId }
              ],
              status: {
                [dataContext.Sequelize.Op.in]: ['pending_acceptance', 'escrowed', 'completed', 'released']
              }
            },
            order: [['createdAt', 'DESC']]
          });

          if (activeTrade) {
            tradeId = activeTrade.id;
            console.log('✅ [Socket] Found active trade for chat message notification:', tradeId);
          }
        } catch (tradeError) {
          console.log('⚠️ [Socket] Could not find active trade for chat room:', tradeError.message);
        }

        if (receiver && sender) {
          await dataContext.Notification.create({
            userId: data.receiverId,
            type: 'chat_message',
            title: 'New Message',
            message: `${sender.username || 'Someone'} sent you a message: "${data.message.substring(0, 50)}${data.message.length > 50 ? '...' : ''}"`,
            data: {
              senderId: user.id,
              receiverId: data.receiverId,
              chatRoomId: data.chatRoomId,
              messageId: message.id,
              senderName: sender.username,
              tradeId: tradeId, // Include tradeId for complete chat context
              perkId: chatRoom.perkId,
              buyerId: chatRoom.buyerId,
              sellerId: chatRoom.sellerId
            },
            priority: 'medium',
            actionUrl: null // Will open chat modal
          });

          console.log('📬 [Socket] Created chat message notification for user:', data.receiverId, 'with tradeId:', tradeId);
        }
      } catch (error) {
        console.error('❌ [Socket] Failed to create chat message notification:', error);
      }

      // Broadcast to room using the correct room ID (primary key)
      console.log('📤 [Socket] Broadcasting message to room:', `chatRoom-${chatRoom.id}`, messageToSend);
      io.to(`chatRoom-${chatRoom.id}`).emit("message", messageToSend);

      // Also emit a specific notification event for real-time notification updates
      io.to(`chatRoom-${chatRoom.id}`).emit("newNotification", {
        type: 'chat_message',
        receiverId: data.receiverId,
        senderId: user.id,
        chatRoomId: data.chatRoomId,
        message: data.message
      });

      console.log('✅ [Socket] Message broadcast and notification sent');
    } catch (error) {
      console.error('❌ [Socket] Error handling message:', error);
      socket.emit("error", "Failed to send message");
    }
  });

  // Handle escrow accept request (seller)
  socket.on("acceptEscrow", async ({ tradeId, txId }) => {
    if (!userWallet) return socket.emit("error", "Not authenticated");

    try {
      const response = await axios.put(
        `${config.API_URL}/perks/trades/${tradeId}/accept`,
        { txId, acceptedAt: new Date().toISOString() },
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        }
      );

      if (response.status === 200) {
        // Emit escrow acceptance events
        io.to(`trade-${tradeId}`).emit("escrowAccepted", { tradeId, txId });
        io.to(`trade-${tradeId}`).emit("tradeStatus", { tradeId, status: "escrowed" });

        // Also emit to chat room if it exists
        const trade = response.data.data;
        if (trade) {
          // Find the trade to get chat room info
          const tradeRecord = await dataContext.TokenPurchased.findByPk(tradeId, {
            include: [{ model: dataContext.Perk, as: 'perk' }]
          });

          if (tradeRecord) {
            const chatRoomId = `${tradeRecord.userId}-${tradeRecord.perk.userId}-${tradeRecord.perkId}`;
            io.to(`chatRoom-${chatRoomId}`).emit("tradeStatus", { tradeId, status: "escrowed" });
            io.to(`chatRoom-${chatRoomId}`).emit("escrowAccepted", { tradeId, txId });
          }
        }

        console.log(`📡 [Socket] Broadcasted escrow acceptance for tradeId: ${tradeId}`);
      }
    } catch (error) {
      console.error('Accept escrow error:', error);
      socket.emit("error", "Accept failed");
    }
  });

  // Handle perk release (seller)
  socket.on("releasePerk", async ({ tradeId }) => {
    if (!userWallet) return socket.emit("error", "Not authenticated");
    
    try {
      const trade = await dataContext.TokenPurchased.findByPk(tradeId);
      if (!trade || trade.to !== userWallet) {
        return socket.emit("error", "Invalid trade");
      }

      // Call release endpoint
      const response = await axios.post(`${config.API_URL}/perks/releasePerk`, {
        tradeId,
        sellerWallet: userWallet
      });

      if (response.status === 200) {
        // Emit both legacy and new events for compatibility
        io.to(`trade-${tradeId}`).emit("perkReleased", { tradeId });
        io.to(`trade-${tradeId}`).emit("tradeStatus", { tradeId, status: "completed" });

        // Also emit to chat room if it exists
        const chatRoomId = `${trade.from}-${trade.to}-${trade.perkId}`;
        io.to(`chatRoom-${chatRoomId}`).emit("tradeStatus", { tradeId, status: "completed" });

        console.log(`📡 [Socket] Broadcasted trade completion for tradeId: ${tradeId}`);
      }
    } catch (error) {
      socket.emit("error", "Release failed");
    }
  });

  // Handle refund request (buyer)
  socket.on("requestRefund", async ({ tradeId }) => {
    if (!userWallet) return socket.emit("error", "Not authenticated");
    
    try {
      const trade = await dataContext.TokenPurchased.findByPk(tradeId);
      if (!trade || trade.from !== userWallet) {
        return socket.emit("error", "Invalid trade");
      }

      // Call refund endpoint
      const response = await axios.post(`${config.API_URL}/perks/refundPerk`, {
        tradeId,
        buyerWallet: userWallet
      });

      if (response.status === 200) {
        // Emit both legacy and new events for compatibility
        io.to(`trade-${tradeId}`).emit("refundProcessed", { tradeId });
        io.to(`trade-${tradeId}`).emit("tradeStatus", { tradeId, status: "refunded" });

        // Also emit to chat room if it exists
        const chatRoomId = `${trade.from}-${trade.to}-${trade.perkId}`;
        io.to(`chatRoom-${chatRoomId}`).emit("tradeStatus", { tradeId, status: "refunded" });

        console.log(`📡 [Socket] Broadcasted trade refund for tradeId: ${tradeId}`);
      }
    } catch (error) {
      socket.emit("error", "Refund failed");
    }
  });

  // Disconnect handler
  socket.on("disconnect", () => {
    console.log(`User disconnected: ${socket.id}`);
  });
});

// Global function to broadcast system notifications to all connected users
global.broadcastSystemNotification = (notificationData) => {
  console.log('📢 [Socket] Broadcasting system notification to all users:', notificationData);
  io.emit('systemNotification', notificationData);
};


  startServer = async () => {
    try {
      console.log("🔍 Validating environment and initializing database...");
      await initializeDatabase();
      await cleanupExistingAccount();
      await initializeContract();
      await updateGlobalConfig();
      server.listen(config.PORT, function () {
        const address = this.address();
        const port = config.PORT;
        const protocol = port == 443 ? 'https' : 'http';
        const host = address.address === '::' ? 'localhost' : address.address;
        console.log(`🚀 Server is running at: ${protocol}://${host}:${port}`);
      });
    } catch (err) {
      console.error("❌ Startup failed:", err);
      process.exit(1);
    }
  };
  
  startServer();
  // module.exports = { startServer };
