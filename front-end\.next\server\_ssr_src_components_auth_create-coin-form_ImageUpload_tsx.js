"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_auth_create-coin-form_ImageUpload_tsx";
exports.ids = ["_ssr_src_components_auth_create-coin-form_ImageUpload_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/auth/create-coin-form/ImageUpload.tsx":
/*!**************************************************************!*\
  !*** ./src/components/auth/create-coin-form/ImageUpload.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/components/auth/create-coin-form/constants.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ImageUpload = ({ picture, previewUrl, error, isSubmitting, onFileChange, onRemoveImage })=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const inputClass = \"self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200\";\n    const inputErrorClass = \"self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: `${error ? inputErrorClass : inputClass} relative`,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_2__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center gap-3\",\n                        children: previewUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: previewUrl,\n                                    alt: \"Preview\",\n                                    width: 40,\n                                    height: 40,\n                                    className: \"w-10 h-10 rounded object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700 flex-1 truncate\",\n                                    children: picture?.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    type: \"button\",\n                                    onClick: onRemoveImage,\n                                    className: \"text-red-500 hover:text-red-700 p-1\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 text-gray-400 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('createCoinForm.uploadPicture')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    !previewUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.label, {\n                        className: \"px-4 py-2 bg-[#F58A38] text-white rounded cursor-pointer hover:bg-[#e55a00] transition-colors flex items-center gap-2\",\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined),\n                            t('createCoinForm.upload'),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                className: \"hidden\",\n                                onChange: onFileChange,\n                                accept: \"image/*\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/create-coin-form/ImageUpload.tsx\n");

/***/ })

};
;