import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import { ROUTES } from '@/constants';

const Logo: React.FC = () => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <Link href={ROUTES.HOME} className="flex-shrink-0">
        <Image
          src="/images/o-back.svg"
          alt="FunHi"
          width={113}
          height={26}
          className="w-[68px] lg:w-[113px] h-auto"
        />
      </Link>
    </motion.div>
  );
};

export default Logo; 