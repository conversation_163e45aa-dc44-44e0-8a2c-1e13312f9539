'use client';

import { motion, AnimatePresence } from 'framer-motion';
import React, { ReactNode, useRef, useEffect, useState, useCallback } from 'react';

import { staggerContainer } from '@/lib/animations';

// TypeScript interfaces
interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  'data-testid'?: string;
  'aria-label'?: string;
}

interface AnimatedWrapperProps extends BaseComponentProps {
  delay?: number;
  animation?: 'fadeInUp' | 'fadeIn' | 'slideIn' | 'scaleIn' | 'slideInFromLeft' | 'slideInFromRight';
  threshold?: number;
  triggerOnce?: boolean;
  disabled?: boolean;
}

interface StaggeredGridProps extends BaseComponentProps {
  staggerDelay?: number;
  threshold?: number;
  triggerOnce?: boolean;
}

interface StaggeredItemProps extends BaseComponentProps {
  index?: number;
}

interface PageTransitionProps extends BaseComponentProps {
  duration?: number;
}

// Custom hook for intersection observer
const useIntersectionObserver = (
  threshold = 0.1,
  triggerOnce = true
) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const ref = useRef<HTMLElement>(null);

  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    if (entry.isIntersecting) {
      setIsVisible(true);
      if (triggerOnce) {
        setHasTriggered(true);
      }
    } else if (!triggerOnce) {
      setIsVisible(false);
    }
  }, [triggerOnce]);

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      threshold,
      rootMargin: '50px',
    });

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [handleIntersection, threshold]);

  return { ref, isVisible: isVisible || hasTriggered };
};

// Animation variants
const getAnimationVariants = (animation: string, delay: number = 0) => {
  const baseTransition = { delay, duration: 0.6, ease: 'easeOut' };

  switch (animation) {
    case 'fadeInUp':
      return {
        initial: { opacity: 0, y: 20 },
        animate: { 
          opacity: 1, 
          y: 0, 
          transition: { delay, duration: 0.6, ease: 'easeOut' } 
        },
        exit: { opacity: 0, y: -20 },
      };
    case 'fadeIn':
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1, transition: baseTransition },
      };
    case 'slideIn':
      return {
        initial: { opacity: 0, x: -20 },
        animate: { opacity: 1, x: 0, transition: baseTransition },
      };
    case 'scaleIn':
      return {
        initial: { opacity: 0, scale: 0.95 },
        animate: { opacity: 1, scale: 1, transition: baseTransition },
      };
    case 'slideInFromLeft':
      return {
        initial: { opacity: 0, x: -50 },
        animate: { opacity: 1, x: 0, transition: baseTransition },
      };
    case 'slideInFromRight':
      return {
        initial: { opacity: 0, x: 50 },
        animate: { opacity: 1, x: 0, transition: baseTransition },
      };
    default:
      return {
        initial: { opacity: 0, y: 20 },
        animate: { 
          opacity: 1, 
          y: 0, 
          transition: { delay, duration: 0.6, ease: 'easeOut' } 
        },
        exit: { opacity: 0, y: -20 },
      };
  }
};

export const AnimatedWrapper: React.FC<AnimatedWrapperProps> = ({
  children,
  className = '',
  delay = 0,
  animation = 'fadeInUp',
  threshold = 0.1,
  triggerOnce = true,
  disabled = false,
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
}) => {
  const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);

  if (disabled) {
    return (
      <div className={className} data-testid={dataTestId} aria-label={ariaLabel}>
        {children}
      </div>
    );
  }

  return (
    <motion.div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={className}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      {...getAnimationVariants(animation, delay)}
      initial="initial"
      animate={isVisible ? 'animate' : 'initial'}
    >
      {children}
    </motion.div>
  );
};

export const StaggeredGrid: React.FC<StaggeredGridProps> = ({
  children,
  className = '',
  staggerDelay = 0.1,
  threshold = 0.1,
  triggerOnce = true,
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
}) => {
  const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);

  return (
    <motion.div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={className}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      variants={{
        ...staggerContainer,
        animate: {
          transition: {
            staggerChildren: staggerDelay,
            delayChildren: 0.1,
          },
        },
      }}
      initial="initial"
      animate={isVisible ? 'animate' : 'initial'}
    >
      {children}
    </motion.div>
  );
};

export const StaggeredItem: React.FC<StaggeredItemProps> = ({
  children,
  className = '',
  index = 0,
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
}) => {
  return (
    <motion.div
      className={className}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: 1,
        y: 0,
        transition: {
          delay: index * 0.1,
          duration: 0.4,
          ease: [0.4, 0.0, 0.2, 1],
        },
      }}
      exit={{
        opacity: 0,
        y: -20,
        transition: {
          duration: 0.2,
          ease: [0.4, 0.0, 1, 1],
        },
      }}
    >
      {children}
    </motion.div>
  );
};

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  duration = 0.4,
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
}) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        className={className}
        data-testid={dataTestId}
        aria-label={ariaLabel}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration, ease: 'easeOut' }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};
