class Timer {
    callback;
    interval;
    timerId;
    isRunning;

    constructor(callback, intervalInSeconds) {
        this.callback = callback;
        this.interval = intervalInSeconds;
        this.timerId = null;
        this.isRunning = false;
    }

    async start() {
        if (this.isRunning) return;

        this.isRunning = true;
        await this.callback();
        this.nextRun();
    }

    stop() {
        if (this.timerId) {
            clearTimeout(this.timerId);
            this.timerId = null;
        }
        this.isRunning = false;
    }

    nextRun() {
        this.timerId = setTimeout(async () => {
            try {
                await this.callback();
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Timer error:', error);
            } finally {
                if (this.isRunning) {
                    this.nextRun();
                }
            }
        }, this.interval);
    }
}
module.exports = Timer;