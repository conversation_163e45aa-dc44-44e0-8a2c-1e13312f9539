'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import React, { useEffect, useCallback, useMemo } from 'react';

import Card from '@/components/shared/card';
import CardSkeleton from '@/components/shared/card-skeleton';
import { useAppContext } from '@/contexts/AppContext';
import { useFilters, FilterState } from '@/hooks/useFilters';
import { usePerks, Coin } from '@/hooks/usePerks';
import { staggerContainer } from '@/lib/animations';
import { fetchNotifications } from '@/lib/api';
import { useRouter } from 'next/navigation';
import { initiateChatRoom } from '@/axios/requests';
import { useTranslation } from '@/hooks/useTranslation';
import dynamic from 'next/dynamic';

const Filters = dynamic(() => import('@/components/shared/filters').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const StaggeredGrid = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredGrid })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const StaggeredItem = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredItem })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const PageTransition = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.PageTransition })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const ErrorState = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.ErrorState })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const SearchEmptyState = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.SearchEmptyState })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const NoPerksEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoPerksEmpty })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

export default function ParksShop() {
  const { filters, setCategory, setSearch, setSortBy, setFilterVerified } =
    useFilters();

  const { setNotificationCount, state } = useAppContext();

  const memoizedFilters = useMemo(
    () => filters,
    [filters.category, filters.search, filters.sortBy, filters.filterVerified]
  );

  const { perks, loading, error } = usePerks(memoizedFilters);

  const { t } = useTranslation();

  useEffect(() => {
    let isMounted = true;

    const loadNotifications = async () => {
      try {
        const notificationData = await fetchNotifications();
        if (isMounted) {
          setNotificationCount(notificationData.length);
        }
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        // Silently fail for notifications as they are not critical
      }
    };

    loadNotifications();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleFilterChange = useCallback(
    (newFilters: FilterState) => {
      if (newFilters.category !== filters.category) {
        setCategory(newFilters.category);
      }
      if (newFilters.search !== filters.search) {
        setSearch(newFilters.search);
      }
      if (newFilters.sortBy !== filters.sortBy) {
        setSortBy(newFilters.sortBy);
      }
      if (newFilters.filterVerified !== filters.filterVerified) {
        setFilterVerified(newFilters.filterVerified);
      }
    },
    [
      filters.category,
      filters.search,
      filters.sortBy,
      filters.filterVerified,
      setCategory,
      setSearch,
      setSortBy,
      setFilterVerified,
    ]
  );

  const handleBuyCoin = useCallback((coin: Coin) => {
    console.log("click on buyy");
  }, []);

  const handleRetry = useCallback(() => {
    window.location.reload();
  }, []);

  const hasSearchTerm = useMemo(
    () => filters.search.trim().length > 0,
    [filters.search]
  );
  const showEmptyState = useMemo(
    () => !loading && perks.length === 0,
    [loading, perks.length]
  );
  const showErrorState = useMemo(() => error && !loading, [error, loading]);

  const router = useRouter();

  // Handler for Buy button: initiate chat, then redirect to perk details and open chat
  const handleBuyClick = async (perk: any) => {
    console.log('Buy button clicked', perk);
    const buyerId = state.userBo?.id;
    const sellerId = perk.sellerId || perk.user?.id;
    const perkId = perk.id;
    console.log('buyerId:', buyerId, 'sellerId:', sellerId, 'perkId:', perkId);
    if (!buyerId || !sellerId || !perkId) {
      console.warn('Missing required IDs for chat initiation');
      return;
    }
    try {
      const result = await initiateChatRoom({ buyerId, sellerId, perkId });
      console.log('initiateChatRoom result:', result);
      if (result.status === 200) {
        // Redirect to perk details with openChat=1 and chatRoomId
        router.push(`/perks-shop/${perkId}?openChat=1&chatRoomId=${result.chatRoomId}`);
      } else {
        console.warn('initiateChatRoom returned non-200 status:', result);
      }
    } catch (err) {
      console.error('Error initiating chat:', err);
      // Optionally show a toast here
    }
  };

  return (
    <PageTransition>
      <div className="max-w-[1840px] px-0 md:px-8 mx-auto min-h-screen">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Filters
            onFilterChange={handleFilterChange}
            title={t('ui.trendingPerks')}
            search={false}
            loading={loading}
          />
        </motion.div>

        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mt-4"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              {Array.from({ length: 10 }).map((_, index) => (
                <motion.div
                  key={`skeleton-${index}`}
                  variants={{
                    initial: { opacity: 0, y: 20 },
                    animate: {
                      opacity: 1,
                      y: 0,
                      transition: { delay: index * 0.1 },
                    },
                    exit: { opacity: 0, y: -20 },
                  }}
                >
                  <CardSkeleton buttonType="buy" />
                </motion.div>
              ))}
            </motion.div>
          ) : showErrorState ? (
            <motion.div
              key="error"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <ErrorState onRetry={handleRetry} />
            </motion.div>
          ) : showEmptyState ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              {hasSearchTerm ? (
                <SearchEmptyState searchTerm={filters.search} />
              ) : (
                <NoPerksEmpty />
              )}
            </motion.div>
          ) : (
            <StaggeredGrid
              key="perks"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mt-4"
              staggerDelay={0.05}
            >
              {perks.map((perk, i) => (
                <StaggeredItem key={`perk-${perk.id ?? 'noid'}-${i}`}>
                  <Link href={`/perks-shop/${perk.id}`}>
                    <Card
                      key={`perk-card-${perk.id ?? 'noid'}-${i}`}
                      name={perk.name}
                      isLive={!(i % 2)}
                      price={perk.price}
                      username={perk.username}
                      imageUrl={perk.imageUrl}
                      handle={perk.handle}
                      buttonType="buy"
                      soldCount={perk.soldCount}
                      remainingCount={perk.remainingCount}
                      timeLeft={perk.timeLeft}
                      isVerified={perk.isVerified}
                      onBuyClick={() => handleBuyClick({ ...perk, sellerId: perk.user?.id })}

                    />
                  </Link>
                </StaggeredItem>
              ))}
            </StaggeredGrid>
          )}
        </AnimatePresence>

        {!loading && !showEmptyState && !showErrorState && (
          <motion.div
            className="flex justify-center mt-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <motion.div
              className="text-gray-600 font-medium"
              whileHover={{ scale: 1.05 }}
            >
              {t('ui.showingPerks', { count: `${perks.length}` })}
            </motion.div>
          </motion.div>
        )}
      </div>
    </PageTransition>
  );
}
