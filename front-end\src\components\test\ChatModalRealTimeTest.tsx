'use client';

import React, { useState } from 'react';
import { useChatModalState } from '@/contexts/ChatModalStateContext';
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import ChatModal from '@/components/shared/chat/ChatModal';

/**
 * Test component to demonstrate real-time chat modal state management
 * This component simulates:
 * 1. Opening a chat modal
 * 2. Simulating trade status updates
 * 3. Showing how the modal updates without closing/reopening
 */
const ChatModalRealTimeTest: React.FC = () => {
  const { openModal, closeModal } = useGlobalModal();
  const { syncTradeStatusUpdate, getChatModalState } = useChatModalState();
  const [testChatRoomId] = useState('test-chat-room-123');
  const [testTradeId] = useState(12345);

  // Mock trade data
  const mockActiveTrade = {
    id: testTradeId,
    tradeId: testTradeId,
    status: 'pending_acceptance',
    userId: 1,
    perkId: 1,
    from: 'buyer-wallet',
    to: 'seller-wallet',
    createdAt: new Date().toISOString(),
    disputeStatus: 'none'
  };

  const openTestChatModal = () => {
    const modalId = `chat-modal-${testChatRoomId}`;
    
    openModal({
      id: modalId,
      component: React.createElement(ChatModal, {
        chatRoomId: testChatRoomId,
        buyerId: 1,
        sellerId: 2,
        onClose: () => closeModal(modalId),
        onRelease: () => {
          console.log('🔄 [Test] Release function called');
          // Simulate escrow release
          setTimeout(() => {
            syncTradeStatusUpdate(testTradeId, 'completed');
          }, 1000);
        },
        onRefund: () => {
          console.log('🔄 [Test] Refund function called');
          // Simulate refund
          setTimeout(() => {
            syncTradeStatusUpdate(testTradeId, 'refunded');
          }, 1000);
        },
        onReport: () => {
          console.log('🔍 [Test] Report function called');
        },
        onAccept: () => {
          console.log('🔄 [Test] Accept function called');
          // Simulate escrow acceptance
          setTimeout(() => {
            syncTradeStatusUpdate(testTradeId, 'escrowed');
          }, 1000);
        },
        activeTrade: mockActiveTrade
      }),
      closeOnBackdropClick: false,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-black/50 backdrop-blur-sm',
      modalClassName: '',
      disableScroll: true
    });
  };

  const simulateStatusUpdates = () => {
    const statuses = ['pending_acceptance', 'escrowed', 'completed', 'refunded'];
    let currentIndex = 0;

    const updateStatus = () => {
      if (currentIndex < statuses.length) {
        const status = statuses[currentIndex];
        console.log(`🎯 [Test] Simulating status update: ${status}`);
        syncTradeStatusUpdate(testTradeId, status);
        currentIndex++;
        setTimeout(updateStatus, 2000); // Update every 2 seconds
      }
    };

    updateStatus();
  };

  const getCurrentState = () => {
    const state = getChatModalState(testChatRoomId);
    console.log('📊 [Test] Current chat modal state:', state);
    alert(`Current state: ${JSON.stringify(state, null, 2)}`);
  };

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h2 className="text-xl font-bold mb-4">Chat Modal Real-time Test</h2>
      <p className="text-gray-600 mb-4">
        This test demonstrates real-time chat modal state management:
      </p>
      <ul className="list-disc list-inside text-sm text-gray-600 mb-6">
        <li>Open a chat modal with mock trade data</li>
        <li>Simulate trade status updates without closing the modal</li>
        <li>Watch buttons and status change in real-time</li>
        <li>Test notification-like behavior</li>
      </ul>
      
      <div className="space-y-3">
        <button
          onClick={openTestChatModal}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Open Test Chat Modal
        </button>
        
        <button
          onClick={simulateStatusUpdates}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Simulate Status Updates
        </button>
        
        <button
          onClick={() => syncTradeStatusUpdate(testTradeId, 'escrowed')}
          className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
        >
          Set Status: Escrowed
        </button>
        
        <button
          onClick={() => syncTradeStatusUpdate(testTradeId, 'completed')}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Set Status: Completed
        </button>
        
        <button
          onClick={getCurrentState}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Get Current State
        </button>
      </div>
      
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 className="font-semibold text-yellow-800">Test Instructions:</h3>
        <ol className="list-decimal list-inside text-sm text-yellow-700 mt-2">
          <li>Click "Open Test Chat Modal" to open the modal</li>
          <li>Click "Simulate Status Updates" to see real-time updates</li>
          <li>Notice how the modal updates without closing/reopening</li>
          <li>Try clicking the individual status buttons to test specific states</li>
          <li>Use "Get Current State" to inspect the internal state</li>
        </ol>
      </div>
    </div>
  );
};

export default ChatModalRealTimeTest;
