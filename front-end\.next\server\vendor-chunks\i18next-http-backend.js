/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/i18next-http-backend";
exports.ids = ["vendor-chunks/i18next-http-backend"];
exports.modules = {

/***/ "(ssr)/./node_modules/i18next-http-backend/esm/index.js":
/*!********************************************************!*\
  !*** ./node_modules/i18next-http-backend/esm/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/i18next-http-backend/esm/utils.js\");\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./request.js */ \"(ssr)/./node_modules/i18next-http-backend/esm/request.js\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n\n\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: _request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.makePromise)(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Backend);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaTE4bmV4dC1odHRwLWJhY2tlbmQvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLHNCQUFzQiwyQkFBMkIsb0dBQW9HLG1CQUFtQixpQkFBaUIsc0hBQXNIO0FBQy9TLHlCQUF5Qix3QkFBd0Isb0NBQW9DLHlDQUF5QyxrQ0FBa0MsMERBQTBELDBCQUEwQjtBQUNwUCw0QkFBNEIsZ0JBQWdCLHNCQUFzQixPQUFPLGtEQUFrRCxzREFBc0QsOEJBQThCLG1KQUFtSixxRUFBcUUsS0FBSztBQUM1YSxpQ0FBaUM7QUFDakMsbUNBQW1DLGdCQUFnQixjQUFjLE9BQU8sY0FBYztBQUN0RixpQ0FBaUMscUhBQXFILGNBQWM7QUFDcEssb0NBQW9DLG9FQUFvRSwwREFBMEQ7QUFDbEssNkJBQTZCLG1DQUFtQztBQUNoRSw4QkFBOEIsNENBQTRDLCtCQUErQixvQkFBb0IsbUNBQW1DLHNDQUFzQyx1RUFBdUU7QUFDcE87QUFDTjtBQUNuQztBQUNBO0FBQ0EsMEJBQTBCLEtBQUssR0FBRyxJQUFJO0FBQ3RDLDZCQUE2QixLQUFLLEdBQUcsSUFBSTtBQUN6QztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0IsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYSxtREFBTztBQUNwQjtBQUNBLHFCQUFxQjtBQUNyQix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsb0NBQW9DO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixzREFBVztBQUM1QjtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3SEFBd0g7QUFDeEgsdUdBQXVHO0FBQ3ZHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0EsaUVBQWUsT0FBTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcaTE4bmV4dC1odHRwLWJhY2tlbmRcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX3R5cGVvZihvKSB7IFwiQGJhYmVsL2hlbHBlcnMgLSB0eXBlb2ZcIjsgcmV0dXJuIF90eXBlb2YgPSBcImZ1bmN0aW9uXCIgPT0gdHlwZW9mIFN5bWJvbCAmJiBcInN5bWJvbFwiID09IHR5cGVvZiBTeW1ib2wuaXRlcmF0b3IgPyBmdW5jdGlvbiAobykgeyByZXR1cm4gdHlwZW9mIG87IH0gOiBmdW5jdGlvbiAobykgeyByZXR1cm4gbyAmJiBcImZ1bmN0aW9uXCIgPT0gdHlwZW9mIFN5bWJvbCAmJiBvLmNvbnN0cnVjdG9yID09PSBTeW1ib2wgJiYgbyAhPT0gU3ltYm9sLnByb3RvdHlwZSA/IFwic3ltYm9sXCIgOiB0eXBlb2YgbzsgfSwgX3R5cGVvZihvKTsgfVxuZnVuY3Rpb24gb3duS2V5cyhlLCByKSB7IHZhciB0ID0gT2JqZWN0LmtleXMoZSk7IGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7IHZhciBvID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhlKTsgciAmJiAobyA9IG8uZmlsdGVyKGZ1bmN0aW9uIChyKSB7IHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGUsIHIpLmVudW1lcmFibGU7IH0pKSwgdC5wdXNoLmFwcGx5KHQsIG8pOyB9IHJldHVybiB0OyB9XG5mdW5jdGlvbiBfb2JqZWN0U3ByZWFkKGUpIHsgZm9yICh2YXIgciA9IDE7IHIgPCBhcmd1bWVudHMubGVuZ3RoOyByKyspIHsgdmFyIHQgPSBudWxsICE9IGFyZ3VtZW50c1tyXSA/IGFyZ3VtZW50c1tyXSA6IHt9OyByICUgMiA/IG93bktleXMoT2JqZWN0KHQpLCAhMCkuZm9yRWFjaChmdW5jdGlvbiAocikgeyBfZGVmaW5lUHJvcGVydHkoZSwgciwgdFtyXSk7IH0pIDogT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMgPyBPYmplY3QuZGVmaW5lUHJvcGVydGllcyhlLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyh0KSkgOiBvd25LZXlzKE9iamVjdCh0KSkuZm9yRWFjaChmdW5jdGlvbiAocikgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgciwgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0LCByKSk7IH0pOyB9IHJldHVybiBlOyB9XG5mdW5jdGlvbiBfY2xhc3NDYWxsQ2hlY2soYSwgbikgeyBpZiAoIShhIGluc3RhbmNlb2YgbikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb25cIik7IH1cbmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0aWVzKGUsIHIpIHsgZm9yICh2YXIgdCA9IDA7IHQgPCByLmxlbmd0aDsgdCsrKSB7IHZhciBvID0gclt0XTsgby5lbnVtZXJhYmxlID0gby5lbnVtZXJhYmxlIHx8ICExLCBvLmNvbmZpZ3VyYWJsZSA9ICEwLCBcInZhbHVlXCIgaW4gbyAmJiAoby53cml0YWJsZSA9ICEwKSwgT2JqZWN0LmRlZmluZVByb3BlcnR5KGUsIF90b1Byb3BlcnR5S2V5KG8ua2V5KSwgbyk7IH0gfVxuZnVuY3Rpb24gX2NyZWF0ZUNsYXNzKGUsIHIsIHQpIHsgcmV0dXJuIHIgJiYgX2RlZmluZVByb3BlcnRpZXMoZS5wcm90b3R5cGUsIHIpLCB0ICYmIF9kZWZpbmVQcm9wZXJ0aWVzKGUsIHQpLCBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgXCJwcm90b3R5cGVcIiwgeyB3cml0YWJsZTogITEgfSksIGU7IH1cbmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShlLCByLCB0KSB7IHJldHVybiAociA9IF90b1Byb3BlcnR5S2V5KHIpKSBpbiBlID8gT2JqZWN0LmRlZmluZVByb3BlcnR5KGUsIHIsIHsgdmFsdWU6IHQsIGVudW1lcmFibGU6ICEwLCBjb25maWd1cmFibGU6ICEwLCB3cml0YWJsZTogITAgfSkgOiBlW3JdID0gdCwgZTsgfVxuZnVuY3Rpb24gX3RvUHJvcGVydHlLZXkodCkgeyB2YXIgaSA9IF90b1ByaW1pdGl2ZSh0LCBcInN0cmluZ1wiKTsgcmV0dXJuIFwic3ltYm9sXCIgPT0gX3R5cGVvZihpKSA/IGkgOiBpICsgXCJcIjsgfVxuZnVuY3Rpb24gX3RvUHJpbWl0aXZlKHQsIHIpIHsgaWYgKFwib2JqZWN0XCIgIT0gX3R5cGVvZih0KSB8fCAhdCkgcmV0dXJuIHQ7IHZhciBlID0gdFtTeW1ib2wudG9QcmltaXRpdmVdOyBpZiAodm9pZCAwICE9PSBlKSB7IHZhciBpID0gZS5jYWxsKHQsIHIgfHwgXCJkZWZhdWx0XCIpOyBpZiAoXCJvYmplY3RcIiAhPSBfdHlwZW9mKGkpKSByZXR1cm4gaTsgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkBAdG9QcmltaXRpdmUgbXVzdCByZXR1cm4gYSBwcmltaXRpdmUgdmFsdWUuXCIpOyB9IHJldHVybiAoXCJzdHJpbmdcIiA9PT0gciA/IFN0cmluZyA6IE51bWJlcikodCk7IH1cbmltcG9ydCB7IG1ha2VQcm9taXNlIH0gZnJvbSAnLi91dGlscy5qcyc7XG5pbXBvcnQgcmVxdWVzdCBmcm9tICcuL3JlcXVlc3QuanMnO1xudmFyIGdldERlZmF1bHRzID0gZnVuY3Rpb24gZ2V0RGVmYXVsdHMoKSB7XG4gIHJldHVybiB7XG4gICAgbG9hZFBhdGg6ICcvbG9jYWxlcy97e2xuZ319L3t7bnN9fS5qc29uJyxcbiAgICBhZGRQYXRoOiAnL2xvY2FsZXMvYWRkL3t7bG5nfX0ve3tuc319JyxcbiAgICBwYXJzZTogZnVuY3Rpb24gcGFyc2UoZGF0YSkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2UoZGF0YSk7XG4gICAgfSxcbiAgICBzdHJpbmdpZnk6IEpTT04uc3RyaW5naWZ5LFxuICAgIHBhcnNlUGF5bG9hZDogZnVuY3Rpb24gcGFyc2VQYXlsb2FkKG5hbWVzcGFjZSwga2V5LCBmYWxsYmFja1ZhbHVlKSB7XG4gICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KHt9LCBrZXksIGZhbGxiYWNrVmFsdWUgfHwgJycpO1xuICAgIH0sXG4gICAgcGFyc2VMb2FkUGF5bG9hZDogZnVuY3Rpb24gcGFyc2VMb2FkUGF5bG9hZChsYW5ndWFnZXMsIG5hbWVzcGFjZXMpIHtcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfSxcbiAgICByZXF1ZXN0OiByZXF1ZXN0LFxuICAgIHJlbG9hZEludGVydmFsOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGZhbHNlIDogNjAgKiA2MCAqIDEwMDAsXG4gICAgY3VzdG9tSGVhZGVyczoge30sXG4gICAgcXVlcnlTdHJpbmdQYXJhbXM6IHt9LFxuICAgIGNyb3NzRG9tYWluOiBmYWxzZSxcbiAgICB3aXRoQ3JlZGVudGlhbHM6IGZhbHNlLFxuICAgIG92ZXJyaWRlTWltZVR5cGU6IGZhbHNlLFxuICAgIHJlcXVlc3RPcHRpb25zOiB7XG4gICAgICBtb2RlOiAnY29ycycsXG4gICAgICBjcmVkZW50aWFsczogJ3NhbWUtb3JpZ2luJyxcbiAgICAgIGNhY2hlOiAnZGVmYXVsdCdcbiAgICB9XG4gIH07XG59O1xudmFyIEJhY2tlbmQgPSBmdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIEJhY2tlbmQoc2VydmljZXMpIHtcbiAgICB2YXIgb3B0aW9ucyA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDoge307XG4gICAgdmFyIGFsbE9wdGlvbnMgPSBhcmd1bWVudHMubGVuZ3RoID4gMiAmJiBhcmd1bWVudHNbMl0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1syXSA6IHt9O1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBCYWNrZW5kKTtcbiAgICB0aGlzLnNlcnZpY2VzID0gc2VydmljZXM7XG4gICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucztcbiAgICB0aGlzLmFsbE9wdGlvbnMgPSBhbGxPcHRpb25zO1xuICAgIHRoaXMudHlwZSA9ICdiYWNrZW5kJztcbiAgICB0aGlzLmluaXQoc2VydmljZXMsIG9wdGlvbnMsIGFsbE9wdGlvbnMpO1xuICB9XG4gIHJldHVybiBfY3JlYXRlQ2xhc3MoQmFja2VuZCwgW3tcbiAgICBrZXk6IFwiaW5pdFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBpbml0KHNlcnZpY2VzKSB7XG4gICAgICB2YXIgX3RoaXMgPSB0aGlzO1xuICAgICAgdmFyIG9wdGlvbnMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHt9O1xuICAgICAgdmFyIGFsbE9wdGlvbnMgPSBhcmd1bWVudHMubGVuZ3RoID4gMiAmJiBhcmd1bWVudHNbMl0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1syXSA6IHt9O1xuICAgICAgdGhpcy5zZXJ2aWNlcyA9IHNlcnZpY2VzO1xuICAgICAgdGhpcy5vcHRpb25zID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGdldERlZmF1bHRzKCkpLCB0aGlzLm9wdGlvbnMgfHwge30pLCBvcHRpb25zKTtcbiAgICAgIHRoaXMuYWxsT3B0aW9ucyA9IGFsbE9wdGlvbnM7XG4gICAgICBpZiAodGhpcy5zZXJ2aWNlcyAmJiB0aGlzLm9wdGlvbnMucmVsb2FkSW50ZXJ2YWwpIHtcbiAgICAgICAgdmFyIHRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHJldHVybiBfdGhpcy5yZWxvYWQoKTtcbiAgICAgICAgfSwgdGhpcy5vcHRpb25zLnJlbG9hZEludGVydmFsKTtcbiAgICAgICAgaWYgKF90eXBlb2YodGltZXIpID09PSAnb2JqZWN0JyAmJiB0eXBlb2YgdGltZXIudW5yZWYgPT09ICdmdW5jdGlvbicpIHRpbWVyLnVucmVmKCk7XG4gICAgICB9XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInJlYWRNdWx0aVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZWFkTXVsdGkobGFuZ3VhZ2VzLCBuYW1lc3BhY2VzLCBjYWxsYmFjaykge1xuICAgICAgdGhpcy5fcmVhZEFueShsYW5ndWFnZXMsIGxhbmd1YWdlcywgbmFtZXNwYWNlcywgbmFtZXNwYWNlcywgY2FsbGJhY2spO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJyZWFkXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlYWQobGFuZ3VhZ2UsIG5hbWVzcGFjZSwgY2FsbGJhY2spIHtcbiAgICAgIHRoaXMuX3JlYWRBbnkoW2xhbmd1YWdlXSwgbGFuZ3VhZ2UsIFtuYW1lc3BhY2VdLCBuYW1lc3BhY2UsIGNhbGxiYWNrKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiX3JlYWRBbnlcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gX3JlYWRBbnkobGFuZ3VhZ2VzLCBsb2FkVXJsTGFuZ3VhZ2VzLCBuYW1lc3BhY2VzLCBsb2FkVXJsTmFtZXNwYWNlcywgY2FsbGJhY2spIHtcbiAgICAgIHZhciBfdGhpczIgPSB0aGlzO1xuICAgICAgdmFyIGxvYWRQYXRoID0gdGhpcy5vcHRpb25zLmxvYWRQYXRoO1xuICAgICAgaWYgKHR5cGVvZiB0aGlzLm9wdGlvbnMubG9hZFBhdGggPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgbG9hZFBhdGggPSB0aGlzLm9wdGlvbnMubG9hZFBhdGgobGFuZ3VhZ2VzLCBuYW1lc3BhY2VzKTtcbiAgICAgIH1cbiAgICAgIGxvYWRQYXRoID0gbWFrZVByb21pc2UobG9hZFBhdGgpO1xuICAgICAgbG9hZFBhdGgudGhlbihmdW5jdGlvbiAocmVzb2x2ZWRMb2FkUGF0aCkge1xuICAgICAgICBpZiAoIXJlc29sdmVkTG9hZFBhdGgpIHJldHVybiBjYWxsYmFjayhudWxsLCB7fSk7XG4gICAgICAgIHZhciB1cmwgPSBfdGhpczIuc2VydmljZXMuaW50ZXJwb2xhdG9yLmludGVycG9sYXRlKHJlc29sdmVkTG9hZFBhdGgsIHtcbiAgICAgICAgICBsbmc6IGxhbmd1YWdlcy5qb2luKCcrJyksXG4gICAgICAgICAgbnM6IG5hbWVzcGFjZXMuam9pbignKycpXG4gICAgICAgIH0pO1xuICAgICAgICBfdGhpczIubG9hZFVybCh1cmwsIGNhbGxiYWNrLCBsb2FkVXJsTGFuZ3VhZ2VzLCBsb2FkVXJsTmFtZXNwYWNlcyk7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwibG9hZFVybFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBsb2FkVXJsKHVybCwgY2FsbGJhY2ssIGxhbmd1YWdlcywgbmFtZXNwYWNlcykge1xuICAgICAgdmFyIF90aGlzMyA9IHRoaXM7XG4gICAgICB2YXIgbG5nID0gdHlwZW9mIGxhbmd1YWdlcyA9PT0gJ3N0cmluZycgPyBbbGFuZ3VhZ2VzXSA6IGxhbmd1YWdlcztcbiAgICAgIHZhciBucyA9IHR5cGVvZiBuYW1lc3BhY2VzID09PSAnc3RyaW5nJyA/IFtuYW1lc3BhY2VzXSA6IG5hbWVzcGFjZXM7XG4gICAgICB2YXIgcGF5bG9hZCA9IHRoaXMub3B0aW9ucy5wYXJzZUxvYWRQYXlsb2FkKGxuZywgbnMpO1xuICAgICAgdGhpcy5vcHRpb25zLnJlcXVlc3QodGhpcy5vcHRpb25zLCB1cmwsIHBheWxvYWQsIGZ1bmN0aW9uIChlcnIsIHJlcykge1xuICAgICAgICBpZiAocmVzICYmIChyZXMuc3RhdHVzID49IDUwMCAmJiByZXMuc3RhdHVzIDwgNjAwIHx8ICFyZXMuc3RhdHVzKSkgcmV0dXJuIGNhbGxiYWNrKCdmYWlsZWQgbG9hZGluZyAnICsgdXJsICsgJzsgc3RhdHVzIGNvZGU6ICcgKyByZXMuc3RhdHVzLCB0cnVlKTtcbiAgICAgICAgaWYgKHJlcyAmJiByZXMuc3RhdHVzID49IDQwMCAmJiByZXMuc3RhdHVzIDwgNTAwKSByZXR1cm4gY2FsbGJhY2soJ2ZhaWxlZCBsb2FkaW5nICcgKyB1cmwgKyAnOyBzdGF0dXMgY29kZTogJyArIHJlcy5zdGF0dXMsIGZhbHNlKTtcbiAgICAgICAgaWYgKCFyZXMgJiYgZXJyICYmIGVyci5tZXNzYWdlKSB7XG4gICAgICAgICAgdmFyIGVycm9yTWVzc2FnZSA9IGVyci5tZXNzYWdlLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgdmFyIGlzTmV0d29ya0Vycm9yID0gWydmYWlsZWQnLCAnZmV0Y2gnLCAnbmV0d29yaycsICdsb2FkJ10uZmluZChmdW5jdGlvbiAodGVybSkge1xuICAgICAgICAgICAgcmV0dXJuIGVycm9yTWVzc2FnZS5pbmRleE9mKHRlcm0pID4gLTE7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgaWYgKGlzTmV0d29ya0Vycm9yKSB7XG4gICAgICAgICAgICByZXR1cm4gY2FsbGJhY2soJ2ZhaWxlZCBsb2FkaW5nICcgKyB1cmwgKyAnOiAnICsgZXJyLm1lc3NhZ2UsIHRydWUpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoZXJyKSByZXR1cm4gY2FsbGJhY2soZXJyLCBmYWxzZSk7XG4gICAgICAgIHZhciByZXQsIHBhcnNlRXJyO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGlmICh0eXBlb2YgcmVzLmRhdGEgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICByZXQgPSBfdGhpczMub3B0aW9ucy5wYXJzZShyZXMuZGF0YSwgbGFuZ3VhZ2VzLCBuYW1lc3BhY2VzKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0ID0gcmVzLmRhdGE7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgcGFyc2VFcnIgPSAnZmFpbGVkIHBhcnNpbmcgJyArIHVybCArICcgdG8ganNvbic7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHBhcnNlRXJyKSByZXR1cm4gY2FsbGJhY2socGFyc2VFcnIsIGZhbHNlKTtcbiAgICAgICAgY2FsbGJhY2sobnVsbCwgcmV0KTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJjcmVhdGVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gY3JlYXRlKGxhbmd1YWdlcywgbmFtZXNwYWNlLCBrZXksIGZhbGxiYWNrVmFsdWUsIGNhbGxiYWNrKSB7XG4gICAgICB2YXIgX3RoaXM0ID0gdGhpcztcbiAgICAgIGlmICghdGhpcy5vcHRpb25zLmFkZFBhdGgpIHJldHVybjtcbiAgICAgIGlmICh0eXBlb2YgbGFuZ3VhZ2VzID09PSAnc3RyaW5nJykgbGFuZ3VhZ2VzID0gW2xhbmd1YWdlc107XG4gICAgICB2YXIgcGF5bG9hZCA9IHRoaXMub3B0aW9ucy5wYXJzZVBheWxvYWQobmFtZXNwYWNlLCBrZXksIGZhbGxiYWNrVmFsdWUpO1xuICAgICAgdmFyIGZpbmlzaGVkID0gMDtcbiAgICAgIHZhciBkYXRhQXJyYXkgPSBbXTtcbiAgICAgIHZhciByZXNBcnJheSA9IFtdO1xuICAgICAgbGFuZ3VhZ2VzLmZvckVhY2goZnVuY3Rpb24gKGxuZykge1xuICAgICAgICB2YXIgYWRkUGF0aCA9IF90aGlzNC5vcHRpb25zLmFkZFBhdGg7XG4gICAgICAgIGlmICh0eXBlb2YgX3RoaXM0Lm9wdGlvbnMuYWRkUGF0aCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgIGFkZFBhdGggPSBfdGhpczQub3B0aW9ucy5hZGRQYXRoKGxuZywgbmFtZXNwYWNlKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgdXJsID0gX3RoaXM0LnNlcnZpY2VzLmludGVycG9sYXRvci5pbnRlcnBvbGF0ZShhZGRQYXRoLCB7XG4gICAgICAgICAgbG5nOiBsbmcsXG4gICAgICAgICAgbnM6IG5hbWVzcGFjZVxuICAgICAgICB9KTtcbiAgICAgICAgX3RoaXM0Lm9wdGlvbnMucmVxdWVzdChfdGhpczQub3B0aW9ucywgdXJsLCBwYXlsb2FkLCBmdW5jdGlvbiAoZGF0YSwgcmVzKSB7XG4gICAgICAgICAgZmluaXNoZWQgKz0gMTtcbiAgICAgICAgICBkYXRhQXJyYXkucHVzaChkYXRhKTtcbiAgICAgICAgICByZXNBcnJheS5wdXNoKHJlcyk7XG4gICAgICAgICAgaWYgKGZpbmlzaGVkID09PSBsYW5ndWFnZXMubGVuZ3RoKSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGNhbGxiYWNrID09PSAnZnVuY3Rpb24nKSBjYWxsYmFjayhkYXRhQXJyYXksIHJlc0FycmF5KTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInJlbG9hZFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZWxvYWQoKSB7XG4gICAgICB2YXIgX3RoaXM1ID0gdGhpcztcbiAgICAgIHZhciBfdGhpcyRzZXJ2aWNlcyA9IHRoaXMuc2VydmljZXMsXG4gICAgICAgIGJhY2tlbmRDb25uZWN0b3IgPSBfdGhpcyRzZXJ2aWNlcy5iYWNrZW5kQ29ubmVjdG9yLFxuICAgICAgICBsYW5ndWFnZVV0aWxzID0gX3RoaXMkc2VydmljZXMubGFuZ3VhZ2VVdGlscyxcbiAgICAgICAgbG9nZ2VyID0gX3RoaXMkc2VydmljZXMubG9nZ2VyO1xuICAgICAgdmFyIGN1cnJlbnRMYW5ndWFnZSA9IGJhY2tlbmRDb25uZWN0b3IubGFuZ3VhZ2U7XG4gICAgICBpZiAoY3VycmVudExhbmd1YWdlICYmIGN1cnJlbnRMYW5ndWFnZS50b0xvd2VyQ2FzZSgpID09PSAnY2ltb2RlJykgcmV0dXJuO1xuICAgICAgdmFyIHRvTG9hZCA9IFtdO1xuICAgICAgdmFyIGFwcGVuZCA9IGZ1bmN0aW9uIGFwcGVuZChsbmcpIHtcbiAgICAgICAgdmFyIGxuZ3MgPSBsYW5ndWFnZVV0aWxzLnRvUmVzb2x2ZUhpZXJhcmNoeShsbmcpO1xuICAgICAgICBsbmdzLmZvckVhY2goZnVuY3Rpb24gKGwpIHtcbiAgICAgICAgICBpZiAodG9Mb2FkLmluZGV4T2YobCkgPCAwKSB0b0xvYWQucHVzaChsKTtcbiAgICAgICAgfSk7XG4gICAgICB9O1xuICAgICAgYXBwZW5kKGN1cnJlbnRMYW5ndWFnZSk7XG4gICAgICBpZiAodGhpcy5hbGxPcHRpb25zLnByZWxvYWQpIHRoaXMuYWxsT3B0aW9ucy5wcmVsb2FkLmZvckVhY2goZnVuY3Rpb24gKGwpIHtcbiAgICAgICAgcmV0dXJuIGFwcGVuZChsKTtcbiAgICAgIH0pO1xuICAgICAgdG9Mb2FkLmZvckVhY2goZnVuY3Rpb24gKGxuZykge1xuICAgICAgICBfdGhpczUuYWxsT3B0aW9ucy5ucy5mb3JFYWNoKGZ1bmN0aW9uIChucykge1xuICAgICAgICAgIGJhY2tlbmRDb25uZWN0b3IucmVhZChsbmcsIG5zLCAncmVhZCcsIG51bGwsIG51bGwsIGZ1bmN0aW9uIChlcnIsIGRhdGEpIHtcbiAgICAgICAgICAgIGlmIChlcnIpIGxvZ2dlci53YXJuKFwibG9hZGluZyBuYW1lc3BhY2UgXCIuY29uY2F0KG5zLCBcIiBmb3IgbGFuZ3VhZ2UgXCIpLmNvbmNhdChsbmcsIFwiIGZhaWxlZFwiKSwgZXJyKTtcbiAgICAgICAgICAgIGlmICghZXJyICYmIGRhdGEpIGxvZ2dlci5sb2coXCJsb2FkZWQgbmFtZXNwYWNlIFwiLmNvbmNhdChucywgXCIgZm9yIGxhbmd1YWdlIFwiKS5jb25jYXQobG5nKSwgZGF0YSk7XG4gICAgICAgICAgICBiYWNrZW5kQ29ubmVjdG9yLmxvYWRlZChcIlwiLmNvbmNhdChsbmcsIFwifFwiKS5jb25jYXQobnMpLCBlcnIsIGRhdGEpO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfV0pO1xufSgpO1xuQmFja2VuZC50eXBlID0gJ2JhY2tlbmQnO1xuZXhwb3J0IGRlZmF1bHQgQmFja2VuZDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-http-backend/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/i18next-http-backend/esm/request.js":
/*!**********************************************************!*\
  !*** ./node_modules/i18next-http-backend/esm/request.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/i18next-http-backend/esm/utils.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hasXMLHttpRequest)()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nif (!fetchApi && !XmlHttpRequestApi && !ActiveXObjectApi) {\n  try {\n    __webpack_require__.e(/*! import() */ \"vendor-chunks/i18next-http-backend\").then(__webpack_require__.t.bind(__webpack_require__, /*! cross-fetch */ \"(ssr)/./node_modules/i18next-http-backend/node_modules/cross-fetch/dist/node-ponyfill.js\", 19)).then(function (mod) {\n      fetchApi = mod.default;\n    }).catch(function () {});\n  } catch (e) {}\n}\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x = XmlHttpRequestApi ? new XmlHttpRequestApi() : new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hasXMLHttpRequest)() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (request);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-http-backend/esm/request.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/i18next-http-backend/esm/utils.js":
/*!********************************************************!*\
  !*** ./node_modules/i18next-http-backend/esm/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaults: () => (/* binding */ defaults),\n/* harmony export */   hasXMLHttpRequest: () => (/* binding */ hasXMLHttpRequest),\n/* harmony export */   makePromise: () => (/* binding */ makePromise)\n/* harmony export */ });\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nfunction makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-http-backend/esm/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/i18next-http-backend/node_modules/cross-fetch/dist/node-ponyfill.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/i18next-http-backend/node_modules/cross-fetch/dist/node-ponyfill.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("const nodeFetch = __webpack_require__(/*! node-fetch */ \"(ssr)/./node_modules/node-fetch/lib/index.mjs\")\nconst realFetch = nodeFetch.default || nodeFetch\n\nconst fetch = function (url, options) {\n  // Support schemaless URIs on the server for parity with the browser.\n  // Ex: //github.com/ -> https://github.com/\n  if (/^\\/\\//.test(url)) {\n    url = 'https:' + url\n  }\n  return realFetch.call(this, url, options)\n}\n\nfetch.ponyfill = true\n\nmodule.exports = exports = fetch\nexports.fetch = fetch\nexports.Headers = nodeFetch.Headers\nexports.Request = nodeFetch.Request\nexports.Response = nodeFetch.Response\n\n// Needed for TypeScript consumers without esModuleInterop.\nexports[\"default\"] = fetch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaTE4bmV4dC1odHRwLWJhY2tlbmQvbm9kZV9tb2R1bGVzL2Nyb3NzLWZldGNoL2Rpc3Qvbm9kZS1wb255ZmlsbC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrQkFBa0IsbUJBQU8sQ0FBQyxpRUFBWTtBQUN0Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsYUFBYTtBQUNiLGVBQWU7QUFDZixlQUFlO0FBQ2YsZ0JBQWdCOztBQUVoQjtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxpMThuZXh0LWh0dHAtYmFja2VuZFxcbm9kZV9tb2R1bGVzXFxjcm9zcy1mZXRjaFxcZGlzdFxcbm9kZS1wb255ZmlsbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBub2RlRmV0Y2ggPSByZXF1aXJlKCdub2RlLWZldGNoJylcbmNvbnN0IHJlYWxGZXRjaCA9IG5vZGVGZXRjaC5kZWZhdWx0IHx8IG5vZGVGZXRjaFxuXG5jb25zdCBmZXRjaCA9IGZ1bmN0aW9uICh1cmwsIG9wdGlvbnMpIHtcbiAgLy8gU3VwcG9ydCBzY2hlbWFsZXNzIFVSSXMgb24gdGhlIHNlcnZlciBmb3IgcGFyaXR5IHdpdGggdGhlIGJyb3dzZXIuXG4gIC8vIEV4OiAvL2dpdGh1Yi5jb20vIC0+IGh0dHBzOi8vZ2l0aHViLmNvbS9cbiAgaWYgKC9eXFwvXFwvLy50ZXN0KHVybCkpIHtcbiAgICB1cmwgPSAnaHR0cHM6JyArIHVybFxuICB9XG4gIHJldHVybiByZWFsRmV0Y2guY2FsbCh0aGlzLCB1cmwsIG9wdGlvbnMpXG59XG5cbmZldGNoLnBvbnlmaWxsID0gdHJ1ZVxuXG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMgPSBmZXRjaFxuZXhwb3J0cy5mZXRjaCA9IGZldGNoXG5leHBvcnRzLkhlYWRlcnMgPSBub2RlRmV0Y2guSGVhZGVyc1xuZXhwb3J0cy5SZXF1ZXN0ID0gbm9kZUZldGNoLlJlcXVlc3RcbmV4cG9ydHMuUmVzcG9uc2UgPSBub2RlRmV0Y2guUmVzcG9uc2VcblxuLy8gTmVlZGVkIGZvciBUeXBlU2NyaXB0IGNvbnN1bWVycyB3aXRob3V0IGVzTW9kdWxlSW50ZXJvcC5cbmV4cG9ydHMuZGVmYXVsdCA9IGZldGNoXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-http-backend/node_modules/cross-fetch/dist/node-ponyfill.js\n");

/***/ })

};
;