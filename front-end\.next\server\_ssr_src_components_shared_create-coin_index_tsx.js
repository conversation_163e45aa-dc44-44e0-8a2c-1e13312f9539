"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_create-coin_index_tsx";
exports.ids = ["_ssr_src_components_shared_create-coin_index_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/create-coin/index.tsx":
/*!*****************************************************!*\
  !*** ./src/components/shared/create-coin/index.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateCoinPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CreateCoinForm = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/components/auth/create-coin-form */ \"(ssr)/./src/components/auth/create-coin-form/index.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\shared\\\\create-coin\\\\index.tsx -> \" + \"@/components/auth/create-coin-form\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse h-96 bg-gray-200 rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\create-coin\\\\index.tsx\",\n            lineNumber: 7,\n            columnNumber: 18\n        }, undefined)\n});\nfunction CreateCoinPage({ onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#000000c7] flex flex-col items-center justify-center p-4 fixed top-0 left-0 w-full h-full z-10\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\create-coin\\\\index.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zaGFyZWQvY3JlYXRlLWNvaW4vaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMEI7QUFDUztBQUVuQyxNQUFNRSxpQkFBaUJELHdEQUFPQSxDQUFDLElBQU0sd0xBQTRDLENBQUNFLElBQUksQ0FBQ0MsQ0FBQUEsTUFBUTtZQUFFQyxTQUFTRCxJQUFJQyxPQUFPO1FBQUM7Ozs7OztJQUNwSEMsU0FBUyxrQkFBTSw4REFBQ0M7WUFBSUMsV0FBVTs7Ozs7OztBQUdqQixTQUFTQyxlQUFlLEVBQUVDLE9BQU8sRUFBMkI7SUFDekUscUJBQ0UsOERBQUNIO1FBQUlDLFdBQVU7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXHNyY1xcY29tcG9uZW50c1xcc2hhcmVkXFxjcmVhdGUtY29pblxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG5jb25zdCBDcmVhdGVDb2luRm9ybSA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvYXV0aC9jcmVhdGUtY29pbi1mb3JtJykudGhlbihtb2QgPT4gKHsgZGVmYXVsdDogbW9kLmRlZmF1bHQgfSkpLCB7XHJcbiAgbG9hZGluZzogKCkgPT4gPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlIGgtOTYgYmctZ3JheS0yMDAgcm91bmRlZC1sZ1wiPjwvZGl2PixcclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDcmVhdGVDb2luUGFnZSh7IG9uQ2xvc2UgfTogeyBvbkNsb3NlOiAoKSA9PiB2b2lkIH0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzAwMDAwMGM3XSBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgZml4ZWQgdG9wLTAgbGVmdC0wIHctZnVsbCBoLWZ1bGwgei0xMFwiPlxyXG4gICAgICBcclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiZHluYW1pYyIsIkNyZWF0ZUNvaW5Gb3JtIiwidGhlbiIsIm1vZCIsImRlZmF1bHQiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiQ3JlYXRlQ29pblBhZ2UiLCJvbkNsb3NlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/create-coin/index.tsx\n");

/***/ })

};
;