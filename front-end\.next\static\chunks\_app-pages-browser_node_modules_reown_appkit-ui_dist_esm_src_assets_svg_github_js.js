"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_github_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   githubSvg: () => (/* binding */ githubSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst githubSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#1B1F23\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M8 19.89a12 12 0 1 1 15.8 11.38c-.6.12-.8-.26-.8-.57v-3.3c0-1.12-.4-1.85-.82-2.22 2.67-.3 5.48-1.31 5.48-5.92 0-1.31-.47-2.38-1.24-3.22.13-.3.54-1.52-.12-3.18 0 0-1-.32-3.3 1.23a11.54 11.54 0 0 0-6 0c-2.3-1.55-3.3-1.23-3.3-1.23a4.32 4.32 0 0 0-.12 3.18 4.64 4.64 0 0 0-1.24 3.22c0 4.6 2.8 5.63 5.47 5.93-.34.3-.65.83-.76 1.6-.69.31-2.42.84-3.5-1 0 0-.63-1.15-1.83-1.23 0 0-1.18-.02-.09.73 0 0 .8.37 1.34 1.76 0 0 .7 2.14 4.03 1.41v2.24c0 .31-.2.68-.8.57A12 12 0 0 1 8 19.9Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=github.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js\n"));

/***/ })

}]);