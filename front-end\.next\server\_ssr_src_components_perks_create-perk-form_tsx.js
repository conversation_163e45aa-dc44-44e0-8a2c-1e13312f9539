"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_perks_create-perk-form_tsx";
exports.ids = ["_ssr_src_components_perks_create-perk-form_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/perks/components/FormHandlers.ts":
/*!*********************************************************!*\
  !*** ./src/components/perks/components/FormHandlers.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFormHandlers: () => (/* binding */ useFormHandlers)\n/* harmony export */ });\n/* harmony import */ var _FormValidation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FormValidation */ \"(ssr)/./src/components/perks/components/FormValidation.ts\");\n/* harmony import */ var _hooks_useGenericFormHandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../hooks/useGenericFormHandler */ \"(ssr)/./src/hooks/useGenericFormHandler.ts\");\n/* harmony import */ var _hooks_useFileUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../hooks/useFileUpload */ \"(ssr)/./src/hooks/useFileUpload.ts\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/environment */ \"(ssr)/./src/config/environment.ts\");\n\n\n\n\nconst useFormHandlers = (formData, setFormData, errors, setErrors, fileInputId = 'picture-upload')=>{\n    // Define validation rules for the form\n    const validationRules = {\n        name: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateName)(value),\n        price: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validatePrice)(value),\n        fulfillmentLink: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateFulfillmentLink)(value),\n        stockAmount: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateStockAmount)(value, formData.limitedStock),\n        category: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateCategory)(value)\n    };\n    // Use generic form handler with type assertions for compatibility\n    const { handleChange, handleCheckboxChange, handleBlur } = (0,_hooks_useGenericFormHandler__WEBPACK_IMPORTED_MODULE_1__.useGenericFormHandler)({\n        formData,\n        setFormData,\n        errors: errors,\n        setErrors: setErrors,\n        validationRules\n    });\n    // Use file upload handler with type assertions for compatibility\n    const { handleFileChange, handleDeleteFile } = (0,_hooks_useFileUpload__WEBPACK_IMPORTED_MODULE_2__.useFileUpload)({\n        formData,\n        setFormData,\n        errors: errors,\n        setErrors: setErrors,\n        fieldName: 'picture',\n        errorFieldName: 'picture',\n        options: {\n            fileInputId,\n            maxSize: _config_environment__WEBPACK_IMPORTED_MODULE_3__.UPLOAD_CONFIG.MAX_FILE_SIZE,\n            allowedTypes: _config_environment__WEBPACK_IMPORTED_MODULE_3__.UPLOAD_CONFIG.ALLOWED_FILE_TYPES\n        }\n    });\n    // Maintain backward compatibility by renaming the delete function\n    const handleDeletePicture = handleDeleteFile;\n    return {\n        handleChange,\n        handleCheckboxChange,\n        handleBlur,\n        handleFileChange,\n        handleDeletePicture\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/perks/components/FormHandlers.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/perks/components/FormSubmission.ts":
/*!***********************************************************!*\
  !*** ./src/components/perks/components/FormSubmission.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFormSubmission: () => (/* binding */ useFormSubmission)\n/* harmony export */ });\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../axios/requests */ \"(ssr)/./src/axios/requests.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(ssr)/./src/utils/helpers.ts\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/errorHandling */ \"(ssr)/./src/utils/errorHandling.ts\");\n\n\n\nconst useFormSubmission = (formData, userID, onClose, setSubmitted, setIsSubmitting, setFormData, validateForm, hasErrors, t)=>{\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const newErrors = validateForm(formData, userID);\n        if (!hasErrors(newErrors)) {\n            setIsSubmitting(true);\n            let imageUrl = '';\n            if (formData.picture) {\n                try {\n                    imageUrl = await (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.uploadToPinata)(formData.picture);\n                } catch (error) {\n                    console.error('Image upload failed:', error);\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.FORM.IMAGE_UPLOAD_FAILED));\n                    setIsSubmitting(false);\n                    return newErrors;\n                }\n            }\n            // Get userId from localStorage if available\n            let submissionUserId = userID;\n            if (false) {}\n            const newPerk = {\n                name: formData.name,\n                description: formData.description,\n                price: formData.price,\n                fulfillmentLink: formData.fulfillmentLink,\n                image: imageUrl,\n                isLimited: formData.limitedStock,\n                stockAmount: parseInt(formData.stockAmount),\n                tokenAmount: parseInt(formData.tokenAmount) || 1,\n                category: formData.category,\n                userId: submissionUserId\n            };\n            try {\n                const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_0__.addPerk)(newPerk);\n                if (response.status === 201) {\n                    setSubmitted(true);\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showSuccessToast)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_SUCCESS);\n                    // Reset form after 2 seconds\n                    setTimeout(()=>{\n                        setFormData({\n                            name: '',\n                            description: '',\n                            price: '',\n                            fulfillmentLink: '',\n                            picture: null,\n                            limitedStock: false,\n                            stockAmount: '',\n                            tokenAmount: '1',\n                            category: ''\n                        });\n                        setSubmitted(false);\n                        onClose();\n                    }, 2000);\n                } else {\n                    const errorMessage = response.data?.message || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(errorMessage, response.status));\n                }\n            } catch (error) {\n                console.error('Perk creation failed:', error);\n                // Handle different types of errors with specific messages\n                if (error.response?.status === 401) {\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.AUTH.LOGIN_REQUIRED));\n                } else if (error.response?.status === 400) {\n                    const errorMessage = error.response?.data?.message || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.FORM.VALIDATION_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(errorMessage));\n                } else if (error.response?.status >= 500) {\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.NETWORK.SERVER_ERROR, error.response?.status));\n                } else {\n                    const errorMessage = error.response?.data?.message || error.message || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(errorMessage));\n                }\n            } finally{\n                setIsSubmitting(false);\n            }\n        }\n        return newErrors;\n    };\n    return {\n        handleSubmit\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/perks/components/FormSubmission.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/perks/components/FormValidation.ts":
/*!***********************************************************!*\
  !*** ./src/components/perks/components/FormValidation.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateCategory: () => (/* binding */ validateCategory),\n/* harmony export */   validateFulfillmentLink: () => (/* binding */ validateFulfillmentLink),\n/* harmony export */   validateName: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateName),\n/* harmony export */   validatePrice: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validatePrice),\n/* harmony export */   validateStockAmount: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateStockAmount)\n/* harmony export */ });\n/* harmony import */ var _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/formValidation */ \"(ssr)/./src/utils/formValidation.ts\");\n\nconst validateFulfillmentLink = (link)=>{\n    return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateUrl)(link, 'fulfillment link');\n};\nconst validateCategory = (category)=>{\n    if (!category || category.trim() === '') {\n        return 'Please select a category';\n    }\n    return '';\n};\n// Re-export the shared functions for backward compatibility\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wZXJrcy9jb21wb25lbnRzL0Zvcm1WYWxpZGF0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUt1QztBQUVoQyxNQUFNSSwwQkFBMEIsQ0FBQ0M7SUFDdEMsT0FBT0gsa0VBQVdBLENBQUNHLE1BQU07QUFDM0IsRUFBRTtBQUVLLE1BQU1DLG1CQUFtQixDQUFDQztJQUMvQixJQUFJLENBQUNBLFlBQVlBLFNBQVNDLElBQUksT0FBTyxJQUFJO1FBQ3ZDLE9BQU87SUFDVDtJQUNBLE9BQU87QUFDVCxFQUFFO0FBR0YsNERBQTREO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxzcmNcXGNvbXBvbmVudHNcXHBlcmtzXFxjb21wb25lbnRzXFxGb3JtVmFsaWRhdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gIHZhbGlkYXRlTmFtZSxcclxuICB2YWxpZGF0ZVByaWNlLFxyXG4gIHZhbGlkYXRlVXJsLFxyXG4gIHZhbGlkYXRlU3RvY2tBbW91bnQsXHJcbn0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZm9ybVZhbGlkYXRpb24nO1xyXG5cclxuZXhwb3J0IGNvbnN0IHZhbGlkYXRlRnVsZmlsbG1lbnRMaW5rID0gKGxpbms6IHN0cmluZyk6IHN0cmluZyA9PiB7XHJcbiAgcmV0dXJuIHZhbGlkYXRlVXJsKGxpbmssICdmdWxmaWxsbWVudCBsaW5rJyk7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdmFsaWRhdGVDYXRlZ29yeSA9IChjYXRlZ29yeTogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICBpZiAoIWNhdGVnb3J5IHx8IGNhdGVnb3J5LnRyaW0oKSA9PT0gJycpIHtcclxuICAgIHJldHVybiAnUGxlYXNlIHNlbGVjdCBhIGNhdGVnb3J5JztcclxuICB9XHJcbiAgcmV0dXJuICcnO1xyXG59O1xyXG5cclxuXHJcbi8vIFJlLWV4cG9ydCB0aGUgc2hhcmVkIGZ1bmN0aW9ucyBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxyXG5leHBvcnQgeyB2YWxpZGF0ZU5hbWUsIHZhbGlkYXRlUHJpY2UsIHZhbGlkYXRlU3RvY2tBbW91bnQgfTtcclxuIl0sIm5hbWVzIjpbInZhbGlkYXRlTmFtZSIsInZhbGlkYXRlUHJpY2UiLCJ2YWxpZGF0ZVVybCIsInZhbGlkYXRlU3RvY2tBbW91bnQiLCJ2YWxpZGF0ZUZ1bGZpbGxtZW50TGluayIsImxpbmsiLCJ2YWxpZGF0ZUNhdGVnb3J5IiwiY2F0ZWdvcnkiLCJ0cmltIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/perks/components/FormValidation.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/perks/create-perk-form.tsx":
/*!***************************************************!*\
  !*** ./src/components/perks/create-perk-form.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AppContext */ \"(ssr)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _utils_formValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/formValidation */ \"(ssr)/./src/utils/formValidation.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/errorHandling */ \"(ssr)/./src/utils/errorHandling.ts\");\n/* harmony import */ var _components_FormHandlers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/FormHandlers */ \"(ssr)/./src/components/perks/components/FormHandlers.ts\");\n/* harmony import */ var _components_FormSubmission__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/FormSubmission */ \"(ssr)/./src/components/perks/components/FormSubmission.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst FormFields = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/lucide-react\"), __webpack_require__.e(\"_ssr_src_components_perks_components_FormFields_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./components/FormFields */ \"(ssr)/./src/components/perks/components/FormFields.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\perks\\\\create-perk-form.tsx -> \" + \"./components/FormFields\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse h-96 bg-gray-200 rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n            lineNumber: 28,\n            columnNumber: 18\n        }, undefined)\n});\n// Validation functions using shared utilities\nconst validateFulfillmentLink = (link)=>{\n    return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(link, 'fulfillment link');\n};\nconst validateCategory = (category)=>{\n    if (!category || category.trim() === '') {\n        return 'Please select a category';\n    }\n    return '';\n};\nconst validateForm = (formData, userId)=>{\n    return {\n        name: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(formData.name),\n        description: '',\n        price: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(formData.price),\n        fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),\n        picture: !formData.picture ? 'Please upload an image for your perk' : '',\n        stockAmount: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(formData.stockAmount, formData.limitedStock),\n        category: validateCategory(formData.category),\n        api: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUserLogin)(userId)\n    };\n};\nconst CreatePerkForm = ({ onClose })=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { state } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__.useAppContext)();\n    const boxRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: '',\n        description: '',\n        price: '',\n        fulfillmentLink: '',\n        picture: null,\n        limitedStock: false,\n        stockAmount: '',\n        tokenAmount: '1',\n        category: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: '',\n        description: '',\n        price: '',\n        fulfillmentLink: '',\n        picture: '',\n        stockAmount: '',\n        tokenAmount: '',\n        category: '',\n        api: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validationProgress, setValidationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const userBo = typeof state.userBo === 'string' ? JSON.parse(state.userBo) : state.userBo;\n    const userID = userBo?.id;\n    // Validation functions using shared utilities\n    const validateFulfillmentLink = (link)=>{\n        return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(link, t('createPerkForm.fulfillmentLink'));\n    };\n    const validateCategory = (category)=>{\n        if (!category || category.trim() === '') {\n            return t('createPerkForm.errorSelectCategory');\n        }\n        return '';\n    };\n    const validateTokenAmount = (tokenAmount)=>{\n        if (!tokenAmount || tokenAmount.trim() === '') {\n            return 'Token amount is required';\n        }\n        const amount = parseInt(tokenAmount);\n        if (isNaN(amount) || amount < 1) {\n            return 'Token amount must be a positive number';\n        }\n        return '';\n    };\n    const validateForm = (formData, userId)=>{\n        return {\n            name: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(formData.name),\n            description: '',\n            price: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(formData.price),\n            fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),\n            picture: !formData.picture ? t('createPerkForm.errorUploadPicture') : '',\n            stockAmount: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(formData.stockAmount, formData.limitedStock),\n            tokenAmount: validateTokenAmount(formData.tokenAmount),\n            category: validateCategory(formData.category),\n            api: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUserLogin)(userId)\n        };\n    };\n    const { handleSubmit } = (0,_components_FormSubmission__WEBPACK_IMPORTED_MODULE_9__.useFormSubmission)(formData, userID, onClose, setSubmitted, setIsSubmitting, setFormData, validateForm, _utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors, t);\n    const { handleChange, handleCheckboxChange, handleBlur, handleFileChange, handleDeletePicture } = (0,_components_FormHandlers__WEBPACK_IMPORTED_MODULE_8__.useFormHandlers)(formData, setFormData, errors, setErrors);\n    // Enhanced form submission with better error handling\n    const handleFormSubmit = async (e)=>{\n        e.preventDefault();\n        setIsValidating(true);\n        setValidationProgress({});\n        // Validate form\n        const newErrors = validateForm(formData, userID);\n        setErrors(newErrors);\n        // Check for validation errors\n        if ((0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(newErrors)) {\n            setIsValidating(false);\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.showErrorToast)('Please fix the errors before submitting');\n            return;\n        }\n        setIsValidating(false);\n        // Proceed with submission\n        try {\n            await handleSubmit(e);\n        } catch (error) {\n            let errorMessage = 'Failed to create perk';\n            if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.ValidationError) {\n                errorMessage = error.message;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.message\n                    }));\n            } else if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.NetworkError) {\n                errorMessage = 'Network error. Please check your connection and try again.';\n            } else if (error.response?.data?.message) {\n                errorMessage = error.response.data.message;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.response.data.message\n                    }));\n            } else if (error.error) {\n                errorMessage = error.error;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.error\n                    }));\n            } else {\n                setErrors((prev)=>({\n                        ...prev,\n                        api: 'Something went wrong. Please try again.'\n                    }));\n            }\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.showErrorToast)(errorMessage);\n        }\n    };\n    // Enhanced field validation with progress tracking\n    const handleFieldValidation = (fieldName, value)=>{\n        setValidationProgress((prev)=>({\n                ...prev,\n                [fieldName]: true\n            }));\n        // Simulate validation delay for better UX\n        setTimeout(()=>{\n            let fieldError = '';\n            switch(fieldName){\n                case 'name':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(value);\n                    break;\n                case 'price':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(value);\n                    break;\n                case 'fulfillmentLink':\n                    fieldError = validateFulfillmentLink(value);\n                    break;\n                case 'category':\n                    fieldError = validateCategory(value);\n                    break;\n                case 'stockAmount':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(value, formData.limitedStock);\n                    break;\n                default:\n                    fieldError = '';\n            }\n            setErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: fieldError\n                }));\n            setValidationProgress((prev)=>({\n                    ...prev,\n                    [fieldName]: false\n                }));\n        }, 300);\n    };\n    // Enhanced change handler with validation\n    const handleEnhancedChange = (e)=>{\n        handleChange(e);\n        // Clear API error when user starts typing\n        if (errors.api) {\n            setErrors((prev)=>({\n                    ...prev,\n                    api: ''\n                }));\n        }\n        // Validate field after change\n        handleFieldValidation(e.target.name, e.target.value);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"CreatePerkForm.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"CreatePerkForm.useEffect.handleClickOutside\": (event)=>{\n                    if (boxRef.current && !boxRef.current.contains(event.target)) {\n                        onClose();\n                    }\n                }\n            }[\"CreatePerkForm.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"CreatePerkForm.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"CreatePerkForm.useEffect\"];\n        }\n    }[\"CreatePerkForm.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        className: \"w-[563px] max-w-full mx-auto bg-white rounded-[24px] md:rounded-[48px] p-4 md:p-10 pt-6 flex flex-col relative\",\n        ref: boxRef,\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.9\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"block lg:hidden absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                onClick: onClose,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: '/icons/close.svg',\n                    alt: t('common.close'),\n                    width: 24,\n                    height: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            submitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                className: \"flex flex-col items-center gap-7 py-10\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px]\",\n                        children: t('createPerkForm.successTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.svg, {\n                        className: \"w-24 h-24 text-green-500 mx-auto\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 0.2,\n                            type: \"spring\",\n                            stiffness: 200\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold font-['IBM_Plex_Sans']\",\n                        children: t('createPerkForm.successMessage')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 font-['IBM_Plex_Sans']\",\n                        children: t('createPerkForm.successDescription')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleFormSubmit,\n                className: \"flex flex-col w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mb-8\",\n                        children: t('createPerkForm.formTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormFields, {\n                        formData: formData,\n                        errors: errors,\n                        handleChange: handleEnhancedChange,\n                        handleBlur: handleBlur,\n                        handleCheckboxChange: handleCheckboxChange,\n                        handleFileChange: handleFileChange,\n                        handleDeletePicture: handleDeletePicture,\n                        t: t,\n                        validationProgress: validationProgress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: errors.api && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500 mr-2\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 font-semibold text-base\",\n                                        children: errors.api\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, undefined),\n                    isValidating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        className: \"flex items-center justify-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Validating form...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isSubmitting || isValidating || (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(errors),\n                        className: `w-full h-12 px-5 py-2 rounded-sm inline-flex justify-center items-center gap-2.5 mt-8 mx-auto transition-all duration-200 ${isSubmitting || isValidating || (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(errors) ? 'bg-gray-400 cursor-not-allowed' : 'bg-black hover:bg-gray-800'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                children: isSubmitting ? 'Creating perk...' : isValidating ? 'Validating...' : 'Create my perk'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined),\n                            (isSubmitting || isValidating) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CreatePerkForm);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/perks/create-perk-form.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useFileUpload.ts":
/*!************************************!*\
  !*** ./src/hooks/useFileUpload.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFileUpload: () => (/* binding */ useFileUpload)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/environment */ \"(ssr)/./src/config/environment.ts\");\n\n\nconst useFileUpload = ({ formData, setFormData, errors, setErrors, fieldName, errorFieldName, options = {} })=>{\n    const { maxSize = _config_environment__WEBPACK_IMPORTED_MODULE_1__.UPLOAD_CONFIG.MAX_FILE_SIZE, allowedTypes = _config_environment__WEBPACK_IMPORTED_MODULE_1__.UPLOAD_CONFIG.ALLOWED_FILE_TYPES, fileInputId = \"file-upload\" } = options;\n    const errorKey = errorFieldName || String(fieldName);\n    const validateFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[validateFile]\": (file)=>{\n            if (file.size > maxSize) {\n                return `File must be less than ${Math.round(maxSize / (1024 * 1024))}MB`;\n            }\n            if (!allowedTypes.includes(file.type)) {\n                return `Only ${allowedTypes.map({\n                    \"useFileUpload.useCallback[validateFile]\": (type)=>type.split(\"/\")[1].toUpperCase()\n                }[\"useFileUpload.useCallback[validateFile]\"]).join(\", \")} files are allowed`;\n            }\n            return \"\";\n        }\n    }[\"useFileUpload.useCallback[validateFile]\"], [\n        maxSize,\n        allowedTypes\n    ]);\n    const handleFileChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[handleFileChange]\": (e)=>{\n            if (e.target.files && e.target.files[0]) {\n                const file = e.target.files[0];\n                // Update form data\n                setFormData({\n                    \"useFileUpload.useCallback[handleFileChange]\": (prev)=>({\n                            ...prev,\n                            [fieldName]: file\n                        })\n                }[\"useFileUpload.useCallback[handleFileChange]\"]);\n                // Validate file\n                const error = validateFile(file);\n                setErrors({\n                    \"useFileUpload.useCallback[handleFileChange]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: error\n                        })\n                }[\"useFileUpload.useCallback[handleFileChange]\"]);\n            }\n        }\n    }[\"useFileUpload.useCallback[handleFileChange]\"], [\n        fieldName,\n        errorKey\n    ]);\n    const handleDeleteFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[handleDeleteFile]\": ()=>{\n            // Clear form data\n            setFormData({\n                \"useFileUpload.useCallback[handleDeleteFile]\": (prev)=>({\n                        ...prev,\n                        [fieldName]: null\n                    })\n            }[\"useFileUpload.useCallback[handleDeleteFile]\"]);\n            // Clear error\n            setErrors({\n                \"useFileUpload.useCallback[handleDeleteFile]\": (prev)=>({\n                        ...prev,\n                        [errorKey]: \"\"\n                    })\n            }[\"useFileUpload.useCallback[handleDeleteFile]\"]);\n            // Reset file input\n            const fileInput = document.getElementById(fileInputId);\n            if (fileInput) {\n                fileInput.value = \"\";\n            }\n        }\n    }[\"useFileUpload.useCallback[handleDeleteFile]\"], [\n        fieldName,\n        errorKey,\n        fileInputId\n    ]);\n    const setFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[setFile]\": (file)=>{\n            setFormData({\n                \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                        ...prev,\n                        [fieldName]: file\n                    })\n            }[\"useFileUpload.useCallback[setFile]\"]);\n            if (file) {\n                const error = validateFile(file);\n                setErrors({\n                    \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: error\n                        })\n                }[\"useFileUpload.useCallback[setFile]\"]);\n            } else {\n                setErrors({\n                    \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: \"\"\n                        })\n                }[\"useFileUpload.useCallback[setFile]\"]);\n            }\n        }\n    }[\"useFileUpload.useCallback[setFile]\"], [\n        fieldName,\n        errorKey\n    ]);\n    return {\n        handleFileChange,\n        handleDeleteFile,\n        setFile,\n        validateFile\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useFileUpload.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useGenericFormHandler.ts":
/*!********************************************!*\
  !*** ./src/hooks/useGenericFormHandler.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGenericFormHandler: () => (/* binding */ useGenericFormHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useGenericFormHandler = ({ formData, setFormData, errors, setErrors, validationRules = {} })=>{\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleChange]\": (e)=>{\n            const { name, value } = e.target;\n            setFormData({\n                \"useGenericFormHandler.useCallback[handleChange]\": (prev)=>({\n                        ...prev,\n                        [name]: value\n                    })\n            }[\"useGenericFormHandler.useCallback[handleChange]\"]);\n            // Clear error when user starts typing\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleChange]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[handleChange]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleChange]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const handleCheckboxChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (e)=>{\n            const { name, checked } = e.target;\n            setFormData({\n                \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (prev)=>({\n                        ...prev,\n                        [name]: checked\n                    })\n            }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"]);\n            // Clear error when user changes checkbox\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleBlur]\": (e)=>{\n            const { name, value } = e.target;\n            const fieldName = name;\n            // Run validation if rule exists\n            const validationRule = validationRules[fieldName];\n            if (validationRule) {\n                const error = validationRule(value, formData);\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleBlur]\": (prev)=>({\n                            ...prev,\n                            [name]: error\n                        })\n                }[\"useGenericFormHandler.useCallback[handleBlur]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleBlur]\"], [\n        validationRules,\n        formData,\n        setErrors\n    ]);\n    const setFieldValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[setFieldValue]\": (name, value)=>{\n            setFormData({\n                \"useGenericFormHandler.useCallback[setFieldValue]\": (prev)=>({\n                        ...prev,\n                        [name]: value\n                    })\n            }[\"useGenericFormHandler.useCallback[setFieldValue]\"]);\n            // Clear error when programmatically setting value\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[setFieldValue]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[setFieldValue]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[setFieldValue]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const validateField = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[validateField]\": (name, value)=>{\n            const validationRule = validationRules[name];\n            if (validationRule) {\n                return validationRule(value, formData);\n            }\n            return '';\n        }\n    }[\"useGenericFormHandler.useCallback[validateField]\"], [\n        validationRules,\n        formData\n    ]);\n    const clearFieldError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[clearFieldError]\": (name)=>{\n            setErrors({\n                \"useGenericFormHandler.useCallback[clearFieldError]\": (prev)=>({\n                        ...prev,\n                        [name]: ''\n                    })\n            }[\"useGenericFormHandler.useCallback[clearFieldError]\"]);\n        }\n    }[\"useGenericFormHandler.useCallback[clearFieldError]\"], [\n        setErrors\n    ]);\n    return {\n        handleChange,\n        handleCheckboxChange,\n        handleBlur,\n        setFieldValue,\n        validateField,\n        clearFieldError\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlR2VuZXJpY0Zvcm1IYW5kbGVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQWNwQyxNQUFNQyx3QkFBd0IsQ0FBZ0MsRUFDbkVDLFFBQVEsRUFDUkMsV0FBVyxFQUNYQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsa0JBQWtCLENBQUMsQ0FBQyxFQUNZO0lBQ2hDLE1BQU1DLGVBQWVQLGtEQUFXQTsyREFDOUIsQ0FDRVE7WUFJQSxNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUdGLEVBQUVHLE1BQU07WUFDaENSO21FQUFZLENBQUNTLE9BQVU7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRSxDQUFDSCxLQUFLLEVBQUVDO29CQUFNOztZQUVoRCxzQ0FBc0M7WUFDdEMsSUFBSU4sTUFBTSxDQUFDSyxLQUFnQixFQUFFO2dCQUMzQko7dUVBQVUsQ0FBQ08sT0FBVTs0QkFBRSxHQUFHQSxJQUFJOzRCQUFFLENBQUNILEtBQUssRUFBRTt3QkFBRzs7WUFDN0M7UUFDRjswREFDQTtRQUFDTjtRQUFhQztRQUFRQztLQUFVO0lBR2xDLE1BQU1RLHVCQUF1QmIsa0RBQVdBO21FQUN0QyxDQUFDUTtZQUNDLE1BQU0sRUFBRUMsSUFBSSxFQUFFSyxPQUFPLEVBQUUsR0FBR04sRUFBRUcsTUFBTTtZQUNsQ1I7MkVBQVksQ0FBQ1MsT0FBVTt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFLENBQUNILEtBQUssRUFBRUs7b0JBQVE7O1lBRWxELHlDQUF5QztZQUN6QyxJQUFJVixNQUFNLENBQUNLLEtBQWdCLEVBQUU7Z0JBQzNCSjsrRUFBVSxDQUFDTyxPQUFVOzRCQUFFLEdBQUdBLElBQUk7NEJBQUUsQ0FBQ0gsS0FBSyxFQUFFO3dCQUFHOztZQUM3QztRQUNGO2tFQUNBO1FBQUNOO1FBQWFDO1FBQVFDO0tBQVU7SUFHbEMsTUFBTVUsYUFBYWYsa0RBQVdBO3lEQUM1QixDQUNFUTtZQUlBLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR0YsRUFBRUcsTUFBTTtZQUNoQyxNQUFNSyxZQUFZUDtZQUVsQixnQ0FBZ0M7WUFDaEMsTUFBTVEsaUJBQWlCWCxlQUFlLENBQUNVLFVBQVU7WUFDakQsSUFBSUMsZ0JBQWdCO2dCQUNsQixNQUFNQyxRQUFRRCxlQUFlUCxPQUFPUjtnQkFDcENHO3FFQUFVLENBQUNPLE9BQVU7NEJBQUUsR0FBR0EsSUFBSTs0QkFBRSxDQUFDSCxLQUFLLEVBQUVTO3dCQUFNOztZQUNoRDtRQUNGO3dEQUNBO1FBQUNaO1FBQWlCSjtRQUFVRztLQUFVO0lBR3hDLE1BQU1jLGdCQUFnQm5CLGtEQUFXQTs0REFDL0IsQ0FBQ1MsTUFBZUM7WUFDZFA7b0VBQVksQ0FBQ1MsT0FBVTt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFLENBQUNILEtBQUssRUFBRUM7b0JBQU07O1lBRWhELGtEQUFrRDtZQUNsRCxJQUFJTixNQUFNLENBQUNLLEtBQUssRUFBRTtnQkFDaEJKO3dFQUFVLENBQUNPLE9BQVU7NEJBQUUsR0FBR0EsSUFBSTs0QkFBRSxDQUFDSCxLQUFLLEVBQUU7d0JBQUc7O1lBQzdDO1FBQ0Y7MkRBQ0E7UUFBQ047UUFBYUM7UUFBUUM7S0FBVTtJQUdsQyxNQUFNZSxnQkFBZ0JwQixrREFBV0E7NERBQy9CLENBQUNTLE1BQWVDO1lBQ2QsTUFBTU8saUJBQWlCWCxlQUFlLENBQUNHLEtBQUs7WUFDNUMsSUFBSVEsZ0JBQWdCO2dCQUNsQixPQUFPQSxlQUFlUCxPQUFPUjtZQUMvQjtZQUNBLE9BQU87UUFDVDsyREFDQTtRQUFDSTtRQUFpQko7S0FBUztJQUc3QixNQUFNbUIsa0JBQWtCckIsa0RBQVdBOzhEQUNqQyxDQUFDUztZQUNDSjtzRUFBVSxDQUFDTyxPQUFVO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUsQ0FBQ0gsS0FBSyxFQUFFO29CQUFHOztRQUM3Qzs2REFDQTtRQUFDSjtLQUFVO0lBR2IsT0FBTztRQUNMRTtRQUNBTTtRQUNBRTtRQUNBSTtRQUNBQztRQUNBQztJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXHNyY1xcaG9va3NcXHVzZUdlbmVyaWNGb3JtSGFuZGxlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XHJcblxyXG50eXBlIEZvcm1FcnJvcnM8VD4gPSBSZWNvcmQ8a2V5b2YgVCwgc3RyaW5nPjtcclxuXHJcbmludGVyZmFjZSBVc2VHZW5lcmljRm9ybUhhbmRsZXJPcHRpb25zPFQ+IHtcclxuICBmb3JtRGF0YTogVDtcclxuICBzZXRGb3JtRGF0YTogUmVhY3QuRGlzcGF0Y2g8UmVhY3QuU2V0U3RhdGVBY3Rpb248VD4+O1xyXG4gIGVycm9yczogRm9ybUVycm9yczxUPjtcclxuICBzZXRFcnJvcnM6IFJlYWN0LkRpc3BhdGNoPFJlYWN0LlNldFN0YXRlQWN0aW9uPEZvcm1FcnJvcnM8VD4+PjtcclxuICB2YWxpZGF0aW9uUnVsZXM/OiBQYXJ0aWFsPFxyXG4gICAgUmVjb3JkPGtleW9mIFQsICh2YWx1ZTogYW55LCBmb3JtRGF0YT86IFQpID0+IHN0cmluZz5cclxuICA+O1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXNlR2VuZXJpY0Zvcm1IYW5kbGVyID0gPFQgZXh0ZW5kcyBSZWNvcmQ8c3RyaW5nLCBhbnk+Pih7XHJcbiAgZm9ybURhdGEsXHJcbiAgc2V0Rm9ybURhdGEsXHJcbiAgZXJyb3JzLFxyXG4gIHNldEVycm9ycyxcclxuICB2YWxpZGF0aW9uUnVsZXMgPSB7fSxcclxufTogVXNlR2VuZXJpY0Zvcm1IYW5kbGVyT3B0aW9uczxUPikgPT4ge1xyXG4gIGNvbnN0IGhhbmRsZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxyXG4gICAgKFxyXG4gICAgICBlOiBSZWFjdC5DaGFuZ2VFdmVudDxcclxuICAgICAgICBIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFRleHRBcmVhRWxlbWVudCB8IEhUTUxTZWxlY3RFbGVtZW50XHJcbiAgICAgID5cclxuICAgICkgPT4ge1xyXG4gICAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldDtcclxuICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7IC4uLnByZXYsIFtuYW1lXTogdmFsdWUgfSkpO1xyXG5cclxuICAgICAgLy8gQ2xlYXIgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcclxuICAgICAgaWYgKGVycm9yc1tuYW1lIGFzIGtleW9mIFRdKSB7XHJcbiAgICAgICAgc2V0RXJyb3JzKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBbbmFtZV06ICcnIH0pKTtcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIFtzZXRGb3JtRGF0YSwgZXJyb3JzLCBzZXRFcnJvcnNdXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hlY2tib3hDaGFuZ2UgPSB1c2VDYWxsYmFjayhcclxuICAgIChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgICBjb25zdCB7IG5hbWUsIGNoZWNrZWQgfSA9IGUudGFyZ2V0O1xyXG4gICAgICBzZXRGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgW25hbWVdOiBjaGVja2VkIH0pKTtcclxuXHJcbiAgICAgIC8vIENsZWFyIGVycm9yIHdoZW4gdXNlciBjaGFuZ2VzIGNoZWNrYm94XHJcbiAgICAgIGlmIChlcnJvcnNbbmFtZSBhcyBrZXlvZiBUXSkge1xyXG4gICAgICAgIHNldEVycm9ycygocHJldikgPT4gKHsgLi4ucHJldiwgW25hbWVdOiAnJyB9KSk7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBbc2V0Rm9ybURhdGEsIGVycm9ycywgc2V0RXJyb3JzXVxyXG4gICk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJsdXIgPSB1c2VDYWxsYmFjayhcclxuICAgIChcclxuICAgICAgZTogUmVhY3QuRm9jdXNFdmVudDxcclxuICAgICAgICBIVE1MSW5wdXRFbGVtZW50IHwgSFRNTFRleHRBcmVhRWxlbWVudCB8IEhUTUxTZWxlY3RFbGVtZW50XHJcbiAgICAgID5cclxuICAgICkgPT4ge1xyXG4gICAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldDtcclxuICAgICAgY29uc3QgZmllbGROYW1lID0gbmFtZSBhcyBrZXlvZiBUO1xyXG5cclxuICAgICAgLy8gUnVuIHZhbGlkYXRpb24gaWYgcnVsZSBleGlzdHNcclxuICAgICAgY29uc3QgdmFsaWRhdGlvblJ1bGUgPSB2YWxpZGF0aW9uUnVsZXNbZmllbGROYW1lXTtcclxuICAgICAgaWYgKHZhbGlkYXRpb25SdWxlKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3IgPSB2YWxpZGF0aW9uUnVsZSh2YWx1ZSwgZm9ybURhdGEpO1xyXG4gICAgICAgIHNldEVycm9ycygocHJldikgPT4gKHsgLi4ucHJldiwgW25hbWVdOiBlcnJvciB9KSk7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBbdmFsaWRhdGlvblJ1bGVzLCBmb3JtRGF0YSwgc2V0RXJyb3JzXVxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHNldEZpZWxkVmFsdWUgPSB1c2VDYWxsYmFjayhcclxuICAgIChuYW1lOiBrZXlvZiBULCB2YWx1ZTogVFtrZXlvZiBUXSkgPT4ge1xyXG4gICAgICBzZXRGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgW25hbWVdOiB2YWx1ZSB9KSk7XHJcblxyXG4gICAgICAvLyBDbGVhciBlcnJvciB3aGVuIHByb2dyYW1tYXRpY2FsbHkgc2V0dGluZyB2YWx1ZVxyXG4gICAgICBpZiAoZXJyb3JzW25hbWVdKSB7XHJcbiAgICAgICAgc2V0RXJyb3JzKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBbbmFtZV06ICcnIH0pKTtcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIFtzZXRGb3JtRGF0YSwgZXJyb3JzLCBzZXRFcnJvcnNdXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVGaWVsZCA9IHVzZUNhbGxiYWNrKFxyXG4gICAgKG5hbWU6IGtleW9mIFQsIHZhbHVlOiBUW2tleW9mIFRdKTogc3RyaW5nID0+IHtcclxuICAgICAgY29uc3QgdmFsaWRhdGlvblJ1bGUgPSB2YWxpZGF0aW9uUnVsZXNbbmFtZV07XHJcbiAgICAgIGlmICh2YWxpZGF0aW9uUnVsZSkge1xyXG4gICAgICAgIHJldHVybiB2YWxpZGF0aW9uUnVsZSh2YWx1ZSwgZm9ybURhdGEpO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiAnJztcclxuICAgIH0sXHJcbiAgICBbdmFsaWRhdGlvblJ1bGVzLCBmb3JtRGF0YV1cclxuICApO1xyXG5cclxuICBjb25zdCBjbGVhckZpZWxkRXJyb3IgPSB1c2VDYWxsYmFjayhcclxuICAgIChuYW1lOiBrZXlvZiBUKSA9PiB7XHJcbiAgICAgIHNldEVycm9ycygocHJldikgPT4gKHsgLi4ucHJldiwgW25hbWVdOiAnJyB9KSk7XHJcbiAgICB9LFxyXG4gICAgW3NldEVycm9yc11cclxuICApO1xyXG5cclxuICByZXR1cm4ge1xyXG4gICAgaGFuZGxlQ2hhbmdlLFxyXG4gICAgaGFuZGxlQ2hlY2tib3hDaGFuZ2UsXHJcbiAgICBoYW5kbGVCbHVyLFxyXG4gICAgc2V0RmllbGRWYWx1ZSxcclxuICAgIHZhbGlkYXRlRmllbGQsXHJcbiAgICBjbGVhckZpZWxkRXJyb3IsXHJcbiAgfTtcclxufTtcclxuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlR2VuZXJpY0Zvcm1IYW5kbGVyIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImVycm9ycyIsInNldEVycm9ycyIsInZhbGlkYXRpb25SdWxlcyIsImhhbmRsZUNoYW5nZSIsImUiLCJuYW1lIiwidmFsdWUiLCJ0YXJnZXQiLCJwcmV2IiwiaGFuZGxlQ2hlY2tib3hDaGFuZ2UiLCJjaGVja2VkIiwiaGFuZGxlQmx1ciIsImZpZWxkTmFtZSIsInZhbGlkYXRpb25SdWxlIiwiZXJyb3IiLCJzZXRGaWVsZFZhbHVlIiwidmFsaWRhdGVGaWVsZCIsImNsZWFyRmllbGRFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useGenericFormHandler.ts\n");

/***/ })

};
;