'use client';
import React, { useState } from 'react';

const NotificationTester = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testSystemNotification = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/test-single`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`✅ Success: Created notification ID ${data.data.notificationId} with type "${data.data.type}"`);
      } else {
        setResult(`❌ Error: ${data.message}`);
      }
    } catch (error) {
      setResult(`❌ Network Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSystemBroadcast = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/test-system`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`✅ Success: Broadcast to ${data.data.recipientCount} users`);
      } else {
        setResult(`❌ Error: ${data.message}`);
      }
    } catch (error) {
      setResult(`❌ Network Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const debugNotificationTypes = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/debug-types`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`📊 Debug Info:\nType Counts: ${JSON.stringify(data.data.typeCounts, null, 2)}\nRecent: ${JSON.stringify(data.data.recentNotifications.slice(0, 3), null, 2)}`);
      } else {
        setResult(`❌ Error: ${data.message}`);
      }
    } catch (error) {
      setResult(`❌ Network Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">🧪 Notification System Tester</h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={testSystemNotification}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 mr-2"
        >
          {loading ? 'Testing...' : 'Test Single System Notification'}
        </button>
        
        <button
          onClick={testSystemBroadcast}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 mr-2"
        >
          {loading ? 'Testing...' : 'Test System Broadcast'}
        </button>
        
        <button
          onClick={debugNotificationTypes}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Debug Notification Types'}
        </button>
      </div>
      
      {result && (
        <div className="p-3 bg-white rounded border">
          <pre className="text-sm whitespace-pre-wrap">{result}</pre>
        </div>
      )}
    </div>
  );
};

export default NotificationTester;
