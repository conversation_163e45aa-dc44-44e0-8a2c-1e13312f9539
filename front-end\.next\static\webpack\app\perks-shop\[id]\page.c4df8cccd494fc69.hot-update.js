"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx":
/*!************************************************!*\
  !*** ./src/contexts/ChatModalStateContext.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatModalStateProvider: () => (/* binding */ ChatModalStateProvider),\n/* harmony export */   useChatModalState: () => (/* binding */ useChatModalState)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatModalStateProvider,useChatModalState auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst ChatModalStateContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatModalStateProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [chatModalStates, setChatModalStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const { updateModal, getModalById } = (0,_GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal)();\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // Keep ref in sync with state\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ChatModalStateProvider.useEffect\": ()=>{\n            stateRef.current = chatModalStates;\n        }\n    }[\"ChatModalStateProvider.useEffect\"], [\n        chatModalStates\n    ]);\n    const getChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[getChatModalState]\": (chatRoomId)=>{\n            return stateRef.current.get(chatRoomId);\n        }\n    }[\"ChatModalStateProvider.useCallback[getChatModalState]\"], []);\n    const updateChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateChatModalState]\": (chatRoomId, updates)=>{\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating state for chatRoom \".concat(chatRoomId, \":\"), updates);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[updateChatModalState]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    const currentState = newMap.get(chatRoomId);\n                    if (currentState) {\n                        const updatedState = {\n                            ...currentState,\n                            ...updates,\n                            lastUpdated: Date.now()\n                        };\n                        newMap.set(chatRoomId, updatedState);\n                        console.log(\"✅ [ChatModalStateContext] Updated state for \".concat(chatRoomId, \":\"), updatedState);\n                    } else {\n                        console.log(\"⚠️ [ChatModalStateContext] No state found for chatRoom \".concat(chatRoomId));\n                    }\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"], []);\n    const registerChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[registerChatModal]\": (chatRoomId, initialState)=>{\n            console.log(\"\\uD83D\\uDCDD [ChatModalStateContext] Registering chatRoom \".concat(chatRoomId, \":\"), initialState);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[registerChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.set(chatRoomId, {\n                        ...initialState,\n                        lastUpdated: Date.now()\n                    });\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[registerChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[registerChatModal]\"], []);\n    const unregisterChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (chatRoomId)=>{\n            console.log(\"\\uD83D\\uDDD1️ [ChatModalStateContext] Unregistering chatRoom \".concat(chatRoomId));\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.delete(chatRoomId);\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"], []);\n    const updateModalProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateModalProps]\": (chatRoomId, newProps)=>{\n            const modalId = \"chat-modal-\".concat(chatRoomId);\n            const existingModal = getModalById(modalId);\n            if (!existingModal) {\n                console.log(\"⚠️ [ChatModalStateContext] No modal found with id \".concat(modalId));\n                return false;\n            }\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating modal props for \".concat(modalId, \":\"), newProps);\n            // Update the modal component with new props\n            const updatedComponent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(existingModal.component, {\n                ...existingModal.component.props || {},\n                ...newProps,\n                key: \"\".concat(modalId, \"-\").concat(Date.now()) // Force re-render with new props\n            });\n            return updateModal(modalId, {\n                component: updatedComponent,\n                updateProps: newProps\n            });\n        }\n    }[\"ChatModalStateProvider.useCallback[updateModalProps]\"], [\n        getModalById,\n        updateModal\n    ]);\n    const value = {\n        getChatModalState,\n        updateChatModalState,\n        registerChatModal,\n        unregisterChatModal,\n        updateModalProps\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatModalStateContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\ChatModalStateContext.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModalStateProvider, \"Pw3fFegQKADjC2uhD+eDqlRHPds=\", false, function() {\n    return [\n        _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal\n    ];\n});\n_c = ChatModalStateProvider;\nconst useChatModalState = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatModalStateContext);\n    if (!context) {\n        throw new Error('useChatModalState must be used within a ChatModalStateProvider');\n    }\n    return context;\n};\n_s1(useChatModalState, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatModalStateProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9DaGF0TW9kYWxTdGF0ZUNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1HO0FBQzdDO0FBb0J0RCxNQUFNTyxzQ0FBd0JOLG9EQUFhQSxDQUFtQztBQUV2RSxNQUFNTyx5QkFBa0U7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQzFGLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR1IsK0NBQVFBLENBQThCLElBQUlTO0lBQ3hGLE1BQU0sRUFBRUMsV0FBVyxFQUFFQyxZQUFZLEVBQUUsR0FBR1IsbUVBQWNBO0lBQ3BELE1BQU1TLFdBQVdWLDZDQUFNQSxDQUE4QixJQUFJTztJQUV6RCw4QkFBOEI7SUFDOUJaLHNEQUFlOzRDQUFDO1lBQ2RlLFNBQVNFLE9BQU8sR0FBR1A7UUFDckI7MkNBQUc7UUFBQ0E7S0FBZ0I7SUFFcEIsTUFBTVEsb0JBQW9CZCxrREFBV0E7aUVBQUMsQ0FBQ2U7WUFDckMsT0FBT0osU0FBU0UsT0FBTyxDQUFDRyxHQUFHLENBQUNEO1FBQzlCO2dFQUFHLEVBQUU7SUFFTCxNQUFNRSx1QkFBdUJqQixrREFBV0E7b0VBQUMsQ0FBQ2UsWUFBb0JHO1lBQzVEQyxRQUFRQyxHQUFHLENBQUMsb0VBQXFFLE9BQVhMLFlBQVcsTUFBSUc7WUFFckZYOzRFQUFtQmMsQ0FBQUE7b0JBQ2pCLE1BQU1DLFNBQVMsSUFBSWQsSUFBSWE7b0JBQ3ZCLE1BQU1FLGVBQWVELE9BQU9OLEdBQUcsQ0FBQ0Q7b0JBRWhDLElBQUlRLGNBQWM7d0JBQ2hCLE1BQU1DLGVBQWU7NEJBQ25CLEdBQUdELFlBQVk7NEJBQ2YsR0FBR0wsT0FBTzs0QkFDVk8sYUFBYUMsS0FBS0MsR0FBRzt3QkFDdkI7d0JBQ0FMLE9BQU9NLEdBQUcsQ0FBQ2IsWUFBWVM7d0JBQ3ZCTCxRQUFRQyxHQUFHLENBQUMsK0NBQTBELE9BQVhMLFlBQVcsTUFBSVM7b0JBQzVFLE9BQU87d0JBQ0xMLFFBQVFDLEdBQUcsQ0FBQywwREFBcUUsT0FBWEw7b0JBQ3hFO29CQUVBLE9BQU9PO2dCQUNUOztRQUNGO21FQUFHLEVBQUU7SUFFTCxNQUFNTyxvQkFBb0I3QixrREFBV0E7aUVBQUMsQ0FBQ2UsWUFBb0JlO1lBQ3pEWCxRQUFRQyxHQUFHLENBQUMsNkRBQThELE9BQVhMLFlBQVcsTUFBSWU7WUFDOUV2Qjt5RUFBbUJjLENBQUFBO29CQUNqQixNQUFNQyxTQUFTLElBQUlkLElBQUlhO29CQUN2QkMsT0FBT00sR0FBRyxDQUFDYixZQUFZO3dCQUFFLEdBQUdlLFlBQVk7d0JBQUVMLGFBQWFDLEtBQUtDLEdBQUc7b0JBQUc7b0JBQ2xFLE9BQU9MO2dCQUNUOztRQUNGO2dFQUFHLEVBQUU7SUFFTCxNQUFNUyxzQkFBc0IvQixrREFBV0E7bUVBQUMsQ0FBQ2U7WUFDdkNJLFFBQVFDLEdBQUcsQ0FBQyxnRUFBaUUsT0FBWEw7WUFDbEVSOzJFQUFtQmMsQ0FBQUE7b0JBQ2pCLE1BQU1DLFNBQVMsSUFBSWQsSUFBSWE7b0JBQ3ZCQyxPQUFPVSxNQUFNLENBQUNqQjtvQkFDZCxPQUFPTztnQkFDVDs7UUFDRjtrRUFBRyxFQUFFO0lBRUwsTUFBTVcsbUJBQW1CakMsa0RBQVdBO2dFQUFDLENBQUNlLFlBQW9CbUI7WUFDeEQsTUFBTUMsVUFBVSxjQUF5QixPQUFYcEI7WUFDOUIsTUFBTXFCLGdCQUFnQjFCLGFBQWF5QjtZQUVuQyxJQUFJLENBQUNDLGVBQWU7Z0JBQ2xCakIsUUFBUUMsR0FBRyxDQUFDLHFEQUE2RCxPQUFSZTtnQkFDakUsT0FBTztZQUNUO1lBRUFoQixRQUFRQyxHQUFHLENBQUMsaUVBQStELE9BQVJlLFNBQVEsTUFBSUQ7WUFFL0UsNENBQTRDO1lBQzVDLE1BQU1HLGlDQUFtQnpDLHlEQUFrQixDQUN6Q3dDLGNBQWNHLFNBQVMsRUFDdkI7Z0JBQ0UsR0FBSSxjQUFlQSxTQUFTLENBQXdCQyxLQUFLLElBQUksQ0FBQyxDQUFDO2dCQUMvRCxHQUFHTixRQUFRO2dCQUNYTyxLQUFLLEdBQWNmLE9BQVhTLFNBQVEsS0FBYyxPQUFYVCxLQUFLQyxHQUFHLElBQUssaUNBQWlDO1lBQ25FO1lBR0YsT0FBT2xCLFlBQVkwQixTQUFTO2dCQUMxQkksV0FBV0Y7Z0JBQ1hLLGFBQWFSO1lBQ2Y7UUFDRjsrREFBRztRQUFDeEI7UUFBY0Q7S0FBWTtJQUU5QixNQUFNa0MsUUFBbUM7UUFDdkM3QjtRQUNBRztRQUNBWTtRQUNBRTtRQUNBRTtJQUNGO0lBRUEscUJBQ0UsOERBQUM5QixzQkFBc0J5QyxRQUFRO1FBQUNELE9BQU9BO2tCQUNwQ3RDOzs7Ozs7QUFHUCxFQUFFO0dBL0ZXRDs7UUFFMkJGLCtEQUFjQTs7O0tBRnpDRTtBQWlHTixNQUFNeUMsb0JBQW9COztJQUMvQixNQUFNQyxVQUFVaEQsaURBQVVBLENBQUNLO0lBQzNCLElBQUksQ0FBQzJDLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUU7SUFOV0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxzcmNcXGNvbnRleHRzXFxDaGF0TW9kYWxTdGF0ZUNvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VHbG9iYWxNb2RhbCB9IGZyb20gJy4vR2xvYmFsTW9kYWxDb250ZXh0JztcbmltcG9ydCB7IHVzZVNvY2tldCB9IGZyb20gJy4vU29ja2V0UHJvdmlkZXInO1xuXG5pbnRlcmZhY2UgQ2hhdE1vZGFsU3RhdGUge1xuICBjaGF0Um9vbUlkOiBzdHJpbmc7XG4gIGFjdGl2ZVRyYWRlPzogYW55O1xuICBjdXJyZW50VHJhZGVTdGF0dXM/OiBzdHJpbmc7XG4gIGlzT3BlcmF0aW9uSW5Qcm9ncmVzcz86IGJvb2xlYW47XG4gIGxhc3RVcGRhdGVkPzogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQ2hhdE1vZGFsU3RhdGVDb250ZXh0VHlwZSB7XG4gIGdldENoYXRNb2RhbFN0YXRlOiAoY2hhdFJvb21JZDogc3RyaW5nKSA9PiBDaGF0TW9kYWxTdGF0ZSB8IHVuZGVmaW5lZDtcbiAgdXBkYXRlQ2hhdE1vZGFsU3RhdGU6IChjaGF0Um9vbUlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8Q2hhdE1vZGFsU3RhdGU+KSA9PiB2b2lkO1xuICByZWdpc3RlckNoYXRNb2RhbDogKGNoYXRSb29tSWQ6IHN0cmluZywgaW5pdGlhbFN0YXRlOiBDaGF0TW9kYWxTdGF0ZSkgPT4gdm9pZDtcbiAgdW5yZWdpc3RlckNoYXRNb2RhbDogKGNoYXRSb29tSWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgdXBkYXRlTW9kYWxQcm9wczogKGNoYXRSb29tSWQ6IHN0cmluZywgbmV3UHJvcHM6IGFueSkgPT4gYm9vbGVhbjtcbiAgc3luY1RyYWRlU3RhdHVzVXBkYXRlOiAodHJhZGVJZDogc3RyaW5nIHwgbnVtYmVyLCBzdGF0dXM6IHN0cmluZykgPT4gdm9pZDtcbn1cblxuY29uc3QgQ2hhdE1vZGFsU3RhdGVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxDaGF0TW9kYWxTdGF0ZUNvbnRleHRUeXBlIHwgbnVsbD4obnVsbCk7XG5cbmV4cG9ydCBjb25zdCBDaGF0TW9kYWxTdGF0ZVByb3ZpZGVyOiBSZWFjdC5GQzx7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfT4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFtjaGF0TW9kYWxTdGF0ZXMsIHNldENoYXRNb2RhbFN0YXRlc10gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCBDaGF0TW9kYWxTdGF0ZT4+KG5ldyBNYXAoKSk7XG4gIGNvbnN0IHsgdXBkYXRlTW9kYWwsIGdldE1vZGFsQnlJZCB9ID0gdXNlR2xvYmFsTW9kYWwoKTtcbiAgY29uc3Qgc3RhdGVSZWYgPSB1c2VSZWY8TWFwPHN0cmluZywgQ2hhdE1vZGFsU3RhdGU+PihuZXcgTWFwKCkpO1xuXG4gIC8vIEtlZXAgcmVmIGluIHN5bmMgd2l0aCBzdGF0ZVxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIHN0YXRlUmVmLmN1cnJlbnQgPSBjaGF0TW9kYWxTdGF0ZXM7XG4gIH0sIFtjaGF0TW9kYWxTdGF0ZXNdKTtcblxuICBjb25zdCBnZXRDaGF0TW9kYWxTdGF0ZSA9IHVzZUNhbGxiYWNrKChjaGF0Um9vbUlkOiBzdHJpbmcpOiBDaGF0TW9kYWxTdGF0ZSB8IHVuZGVmaW5lZCA9PiB7XG4gICAgcmV0dXJuIHN0YXRlUmVmLmN1cnJlbnQuZ2V0KGNoYXRSb29tSWQpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgdXBkYXRlQ2hhdE1vZGFsU3RhdGUgPSB1c2VDYWxsYmFjaygoY2hhdFJvb21JZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPENoYXRNb2RhbFN0YXRlPikgPT4ge1xuICAgIGNvbnNvbGUubG9nKGDwn5SEIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIFVwZGF0aW5nIHN0YXRlIGZvciBjaGF0Um9vbSAke2NoYXRSb29tSWR9OmAsIHVwZGF0ZXMpO1xuICAgIFxuICAgIHNldENoYXRNb2RhbFN0YXRlcyhwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld01hcCA9IG5ldyBNYXAocHJldik7XG4gICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBuZXdNYXAuZ2V0KGNoYXRSb29tSWQpO1xuICAgICAgXG4gICAgICBpZiAoY3VycmVudFN0YXRlKSB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRTdGF0ZSA9IHtcbiAgICAgICAgICAuLi5jdXJyZW50U3RhdGUsXG4gICAgICAgICAgLi4udXBkYXRlcyxcbiAgICAgICAgICBsYXN0VXBkYXRlZDogRGF0ZS5ub3coKVxuICAgICAgICB9O1xuICAgICAgICBuZXdNYXAuc2V0KGNoYXRSb29tSWQsIHVwZGF0ZWRTdGF0ZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gVXBkYXRlZCBzdGF0ZSBmb3IgJHtjaGF0Um9vbUlkfTpgLCB1cGRhdGVkU3RhdGUpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coYOKaoO+4jyBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBObyBzdGF0ZSBmb3VuZCBmb3IgY2hhdFJvb20gJHtjaGF0Um9vbUlkfWApO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gbmV3TWFwO1xuICAgIH0pO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgcmVnaXN0ZXJDaGF0TW9kYWwgPSB1c2VDYWxsYmFjaygoY2hhdFJvb21JZDogc3RyaW5nLCBpbml0aWFsU3RhdGU6IENoYXRNb2RhbFN0YXRlKSA9PiB7XG4gICAgY29uc29sZS5sb2coYPCfk50gW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gUmVnaXN0ZXJpbmcgY2hhdFJvb20gJHtjaGF0Um9vbUlkfTpgLCBpbml0aWFsU3RhdGUpO1xuICAgIHNldENoYXRNb2RhbFN0YXRlcyhwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld01hcCA9IG5ldyBNYXAocHJldik7XG4gICAgICBuZXdNYXAuc2V0KGNoYXRSb29tSWQsIHsgLi4uaW5pdGlhbFN0YXRlLCBsYXN0VXBkYXRlZDogRGF0ZS5ub3coKSB9KTtcbiAgICAgIHJldHVybiBuZXdNYXA7XG4gICAgfSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB1bnJlZ2lzdGVyQ2hhdE1vZGFsID0gdXNlQ2FsbGJhY2soKGNoYXRSb29tSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKGDwn5eR77iPIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIFVucmVnaXN0ZXJpbmcgY2hhdFJvb20gJHtjaGF0Um9vbUlkfWApO1xuICAgIHNldENoYXRNb2RhbFN0YXRlcyhwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld01hcCA9IG5ldyBNYXAocHJldik7XG4gICAgICBuZXdNYXAuZGVsZXRlKGNoYXRSb29tSWQpO1xuICAgICAgcmV0dXJuIG5ld01hcDtcbiAgICB9KTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHVwZGF0ZU1vZGFsUHJvcHMgPSB1c2VDYWxsYmFjaygoY2hhdFJvb21JZDogc3RyaW5nLCBuZXdQcm9wczogYW55KTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgbW9kYWxJZCA9IGBjaGF0LW1vZGFsLSR7Y2hhdFJvb21JZH1gO1xuICAgIGNvbnN0IGV4aXN0aW5nTW9kYWwgPSBnZXRNb2RhbEJ5SWQobW9kYWxJZCk7XG4gICAgXG4gICAgaWYgKCFleGlzdGluZ01vZGFsKSB7XG4gICAgICBjb25zb2xlLmxvZyhg4pqg77iPIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIE5vIG1vZGFsIGZvdW5kIHdpdGggaWQgJHttb2RhbElkfWApO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDwn5SEIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIFVwZGF0aW5nIG1vZGFsIHByb3BzIGZvciAke21vZGFsSWR9OmAsIG5ld1Byb3BzKTtcbiAgICBcbiAgICAvLyBVcGRhdGUgdGhlIG1vZGFsIGNvbXBvbmVudCB3aXRoIG5ldyBwcm9wc1xuICAgIGNvbnN0IHVwZGF0ZWRDb21wb25lbnQgPSBSZWFjdC5jbG9uZUVsZW1lbnQoXG4gICAgICBleGlzdGluZ01vZGFsLmNvbXBvbmVudCBhcyBSZWFjdC5SZWFjdEVsZW1lbnQsXG4gICAgICB7XG4gICAgICAgIC4uLigoZXhpc3RpbmdNb2RhbC5jb21wb25lbnQgYXMgUmVhY3QuUmVhY3RFbGVtZW50KS5wcm9wcyB8fCB7fSksXG4gICAgICAgIC4uLm5ld1Byb3BzLFxuICAgICAgICBrZXk6IGAke21vZGFsSWR9LSR7RGF0ZS5ub3coKX1gIC8vIEZvcmNlIHJlLXJlbmRlciB3aXRoIG5ldyBwcm9wc1xuICAgICAgfVxuICAgICk7XG5cbiAgICByZXR1cm4gdXBkYXRlTW9kYWwobW9kYWxJZCwge1xuICAgICAgY29tcG9uZW50OiB1cGRhdGVkQ29tcG9uZW50LFxuICAgICAgdXBkYXRlUHJvcHM6IG5ld1Byb3BzXG4gICAgfSk7XG4gIH0sIFtnZXRNb2RhbEJ5SWQsIHVwZGF0ZU1vZGFsXSk7XG5cbiAgY29uc3QgdmFsdWU6IENoYXRNb2RhbFN0YXRlQ29udGV4dFR5cGUgPSB7XG4gICAgZ2V0Q2hhdE1vZGFsU3RhdGUsXG4gICAgdXBkYXRlQ2hhdE1vZGFsU3RhdGUsXG4gICAgcmVnaXN0ZXJDaGF0TW9kYWwsXG4gICAgdW5yZWdpc3RlckNoYXRNb2RhbCxcbiAgICB1cGRhdGVNb2RhbFByb3BzXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2hhdE1vZGFsU3RhdGVDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9DaGF0TW9kYWxTdGF0ZUNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgY29uc3QgdXNlQ2hhdE1vZGFsU3RhdGUgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KENoYXRNb2RhbFN0YXRlQ29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQ2hhdE1vZGFsU3RhdGUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIENoYXRNb2RhbFN0YXRlUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VHbG9iYWxNb2RhbCIsIkNoYXRNb2RhbFN0YXRlQ29udGV4dCIsIkNoYXRNb2RhbFN0YXRlUHJvdmlkZXIiLCJjaGlsZHJlbiIsImNoYXRNb2RhbFN0YXRlcyIsInNldENoYXRNb2RhbFN0YXRlcyIsIk1hcCIsInVwZGF0ZU1vZGFsIiwiZ2V0TW9kYWxCeUlkIiwic3RhdGVSZWYiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwiZ2V0Q2hhdE1vZGFsU3RhdGUiLCJjaGF0Um9vbUlkIiwiZ2V0IiwidXBkYXRlQ2hhdE1vZGFsU3RhdGUiLCJ1cGRhdGVzIiwiY29uc29sZSIsImxvZyIsInByZXYiLCJuZXdNYXAiLCJjdXJyZW50U3RhdGUiLCJ1cGRhdGVkU3RhdGUiLCJsYXN0VXBkYXRlZCIsIkRhdGUiLCJub3ciLCJzZXQiLCJyZWdpc3RlckNoYXRNb2RhbCIsImluaXRpYWxTdGF0ZSIsInVucmVnaXN0ZXJDaGF0TW9kYWwiLCJkZWxldGUiLCJ1cGRhdGVNb2RhbFByb3BzIiwibmV3UHJvcHMiLCJtb2RhbElkIiwiZXhpc3RpbmdNb2RhbCIsInVwZGF0ZWRDb21wb25lbnQiLCJjbG9uZUVsZW1lbnQiLCJjb21wb25lbnQiLCJwcm9wcyIsImtleSIsInVwZGF0ZVByb3BzIiwidmFsdWUiLCJQcm92aWRlciIsInVzZUNoYXRNb2RhbFN0YXRlIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\n"));

/***/ })

});