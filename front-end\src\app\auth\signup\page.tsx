"use client";

import { useRouter } from 'next/navigation';
import React from 'react';

import SignupModal from '@/components/auth/signup-modal';
import { useTranslation } from '@/hooks/useTranslation';
import { useGlobalModal } from '@/contexts/GlobalModalContext';

export default function SignupPage() {
  const { openModal, closeModal, isModalOpen } = useGlobalModal();
  const router = useRouter();
  const { t } = useTranslation();

  const handleCloseModal = () => {
    closeModal("signup");
    router.replace('/')
  };

  const handleOpenModal = () => {
    openModal({
      id: "signup",
      component: <SignupModal onClose={handleCloseModal} />,
      closeOnBackdropClick: true,
      closeOnEscape: true,
      disableScroll: true,
      zIndex: 1000,
      backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
      modalClassName: 'flex flex-col items-center justify-center p-4',
      preventClose: true,
      onClose: handleCloseModal,
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-0 overflow-auto">
      {!isModalOpen("signup") && (
        <button
          onClick={handleOpenModal}
          className="bg-black text-white px-8 py-4 rounded-lg font-medium text-lg hover:bg-gray-800"
        >
          {t('ui.openSignupModal')}
        </button>
      )}
    </div>
  );
} 