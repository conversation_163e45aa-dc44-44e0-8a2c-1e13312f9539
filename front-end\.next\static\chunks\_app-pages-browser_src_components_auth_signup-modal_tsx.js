"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_auth_signup-modal_tsx"],{

/***/ "(app-pages-browser)/./src/components/auth/signup-modal.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/signup-modal.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst SignupModal = (param)=>{\n    let { onClose, onSuccessfulLogin } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { isModalOpen } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_7__.useGlobalModal)();\n    const isOpen = isModalOpen(\"signup\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        username: \"\",\n        email: \"\",\n        password: \"\",\n        terms: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [agreedToTerms, setAgreedToTerms] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        email: \"\",\n        password: \"\",\n        success: \"\",\n        terms: \"\"\n    });\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear errors when user types\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const handleTogglePassword = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const validateEmail = (email)=>{\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(email);\n    };\n    const validatePassword = (password)=>{\n        // Password must be at least 8 characters, include uppercase, lowercase, number, and special character\n        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\n        return passwordRegex.test(password);\n    };\n    const handleBlur = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"email\" && value && !validateEmail(value)) {\n            setErrors((prev)=>({\n                    ...prev,\n                    email: \"Please enter a valid email address\"\n                }));\n        } else if (name === \"password\" && value && !validatePassword(value)) {\n            setErrors((prev)=>({\n                    ...prev,\n                    password: \"Password must be at least 8 characters and include uppercase, lowercase, number, and special character\"\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        let hasError = false;\n        // Validate email\n        if (!validateEmail(formData.email)) {\n            setErrors((prev)=>({\n                    ...prev,\n                    email: \"Please enter a valid email address\"\n                }));\n            hasError = true;\n        }\n        // Validate password\n        if (!validatePassword(formData.password)) {\n            setErrors((prev)=>({\n                    ...prev,\n                    password: \"Password must be at least 8 characters and include uppercase, lowercase, number, and special character\"\n                }));\n            hasError = true;\n        }\n        // Validate terms checkbox\n        if (!agreedToTerms) {\n            setErrors((prev)=>({\n                    ...prev,\n                    terms: \"You must agree to the Terms, Privacy Policy and Fees.\"\n                }));\n            hasError = true;\n        } else {\n            // Clear terms error if checked now\n            setErrors((prev)=>({\n                    ...prev,\n                    terms: \"\"\n                }));\n        }\n        if (hasError) return;\n        // Call signup API\n        const res = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_6__.signupUser)({\n            username: formData.username,\n            email: formData.email,\n            password: formData.password\n        });\n        if (res.status != 200) {\n            setErrors((prev)=>({\n                    ...prev,\n                    password: \"\",\n                    success: res.message\n                }));\n            return;\n        } else {\n            setErrors((prev)=>({\n                    ...prev,\n                    success: res.message,\n                    password: \"\"\n                }));\n            setTimeout(()=>{\n                onClose();\n                router.push(\"/auth/signin\");\n            // if (onSuccessfulLogin) onSuccessfulLogin();\n            }, 3000);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center z-50 p-0 modal-backdrop\",\n        style: {\n            backgroundColor: \"#000000b0\"\n        },\n        onClick: (e)=>{\n            if (e.target === e.currentTarget) {\n                onClose();\n            }\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-[24px] md:rounded-[48px] w-full max-w-[705px] p-4 md:p-12 relative flex flex-col m-4 max-h-full overflow-auto\",\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                    onClick: ()=>onClose(),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/icons/close.svg\",\n                        alt: \"Menu\",\n                        width: 24,\n                        height: 24,\n                        style: {\n                            filter: 'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12 max-w-[459px] m-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full text-center justify-start text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mx-auto mb-3\",\n                            children: t('authModal.signupTitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"self-stretch text-center justify-start text-gray-500 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                            children: t('authModal.signupSubtitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"flex-1 flex flex-col max-w-[459px] m-auto w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center border border-[#D0D5DD] rounded-lg h-[52px] w-full mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#98A2B3] mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/icons/username.svg\",\n                                            alt: \"Username\",\n                                            width: 24,\n                                            height: 24\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"username\",\n                                        placeholder: t('authModal.username'),\n                                        value: formData.username,\n                                        onChange: handleChange,\n                                        className: \"flex-1 outline-none text-gray-700 h-full text-[18px]\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center border border-[#D0D5DD] rounded-lg h-[52px] w-full mx-auto px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#98A2B3] mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/icons/email.svg\",\n                                                alt: \"Email\",\n                                                width: 24,\n                                                height: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            placeholder: t('authModal.email'),\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            onBlur: handleBlur,\n                                            className: \"flex-1 outline-none text-gray-700 h-full text-[18px] \".concat(errors.email ? \"border-red-500\" : \"border-[#D0D5DD]\", \" focus:outline-none focus:ring-2 focus:ring-black\"),\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-sm mt-1 w-full mx-auto\",\n                                    children: t('authModal.invalidEmail')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center border border-[#D0D5DD] rounded-lg h-[52px] w-full mx-auto px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#98A2B3] mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/icons/password.svg\",\n                                                alt: \"Password\",\n                                                width: 24,\n                                                height: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            placeholder: t('authModal.password'),\n                                            className: \"flex-1 outline-none text-gray-700 h-full text-[18px]\",\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            onBlur: handleBlur,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleTogglePassword,\n                                            className: \"ml-2 text-[#98A2B3] focus:outline-none\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"20\",\n                                                height: \"20\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"1\",\n                                                        y1: \"1\",\n                                                        x2: \"23\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"20\",\n                                                height: \"20\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined),\n                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-sm mt-1 w-full mx-auto\",\n                                    children: t('authModal.passwordPolicy')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 flex items-start w-full mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"terms\",\n                                    checked: agreedToTerms,\n                                    onChange: ()=>{\n                                        setAgreedToTerms(!agreedToTerms);\n                                        if (!agreedToTerms && errors.terms) {\n                                            setErrors((prev)=>({\n                                                    ...prev,\n                                                    terms: \"\"\n                                                }));\n                                        }\n                                    },\n                                    className: \"mt-1 mr-3 h-5 w-5 border border-[#98A2B3] rounded-[10px] text-black focus:ring-0\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"terms\",\n                                    className: \"font-['IBM_Plex_Sans'] font-semibold text-base leading-normal text-[#667085] \",\n                                    children: t('authModal.termsLabel')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined),\n                        errors.terms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-sm mt-1 w-full mx-auto\",\n                                    children: t('authModal.agreeTerms')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, undefined),\n                        errors.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-500 font-semibold text-base mt-1 w-full mx-auto\",\n                            children: t('authModal.success')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"w-full h-12 px-5 py-2 bg-black rounded-sm mx-auto inline-flex justify-center items-center gap-2.5 mb-7\",\n                            onClick: handleSubmit,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                children: t('authModal.signupButton')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"self-stretch text-center justify-start text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                                children: \"Or sign up with\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full md:max-w-96 flex flex-col md:flex-row justify-start items-start gap-5 mx-auto mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-44 px-6 py-3 bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300 flex justify-center items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 48 48\",\n                                            width: \"24\",\n                                            height: \"24\",\n                                            className: \"mr-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FFC107\",\n                                                    d: \"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FF3D00\",\n                                                    d: \"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4CAF50\",\n                                                    d: \"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#1976D2\",\n                                                    d: \"M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center justify-start text-gray-900 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-44 px-6 py-3 bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300 flex justify-center items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 48 48\",\n                                            width: \"24\",\n                                            height: \"24\",\n                                            className: \"mr-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#039be5\",\n                                                    d: \"M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#fff\",\n                                                    d: \"M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center justify-start text-gray-900 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                            children: t('authModal.facebook')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-72 text-center justify-start mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                                        children: t('authModal.alreadyHaveAccount')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signin\",\n                                        className: \"text-neutral-800 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal hover:underline\",\n                                        children: t('authModal.login')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\signup-modal.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SignupModal, \"6AW6cOvCG7XfMsVIybHJtZfFMj4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_7__.useGlobalModal,\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = SignupModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SignupModal);\nvar _c;\n$RefreshReg$(_c, \"SignupModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/signup-modal.tsx\n"));

/***/ })

}]);