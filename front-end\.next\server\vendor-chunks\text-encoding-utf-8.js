"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/text-encoding-utf-8";
exports.ids = ["vendor-chunks/text-encoding-utf-8"];
exports.modules = {

/***/ "(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js":
/*!**************************************************************!*\
  !*** ./node_modules/text-encoding-utf-8/lib/encoding.lib.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n// This is free and unencumbered software released into the public domain.\n// See LICENSE.md for more information.\n\n//\n// Utilities\n//\n\n/**\n * @param {number} a The number to test.\n * @param {number} min The minimum value in the range, inclusive.\n * @param {number} max The maximum value in the range, inclusive.\n * @return {boolean} True if a >= min and a <= max.\n */\nfunction inRange(a, min, max) {\n  return min <= a && a <= max;\n}\n\n/**\n * @param {*} o\n * @return {Object}\n */\nfunction ToDictionary(o) {\n  if (o === undefined) return {};\n  if (o === Object(o)) return o;\n  throw TypeError('Could not convert argument to dictionary');\n}\n\n/**\n * @param {string} string Input string of UTF-16 code units.\n * @return {!Array.<number>} Code points.\n */\nfunction stringToCodePoints(string) {\n  // https://heycam.github.io/webidl/#dfn-obtain-unicode\n\n  // 1. Let S be the DOMString value.\n  var s = String(string);\n\n  // 2. Let n be the length of S.\n  var n = s.length;\n\n  // 3. Initialize i to 0.\n  var i = 0;\n\n  // 4. Initialize U to be an empty sequence of Unicode characters.\n  var u = [];\n\n  // 5. While i < n:\n  while (i < n) {\n\n    // 1. Let c be the code unit in S at index i.\n    var c = s.charCodeAt(i);\n\n    // 2. Depending on the value of c:\n\n    // c < 0xD800 or c > 0xDFFF\n    if (c < 0xD800 || c > 0xDFFF) {\n      // Append to U the Unicode character with code point c.\n      u.push(c);\n    }\n\n    // 0xDC00 ≤ c ≤ 0xDFFF\n    else if (0xDC00 <= c && c <= 0xDFFF) {\n      // Append to U a U+FFFD REPLACEMENT CHARACTER.\n      u.push(0xFFFD);\n    }\n\n    // 0xD800 ≤ c ≤ 0xDBFF\n    else if (0xD800 <= c && c <= 0xDBFF) {\n      // 1. If i = n−1, then append to U a U+FFFD REPLACEMENT\n      // CHARACTER.\n      if (i === n - 1) {\n        u.push(0xFFFD);\n      }\n      // 2. Otherwise, i < n−1:\n      else {\n        // 1. Let d be the code unit in S at index i+1.\n        var d = string.charCodeAt(i + 1);\n\n        // 2. If 0xDC00 ≤ d ≤ 0xDFFF, then:\n        if (0xDC00 <= d && d <= 0xDFFF) {\n          // 1. Let a be c & 0x3FF.\n          var a = c & 0x3FF;\n\n          // 2. Let b be d & 0x3FF.\n          var b = d & 0x3FF;\n\n          // 3. Append to U the Unicode character with code point\n          // 2^16+2^10*a+b.\n          u.push(0x10000 + (a << 10) + b);\n\n          // 4. Set i to i+1.\n          i += 1;\n        }\n\n        // 3. Otherwise, d < 0xDC00 or d > 0xDFFF. Append to U a\n        // U+FFFD REPLACEMENT CHARACTER.\n        else  {\n          u.push(0xFFFD);\n        }\n      }\n    }\n\n    // 3. Set i to i+1.\n    i += 1;\n  }\n\n  // 6. Return U.\n  return u;\n}\n\n/**\n * @param {!Array.<number>} code_points Array of code points.\n * @return {string} string String of UTF-16 code units.\n */\nfunction codePointsToString(code_points) {\n  var s = '';\n  for (var i = 0; i < code_points.length; ++i) {\n    var cp = code_points[i];\n    if (cp <= 0xFFFF) {\n      s += String.fromCharCode(cp);\n    } else {\n      cp -= 0x10000;\n      s += String.fromCharCode((cp >> 10) + 0xD800,\n                               (cp & 0x3FF) + 0xDC00);\n    }\n  }\n  return s;\n}\n\n\n//\n// Implementation of Encoding specification\n// https://encoding.spec.whatwg.org/\n//\n\n//\n// 3. Terminology\n//\n\n/**\n * End-of-stream is a special token that signifies no more tokens\n * are in the stream.\n * @const\n */ var end_of_stream = -1;\n\n/**\n * A stream represents an ordered sequence of tokens.\n *\n * @constructor\n * @param {!(Array.<number>|Uint8Array)} tokens Array of tokens that provide the\n * stream.\n */\nfunction Stream(tokens) {\n  /** @type {!Array.<number>} */\n  this.tokens = [].slice.call(tokens);\n}\n\nStream.prototype = {\n  /**\n   * @return {boolean} True if end-of-stream has been hit.\n   */\n  endOfStream: function() {\n    return !this.tokens.length;\n  },\n\n  /**\n   * When a token is read from a stream, the first token in the\n   * stream must be returned and subsequently removed, and\n   * end-of-stream must be returned otherwise.\n   *\n   * @return {number} Get the next token from the stream, or\n   * end_of_stream.\n   */\n   read: function() {\n    if (!this.tokens.length)\n      return end_of_stream;\n     return this.tokens.shift();\n   },\n\n  /**\n   * When one or more tokens are prepended to a stream, those tokens\n   * must be inserted, in given order, before the first token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The token(s) to prepend to the stream.\n   */\n  prepend: function(token) {\n    if (Array.isArray(token)) {\n      var tokens = /**@type {!Array.<number>}*/(token);\n      while (tokens.length)\n        this.tokens.unshift(tokens.pop());\n    } else {\n      this.tokens.unshift(token);\n    }\n  },\n\n  /**\n   * When one or more tokens are pushed to a stream, those tokens\n   * must be inserted, in given order, after the last token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The tokens(s) to prepend to the stream.\n   */\n  push: function(token) {\n    if (Array.isArray(token)) {\n      var tokens = /**@type {!Array.<number>}*/(token);\n      while (tokens.length)\n        this.tokens.push(tokens.shift());\n    } else {\n      this.tokens.push(token);\n    }\n  }\n};\n\n//\n// 4. Encodings\n//\n\n// 4.1 Encoders and decoders\n\n/** @const */\nvar finished = -1;\n\n/**\n * @param {boolean} fatal If true, decoding errors raise an exception.\n * @param {number=} opt_code_point Override the standard fallback code point.\n * @return {number} The code point to insert on a decoding error.\n */\nfunction decoderError(fatal, opt_code_point) {\n  if (fatal)\n    throw TypeError('Decoder error');\n  return opt_code_point || 0xFFFD;\n}\n\n//\n// 7. API\n//\n\n/** @const */ var DEFAULT_ENCODING = 'utf-8';\n\n// 7.1 Interface TextDecoder\n\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */\nfunction TextDecoder(encoding, options) {\n  if (!(this instanceof TextDecoder)) {\n    return new TextDecoder(encoding, options);\n  }\n  encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n  if (encoding !== DEFAULT_ENCODING) {\n    throw new Error('Encoding not supported. Only utf-8 is supported');\n  }\n  options = ToDictionary(options);\n\n  /** @private @type {boolean} */\n  this._streaming = false;\n  /** @private @type {boolean} */\n  this._BOMseen = false;\n  /** @private @type {?Decoder} */\n  this._decoder = null;\n  /** @private @type {boolean} */\n  this._fatal = Boolean(options['fatal']);\n  /** @private @type {boolean} */\n  this._ignoreBOM = Boolean(options['ignoreBOM']);\n\n  Object.defineProperty(this, 'encoding', {value: 'utf-8'});\n  Object.defineProperty(this, 'fatal', {value: this._fatal});\n  Object.defineProperty(this, 'ignoreBOM', {value: this._ignoreBOM});\n}\n\nTextDecoder.prototype = {\n  /**\n   * @param {ArrayBufferView=} input The buffer of bytes to decode.\n   * @param {Object=} options\n   * @return {string} The decoded string.\n   */\n  decode: function decode(input, options) {\n    var bytes;\n    if (typeof input === 'object' && input instanceof ArrayBuffer) {\n      bytes = new Uint8Array(input);\n    } else if (typeof input === 'object' && 'buffer' in input &&\n               input.buffer instanceof ArrayBuffer) {\n      bytes = new Uint8Array(input.buffer,\n                             input.byteOffset,\n                             input.byteLength);\n    } else {\n      bytes = new Uint8Array(0);\n    }\n\n    options = ToDictionary(options);\n\n    if (!this._streaming) {\n      this._decoder = new UTF8Decoder({fatal: this._fatal});\n      this._BOMseen = false;\n    }\n    this._streaming = Boolean(options['stream']);\n\n    var input_stream = new Stream(bytes);\n\n    var code_points = [];\n\n    /** @type {?(number|!Array.<number>)} */\n    var result;\n\n    while (!input_stream.endOfStream()) {\n      result = this._decoder.handler(input_stream, input_stream.read());\n      if (result === finished)\n        break;\n      if (result === null)\n        continue;\n      if (Array.isArray(result))\n        code_points.push.apply(code_points, /**@type {!Array.<number>}*/(result));\n      else\n        code_points.push(result);\n    }\n    if (!this._streaming) {\n      do {\n        result = this._decoder.handler(input_stream, input_stream.read());\n        if (result === finished)\n          break;\n        if (result === null)\n          continue;\n        if (Array.isArray(result))\n          code_points.push.apply(code_points, /**@type {!Array.<number>}*/(result));\n        else\n          code_points.push(result);\n      } while (!input_stream.endOfStream());\n      this._decoder = null;\n    }\n\n    if (code_points.length) {\n      // If encoding is one of utf-8, utf-16be, and utf-16le, and\n      // ignore BOM flag and BOM seen flag are unset, run these\n      // subsubsteps:\n      if (['utf-8'].indexOf(this.encoding) !== -1 &&\n          !this._ignoreBOM && !this._BOMseen) {\n        // If token is U+FEFF, set BOM seen flag.\n        if (code_points[0] === 0xFEFF) {\n          this._BOMseen = true;\n          code_points.shift();\n        } else {\n          // Otherwise, if token is not end-of-stream, set BOM seen\n          // flag and append token to output.\n          this._BOMseen = true;\n        }\n      }\n    }\n\n    return codePointsToString(code_points);\n  }\n};\n\n// 7.2 Interface TextEncoder\n\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */\nfunction TextEncoder(encoding, options) {\n  if (!(this instanceof TextEncoder))\n    return new TextEncoder(encoding, options);\n  encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n  if (encoding !== DEFAULT_ENCODING) {\n    throw new Error('Encoding not supported. Only utf-8 is supported');\n  }\n  options = ToDictionary(options);\n\n  /** @private @type {boolean} */\n  this._streaming = false;\n  /** @private @type {?Encoder} */\n  this._encoder = null;\n  /** @private @type {{fatal: boolean}} */\n  this._options = {fatal: Boolean(options['fatal'])};\n\n  Object.defineProperty(this, 'encoding', {value: 'utf-8'});\n}\n\nTextEncoder.prototype = {\n  /**\n   * @param {string=} opt_string The string to encode.\n   * @param {Object=} options\n   * @return {Uint8Array} Encoded bytes, as a Uint8Array.\n   */\n  encode: function encode(opt_string, options) {\n    opt_string = opt_string ? String(opt_string) : '';\n    options = ToDictionary(options);\n\n    // NOTE: This option is nonstandard. None of the encodings\n    // permitted for encoding (i.e. UTF-8, UTF-16) are stateful,\n    // so streaming is not necessary.\n    if (!this._streaming)\n      this._encoder = new UTF8Encoder(this._options);\n    this._streaming = Boolean(options['stream']);\n\n    var bytes = [];\n    var input_stream = new Stream(stringToCodePoints(opt_string));\n    /** @type {?(number|!Array.<number>)} */\n    var result;\n    while (!input_stream.endOfStream()) {\n      result = this._encoder.handler(input_stream, input_stream.read());\n      if (result === finished)\n        break;\n      if (Array.isArray(result))\n        bytes.push.apply(bytes, /**@type {!Array.<number>}*/(result));\n      else\n        bytes.push(result);\n    }\n    if (!this._streaming) {\n      while (true) {\n        result = this._encoder.handler(input_stream, input_stream.read());\n        if (result === finished)\n          break;\n        if (Array.isArray(result))\n          bytes.push.apply(bytes, /**@type {!Array.<number>}*/(result));\n        else\n          bytes.push(result);\n      }\n      this._encoder = null;\n    }\n    return new Uint8Array(bytes);\n  }\n};\n\n//\n// 8. The encoding\n//\n\n// 8.1 utf-8\n\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nfunction UTF8Decoder(options) {\n  var fatal = options.fatal;\n\n  // utf-8's decoder's has an associated utf-8 code point, utf-8\n  // bytes seen, and utf-8 bytes needed (all initially 0), a utf-8\n  // lower boundary (initially 0x80), and a utf-8 upper boundary\n  // (initially 0xBF).\n  var /** @type {number} */ utf8_code_point = 0,\n      /** @type {number} */ utf8_bytes_seen = 0,\n      /** @type {number} */ utf8_bytes_needed = 0,\n      /** @type {number} */ utf8_lower_boundary = 0x80,\n      /** @type {number} */ utf8_upper_boundary = 0xBF;\n\n  /**\n   * @param {Stream} stream The stream of bytes being decoded.\n   * @param {number} bite The next byte read from the stream.\n   * @return {?(number|!Array.<number>)} The next code point(s)\n   *     decoded, or null if not enough data exists in the input\n   *     stream to decode a complete code point.\n   */\n  this.handler = function(stream, bite) {\n    // 1. If byte is end-of-stream and utf-8 bytes needed is not 0,\n    // set utf-8 bytes needed to 0 and return error.\n    if (bite === end_of_stream && utf8_bytes_needed !== 0) {\n      utf8_bytes_needed = 0;\n      return decoderError(fatal);\n    }\n\n    // 2. If byte is end-of-stream, return finished.\n    if (bite === end_of_stream)\n      return finished;\n\n    // 3. If utf-8 bytes needed is 0, based on byte:\n    if (utf8_bytes_needed === 0) {\n\n      // 0x00 to 0x7F\n      if (inRange(bite, 0x00, 0x7F)) {\n        // Return a code point whose value is byte.\n        return bite;\n      }\n\n      // 0xC2 to 0xDF\n      if (inRange(bite, 0xC2, 0xDF)) {\n        // Set utf-8 bytes needed to 1 and utf-8 code point to byte\n        // − 0xC0.\n        utf8_bytes_needed = 1;\n        utf8_code_point = bite - 0xC0;\n      }\n\n      // 0xE0 to 0xEF\n      else if (inRange(bite, 0xE0, 0xEF)) {\n        // 1. If byte is 0xE0, set utf-8 lower boundary to 0xA0.\n        if (bite === 0xE0)\n          utf8_lower_boundary = 0xA0;\n        // 2. If byte is 0xED, set utf-8 upper boundary to 0x9F.\n        if (bite === 0xED)\n          utf8_upper_boundary = 0x9F;\n        // 3. Set utf-8 bytes needed to 2 and utf-8 code point to\n        // byte − 0xE0.\n        utf8_bytes_needed = 2;\n        utf8_code_point = bite - 0xE0;\n      }\n\n      // 0xF0 to 0xF4\n      else if (inRange(bite, 0xF0, 0xF4)) {\n        // 1. If byte is 0xF0, set utf-8 lower boundary to 0x90.\n        if (bite === 0xF0)\n          utf8_lower_boundary = 0x90;\n        // 2. If byte is 0xF4, set utf-8 upper boundary to 0x8F.\n        if (bite === 0xF4)\n          utf8_upper_boundary = 0x8F;\n        // 3. Set utf-8 bytes needed to 3 and utf-8 code point to\n        // byte − 0xF0.\n        utf8_bytes_needed = 3;\n        utf8_code_point = bite - 0xF0;\n      }\n\n      // Otherwise\n      else {\n        // Return error.\n        return decoderError(fatal);\n      }\n\n      // Then (byte is in the range 0xC2 to 0xF4) set utf-8 code\n      // point to utf-8 code point << (6 × utf-8 bytes needed) and\n      // return continue.\n      utf8_code_point = utf8_code_point << (6 * utf8_bytes_needed);\n      return null;\n    }\n\n    // 4. If byte is not in the range utf-8 lower boundary to utf-8\n    // upper boundary, run these substeps:\n    if (!inRange(bite, utf8_lower_boundary, utf8_upper_boundary)) {\n\n      // 1. Set utf-8 code point, utf-8 bytes needed, and utf-8\n      // bytes seen to 0, set utf-8 lower boundary to 0x80, and set\n      // utf-8 upper boundary to 0xBF.\n      utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n      utf8_lower_boundary = 0x80;\n      utf8_upper_boundary = 0xBF;\n\n      // 2. Prepend byte to stream.\n      stream.prepend(bite);\n\n      // 3. Return error.\n      return decoderError(fatal);\n    }\n\n    // 5. Set utf-8 lower boundary to 0x80 and utf-8 upper boundary\n    // to 0xBF.\n    utf8_lower_boundary = 0x80;\n    utf8_upper_boundary = 0xBF;\n\n    // 6. Increase utf-8 bytes seen by one and set utf-8 code point\n    // to utf-8 code point + (byte − 0x80) << (6 × (utf-8 bytes\n    // needed − utf-8 bytes seen)).\n    utf8_bytes_seen += 1;\n    utf8_code_point += (bite - 0x80) << (6 * (utf8_bytes_needed - utf8_bytes_seen));\n\n    // 7. If utf-8 bytes seen is not equal to utf-8 bytes needed,\n    // continue.\n    if (utf8_bytes_seen !== utf8_bytes_needed)\n      return null;\n\n    // 8. Let code point be utf-8 code point.\n    var code_point = utf8_code_point;\n\n    // 9. Set utf-8 code point, utf-8 bytes needed, and utf-8 bytes\n    // seen to 0.\n    utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n\n    // 10. Return a code point whose value is code point.\n    return code_point;\n  };\n}\n\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nfunction UTF8Encoder(options) {\n  var fatal = options.fatal;\n  /**\n   * @param {Stream} stream Input stream.\n   * @param {number} code_point Next code point read from the stream.\n   * @return {(number|!Array.<number>)} Byte(s) to emit.\n   */\n  this.handler = function(stream, code_point) {\n    // 1. If code point is end-of-stream, return finished.\n    if (code_point === end_of_stream)\n      return finished;\n\n    // 2. If code point is in the range U+0000 to U+007F, return a\n    // byte whose value is code point.\n    if (inRange(code_point, 0x0000, 0x007f))\n      return code_point;\n\n    // 3. Set count and offset based on the range code point is in:\n    var count, offset;\n    // U+0080 to U+07FF:    1 and 0xC0\n    if (inRange(code_point, 0x0080, 0x07FF)) {\n      count = 1;\n      offset = 0xC0;\n    }\n    // U+0800 to U+FFFF:    2 and 0xE0\n    else if (inRange(code_point, 0x0800, 0xFFFF)) {\n      count = 2;\n      offset = 0xE0;\n    }\n    // U+10000 to U+10FFFF: 3 and 0xF0\n    else if (inRange(code_point, 0x10000, 0x10FFFF)) {\n      count = 3;\n      offset = 0xF0;\n    }\n\n    // 4.Let bytes be a byte sequence whose first byte is (code\n    // point >> (6 × count)) + offset.\n    var bytes = [(code_point >> (6 * count)) + offset];\n\n    // 5. Run these substeps while count is greater than 0:\n    while (count > 0) {\n\n      // 1. Set temp to code point >> (6 × (count − 1)).\n      var temp = code_point >> (6 * (count - 1));\n\n      // 2. Append to bytes 0x80 | (temp & 0x3F).\n      bytes.push(0x80 | (temp & 0x3F));\n\n      // 3. Decrease count by one.\n      count -= 1;\n    }\n\n    // 6. Return bytes bytes, in order.\n    return bytes;\n  };\n}\n\nexports.TextEncoder = TextEncoder;\nexports.TextDecoder = TextDecoder;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\n");

/***/ })

};
;