const { Model, DataTypes } = require("sequelize");

class Moderator extends Model {
    static initModel(sequelize) {
        return Moderator.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    unique: true,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                isActive: {
                    type: DataTypes.BOOLEAN,
                    allowNull: false,
                    defaultValue: true,
                },
                totalDisputesHandled: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 0,
                },
                totalDisputesResolved: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 0,
                },
                averageResolutionTime: {
                    type: DataTypes.FLOAT,
                    allowNull: true,
                    comment: 'Average resolution time in hours',
                },
                reputationScore: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                    defaultValue: 100.0,
                    validate: {
                        min: 0,
                        max: 1000,
                    },
                },
                specializations: {
                    type: DataTypes.JSON,
                    allowNull: true,
                    comment: 'Array of specialization areas like ["digital_goods", "physical_goods", "services"]',
                },
                maxConcurrentDisputes: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 10,
                },
                currentActiveDisputes: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 0,
                },
                lastActiveAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },
                addedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                addedBy: {
                    type: DataTypes.BIGINT,
                    allowNull: true,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'SET NULL',
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'moderators',
                timestamps: true,
                indexes: [
                    {
                        fields: ['userId'],
                        unique: true,
                    },
                    {
                        fields: ['isActive'],
                    },
                    {
                        fields: ['reputationScore'],
                    },
                ],
            }
        );
    }

    static associate(models) {
        // Moderator belongs to a user
        Moderator.belongsTo(models.User, { 
            foreignKey: 'userId', 
            as: 'user' 
        });

        // Moderator was added by another user
        Moderator.belongsTo(models.User, { 
            foreignKey: 'addedBy', 
            as: 'addedByUser' 
        });

        // Moderator has many disputes
        Moderator.hasMany(models.Dispute, { 
            foreignKey: 'moderatorId', 
            as: 'disputes' 
        });
    }

    // Instance method to check if moderator can take more disputes
    canTakeMoreDisputes() {
        return this.isActive && this.currentActiveDisputes < this.maxConcurrentDisputes;
    }

    // Instance method to update activity
    async updateActivity() {
        this.lastActiveAt = new Date();
        await this.save();
    }
}

module.exports = Moderator;
