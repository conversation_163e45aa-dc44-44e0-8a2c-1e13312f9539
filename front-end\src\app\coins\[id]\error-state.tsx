import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import { fadeInUp } from '@/lib/animations';
import { useTranslation } from '@/hooks/useTranslation';

interface ErrorStateProps {
  error: string | null;
}

export const ErrorState: React.FC<ErrorStateProps> = ({ error }) => {
  const { t } = useTranslation();
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          className="flex flex-col items-center justify-center py-20"
          {...fadeInUp}
        >
          <motion.div
            className="mb-6 p-6 bg-red-100 rounded-full"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          >
            <AlertCircle size={64} className="text-red-500" />
          </motion.div>
          <motion.h2
            className="text-3xl font-bold text-gray-900 mb-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {t('coins.tokenNotFound')}
          </motion.h2>
          <motion.p
            className="text-gray-600 mb-8 text-center max-w-md"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {error || t('coins.tokenNotFoundDescription')}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Link
              href="/"
              className="inline-flex items-center gap-3 bg-[#F58A38] text-white px-8 py-4 rounded-xl font-semibold hover:bg-[#FF6600] transition-all duration-300 transform hover:scale-105"
            >
              <ArrowLeft size={20} />
              {t('coins.backToHome')}
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}; 