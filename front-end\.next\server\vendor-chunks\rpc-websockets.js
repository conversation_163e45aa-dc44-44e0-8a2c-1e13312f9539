"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rpc-websockets";
exports.ids = ["vendor-chunks/rpc-websockets"];
exports.modules = {

/***/ "(ssr)/./node_modules/rpc-websockets/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/rpc-websockets/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CommonClient: () => (/* binding */ CommonClient),\n/* harmony export */   DefaultDataPack: () => (/* binding */ DefaultDataPack),\n/* harmony export */   Server: () => (/* binding */ Server),\n/* harmony export */   WebSocket: () => (/* binding */ WebSocket),\n/* harmony export */   createError: () => (/* binding */ createError)\n/* harmony export */ });\n/* harmony import */ var ws__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ws */ \"(ssr)/./node_modules/ws/wrapper.mjs\");\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs\");\n/* harmony import */ var node_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:url */ \"node:url\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/v1.js\");\n\n\n\n\n\n// src/lib/client/websocket.ts\nfunction WebSocket(address, options) {\n  return new ws__WEBPACK_IMPORTED_MODULE_0__[\"default\"](address, options);\n}\n\n// src/lib/utils.ts\nvar DefaultDataPack = class {\n  encode(value) {\n    return JSON.stringify(value);\n  }\n  decode(value) {\n    return JSON.parse(value);\n  }\n};\n\n// src/lib/client.ts\nvar CommonClient = class extends eventemitter3__WEBPACK_IMPORTED_MODULE_1__.EventEmitter {\n  address;\n  rpc_id;\n  queue;\n  options;\n  autoconnect;\n  ready;\n  reconnect;\n  reconnect_timer_id;\n  reconnect_interval;\n  max_reconnects;\n  rest_options;\n  current_reconnects;\n  generate_request_id;\n  socket;\n  webSocketFactory;\n  dataPack;\n  /**\n  * Instantiate a Client class.\n  * @constructor\n  * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n  * @param {String} address - url to a websocket server\n  * @param {Object} options - ws options object with reconnect parameters\n  * @param {Function} generate_request_id - custom generation request Id\n  * @param {DataPack} dataPack - data pack contains encoder and decoder\n  * @return {CommonClient}\n  */\n  constructor(webSocketFactory, address = \"ws://localhost:8080\", {\n    autoconnect = true,\n    reconnect = true,\n    reconnect_interval = 1e3,\n    max_reconnects = 5,\n    ...rest_options\n  } = {}, generate_request_id, dataPack) {\n    super();\n    this.webSocketFactory = webSocketFactory;\n    this.queue = {};\n    this.rpc_id = 0;\n    this.address = address;\n    this.autoconnect = autoconnect;\n    this.ready = false;\n    this.reconnect = reconnect;\n    this.reconnect_timer_id = void 0;\n    this.reconnect_interval = reconnect_interval;\n    this.max_reconnects = max_reconnects;\n    this.rest_options = rest_options;\n    this.current_reconnects = 0;\n    this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\" ? ++this.rpc_id : Number(this.rpc_id) + 1);\n    if (!dataPack) this.dataPack = new DefaultDataPack();\n    else this.dataPack = dataPack;\n    if (this.autoconnect)\n      this._connect(this.address, {\n        autoconnect: this.autoconnect,\n        reconnect: this.reconnect,\n        reconnect_interval: this.reconnect_interval,\n        max_reconnects: this.max_reconnects,\n        ...this.rest_options\n      });\n  }\n  /**\n  * Connects to a defined server if not connected already.\n  * @method\n  * @return {Undefined}\n  */\n  connect() {\n    if (this.socket) return;\n    this._connect(this.address, {\n      autoconnect: this.autoconnect,\n      reconnect: this.reconnect,\n      reconnect_interval: this.reconnect_interval,\n      max_reconnects: this.max_reconnects,\n      ...this.rest_options\n    });\n  }\n  /**\n  * Calls a registered RPC method on server.\n  * @method\n  * @param {String} method - RPC method name\n  * @param {Object|Array} params - optional method parameters\n  * @param {Number} timeout - RPC reply timeout value\n  * @param {Object} ws_opts - options passed to ws\n  * @return {Promise}\n  */\n  call(method, params, timeout, ws_opts) {\n    if (!ws_opts && \"object\" === typeof timeout) {\n      ws_opts = timeout;\n      timeout = null;\n    }\n    return new Promise((resolve, reject) => {\n      if (!this.ready) return reject(new Error(\"socket not ready\"));\n      const rpc_id = this.generate_request_id(method, params);\n      const message = {\n        jsonrpc: \"2.0\",\n        method,\n        params: params || void 0,\n        id: rpc_id\n      };\n      this.socket.send(this.dataPack.encode(message), ws_opts, (error) => {\n        if (error) return reject(error);\n        this.queue[rpc_id] = { promise: [resolve, reject] };\n        if (timeout) {\n          this.queue[rpc_id].timeout = setTimeout(() => {\n            delete this.queue[rpc_id];\n            reject(new Error(\"reply timeout\"));\n          }, timeout);\n        }\n      });\n    });\n  }\n  /**\n  * Logins with the other side of the connection.\n  * @method\n  * @param {Object} params - Login credentials object\n  * @return {Promise}\n  */\n  async login(params) {\n    const resp = await this.call(\"rpc.login\", params);\n    if (!resp) throw new Error(\"authentication failed\");\n    return resp;\n  }\n  /**\n  * Fetches a list of client's methods registered on server.\n  * @method\n  * @return {Array}\n  */\n  async listMethods() {\n    return await this.call(\"__listMethods\");\n  }\n  /**\n  * Sends a JSON-RPC 2.0 notification to server.\n  * @method\n  * @param {String} method - RPC method name\n  * @param {Object} params - optional method parameters\n  * @return {Promise}\n  */\n  notify(method, params) {\n    return new Promise((resolve, reject) => {\n      if (!this.ready) return reject(new Error(\"socket not ready\"));\n      const message = {\n        jsonrpc: \"2.0\",\n        method,\n        params\n      };\n      this.socket.send(this.dataPack.encode(message), (error) => {\n        if (error) return reject(error);\n        resolve();\n      });\n    });\n  }\n  /**\n  * Subscribes for a defined event.\n  * @method\n  * @param {String|Array} event - event name\n  * @return {Undefined}\n  * @throws {Error}\n  */\n  async subscribe(event) {\n    if (typeof event === \"string\") event = [event];\n    const result = await this.call(\"rpc.on\", event);\n    if (typeof event === \"string\" && result[event] !== \"ok\")\n      throw new Error(\n        \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n      );\n    return result;\n  }\n  /**\n  * Unsubscribes from a defined event.\n  * @method\n  * @param {String|Array} event - event name\n  * @return {Undefined}\n  * @throws {Error}\n  */\n  async unsubscribe(event) {\n    if (typeof event === \"string\") event = [event];\n    const result = await this.call(\"rpc.off\", event);\n    if (typeof event === \"string\" && result[event] !== \"ok\")\n      throw new Error(\"Failed unsubscribing from an event with: \" + result);\n    return result;\n  }\n  /**\n  * Closes a WebSocket connection gracefully.\n  * @method\n  * @param {Number} code - socket close code\n  * @param {String} data - optional data to be sent before closing\n  * @return {Undefined}\n  */\n  close(code, data) {\n    this.socket.close(code || 1e3, data);\n  }\n  /**\n  * Enable / disable automatic reconnection.\n  * @method\n  * @param {Boolean} reconnect - enable / disable reconnection\n  * @return {Undefined}\n  */\n  setAutoReconnect(reconnect) {\n    this.reconnect = reconnect;\n  }\n  /**\n  * Set the interval between reconnection attempts.\n  * @method\n  * @param {Number} interval - reconnection interval in milliseconds\n  * @return {Undefined}\n  */\n  setReconnectInterval(interval) {\n    this.reconnect_interval = interval;\n  }\n  /**\n  * Set the maximum number of reconnection attempts.\n  * @method\n  * @param {Number} max_reconnects - maximum reconnection attempts\n  * @return {Undefined}\n  */\n  setMaxReconnects(max_reconnects) {\n    this.max_reconnects = max_reconnects;\n  }\n  /**\n  * Connection/Message handler.\n  * @method\n  * @private\n  * @param {String} address - WebSocket API address\n  * @param {Object} options - ws options object\n  * @return {Undefined}\n  */\n  _connect(address, options) {\n    clearTimeout(this.reconnect_timer_id);\n    this.socket = this.webSocketFactory(address, options);\n    this.socket.addEventListener(\"open\", () => {\n      this.ready = true;\n      this.emit(\"open\");\n      this.current_reconnects = 0;\n    });\n    this.socket.addEventListener(\"message\", ({ data: message }) => {\n      if (message instanceof ArrayBuffer)\n        message = Buffer.from(message).toString();\n      try {\n        message = this.dataPack.decode(message);\n      } catch (error) {\n        return;\n      }\n      if (message.notification && this.listeners(message.notification).length) {\n        if (!Object.keys(message.params).length)\n          return this.emit(message.notification);\n        const args = [message.notification];\n        if (message.params.constructor === Object) args.push(message.params);\n        else\n          for (let i = 0; i < message.params.length; i++)\n            args.push(message.params[i]);\n        return Promise.resolve().then(() => {\n          this.emit.apply(this, args);\n        });\n      }\n      if (!this.queue[message.id]) {\n        if (message.method) {\n          return Promise.resolve().then(() => {\n            this.emit(message.method, message?.params);\n          });\n        }\n        return;\n      }\n      if (\"error\" in message === \"result\" in message)\n        this.queue[message.id].promise[1](\n          new Error(\n            'Server response malformed. Response must include either \"result\" or \"error\", but not both.'\n          )\n        );\n      if (this.queue[message.id].timeout)\n        clearTimeout(this.queue[message.id].timeout);\n      if (message.error) this.queue[message.id].promise[1](message.error);\n      else this.queue[message.id].promise[0](message.result);\n      delete this.queue[message.id];\n    });\n    this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error));\n    this.socket.addEventListener(\"close\", ({ code, reason }) => {\n      if (this.ready)\n        setTimeout(() => this.emit(\"close\", code, reason), 0);\n      this.ready = false;\n      this.socket = void 0;\n      if (code === 1e3) return;\n      this.current_reconnects++;\n      if (this.reconnect && (this.max_reconnects > this.current_reconnects || this.max_reconnects === 0))\n        this.reconnect_timer_id = setTimeout(\n          () => this._connect(address, options),\n          this.reconnect_interval\n        );\n    });\n  }\n};\nvar Server = class extends eventemitter3__WEBPACK_IMPORTED_MODULE_1__.EventEmitter {\n  namespaces;\n  dataPack;\n  wss;\n  /**\n  * Instantiate a Server class.\n  * @constructor\n  * @param {Object} options - ws constructor's parameters with rpc\n  * @param {DataPack} dataPack - data pack contains encoder and decoder\n  * @return {Server} - returns a new Server instance\n  */\n  constructor(options, dataPack) {\n    super();\n    this.namespaces = {};\n    if (!dataPack) this.dataPack = new DefaultDataPack();\n    else this.dataPack = dataPack;\n    this.wss = new ws__WEBPACK_IMPORTED_MODULE_0__.WebSocketServer(options);\n    this.wss.on(\"listening\", () => this.emit(\"listening\"));\n    this.wss.on(\"connection\", (socket, request) => {\n      const u = node_url__WEBPACK_IMPORTED_MODULE_2__.parse(request.url, true);\n      const ns = u.pathname;\n      if (u.query.socket_id) socket._id = u.query.socket_id;\n      else socket._id = (0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n      socket[\"_authenticated\"] = false;\n      socket.on(\"error\", (error) => this.emit(\"socket-error\", socket, error));\n      socket.on(\"close\", () => {\n        this.namespaces[ns].clients.delete(socket._id);\n        for (const event of Object.keys(this.namespaces[ns].events)) {\n          const index = this.namespaces[ns].events[event].sockets.indexOf(\n            socket._id\n          );\n          if (index >= 0)\n            this.namespaces[ns].events[event].sockets.splice(index, 1);\n        }\n        this.emit(\"disconnection\", socket);\n      });\n      if (!this.namespaces[ns]) this._generateNamespace(ns);\n      this.namespaces[ns].clients.set(socket._id, socket);\n      this.emit(\"connection\", socket, request);\n      return this._handleRPC(socket, ns);\n    });\n    this.wss.on(\"error\", (error) => this.emit(\"error\", error));\n  }\n  /**\n  * Registers an RPC method.\n  * @method\n  * @param {String} name - method name\n  * @param {Function} fn - a callee function\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - returns an IMethod object\n  */\n  register(name, fn, ns = \"/\") {\n    if (!this.namespaces[ns]) this._generateNamespace(ns);\n    this.namespaces[ns].rpc_methods[name] = {\n      fn,\n      protected: false\n    };\n    return {\n      protected: () => this._makeProtectedMethod(name, ns),\n      public: () => this._makePublicMethod(name, ns)\n    };\n  }\n  /**\n  * Sets an auth method.\n  * @method\n  * @param {Function} fn - an arbitrary auth method\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Undefined}\n  */\n  setAuth(fn, ns = \"/\") {\n    this.register(\"rpc.login\", fn, ns);\n  }\n  /**\n  * Marks an RPC method as protected.\n  * @method\n  * @param {String} name - method name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makeProtectedMethod(name, ns = \"/\") {\n    this.namespaces[ns].rpc_methods[name].protected = true;\n  }\n  /**\n  * Marks an RPC method as public.\n  * @method\n  * @param {String} name - method name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makePublicMethod(name, ns = \"/\") {\n    this.namespaces[ns].rpc_methods[name].protected = false;\n  }\n  /**\n  * Marks an event as protected.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makeProtectedEvent(name, ns = \"/\") {\n    this.namespaces[ns].events[name].protected = true;\n  }\n  /**\n  * Marks an event as public.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makePublicEvent(name, ns = \"/\") {\n    this.namespaces[ns].events[name].protected = false;\n  }\n  /**\n  * Removes a namespace and closes all connections\n  * @method\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Undefined}\n  */\n  closeNamespace(ns) {\n    const namespace = this.namespaces[ns];\n    if (namespace) {\n      delete namespace.rpc_methods;\n      delete namespace.events;\n      for (const socket of namespace.clients.values()) socket.close();\n      delete this.namespaces[ns];\n    }\n  }\n  /**\n  * Creates a new event that can be emitted to clients.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - returns an IEvent object\n  */\n  event(name, ns = \"/\") {\n    if (!this.namespaces[ns]) this._generateNamespace(ns);\n    else {\n      const index = this.namespaces[ns].events[name];\n      if (index !== void 0)\n        throw new Error(`Already registered event ${ns}${name}`);\n    }\n    this.namespaces[ns].events[name] = {\n      sockets: [],\n      protected: false\n    };\n    this.on(name, (...params) => {\n      if (params.length === 1 && params[0] instanceof Object)\n        params = params[0];\n      for (const socket_id of this.namespaces[ns].events[name].sockets) {\n        const socket = this.namespaces[ns].clients.get(socket_id);\n        if (!socket) continue;\n        socket.send(\n          this.dataPack.encode({\n            notification: name,\n            params\n          })\n        );\n      }\n    });\n    return {\n      protected: () => this._makeProtectedEvent(name, ns),\n      public: () => this._makePublicEvent(name, ns)\n    };\n  }\n  /**\n  * Returns a requested namespace object\n  * @method\n  * @param {String} name - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - namespace object\n  */\n  of(name) {\n    if (!this.namespaces[name]) this._generateNamespace(name);\n    const self = this;\n    return {\n      // self.register convenience method\n      register(fn_name, fn) {\n        if (arguments.length !== 2)\n          throw new Error(\"must provide exactly two arguments\");\n        if (typeof fn_name !== \"string\")\n          throw new Error(\"name must be a string\");\n        if (typeof fn !== \"function\")\n          throw new Error(\"handler must be a function\");\n        return self.register(fn_name, fn, name);\n      },\n      // self.event convenience method\n      event(ev_name) {\n        if (arguments.length !== 1)\n          throw new Error(\"must provide exactly one argument\");\n        if (typeof ev_name !== \"string\")\n          throw new Error(\"name must be a string\");\n        return self.event(ev_name, name);\n      },\n      // self.eventList convenience method\n      get eventList() {\n        return Object.keys(self.namespaces[name].events);\n      },\n      /**\n      * Emits a specified event to this namespace.\n      * @inner\n      * @method\n      * @param {String} event - event name\n      * @param {Array} params - event parameters\n      * @return {Undefined}\n      */\n      emit(event, ...params) {\n        const socket_ids = [...self.namespaces[name].clients.keys()];\n        for (let i = 0, id; id = socket_ids[i]; ++i) {\n          self.namespaces[name].clients.get(id).send(\n            self.dataPack.encode({\n              notification: event,\n              params: params || []\n            })\n          );\n        }\n      },\n      /**\n      * Returns a name of this namespace.\n      * @inner\n      * @method\n      * @kind constant\n      * @return {String}\n      */\n      get name() {\n        return name;\n      },\n      /**\n      * Returns a hash of websocket objects connected to this namespace.\n      * @inner\n      * @method\n      * @return {Object}\n      */\n      connected() {\n        const socket_ids = [...self.namespaces[name].clients.keys()];\n        return socket_ids.reduce(\n          (acc, curr) => ({\n            ...acc,\n            [curr]: self.namespaces[name].clients.get(curr)\n          }),\n          {}\n        );\n      },\n      /**\n      * Returns a list of client unique identifiers connected to this namespace.\n      * @inner\n      * @method\n      * @return {Array}\n      */\n      clients() {\n        return self.namespaces[name];\n      }\n    };\n  }\n  /**\n  * Lists all created events in a given namespace. Defaults to \"/\".\n  * @method\n  * @param {String} ns - namespaces identifier\n  * @readonly\n  * @return {Array} - returns a list of created events\n  */\n  eventList(ns = \"/\") {\n    if (!this.namespaces[ns]) return [];\n    return Object.keys(this.namespaces[ns].events);\n  }\n  /**\n  * Creates a JSON-RPC 2.0 compliant error\n  * @method\n  * @param {Number} code - indicates the error type that occurred\n  * @param {String} message - provides a short description of the error\n  * @param {String|Object} data - details containing additional information about the error\n  * @return {Object}\n  */\n  createError(code, message, data) {\n    return {\n      code,\n      message,\n      data: data || null\n    };\n  }\n  /**\n  * Closes the server and terminates all clients.\n  * @method\n  * @return {Promise}\n  */\n  close() {\n    return new Promise((resolve, reject) => {\n      try {\n        this.wss.close();\n        this.emit(\"close\");\n        resolve();\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n  /**\n  * Handles all WebSocket JSON RPC 2.0 requests.\n  * @private\n  * @param {Object} socket - ws socket instance\n  * @param {String} ns - namespaces identifier\n  * @return {Undefined}\n  */\n  _handleRPC(socket, ns = \"/\") {\n    socket.on(\"message\", async (data) => {\n      const msg_options = {};\n      if (data instanceof ArrayBuffer) {\n        msg_options.binary = true;\n        data = Buffer.from(data).toString();\n      }\n      if (socket.readyState !== 1) return;\n      let parsedData;\n      try {\n        parsedData = this.dataPack.decode(data);\n      } catch (error) {\n        return socket.send(\n          this.dataPack.encode({\n            jsonrpc: \"2.0\",\n            error: createError(-32700, error.toString()),\n            id: null\n          }),\n          msg_options\n        );\n      }\n      if (Array.isArray(parsedData)) {\n        if (!parsedData.length)\n          return socket.send(\n            this.dataPack.encode({\n              jsonrpc: \"2.0\",\n              error: createError(-32600, \"Invalid array\"),\n              id: null\n            }),\n            msg_options\n          );\n        const responses = [];\n        for (const message of parsedData) {\n          const response2 = await this._runMethod(message, socket._id, ns);\n          if (!response2) continue;\n          responses.push(response2);\n        }\n        if (!responses.length) return;\n        return socket.send(this.dataPack.encode(responses), msg_options);\n      }\n      const response = await this._runMethod(parsedData, socket._id, ns);\n      if (!response) return;\n      return socket.send(this.dataPack.encode(response), msg_options);\n    });\n  }\n  /**\n  * Runs a defined RPC method.\n  * @private\n  * @param {Object} message - a message received\n  * @param {Object} socket_id - user's socket id\n  * @param {String} ns - namespaces identifier\n  * @return {Object|undefined}\n  */\n  async _runMethod(message, socket_id, ns = \"/\") {\n    if (typeof message !== \"object\" || message === null)\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600),\n        id: null\n      };\n    if (message.jsonrpc !== \"2.0\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600, \"Invalid JSON RPC version\"),\n        id: message.id || null\n      };\n    if (!message.method)\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32602, \"Method not specified\"),\n        id: message.id || null\n      };\n    if (typeof message.method !== \"string\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600, \"Invalid method name\"),\n        id: message.id || null\n      };\n    if (message.params && typeof message.params === \"string\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600),\n        id: message.id || null\n      };\n    if (message.method === \"rpc.on\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32e3),\n          id: message.id || null\n        };\n      const results = {};\n      const event_names = Object.keys(this.namespaces[ns].events);\n      for (const name of message.params) {\n        const index = event_names.indexOf(name);\n        const namespace = this.namespaces[ns];\n        if (index === -1) {\n          results[name] = \"provided event invalid\";\n          continue;\n        }\n        if (namespace.events[event_names[index]].protected === true && namespace.clients.get(socket_id)[\"_authenticated\"] === false) {\n          return {\n            jsonrpc: \"2.0\",\n            error: createError(-32606),\n            id: message.id || null\n          };\n        }\n        const socket_index = namespace.events[event_names[index]].sockets.indexOf(socket_id);\n        if (socket_index >= 0) {\n          results[name] = \"socket has already been subscribed to event\";\n          continue;\n        }\n        namespace.events[event_names[index]].sockets.push(socket_id);\n        results[name] = \"ok\";\n      }\n      return {\n        jsonrpc: \"2.0\",\n        result: results,\n        id: message.id || null\n      };\n    } else if (message.method === \"rpc.off\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32e3),\n          id: message.id || null\n        };\n      const results = {};\n      for (const name of message.params) {\n        if (!this.namespaces[ns].events[name]) {\n          results[name] = \"provided event invalid\";\n          continue;\n        }\n        const index = this.namespaces[ns].events[name].sockets.indexOf(socket_id);\n        if (index === -1) {\n          results[name] = \"not subscribed\";\n          continue;\n        }\n        this.namespaces[ns].events[name].sockets.splice(index, 1);\n        results[name] = \"ok\";\n      }\n      return {\n        jsonrpc: \"2.0\",\n        result: results,\n        id: message.id || null\n      };\n    } else if (message.method === \"rpc.login\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32604),\n          id: message.id || null\n        };\n    }\n    if (!this.namespaces[ns].rpc_methods[message.method]) {\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32601),\n        id: message.id || null\n      };\n    }\n    let response = null;\n    if (this.namespaces[ns].rpc_methods[message.method].protected === true && this.namespaces[ns].clients.get(socket_id)[\"_authenticated\"] === false) {\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32605),\n        id: message.id || null\n      };\n    }\n    try {\n      response = await this.namespaces[ns].rpc_methods[message.method].fn(\n        message.params,\n        socket_id\n      );\n    } catch (error) {\n      if (!message.id) return;\n      if (error instanceof Error)\n        return {\n          jsonrpc: \"2.0\",\n          error: {\n            code: -32e3,\n            message: error.name,\n            data: error.message\n          },\n          id: message.id\n        };\n      return {\n        jsonrpc: \"2.0\",\n        error,\n        id: message.id\n      };\n    }\n    if (!message.id) return;\n    if (message.method === \"rpc.login\" && response === true) {\n      const s = this.namespaces[ns].clients.get(socket_id);\n      s[\"_authenticated\"] = true;\n      this.namespaces[ns].clients.set(socket_id, s);\n    }\n    return {\n      jsonrpc: \"2.0\",\n      result: response,\n      id: message.id\n    };\n  }\n  /**\n  * Generate a new namespace store.\n  * Also preregister some special namespace methods.\n  * @private\n  * @param {String} name - namespaces identifier\n  * @return {undefined}\n  */\n  _generateNamespace(name) {\n    this.namespaces[name] = {\n      rpc_methods: {\n        __listMethods: {\n          fn: () => Object.keys(this.namespaces[name].rpc_methods),\n          protected: false\n        }\n      },\n      clients: /* @__PURE__ */ new Map(),\n      events: {}\n    };\n  }\n};\nvar RPC_ERRORS = /* @__PURE__ */ new Map([\n  [-32e3, \"Event not provided\"],\n  [-32600, \"Invalid Request\"],\n  [-32601, \"Method not found\"],\n  [-32602, \"Invalid params\"],\n  [-32603, \"Internal error\"],\n  [-32604, \"Params not found\"],\n  [-32605, \"Method forbidden\"],\n  [-32606, \"Event forbidden\"],\n  [-32700, \"Parse error\"]\n]);\nfunction createError(code, details) {\n  const error = {\n    code,\n    message: RPC_ERRORS.get(code) || \"Internal Server Error\"\n  };\n  if (details) error[\"data\"] = details;\n  return error;\n}\n\n// src/index.ts\nvar Client = class extends CommonClient {\n  constructor(address = \"ws://localhost:8080\", {\n    autoconnect = true,\n    reconnect = true,\n    reconnect_interval = 1e3,\n    max_reconnects = 5,\n    ...rest_options\n  } = {}, generate_request_id) {\n    super(\n      WebSocket,\n      address,\n      {\n        autoconnect,\n        reconnect,\n        reconnect_interval,\n        max_reconnects,\n        ...rest_options\n      },\n      generate_request_id\n    );\n  }\n};\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/eventemitter3/index.js ***!
  \*************************************************************************/
/***/ ((module) => {

eval("\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif (true) {\n  module.exports = EventEmitter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLFdBQVcsR0FBRztBQUNkLFdBQVcsU0FBUztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsY0FBYztBQUN6QixXQUFXLGlCQUFpQjtBQUM1QixXQUFXLFVBQVU7QUFDckIsV0FBVyxHQUFHO0FBQ2QsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekIsV0FBVyxpQkFBaUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGlCQUFpQjtBQUM1QixhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBLDBEQUEwRCxPQUFPO0FBQ2pFO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGlCQUFpQjtBQUM1QixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGlCQUFpQjtBQUM1QixhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDBDQUEwQyxTQUFTO0FBQ25EO0FBQ0E7O0FBRUE7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQSxnQkFBZ0IsWUFBWTtBQUM1Qjs7QUFFQTtBQUNBLDREQUE0RDtBQUM1RCxnRUFBZ0U7QUFDaEUsb0VBQW9FO0FBQ3BFLHdFQUF3RTtBQUN4RTtBQUNBLDJEQUEyRCxTQUFTO0FBQ3BFO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGlCQUFpQjtBQUM1QixXQUFXLFVBQVU7QUFDckIsV0FBVyxHQUFHO0FBQ2QsYUFBYSxjQUFjO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxpQkFBaUI7QUFDNUIsV0FBVyxVQUFVO0FBQ3JCLFdBQVcsR0FBRztBQUNkLGFBQWEsY0FBYztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsaUJBQWlCO0FBQzVCLFdBQVcsVUFBVTtBQUNyQixXQUFXLEdBQUc7QUFDZCxXQUFXLFNBQVM7QUFDcEIsYUFBYSxjQUFjO0FBQzNCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiw0REFBNEQsWUFBWTtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsaUJBQWlCO0FBQzVCLGFBQWEsY0FBYztBQUMzQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLElBQTZCO0FBQ2pDO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXHJwYy13ZWJzb2NrZXRzXFxub2RlX21vZHVsZXNcXGV2ZW50ZW1pdHRlcjNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIGhhcyA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHlcbiAgLCBwcmVmaXggPSAnfic7XG5cbi8qKlxuICogQ29uc3RydWN0b3IgdG8gY3JlYXRlIGEgc3RvcmFnZSBmb3Igb3VyIGBFRWAgb2JqZWN0cy5cbiAqIEFuIGBFdmVudHNgIGluc3RhbmNlIGlzIGEgcGxhaW4gb2JqZWN0IHdob3NlIHByb3BlcnRpZXMgYXJlIGV2ZW50IG5hbWVzLlxuICpcbiAqIEBjb25zdHJ1Y3RvclxuICogQHByaXZhdGVcbiAqL1xuZnVuY3Rpb24gRXZlbnRzKCkge31cblxuLy9cbi8vIFdlIHRyeSB0byBub3QgaW5oZXJpdCBmcm9tIGBPYmplY3QucHJvdG90eXBlYC4gSW4gc29tZSBlbmdpbmVzIGNyZWF0aW5nIGFuXG4vLyBpbnN0YW5jZSBpbiB0aGlzIHdheSBpcyBmYXN0ZXIgdGhhbiBjYWxsaW5nIGBPYmplY3QuY3JlYXRlKG51bGwpYCBkaXJlY3RseS5cbi8vIElmIGBPYmplY3QuY3JlYXRlKG51bGwpYCBpcyBub3Qgc3VwcG9ydGVkIHdlIHByZWZpeCB0aGUgZXZlbnQgbmFtZXMgd2l0aCBhXG4vLyBjaGFyYWN0ZXIgdG8gbWFrZSBzdXJlIHRoYXQgdGhlIGJ1aWx0LWluIG9iamVjdCBwcm9wZXJ0aWVzIGFyZSBub3Rcbi8vIG92ZXJyaWRkZW4gb3IgdXNlZCBhcyBhbiBhdHRhY2sgdmVjdG9yLlxuLy9cbmlmIChPYmplY3QuY3JlYXRlKSB7XG4gIEV2ZW50cy5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKG51bGwpO1xuXG4gIC8vXG4gIC8vIFRoaXMgaGFjayBpcyBuZWVkZWQgYmVjYXVzZSB0aGUgYF9fcHJvdG9fX2AgcHJvcGVydHkgaXMgc3RpbGwgaW5oZXJpdGVkIGluXG4gIC8vIHNvbWUgb2xkIGJyb3dzZXJzIGxpa2UgQW5kcm9pZCA0LCBpUGhvbmUgNS4xLCBPcGVyYSAxMSBhbmQgU2FmYXJpIDUuXG4gIC8vXG4gIGlmICghbmV3IEV2ZW50cygpLl9fcHJvdG9fXykgcHJlZml4ID0gZmFsc2U7XG59XG5cbi8qKlxuICogUmVwcmVzZW50YXRpb24gb2YgYSBzaW5nbGUgZXZlbnQgbGlzdGVuZXIuXG4gKlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gVGhlIGxpc3RlbmVyIGZ1bmN0aW9uLlxuICogQHBhcmFtIHsqfSBjb250ZXh0IFRoZSBjb250ZXh0IHRvIGludm9rZSB0aGUgbGlzdGVuZXIgd2l0aC5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gW29uY2U9ZmFsc2VdIFNwZWNpZnkgaWYgdGhlIGxpc3RlbmVyIGlzIGEgb25lLXRpbWUgbGlzdGVuZXIuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIEVFKGZuLCBjb250ZXh0LCBvbmNlKSB7XG4gIHRoaXMuZm4gPSBmbjtcbiAgdGhpcy5jb250ZXh0ID0gY29udGV4dDtcbiAgdGhpcy5vbmNlID0gb25jZSB8fCBmYWxzZTtcbn1cblxuLyoqXG4gKiBBZGQgYSBsaXN0ZW5lciBmb3IgYSBnaXZlbiBldmVudC5cbiAqXG4gKiBAcGFyYW0ge0V2ZW50RW1pdHRlcn0gZW1pdHRlciBSZWZlcmVuY2UgdG8gdGhlIGBFdmVudEVtaXR0ZXJgIGluc3RhbmNlLlxuICogQHBhcmFtIHsoU3RyaW5nfFN5bWJvbCl9IGV2ZW50IFRoZSBldmVudCBuYW1lLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gVGhlIGxpc3RlbmVyIGZ1bmN0aW9uLlxuICogQHBhcmFtIHsqfSBjb250ZXh0IFRoZSBjb250ZXh0IHRvIGludm9rZSB0aGUgbGlzdGVuZXIgd2l0aC5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gb25jZSBTcGVjaWZ5IGlmIHRoZSBsaXN0ZW5lciBpcyBhIG9uZS10aW1lIGxpc3RlbmVyLlxuICogQHJldHVybnMge0V2ZW50RW1pdHRlcn1cbiAqIEBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIGFkZExpc3RlbmVyKGVtaXR0ZXIsIGV2ZW50LCBmbiwgY29udGV4dCwgb25jZSkge1xuICBpZiAodHlwZW9mIGZuICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIGxpc3RlbmVyIG11c3QgYmUgYSBmdW5jdGlvbicpO1xuICB9XG5cbiAgdmFyIGxpc3RlbmVyID0gbmV3IEVFKGZuLCBjb250ZXh0IHx8IGVtaXR0ZXIsIG9uY2UpXG4gICAgLCBldnQgPSBwcmVmaXggPyBwcmVmaXggKyBldmVudCA6IGV2ZW50O1xuXG4gIGlmICghZW1pdHRlci5fZXZlbnRzW2V2dF0pIGVtaXR0ZXIuX2V2ZW50c1tldnRdID0gbGlzdGVuZXIsIGVtaXR0ZXIuX2V2ZW50c0NvdW50Kys7XG4gIGVsc2UgaWYgKCFlbWl0dGVyLl9ldmVudHNbZXZ0XS5mbikgZW1pdHRlci5fZXZlbnRzW2V2dF0ucHVzaChsaXN0ZW5lcik7XG4gIGVsc2UgZW1pdHRlci5fZXZlbnRzW2V2dF0gPSBbZW1pdHRlci5fZXZlbnRzW2V2dF0sIGxpc3RlbmVyXTtcblxuICByZXR1cm4gZW1pdHRlcjtcbn1cblxuLyoqXG4gKiBDbGVhciBldmVudCBieSBuYW1lLlxuICpcbiAqIEBwYXJhbSB7RXZlbnRFbWl0dGVyfSBlbWl0dGVyIFJlZmVyZW5jZSB0byB0aGUgYEV2ZW50RW1pdHRlcmAgaW5zdGFuY2UuXG4gKiBAcGFyYW0geyhTdHJpbmd8U3ltYm9sKX0gZXZ0IFRoZSBFdmVudCBuYW1lLlxuICogQHByaXZhdGVcbiAqL1xuZnVuY3Rpb24gY2xlYXJFdmVudChlbWl0dGVyLCBldnQpIHtcbiAgaWYgKC0tZW1pdHRlci5fZXZlbnRzQ291bnQgPT09IDApIGVtaXR0ZXIuX2V2ZW50cyA9IG5ldyBFdmVudHMoKTtcbiAgZWxzZSBkZWxldGUgZW1pdHRlci5fZXZlbnRzW2V2dF07XG59XG5cbi8qKlxuICogTWluaW1hbCBgRXZlbnRFbWl0dGVyYCBpbnRlcmZhY2UgdGhhdCBpcyBtb2xkZWQgYWdhaW5zdCB0aGUgTm9kZS5qc1xuICogYEV2ZW50RW1pdHRlcmAgaW50ZXJmYWNlLlxuICpcbiAqIEBjb25zdHJ1Y3RvclxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBFdmVudEVtaXR0ZXIoKSB7XG4gIHRoaXMuX2V2ZW50cyA9IG5ldyBFdmVudHMoKTtcbiAgdGhpcy5fZXZlbnRzQ291bnQgPSAwO1xufVxuXG4vKipcbiAqIFJldHVybiBhbiBhcnJheSBsaXN0aW5nIHRoZSBldmVudHMgZm9yIHdoaWNoIHRoZSBlbWl0dGVyIGhhcyByZWdpc3RlcmVkXG4gKiBsaXN0ZW5lcnMuXG4gKlxuICogQHJldHVybnMge0FycmF5fVxuICogQHB1YmxpY1xuICovXG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLmV2ZW50TmFtZXMgPSBmdW5jdGlvbiBldmVudE5hbWVzKCkge1xuICB2YXIgbmFtZXMgPSBbXVxuICAgICwgZXZlbnRzXG4gICAgLCBuYW1lO1xuXG4gIGlmICh0aGlzLl9ldmVudHNDb3VudCA9PT0gMCkgcmV0dXJuIG5hbWVzO1xuXG4gIGZvciAobmFtZSBpbiAoZXZlbnRzID0gdGhpcy5fZXZlbnRzKSkge1xuICAgIGlmIChoYXMuY2FsbChldmVudHMsIG5hbWUpKSBuYW1lcy5wdXNoKHByZWZpeCA/IG5hbWUuc2xpY2UoMSkgOiBuYW1lKTtcbiAgfVxuXG4gIGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7XG4gICAgcmV0dXJuIG5hbWVzLmNvbmNhdChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKGV2ZW50cykpO1xuICB9XG5cbiAgcmV0dXJuIG5hbWVzO1xufTtcblxuLyoqXG4gKiBSZXR1cm4gdGhlIGxpc3RlbmVycyByZWdpc3RlcmVkIGZvciBhIGdpdmVuIGV2ZW50LlxuICpcbiAqIEBwYXJhbSB7KFN0cmluZ3xTeW1ib2wpfSBldmVudCBUaGUgZXZlbnQgbmFtZS5cbiAqIEByZXR1cm5zIHtBcnJheX0gVGhlIHJlZ2lzdGVyZWQgbGlzdGVuZXJzLlxuICogQHB1YmxpY1xuICovXG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLmxpc3RlbmVycyA9IGZ1bmN0aW9uIGxpc3RlbmVycyhldmVudCkge1xuICB2YXIgZXZ0ID0gcHJlZml4ID8gcHJlZml4ICsgZXZlbnQgOiBldmVudFxuICAgICwgaGFuZGxlcnMgPSB0aGlzLl9ldmVudHNbZXZ0XTtcblxuICBpZiAoIWhhbmRsZXJzKSByZXR1cm4gW107XG4gIGlmIChoYW5kbGVycy5mbikgcmV0dXJuIFtoYW5kbGVycy5mbl07XG5cbiAgZm9yICh2YXIgaSA9IDAsIGwgPSBoYW5kbGVycy5sZW5ndGgsIGVlID0gbmV3IEFycmF5KGwpOyBpIDwgbDsgaSsrKSB7XG4gICAgZWVbaV0gPSBoYW5kbGVyc1tpXS5mbjtcbiAgfVxuXG4gIHJldHVybiBlZTtcbn07XG5cbi8qKlxuICogUmV0dXJuIHRoZSBudW1iZXIgb2YgbGlzdGVuZXJzIGxpc3RlbmluZyB0byBhIGdpdmVuIGV2ZW50LlxuICpcbiAqIEBwYXJhbSB7KFN0cmluZ3xTeW1ib2wpfSBldmVudCBUaGUgZXZlbnQgbmFtZS5cbiAqIEByZXR1cm5zIHtOdW1iZXJ9IFRoZSBudW1iZXIgb2YgbGlzdGVuZXJzLlxuICogQHB1YmxpY1xuICovXG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLmxpc3RlbmVyQ291bnQgPSBmdW5jdGlvbiBsaXN0ZW5lckNvdW50KGV2ZW50KSB7XG4gIHZhciBldnQgPSBwcmVmaXggPyBwcmVmaXggKyBldmVudCA6IGV2ZW50XG4gICAgLCBsaXN0ZW5lcnMgPSB0aGlzLl9ldmVudHNbZXZ0XTtcblxuICBpZiAoIWxpc3RlbmVycykgcmV0dXJuIDA7XG4gIGlmIChsaXN0ZW5lcnMuZm4pIHJldHVybiAxO1xuICByZXR1cm4gbGlzdGVuZXJzLmxlbmd0aDtcbn07XG5cbi8qKlxuICogQ2FsbHMgZWFjaCBvZiB0aGUgbGlzdGVuZXJzIHJlZ2lzdGVyZWQgZm9yIGEgZ2l2ZW4gZXZlbnQuXG4gKlxuICogQHBhcmFtIHsoU3RyaW5nfFN5bWJvbCl9IGV2ZW50IFRoZSBldmVudCBuYW1lLlxuICogQHJldHVybnMge0Jvb2xlYW59IGB0cnVlYCBpZiB0aGUgZXZlbnQgaGFkIGxpc3RlbmVycywgZWxzZSBgZmFsc2VgLlxuICogQHB1YmxpY1xuICovXG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLmVtaXQgPSBmdW5jdGlvbiBlbWl0KGV2ZW50LCBhMSwgYTIsIGEzLCBhNCwgYTUpIHtcbiAgdmFyIGV2dCA9IHByZWZpeCA/IHByZWZpeCArIGV2ZW50IDogZXZlbnQ7XG5cbiAgaWYgKCF0aGlzLl9ldmVudHNbZXZ0XSkgcmV0dXJuIGZhbHNlO1xuXG4gIHZhciBsaXN0ZW5lcnMgPSB0aGlzLl9ldmVudHNbZXZ0XVxuICAgICwgbGVuID0gYXJndW1lbnRzLmxlbmd0aFxuICAgICwgYXJnc1xuICAgICwgaTtcblxuICBpZiAobGlzdGVuZXJzLmZuKSB7XG4gICAgaWYgKGxpc3RlbmVycy5vbmNlKSB0aGlzLnJlbW92ZUxpc3RlbmVyKGV2ZW50LCBsaXN0ZW5lcnMuZm4sIHVuZGVmaW5lZCwgdHJ1ZSk7XG5cbiAgICBzd2l0Y2ggKGxlbikge1xuICAgICAgY2FzZSAxOiByZXR1cm4gbGlzdGVuZXJzLmZuLmNhbGwobGlzdGVuZXJzLmNvbnRleHQpLCB0cnVlO1xuICAgICAgY2FzZSAyOiByZXR1cm4gbGlzdGVuZXJzLmZuLmNhbGwobGlzdGVuZXJzLmNvbnRleHQsIGExKSwgdHJ1ZTtcbiAgICAgIGNhc2UgMzogcmV0dXJuIGxpc3RlbmVycy5mbi5jYWxsKGxpc3RlbmVycy5jb250ZXh0LCBhMSwgYTIpLCB0cnVlO1xuICAgICAgY2FzZSA0OiByZXR1cm4gbGlzdGVuZXJzLmZuLmNhbGwobGlzdGVuZXJzLmNvbnRleHQsIGExLCBhMiwgYTMpLCB0cnVlO1xuICAgICAgY2FzZSA1OiByZXR1cm4gbGlzdGVuZXJzLmZuLmNhbGwobGlzdGVuZXJzLmNvbnRleHQsIGExLCBhMiwgYTMsIGE0KSwgdHJ1ZTtcbiAgICAgIGNhc2UgNjogcmV0dXJuIGxpc3RlbmVycy5mbi5jYWxsKGxpc3RlbmVycy5jb250ZXh0LCBhMSwgYTIsIGEzLCBhNCwgYTUpLCB0cnVlO1xuICAgIH1cblxuICAgIGZvciAoaSA9IDEsIGFyZ3MgPSBuZXcgQXJyYXkobGVuIC0xKTsgaSA8IGxlbjsgaSsrKSB7XG4gICAgICBhcmdzW2kgLSAxXSA9IGFyZ3VtZW50c1tpXTtcbiAgICB9XG5cbiAgICBsaXN0ZW5lcnMuZm4uYXBwbHkobGlzdGVuZXJzLmNvbnRleHQsIGFyZ3MpO1xuICB9IGVsc2Uge1xuICAgIHZhciBsZW5ndGggPSBsaXN0ZW5lcnMubGVuZ3RoXG4gICAgICAsIGo7XG5cbiAgICBmb3IgKGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmIChsaXN0ZW5lcnNbaV0ub25jZSkgdGhpcy5yZW1vdmVMaXN0ZW5lcihldmVudCwgbGlzdGVuZXJzW2ldLmZuLCB1bmRlZmluZWQsIHRydWUpO1xuXG4gICAgICBzd2l0Y2ggKGxlbikge1xuICAgICAgICBjYXNlIDE6IGxpc3RlbmVyc1tpXS5mbi5jYWxsKGxpc3RlbmVyc1tpXS5jb250ZXh0KTsgYnJlYWs7XG4gICAgICAgIGNhc2UgMjogbGlzdGVuZXJzW2ldLmZuLmNhbGwobGlzdGVuZXJzW2ldLmNvbnRleHQsIGExKTsgYnJlYWs7XG4gICAgICAgIGNhc2UgMzogbGlzdGVuZXJzW2ldLmZuLmNhbGwobGlzdGVuZXJzW2ldLmNvbnRleHQsIGExLCBhMik7IGJyZWFrO1xuICAgICAgICBjYXNlIDQ6IGxpc3RlbmVyc1tpXS5mbi5jYWxsKGxpc3RlbmVyc1tpXS5jb250ZXh0LCBhMSwgYTIsIGEzKTsgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgaWYgKCFhcmdzKSBmb3IgKGogPSAxLCBhcmdzID0gbmV3IEFycmF5KGxlbiAtMSk7IGogPCBsZW47IGorKykge1xuICAgICAgICAgICAgYXJnc1tqIC0gMV0gPSBhcmd1bWVudHNbal07XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgbGlzdGVuZXJzW2ldLmZuLmFwcGx5KGxpc3RlbmVyc1tpXS5jb250ZXh0LCBhcmdzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdHJ1ZTtcbn07XG5cbi8qKlxuICogQWRkIGEgbGlzdGVuZXIgZm9yIGEgZ2l2ZW4gZXZlbnQuXG4gKlxuICogQHBhcmFtIHsoU3RyaW5nfFN5bWJvbCl9IGV2ZW50IFRoZSBldmVudCBuYW1lLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gVGhlIGxpc3RlbmVyIGZ1bmN0aW9uLlxuICogQHBhcmFtIHsqfSBbY29udGV4dD10aGlzXSBUaGUgY29udGV4dCB0byBpbnZva2UgdGhlIGxpc3RlbmVyIHdpdGguXG4gKiBAcmV0dXJucyB7RXZlbnRFbWl0dGVyfSBgdGhpc2AuXG4gKiBAcHVibGljXG4gKi9cbkV2ZW50RW1pdHRlci5wcm90b3R5cGUub24gPSBmdW5jdGlvbiBvbihldmVudCwgZm4sIGNvbnRleHQpIHtcbiAgcmV0dXJuIGFkZExpc3RlbmVyKHRoaXMsIGV2ZW50LCBmbiwgY29udGV4dCwgZmFsc2UpO1xufTtcblxuLyoqXG4gKiBBZGQgYSBvbmUtdGltZSBsaXN0ZW5lciBmb3IgYSBnaXZlbiBldmVudC5cbiAqXG4gKiBAcGFyYW0geyhTdHJpbmd8U3ltYm9sKX0gZXZlbnQgVGhlIGV2ZW50IG5hbWUuXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBmbiBUaGUgbGlzdGVuZXIgZnVuY3Rpb24uXG4gKiBAcGFyYW0geyp9IFtjb250ZXh0PXRoaXNdIFRoZSBjb250ZXh0IHRvIGludm9rZSB0aGUgbGlzdGVuZXIgd2l0aC5cbiAqIEByZXR1cm5zIHtFdmVudEVtaXR0ZXJ9IGB0aGlzYC5cbiAqIEBwdWJsaWNcbiAqL1xuRXZlbnRFbWl0dGVyLnByb3RvdHlwZS5vbmNlID0gZnVuY3Rpb24gb25jZShldmVudCwgZm4sIGNvbnRleHQpIHtcbiAgcmV0dXJuIGFkZExpc3RlbmVyKHRoaXMsIGV2ZW50LCBmbiwgY29udGV4dCwgdHJ1ZSk7XG59O1xuXG4vKipcbiAqIFJlbW92ZSB0aGUgbGlzdGVuZXJzIG9mIGEgZ2l2ZW4gZXZlbnQuXG4gKlxuICogQHBhcmFtIHsoU3RyaW5nfFN5bWJvbCl9IGV2ZW50IFRoZSBldmVudCBuYW1lLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gT25seSByZW1vdmUgdGhlIGxpc3RlbmVycyB0aGF0IG1hdGNoIHRoaXMgZnVuY3Rpb24uXG4gKiBAcGFyYW0geyp9IGNvbnRleHQgT25seSByZW1vdmUgdGhlIGxpc3RlbmVycyB0aGF0IGhhdmUgdGhpcyBjb250ZXh0LlxuICogQHBhcmFtIHtCb29sZWFufSBvbmNlIE9ubHkgcmVtb3ZlIG9uZS10aW1lIGxpc3RlbmVycy5cbiAqIEByZXR1cm5zIHtFdmVudEVtaXR0ZXJ9IGB0aGlzYC5cbiAqIEBwdWJsaWNcbiAqL1xuRXZlbnRFbWl0dGVyLnByb3RvdHlwZS5yZW1vdmVMaXN0ZW5lciA9IGZ1bmN0aW9uIHJlbW92ZUxpc3RlbmVyKGV2ZW50LCBmbiwgY29udGV4dCwgb25jZSkge1xuICB2YXIgZXZ0ID0gcHJlZml4ID8gcHJlZml4ICsgZXZlbnQgOiBldmVudDtcblxuICBpZiAoIXRoaXMuX2V2ZW50c1tldnRdKSByZXR1cm4gdGhpcztcbiAgaWYgKCFmbikge1xuICAgIGNsZWFyRXZlbnQodGhpcywgZXZ0KTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHZhciBsaXN0ZW5lcnMgPSB0aGlzLl9ldmVudHNbZXZ0XTtcblxuICBpZiAobGlzdGVuZXJzLmZuKSB7XG4gICAgaWYgKFxuICAgICAgbGlzdGVuZXJzLmZuID09PSBmbiAmJlxuICAgICAgKCFvbmNlIHx8IGxpc3RlbmVycy5vbmNlKSAmJlxuICAgICAgKCFjb250ZXh0IHx8IGxpc3RlbmVycy5jb250ZXh0ID09PSBjb250ZXh0KVxuICAgICkge1xuICAgICAgY2xlYXJFdmVudCh0aGlzLCBldnQpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKHZhciBpID0gMCwgZXZlbnRzID0gW10sIGxlbmd0aCA9IGxpc3RlbmVycy5sZW5ndGg7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgICAgaWYgKFxuICAgICAgICBsaXN0ZW5lcnNbaV0uZm4gIT09IGZuIHx8XG4gICAgICAgIChvbmNlICYmICFsaXN0ZW5lcnNbaV0ub25jZSkgfHxcbiAgICAgICAgKGNvbnRleHQgJiYgbGlzdGVuZXJzW2ldLmNvbnRleHQgIT09IGNvbnRleHQpXG4gICAgICApIHtcbiAgICAgICAgZXZlbnRzLnB1c2gobGlzdGVuZXJzW2ldKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvL1xuICAgIC8vIFJlc2V0IHRoZSBhcnJheSwgb3IgcmVtb3ZlIGl0IGNvbXBsZXRlbHkgaWYgd2UgaGF2ZSBubyBtb3JlIGxpc3RlbmVycy5cbiAgICAvL1xuICAgIGlmIChldmVudHMubGVuZ3RoKSB0aGlzLl9ldmVudHNbZXZ0XSA9IGV2ZW50cy5sZW5ndGggPT09IDEgPyBldmVudHNbMF0gOiBldmVudHM7XG4gICAgZWxzZSBjbGVhckV2ZW50KHRoaXMsIGV2dCk7XG4gIH1cblxuICByZXR1cm4gdGhpcztcbn07XG5cbi8qKlxuICogUmVtb3ZlIGFsbCBsaXN0ZW5lcnMsIG9yIHRob3NlIG9mIHRoZSBzcGVjaWZpZWQgZXZlbnQuXG4gKlxuICogQHBhcmFtIHsoU3RyaW5nfFN5bWJvbCl9IFtldmVudF0gVGhlIGV2ZW50IG5hbWUuXG4gKiBAcmV0dXJucyB7RXZlbnRFbWl0dGVyfSBgdGhpc2AuXG4gKiBAcHVibGljXG4gKi9cbkV2ZW50RW1pdHRlci5wcm90b3R5cGUucmVtb3ZlQWxsTGlzdGVuZXJzID0gZnVuY3Rpb24gcmVtb3ZlQWxsTGlzdGVuZXJzKGV2ZW50KSB7XG4gIHZhciBldnQ7XG5cbiAgaWYgKGV2ZW50KSB7XG4gICAgZXZ0ID0gcHJlZml4ID8gcHJlZml4ICsgZXZlbnQgOiBldmVudDtcbiAgICBpZiAodGhpcy5fZXZlbnRzW2V2dF0pIGNsZWFyRXZlbnQodGhpcywgZXZ0KTtcbiAgfSBlbHNlIHtcbiAgICB0aGlzLl9ldmVudHMgPSBuZXcgRXZlbnRzKCk7XG4gICAgdGhpcy5fZXZlbnRzQ291bnQgPSAwO1xuICB9XG5cbiAgcmV0dXJuIHRoaXM7XG59O1xuXG4vL1xuLy8gQWxpYXMgbWV0aG9kcyBuYW1lcyBiZWNhdXNlIHBlb3BsZSByb2xsIGxpa2UgdGhhdC5cbi8vXG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLm9mZiA9IEV2ZW50RW1pdHRlci5wcm90b3R5cGUucmVtb3ZlTGlzdGVuZXI7XG5FdmVudEVtaXR0ZXIucHJvdG90eXBlLmFkZExpc3RlbmVyID0gRXZlbnRFbWl0dGVyLnByb3RvdHlwZS5vbjtcblxuLy9cbi8vIEV4cG9zZSB0aGUgcHJlZml4LlxuLy9cbkV2ZW50RW1pdHRlci5wcmVmaXhlZCA9IHByZWZpeDtcblxuLy9cbi8vIEFsbG93IGBFdmVudEVtaXR0ZXJgIHRvIGJlIGltcG9ydGVkIGFzIG1vZHVsZSBuYW1lc3BhY2UuXG4vL1xuRXZlbnRFbWl0dGVyLkV2ZW50RW1pdHRlciA9IEV2ZW50RW1pdHRlcjtcblxuLy9cbi8vIEV4cG9zZSB0aGUgbW9kdWxlLlxuLy9cbmlmICgndW5kZWZpbmVkJyAhPT0gdHlwZW9mIG1vZHVsZSkge1xuICBtb2R1bGUuZXhwb3J0cyA9IEV2ZW50RW1pdHRlcjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventEmitter: () => (/* reexport default export from named module */ _index_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_index_js__WEBPACK_IMPORTED_MODULE_0__);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQzs7QUFFZDtBQUN2QixpRUFBZSxzQ0FBWSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xccnBjLXdlYnNvY2tldHNcXG5vZGVfbW9kdWxlc1xcZXZlbnRlbWl0dGVyM1xcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBFdmVudEVtaXR0ZXIgZnJvbSAnLi9pbmRleC5qcydcblxuZXhwb3J0IHsgRXZlbnRFbWl0dGVyIH1cbmV4cG9ydCBkZWZhdWx0IEV2ZW50RW1pdHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/regex.js":
/*!******************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/regex.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9yZWdleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYyxFQUFFLFVBQVUsRUFBRSxlQUFlLEVBQUUsZ0JBQWdCLEVBQUUsVUFBVSxHQUFHLHlDQUF5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xccnBjLXdlYnNvY2tldHNcXG5vZGVfbW9kdWxlc1xcdXVpZFxcZGlzdFxcZXNtLW5vZGVcXHJlZ2V4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IC9eKD86WzAtOWEtZl17OH0tWzAtOWEtZl17NH0tWzEtNV1bMC05YS1mXXszfS1bODlhYl1bMC05YS1mXXszfS1bMC05YS1mXXsxMn18MDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAwKSQvaTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/rng.js":
/*!****************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/rng.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\n\nlet poolPtr = rnds8Pool.length;\nfunction rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    crypto__WEBPACK_IMPORTED_MODULE_0___default().randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9ybmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRCO0FBQzVCLHVDQUF1Qzs7QUFFdkM7QUFDZTtBQUNmO0FBQ0EsSUFBSSw0REFBcUI7QUFDekI7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxycGMtd2Vic29ja2V0c1xcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tbm9kZVxccm5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJztcbmNvbnN0IHJuZHM4UG9vbCA9IG5ldyBVaW50OEFycmF5KDI1Nik7IC8vICMgb2YgcmFuZG9tIHZhbHVlcyB0byBwcmUtYWxsb2NhdGVcblxubGV0IHBvb2xQdHIgPSBybmRzOFBvb2wubGVuZ3RoO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcm5nKCkge1xuICBpZiAocG9vbFB0ciA+IHJuZHM4UG9vbC5sZW5ndGggLSAxNikge1xuICAgIGNyeXB0by5yYW5kb21GaWxsU3luYyhybmRzOFBvb2wpO1xuICAgIHBvb2xQdHIgPSAwO1xuICB9XG5cbiAgcmV0dXJuIHJuZHM4UG9vbC5zbGljZShwb29sUHRyLCBwb29sUHRyICs9IDE2KTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/rng.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/stringify.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/stringify.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\n\nfunction stringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  const uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9zdHJpbmdpZnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsZ0JBQWdCLFNBQVM7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSw0Z0JBQTRnQjtBQUM1Z0I7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsT0FBTyx3REFBUTtBQUNmO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxpRUFBZSxTQUFTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxycGMtd2Vic29ja2V0c1xcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tbm9kZVxcc3RyaW5naWZ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB2YWxpZGF0ZSBmcm9tICcuL3ZhbGlkYXRlLmpzJztcbi8qKlxuICogQ29udmVydCBhcnJheSBvZiAxNiBieXRlIHZhbHVlcyB0byBVVUlEIHN0cmluZyBmb3JtYXQgb2YgdGhlIGZvcm06XG4gKiBYWFhYWFhYWC1YWFhYLVhYWFgtWFhYWC1YWFhYWFhYWFhYWFhcbiAqL1xuXG5jb25zdCBieXRlVG9IZXggPSBbXTtcblxuZm9yIChsZXQgaSA9IDA7IGkgPCAyNTY7ICsraSkge1xuICBieXRlVG9IZXgucHVzaCgoaSArIDB4MTAwKS50b1N0cmluZygxNikuc3Vic3RyKDEpKTtcbn1cblxuZnVuY3Rpb24gc3RyaW5naWZ5KGFyciwgb2Zmc2V0ID0gMCkge1xuICAvLyBOb3RlOiBCZSBjYXJlZnVsIGVkaXRpbmcgdGhpcyBjb2RlISAgSXQncyBiZWVuIHR1bmVkIGZvciBwZXJmb3JtYW5jZVxuICAvLyBhbmQgd29ya3MgaW4gd2F5cyB5b3UgbWF5IG5vdCBleHBlY3QuIFNlZSBodHRwczovL2dpdGh1Yi5jb20vdXVpZGpzL3V1aWQvcHVsbC80MzRcbiAgY29uc3QgdXVpZCA9IChieXRlVG9IZXhbYXJyW29mZnNldCArIDBdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMV1dICsgYnl0ZVRvSGV4W2FycltvZmZzZXQgKyAyXV0gKyBieXRlVG9IZXhbYXJyW29mZnNldCArIDNdXSArICctJyArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgNF1dICsgYnl0ZVRvSGV4W2FycltvZmZzZXQgKyA1XV0gKyAnLScgKyBieXRlVG9IZXhbYXJyW29mZnNldCArIDZdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgN11dICsgJy0nICsgYnl0ZVRvSGV4W2FycltvZmZzZXQgKyA4XV0gKyBieXRlVG9IZXhbYXJyW29mZnNldCArIDldXSArICctJyArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTBdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTFdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTJdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTNdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTRdXSArIGJ5dGVUb0hleFthcnJbb2Zmc2V0ICsgMTVdXSkudG9Mb3dlckNhc2UoKTsgLy8gQ29uc2lzdGVuY3kgY2hlY2sgZm9yIHZhbGlkIFVVSUQuICBJZiB0aGlzIHRocm93cywgaXQncyBsaWtlbHkgZHVlIHRvIG9uZVxuICAvLyBvZiB0aGUgZm9sbG93aW5nOlxuICAvLyAtIE9uZSBvciBtb3JlIGlucHV0IGFycmF5IHZhbHVlcyBkb24ndCBtYXAgdG8gYSBoZXggb2N0ZXQgKGxlYWRpbmcgdG9cbiAgLy8gXCJ1bmRlZmluZWRcIiBpbiB0aGUgdXVpZClcbiAgLy8gLSBJbnZhbGlkIGlucHV0IHZhbHVlcyBmb3IgdGhlIFJGQyBgdmVyc2lvbmAgb3IgYHZhcmlhbnRgIGZpZWxkc1xuXG4gIGlmICghdmFsaWRhdGUodXVpZCkpIHtcbiAgICB0aHJvdyBUeXBlRXJyb3IoJ1N0cmluZ2lmaWVkIFVVSUQgaXMgaW52YWxpZCcpO1xuICB9XG5cbiAgcmV0dXJuIHV1aWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHN0cmluZ2lmeTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/v1.js":
/*!***************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/v1.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rng.js */ \"(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/stringify.js\");\n\n // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(b);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS92MS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkI7QUFDWSxDQUFDO0FBQ3hDO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlOzs7QUFHZjtBQUNBLG9CQUFvQjs7QUFFcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdGQUFnRjtBQUNoRjtBQUNBOztBQUVBO0FBQ0Esd0RBQXdELCtDQUFHOztBQUUzRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7OztBQUdBLHdFQUF3RTtBQUN4RTs7QUFFQSw0RUFBNEU7O0FBRTVFLGdFQUFnRTs7QUFFaEU7QUFDQTtBQUNBLElBQUk7QUFDSjs7O0FBR0E7QUFDQTtBQUNBLElBQUk7OztBQUdKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCOztBQUV4QiwyQkFBMkI7O0FBRTNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCOztBQUV0QjtBQUNBO0FBQ0EsdUJBQXVCOztBQUV2QixvQ0FBb0M7O0FBRXBDLDhCQUE4Qjs7QUFFOUIsa0NBQWtDOztBQUVsQyw0QkFBNEI7O0FBRTVCLGtCQUFrQixPQUFPO0FBQ3pCO0FBQ0E7O0FBRUEsZ0JBQWdCLHlEQUFTO0FBQ3pCOztBQUVBLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXHJwYy13ZWJzb2NrZXRzXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFx2MS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcm5nIGZyb20gJy4vcm5nLmpzJztcbmltcG9ydCBzdHJpbmdpZnkgZnJvbSAnLi9zdHJpbmdpZnkuanMnOyAvLyAqKmB2MSgpYCAtIEdlbmVyYXRlIHRpbWUtYmFzZWQgVVVJRCoqXG4vL1xuLy8gSW5zcGlyZWQgYnkgaHR0cHM6Ly9naXRodWIuY29tL0xpb3NLL1VVSUQuanNcbi8vIGFuZCBodHRwOi8vZG9jcy5weXRob24ub3JnL2xpYnJhcnkvdXVpZC5odG1sXG5cbmxldCBfbm9kZUlkO1xuXG5sZXQgX2Nsb2Nrc2VxOyAvLyBQcmV2aW91cyB1dWlkIGNyZWF0aW9uIHRpbWVcblxuXG5sZXQgX2xhc3RNU2VjcyA9IDA7XG5sZXQgX2xhc3ROU2VjcyA9IDA7IC8vIFNlZSBodHRwczovL2dpdGh1Yi5jb20vdXVpZGpzL3V1aWQgZm9yIEFQSSBkZXRhaWxzXG5cbmZ1bmN0aW9uIHYxKG9wdGlvbnMsIGJ1Ziwgb2Zmc2V0KSB7XG4gIGxldCBpID0gYnVmICYmIG9mZnNldCB8fCAwO1xuICBjb25zdCBiID0gYnVmIHx8IG5ldyBBcnJheSgxNik7XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICBsZXQgbm9kZSA9IG9wdGlvbnMubm9kZSB8fCBfbm9kZUlkO1xuICBsZXQgY2xvY2tzZXEgPSBvcHRpb25zLmNsb2Nrc2VxICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLmNsb2Nrc2VxIDogX2Nsb2Nrc2VxOyAvLyBub2RlIGFuZCBjbG9ja3NlcSBuZWVkIHRvIGJlIGluaXRpYWxpemVkIHRvIHJhbmRvbSB2YWx1ZXMgaWYgdGhleSdyZSBub3RcbiAgLy8gc3BlY2lmaWVkLiAgV2UgZG8gdGhpcyBsYXppbHkgdG8gbWluaW1pemUgaXNzdWVzIHJlbGF0ZWQgdG8gaW5zdWZmaWNpZW50XG4gIC8vIHN5c3RlbSBlbnRyb3B5LiAgU2VlICMxODlcblxuICBpZiAobm9kZSA9PSBudWxsIHx8IGNsb2Nrc2VxID09IG51bGwpIHtcbiAgICBjb25zdCBzZWVkQnl0ZXMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpO1xuXG4gICAgaWYgKG5vZGUgPT0gbnVsbCkge1xuICAgICAgLy8gUGVyIDQuNSwgY3JlYXRlIGFuZCA0OC1iaXQgbm9kZSBpZCwgKDQ3IHJhbmRvbSBiaXRzICsgbXVsdGljYXN0IGJpdCA9IDEpXG4gICAgICBub2RlID0gX25vZGVJZCA9IFtzZWVkQnl0ZXNbMF0gfCAweDAxLCBzZWVkQnl0ZXNbMV0sIHNlZWRCeXRlc1syXSwgc2VlZEJ5dGVzWzNdLCBzZWVkQnl0ZXNbNF0sIHNlZWRCeXRlc1s1XV07XG4gICAgfVxuXG4gICAgaWYgKGNsb2Nrc2VxID09IG51bGwpIHtcbiAgICAgIC8vIFBlciA0LjIuMiwgcmFuZG9taXplICgxNCBiaXQpIGNsb2Nrc2VxXG4gICAgICBjbG9ja3NlcSA9IF9jbG9ja3NlcSA9IChzZWVkQnl0ZXNbNl0gPDwgOCB8IHNlZWRCeXRlc1s3XSkgJiAweDNmZmY7XG4gICAgfVxuICB9IC8vIFVVSUQgdGltZXN0YW1wcyBhcmUgMTAwIG5hbm8tc2Vjb25kIHVuaXRzIHNpbmNlIHRoZSBHcmVnb3JpYW4gZXBvY2gsXG4gIC8vICgxNTgyLTEwLTE1IDAwOjAwKS4gIEpTTnVtYmVycyBhcmVuJ3QgcHJlY2lzZSBlbm91Z2ggZm9yIHRoaXMsIHNvXG4gIC8vIHRpbWUgaXMgaGFuZGxlZCBpbnRlcm5hbGx5IGFzICdtc2VjcycgKGludGVnZXIgbWlsbGlzZWNvbmRzKSBhbmQgJ25zZWNzJ1xuICAvLyAoMTAwLW5hbm9zZWNvbmRzIG9mZnNldCBmcm9tIG1zZWNzKSBzaW5jZSB1bml4IGVwb2NoLCAxOTcwLTAxLTAxIDAwOjAwLlxuXG5cbiAgbGV0IG1zZWNzID0gb3B0aW9ucy5tc2VjcyAhPT0gdW5kZWZpbmVkID8gb3B0aW9ucy5tc2VjcyA6IERhdGUubm93KCk7IC8vIFBlciA0LjIuMS4yLCB1c2UgY291bnQgb2YgdXVpZCdzIGdlbmVyYXRlZCBkdXJpbmcgdGhlIGN1cnJlbnQgY2xvY2tcbiAgLy8gY3ljbGUgdG8gc2ltdWxhdGUgaGlnaGVyIHJlc29sdXRpb24gY2xvY2tcblxuICBsZXQgbnNlY3MgPSBvcHRpb25zLm5zZWNzICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLm5zZWNzIDogX2xhc3ROU2VjcyArIDE7IC8vIFRpbWUgc2luY2UgbGFzdCB1dWlkIGNyZWF0aW9uIChpbiBtc2VjcylcblxuICBjb25zdCBkdCA9IG1zZWNzIC0gX2xhc3RNU2VjcyArIChuc2VjcyAtIF9sYXN0TlNlY3MpIC8gMTAwMDA7IC8vIFBlciA0LjIuMS4yLCBCdW1wIGNsb2Nrc2VxIG9uIGNsb2NrIHJlZ3Jlc3Npb25cblxuICBpZiAoZHQgPCAwICYmIG9wdGlvbnMuY2xvY2tzZXEgPT09IHVuZGVmaW5lZCkge1xuICAgIGNsb2Nrc2VxID0gY2xvY2tzZXEgKyAxICYgMHgzZmZmO1xuICB9IC8vIFJlc2V0IG5zZWNzIGlmIGNsb2NrIHJlZ3Jlc3NlcyAobmV3IGNsb2Nrc2VxKSBvciB3ZSd2ZSBtb3ZlZCBvbnRvIGEgbmV3XG4gIC8vIHRpbWUgaW50ZXJ2YWxcblxuXG4gIGlmICgoZHQgPCAwIHx8IG1zZWNzID4gX2xhc3RNU2VjcykgJiYgb3B0aW9ucy5uc2VjcyA9PT0gdW5kZWZpbmVkKSB7XG4gICAgbnNlY3MgPSAwO1xuICB9IC8vIFBlciA0LjIuMS4yIFRocm93IGVycm9yIGlmIHRvbyBtYW55IHV1aWRzIGFyZSByZXF1ZXN0ZWRcblxuXG4gIGlmIChuc2VjcyA+PSAxMDAwMCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcInV1aWQudjEoKTogQ2FuJ3QgY3JlYXRlIG1vcmUgdGhhbiAxME0gdXVpZHMvc2VjXCIpO1xuICB9XG5cbiAgX2xhc3RNU2VjcyA9IG1zZWNzO1xuICBfbGFzdE5TZWNzID0gbnNlY3M7XG4gIF9jbG9ja3NlcSA9IGNsb2Nrc2VxOyAvLyBQZXIgNC4xLjQgLSBDb252ZXJ0IGZyb20gdW5peCBlcG9jaCB0byBHcmVnb3JpYW4gZXBvY2hcblxuICBtc2VjcyArPSAxMjIxOTI5MjgwMDAwMDsgLy8gYHRpbWVfbG93YFxuXG4gIGNvbnN0IHRsID0gKChtc2VjcyAmIDB4ZmZmZmZmZikgKiAxMDAwMCArIG5zZWNzKSAlIDB4MTAwMDAwMDAwO1xuICBiW2krK10gPSB0bCA+Pj4gMjQgJiAweGZmO1xuICBiW2krK10gPSB0bCA+Pj4gMTYgJiAweGZmO1xuICBiW2krK10gPSB0bCA+Pj4gOCAmIDB4ZmY7XG4gIGJbaSsrXSA9IHRsICYgMHhmZjsgLy8gYHRpbWVfbWlkYFxuXG4gIGNvbnN0IHRtaCA9IG1zZWNzIC8gMHgxMDAwMDAwMDAgKiAxMDAwMCAmIDB4ZmZmZmZmZjtcbiAgYltpKytdID0gdG1oID4+PiA4ICYgMHhmZjtcbiAgYltpKytdID0gdG1oICYgMHhmZjsgLy8gYHRpbWVfaGlnaF9hbmRfdmVyc2lvbmBcblxuICBiW2krK10gPSB0bWggPj4+IDI0ICYgMHhmIHwgMHgxMDsgLy8gaW5jbHVkZSB2ZXJzaW9uXG5cbiAgYltpKytdID0gdG1oID4+PiAxNiAmIDB4ZmY7IC8vIGBjbG9ja19zZXFfaGlfYW5kX3Jlc2VydmVkYCAoUGVyIDQuMi4yIC0gaW5jbHVkZSB2YXJpYW50KVxuXG4gIGJbaSsrXSA9IGNsb2Nrc2VxID4+PiA4IHwgMHg4MDsgLy8gYGNsb2NrX3NlcV9sb3dgXG5cbiAgYltpKytdID0gY2xvY2tzZXEgJiAweGZmOyAvLyBgbm9kZWBcblxuICBmb3IgKGxldCBuID0gMDsgbiA8IDY7ICsrbikge1xuICAgIGJbaSArIG5dID0gbm9kZVtuXTtcbiAgfVxuXG4gIHJldHVybiBidWYgfHwgc3RyaW5naWZ5KGIpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2MTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/v1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/validate.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/validate.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS92YWxpZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjs7QUFFL0I7QUFDQSxxQ0FBcUMsaURBQUs7QUFDMUM7O0FBRUEsaUVBQWUsUUFBUSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xccnBjLXdlYnNvY2tldHNcXG5vZGVfbW9kdWxlc1xcdXVpZFxcZGlzdFxcZXNtLW5vZGVcXHZhbGlkYXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSRUdFWCBmcm9tICcuL3JlZ2V4LmpzJztcblxuZnVuY3Rpb24gdmFsaWRhdGUodXVpZCkge1xuICByZXR1cm4gdHlwZW9mIHV1aWQgPT09ICdzdHJpbmcnICYmIFJFR0VYLnRlc3QodXVpZCk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rpc-websockets/node_modules/uuid/dist/esm-node/validate.js\n");

/***/ })

};
;