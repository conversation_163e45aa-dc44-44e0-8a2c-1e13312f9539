import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';

import { ROUTES } from '@/constants';
import { useTranslation } from '@/hooks/useTranslation';

interface MobileMenuProps {
  menuRef: React.RefObject<HTMLDivElement | null>;
  isActiveRoute: (route: string) => boolean;
  handleNavItemClick: (item: any) => void;
  hideCreate: boolean;
  handleCreateTokenClick: () => void;
  handleCreatePerkClick: () => void;
  authenticated: boolean;
  login: () => void;
  logout: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  menuRef,
  isActiveRoute,
  handleNavItemClick,
  hideCreate,
  handleCreateTokenClick,
  handleCreatePerkClick,
  authenticated,
  login,
  logout,
}) => {
  const { t, isReady } = useTranslation();

  const navigationItems = [
    {
      label: t('navigation.dashboard'),
      route: ROUTES.DASHBOARD,
      icon: "/icons/dashboard.svg",
      activeIcon: "/icons/dashboard.svg",
      requiresAuth: true,
    },
    {
      label: t('navigation.perks'),
      route: ROUTES.PERKS,
      icon: "/icons/perks.svg",
      activeIcon: "/icons/perks.svg",
      requiresAuth: false,
    },
    {
      label: t('navigation.coins'),
      route: ROUTES.HOME,
      icon: "/icons/coin.svg",
      activeIcon: "/icons/coin.svg",
      requiresAuth: false,
    },
  ];

  return (
    !isReady ? null : (
      <motion.div
        ref={menuRef}
        className="absolute top-full left-0 right-0 z-50 lg:hidden flex flex-col bg-[#F5F5F5] border-b border-[#E6E6E6] overflow-hidden shadow-lg"
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: "auto", opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div className="p-3 flex flex-col gap-2">
          {navigationItems.map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`flex items-center gap-2 p-2.5 text-sm font-medium cursor-pointer rounded-lg transition-colors ${
                isActiveRoute(item.route)
                  ? "bg-black text-white"
                  : "hover:bg-gray-200"
              }`}
              onClick={() => handleNavItemClick(item)}
            >
              <Image
                src={isActiveRoute(item.route) ? item.activeIcon : item.icon}
                alt={item.label}
                width={20}
                height={20}
                className={`${isActiveRoute(item.route) ? "invert" : ""} h-auto`}
              />
              {item.label}
            </motion.div>
          ))}
          {!hideCreate && (
            <>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="flex items-center p-2.5 text-sm font-medium cursor-pointer rounded-lg hover:bg-gray-200"
                onClick={handleCreateTokenClick}
              >
                {t('header.createYourToken')}
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.45 }}
                className="flex items-center p-2.5 text-sm font-medium cursor-pointer rounded-lg hover:bg-gray-200"
                onClick={handleCreatePerkClick}
              >
                {t('header.createPerk')}
              </motion.div>
            </>
          )}

          <div className="border-t border-gray-300 mt-1 pt-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              {!authenticated ? (
                <div
                  onClick={login}
                  className="flex items-center p-2.5 text-sm font-medium cursor-pointer bg-black text-white rounded-lg m-1 hover:bg-gray-800 transition-colors"
                >
                  {t('auth.login')}
                </div>
              ) : (
                <div
                  className="flex items-center p-2.5 text-sm font-medium cursor-pointer bg-black text-white rounded-lg m-1 hover:bg-gray-800 transition-colors"
                  onClick={logout}
                >
                  {t('navigation.logout')}
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </motion.div>
    )
  );
};

export default MobileMenu; 