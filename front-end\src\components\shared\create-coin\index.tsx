'use client';

import React from 'react';
import dynamic from 'next/dynamic';

const CreateCoinForm = dynamic(() => import('@/components/auth/create-coin-form').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

export default function CreateCoinPage({ onClose }: { onClose: () => void }) {
  return (
    <div className="bg-[#000000c7] flex flex-col items-center justify-center p-4 fixed top-0 left-0 w-full h-full z-10">
      
    </div>
  );
}
