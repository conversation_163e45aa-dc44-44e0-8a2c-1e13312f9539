import { useState, useCallback } from 'react';
import { 
  CongratulationType, 
  ActionButton, 
  SuccessMetric,
  CongratulationsModalProps 
} from '@/components/ui/CongratulationsModal';
import { EscrowOperationType } from '@/utils/escrow';

// Modal State Interface
interface ModalState {
  isOpen: boolean;
  type: CongratulationType;
  title?: string;
  message?: string;
  subtitle?: string;
  actions?: ActionButton[];
  metrics?: SuccessMetric[];
  transactionId?: string;
  amount?: string;
  autoDismiss?: boolean;
  showConfetti?: boolean;
}

// Initial state
const initialState: ModalState = {
  isOpen: false,
  type: 'general_success',
  autoDismiss: true,
  showConfetti: true
};

// Hook Props
interface UseCongratulationsModalProps {
  onNavigateToChat?: (chatRoomId: string) => void;
  onViewTransaction?: (txId: string) => void;
  onShareSuccess?: (type: CongratulationType, txId?: string) => void;
  onContinueShopping?: () => void;
}

// Hook Return Type
interface UseCongratulationsModalReturn {
  // State
  modalState: ModalState;
  isOpen: boolean;
  
  // Actions
  showCongratulations: (config: Partial<ModalState>) => void;
  hideCongratulations: () => void;
  
  // Escrow-specific helpers
  showEscrowSuccess: (
    operation: EscrowOperationType,
    txId: string,
    amount?: string,
    additionalData?: any
  ) => void;
  
  // Quick success methods
  showPurchaseSuccess: (txId: string, amount: string, chatRoomId?: string) => void;
  showTransactionComplete: (txId: string, amount?: string) => void;
  showFundsReleased: (txId: string, amount: string) => void;
  showRefundProcessed: (txId: string, amount: string) => void;
  showDisputeResolved: (disputeId: string, resolution?: string) => void;
  
  // Modal props for component
  getModalProps: () => Omit<CongratulationsModalProps, 'children'>;
}

export const useCongratulationsModal = ({
  onNavigateToChat,
  onViewTransaction,
  onShareSuccess,
  onContinueShopping
}: UseCongratulationsModalProps = {}): UseCongratulationsModalReturn => {
  
  const [modalState, setModalState] = useState<ModalState>(initialState);

  // Show congratulations modal
  const showCongratulations = useCallback((config: Partial<ModalState>) => {
    setModalState(prev => ({
      ...prev,
      ...config,
      isOpen: true
    }));
  }, []);

  // Hide congratulations modal
  const hideCongratulations = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }));
  }, []);

  // Create action buttons based on context
  const createActions = useCallback((
    type: CongratulationType,
    txId?: string,
    chatRoomId?: string,
    additionalActions?: ActionButton[]
  ): ActionButton[] => {
    const actions: ActionButton[] = [];

    // Add context-specific actions
    switch (type) {
      case 'purchase_success':
        if (chatRoomId && onNavigateToChat) {
          actions.push({
            label: 'Open Chat',
            onClick: () => {
              hideCongratulations();
              onNavigateToChat(chatRoomId);
            },
            variant: 'primary',
            icon: 'chat'
          });
        }
        break;
        
      case 'transaction_complete':
      case 'funds_released':
      case 'refund_processed':
        if (onContinueShopping) {
          actions.push({
            label: 'Continue Shopping',
            onClick: () => {
              hideCongratulations();
              onContinueShopping();
            },
            variant: 'primary',
            icon: 'shopping'
          });
        }
        break;
    }

    // Add view transaction action if txId is provided
    if (txId && onViewTransaction) {
      actions.push({
        label: 'View Transaction',
        onClick: () => {
          hideCongratulations();
          onViewTransaction(txId);
        },
        variant: 'secondary',
        icon: 'view'
      });
    }

    // Add share action
    if (onShareSuccess) {
      actions.push({
        label: 'Share',
        onClick: () => onShareSuccess(type, txId),
        variant: 'outline',
        icon: 'share'
      });
    }

    // Add any additional custom actions
    if (additionalActions) {
      actions.push(...additionalActions);
    }

    // Always add a close action if no other primary action exists
    if (!actions.some(action => action.variant === 'primary' || !action.variant)) {
      actions.push({
        label: 'Continue',
        onClick: hideCongratulations,
        variant: 'primary'
      });
    }

    return actions;
  }, [hideCongratulations, onNavigateToChat, onViewTransaction, onShareSuccess, onContinueShopping]);

  // Escrow-specific success handler
  const showEscrowSuccess = useCallback((
    operation: EscrowOperationType,
    txId: string,
    amount?: string,
    additionalData?: any
  ) => {
    const typeMapping: Record<EscrowOperationType, CongratulationType> = {
      buy: 'purchase_success',
      sell: 'funds_released',
      refund: 'refund_processed',
      dispute: 'dispute_resolved',
      accept: 'general_success'
    };

    const type = typeMapping[operation];
    const actions = createActions(type, txId, additionalData?.chatRoomId);

    const metrics: SuccessMetric[] = [];
    if (amount) {
      metrics.push({
        label: 'Amount',
        value: amount,
        icon: '💰'
      });
    }

    if (additionalData?.escrowId) {
      metrics.push({
        label: 'Escrow ID',
        value: additionalData.escrowId,
        icon: '🔒'
      });
    }

    showCongratulations({
      type,
      transactionId: txId,
      amount,
      actions,
      metrics,
      subtitle: additionalData?.subtitle
    });
  }, [createActions, showCongratulations]);

  // Quick success methods
  const showPurchaseSuccess = useCallback((
    txId: string, 
    amount: string, 
    chatRoomId?: string
  ) => {
    const actions = createActions('purchase_success', txId, chatRoomId);
    
    showCongratulations({
      type: 'purchase_success',
      title: 'Purchase Successful! 🎉',
      message: 'Your escrow transaction has been created and your purchase is secure.',
      subtitle: chatRoomId ? 'You can now communicate with the seller in the chat.' : undefined,
      transactionId: txId,
      amount,
      actions,
      metrics: [
        {
          label: 'Amount Paid',
          value: amount,
          icon: '💰'
        },
        {
          label: 'Status',
          value: 'Secured in Escrow',
          icon: '🔒'
        }
      ]
    });
  }, [createActions, showCongratulations]);

  const showTransactionComplete = useCallback((txId: string, amount?: string) => {
    const actions = createActions('transaction_complete', txId);
    
    showCongratulations({
      type: 'transaction_complete',
      transactionId: txId,
      amount,
      actions
    });
  }, [createActions, showCongratulations]);

  const showFundsReleased = useCallback((txId: string, amount: string) => {
    const actions = createActions('funds_released', txId);
    
    showCongratulations({
      type: 'funds_released',
      title: 'Payment Released! 💰',
      message: 'The funds have been successfully released to the seller.',
      subtitle: 'The transaction is now complete.',
      transactionId: txId,
      amount,
      actions,
      metrics: [
        {
          label: 'Amount Released',
          value: amount,
          icon: '💸'
        },
        {
          label: 'Status',
          value: 'Transaction Complete',
          icon: '✅'
        }
      ]
    });
  }, [createActions, showCongratulations]);

  const showRefundProcessed = useCallback((txId: string, amount: string) => {
    const actions = createActions('refund_processed', txId);
    
    showCongratulations({
      type: 'refund_processed',
      title: 'Refund Processed! 🔄',
      message: 'Your refund has been processed and the funds have been returned to your wallet.',
      transactionId: txId,
      amount,
      actions,
      metrics: [
        {
          label: 'Refund Amount',
          value: amount,
          icon: '💰'
        },
        {
          label: 'Status',
          value: 'Refund Complete',
          icon: '✅'
        }
      ]
    });
  }, [createActions, showCongratulations]);

  const showDisputeResolved = useCallback((disputeId: string, resolution?: string) => {
    const actions = createActions('dispute_resolved', disputeId);
    
    showCongratulations({
      type: 'dispute_resolved',
      title: 'Dispute Resolved! ⚖️',
      message: 'The dispute has been resolved successfully.',
      subtitle: resolution || 'Thank you for your patience during the resolution process.',
      transactionId: disputeId,
      actions,
      metrics: [
        {
          label: 'Dispute ID',
          value: disputeId,
          icon: 'document'
        },
        {
          label: 'Status',
          value: 'Resolved',
          icon: '✅'
        }
      ]
    });
  }, [createActions, showCongratulations]);

  // Get modal props for the component
  const getModalProps = useCallback((): Omit<CongratulationsModalProps, 'children'> => ({
    isOpen: modalState.isOpen,
    onClose: hideCongratulations,
    type: modalState.type,
    title: modalState.title,
    message: modalState.message,
    subtitle: modalState.subtitle,
    actions: modalState.actions,
    metrics: modalState.metrics,
    transactionId: modalState.transactionId,
    amount: modalState.amount,
    autoDismiss: modalState.autoDismiss,
    showConfetti: modalState.showConfetti
  }), [modalState, hideCongratulations]);

  return {
    modalState,
    isOpen: modalState.isOpen,
    showCongratulations,
    hideCongratulations,
    showEscrowSuccess,
    showPurchaseSuccess,
    showTransactionComplete,
    showFundsReleased,
    showRefundProcessed,
    showDisputeResolved,
    getModalProps
  };
};
