'use client';

import React, { useState } from 'react';
import { useTranslation } from '../../../hooks/useTranslation';

interface LinkBoxProps {
  link: string;
}

const LinkBox: React.FC<LinkBoxProps> = ({ link }) => {
  const { t } = useTranslation();
  const title = t('linkBox.title');
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(link);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="flex flex-col gap-4 items-center justify-center w-full">
      <div className="w-[565px] max-w-full bg-white rounded-2xl border border-gray-200 shadow-sm p-6 md:p-8">
        <div className="text-center text-gray-900 text-xl md:text-2xl font-semibold font-['Inter'] leading-relaxed mb-6">
          {title}
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl border border-gray-200">
          <div className="flex-1 text-gray-700 text-sm md:text-base font-medium font-['Inter'] truncate">
            {link}
          </div>

          <button
            className={`px-4 py-2 text-sm font-semibold rounded-lg transition-colors duration-200 ${
              copied
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : 'bg-[#FF6600] hover:bg-[#E55A00] text-white'
            }`}
            onClick={handleCopy}
          >
            {copied ? t('linkBox.copied') : t('linkBox.copy')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkBox;
