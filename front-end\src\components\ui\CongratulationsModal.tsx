"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  Star,
  Gift,
  Trophy,
  Sparkles,
  ArrowRight,
  Share2,
  Eye,
  X,
  DollarSign,
  Shield,
  Clock,
  MessageCircle,
  ShoppingBag,
  FileText,
  Search,
  Scale
} from 'lucide-react';

// Congratulations Types
export type CongratulationType = 
  | 'purchase_success'
  | 'transaction_complete'
  | 'escrow_created'
  | 'funds_released'
  | 'refund_processed'
  | 'dispute_resolved'
  | 'general_success';

// Action Button Configuration
export interface ActionButton {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: React.ReactNode | string;
}

// Success Metrics
export interface SuccessMetric {
  label: string;
  value: string;
  icon?: React.ReactNode;
}

// Modal Props
export interface CongratulationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: CongratulationType;
  title?: string;
  message?: string;
  subtitle?: string;
  actions?: ActionButton[];
  metrics?: SuccessMetric[];
  autoDismiss?: boolean;
  autoDismissDelay?: number;
  showConfetti?: boolean;
  transactionId?: string;
  amount?: string;
  className?: string;
}

// Type-specific configurations with brand color #FF6600
const getTypeConfig = (type: CongratulationType) => {
  const configs = {
    purchase_success: {
      icon: <Gift className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Purchase Successful!',
      defaultMessage: 'Your escrow transaction has been created successfully.',
      particles: '🎉'
    },
    transaction_complete: {
      icon: <CheckCircle className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Transaction Complete!',
      defaultMessage: 'Your transaction has been processed successfully.',
      particles: '✨'
    },
    escrow_created: {
      icon: <Shield className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Escrow Created!',
      defaultMessage: 'Your funds are now safely secured in escrow.',
      particles: '🛡️'
    },
    funds_released: {
      icon: <DollarSign className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Funds Released!',
      defaultMessage: 'Payment has been successfully released to the seller.',
      particles: '💰'
    },
    refund_processed: {
      icon: <Clock className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Refund Processed!',
      defaultMessage: 'Your refund has been processed and funds returned.',
      particles: '🔄'
    },
    dispute_resolved: {
      icon: <Trophy className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Dispute Resolved!',
      defaultMessage: 'The dispute has been resolved successfully.',
      particles: '⚖️'
    },
    general_success: {
      icon: <Star className="w-8 h-8" />,
      color: 'text-slate-700',
      bgColor: 'bg-orange-50',
      accentColor: 'bg-[#FF6600]',
      borderColor: 'border-orange-200',
      defaultTitle: 'Success!',
      defaultMessage: 'Operation completed successfully.',
      particles: '🌟'
    }
  };

  return configs[type];
};

// Helper function to render icons
const renderIcon = (icon: React.ReactNode | string) => {
  if (typeof icon === 'string') {
    const iconMap = {
      chat: <MessageCircle className="w-4 h-4" />,
      shopping: <ShoppingBag className="w-4 h-4" />,
      view: <Eye className="w-4 h-4" />,
      share: <Share2 className="w-4 h-4" />,
      document: <FileText className="w-4 h-4" />,
      search: <Search className="w-4 h-4" />,
      scale: <Scale className="w-4 h-4" />
    };

    // Check if it's a mapped icon
    const mappedIcon = iconMap[icon as keyof typeof iconMap];
    if (mappedIcon) {
      return mappedIcon;
    }

    // If it's an emoji or other string, render it directly
    return <span>{icon}</span>;
  }
  return icon;
};

// Enhanced Confetti Particle Component
const ConfettiParticle: React.FC<{ delay: number; particle: string; index: number }> = ({ delay, particle, index }) => {
  const isEven = index % 2 === 0;
  const randomX = Math.random() * 600 - 300;
  const randomRotation = Math.random() * 720 + 360;

  return (
    <motion.div
      initial={{
        opacity: 0,
        y: -50,
        x: 0,
        rotate: 0,
        scale: 0
      }}
      animate={{
        opacity: [0, 1, 1, 1, 0],
        y: [0, -100, 50, 150, 300],
        x: [0, randomX * 0.3, randomX * 0.6, randomX, randomX * 1.2],
        rotate: [0, randomRotation * 0.5, randomRotation],
        scale: [0, 1.2, 1, 0.8, 0]
      }}
      transition={{
        duration: 4,
        delay,
        ease: "easeOut",
        times: [0, 0.2, 0.4, 0.7, 1]
      }}
      className="absolute text-2xl pointer-events-none z-10"
      style={{
        left: '50%',
        top: '10%',
        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
      }}
    >
      {particle}
    </motion.div>
  );
};

// Floating Sparkle Component
const FloatingSparkle: React.FC<{ delay: number; size: 'sm' | 'md' | 'lg' }> = ({ delay, size }) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  const randomX = Math.random() * 400 - 200;
  const randomY = Math.random() * 300 - 150;

  return (
    <motion.div
      initial={{
        opacity: 0,
        scale: 0,
        x: 0,
        y: 0
      }}
      animate={{
        opacity: [0, 1, 1, 0],
        scale: [0, 1, 1.5, 0],
        x: randomX,
        y: randomY,
        rotate: [0, 180, 360]
      }}
      transition={{
        duration: 3,
        delay,
        ease: "easeOut"
      }}
      className={`absolute ${sizeClasses[size]} bg-yellow-400 rounded-full pointer-events-none`}
      style={{
        left: '50%',
        top: '50%',
        boxShadow: '0 0 10px rgba(255, 215, 0, 0.6)'
      }}
    />
  );
};

// Success Ripple Effect
const SuccessRipple: React.FC = () => {
  return (
    <div className="absolute inset-0 pointer-events-none">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          initial={{ scale: 0, opacity: 0.8 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{
            duration: 2,
            delay: index * 0.3,
            ease: "easeOut"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 border-2 border-green-400 rounded-full"
        />
      ))}
    </div>
  );
};

// Main Congratulations Modal Component
export const CongratulationsModal: React.FC<CongratulationsModalProps> = ({
  isOpen,
  onClose,
  type,
  title,
  message,
  subtitle,
  actions = [],
  metrics = [],
  autoDismiss = false,
  autoDismissDelay = 5000,
  showConfetti = true,
  transactionId,
  amount,
  className = ''
}) => {
  const [timeLeft, setTimeLeft] = useState(autoDismissDelay / 1000);
  const config = getTypeConfig(type);

  // Auto-dismiss timer
  useEffect(() => {
    if (!isOpen || !autoDismiss) return;

    const timer = setTimeout(() => {
      onClose();
    }, autoDismissDelay);

    const countdown = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(countdown);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(countdown);
    };
  }, [isOpen, autoDismiss, autoDismissDelay, onClose]);

  // Reset timer when modal opens
  useEffect(() => {
    if (isOpen) {
      setTimeLeft(autoDismissDelay / 1000);
    }
  }, [isOpen, autoDismissDelay]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-slate-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          className={`bg-white rounded-2xl shadow-2xl border border-slate-200 max-w-md w-full relative overflow-hidden ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Enhanced Confetti and Effects */}
          {showConfetti && (
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {/* Success Ripple Effect */}
              <SuccessRipple />

              {/* Confetti Particles */}
              {Array.from({ length: 25 }).map((_, index) => (
                <ConfettiParticle
                  key={`confetti-${index}`}
                  index={index}
                  delay={index * 0.08}
                  particle={config.particles}
                />
              ))}

              {/* Floating Sparkles */}
              {Array.from({ length: 15 }).map((_, index) => (
                <FloatingSparkle
                  key={`sparkle-${index}`}
                  delay={0.5 + index * 0.1}
                  size={index % 3 === 0 ? 'lg' : index % 2 === 0 ? 'md' : 'sm'}
                />
              ))}
            </div>
          )}

          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-slate-400 hover:text-slate-600 transition-colors z-10 p-1 rounded-lg hover:bg-slate-100"
          >
            <X className="w-5 h-5" />
          </button>

          {/* Auto-dismiss Timer */}
          {autoDismiss && timeLeft > 0 && (
            <motion.div
              initial={{ width: '100%' }}
              animate={{ width: '0%' }}
              transition={{ duration: autoDismissDelay / 1000, ease: 'linear' }}
              className={`absolute top-0 left-0 h-1 ${config.accentColor}`}
            />
          )}

          {/* Header Section */}
          <div className="text-center pt-8 pb-6 px-6">
            {/* Animated Icon */}
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ 
                type: 'spring', 
                stiffness: 200, 
                delay: 0.2,
                duration: 0.8
              }}
              className={`inline-flex items-center justify-center w-20 h-20 ${config.bgColor} rounded-full mb-6`}
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  repeatType: 'reverse'
                }}
                className={config.color}
              >
                {config.icon}
              </motion.div>
            </motion.div>

            {/* Title */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-2xl font-bold text-slate-900 mb-2"
            >
              {title || config.defaultTitle}
            </motion.h2>

            {/* Message */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-slate-600 mb-2 leading-relaxed"
            >
              {message || config.defaultMessage}
            </motion.p>

            {/* Subtitle */}
            {subtitle && (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-sm text-slate-500"
              >
                {subtitle}
              </motion.p>
            )}
          </div>

          {/* Metrics Section */}
          {(metrics.length > 0 || amount || transactionId) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="px-6 pb-6"
            >
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                {amount && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 text-sm">Amount</span>
                    <span className="font-semibold text-gray-900">{amount}</span>
                  </div>
                )}
                
                {transactionId && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 text-sm">Transaction ID</span>
                    <span className="font-mono text-xs text-gray-700">
                      {transactionId.length > 16 
                        ? `${transactionId.slice(0, 8)}...${transactionId.slice(-8)}`
                        : transactionId
                      }
                    </span>
                  </div>
                )}

                {metrics.map((metric, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-2">
                      {metric.icon && <span className="text-slate-500">{metric.icon}</span>}
                      <span className="text-slate-600 text-sm">{metric.label}</span>
                    </div>
                    <span className="font-semibold text-slate-900">{metric.value}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Actions Section */}
          {actions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="px-6 pb-6"
            >
              <div className="space-y-3">
                {actions.map((action, index) => (
                  <motion.button
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    onClick={action.onClick}
                    className={`w-full flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:ring-2 focus:ring-offset-2 ${
                      action.variant === 'primary' || !action.variant
                        ? `bg-[#FF6600] text-white hover:bg-[#E55A00] focus:ring-[#FF6600] focus:ring-opacity-50`
                        : action.variant === 'secondary'
                        ? 'bg-slate-100 text-slate-700 hover:bg-slate-200 focus:ring-slate-500'
                        : 'border border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {action.icon && <span>{renderIcon(action.icon)}</span>}
                    <span>{action.label}</span>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Auto-dismiss indicator */}
          {autoDismiss && timeLeft > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="text-center pb-4 px-6"
            >
              <p className="text-xs text-gray-500">
                Auto-closing in {timeLeft} seconds
              </p>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
