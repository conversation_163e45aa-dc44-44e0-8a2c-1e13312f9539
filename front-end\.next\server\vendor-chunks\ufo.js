"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ufo";
exports.ids = ["vendor-chunks/ufo"];
exports.modules = {

/***/ "(ssr)/./node_modules/ufo/dist/index.mjs":
/*!*****************************************!*\
  !*** ./node_modules/ufo/dist/index.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $URL: () => (/* binding */ $URL),\n/* harmony export */   cleanDoubleSlashes: () => (/* binding */ cleanDoubleSlashes),\n/* harmony export */   createURL: () => (/* binding */ createURL),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodePath: () => (/* binding */ decodePath),\n/* harmony export */   decodeQueryKey: () => (/* binding */ decodeQueryKey),\n/* harmony export */   decodeQueryValue: () => (/* binding */ decodeQueryValue),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeHash: () => (/* binding */ encodeHash),\n/* harmony export */   encodeHost: () => (/* binding */ encodeHost),\n/* harmony export */   encodeParam: () => (/* binding */ encodeParam),\n/* harmony export */   encodePath: () => (/* binding */ encodePath),\n/* harmony export */   encodeQueryItem: () => (/* binding */ encodeQueryItem),\n/* harmony export */   encodeQueryKey: () => (/* binding */ encodeQueryKey),\n/* harmony export */   encodeQueryValue: () => (/* binding */ encodeQueryValue),\n/* harmony export */   filterQuery: () => (/* binding */ filterQuery),\n/* harmony export */   getQuery: () => (/* binding */ getQuery),\n/* harmony export */   hasLeadingSlash: () => (/* binding */ hasLeadingSlash),\n/* harmony export */   hasProtocol: () => (/* binding */ hasProtocol),\n/* harmony export */   hasTrailingSlash: () => (/* binding */ hasTrailingSlash),\n/* harmony export */   isEmptyURL: () => (/* binding */ isEmptyURL),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   isNonEmptyURL: () => (/* binding */ isNonEmptyURL),\n/* harmony export */   isRelative: () => (/* binding */ isRelative),\n/* harmony export */   isSamePath: () => (/* binding */ isSamePath),\n/* harmony export */   isScriptProtocol: () => (/* binding */ isScriptProtocol),\n/* harmony export */   joinRelativeURL: () => (/* binding */ joinRelativeURL),\n/* harmony export */   joinURL: () => (/* binding */ joinURL),\n/* harmony export */   normalizeURL: () => (/* binding */ normalizeURL),\n/* harmony export */   parseAuth: () => (/* binding */ parseAuth),\n/* harmony export */   parseFilename: () => (/* binding */ parseFilename),\n/* harmony export */   parseHost: () => (/* binding */ parseHost),\n/* harmony export */   parsePath: () => (/* binding */ parsePath),\n/* harmony export */   parseQuery: () => (/* binding */ parseQuery),\n/* harmony export */   parseURL: () => (/* binding */ parseURL),\n/* harmony export */   resolveURL: () => (/* binding */ resolveURL),\n/* harmony export */   stringifyParsedURL: () => (/* binding */ stringifyParsedURL),\n/* harmony export */   stringifyQuery: () => (/* binding */ stringifyQuery),\n/* harmony export */   withBase: () => (/* binding */ withBase),\n/* harmony export */   withFragment: () => (/* binding */ withFragment),\n/* harmony export */   withHttp: () => (/* binding */ withHttp),\n/* harmony export */   withHttps: () => (/* binding */ withHttps),\n/* harmony export */   withLeadingSlash: () => (/* binding */ withLeadingSlash),\n/* harmony export */   withProtocol: () => (/* binding */ withProtocol),\n/* harmony export */   withQuery: () => (/* binding */ withQuery),\n/* harmony export */   withTrailingSlash: () => (/* binding */ withTrailingSlash),\n/* harmony export */   withoutBase: () => (/* binding */ withoutBase),\n/* harmony export */   withoutFragment: () => (/* binding */ withoutFragment),\n/* harmony export */   withoutHost: () => (/* binding */ withoutHost),\n/* harmony export */   withoutLeadingSlash: () => (/* binding */ withoutLeadingSlash),\n/* harmony export */   withoutProtocol: () => (/* binding */ withoutProtocol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash)\n/* harmony export */ });\nconst n = /[^\\0-\\x7E]/;\nconst t = /[\\x2E\\u3002\\uFF0E\\uFF61]/g;\nconst o = {\n  overflow: \"Overflow Error\",\n  \"not-basic\": \"Illegal Input\",\n  \"invalid-input\": \"Invalid Input\"\n};\nconst e = Math.floor;\nconst r = String.fromCharCode;\nfunction s(n2) {\n  throw new RangeError(o[n2]);\n}\nconst c = function(n2, t2) {\n  return n2 + 22 + 75 * (n2 < 26) - ((t2 != 0) << 5);\n};\nconst u = function(n2, t2, o2) {\n  let r2 = 0;\n  for (n2 = o2 ? e(n2 / 700) : n2 >> 1, n2 += e(n2 / t2); n2 > 455; r2 += 36) {\n    n2 = e(n2 / 35);\n  }\n  return e(r2 + 36 * n2 / (n2 + 38));\n};\nfunction toASCII(o2) {\n  return function(n2, o3) {\n    const e2 = n2.split(\"@\");\n    let r2 = \"\";\n    e2.length > 1 && (r2 = e2[0] + \"@\", n2 = e2[1]);\n    const s2 = function(n3, t2) {\n      const o4 = [];\n      let e3 = n3.length;\n      for (; e3--; ) {\n        o4[e3] = t2(n3[e3]);\n      }\n      return o4;\n    }((n2 = n2.replace(t, \".\")).split(\".\"), o3).join(\".\");\n    return r2 + s2;\n  }(o2, function(t2) {\n    return n.test(t2) ? \"xn--\" + function(n2) {\n      const t3 = [];\n      const o3 = (n2 = function(n3) {\n        const t4 = [];\n        let o4 = 0;\n        const e2 = n3.length;\n        for (; o4 < e2; ) {\n          const r2 = n3.charCodeAt(o4++);\n          if (r2 >= 55296 && r2 <= 56319 && o4 < e2) {\n            const e3 = n3.charCodeAt(o4++);\n            (64512 & e3) == 56320 ? t4.push(((1023 & r2) << 10) + (1023 & e3) + 65536) : (t4.push(r2), o4--);\n          } else {\n            t4.push(r2);\n          }\n        }\n        return t4;\n      }(n2)).length;\n      let f = 128;\n      let i = 0;\n      let l = 72;\n      for (const o4 of n2) {\n        o4 < 128 && t3.push(r(o4));\n      }\n      const h = t3.length;\n      let p = h;\n      for (h && t3.push(\"-\"); p < o3; ) {\n        let o4 = 2147483647;\n        for (const t4 of n2) {\n          t4 >= f && t4 < o4 && (o4 = t4);\n        }\n        const a = p + 1;\n        o4 - f > e((2147483647 - i) / a) && s(\"overflow\"), i += (o4 - f) * a, f = o4;\n        for (const o5 of n2) {\n          if (o5 < f && ++i > 2147483647 && s(\"overflow\"), o5 == f) {\n            let n3 = i;\n            for (let o6 = 36; ; o6 += 36) {\n              const s2 = o6 <= l ? 1 : o6 >= l + 26 ? 26 : o6 - l;\n              if (n3 < s2) {\n                break;\n              }\n              const u2 = n3 - s2;\n              const f2 = 36 - s2;\n              t3.push(r(c(s2 + u2 % f2, 0))), n3 = e(u2 / f2);\n            }\n            t3.push(r(c(n3, 0))), l = u(i, a, p == h), i = 0, ++p;\n          }\n        }\n        ++i, ++f;\n      }\n      return t3.join(\"\");\n    }(t2) : t2;\n  });\n}\n\nconst HASH_RE = /#/g;\nconst AMPERSAND_RE = /&/g;\nconst SLASH_RE = /\\//g;\nconst EQUAL_RE = /=/g;\nconst IM_RE = /\\?/g;\nconst PLUS_RE = /\\+/g;\nconst ENC_CARET_RE = /%5e/gi;\nconst ENC_BACKTICK_RE = /%60/gi;\nconst ENC_CURLY_OPEN_RE = /%7b/gi;\nconst ENC_PIPE_RE = /%7c/gi;\nconst ENC_CURLY_CLOSE_RE = /%7d/gi;\nconst ENC_SPACE_RE = /%20/gi;\nconst ENC_SLASH_RE = /%2f/gi;\nconst ENC_ENC_SLASH_RE = /%252f/gi;\nfunction encode(text) {\n  return encodeURI(\"\" + text).replace(ENC_PIPE_RE, \"|\");\n}\nfunction encodeHash(text) {\n  return encode(text).replace(ENC_CURLY_OPEN_RE, \"{\").replace(ENC_CURLY_CLOSE_RE, \"}\").replace(ENC_CARET_RE, \"^\");\n}\nfunction encodeQueryValue(input) {\n  return encode(typeof input === \"string\" ? input : JSON.stringify(input)).replace(PLUS_RE, \"%2B\").replace(ENC_SPACE_RE, \"+\").replace(HASH_RE, \"%23\").replace(AMPERSAND_RE, \"%26\").replace(ENC_BACKTICK_RE, \"`\").replace(ENC_CARET_RE, \"^\").replace(SLASH_RE, \"%2F\");\n}\nfunction encodeQueryKey(text) {\n  return encodeQueryValue(text).replace(EQUAL_RE, \"%3D\");\n}\nfunction encodePath(text) {\n  return encode(text).replace(HASH_RE, \"%23\").replace(IM_RE, \"%3F\").replace(ENC_ENC_SLASH_RE, \"%2F\").replace(AMPERSAND_RE, \"%26\").replace(PLUS_RE, \"%2B\");\n}\nfunction encodeParam(text) {\n  return encodePath(text).replace(SLASH_RE, \"%2F\");\n}\nfunction decode(text = \"\") {\n  try {\n    return decodeURIComponent(\"\" + text);\n  } catch {\n    return \"\" + text;\n  }\n}\nfunction decodePath(text) {\n  return decode(text.replace(ENC_SLASH_RE, \"%252F\"));\n}\nfunction decodeQueryKey(text) {\n  return decode(text.replace(PLUS_RE, \" \"));\n}\nfunction decodeQueryValue(text) {\n  return decode(text.replace(PLUS_RE, \" \"));\n}\nfunction encodeHost(name = \"\") {\n  return toASCII(name);\n}\n\nfunction parseQuery(parametersString = \"\") {\n  const object = /* @__PURE__ */ Object.create(null);\n  if (parametersString[0] === \"?\") {\n    parametersString = parametersString.slice(1);\n  }\n  for (const parameter of parametersString.split(\"&\")) {\n    const s = parameter.match(/([^=]+)=?(.*)/) || [];\n    if (s.length < 2) {\n      continue;\n    }\n    const key = decodeQueryKey(s[1]);\n    if (key === \"__proto__\" || key === \"constructor\") {\n      continue;\n    }\n    const value = decodeQueryValue(s[2] || \"\");\n    if (object[key] === void 0) {\n      object[key] = value;\n    } else if (Array.isArray(object[key])) {\n      object[key].push(value);\n    } else {\n      object[key] = [object[key], value];\n    }\n  }\n  return object;\n}\nfunction encodeQueryItem(key, value) {\n  if (typeof value === \"number\" || typeof value === \"boolean\") {\n    value = String(value);\n  }\n  if (!value) {\n    return encodeQueryKey(key);\n  }\n  if (Array.isArray(value)) {\n    return value.map(\n      (_value) => `${encodeQueryKey(key)}=${encodeQueryValue(_value)}`\n    ).join(\"&\");\n  }\n  return `${encodeQueryKey(key)}=${encodeQueryValue(value)}`;\n}\nfunction stringifyQuery(query) {\n  return Object.keys(query).filter((k) => query[k] !== void 0).map((k) => encodeQueryItem(k, query[k])).filter(Boolean).join(\"&\");\n}\n\nconst PROTOCOL_STRICT_REGEX = /^[\\s\\w\\0+.-]{2,}:([/\\\\]{1,2})/;\nconst PROTOCOL_REGEX = /^[\\s\\w\\0+.-]{2,}:([/\\\\]{2})?/;\nconst PROTOCOL_RELATIVE_REGEX = /^([/\\\\]\\s*){2,}[^/\\\\]/;\nconst PROTOCOL_SCRIPT_RE = /^[\\s\\0]*(blob|data|javascript|vbscript):$/i;\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\nfunction isRelative(inputString) {\n  return [\"./\", \"../\"].some((string_) => inputString.startsWith(string_));\n}\nfunction hasProtocol(inputString, opts = {}) {\n  if (typeof opts === \"boolean\") {\n    opts = { acceptRelative: opts };\n  }\n  if (opts.strict) {\n    return PROTOCOL_STRICT_REGEX.test(inputString);\n  }\n  return PROTOCOL_REGEX.test(inputString) || (opts.acceptRelative ? PROTOCOL_RELATIVE_REGEX.test(inputString) : false);\n}\nfunction isScriptProtocol(protocol) {\n  return !!protocol && PROTOCOL_SCRIPT_RE.test(protocol);\n}\nfunction hasTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return input.endsWith(\"/\");\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\nfunction withoutTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || \"/\";\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || \"/\";\n  }\n  let path = input;\n  let fragment = \"\";\n  const fragmentIndex = input.indexOf(\"#\");\n  if (fragmentIndex !== -1) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split(\"?\");\n  const cleanPath = s0.endsWith(\"/\") ? s0.slice(0, -1) : s0;\n  return (cleanPath || \"/\") + (s.length > 0 ? `?${s.join(\"?\")}` : \"\") + fragment;\n}\nfunction withTrailingSlash(input = \"\", respectQueryAndFragment) {\n  if (!respectQueryAndFragment) {\n    return input.endsWith(\"/\") ? input : input + \"/\";\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || \"/\";\n  }\n  let path = input;\n  let fragment = \"\";\n  const fragmentIndex = input.indexOf(\"#\");\n  if (fragmentIndex !== -1) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split(\"?\");\n  return s0 + \"/\" + (s.length > 0 ? `?${s.join(\"?\")}` : \"\") + fragment;\n}\nfunction hasLeadingSlash(input = \"\") {\n  return input.startsWith(\"/\");\n}\nfunction withoutLeadingSlash(input = \"\") {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || \"/\";\n}\nfunction withLeadingSlash(input = \"\") {\n  return hasLeadingSlash(input) ? input : \"/\" + input;\n}\nfunction cleanDoubleSlashes(input = \"\") {\n  return input.split(\"://\").map((string_) => string_.replace(/\\/{2,}/g, \"/\")).join(\"://\");\n}\nfunction withBase(input, base) {\n  if (isEmptyURL(base) || hasProtocol(input)) {\n    return input;\n  }\n  const _base = withoutTrailingSlash(base);\n  if (input.startsWith(_base)) {\n    return input;\n  }\n  return joinURL(_base, input);\n}\nfunction withoutBase(input, base) {\n  if (isEmptyURL(base)) {\n    return input;\n  }\n  const _base = withoutTrailingSlash(base);\n  if (!input.startsWith(_base)) {\n    return input;\n  }\n  const trimmed = input.slice(_base.length);\n  return trimmed[0] === \"/\" ? trimmed : \"/\" + trimmed;\n}\nfunction withQuery(input, query) {\n  const parsed = parseURL(input);\n  const mergedQuery = { ...parseQuery(parsed.search), ...query };\n  parsed.search = stringifyQuery(mergedQuery);\n  return stringifyParsedURL(parsed);\n}\nfunction filterQuery(input, predicate) {\n  if (!input.includes(\"?\")) {\n    return input;\n  }\n  const parsed = parseURL(input);\n  const query = parseQuery(parsed.search);\n  const filteredQuery = Object.fromEntries(\n    Object.entries(query).filter(([key, value]) => predicate(key, value))\n  );\n  parsed.search = stringifyQuery(filteredQuery);\n  return stringifyParsedURL(parsed);\n}\nfunction getQuery(input) {\n  return parseQuery(parseURL(input).search);\n}\nfunction isEmptyURL(url) {\n  return !url || url === \"/\";\n}\nfunction isNonEmptyURL(url) {\n  return url && url !== \"/\";\n}\nfunction joinURL(base, ...input) {\n  let url = base || \"\";\n  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {\n    if (url) {\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, \"\");\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n  return url;\n}\nfunction joinRelativeURL(..._input) {\n  const JOIN_SEGMENT_SPLIT_RE = /\\/(?!\\/)/;\n  const input = _input.filter(Boolean);\n  const segments = [];\n  let segmentsDepth = 0;\n  for (const i of input) {\n    if (!i || i === \"/\") {\n      continue;\n    }\n    for (const [sindex, s] of i.split(JOIN_SEGMENT_SPLIT_RE).entries()) {\n      if (!s || s === \".\") {\n        continue;\n      }\n      if (s === \"..\") {\n        if (segments.length === 1 && hasProtocol(segments[0])) {\n          continue;\n        }\n        segments.pop();\n        segmentsDepth--;\n        continue;\n      }\n      if (sindex === 1 && segments[segments.length - 1]?.endsWith(\":/\")) {\n        segments[segments.length - 1] += \"/\" + s;\n        continue;\n      }\n      segments.push(s);\n      segmentsDepth++;\n    }\n  }\n  let url = segments.join(\"/\");\n  if (segmentsDepth >= 0) {\n    if (input[0]?.startsWith(\"/\") && !url.startsWith(\"/\")) {\n      url = \"/\" + url;\n    } else if (input[0]?.startsWith(\"./\") && !url.startsWith(\"./\")) {\n      url = \"./\" + url;\n    }\n  } else {\n    url = \"../\".repeat(-1 * segmentsDepth) + url;\n  }\n  if (input[input.length - 1]?.endsWith(\"/\") && !url.endsWith(\"/\")) {\n    url += \"/\";\n  }\n  return url;\n}\nfunction withHttp(input) {\n  return withProtocol(input, \"http://\");\n}\nfunction withHttps(input) {\n  return withProtocol(input, \"https://\");\n}\nfunction withoutProtocol(input) {\n  return withProtocol(input, \"\");\n}\nfunction withProtocol(input, protocol) {\n  let match = input.match(PROTOCOL_REGEX);\n  if (!match) {\n    match = input.match(/^\\/{2,}/);\n  }\n  if (!match) {\n    return protocol + input;\n  }\n  return protocol + input.slice(match[0].length);\n}\nfunction normalizeURL(input) {\n  const parsed = parseURL(input);\n  parsed.pathname = encodePath(decodePath(parsed.pathname));\n  parsed.hash = encodeHash(decode(parsed.hash));\n  parsed.host = encodeHost(decode(parsed.host));\n  parsed.search = stringifyQuery(parseQuery(parsed.search));\n  return stringifyParsedURL(parsed);\n}\nfunction resolveURL(base = \"\", ...inputs) {\n  if (typeof base !== \"string\") {\n    throw new TypeError(\n      `URL input should be string received ${typeof base} (${base})`\n    );\n  }\n  const filteredInputs = inputs.filter((input) => isNonEmptyURL(input));\n  if (filteredInputs.length === 0) {\n    return base;\n  }\n  const url = parseURL(base);\n  for (const inputSegment of filteredInputs) {\n    const urlSegment = parseURL(inputSegment);\n    if (urlSegment.pathname) {\n      url.pathname = withTrailingSlash(url.pathname) + withoutLeadingSlash(urlSegment.pathname);\n    }\n    if (urlSegment.hash && urlSegment.hash !== \"#\") {\n      url.hash = urlSegment.hash;\n    }\n    if (urlSegment.search && urlSegment.search !== \"?\") {\n      if (url.search && url.search !== \"?\") {\n        const queryString = stringifyQuery({\n          ...parseQuery(url.search),\n          ...parseQuery(urlSegment.search)\n        });\n        url.search = queryString.length > 0 ? \"?\" + queryString : \"\";\n      } else {\n        url.search = urlSegment.search;\n      }\n    }\n  }\n  return stringifyParsedURL(url);\n}\nfunction isSamePath(p1, p2) {\n  return decode(withoutTrailingSlash(p1)) === decode(withoutTrailingSlash(p2));\n}\nfunction isEqual(a, b, options = {}) {\n  if (!options.trailingSlash) {\n    a = withTrailingSlash(a);\n    b = withTrailingSlash(b);\n  }\n  if (!options.leadingSlash) {\n    a = withLeadingSlash(a);\n    b = withLeadingSlash(b);\n  }\n  if (!options.encoding) {\n    a = decode(a);\n    b = decode(b);\n  }\n  return a === b;\n}\nfunction withFragment(input, hash) {\n  if (!hash || hash === \"#\") {\n    return input;\n  }\n  const parsed = parseURL(input);\n  parsed.hash = hash === \"\" ? \"\" : \"#\" + encodeHash(hash);\n  return stringifyParsedURL(parsed);\n}\nfunction withoutFragment(input) {\n  return stringifyParsedURL({ ...parseURL(input), hash: \"\" });\n}\nfunction withoutHost(input) {\n  const parsed = parseURL(input);\n  return (parsed.pathname || \"/\") + parsed.search + parsed.hash;\n}\n\nconst protocolRelative = Symbol.for(\"ufo:protocolRelative\");\nfunction parseURL(input = \"\", defaultProto) {\n  const _specialProtoMatch = input.match(\n    /^[\\s\\0]*(blob:|data:|javascript:|vbscript:)(.*)/i\n  );\n  if (_specialProtoMatch) {\n    const [, _proto, _pathname = \"\"] = _specialProtoMatch;\n    return {\n      protocol: _proto.toLowerCase(),\n      pathname: _pathname,\n      href: _proto + _pathname,\n      auth: \"\",\n      host: \"\",\n      search: \"\",\n      hash: \"\"\n    };\n  }\n  if (!hasProtocol(input, { acceptRelative: true })) {\n    return defaultProto ? parseURL(defaultProto + input) : parsePath(input);\n  }\n  const [, protocol = \"\", auth, hostAndPath = \"\"] = input.replace(/\\\\/g, \"/\").match(/^[\\s\\0]*([\\w+.-]{2,}:)?\\/\\/([^/@]+@)?(.*)/) || [];\n  let [, host = \"\", path = \"\"] = hostAndPath.match(/([^#/?]*)(.*)?/) || [];\n  if (protocol === \"file:\") {\n    path = path.replace(/\\/(?=[A-Za-z]:)/, \"\");\n  }\n  const { pathname, search, hash } = parsePath(path);\n  return {\n    protocol: protocol.toLowerCase(),\n    auth: auth ? auth.slice(0, Math.max(0, auth.length - 1)) : \"\",\n    host,\n    pathname,\n    search,\n    hash,\n    [protocolRelative]: !protocol\n  };\n}\nfunction parsePath(input = \"\") {\n  const [pathname = \"\", search = \"\", hash = \"\"] = (input.match(/([^#?]*)(\\?[^#]*)?(#.*)?/) || []).splice(1);\n  return {\n    pathname,\n    search,\n    hash\n  };\n}\nfunction parseAuth(input = \"\") {\n  const [username, password] = input.split(\":\");\n  return {\n    username: decode(username),\n    password: decode(password)\n  };\n}\nfunction parseHost(input = \"\") {\n  const [hostname, port] = (input.match(/([^/:]*):?(\\d+)?/) || []).splice(1);\n  return {\n    hostname: decode(hostname),\n    port\n  };\n}\nfunction stringifyParsedURL(parsed) {\n  const pathname = parsed.pathname || \"\";\n  const search = parsed.search ? (parsed.search.startsWith(\"?\") ? \"\" : \"?\") + parsed.search : \"\";\n  const hash = parsed.hash || \"\";\n  const auth = parsed.auth ? parsed.auth + \"@\" : \"\";\n  const host = parsed.host || \"\";\n  const proto = parsed.protocol || parsed[protocolRelative] ? (parsed.protocol || \"\") + \"//\" : \"\";\n  return proto + auth + host + pathname + search + hash;\n}\nconst FILENAME_STRICT_REGEX = /\\/([^/]+\\.[^/]+)$/;\nconst FILENAME_REGEX = /\\/([^/]+)$/;\nfunction parseFilename(input = \"\", opts) {\n  const { pathname } = parseURL(input);\n  const matches = opts?.strict ? pathname.match(FILENAME_STRICT_REGEX) : pathname.match(FILENAME_REGEX);\n  return matches ? matches[1] : void 0;\n}\n\nclass $URL {\n  protocol;\n  host;\n  auth;\n  pathname;\n  query = {};\n  hash;\n  constructor(input = \"\") {\n    if (typeof input !== \"string\") {\n      throw new TypeError(\n        `URL input should be string received ${typeof input} (${input})`\n      );\n    }\n    const parsed = parseURL(input);\n    this.protocol = decode(parsed.protocol);\n    this.host = decode(parsed.host);\n    this.auth = decode(parsed.auth);\n    this.pathname = decodePath(parsed.pathname);\n    this.query = parseQuery(parsed.search);\n    this.hash = decode(parsed.hash);\n  }\n  get hostname() {\n    return parseHost(this.host).hostname;\n  }\n  get port() {\n    return parseHost(this.host).port || \"\";\n  }\n  get username() {\n    return parseAuth(this.auth).username;\n  }\n  get password() {\n    return parseAuth(this.auth).password || \"\";\n  }\n  get hasProtocol() {\n    return this.protocol.length;\n  }\n  get isAbsolute() {\n    return this.hasProtocol || this.pathname[0] === \"/\";\n  }\n  get search() {\n    const q = stringifyQuery(this.query);\n    return q.length > 0 ? \"?\" + q : \"\";\n  }\n  get searchParams() {\n    const p = new URLSearchParams();\n    for (const name in this.query) {\n      const value = this.query[name];\n      if (Array.isArray(value)) {\n        for (const v of value) {\n          p.append(name, v);\n        }\n      } else {\n        p.append(\n          name,\n          typeof value === \"string\" ? value : JSON.stringify(value)\n        );\n      }\n    }\n    return p;\n  }\n  get origin() {\n    return (this.protocol ? this.protocol + \"//\" : \"\") + encodeHost(this.host);\n  }\n  get fullpath() {\n    return encodePath(this.pathname) + this.search + encodeHash(this.hash);\n  }\n  get encodedAuth() {\n    if (!this.auth) {\n      return \"\";\n    }\n    const { username, password } = parseAuth(this.auth);\n    return encodeURIComponent(username) + (password ? \":\" + encodeURIComponent(password) : \"\");\n  }\n  get href() {\n    const auth = this.encodedAuth;\n    const originWithAuth = (this.protocol ? this.protocol + \"//\" : \"\") + (auth ? auth + \"@\" : \"\") + encodeHost(this.host);\n    return this.hasProtocol && this.isAbsolute ? originWithAuth + this.fullpath : this.fullpath;\n  }\n  append(url) {\n    if (url.hasProtocol) {\n      throw new Error(\"Cannot append a URL with protocol\");\n    }\n    Object.assign(this.query, url.query);\n    if (url.pathname) {\n      this.pathname = withTrailingSlash(this.pathname) + withoutLeadingSlash(url.pathname);\n    }\n    if (url.hash) {\n      this.hash = url.hash;\n    }\n  }\n  toJSON() {\n    return this.href;\n  }\n  toString() {\n    return this.href;\n  }\n}\nfunction createURL(input) {\n  return new $URL(input);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWZvL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELFVBQVU7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxNQUFNO0FBQ25CO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFNBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixRQUFRO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsaUNBQWlDO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsb0JBQW9CLEdBQUcseUJBQXlCO0FBQ3JFO0FBQ0E7QUFDQSxZQUFZLG9CQUFvQixHQUFHLHdCQUF3QjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0Q0FBNEMsR0FBRyxRQUFRLElBQUk7QUFDM0QscUNBQXFDLEdBQUcsUUFBUSxFQUFFO0FBQ2xELDZDQUE2QyxHQUFHO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQztBQUMzQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxZQUFZO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLFlBQVk7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxHQUFHO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLEdBQUc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxhQUFhLEdBQUcsS0FBSztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLDhCQUE4QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsc0JBQXNCO0FBQ2xEO0FBQ0E7QUFDQSxzR0FBc0csR0FBRztBQUN6RztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUseUJBQXlCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLFdBQVc7QUFDckI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLGNBQWMsR0FBRyxNQUFNO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVkscUJBQXFCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcdWZvXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbiA9IC9bXlxcMC1cXHg3RV0vO1xuY29uc3QgdCA9IC9bXFx4MkVcXHUzMDAyXFx1RkYwRVxcdUZGNjFdL2c7XG5jb25zdCBvID0ge1xuICBvdmVyZmxvdzogXCJPdmVyZmxvdyBFcnJvclwiLFxuICBcIm5vdC1iYXNpY1wiOiBcIklsbGVnYWwgSW5wdXRcIixcbiAgXCJpbnZhbGlkLWlucHV0XCI6IFwiSW52YWxpZCBJbnB1dFwiXG59O1xuY29uc3QgZSA9IE1hdGguZmxvb3I7XG5jb25zdCByID0gU3RyaW5nLmZyb21DaGFyQ29kZTtcbmZ1bmN0aW9uIHMobjIpIHtcbiAgdGhyb3cgbmV3IFJhbmdlRXJyb3Iob1tuMl0pO1xufVxuY29uc3QgYyA9IGZ1bmN0aW9uKG4yLCB0Mikge1xuICByZXR1cm4gbjIgKyAyMiArIDc1ICogKG4yIDwgMjYpIC0gKCh0MiAhPSAwKSA8PCA1KTtcbn07XG5jb25zdCB1ID0gZnVuY3Rpb24objIsIHQyLCBvMikge1xuICBsZXQgcjIgPSAwO1xuICBmb3IgKG4yID0gbzIgPyBlKG4yIC8gNzAwKSA6IG4yID4+IDEsIG4yICs9IGUobjIgLyB0Mik7IG4yID4gNDU1OyByMiArPSAzNikge1xuICAgIG4yID0gZShuMiAvIDM1KTtcbiAgfVxuICByZXR1cm4gZShyMiArIDM2ICogbjIgLyAobjIgKyAzOCkpO1xufTtcbmZ1bmN0aW9uIHRvQVNDSUkobzIpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKG4yLCBvMykge1xuICAgIGNvbnN0IGUyID0gbjIuc3BsaXQoXCJAXCIpO1xuICAgIGxldCByMiA9IFwiXCI7XG4gICAgZTIubGVuZ3RoID4gMSAmJiAocjIgPSBlMlswXSArIFwiQFwiLCBuMiA9IGUyWzFdKTtcbiAgICBjb25zdCBzMiA9IGZ1bmN0aW9uKG4zLCB0Mikge1xuICAgICAgY29uc3QgbzQgPSBbXTtcbiAgICAgIGxldCBlMyA9IG4zLmxlbmd0aDtcbiAgICAgIGZvciAoOyBlMy0tOyApIHtcbiAgICAgICAgbzRbZTNdID0gdDIobjNbZTNdKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBvNDtcbiAgICB9KChuMiA9IG4yLnJlcGxhY2UodCwgXCIuXCIpKS5zcGxpdChcIi5cIiksIG8zKS5qb2luKFwiLlwiKTtcbiAgICByZXR1cm4gcjIgKyBzMjtcbiAgfShvMiwgZnVuY3Rpb24odDIpIHtcbiAgICByZXR1cm4gbi50ZXN0KHQyKSA/IFwieG4tLVwiICsgZnVuY3Rpb24objIpIHtcbiAgICAgIGNvbnN0IHQzID0gW107XG4gICAgICBjb25zdCBvMyA9IChuMiA9IGZ1bmN0aW9uKG4zKSB7XG4gICAgICAgIGNvbnN0IHQ0ID0gW107XG4gICAgICAgIGxldCBvNCA9IDA7XG4gICAgICAgIGNvbnN0IGUyID0gbjMubGVuZ3RoO1xuICAgICAgICBmb3IgKDsgbzQgPCBlMjsgKSB7XG4gICAgICAgICAgY29uc3QgcjIgPSBuMy5jaGFyQ29kZUF0KG80KyspO1xuICAgICAgICAgIGlmIChyMiA+PSA1NTI5NiAmJiByMiA8PSA1NjMxOSAmJiBvNCA8IGUyKSB7XG4gICAgICAgICAgICBjb25zdCBlMyA9IG4zLmNoYXJDb2RlQXQobzQrKyk7XG4gICAgICAgICAgICAoNjQ1MTIgJiBlMykgPT0gNTYzMjAgPyB0NC5wdXNoKCgoMTAyMyAmIHIyKSA8PCAxMCkgKyAoMTAyMyAmIGUzKSArIDY1NTM2KSA6ICh0NC5wdXNoKHIyKSwgbzQtLSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHQ0LnB1c2gocjIpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdDQ7XG4gICAgICB9KG4yKSkubGVuZ3RoO1xuICAgICAgbGV0IGYgPSAxMjg7XG4gICAgICBsZXQgaSA9IDA7XG4gICAgICBsZXQgbCA9IDcyO1xuICAgICAgZm9yIChjb25zdCBvNCBvZiBuMikge1xuICAgICAgICBvNCA8IDEyOCAmJiB0My5wdXNoKHIobzQpKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGggPSB0My5sZW5ndGg7XG4gICAgICBsZXQgcCA9IGg7XG4gICAgICBmb3IgKGggJiYgdDMucHVzaChcIi1cIik7IHAgPCBvMzsgKSB7XG4gICAgICAgIGxldCBvNCA9IDIxNDc0ODM2NDc7XG4gICAgICAgIGZvciAoY29uc3QgdDQgb2YgbjIpIHtcbiAgICAgICAgICB0NCA+PSBmICYmIHQ0IDwgbzQgJiYgKG80ID0gdDQpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGEgPSBwICsgMTtcbiAgICAgICAgbzQgLSBmID4gZSgoMjE0NzQ4MzY0NyAtIGkpIC8gYSkgJiYgcyhcIm92ZXJmbG93XCIpLCBpICs9IChvNCAtIGYpICogYSwgZiA9IG80O1xuICAgICAgICBmb3IgKGNvbnN0IG81IG9mIG4yKSB7XG4gICAgICAgICAgaWYgKG81IDwgZiAmJiArK2kgPiAyMTQ3NDgzNjQ3ICYmIHMoXCJvdmVyZmxvd1wiKSwgbzUgPT0gZikge1xuICAgICAgICAgICAgbGV0IG4zID0gaTtcbiAgICAgICAgICAgIGZvciAobGV0IG82ID0gMzY7IDsgbzYgKz0gMzYpIHtcbiAgICAgICAgICAgICAgY29uc3QgczIgPSBvNiA8PSBsID8gMSA6IG82ID49IGwgKyAyNiA/IDI2IDogbzYgLSBsO1xuICAgICAgICAgICAgICBpZiAobjMgPCBzMikge1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGNvbnN0IHUyID0gbjMgLSBzMjtcbiAgICAgICAgICAgICAgY29uc3QgZjIgPSAzNiAtIHMyO1xuICAgICAgICAgICAgICB0My5wdXNoKHIoYyhzMiArIHUyICUgZjIsIDApKSksIG4zID0gZSh1MiAvIGYyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHQzLnB1c2gocihjKG4zLCAwKSkpLCBsID0gdShpLCBhLCBwID09IGgpLCBpID0gMCwgKytwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICArK2ksICsrZjtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0My5qb2luKFwiXCIpO1xuICAgIH0odDIpIDogdDI7XG4gIH0pO1xufVxuXG5jb25zdCBIQVNIX1JFID0gLyMvZztcbmNvbnN0IEFNUEVSU0FORF9SRSA9IC8mL2c7XG5jb25zdCBTTEFTSF9SRSA9IC9cXC8vZztcbmNvbnN0IEVRVUFMX1JFID0gLz0vZztcbmNvbnN0IElNX1JFID0gL1xcPy9nO1xuY29uc3QgUExVU19SRSA9IC9cXCsvZztcbmNvbnN0IEVOQ19DQVJFVF9SRSA9IC8lNWUvZ2k7XG5jb25zdCBFTkNfQkFDS1RJQ0tfUkUgPSAvJTYwL2dpO1xuY29uc3QgRU5DX0NVUkxZX09QRU5fUkUgPSAvJTdiL2dpO1xuY29uc3QgRU5DX1BJUEVfUkUgPSAvJTdjL2dpO1xuY29uc3QgRU5DX0NVUkxZX0NMT1NFX1JFID0gLyU3ZC9naTtcbmNvbnN0IEVOQ19TUEFDRV9SRSA9IC8lMjAvZ2k7XG5jb25zdCBFTkNfU0xBU0hfUkUgPSAvJTJmL2dpO1xuY29uc3QgRU5DX0VOQ19TTEFTSF9SRSA9IC8lMjUyZi9naTtcbmZ1bmN0aW9uIGVuY29kZSh0ZXh0KSB7XG4gIHJldHVybiBlbmNvZGVVUkkoXCJcIiArIHRleHQpLnJlcGxhY2UoRU5DX1BJUEVfUkUsIFwifFwiKTtcbn1cbmZ1bmN0aW9uIGVuY29kZUhhc2godGV4dCkge1xuICByZXR1cm4gZW5jb2RlKHRleHQpLnJlcGxhY2UoRU5DX0NVUkxZX09QRU5fUkUsIFwie1wiKS5yZXBsYWNlKEVOQ19DVVJMWV9DTE9TRV9SRSwgXCJ9XCIpLnJlcGxhY2UoRU5DX0NBUkVUX1JFLCBcIl5cIik7XG59XG5mdW5jdGlvbiBlbmNvZGVRdWVyeVZhbHVlKGlucHV0KSB7XG4gIHJldHVybiBlbmNvZGUodHlwZW9mIGlucHV0ID09PSBcInN0cmluZ1wiID8gaW5wdXQgOiBKU09OLnN0cmluZ2lmeShpbnB1dCkpLnJlcGxhY2UoUExVU19SRSwgXCIlMkJcIikucmVwbGFjZShFTkNfU1BBQ0VfUkUsIFwiK1wiKS5yZXBsYWNlKEhBU0hfUkUsIFwiJTIzXCIpLnJlcGxhY2UoQU1QRVJTQU5EX1JFLCBcIiUyNlwiKS5yZXBsYWNlKEVOQ19CQUNLVElDS19SRSwgXCJgXCIpLnJlcGxhY2UoRU5DX0NBUkVUX1JFLCBcIl5cIikucmVwbGFjZShTTEFTSF9SRSwgXCIlMkZcIik7XG59XG5mdW5jdGlvbiBlbmNvZGVRdWVyeUtleSh0ZXh0KSB7XG4gIHJldHVybiBlbmNvZGVRdWVyeVZhbHVlKHRleHQpLnJlcGxhY2UoRVFVQUxfUkUsIFwiJTNEXCIpO1xufVxuZnVuY3Rpb24gZW5jb2RlUGF0aCh0ZXh0KSB7XG4gIHJldHVybiBlbmNvZGUodGV4dCkucmVwbGFjZShIQVNIX1JFLCBcIiUyM1wiKS5yZXBsYWNlKElNX1JFLCBcIiUzRlwiKS5yZXBsYWNlKEVOQ19FTkNfU0xBU0hfUkUsIFwiJTJGXCIpLnJlcGxhY2UoQU1QRVJTQU5EX1JFLCBcIiUyNlwiKS5yZXBsYWNlKFBMVVNfUkUsIFwiJTJCXCIpO1xufVxuZnVuY3Rpb24gZW5jb2RlUGFyYW0odGV4dCkge1xuICByZXR1cm4gZW5jb2RlUGF0aCh0ZXh0KS5yZXBsYWNlKFNMQVNIX1JFLCBcIiUyRlwiKTtcbn1cbmZ1bmN0aW9uIGRlY29kZSh0ZXh0ID0gXCJcIikge1xuICB0cnkge1xuICAgIHJldHVybiBkZWNvZGVVUklDb21wb25lbnQoXCJcIiArIHRleHQpO1xuICB9IGNhdGNoIHtcbiAgICByZXR1cm4gXCJcIiArIHRleHQ7XG4gIH1cbn1cbmZ1bmN0aW9uIGRlY29kZVBhdGgodGV4dCkge1xuICByZXR1cm4gZGVjb2RlKHRleHQucmVwbGFjZShFTkNfU0xBU0hfUkUsIFwiJTI1MkZcIikpO1xufVxuZnVuY3Rpb24gZGVjb2RlUXVlcnlLZXkodGV4dCkge1xuICByZXR1cm4gZGVjb2RlKHRleHQucmVwbGFjZShQTFVTX1JFLCBcIiBcIikpO1xufVxuZnVuY3Rpb24gZGVjb2RlUXVlcnlWYWx1ZSh0ZXh0KSB7XG4gIHJldHVybiBkZWNvZGUodGV4dC5yZXBsYWNlKFBMVVNfUkUsIFwiIFwiKSk7XG59XG5mdW5jdGlvbiBlbmNvZGVIb3N0KG5hbWUgPSBcIlwiKSB7XG4gIHJldHVybiB0b0FTQ0lJKG5hbWUpO1xufVxuXG5mdW5jdGlvbiBwYXJzZVF1ZXJ5KHBhcmFtZXRlcnNTdHJpbmcgPSBcIlwiKSB7XG4gIGNvbnN0IG9iamVjdCA9IC8qIEBfX1BVUkVfXyAqLyBPYmplY3QuY3JlYXRlKG51bGwpO1xuICBpZiAocGFyYW1ldGVyc1N0cmluZ1swXSA9PT0gXCI/XCIpIHtcbiAgICBwYXJhbWV0ZXJzU3RyaW5nID0gcGFyYW1ldGVyc1N0cmluZy5zbGljZSgxKTtcbiAgfVxuICBmb3IgKGNvbnN0IHBhcmFtZXRlciBvZiBwYXJhbWV0ZXJzU3RyaW5nLnNwbGl0KFwiJlwiKSkge1xuICAgIGNvbnN0IHMgPSBwYXJhbWV0ZXIubWF0Y2goLyhbXj1dKyk9PyguKikvKSB8fCBbXTtcbiAgICBpZiAocy5sZW5ndGggPCAyKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgY29uc3Qga2V5ID0gZGVjb2RlUXVlcnlLZXkoc1sxXSk7XG4gICAgaWYgKGtleSA9PT0gXCJfX3Byb3RvX19cIiB8fCBrZXkgPT09IFwiY29uc3RydWN0b3JcIikge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IHZhbHVlID0gZGVjb2RlUXVlcnlWYWx1ZShzWzJdIHx8IFwiXCIpO1xuICAgIGlmIChvYmplY3Rba2V5XSA9PT0gdm9pZCAwKSB7XG4gICAgICBvYmplY3Rba2V5XSA9IHZhbHVlO1xuICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShvYmplY3Rba2V5XSkpIHtcbiAgICAgIG9iamVjdFtrZXldLnB1c2godmFsdWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICBvYmplY3Rba2V5XSA9IFtvYmplY3Rba2V5XSwgdmFsdWVdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqZWN0O1xufVxuZnVuY3Rpb24gZW5jb2RlUXVlcnlJdGVtKGtleSwgdmFsdWUpIHtcbiAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJudW1iZXJcIiB8fCB0eXBlb2YgdmFsdWUgPT09IFwiYm9vbGVhblwiKSB7XG4gICAgdmFsdWUgPSBTdHJpbmcodmFsdWUpO1xuICB9XG4gIGlmICghdmFsdWUpIHtcbiAgICByZXR1cm4gZW5jb2RlUXVlcnlLZXkoa2V5KTtcbiAgfVxuICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICByZXR1cm4gdmFsdWUubWFwKFxuICAgICAgKF92YWx1ZSkgPT4gYCR7ZW5jb2RlUXVlcnlLZXkoa2V5KX09JHtlbmNvZGVRdWVyeVZhbHVlKF92YWx1ZSl9YFxuICAgICkuam9pbihcIiZcIik7XG4gIH1cbiAgcmV0dXJuIGAke2VuY29kZVF1ZXJ5S2V5KGtleSl9PSR7ZW5jb2RlUXVlcnlWYWx1ZSh2YWx1ZSl9YDtcbn1cbmZ1bmN0aW9uIHN0cmluZ2lmeVF1ZXJ5KHF1ZXJ5KSB7XG4gIHJldHVybiBPYmplY3Qua2V5cyhxdWVyeSkuZmlsdGVyKChrKSA9PiBxdWVyeVtrXSAhPT0gdm9pZCAwKS5tYXAoKGspID0+IGVuY29kZVF1ZXJ5SXRlbShrLCBxdWVyeVtrXSkpLmZpbHRlcihCb29sZWFuKS5qb2luKFwiJlwiKTtcbn1cblxuY29uc3QgUFJPVE9DT0xfU1RSSUNUX1JFR0VYID0gL15bXFxzXFx3XFwwKy4tXXsyLH06KFsvXFxcXF17MSwyfSkvO1xuY29uc3QgUFJPVE9DT0xfUkVHRVggPSAvXltcXHNcXHdcXDArLi1dezIsfTooWy9cXFxcXXsyfSk/LztcbmNvbnN0IFBST1RPQ09MX1JFTEFUSVZFX1JFR0VYID0gL14oWy9cXFxcXVxccyopezIsfVteL1xcXFxdLztcbmNvbnN0IFBST1RPQ09MX1NDUklQVF9SRSA9IC9eW1xcc1xcMF0qKGJsb2J8ZGF0YXxqYXZhc2NyaXB0fHZic2NyaXB0KTokL2k7XG5jb25zdCBUUkFJTElOR19TTEFTSF9SRSA9IC9cXC8kfFxcL1xcP3xcXC8jLztcbmNvbnN0IEpPSU5fTEVBRElOR19TTEFTSF9SRSA9IC9eXFwuP1xcLy87XG5mdW5jdGlvbiBpc1JlbGF0aXZlKGlucHV0U3RyaW5nKSB7XG4gIHJldHVybiBbXCIuL1wiLCBcIi4uL1wiXS5zb21lKChzdHJpbmdfKSA9PiBpbnB1dFN0cmluZy5zdGFydHNXaXRoKHN0cmluZ18pKTtcbn1cbmZ1bmN0aW9uIGhhc1Byb3RvY29sKGlucHV0U3RyaW5nLCBvcHRzID0ge30pIHtcbiAgaWYgKHR5cGVvZiBvcHRzID09PSBcImJvb2xlYW5cIikge1xuICAgIG9wdHMgPSB7IGFjY2VwdFJlbGF0aXZlOiBvcHRzIH07XG4gIH1cbiAgaWYgKG9wdHMuc3RyaWN0KSB7XG4gICAgcmV0dXJuIFBST1RPQ09MX1NUUklDVF9SRUdFWC50ZXN0KGlucHV0U3RyaW5nKTtcbiAgfVxuICByZXR1cm4gUFJPVE9DT0xfUkVHRVgudGVzdChpbnB1dFN0cmluZykgfHwgKG9wdHMuYWNjZXB0UmVsYXRpdmUgPyBQUk9UT0NPTF9SRUxBVElWRV9SRUdFWC50ZXN0KGlucHV0U3RyaW5nKSA6IGZhbHNlKTtcbn1cbmZ1bmN0aW9uIGlzU2NyaXB0UHJvdG9jb2wocHJvdG9jb2wpIHtcbiAgcmV0dXJuICEhcHJvdG9jb2wgJiYgUFJPVE9DT0xfU0NSSVBUX1JFLnRlc3QocHJvdG9jb2wpO1xufVxuZnVuY3Rpb24gaGFzVHJhaWxpbmdTbGFzaChpbnB1dCA9IFwiXCIsIHJlc3BlY3RRdWVyeUFuZEZyYWdtZW50KSB7XG4gIGlmICghcmVzcGVjdFF1ZXJ5QW5kRnJhZ21lbnQpIHtcbiAgICByZXR1cm4gaW5wdXQuZW5kc1dpdGgoXCIvXCIpO1xuICB9XG4gIHJldHVybiBUUkFJTElOR19TTEFTSF9SRS50ZXN0KGlucHV0KTtcbn1cbmZ1bmN0aW9uIHdpdGhvdXRUcmFpbGluZ1NsYXNoKGlucHV0ID0gXCJcIiwgcmVzcGVjdFF1ZXJ5QW5kRnJhZ21lbnQpIHtcbiAgaWYgKCFyZXNwZWN0UXVlcnlBbmRGcmFnbWVudCkge1xuICAgIHJldHVybiAoaGFzVHJhaWxpbmdTbGFzaChpbnB1dCkgPyBpbnB1dC5zbGljZSgwLCAtMSkgOiBpbnB1dCkgfHwgXCIvXCI7XG4gIH1cbiAgaWYgKCFoYXNUcmFpbGluZ1NsYXNoKGlucHV0LCB0cnVlKSkge1xuICAgIHJldHVybiBpbnB1dCB8fCBcIi9cIjtcbiAgfVxuICBsZXQgcGF0aCA9IGlucHV0O1xuICBsZXQgZnJhZ21lbnQgPSBcIlwiO1xuICBjb25zdCBmcmFnbWVudEluZGV4ID0gaW5wdXQuaW5kZXhPZihcIiNcIik7XG4gIGlmIChmcmFnbWVudEluZGV4ICE9PSAtMSkge1xuICAgIHBhdGggPSBpbnB1dC5zbGljZSgwLCBmcmFnbWVudEluZGV4KTtcbiAgICBmcmFnbWVudCA9IGlucHV0LnNsaWNlKGZyYWdtZW50SW5kZXgpO1xuICB9XG4gIGNvbnN0IFtzMCwgLi4uc10gPSBwYXRoLnNwbGl0KFwiP1wiKTtcbiAgY29uc3QgY2xlYW5QYXRoID0gczAuZW5kc1dpdGgoXCIvXCIpID8gczAuc2xpY2UoMCwgLTEpIDogczA7XG4gIHJldHVybiAoY2xlYW5QYXRoIHx8IFwiL1wiKSArIChzLmxlbmd0aCA+IDAgPyBgPyR7cy5qb2luKFwiP1wiKX1gIDogXCJcIikgKyBmcmFnbWVudDtcbn1cbmZ1bmN0aW9uIHdpdGhUcmFpbGluZ1NsYXNoKGlucHV0ID0gXCJcIiwgcmVzcGVjdFF1ZXJ5QW5kRnJhZ21lbnQpIHtcbiAgaWYgKCFyZXNwZWN0UXVlcnlBbmRGcmFnbWVudCkge1xuICAgIHJldHVybiBpbnB1dC5lbmRzV2l0aChcIi9cIikgPyBpbnB1dCA6IGlucHV0ICsgXCIvXCI7XG4gIH1cbiAgaWYgKGhhc1RyYWlsaW5nU2xhc2goaW5wdXQsIHRydWUpKSB7XG4gICAgcmV0dXJuIGlucHV0IHx8IFwiL1wiO1xuICB9XG4gIGxldCBwYXRoID0gaW5wdXQ7XG4gIGxldCBmcmFnbWVudCA9IFwiXCI7XG4gIGNvbnN0IGZyYWdtZW50SW5kZXggPSBpbnB1dC5pbmRleE9mKFwiI1wiKTtcbiAgaWYgKGZyYWdtZW50SW5kZXggIT09IC0xKSB7XG4gICAgcGF0aCA9IGlucHV0LnNsaWNlKDAsIGZyYWdtZW50SW5kZXgpO1xuICAgIGZyYWdtZW50ID0gaW5wdXQuc2xpY2UoZnJhZ21lbnRJbmRleCk7XG4gICAgaWYgKCFwYXRoKSB7XG4gICAgICByZXR1cm4gZnJhZ21lbnQ7XG4gICAgfVxuICB9XG4gIGNvbnN0IFtzMCwgLi4uc10gPSBwYXRoLnNwbGl0KFwiP1wiKTtcbiAgcmV0dXJuIHMwICsgXCIvXCIgKyAocy5sZW5ndGggPiAwID8gYD8ke3Muam9pbihcIj9cIil9YCA6IFwiXCIpICsgZnJhZ21lbnQ7XG59XG5mdW5jdGlvbiBoYXNMZWFkaW5nU2xhc2goaW5wdXQgPSBcIlwiKSB7XG4gIHJldHVybiBpbnB1dC5zdGFydHNXaXRoKFwiL1wiKTtcbn1cbmZ1bmN0aW9uIHdpdGhvdXRMZWFkaW5nU2xhc2goaW5wdXQgPSBcIlwiKSB7XG4gIHJldHVybiAoaGFzTGVhZGluZ1NsYXNoKGlucHV0KSA/IGlucHV0LnNsaWNlKDEpIDogaW5wdXQpIHx8IFwiL1wiO1xufVxuZnVuY3Rpb24gd2l0aExlYWRpbmdTbGFzaChpbnB1dCA9IFwiXCIpIHtcbiAgcmV0dXJuIGhhc0xlYWRpbmdTbGFzaChpbnB1dCkgPyBpbnB1dCA6IFwiL1wiICsgaW5wdXQ7XG59XG5mdW5jdGlvbiBjbGVhbkRvdWJsZVNsYXNoZXMoaW5wdXQgPSBcIlwiKSB7XG4gIHJldHVybiBpbnB1dC5zcGxpdChcIjovL1wiKS5tYXAoKHN0cmluZ18pID0+IHN0cmluZ18ucmVwbGFjZSgvXFwvezIsfS9nLCBcIi9cIikpLmpvaW4oXCI6Ly9cIik7XG59XG5mdW5jdGlvbiB3aXRoQmFzZShpbnB1dCwgYmFzZSkge1xuICBpZiAoaXNFbXB0eVVSTChiYXNlKSB8fCBoYXNQcm90b2NvbChpbnB1dCkpIHtcbiAgICByZXR1cm4gaW5wdXQ7XG4gIH1cbiAgY29uc3QgX2Jhc2UgPSB3aXRob3V0VHJhaWxpbmdTbGFzaChiYXNlKTtcbiAgaWYgKGlucHV0LnN0YXJ0c1dpdGgoX2Jhc2UpKSB7XG4gICAgcmV0dXJuIGlucHV0O1xuICB9XG4gIHJldHVybiBqb2luVVJMKF9iYXNlLCBpbnB1dCk7XG59XG5mdW5jdGlvbiB3aXRob3V0QmFzZShpbnB1dCwgYmFzZSkge1xuICBpZiAoaXNFbXB0eVVSTChiYXNlKSkge1xuICAgIHJldHVybiBpbnB1dDtcbiAgfVxuICBjb25zdCBfYmFzZSA9IHdpdGhvdXRUcmFpbGluZ1NsYXNoKGJhc2UpO1xuICBpZiAoIWlucHV0LnN0YXJ0c1dpdGgoX2Jhc2UpKSB7XG4gICAgcmV0dXJuIGlucHV0O1xuICB9XG4gIGNvbnN0IHRyaW1tZWQgPSBpbnB1dC5zbGljZShfYmFzZS5sZW5ndGgpO1xuICByZXR1cm4gdHJpbW1lZFswXSA9PT0gXCIvXCIgPyB0cmltbWVkIDogXCIvXCIgKyB0cmltbWVkO1xufVxuZnVuY3Rpb24gd2l0aFF1ZXJ5KGlucHV0LCBxdWVyeSkge1xuICBjb25zdCBwYXJzZWQgPSBwYXJzZVVSTChpbnB1dCk7XG4gIGNvbnN0IG1lcmdlZFF1ZXJ5ID0geyAuLi5wYXJzZVF1ZXJ5KHBhcnNlZC5zZWFyY2gpLCAuLi5xdWVyeSB9O1xuICBwYXJzZWQuc2VhcmNoID0gc3RyaW5naWZ5UXVlcnkobWVyZ2VkUXVlcnkpO1xuICByZXR1cm4gc3RyaW5naWZ5UGFyc2VkVVJMKHBhcnNlZCk7XG59XG5mdW5jdGlvbiBmaWx0ZXJRdWVyeShpbnB1dCwgcHJlZGljYXRlKSB7XG4gIGlmICghaW5wdXQuaW5jbHVkZXMoXCI/XCIpKSB7XG4gICAgcmV0dXJuIGlucHV0O1xuICB9XG4gIGNvbnN0IHBhcnNlZCA9IHBhcnNlVVJMKGlucHV0KTtcbiAgY29uc3QgcXVlcnkgPSBwYXJzZVF1ZXJ5KHBhcnNlZC5zZWFyY2gpO1xuICBjb25zdCBmaWx0ZXJlZFF1ZXJ5ID0gT2JqZWN0LmZyb21FbnRyaWVzKFxuICAgIE9iamVjdC5lbnRyaWVzKHF1ZXJ5KS5maWx0ZXIoKFtrZXksIHZhbHVlXSkgPT4gcHJlZGljYXRlKGtleSwgdmFsdWUpKVxuICApO1xuICBwYXJzZWQuc2VhcmNoID0gc3RyaW5naWZ5UXVlcnkoZmlsdGVyZWRRdWVyeSk7XG4gIHJldHVybiBzdHJpbmdpZnlQYXJzZWRVUkwocGFyc2VkKTtcbn1cbmZ1bmN0aW9uIGdldFF1ZXJ5KGlucHV0KSB7XG4gIHJldHVybiBwYXJzZVF1ZXJ5KHBhcnNlVVJMKGlucHV0KS5zZWFyY2gpO1xufVxuZnVuY3Rpb24gaXNFbXB0eVVSTCh1cmwpIHtcbiAgcmV0dXJuICF1cmwgfHwgdXJsID09PSBcIi9cIjtcbn1cbmZ1bmN0aW9uIGlzTm9uRW1wdHlVUkwodXJsKSB7XG4gIHJldHVybiB1cmwgJiYgdXJsICE9PSBcIi9cIjtcbn1cbmZ1bmN0aW9uIGpvaW5VUkwoYmFzZSwgLi4uaW5wdXQpIHtcbiAgbGV0IHVybCA9IGJhc2UgfHwgXCJcIjtcbiAgZm9yIChjb25zdCBzZWdtZW50IG9mIGlucHV0LmZpbHRlcigodXJsMikgPT4gaXNOb25FbXB0eVVSTCh1cmwyKSkpIHtcbiAgICBpZiAodXJsKSB7XG4gICAgICBjb25zdCBfc2VnbWVudCA9IHNlZ21lbnQucmVwbGFjZShKT0lOX0xFQURJTkdfU0xBU0hfUkUsIFwiXCIpO1xuICAgICAgdXJsID0gd2l0aFRyYWlsaW5nU2xhc2godXJsKSArIF9zZWdtZW50O1xuICAgIH0gZWxzZSB7XG4gICAgICB1cmwgPSBzZWdtZW50O1xuICAgIH1cbiAgfVxuICByZXR1cm4gdXJsO1xufVxuZnVuY3Rpb24gam9pblJlbGF0aXZlVVJMKC4uLl9pbnB1dCkge1xuICBjb25zdCBKT0lOX1NFR01FTlRfU1BMSVRfUkUgPSAvXFwvKD8hXFwvKS87XG4gIGNvbnN0IGlucHV0ID0gX2lucHV0LmZpbHRlcihCb29sZWFuKTtcbiAgY29uc3Qgc2VnbWVudHMgPSBbXTtcbiAgbGV0IHNlZ21lbnRzRGVwdGggPSAwO1xuICBmb3IgKGNvbnN0IGkgb2YgaW5wdXQpIHtcbiAgICBpZiAoIWkgfHwgaSA9PT0gXCIvXCIpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IFtzaW5kZXgsIHNdIG9mIGkuc3BsaXQoSk9JTl9TRUdNRU5UX1NQTElUX1JFKS5lbnRyaWVzKCkpIHtcbiAgICAgIGlmICghcyB8fCBzID09PSBcIi5cIikge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmIChzID09PSBcIi4uXCIpIHtcbiAgICAgICAgaWYgKHNlZ21lbnRzLmxlbmd0aCA9PT0gMSAmJiBoYXNQcm90b2NvbChzZWdtZW50c1swXSkpIHtcbiAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBzZWdtZW50cy5wb3AoKTtcbiAgICAgICAgc2VnbWVudHNEZXB0aC0tO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmIChzaW5kZXggPT09IDEgJiYgc2VnbWVudHNbc2VnbWVudHMubGVuZ3RoIC0gMV0/LmVuZHNXaXRoKFwiOi9cIikpIHtcbiAgICAgICAgc2VnbWVudHNbc2VnbWVudHMubGVuZ3RoIC0gMV0gKz0gXCIvXCIgKyBzO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIHNlZ21lbnRzLnB1c2gocyk7XG4gICAgICBzZWdtZW50c0RlcHRoKys7XG4gICAgfVxuICB9XG4gIGxldCB1cmwgPSBzZWdtZW50cy5qb2luKFwiL1wiKTtcbiAgaWYgKHNlZ21lbnRzRGVwdGggPj0gMCkge1xuICAgIGlmIChpbnB1dFswXT8uc3RhcnRzV2l0aChcIi9cIikgJiYgIXVybC5zdGFydHNXaXRoKFwiL1wiKSkge1xuICAgICAgdXJsID0gXCIvXCIgKyB1cmw7XG4gICAgfSBlbHNlIGlmIChpbnB1dFswXT8uc3RhcnRzV2l0aChcIi4vXCIpICYmICF1cmwuc3RhcnRzV2l0aChcIi4vXCIpKSB7XG4gICAgICB1cmwgPSBcIi4vXCIgKyB1cmw7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHVybCA9IFwiLi4vXCIucmVwZWF0KC0xICogc2VnbWVudHNEZXB0aCkgKyB1cmw7XG4gIH1cbiAgaWYgKGlucHV0W2lucHV0Lmxlbmd0aCAtIDFdPy5lbmRzV2l0aChcIi9cIikgJiYgIXVybC5lbmRzV2l0aChcIi9cIikpIHtcbiAgICB1cmwgKz0gXCIvXCI7XG4gIH1cbiAgcmV0dXJuIHVybDtcbn1cbmZ1bmN0aW9uIHdpdGhIdHRwKGlucHV0KSB7XG4gIHJldHVybiB3aXRoUHJvdG9jb2woaW5wdXQsIFwiaHR0cDovL1wiKTtcbn1cbmZ1bmN0aW9uIHdpdGhIdHRwcyhpbnB1dCkge1xuICByZXR1cm4gd2l0aFByb3RvY29sKGlucHV0LCBcImh0dHBzOi8vXCIpO1xufVxuZnVuY3Rpb24gd2l0aG91dFByb3RvY29sKGlucHV0KSB7XG4gIHJldHVybiB3aXRoUHJvdG9jb2woaW5wdXQsIFwiXCIpO1xufVxuZnVuY3Rpb24gd2l0aFByb3RvY29sKGlucHV0LCBwcm90b2NvbCkge1xuICBsZXQgbWF0Y2ggPSBpbnB1dC5tYXRjaChQUk9UT0NPTF9SRUdFWCk7XG4gIGlmICghbWF0Y2gpIHtcbiAgICBtYXRjaCA9IGlucHV0Lm1hdGNoKC9eXFwvezIsfS8pO1xuICB9XG4gIGlmICghbWF0Y2gpIHtcbiAgICByZXR1cm4gcHJvdG9jb2wgKyBpbnB1dDtcbiAgfVxuICByZXR1cm4gcHJvdG9jb2wgKyBpbnB1dC5zbGljZShtYXRjaFswXS5sZW5ndGgpO1xufVxuZnVuY3Rpb24gbm9ybWFsaXplVVJMKGlucHV0KSB7XG4gIGNvbnN0IHBhcnNlZCA9IHBhcnNlVVJMKGlucHV0KTtcbiAgcGFyc2VkLnBhdGhuYW1lID0gZW5jb2RlUGF0aChkZWNvZGVQYXRoKHBhcnNlZC5wYXRobmFtZSkpO1xuICBwYXJzZWQuaGFzaCA9IGVuY29kZUhhc2goZGVjb2RlKHBhcnNlZC5oYXNoKSk7XG4gIHBhcnNlZC5ob3N0ID0gZW5jb2RlSG9zdChkZWNvZGUocGFyc2VkLmhvc3QpKTtcbiAgcGFyc2VkLnNlYXJjaCA9IHN0cmluZ2lmeVF1ZXJ5KHBhcnNlUXVlcnkocGFyc2VkLnNlYXJjaCkpO1xuICByZXR1cm4gc3RyaW5naWZ5UGFyc2VkVVJMKHBhcnNlZCk7XG59XG5mdW5jdGlvbiByZXNvbHZlVVJMKGJhc2UgPSBcIlwiLCAuLi5pbnB1dHMpIHtcbiAgaWYgKHR5cGVvZiBiYXNlICE9PSBcInN0cmluZ1wiKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIGBVUkwgaW5wdXQgc2hvdWxkIGJlIHN0cmluZyByZWNlaXZlZCAke3R5cGVvZiBiYXNlfSAoJHtiYXNlfSlgXG4gICAgKTtcbiAgfVxuICBjb25zdCBmaWx0ZXJlZElucHV0cyA9IGlucHV0cy5maWx0ZXIoKGlucHV0KSA9PiBpc05vbkVtcHR5VVJMKGlucHV0KSk7XG4gIGlmIChmaWx0ZXJlZElucHV0cy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gYmFzZTtcbiAgfVxuICBjb25zdCB1cmwgPSBwYXJzZVVSTChiYXNlKTtcbiAgZm9yIChjb25zdCBpbnB1dFNlZ21lbnQgb2YgZmlsdGVyZWRJbnB1dHMpIHtcbiAgICBjb25zdCB1cmxTZWdtZW50ID0gcGFyc2VVUkwoaW5wdXRTZWdtZW50KTtcbiAgICBpZiAodXJsU2VnbWVudC5wYXRobmFtZSkge1xuICAgICAgdXJsLnBhdGhuYW1lID0gd2l0aFRyYWlsaW5nU2xhc2godXJsLnBhdGhuYW1lKSArIHdpdGhvdXRMZWFkaW5nU2xhc2godXJsU2VnbWVudC5wYXRobmFtZSk7XG4gICAgfVxuICAgIGlmICh1cmxTZWdtZW50Lmhhc2ggJiYgdXJsU2VnbWVudC5oYXNoICE9PSBcIiNcIikge1xuICAgICAgdXJsLmhhc2ggPSB1cmxTZWdtZW50Lmhhc2g7XG4gICAgfVxuICAgIGlmICh1cmxTZWdtZW50LnNlYXJjaCAmJiB1cmxTZWdtZW50LnNlYXJjaCAhPT0gXCI/XCIpIHtcbiAgICAgIGlmICh1cmwuc2VhcmNoICYmIHVybC5zZWFyY2ggIT09IFwiP1wiKSB7XG4gICAgICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gc3RyaW5naWZ5UXVlcnkoe1xuICAgICAgICAgIC4uLnBhcnNlUXVlcnkodXJsLnNlYXJjaCksXG4gICAgICAgICAgLi4ucGFyc2VRdWVyeSh1cmxTZWdtZW50LnNlYXJjaClcbiAgICAgICAgfSk7XG4gICAgICAgIHVybC5zZWFyY2ggPSBxdWVyeVN0cmluZy5sZW5ndGggPiAwID8gXCI/XCIgKyBxdWVyeVN0cmluZyA6IFwiXCI7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB1cmwuc2VhcmNoID0gdXJsU2VnbWVudC5zZWFyY2g7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBzdHJpbmdpZnlQYXJzZWRVUkwodXJsKTtcbn1cbmZ1bmN0aW9uIGlzU2FtZVBhdGgocDEsIHAyKSB7XG4gIHJldHVybiBkZWNvZGUod2l0aG91dFRyYWlsaW5nU2xhc2gocDEpKSA9PT0gZGVjb2RlKHdpdGhvdXRUcmFpbGluZ1NsYXNoKHAyKSk7XG59XG5mdW5jdGlvbiBpc0VxdWFsKGEsIGIsIG9wdGlvbnMgPSB7fSkge1xuICBpZiAoIW9wdGlvbnMudHJhaWxpbmdTbGFzaCkge1xuICAgIGEgPSB3aXRoVHJhaWxpbmdTbGFzaChhKTtcbiAgICBiID0gd2l0aFRyYWlsaW5nU2xhc2goYik7XG4gIH1cbiAgaWYgKCFvcHRpb25zLmxlYWRpbmdTbGFzaCkge1xuICAgIGEgPSB3aXRoTGVhZGluZ1NsYXNoKGEpO1xuICAgIGIgPSB3aXRoTGVhZGluZ1NsYXNoKGIpO1xuICB9XG4gIGlmICghb3B0aW9ucy5lbmNvZGluZykge1xuICAgIGEgPSBkZWNvZGUoYSk7XG4gICAgYiA9IGRlY29kZShiKTtcbiAgfVxuICByZXR1cm4gYSA9PT0gYjtcbn1cbmZ1bmN0aW9uIHdpdGhGcmFnbWVudChpbnB1dCwgaGFzaCkge1xuICBpZiAoIWhhc2ggfHwgaGFzaCA9PT0gXCIjXCIpIHtcbiAgICByZXR1cm4gaW5wdXQ7XG4gIH1cbiAgY29uc3QgcGFyc2VkID0gcGFyc2VVUkwoaW5wdXQpO1xuICBwYXJzZWQuaGFzaCA9IGhhc2ggPT09IFwiXCIgPyBcIlwiIDogXCIjXCIgKyBlbmNvZGVIYXNoKGhhc2gpO1xuICByZXR1cm4gc3RyaW5naWZ5UGFyc2VkVVJMKHBhcnNlZCk7XG59XG5mdW5jdGlvbiB3aXRob3V0RnJhZ21lbnQoaW5wdXQpIHtcbiAgcmV0dXJuIHN0cmluZ2lmeVBhcnNlZFVSTCh7IC4uLnBhcnNlVVJMKGlucHV0KSwgaGFzaDogXCJcIiB9KTtcbn1cbmZ1bmN0aW9uIHdpdGhvdXRIb3N0KGlucHV0KSB7XG4gIGNvbnN0IHBhcnNlZCA9IHBhcnNlVVJMKGlucHV0KTtcbiAgcmV0dXJuIChwYXJzZWQucGF0aG5hbWUgfHwgXCIvXCIpICsgcGFyc2VkLnNlYXJjaCArIHBhcnNlZC5oYXNoO1xufVxuXG5jb25zdCBwcm90b2NvbFJlbGF0aXZlID0gU3ltYm9sLmZvcihcInVmbzpwcm90b2NvbFJlbGF0aXZlXCIpO1xuZnVuY3Rpb24gcGFyc2VVUkwoaW5wdXQgPSBcIlwiLCBkZWZhdWx0UHJvdG8pIHtcbiAgY29uc3QgX3NwZWNpYWxQcm90b01hdGNoID0gaW5wdXQubWF0Y2goXG4gICAgL15bXFxzXFwwXSooYmxvYjp8ZGF0YTp8amF2YXNjcmlwdDp8dmJzY3JpcHQ6KSguKikvaVxuICApO1xuICBpZiAoX3NwZWNpYWxQcm90b01hdGNoKSB7XG4gICAgY29uc3QgWywgX3Byb3RvLCBfcGF0aG5hbWUgPSBcIlwiXSA9IF9zcGVjaWFsUHJvdG9NYXRjaDtcbiAgICByZXR1cm4ge1xuICAgICAgcHJvdG9jb2w6IF9wcm90by50b0xvd2VyQ2FzZSgpLFxuICAgICAgcGF0aG5hbWU6IF9wYXRobmFtZSxcbiAgICAgIGhyZWY6IF9wcm90byArIF9wYXRobmFtZSxcbiAgICAgIGF1dGg6IFwiXCIsXG4gICAgICBob3N0OiBcIlwiLFxuICAgICAgc2VhcmNoOiBcIlwiLFxuICAgICAgaGFzaDogXCJcIlxuICAgIH07XG4gIH1cbiAgaWYgKCFoYXNQcm90b2NvbChpbnB1dCwgeyBhY2NlcHRSZWxhdGl2ZTogdHJ1ZSB9KSkge1xuICAgIHJldHVybiBkZWZhdWx0UHJvdG8gPyBwYXJzZVVSTChkZWZhdWx0UHJvdG8gKyBpbnB1dCkgOiBwYXJzZVBhdGgoaW5wdXQpO1xuICB9XG4gIGNvbnN0IFssIHByb3RvY29sID0gXCJcIiwgYXV0aCwgaG9zdEFuZFBhdGggPSBcIlwiXSA9IGlucHV0LnJlcGxhY2UoL1xcXFwvZywgXCIvXCIpLm1hdGNoKC9eW1xcc1xcMF0qKFtcXHcrLi1dezIsfTopP1xcL1xcLyhbXi9AXStAKT8oLiopLykgfHwgW107XG4gIGxldCBbLCBob3N0ID0gXCJcIiwgcGF0aCA9IFwiXCJdID0gaG9zdEFuZFBhdGgubWF0Y2goLyhbXiMvP10qKSguKik/LykgfHwgW107XG4gIGlmIChwcm90b2NvbCA9PT0gXCJmaWxlOlwiKSB7XG4gICAgcGF0aCA9IHBhdGgucmVwbGFjZSgvXFwvKD89W0EtWmEtel06KS8sIFwiXCIpO1xuICB9XG4gIGNvbnN0IHsgcGF0aG5hbWUsIHNlYXJjaCwgaGFzaCB9ID0gcGFyc2VQYXRoKHBhdGgpO1xuICByZXR1cm4ge1xuICAgIHByb3RvY29sOiBwcm90b2NvbC50b0xvd2VyQ2FzZSgpLFxuICAgIGF1dGg6IGF1dGggPyBhdXRoLnNsaWNlKDAsIE1hdGgubWF4KDAsIGF1dGgubGVuZ3RoIC0gMSkpIDogXCJcIixcbiAgICBob3N0LFxuICAgIHBhdGhuYW1lLFxuICAgIHNlYXJjaCxcbiAgICBoYXNoLFxuICAgIFtwcm90b2NvbFJlbGF0aXZlXTogIXByb3RvY29sXG4gIH07XG59XG5mdW5jdGlvbiBwYXJzZVBhdGgoaW5wdXQgPSBcIlwiKSB7XG4gIGNvbnN0IFtwYXRobmFtZSA9IFwiXCIsIHNlYXJjaCA9IFwiXCIsIGhhc2ggPSBcIlwiXSA9IChpbnB1dC5tYXRjaCgvKFteIz9dKikoXFw/W14jXSopPygjLiopPy8pIHx8IFtdKS5zcGxpY2UoMSk7XG4gIHJldHVybiB7XG4gICAgcGF0aG5hbWUsXG4gICAgc2VhcmNoLFxuICAgIGhhc2hcbiAgfTtcbn1cbmZ1bmN0aW9uIHBhcnNlQXV0aChpbnB1dCA9IFwiXCIpIHtcbiAgY29uc3QgW3VzZXJuYW1lLCBwYXNzd29yZF0gPSBpbnB1dC5zcGxpdChcIjpcIik7XG4gIHJldHVybiB7XG4gICAgdXNlcm5hbWU6IGRlY29kZSh1c2VybmFtZSksXG4gICAgcGFzc3dvcmQ6IGRlY29kZShwYXNzd29yZClcbiAgfTtcbn1cbmZ1bmN0aW9uIHBhcnNlSG9zdChpbnB1dCA9IFwiXCIpIHtcbiAgY29uc3QgW2hvc3RuYW1lLCBwb3J0XSA9IChpbnB1dC5tYXRjaCgvKFteLzpdKik6PyhcXGQrKT8vKSB8fCBbXSkuc3BsaWNlKDEpO1xuICByZXR1cm4ge1xuICAgIGhvc3RuYW1lOiBkZWNvZGUoaG9zdG5hbWUpLFxuICAgIHBvcnRcbiAgfTtcbn1cbmZ1bmN0aW9uIHN0cmluZ2lmeVBhcnNlZFVSTChwYXJzZWQpIHtcbiAgY29uc3QgcGF0aG5hbWUgPSBwYXJzZWQucGF0aG5hbWUgfHwgXCJcIjtcbiAgY29uc3Qgc2VhcmNoID0gcGFyc2VkLnNlYXJjaCA/IChwYXJzZWQuc2VhcmNoLnN0YXJ0c1dpdGgoXCI/XCIpID8gXCJcIiA6IFwiP1wiKSArIHBhcnNlZC5zZWFyY2ggOiBcIlwiO1xuICBjb25zdCBoYXNoID0gcGFyc2VkLmhhc2ggfHwgXCJcIjtcbiAgY29uc3QgYXV0aCA9IHBhcnNlZC5hdXRoID8gcGFyc2VkLmF1dGggKyBcIkBcIiA6IFwiXCI7XG4gIGNvbnN0IGhvc3QgPSBwYXJzZWQuaG9zdCB8fCBcIlwiO1xuICBjb25zdCBwcm90byA9IHBhcnNlZC5wcm90b2NvbCB8fCBwYXJzZWRbcHJvdG9jb2xSZWxhdGl2ZV0gPyAocGFyc2VkLnByb3RvY29sIHx8IFwiXCIpICsgXCIvL1wiIDogXCJcIjtcbiAgcmV0dXJuIHByb3RvICsgYXV0aCArIGhvc3QgKyBwYXRobmFtZSArIHNlYXJjaCArIGhhc2g7XG59XG5jb25zdCBGSUxFTkFNRV9TVFJJQ1RfUkVHRVggPSAvXFwvKFteL10rXFwuW14vXSspJC87XG5jb25zdCBGSUxFTkFNRV9SRUdFWCA9IC9cXC8oW14vXSspJC87XG5mdW5jdGlvbiBwYXJzZUZpbGVuYW1lKGlucHV0ID0gXCJcIiwgb3B0cykge1xuICBjb25zdCB7IHBhdGhuYW1lIH0gPSBwYXJzZVVSTChpbnB1dCk7XG4gIGNvbnN0IG1hdGNoZXMgPSBvcHRzPy5zdHJpY3QgPyBwYXRobmFtZS5tYXRjaChGSUxFTkFNRV9TVFJJQ1RfUkVHRVgpIDogcGF0aG5hbWUubWF0Y2goRklMRU5BTUVfUkVHRVgpO1xuICByZXR1cm4gbWF0Y2hlcyA/IG1hdGNoZXNbMV0gOiB2b2lkIDA7XG59XG5cbmNsYXNzICRVUkwge1xuICBwcm90b2NvbDtcbiAgaG9zdDtcbiAgYXV0aDtcbiAgcGF0aG5hbWU7XG4gIHF1ZXJ5ID0ge307XG4gIGhhc2g7XG4gIGNvbnN0cnVjdG9yKGlucHV0ID0gXCJcIikge1xuICAgIGlmICh0eXBlb2YgaW5wdXQgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICAgIGBVUkwgaW5wdXQgc2hvdWxkIGJlIHN0cmluZyByZWNlaXZlZCAke3R5cGVvZiBpbnB1dH0gKCR7aW5wdXR9KWBcbiAgICAgICk7XG4gICAgfVxuICAgIGNvbnN0IHBhcnNlZCA9IHBhcnNlVVJMKGlucHV0KTtcbiAgICB0aGlzLnByb3RvY29sID0gZGVjb2RlKHBhcnNlZC5wcm90b2NvbCk7XG4gICAgdGhpcy5ob3N0ID0gZGVjb2RlKHBhcnNlZC5ob3N0KTtcbiAgICB0aGlzLmF1dGggPSBkZWNvZGUocGFyc2VkLmF1dGgpO1xuICAgIHRoaXMucGF0aG5hbWUgPSBkZWNvZGVQYXRoKHBhcnNlZC5wYXRobmFtZSk7XG4gICAgdGhpcy5xdWVyeSA9IHBhcnNlUXVlcnkocGFyc2VkLnNlYXJjaCk7XG4gICAgdGhpcy5oYXNoID0gZGVjb2RlKHBhcnNlZC5oYXNoKTtcbiAgfVxuICBnZXQgaG9zdG5hbWUoKSB7XG4gICAgcmV0dXJuIHBhcnNlSG9zdCh0aGlzLmhvc3QpLmhvc3RuYW1lO1xuICB9XG4gIGdldCBwb3J0KCkge1xuICAgIHJldHVybiBwYXJzZUhvc3QodGhpcy5ob3N0KS5wb3J0IHx8IFwiXCI7XG4gIH1cbiAgZ2V0IHVzZXJuYW1lKCkge1xuICAgIHJldHVybiBwYXJzZUF1dGgodGhpcy5hdXRoKS51c2VybmFtZTtcbiAgfVxuICBnZXQgcGFzc3dvcmQoKSB7XG4gICAgcmV0dXJuIHBhcnNlQXV0aCh0aGlzLmF1dGgpLnBhc3N3b3JkIHx8IFwiXCI7XG4gIH1cbiAgZ2V0IGhhc1Byb3RvY29sKCkge1xuICAgIHJldHVybiB0aGlzLnByb3RvY29sLmxlbmd0aDtcbiAgfVxuICBnZXQgaXNBYnNvbHV0ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5oYXNQcm90b2NvbCB8fCB0aGlzLnBhdGhuYW1lWzBdID09PSBcIi9cIjtcbiAgfVxuICBnZXQgc2VhcmNoKCkge1xuICAgIGNvbnN0IHEgPSBzdHJpbmdpZnlRdWVyeSh0aGlzLnF1ZXJ5KTtcbiAgICByZXR1cm4gcS5sZW5ndGggPiAwID8gXCI/XCIgKyBxIDogXCJcIjtcbiAgfVxuICBnZXQgc2VhcmNoUGFyYW1zKCkge1xuICAgIGNvbnN0IHAgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgZm9yIChjb25zdCBuYW1lIGluIHRoaXMucXVlcnkpIHtcbiAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5xdWVyeVtuYW1lXTtcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICBmb3IgKGNvbnN0IHYgb2YgdmFsdWUpIHtcbiAgICAgICAgICBwLmFwcGVuZChuYW1lLCB2KTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcC5hcHBlbmQoXG4gICAgICAgICAgbmFtZSxcbiAgICAgICAgICB0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIgPyB2YWx1ZSA6IEpTT04uc3RyaW5naWZ5KHZhbHVlKVxuICAgICAgICApO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcDtcbiAgfVxuICBnZXQgb3JpZ2luKCkge1xuICAgIHJldHVybiAodGhpcy5wcm90b2NvbCA/IHRoaXMucHJvdG9jb2wgKyBcIi8vXCIgOiBcIlwiKSArIGVuY29kZUhvc3QodGhpcy5ob3N0KTtcbiAgfVxuICBnZXQgZnVsbHBhdGgoKSB7XG4gICAgcmV0dXJuIGVuY29kZVBhdGgodGhpcy5wYXRobmFtZSkgKyB0aGlzLnNlYXJjaCArIGVuY29kZUhhc2godGhpcy5oYXNoKTtcbiAgfVxuICBnZXQgZW5jb2RlZEF1dGgoKSB7XG4gICAgaWYgKCF0aGlzLmF1dGgpIHtcbiAgICAgIHJldHVybiBcIlwiO1xuICAgIH1cbiAgICBjb25zdCB7IHVzZXJuYW1lLCBwYXNzd29yZCB9ID0gcGFyc2VBdXRoKHRoaXMuYXV0aCk7XG4gICAgcmV0dXJuIGVuY29kZVVSSUNvbXBvbmVudCh1c2VybmFtZSkgKyAocGFzc3dvcmQgPyBcIjpcIiArIGVuY29kZVVSSUNvbXBvbmVudChwYXNzd29yZCkgOiBcIlwiKTtcbiAgfVxuICBnZXQgaHJlZigpIHtcbiAgICBjb25zdCBhdXRoID0gdGhpcy5lbmNvZGVkQXV0aDtcbiAgICBjb25zdCBvcmlnaW5XaXRoQXV0aCA9ICh0aGlzLnByb3RvY29sID8gdGhpcy5wcm90b2NvbCArIFwiLy9cIiA6IFwiXCIpICsgKGF1dGggPyBhdXRoICsgXCJAXCIgOiBcIlwiKSArIGVuY29kZUhvc3QodGhpcy5ob3N0KTtcbiAgICByZXR1cm4gdGhpcy5oYXNQcm90b2NvbCAmJiB0aGlzLmlzQWJzb2x1dGUgPyBvcmlnaW5XaXRoQXV0aCArIHRoaXMuZnVsbHBhdGggOiB0aGlzLmZ1bGxwYXRoO1xuICB9XG4gIGFwcGVuZCh1cmwpIHtcbiAgICBpZiAodXJsLmhhc1Byb3RvY29sKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgYXBwZW5kIGEgVVJMIHdpdGggcHJvdG9jb2xcIik7XG4gICAgfVxuICAgIE9iamVjdC5hc3NpZ24odGhpcy5xdWVyeSwgdXJsLnF1ZXJ5KTtcbiAgICBpZiAodXJsLnBhdGhuYW1lKSB7XG4gICAgICB0aGlzLnBhdGhuYW1lID0gd2l0aFRyYWlsaW5nU2xhc2godGhpcy5wYXRobmFtZSkgKyB3aXRob3V0TGVhZGluZ1NsYXNoKHVybC5wYXRobmFtZSk7XG4gICAgfVxuICAgIGlmICh1cmwuaGFzaCkge1xuICAgICAgdGhpcy5oYXNoID0gdXJsLmhhc2g7XG4gICAgfVxuICB9XG4gIHRvSlNPTigpIHtcbiAgICByZXR1cm4gdGhpcy5ocmVmO1xuICB9XG4gIHRvU3RyaW5nKCkge1xuICAgIHJldHVybiB0aGlzLmhyZWY7XG4gIH1cbn1cbmZ1bmN0aW9uIGNyZWF0ZVVSTChpbnB1dCkge1xuICByZXR1cm4gbmV3ICRVUkwoaW5wdXQpO1xufVxuXG5leHBvcnQgeyAkVVJMLCBjbGVhbkRvdWJsZVNsYXNoZXMsIGNyZWF0ZVVSTCwgZGVjb2RlLCBkZWNvZGVQYXRoLCBkZWNvZGVRdWVyeUtleSwgZGVjb2RlUXVlcnlWYWx1ZSwgZW5jb2RlLCBlbmNvZGVIYXNoLCBlbmNvZGVIb3N0LCBlbmNvZGVQYXJhbSwgZW5jb2RlUGF0aCwgZW5jb2RlUXVlcnlJdGVtLCBlbmNvZGVRdWVyeUtleSwgZW5jb2RlUXVlcnlWYWx1ZSwgZmlsdGVyUXVlcnksIGdldFF1ZXJ5LCBoYXNMZWFkaW5nU2xhc2gsIGhhc1Byb3RvY29sLCBoYXNUcmFpbGluZ1NsYXNoLCBpc0VtcHR5VVJMLCBpc0VxdWFsLCBpc05vbkVtcHR5VVJMLCBpc1JlbGF0aXZlLCBpc1NhbWVQYXRoLCBpc1NjcmlwdFByb3RvY29sLCBqb2luUmVsYXRpdmVVUkwsIGpvaW5VUkwsIG5vcm1hbGl6ZVVSTCwgcGFyc2VBdXRoLCBwYXJzZUZpbGVuYW1lLCBwYXJzZUhvc3QsIHBhcnNlUGF0aCwgcGFyc2VRdWVyeSwgcGFyc2VVUkwsIHJlc29sdmVVUkwsIHN0cmluZ2lmeVBhcnNlZFVSTCwgc3RyaW5naWZ5UXVlcnksIHdpdGhCYXNlLCB3aXRoRnJhZ21lbnQsIHdpdGhIdHRwLCB3aXRoSHR0cHMsIHdpdGhMZWFkaW5nU2xhc2gsIHdpdGhQcm90b2NvbCwgd2l0aFF1ZXJ5LCB3aXRoVHJhaWxpbmdTbGFzaCwgd2l0aG91dEJhc2UsIHdpdGhvdXRGcmFnbWVudCwgd2l0aG91dEhvc3QsIHdpdGhvdXRMZWFkaW5nU2xhc2gsIHdpdGhvdXRQcm90b2NvbCwgd2l0aG91dFRyYWlsaW5nU2xhc2ggfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ufo/dist/index.mjs\n");

/***/ })

};
;