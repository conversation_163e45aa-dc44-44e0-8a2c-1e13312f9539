import Image from 'next/image';
import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';


interface AirdropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onClaim: () => void;
  onConnect: () => void;
  airdropCode: string;
  tokenAmount: number;
  isWalletConnected: boolean;
  isValidCode: boolean;
  isClaiming?: boolean;
  isLoading?: boolean;
}

const AirdropModal: React.FC<AirdropModalProps> = ({
  isOpen,
  onClose,
  onClaim,
  onConnect,
  airdropCode,
  tokenAmount,
  isWalletConnected,
  isValidCode,
  isClaiming = false,
  isLoading = false,
}) => {
    const { t } = useTranslation();

if (!isOpen) return null;

  // Show loading state while validating airdrop code
  if (isLoading) {
    return (
      <div
        className="bg-white rounded-[24px] md:rounded-[48px] shadow-xl py-8 px-4 md:px-13 w-[563px] text-center relative m-4"
      >
        <button
          className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={onClose}
          disabled={isLoading}
        >
          <Image
            src={'/icons/close.svg'}
            alt="Close"
            width={24}
            height={24}
            style={{
              filter:
                'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)',
            }}
          />
        </button>

        <div className="mb-6">
          <div className="w-16 h-16 bg-[#FF6600] rounded-full mx-auto mb-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
          <h2 className="text-3xl font-semibold mb-4">Validating Airdrop</h2>
          <p className="text-lg text-gray-600">
            Please wait while we validate your airdrop code:{' '}
            <span className="font-mono font-medium">{airdropCode}</span>
          </p>
        </div>
      </div>
    );
  }

  if (!isValidCode) {
    return (
      <div
        className="bg-white rounded-[24px] md:rounded-[48px] shadow-xl py-8 px-4 md:px-13 w-[563px] text-center relative m-4"
      >
        <button
          className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none"
          onClick={onClose}
        >
          <Image
            src={'/icons/close.svg'}
            alt="Close"
            width={24}
            height={24}
            style={{
              filter:
                'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)',
            }}
          />
        </button>

        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-semibold mb-4">
            Invalid Airdrop Code
          </h2>
          <p className="text-lg text-gray-600">
            The airdrop code <strong>{airdropCode}</strong> is not valid or
            has already been claimed.
          </p>
        </div>

        <button
          onClick={onClose}
          className="text-lg bg-black text-white font-medium w-full py-3 hover:bg-gray-800 transition rounded-lg"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <div
      className="bg-white rounded-[24px] md:rounded-[48px] shadow-xl py-8 px-4 md:px-13 w-[563px] text-center relative m-4"
    >
      <button
        className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={onClose}
        disabled={isClaiming || isLoading}
      >
        <Image
          src={'/icons/close.svg'}
          alt="Close"
          width={24}
          height={24}
          style={{
            filter:
              'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)',
          }}
        />
      </button>

      <div className="mb-6">
        <div className="w-16 h-16 bg-[#FF6600] rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg
            className="w-8 h-8 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        </div>
        <h2 className="text-3xl font-semibold mb-4">Airdrop Available!</h2>
        <p className="text-lg text-gray-600 mb-6">
          You have{' '}
          <span className="font-bold text-[#FF6600]">
            {tokenAmount.toLocaleString()}
          </span>{' '}
          tokens available to claim
        </p>
      </div>

      {!isWalletConnected ? (
        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <svg
                className="w-5 h-5 text-yellow-600 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <p className="text-sm text-yellow-800">
                Connect your wallet to claim your airdrop tokens
              </p>
            </div>
          </div>

          <button
            onClick={onConnect}
            disabled={isClaiming || isLoading}
            className="text-lg bg-[#FF6600] text-white font-medium w-full py-3 hover:bg-[#e55a00] transition rounded-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-[#FF6600]"
          >
            Connect Wallet & Claim
          </button>
        </div>
      ) : (
        <button
          onClick={onClaim}
          disabled={isClaiming || isLoading}
          className="text-lg bg-[#FF6600] text-white font-medium w-full py-3 hover:bg-[#e55a00] transition rounded-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-[#FF6600] flex items-center justify-center"
        >
          {isClaiming ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Claiming...
            </>
          ) : (
            `Claim ${tokenAmount.toLocaleString()} Tokens`
          )}
        </button>
      )}

      <div className="mt-6 pt-4 border-t border-gray-200">
        <p className="text-sm text-gray-500">
          Airdrop Code:{' '}
          <span className="font-mono font-medium">{airdropCode}</span>
        </p>
      </div>
    </div>
  );
};

export default AirdropModal;
