'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React, { useState, useEffect, useRef } from 'react';

interface TabsProps {
  onTabChange: (val: string) => void;
  tabData: { name: string; icon?: React.ReactNode }[];
  selectedTab: string;
  loading?: boolean;
  variant?: 'default' | 'wide'; // Add variant prop for different styles
}

const Tabs: React.FC<TabsProps> = ({
  onTabChange,
  tabData,
  selectedTab,
  loading = false,
  variant = 'default',
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleTabSelect = (tabName: string) => {
    if (loading) return;
    onTabChange(tabName);
    setIsDropdownOpen(false);
  };

  const selectedTabData = tabData.find((tab) => tab.name === selectedTab);

  const mobileView = (
    <div className="relative w-full md:hidden" ref={dropdownRef}>
      <motion.div
        className="w-full py-4 px-5 bg-neutral-200 text-black rounded-lg flex justify-between items-center cursor-pointer disabled:opacity-50"
        onClick={() => !loading && setIsDropdownOpen(!isDropdownOpen)}
        whileHover={!loading ? { backgroundColor: '#d4d4d8' } : {}}
        whileTap={!loading ? { scale: 0.98 } : {}}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-3">
          {selectedTabData?.icon && (
            <div className="text-xl">{selectedTabData.icon}</div>
          )}
          <div className="text-xl font-semibold">{selectedTab}</div>
        </div>
        <motion.div
          animate={{ rotate: isDropdownOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <ChevronDown size={20} />
        </motion.div>
      </motion.div>

      <AnimatePresence>
        {isDropdownOpen && (
          <motion.div
            className="absolute w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            {tabData.map((tab, index) => (
              <motion.div
                key={tab.name}
                className={`py-3 px-5 cursor-pointer transition-colors ${
                  selectedTab === tab.name
                    ? 'bg-black text-white'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => handleTabSelect(tab.name)}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center gap-3">
                  {tab.icon && <div className="text-lg">{tab.icon}</div>}
                  <div className="font-semibold">{tab.name}</div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  const desktopView = (
    <motion.div
      className={`hidden md:flex rounded-lg overflow-hidden border-2 border-[#FF6600] bg-[#FF6600] relative ${
        variant === 'wide'
          ? 'w-full max-w-[800px]' // Make it wider for perks/airdrops/burns
          : 'w-full'
      }`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      {tabData.map((tab, index) => (
        <motion.div
          key={tab.name}
          className={`flex-1 py-4 relative inline-flex justify-center items-center cursor-pointer transition-all duration-300 overflow-hidden ${
            selectedTab === tab.name
              ? 'text-white z-10'
              : 'bg-white text-black hover:bg-gray-50'
          } ${
            index === 0
              ? 'rounded-l-lg'
              : index === 1
              ? 'border-l border-r border-[#FF6600]'
              : index === 2
              ? 'border-r border-[#FF6600]'
              : ''
          } ${index === tabData.length - 1 ? 'rounded-r-lg' : ''} ${
            variant === 'wide'
              ? 'min-w-[200px] px-8' // Add minimum width and more padding for wide variant
              : ''
          }`}
          onClick={() => handleTabSelect(tab.name)}
          whileHover={
            !loading && selectedTab !== tab.name
              ? {
                  scale: 1.02,
                  backgroundColor: '#f9fafb',
                }
              : {}
          }
          whileTap={!loading ? { scale: 0.98 } : {}}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.4 }}
        >
          {selectedTab === tab.name && (
            <motion.div
              className="absolute inset-0 bg-[#FF6600] rounded-inherit"
              layoutId={`activeTab-${variant}`} // Use variant-specific layoutId to prevent conflicts
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 30,
                duration: 0.5,
              }}
            />
          )}

          <motion.div
            className={`text-center font-semibold relative z-10 flex items-center justify-center gap-2 ${
              variant === 'wide'
                ? 'text-base lg:text-lg xl:text-xl 2xl:text-2xl' // Better responsive text sizing for wide variant
                : 'text-base lg:text-xl xl:text-2xl 2xl:text-3xl'
            }`}
            animate={{
              color: selectedTab === tab.name ? '#ffffff' : '#000000',
            }}
            transition={{ duration: 0.3 }}
          >
            {tab.icon && (
              <div
                className={`${
                  variant === 'wide'
                    ? 'text-lg lg:text-xl xl:text-2xl 2xl:text-3xl'
                    : 'text-lg lg:text-2xl xl:text-3xl 2xl:text-4xl'
                }`}
              >
                {tab.icon}
              </div>
            )}
            <span>{tab.name}</span>
          </motion.div>

          {loading && selectedTab === tab.name && (
            <motion.div
              className="absolute inset-0 bg-black/10 rounded-inherit flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
            </motion.div>
          )}
        </motion.div>
      ))}
    </motion.div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className={variant === 'wide' ? 'w-full flex justify-center' : ''}
    >
      {mobileView}
      {desktopView}
    </motion.div>
  );
};

export default Tabs;
