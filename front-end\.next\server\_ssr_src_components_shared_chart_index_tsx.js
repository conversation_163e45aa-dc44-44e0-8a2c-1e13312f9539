"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_chart_index_tsx";
exports.ids = ["_ssr_src_components_shared_chart_index_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/chart/index.tsx":
/*!***********************************************!*\
  !*** ./src/components/shared/chart/index.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradeChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ag_charts_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ag-charts-react */ \"(ssr)/./node_modules/ag-charts-react/dist/package/index.esm.mjs\");\n/* harmony import */ var ag_charts_enterprise__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-charts-enterprise */ \"(ssr)/./node_modules/ag-charts-enterprise/dist/package/main.esm.mjs\");\n/* harmony import */ var _utils_chartUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/chartUtils */ \"(ssr)/./src/components/shared/chart/utils/chartUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nclass ChartErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('Chart Error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full p-4 text-red-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Failed to load chart data. Please refresh the page and try again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nconst Chart = ({ windowWidth })=>{\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Chart.useEffect\": ()=>{\n            const loadChartData = {\n                \"Chart.useEffect.loadChartData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const currentDate = Date.now();\n                        const newData = (0,_utils_chartUtils__WEBPACK_IMPORTED_MODULE_4__.generateMockCandles)(100, currentDate, 0.0033, 60000);\n                        setData(newData);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load chart data');\n                        console.error('Chart data loading error:', err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Chart.useEffect.loadChartData\"];\n            loadChartData();\n        }\n    }[\"Chart.useEffect\"], []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full p-4 text-red-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse bg-gray-200 h-8 w-32 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse bg-gray-200 h-full w-full rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!windowWidth || data.length === 0 || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full p-4 text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_charts_react__WEBPACK_IMPORTED_MODULE_2__.AgCharts, {\n                options: (0,_utils_chartUtils__WEBPACK_IMPORTED_MODULE_4__.getChartOptions)(data, windowWidth)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\nfunction TradeChart() {\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradeChart.useEffect\": ()=>{\n            const handleResize = {\n                \"TradeChart.useEffect.handleResize\": ()=>{\n                    try {\n                        setWindowWidth(window.innerWidth);\n                    } catch (err) {\n                        setError('Failed to update chart dimensions');\n                        console.error('Resize error:', err);\n                    }\n                }\n            }[\"TradeChart.useEffect.handleResize\"];\n            try {\n                handleResize();\n                window.addEventListener('resize', handleResize);\n            } catch (err) {\n                setError('Failed to initialize chart');\n                console.error('Initialization error:', err);\n            }\n            return ({\n                \"TradeChart.useEffect\": ()=>{\n                    try {\n                        window.removeEventListener('resize', handleResize);\n                    } catch (err) {\n                        console.error('Cleanup error:', err);\n                    }\n                }\n            })[\"TradeChart.useEffect\"];\n        }\n    }[\"TradeChart.useEffect\"], [\n        ref\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-max relative rounded-[12px] overflow-hidden shadow-md flex-1\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full p-4 text-red-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-max relative rounded-[12px] overflow-hidden shadow-md flex-1\",\n        ref: ref,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Chart, {\n            windowWidth: ref?.current?.offsetWidth || windowWidth\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chart\\\\index.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/chart/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shared/chart/utils/chartUtils.ts":
/*!*********************************************************!*\
  !*** ./src/components/shared/chart/utils/chartUtils.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateMockCandles: () => (/* binding */ generateMockCandles),\n/* harmony export */   getChartOptions: () => (/* binding */ getChartOptions)\n/* harmony export */ });\nclass ChartError extends Error {\n    constructor(message){\n        super(message);\n        this.name = 'ChartError';\n    }\n}\nconst generateMockCandles = (count = 50, dateNow = 0, startPrice = 0.0033, intervalInMs = 60000)=>{\n    if (count <= 0) {\n        throw new ChartError('Count must be greater than 0');\n    }\n    if (startPrice <= 0) {\n        throw new ChartError('Start price must be greater than 0');\n    }\n    if (intervalInMs <= 0) {\n        throw new ChartError('Interval must be greater than 0');\n    }\n    try {\n        const candles = [];\n        const timestampStart = dateNow - count * intervalInMs;\n        let lastClose = startPrice;\n        for(let i = 0; i < count; i++){\n            const time = timestampStart + i * intervalInMs;\n            const open = lastClose;\n            const high = open + Math.random() * 0.00005;\n            const low = open - Math.random() * 0.00005;\n            const close = low + Math.random() * (high - low);\n            candles.push({\n                time,\n                open: +open.toFixed(6),\n                high: +high.toFixed(6),\n                low: +low.toFixed(6),\n                close: +close.toFixed(6),\n                volume: Math.floor(Math.random() * 100)\n            });\n            lastClose = close;\n        }\n        return candles;\n    } catch (error) {\n        throw new ChartError(`Failed to generate mock candles: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n};\nconst getChartOptions = (data, windowWidth)=>{\n    if (!Array.isArray(data)) {\n        throw new ChartError('Data must be an array');\n    }\n    if (data.length === 0) {\n        throw new ChartError('Data array cannot be empty');\n    }\n    if (windowWidth <= 0) {\n        throw new ChartError('Window width must be greater than 0');\n    }\n    try {\n        return {\n            width: windowWidth,\n            height: windowWidth < 600 ? windowWidth : windowWidth / 1.5,\n            data: data,\n            theme: 'ag-default-dark',\n            background: {\n                fill: '#ffffff'\n            },\n            padding: {\n                bottom: 50\n            },\n            zoom: {\n                enabled: true\n            },\n            annotations: {\n                toolbar: {\n                    padding: 0,\n                    buttons: [\n                        {\n                            value: 'line-menu',\n                            icon: 'line-style-solid'\n                        },\n                        {\n                            value: 'fibonacci-menu',\n                            icon: 'fibonacci-retracement-drawing'\n                        },\n                        {\n                            value: 'text-menu',\n                            icon: 'text-annotation'\n                        },\n                        {\n                            value: 'shape-menu',\n                            icon: 'trend-line-drawing'\n                        },\n                        {\n                            value: 'measurer-menu',\n                            icon: 'measurer-drawing'\n                        },\n                        {\n                            value: 'clear',\n                            icon: 'delete'\n                        }\n                    ]\n                }\n            },\n            series: [\n                {\n                    type: 'candlestick',\n                    xKey: 'time',\n                    openKey: 'open',\n                    highKey: 'high',\n                    lowKey: 'low',\n                    closeKey: 'close',\n                    tooltip: {\n                        renderer: (params)=>{\n                            try {\n                                const { datum } = params;\n                                return `\n                  <div style=\"padding: 4px;\">\n                    <b>${new Date(datum.time).toLocaleString()}</b><br/>\n                    Open: ${datum.open}<br/>\n                    High: ${datum.high}<br/>\n                    Low: ${datum.low}<br/>\n                    Close: ${datum.close}<br/>\n                  </div>\n                `;\n                            } catch (error) {\n                                console.error('Tooltip render error:', error);\n                                return '<div style=\"padding: 4px;\">Error loading tooltip data</div>';\n                            }\n                        }\n                    },\n                    item: {\n                        up: {\n                            fill: '#3BB266',\n                            stroke: '#3BB266',\n                            wick: {\n                                strokeWidth: 2\n                            }\n                        },\n                        down: {\n                            fill: '#E10000',\n                            stroke: '#E10000',\n                            wick: {\n                                strokeWidth: 2\n                            }\n                        }\n                    }\n                }\n            ],\n            axes: [\n                {\n                    type: 'ordinal-time',\n                    position: 'bottom',\n                    crosshair: {\n                        enabled: true\n                    },\n                    label: {\n                        autoRotate: true,\n                        autoRotateAngle: 90,\n                        rotation: windowWidth > 576 ? 0 : 90,\n                        format: '%H:%M',\n                        color: '#111112'\n                    },\n                    tick: {\n                        enabled: true\n                    }\n                },\n                {\n                    type: 'number',\n                    position: 'right',\n                    thickness: windowWidth < 600 ? 40 : 50,\n                    label: {\n                        color: '#111112'\n                    }\n                }\n            ]\n        };\n    } catch (error) {\n        throw new ChartError(`Failed to generate chart options: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/chart/utils/chartUtils.ts\n");

/***/ })

};
;