"use client";

import { motion, TargetAndTransition, VariantLabels } from "framer-motion";
import { Loader2 } from "lucide-react";
import React from "react";

import { buttonHover } from "@/lib/animations";
import { useTranslation } from '../../../../hooks/useTranslation';

interface ActionButtonProps {
  onClick: () => void;
  disabled: boolean;
  isLoading: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  onClick, 
  disabled, 
  isLoading 
}) => {
  const { t } = useTranslation();
  return (
    <motion.button
      className="w-full py-[12px] px-[20px] bg-[#3BB266] text-white rounded-[10px] font-['IBM_Plex_Sans'] font-semibold text-[18px] leading-[150%] transition-all duration-300 hover:bg-[#F58A38] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      onClick={onClick}
      disabled={disabled}
      whileHover={!isLoading ? buttonHover as unknown as (VariantLabels | TargetAndTransition) : {}}
      whileTap={!isLoading ? { scale: 0.95 } : {}}
    >
      {isLoading ? (
        <>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 size={20} />
          </motion.div>
          {t('exchangeForm.processing')}
        </>
      ) : (
        t('exchangeForm.swap')
      )}
    </motion.button>
  );
};

export default ActionButton; 