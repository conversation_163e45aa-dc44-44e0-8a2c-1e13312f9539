import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CreatePerkForm from '../create-perk-form'
import * as errorHandling from '../../../utils/errorHandling'
import * as requests from '../../../axios/requests'

// Mock dependencies
jest.mock('../../../contexts/AppContext', () => ({
  useAppContext: () => ({
    state: {
      userBo: JSON.stringify({ id: 123, username: 'testuser' }),
    },
  }),
}))

jest.mock('../../../utils/errorHandling', () => ({
  showErrorToast: jest.fn(),
  showSuccessToast: jest.fn(),
  TOAST_MESSAGES: {
    PERK: {
      CREATE_SUCCESS: 'Perk created successfully! Your perk is now available in the shop.',
      CREATE_FAILED: 'Failed to create perk. Please check your input and try again.',
    },
    FORM: {
      IMAGE_UPLOAD_FAILED: 'Failed to upload image. Please try again with a different file.',
      VALIDATION_FAILED: 'Please check your input and fix any errors before submitting.',
    },
    AUTH: {
      LOGIN_REQUIRED: 'Please log in to access this feature.',
    },
    NETWORK: {
      SERVER_ERROR: 'Server error occurred. Please try again later.',
    },
  },
  createFormError: jest.fn((message: string) => new Error(message)),
  createAPIError: jest.fn((message: string, status: number) => ({ message, status })),
}))

jest.mock('../../../axios/requests', () => ({
  addPerk: jest.fn(),
}))

jest.mock('../../../utils/helpers', () => ({
  uploadToPinata: jest.fn(),
}))

jest.mock('../../../utils/formValidation', () => ({
  validateName: jest.fn(() => ''),
  validatePrice: jest.fn(() => ''),
  validateUrl: jest.fn(() => ''),
  validateStockAmount: jest.fn(() => ''),
  hasErrors: jest.fn(() => false),
}))

// Mock FormFields component
jest.mock('../components/FormFields', () => {
  return function MockFormFields({ formData, errors, handleChange, handleBlur, handleCheckboxChange, handleFileChange, handleDeletePicture }: any) {
    return (
      <div data-testid="form-fields">
        <input
          data-testid="name-input"
          name="name"
          value={formData.name}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="Perk name"
        />
        <input
          data-testid="price-input"
          name="price"
          value={formData.price}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="Price"
        />
        <input
          data-testid="description-input"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Description"
        />
        <input
          data-testid="fulfillment-link-input"
          name="fulfillmentLink"
          value={formData.fulfillmentLink}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder="Fulfillment link"
        />
        <select
          data-testid="category-select"
          name="category"
          value={formData.category}
          onChange={handleChange}
        >
          <option value="">Select category</option>
          <option value="digital">Digital</option>
          <option value="physical">Physical</option>
        </select>
        <input
          data-testid="file-input"
          type="file"
          onChange={handleFileChange}
          accept="image/*"
        />
        <label>
          <input
            data-testid="limited-stock-checkbox"
            type="checkbox"
            name="limitedStock"
            checked={formData.limitedStock}
            onChange={handleCheckboxChange}
          />
          Limited Stock
        </label>
        {formData.limitedStock && (
          <input
            data-testid="stock-amount-input"
            name="stockAmount"
            value={formData.stockAmount}
            onChange={handleChange}
            placeholder="Stock amount"
          />
        )}
        {formData.picture && (
          <button
            data-testid="delete-picture-button"
            onClick={handleDeletePicture}
          >
            Delete Picture
          </button>
        )}
        {errors.name && <span data-testid="name-error">{errors.name}</span>}
        {errors.price && <span data-testid="price-error">{errors.price}</span>}
        {errors.picture && <span data-testid="picture-error">{errors.picture}</span>}
        {errors.api && <span data-testid="api-error">{errors.api}</span>}
      </div>
    )
  }
})

describe('CreatePerkForm', () => {
  const mockOnClose = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock successful API response by default
    ;(requests.addPerk as jest.Mock).mockResolvedValue({
      status: 201,
      data: { id: 1, name: 'Test Perk' },
    })
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Form Rendering', () => {
    it('should render form with all fields', () => {
      render(<CreatePerkForm onClose={mockOnClose} />)

      expect(screen.getByText('Create Perk')).toBeInTheDocument()
      expect(screen.getByTestId('name-input')).toBeInTheDocument()
      expect(screen.getByTestId('price-input')).toBeInTheDocument()
      expect(screen.getByTestId('description-input')).toBeInTheDocument()
      expect(screen.getByTestId('fulfillment-link-input')).toBeInTheDocument()
      expect(screen.getByTestId('category-select')).toBeInTheDocument()
      expect(screen.getByTestId('file-input')).toBeInTheDocument()
      expect(screen.getByTestId('limited-stock-checkbox')).toBeInTheDocument()
      expect(screen.getByText('Create my perk')).toBeInTheDocument()
    })

    it('should show close button on mobile', () => {
      render(<CreatePerkForm onClose={mockOnClose} />)

      const closeButton = screen.getByRole('button', { name: /close/i })
      expect(closeButton).toBeInTheDocument()
    })

    it('should not show stock amount input initially', () => {
      render(<CreatePerkForm onClose={mockOnClose} />)

      expect(screen.queryByTestId('stock-amount-input')).not.toBeInTheDocument()
    })

    it('should show stock amount input when limited stock is checked', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.click(screen.getByTestId('limited-stock-checkbox'))

      expect(screen.getByTestId('stock-amount-input')).toBeInTheDocument()
    })
  })

  describe('Form Interactions', () => {
    it('should update form data when user types', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const nameInput = screen.getByTestId('name-input')
      await user.type(nameInput, 'Test Perk')

      expect(nameInput).toHaveValue('Test Perk')
    })

    it('should update price field with numeric input', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const priceInput = screen.getByTestId('price-input')
      await user.type(priceInput, '19.99')

      expect(priceInput).toHaveValue('19.99')
    })

    it('should handle category selection', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const categorySelect = screen.getByTestId('category-select')
      await user.selectOptions(categorySelect, 'digital')

      expect(categorySelect).toHaveValue('digital')
    })

    it('should toggle limited stock checkbox', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const checkbox = screen.getByTestId('limited-stock-checkbox')
      
      expect(checkbox).not.toBeChecked()
      
      await user.click(checkbox)
      expect(checkbox).toBeChecked()
      
      await user.click(checkbox)
      expect(checkbox).not.toBeChecked()
    })

    it('should handle file upload', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const fileInput = screen.getByTestId('file-input')

      await user.upload(fileInput, file)

      expect(fileInput.files?.[0]).toBe(file)
    })

    it('should handle file deletion', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // First upload a file
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const fileInput = screen.getByTestId('file-input')
      await user.upload(fileInput, file)

      // Then delete it
      const deleteButton = screen.getByTestId('delete-picture-button')
      await user.click(deleteButton)

      expect(screen.queryByTestId('delete-picture-button')).not.toBeInTheDocument()
    })

    it('should close form when close button is clicked', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)

      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Fill form with valid data
      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '19.99')
      await user.type(screen.getByTestId('description-input'), 'Test description')
      await user.type(screen.getByTestId('fulfillment-link-input'), 'https://example.com')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      // Upload file
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      await user.upload(screen.getByTestId('file-input'), file)

      // Submit form
      const submitButton = screen.getByText('Create my perk')
      await user.click(submitButton)

      await waitFor(() => {
        expect(requests.addPerk).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Perk',
            price: '19.99',
            description: 'Test description',
            fulfillmentLink: 'https://example.com',
            category: 'digital',
            userId: 123,
          })
        )
      })
    })

    it('should show success message after successful submission', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Fill minimal required data
      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      // Submit form
      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(screen.getByText('Success!')).toBeInTheDocument()
        expect(screen.getByText('Perk Created Successfully!')).toBeInTheDocument()
      })
    })

    it('should handle image upload during submission', async () => {
      const { uploadToPinata } = require('../../../utils/helpers')
      uploadToPinata.mockResolvedValue('https://pinata.cloud/image-url')

      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Fill form and upload image
      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      await user.upload(screen.getByTestId('file-input'), file)

      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(uploadToPinata).toHaveBeenCalledWith(file)
        expect(requests.addPerk).toHaveBeenCalledWith(
          expect.objectContaining({
            image: 'https://pinata.cloud/image-url',
          })
        )
      })
    })

    it('should handle submission with limited stock', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Fill form with limited stock
      await user.type(screen.getByTestId('name-input'), 'Limited Perk')
      await user.type(screen.getByTestId('price-input'), '25')
      await user.selectOptions(screen.getByTestId('category-select'), 'physical')
      await user.click(screen.getByTestId('limited-stock-checkbox'))
      await user.type(screen.getByTestId('stock-amount-input'), '100')

      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(requests.addPerk).toHaveBeenCalledWith(
          expect.objectContaining({
            isLimited: true,
            stockAmount: 100,
          })
        )
      })
    })

    it('should disable submit button while submitting', async () => {
      const user = userEvent.setup()
      
      // Mock slow API response
      ;(requests.addPerk as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({ status: 201 }), 1000))
      )

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      const submitButton = screen.getByText('Create my perk')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Processing...')).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('should show API error message on submission failure', async () => {
      const user = userEvent.setup()
      
      ;(requests.addPerk as jest.Mock).mockRejectedValue({
        response: {
          status: 400,
          data: { message: 'Invalid perk data' },
        },
      })

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(errorHandling.showErrorToast).toHaveBeenCalled()
      })
    })

    it('should handle 401 unauthorized error', async () => {
      const user = userEvent.setup()
      
      ;(requests.addPerk as jest.Mock).mockRejectedValue({
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      })

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(errorHandling.showErrorToast).toHaveBeenCalled()
      })
    })

    it('should handle server error (500)', async () => {
      const user = userEvent.setup()
      
      ;(requests.addPerk as jest.Mock).mockRejectedValue({
        response: {
          status: 500,
          data: { message: 'Server error' },
        },
      })

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(errorHandling.showErrorToast).toHaveBeenCalled()
      })
    })

    it('should handle image upload failure', async () => {
      const { uploadToPinata } = require('../../../utils/helpers')
      uploadToPinata.mockRejectedValue(new Error('Upload failed'))

      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      await user.upload(screen.getByTestId('file-input'), file)

      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(errorHandling.showErrorToast).toHaveBeenCalled()
      })
    })

    it('should handle non-201 response status', async () => {
      const user = userEvent.setup()
      
      ;(requests.addPerk as jest.Mock).mockResolvedValue({
        status: 400,
        data: { message: 'Validation failed' },
      })

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.click(screen.getByText('Create my perk'))

      await waitFor(() => {
        expect(errorHandling.showErrorToast).toHaveBeenCalled()
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle very long perk names', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const longName = 'a'.repeat(1000)
      const nameInput = screen.getByTestId('name-input')
      
      await user.type(nameInput, longName)
      
      expect(nameInput).toHaveValue(longName)
    })

    it('should handle special characters in form fields', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      const specialName = 'Test Perk 中文 🚀 @#$%^&*()'
      await user.type(screen.getByTestId('name-input'), specialName)
      
      expect(screen.getByTestId('name-input')).toHaveValue(specialName)
    })

    it('should handle very large file uploads', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Create a large file (>5MB)
      const largeFile = new File(
        [new ArrayBuffer(6 * 1024 * 1024)], 
        'large.jpg', 
        { type: 'image/jpeg' }
      )

      const fileInput = screen.getByTestId('file-input')
      await user.upload(fileInput, largeFile)

      // Should still accept the file (validation happens elsewhere)
      expect(fileInput.files?.[0]).toBe(largeFile)
    })

    it('should handle multiple rapid form submissions', async () => {
      const user = userEvent.setup()
      
      // Mock slow API response
      ;(requests.addPerk as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({ status: 201 }), 500))
      )

      render(<CreatePerkForm onClose={mockOnClose} />)

      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      const submitButton = screen.getByText('Create my perk')
      
      // Click submit multiple times rapidly
      await user.click(submitButton)
      await user.click(submitButton)
      await user.click(submitButton)

      // Should only submit once
      await waitFor(() => {
        expect(requests.addPerk).toHaveBeenCalledTimes(1)
      }, { timeout: 1000 })
    })

    it('should handle user logout during form session', () => {
      // Mock user not logged in
      jest.doMock('../../../contexts/AppContext', () => ({
        useAppContext: () => ({
          state: {
            userBo: null,
          },
        }),
      }))

      render(<CreatePerkForm onClose={mockOnClose} />)

      // Form should still render (validation will catch this on submit)
      expect(screen.getByText('Create Perk')).toBeInTheDocument()
    })

    it('should handle invalid JSON in userBo', () => {
      // Mock invalid JSON
      jest.doMock('../../../contexts/AppContext', () => ({
        useAppContext: () => ({
          state: {
            userBo: 'invalid-json',
          },
        }),
      }))

      expect(() => {
        render(<CreatePerkForm onClose={mockOnClose} />)
      }).not.toThrow()
    })

    it('should handle form reset after successful submission', async () => {
      const user = userEvent.setup()
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Fill form
      await user.type(screen.getByTestId('name-input'), 'Test Perk')
      await user.type(screen.getByTestId('price-input'), '10')
      await user.selectOptions(screen.getByTestId('category-select'), 'digital')

      // Submit form
      await user.click(screen.getByText('Create my perk'))

      // Wait for success state
      await waitFor(() => {
        expect(screen.getByText('Success!')).toBeInTheDocument()
      })

      // Wait for automatic reset (2 second timeout)
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled()
      }, { timeout: 3000 })
    })
  })

  describe('Accessibility', () => {
    it('should have proper form labels and structure', () => {
      render(<CreatePerkForm onClose={mockOnClose} />)

      // Check that form fields are accessible
      expect(screen.getByTestId('name-input')).toBeInTheDocument()
      expect(screen.getByTestId('price-input')).toBeInTheDocument()
      expect(screen.getByTestId('category-select')).toBeInTheDocument()
      expect(screen.getByTestId('file-input')).toBeInTheDocument()
    })

    it('should have accessible submit button', () => {
      render(<CreatePerkForm onClose={mockOnClose} />)

      const submitButton = screen.getByRole('button', { name: /create my perk/i })
      expect(submitButton).toBeInTheDocument()
      expect(submitButton).toHaveAttribute('type', 'submit')
    })

    it('should display error messages accessibly', async () => {
      const { validateName } = require('../../../utils/formValidation')
      validateName.mockReturnValue('Name is required')

      render(<CreatePerkForm onClose={mockOnClose} />)

      // Trigger validation error
      const nameInput = screen.getByTestId('name-input')
      fireEvent.blur(nameInput)

      // Error should be displayed (mock validation)
      // In real implementation, this would show the error
    })
  })
}) 