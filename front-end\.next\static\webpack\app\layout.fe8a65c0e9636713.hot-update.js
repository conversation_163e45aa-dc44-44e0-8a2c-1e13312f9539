"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId,\n            hasTradeId: !!notification.data.tradeId,\n            hasChatRoomId: !!notification.data.chatRoomId,\n            hasPerkId: !!notification.data.perkId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Use getUserTrades to find trades for current user and filter by perkId\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(myUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        // Find active trade for this perk\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(perkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            // Get the most recent trade\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeIdToUse = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                var _tradeResponse_data;\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data,\n                    tradeDataId: (_tradeResponse_data = tradeResponse.data) === null || _tradeResponse_data === void 0 ? void 0 : _tradeResponse_data.id,\n                    tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    // Ensure we have a valid ID - use tradeIdToUse as fallback\n                    const validId = tradeResponse.data.id || tradeIdToUse;\n                    activeTrade = {\n                        id: validId,\n                        status: tradeResponse.data.status,\n                        tradeId: validId,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to notification data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: notification.data.status || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: notification.data.perkId,\n                        escrowId: notification.data.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, don't close it, just focus it\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, focusing existing modal');\n            existingModal.scrollIntoView({\n                behavior: 'smooth'\n            });\n            return;\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            walletConnected: isConnected,\n            hasWallet: !!(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address)\n        });\n        // Ensure we always have functional action functions\n        const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n            alert('Cannot release escrow: Trade information not available');\n        };\n        const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n            alert('Cannot accept escrow: Trade information not available');\n        };\n        const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n            alert('Cannot refund escrow: Trade information not available');\n        };\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: safeOnRelease,\n                onRefund: safeOnRefund,\n                onReport: ()=>{\n                    console.log('🔍 [NotificationBell] Report function called');\n                // TODO: Implement report functionality\n                },\n                onAccept: safeOnAccept,\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 730,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 749,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 748,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 747,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 755,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 764,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 771,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 780,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 779,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 790,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 789,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 798,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 805,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 824,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 822,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 832,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 878,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 891,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 881,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 950,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 994,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1010,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 983,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 962,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1039,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1038,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 899,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 876,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5549d0c6fbb0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTU0OWQwYzZmYmIwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});