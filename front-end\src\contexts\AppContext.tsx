"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useMemo } from 'react';
import { useWallet } from "@/hooks/useWallet";

// Define the shape of userBO (example fields, adjust based on your API)
interface UserBo {
  id: number;
  username: string;
  email: string;
  twoFactorStatus: boolean;
  twoFactorData: {
    verified: boolean;
    secret: string;
    qrCodeUrl: string;
  } | null;
  // Add other fields from your login API response if needed
}

// Define the shape of our global app state
interface AppState {
  userBalance: string;
  notifications: number;
  isLoggedIn: boolean;
  userBo: UserBo | null; // 👈 NEW user object
  unreadSystemNotificationsCount: number; // 👈 NEW
  openChatTradeId: string | number | null; // 👈 NEW
}

// Define the shape of the context
interface AppContextType {
  state: AppState;
  updateUserBalance: (balance: string) => void;
  setNotificationCount: (count: number) => void;
  setUnreadSystemNotificationsCount: (count: number) => void; // 👈 NEW
  setOpenChatTradeId: (tradeId: string | number | null) => void; // 👈 NEW
  login: (userBo: UserBo,token:string) => void; // 👈 Pass userBo on login
  updateUserBo: (userBo: UserBo) => void; // 👈 Pass userBo on updateUserBo
  logout: () => void;
}

// Create the context with a default value
const AppContext = createContext<AppContextType | undefined>(undefined);

// Initial state for the app
const initialState: AppState = {
  userBalance: '0.00 USD',
  notifications: 0,
  isLoggedIn: false,

  userBo: null, // 👈 initialize as null
  unreadSystemNotificationsCount: 0, // 👈 NEW
  openChatTradeId: null, // 👈 NEW
};

// Define the shape of an unread chat message
export interface UnreadChatMessage {
  id: string;
  tradeId: string | number;
  senderId: string | number;
  receiverId: string | number;
  message: string;
  createdAt: string;
  chatRoomId: string; // Add chatRoomId to the interface
}

interface UnreadChatMessagesContextType {
  unreadChatMessages: UnreadChatMessage[];
  addUnreadChatMessage: (msg: UnreadChatMessage) => void;
  removeUnreadChatMessagesForTrade: (tradeId: string | number) => void;
  clearUnreadChatMessages: () => void;
}

const UnreadChatMessagesContext = createContext<UnreadChatMessagesContextType | undefined>(undefined);

export const UnreadChatMessagesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [unreadChatMessages, setUnreadChatMessages] = useState<UnreadChatMessage[]>([]);

  const addUnreadChatMessage = useCallback((msg: UnreadChatMessage) => {
    setUnreadChatMessages(prev => {
      // Prevent duplicates by id
      if (prev.some(m => m.id === msg.id)) return prev;
      return [...prev, msg];
    });
  }, []);

  const removeUnreadChatMessagesForTrade = useCallback((tradeId: string | number) => {
    setUnreadChatMessages(prev => prev.filter(m => m.tradeId !== tradeId));
  }, []);

  const clearUnreadChatMessages = useCallback(() => setUnreadChatMessages([]), []);

  return (
    <UnreadChatMessagesContext.Provider value={{ unreadChatMessages, addUnreadChatMessage, removeUnreadChatMessagesForTrade, clearUnreadChatMessages }}>
      {children}
    </UnreadChatMessagesContext.Provider>
  );
};

export const useUnreadChatMessages = () => {
  const ctx = useContext(UnreadChatMessagesContext);
  if (!ctx) {
    // Fallback: return empty array and no-op functions if not inside provider
    return {
      unreadChatMessages: [],
      addUnreadChatMessage: () => {},
      removeUnreadChatMessagesForTrade: () => {},
      clearUnreadChatMessages: () => {},
    };
  }
  return ctx;
};


/**
 * App context provider component
 */
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<AppState>(initialState);
  const { solanaWallet } = useWallet();

  // Initialize login state from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserBo = localStorage.getItem('userBo');
      const storedToken = localStorage.getItem('token');

      if (storedUserBo && storedUserBo !== 'undefined' && storedToken && storedToken !== 'undefined') {
        try {
          // Safely parse the userBo string into an object of type UserBo
          const parsedUserBo: UserBo = JSON.parse(storedUserBo); // Assuming UserBo is the type you're expecting

          setState(prev => ({
            ...prev,
            isLoggedIn: true,
            userBo: parsedUserBo, // Now it's correctly typed as UserBo
          }));
        } catch (error) {
          console.error('Error parsing stored userBo:', error);
          // Optionally clear localStorage in case of corrupted data
          localStorage.removeItem('userBo');
          localStorage.removeItem('token');
        }
      }
    }
  }, []);

  // Save wallet address to localStorage for chat authentication
  useEffect(() => {
    if (solanaWallet?.address) {
      localStorage.setItem("wallet", solanaWallet.address);
    }
  }, [solanaWallet]);

  // Update user balance
  const updateUserBalance = useCallback((balance: string) => {
    setState(prev => ({ ...prev, userBalance: balance }));
  }, []);

  // Set notification count
  const setNotificationCount = useCallback((count: number) => {
    setState(prev => ({ ...prev, notifications: count }));
  }, []);
  // 👇 NEW: Set unread system notifications count
  const setUnreadSystemNotificationsCount = useCallback((count: number) => {
    setState(prev => ({ ...prev, unreadSystemNotificationsCount: count }));
  }, []);
  // 👇 NEW: Set open chat tradeId
  const setOpenChatTradeId = useCallback((tradeId: string | number | null) => {
    setState(prev => ({ ...prev, openChatTradeId: tradeId }));
  }, []);

  // Login function (save userBo and mark as logged in)
  const login = useCallback((userBo: UserBo, token: string) => {
    setState(prev => ({
      ...prev,
      isLoggedIn: true,
      userBo,
      token,
    }));
    // Save to localStorage
    localStorage.setItem('userBo', JSON.stringify(userBo));
    localStorage.setItem('userSession', 'true');
    localStorage.setItem('token', token);
  }, []);


  const updateUserBo = (newUserBo: UserBo) => {

    // Update state with the actual object (no need to stringify)
    setState((prev) => ({
      ...prev,
      userBo: newUserBo,
    }));

    // Also sync the userBo to localStorage (as string)
    localStorage.setItem('userBo', JSON.stringify(newUserBo));
  };

  // Logout function
  const logout = useCallback(() => {
    setState(prev => ({
      ...prev,
      isLoggedIn: false,
      userBo: null,
    }));
    console.log("logout");

    localStorage.removeItem('userBo');
    localStorage.removeItem('token');
    localStorage.removeItem('userSession');
  }, []);

  // Value to be provided to consumers
  const value = useMemo(() => ({
    state,
    updateUserBalance,
    setNotificationCount,
    setUnreadSystemNotificationsCount, // 👈 NEW
    setOpenChatTradeId, // 👈 NEW
    login,
    logout,
    updateUserBo
  }), [state, updateUserBalance, setNotificationCount, setUnreadSystemNotificationsCount, setOpenChatTradeId, login, logout,updateUserBo]);

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

/**
 * Custom hook to use the app context
 */
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}