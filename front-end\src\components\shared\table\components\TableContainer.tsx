import { motion } from 'framer-motion';
import React from 'react';

interface TableContainerProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  variant?: 'default' | 'minimal' | 'bordered';
  size?: 'sm' | 'md' | 'lg';
}

const TableContainer: React.FC<TableContainerProps> = ({
  children,
  title,
  className = '',
  variant = 'default',
  size = 'md',
}) => {
  const variantStyles = {
    default: 'bg-white border border-gray-200 shadow-sm',
    minimal: 'bg-transparent',
    bordered: 'bg-white border-2 border-gray-300 shadow-md',
  };

  const sizeStyles = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <motion.div
      className={`w-full rounded-xl overflow-hidden ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      {title && (
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        </div>
      )}

      {/* Table Content */}
      <div className="overflow-x-auto">
        {children}
      </div>
    </motion.div>
  );
};

export { TableContainer }; 