"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_EmptyStates_BaseEmptyState_tsx";
exports.ids = ["_ssr_src_components_ui_EmptyStates_BaseEmptyState_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/EmptyStates/BaseEmptyState.tsx":
/*!**********************************************************!*\
  !*** ./src/components/ui/EmptyStates/BaseEmptyState.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/animations */ \"(ssr)/./src/lib/animations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst EmptyState = ({ title, description, icon, action, className = '' })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: `flex flex-col items-center justify-center py-16 px-8 text-center ${className}`,\n        ..._lib_animations__WEBPACK_IMPORTED_MODULE_1__.fadeInUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"mb-6 p-4 bg-gray-100 rounded-full\",\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    delay: 0.2,\n                    type: 'spring',\n                    stiffness: 200\n                },\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h3, {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.3\n                },\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                className: \"text-gray-600 mb-6 max-w-md\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.4\n                },\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 45,\n                columnNumber: 5\n            }, undefined),\n            action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                className: \"bg-[#FF6600] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a00] transition-colors\",\n                onClick: action.onClick,\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.5\n                },\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: action.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmptyState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/EmptyStates/BaseEmptyState.tsx\n");

/***/ })

};
;