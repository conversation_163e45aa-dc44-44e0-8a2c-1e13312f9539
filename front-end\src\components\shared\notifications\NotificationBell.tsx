"use client";

import React, { useState, useEffect, useRef } from 'react';
import { usePrivy } from "@privy-io/react-auth";
import { getNotifications, getUnreadNotificationCount, markNotificationAsRead, markAllNotificationsAsRead, releasePerk, refundPerk } from '@/axios/requests';
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import { useChatModalState } from '@/contexts/ChatModalStateContext';
import { useWallet } from '@/hooks/useWallet';
import { useEscrowOperations } from '@/hooks/useEscrowOperations';
import ChatModal from '@/components/shared/chat/ChatModal';
import RealTimeNotificationListener from './RealTimeNotificationListener';
import { API_CONFIG } from '@/config/environment';

interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  createdAt: string;
  data?: {
    tradeId?: number;
    disputeId?: number;
    buyerId?: number;
    sellerId?: number;
    [key: string]: any;
  };
}

type TabType = 'system' | 'trade';

export default function NotificationBell() {
  const [systemNotifications, setSystemNotifications] = useState<Notification[]>([]);
  const [tradeNotifications, setTradeNotifications] = useState<Notification[]>([]);
  const [systemUnreadCount, setSystemUnreadCount] = useState(0);
  const [tradeUnreadCount, setTradeUnreadCount] = useState(0);
  const [activeTab, setActiveTab] = useState<TabType>('system');
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user } = usePrivy();
  const { openModal, closeModal } = useGlobalModal();
  const { updateModalProps, getChatModalState } = useChatModalState();
  const { solanaWallet, isConnected } = useWallet();
  const escrowOperations = useEscrowOperations();

  // Load notifications and separate them by type
  const loadNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [notificationsResponse, unreadResponse] = await Promise.all([
        getNotifications({ page: 1, pageSize: 20 }),
        getUnreadNotificationCount()
      ]);

      if (notificationsResponse.status === 200) {
        const allNotifications = notificationsResponse.data.notifications;

        // Separate notifications into system and trade categories
        const systemNots: Notification[] = [];
        const tradeNots: Notification[] = [];

        allNotifications.forEach((notification: Notification) => {
          if (isTradeNotification(notification)) {
            tradeNots.push(notification);
          } else {
            systemNots.push(notification);
          }
        });

        console.log('📊 [NotificationBell] Notification categorization:', {
          totalNotifications: allNotifications.length,
          systemNotifications: systemNots.length,
          tradeNotifications: tradeNots.length,
          systemTypes: systemNots.map(n => n.type),
          tradeTypes: tradeNots.map(n => n.type)
        });

        setSystemNotifications(systemNots);
        setTradeNotifications(tradeNots);

        // Calculate unread counts for each category
        const systemUnread = systemNots.filter(n => !n.isRead).length;
        const tradeUnread = tradeNots.filter(n => !n.isRead).length;

        setSystemUnreadCount(systemUnread);
        setTradeUnreadCount(tradeUnread);
      }

      // Note: We calculate unread counts from the filtered notifications
      // rather than using the API response since we need separate counts
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to determine if a notification is trade-related
  const isTradeNotification = (notification: Notification): boolean => {
    // System notifications (non-trade) - these go to System tab
    const systemTypes = [
      'system_announcement',
      'moderator_added',
      'perk_created',
      'token_created',
      // Moderator-specific notifications (go to moderator dashboard)
      'dispute_created',
      'dispute_assigned'
    ];

    // If it's a system type, it's NOT a trade notification
    if (systemTypes.includes(notification.type)) {
      return false;
    }

    // All escrow, trade, and user dispute notifications go to Trade tab
    const tradeTypes = [
      // Trade and escrow notifications
      'perk_purchased',
      'perk_sold',
      'escrow_created',
      'escrow_released',
      'escrow_release_reminder',
      'trade_completed',
      'trade_refunded',
      'trade_reported',
      'trade_disputed',
      // User dispute notifications (trade-related)
      'dispute_resolved',
      // Chat notifications (trade-related)
      'chat_message'
    ];

    // Return true if it's a trade type
    return tradeTypes.includes(notification.type);
  };

  // Get user role in trade for display
  const getUserRoleInTrade = (notification: Notification): string => {
    if (!notification.data?.buyerId || !notification.data?.sellerId) return '';

    const userId = user?.id;
    const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;

    if (notification.data.buyerId === userIdNumber) return 'buyer';
    if (notification.data.sellerId === userIdNumber) return 'seller';
    return '';
  };

  // Helper function to generate consistent chat room ID for a trade
  const generateChatRoomId = (buyerId: number, sellerId: number, perkId: number): string => {
    return `${buyerId}-${sellerId}-${perkId}`;
  };

  // Helper function to determine if notification should open chat modal
  const shouldOpenChatModal = (notification: Notification): boolean => {
    const chatTypes = [
      'perk_sold', 'perk_purchased', 'escrow_released', 'trade_completed',
      'trade_disputed', 'dispute_resolved', 'trade_refunded', 'chat_message',
      'escrow_created', 'escrow_pending_acceptance', 'escrow_accepted',
      'escrow_release_reminder', 'trade_reported'
    ];

    const hasRequiredData = !!(notification.data?.chatRoomId || notification.data?.tradeId);
    const shouldOpen = chatTypes.includes(notification.type) && hasRequiredData;

    console.log('🔍 [NotificationBell] shouldOpenChatModal check:', {
      type: notification.type,
      isValidType: chatTypes.includes(notification.type),
      hasRequiredData,
      chatRoomId: notification.data?.chatRoomId,
      tradeId: notification.data?.tradeId,
      shouldOpen
    });

    return shouldOpen;
  };

  // Unified function to normalize notification data and determine user roles
  const normalizeNotificationData = async (notification: Notification, currentUserId: number) => {
    console.log('🔍 [NotificationBell] Normalizing notification data:', {
      type: notification.type,
      data: notification.data,
      currentUserId,
      currentUserIdType: typeof currentUserId
    });

    if (!notification.data) {
      throw new Error('Notification data is missing');
    }

    if (!currentUserId || isNaN(currentUserId) || currentUserId <= 0) {
      throw new Error(`Invalid currentUserId: ${currentUserId}`);
    }

    let buyerId = notification.data.buyerId;
    let sellerId = notification.data.sellerId;
    let tradeId = notification.data.tradeId;
    let chatRoomId = notification.data.chatRoomId;
    let perkId = notification.data.perkId;

    // Handle different notification types with unified logic
    switch (notification.type) {
      case 'perk_purchased':
      case 'escrow_created':
        // Current user is buyer, notification contains seller info
        buyerId = currentUserId;
        sellerId = notification.data.sellerId || notification.data.receiverId;
        break;

      case 'perk_sold':
      case 'escrow_pending_acceptance':
      case 'escrow_accepted':
        // Current user is seller, notification contains buyer info
        sellerId = currentUserId;
        buyerId = notification.data.buyerId || notification.data.senderId;
        break;

      case 'escrow_released':
      case 'trade_completed':
        // Could be either buyer or seller, determine from notification data
        if (notification.data.buyerId === currentUserId) {
          buyerId = currentUserId;
          sellerId = notification.data.sellerId;
        } else {
          sellerId = currentUserId;
          buyerId = notification.data.buyerId;
        }
        break;

      case 'chat_message':
        // For chat messages, use the buyerId and sellerId directly from notification data
        // These are set when the notification is created in the backend
        buyerId = notification.data.buyerId;
        sellerId = notification.data.sellerId;

        console.log('🔍 [NotificationBell] Chat message notification data:', {
          senderId: notification.data.senderId,
          receiverId: notification.data.receiverId,
          buyerId: notification.data.buyerId,
          sellerId: notification.data.sellerId,
          currentUserId,
          chatRoomId: notification.data.chatRoomId
        });

        // Fallback if buyerId/sellerId not available
        if (!buyerId || !sellerId) {
          if (notification.data.senderId === currentUserId) {
            // I sent the message, I'm talking to the receiver
            buyerId = currentUserId;
            sellerId = notification.data.receiverId;
          } else {
            // I received the message, sender is talking to me
            buyerId = notification.data.senderId;
            sellerId = currentUserId;
          }
        }
        break;

      default:
        // Fallback logic for other types
        if (!buyerId || !sellerId) {
          if (notification.data.buyerId === currentUserId) {
            buyerId = currentUserId;
            sellerId = notification.data.sellerId || notification.data.receiverId;
          } else {
            sellerId = currentUserId;
            buyerId = notification.data.buyerId || notification.data.senderId;
          }
        }
    }

    // Generate chatRoomId if not provided
    if (!chatRoomId && buyerId && sellerId && perkId) {
      chatRoomId = generateChatRoomId(buyerId, sellerId, perkId);
      console.log('🔍 [NotificationBell] Generated chatRoomId:', chatRoomId);
    }

    // Try to find tradeId if not provided (only if we have a valid currentUserId)
    // Skip this lookup for now to avoid API errors - we can work without tradeId
    if (!tradeId && chatRoomId && currentUserId && currentUserId > 0) {
      console.log('🔍 [NotificationBell] TradeId not provided, but we can proceed with chatRoomId:', chatRoomId);
      // We'll skip the getUserTrades call for now since it's causing 422 errors
      // The chat modal can work without a specific tradeId
    }

    return {
      buyerId,
      sellerId,
      tradeId,
      chatRoomId,
      perkId: perkId || notification.data.perkId
    };
  };

  // Create release function for escrow - now works globally without route dependency
  const createReleaseFunction = (notification: Notification, resolvedTradeId?: number | string) => {
    return async () => {
      const tradeIdToUse = resolvedTradeId || notification.data?.tradeId;
      if (!tradeIdToUse || !solanaWallet?.address || !isConnected) {
        console.error('❌ [NotificationBell] Missing data for release:', {
          tradeId: tradeIdToUse,
          wallet: solanaWallet?.address,
          connected: isConnected
        });
        return;
      }

      try {
        console.log('🔍 [NotificationBell] Starting global escrow release from notification');

        // Get trade details for escrow operation
        const tradeResponse = await fetch(`${API_CONFIG.BASE_URL}/perks/trade/${tradeIdToUse}/escrow-data`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!tradeResponse.ok) {
          throw new Error('Failed to fetch trade details');
        }

        const tradeData = await tradeResponse.json();
        const trade = tradeData.data;

        console.log('🔍 [NotificationBell] Trade data for release:', {
          escrowId: trade.escrowId,
          tokenDetails: trade.tokenDetails,
          perkTokenMint: trade.perkTokenMint,
          to: trade.to,
          from: trade.from
        });

        // Execute escrow sell transaction using the hook
        await escrowOperations.executeSell(
          trade.escrowId,
          'So11111111111111111111111111111111111111112', // SOL mint
          trade.tokenDetails?.tokenAddress || trade.perkTokenMint, // Perk token mint
          trade.to, // Seller wallet (current user)
          trade.from, // Buyer wallet
          solanaWallet,
          false // skipValidation
        );

        // Update backend with release
        await releasePerk({
          tradeId: tradeIdToUse.toString(),
          sellerWallet: solanaWallet.address
        });

        console.log('✅ [NotificationBell] Global escrow release completed successfully!');

        // Refresh notifications to show updated status
        await loadNotifications();

      } catch (error) {
        console.error('❌ [NotificationBell] Global release failed:', error);
        // Show user-friendly error message
        alert('Release failed. Please try again or contact support.');
      }
    };
  };

  // Create refund function for escrow - now works globally without route dependency
  const createRefundFunction = (notification: Notification, resolvedTradeId?: number | string) => {
    return async () => {
      const tradeIdToUse = resolvedTradeId || notification.data?.tradeId;
      if (!tradeIdToUse || !solanaWallet?.address || !isConnected) {
        console.error('❌ [NotificationBell] Missing data for refund:', {
          tradeId: tradeIdToUse,
          wallet: solanaWallet?.address,
          connected: isConnected
        });
        return;
      }

      try {
        console.log('🔍 [NotificationBell] Starting global escrow refund from notification');

        // Get trade details for escrow operation
        const tradeResponse = await fetch(`${API_CONFIG.BASE_URL}/perks/trade/${tradeIdToUse}/escrow-data`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!tradeResponse.ok) {
          throw new Error('Failed to fetch trade details');
        }

        const tradeData = await tradeResponse.json();
        const trade = tradeData.data;

        // Execute escrow refund transaction using the hook
        await escrowOperations.executeRefund(
          trade.escrowId,
          'So11111111111111111111111111111111111111112', // SOL mint
          solanaWallet.address, // Buyer wallet
          solanaWallet
        );

        // Update backend with refund
        await refundPerk({
          tradeId: tradeIdToUse.toString(),
          buyerWallet: solanaWallet.address
        });

        console.log('✅ [NotificationBell] Global escrow refund completed successfully!');

        // Refresh notifications to show updated status
        await loadNotifications();

      } catch (error) {
        console.error('❌ [NotificationBell] Global refund failed:', error);
        // Show user-friendly error message
        alert('Refund failed. Please try again or contact support.');
      }
    };
  };

  // Create accept function for escrow - now works globally without route dependency
  const createAcceptFunction = (notification: Notification, resolvedTradeId?: number | string) => {
    return async () => {
      const tradeIdToUse = resolvedTradeId || notification.data?.tradeId;
      if (!tradeIdToUse || !solanaWallet?.address || !isConnected) {
        console.error('❌ [NotificationBell] Missing data for accept:', {
          tradeId: tradeIdToUse,
          wallet: solanaWallet?.address,
          connected: isConnected
        });
        return;
      }

      try {
        console.log('🔍 [NotificationBell] Starting global escrow accept from notification');

        // Execute escrow accept transaction using the utility function with tradeId
        // The function will automatically fetch trade details and calculate amounts
        const { createEscrowAcceptTransaction } = await import('../../../utils/escrow');
        const txId = await createEscrowAcceptTransaction(
          "", // escrowId - will be fetched from trade data
          "0", // purchaseTokenAmount - will be calculated from trade data
          "0", // perkTokenAmount - will be calculated from trade data
          "", // purchaseTokenMint - will be set from trade data
          "", // perkTokenMint - will be fetched from trade data
          solanaWallet.address, // Seller wallet
          solanaWallet,
          tradeIdToUse // tradeId - this will trigger automatic calculation
        );

        // Update backend with acceptance
        const acceptResponse = await fetch(`${API_CONFIG.BASE_URL}/perks/trades/${tradeIdToUse}/accept`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            txId,
            acceptedAt: new Date().toISOString()
          })
        });

        if (!acceptResponse.ok) {
          throw new Error('Failed to update backend with acceptance');
        }

        console.log('✅ [NotificationBell] Global escrow accept completed successfully!');

        // Refresh notifications to show updated status
        await loadNotifications();

        // Show success message
        alert('Escrow accepted successfully! The buyer can now release funds.');

      } catch (error) {
        console.error('❌ [NotificationBell] Global accept failed:', error);
        // Show user-friendly error message
        alert('Accept failed. Please try again or contact support.');
      }
    };
  };

  // Function to open chat modal
  const openChatModal = async (notification: Notification) => {
    console.log('🔍 [NotificationBell] Opening chat modal for notification:', notification);

    if (!user?.id) {
      console.error('❌ [NotificationBell] User not authenticated');
      return;
    }

    const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
    const userBo = userBoStr ? JSON.parse(userBoStr) : null;
    const myUserId = userBo?.id;

    // Use myUserId (numeric database ID) instead of trying to parse Privy DID
    const userIdNumber = myUserId;

    console.log('🔍 [NotificationBell] User validation:', {
      privyUserId: user.id,
      userIdNumber,
      myUserId,
      userBo: userBo ? { id: userBo.id } : null
    });

    if (!myUserId || !userIdNumber || userIdNumber <= 0) {
      console.error('❌ [NotificationBell] Invalid user ID:', {
        myUserId,
        userIdNumber,
        privyUserId: user.id
      });
      return;
    }

    // Use the unified normalization function
    let normalizedData;
    try {
      normalizedData = await normalizeNotificationData(notification, userIdNumber);
    } catch (error: any) {
      console.error('❌ [NotificationBell] Failed to normalize notification data:', error);

      // Fallback to basic notification data if normalization fails
      console.log('🔄 [NotificationBell] Using fallback notification handling');

      // Determine user roles based on notification type
      let buyerId = notification.data?.buyerId;
      let sellerId = notification.data?.sellerId;

      if (!buyerId || !sellerId) {
        // Use simple logic based on notification type
        switch (notification.type) {
          case 'perk_purchased':
          case 'escrow_created':
            buyerId = userIdNumber;
            sellerId = notification.data?.sellerId || notification.data?.receiverId;
            break;
          case 'perk_sold':
          case 'escrow_pending_acceptance':
          case 'escrow_accepted':
            sellerId = userIdNumber;
            buyerId = notification.data?.buyerId || notification.data?.senderId;
            break;
          case 'chat_message':
            // For chat messages, use the data directly or determine from sender/receiver
            buyerId = notification.data?.buyerId;
            sellerId = notification.data?.sellerId;
            if (!buyerId || !sellerId) {
              if (notification.data?.senderId === userIdNumber) {
                buyerId = userIdNumber;
                sellerId = notification.data?.receiverId;
              } else {
                buyerId = notification.data?.senderId;
                sellerId = userIdNumber;
              }
            }
            break;
          default:
            // Default fallback
            buyerId = buyerId || userIdNumber;
            sellerId = sellerId || userIdNumber;
        }
      }

      normalizedData = {
        buyerId,
        sellerId,
        tradeId: notification.data?.tradeId,
        chatRoomId: notification.data?.chatRoomId,
        perkId: notification.data?.perkId
      };

      // Generate chatRoomId if missing
      if (!normalizedData.chatRoomId && normalizedData.buyerId && normalizedData.sellerId && normalizedData.perkId) {
        normalizedData.chatRoomId = generateChatRoomId(normalizedData.buyerId, normalizedData.sellerId, normalizedData.perkId);
        console.log('🔄 [NotificationBell] Generated fallback chatRoomId:', normalizedData.chatRoomId);
      }

      // If we still don't have a chatRoomId, we can't proceed
      if (!normalizedData.chatRoomId) {
        console.error('❌ [NotificationBell] Cannot determine chatRoomId even with fallback');
        return;
      }
    }

    const { buyerId, sellerId, tradeId, chatRoomId, perkId } = normalizedData;

    if (!chatRoomId) {
      console.error('❌ [NotificationBell] Could not determine chatRoomId');
      return;
    }

    console.log('🔍 [NotificationBell] Opening chat modal with normalized data:', {
      chatRoomId,
      buyerId,
      sellerId,
      tradeId,
      perkId,
      myUserId,
      userIdNumber
    });

    // Fetch real trade details if tradeId exists
    let activeTrade = undefined;
    let tradeIdToUse = tradeId;

    if (tradeIdToUse) {
      try {
        console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);
        const { getTradeDetails } = await import('../../../axios/requests');
        const tradeResponse = await getTradeDetails(Number(tradeIdToUse));

        console.log('🔍 [NotificationBell] Trade details response:', {
          status: tradeResponse.status,
          hasData: !!tradeResponse.data,
          tradeData: tradeResponse.data,
          tradeDataId: tradeResponse.data?.id,
          tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []
        });

        if (tradeResponse.status === 200 && tradeResponse.data) {
          // Ensure we have a valid ID - use tradeIdToUse as fallback
          const validId = tradeResponse.data.id || tradeIdToUse;

          activeTrade = {
            id: validId,
            status: tradeResponse.data.status,
            tradeId: validId,
            from: tradeResponse.data.userId, // buyer
            to: tradeResponse.data.perkDetails?.userId, // seller
            createdAt: tradeResponse.data.createdAt,
            disputeStatus: tradeResponse.data.dispute?.status || 'none',
            escrowId: tradeResponse.data.escrowId,
            perkId: tradeResponse.data.perkId
          };
          console.log('✅ [NotificationBell] Trade details fetched successfully:', {
            tradeId: activeTrade.id,
            status: activeTrade.status,
            buyer: activeTrade.from,
            seller: activeTrade.to,
            escrowId: activeTrade.escrowId,
            perkId: activeTrade.perkId
          });
        } else {
          console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');
          // Fallback to normalized data
          activeTrade = {
            id: tradeIdToUse,
            status: notification.data?.status || 'pending_acceptance',
            tradeId: tradeIdToUse,
            from: buyerId,
            to: sellerId,
            perkId: perkId,
            escrowId: notification.data?.escrowId
          };
        }
      } catch (error) {
        console.error('❌ [NotificationBell] Failed to fetch trade details:', error);
        // Fallback to normalized data
        activeTrade = {
          id: tradeIdToUse,
          status: notification.data?.status || 'pending_acceptance',
          tradeId: tradeIdToUse,
          from: buyerId,
          to: sellerId,
          perkId: perkId,
          escrowId: notification.data?.escrowId
        };
        console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);
      }
    } else {
      console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');
    }

    // Use consistent modal ID based on chatRoomId to prevent multiple modals
    const modalId = `chat-modal-${chatRoomId}`;

    // Check if modal is already open - if so, update it instead of creating new one
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
      console.log('✅ [NotificationBell] Chat modal already open, updating with new state');

      // Update the existing modal with new props
      const newProps = {
        activeTrade,
        chatRoomId,
        buyerId: buyerId || myUserId,
        sellerId: sellerId || myUserId,
        onRelease: tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : () => {
          console.error('❌ [NotificationBell] Cannot release: No trade ID available');
          alert('Cannot release escrow: Trade information not available');
        },
        onRefund: tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : () => {
          console.error('❌ [NotificationBell] Cannot refund: No trade ID available');
          alert('Cannot refund escrow: Trade information not available');
        },
        onAccept: tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : () => {
          console.error('❌ [NotificationBell] Cannot accept: No trade ID available');
          alert('Cannot accept escrow: Trade information not available');
        }
      };

      // Update modal props through the state context
      const updated = updateModalProps(chatRoomId, newProps);
      if (updated) {
        console.log('✅ [NotificationBell] Successfully updated existing chat modal');
        existingModal.scrollIntoView({ behavior: 'smooth' });
        setIsOpen(false);
        return;
      } else {
        console.log('⚠️ [NotificationBell] Failed to update modal, will create new one');
      }
    }

    // Final debug before opening modal
    console.log('🎯 [NotificationBell] Final modal state:', {
      notificationType: notification.type,
      tradeIdToUse,
      hasActiveTrade: !!activeTrade,
      activeTrade,
      chatRoomId,
      buyerId: buyerId || myUserId,
      sellerId: sellerId || myUserId,
      walletConnected: isConnected,
      hasWallet: !!solanaWallet?.address
    });

    // Ensure we always have functional action functions
    const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : () => {
      console.error('❌ [NotificationBell] Cannot release: No trade ID available');
      alert('Cannot release escrow: Trade information not available');
    };

    const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : () => {
      console.error('❌ [NotificationBell] Cannot accept: No trade ID available');
      alert('Cannot accept escrow: Trade information not available');
    };

    const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : () => {
      console.error('❌ [NotificationBell] Cannot refund: No trade ID available');
      alert('Cannot refund escrow: Trade information not available');
    };

    // Open ChatModal in global modal
    openModal({
      id: modalId,
      component: React.createElement(ChatModal, {
        chatRoomId,
        buyerId: buyerId || myUserId,
        sellerId: sellerId || myUserId,
        onClose: () => closeModal(modalId),
        onRelease: safeOnRelease,
        onRefund: safeOnRefund,
        onReport: () => {
          console.log('🔍 [NotificationBell] Report function called');
          // TODO: Implement report functionality
        },
        onAccept: safeOnAccept,
        activeTrade
      }),
      closeOnBackdropClick: false,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-black/50 backdrop-blur-sm',
      modalClassName: '',
      disableScroll: true
    });

    // Close the notification dropdown
    setIsOpen(false);
  };

  // Load notifications on mount and when user changes
  useEffect(() => {
    loadNotifications();
  }, [user]);

  // Refresh notifications every 30 seconds
  useEffect(() => {
    const interval = setInterval(loadNotifications, 30000);
    return () => clearInterval(interval);
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Mark notification as read
  const handleNotificationClick = async (notification: Notification) => {
    console.log('🔍 [NotificationBell] Notification clicked:', {
      type: notification.type,
      shouldOpenChat: shouldOpenChatModal(notification),
      chatRoomId: notification.data?.chatRoomId,
      tradeId: notification.data?.tradeId,
      actionUrl: notification.actionUrl,
      notificationData: notification.data
    });

    if (!notification.isRead) {
      try {
        await markNotificationAsRead(notification.id);

        // Update the appropriate notification list
        if (isTradeNotification(notification)) {
          setTradeNotifications(prev =>
            prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
          );
          setTradeUnreadCount(prev => Math.max(0, prev - 1));
        } else {
          setSystemNotifications(prev =>
            prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
          );
          setSystemUnreadCount(prev => Math.max(0, prev - 1));
        }
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }

    // Check if this notification should open chat modal (now all trade notifications open chat modals)
    if (shouldOpenChatModal(notification)) {
      console.log('✅ [NotificationBell] Opening chat modal for notification');
      openChatModal(notification);
    } else if (notification.actionUrl) {
      // Navigate to action URL only for system notifications (moderator dashboard, etc.)
      console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);
      window.location.href = notification.actionUrl;
    } else {
      // For notifications without actionUrl, just mark as read (already handled above)
      console.log('✅ [NotificationBell] Notification marked as read');
    }
  };

  // Mark all as read for current tab
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead();

      // Update both notification lists
      setSystemNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setTradeNotifications(prev => prev.map(n => ({ ...n, isRead: true })));

      // Reset unread counts
      setSystemUnreadCount(0);
      setTradeUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  // Mark all as read for specific tab
  const handleMarkTabAsRead = async (tabType: TabType) => {
    try {
      // Note: This would ideally be a more specific API call
      // For now, we'll mark individual notifications as read
      const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;
      const unreadNotifications = notifications.filter(n => !n.isRead);

      for (const notification of unreadNotifications) {
        await markNotificationAsRead(notification.id);
      }

      // Update the appropriate list
      if (tabType === 'system') {
        setSystemNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setSystemUnreadCount(0);
      } else {
        setTradeNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setTradeUnreadCount(0);
      }
    } catch (error) {
      console.error(`Failed to mark ${tabType} notifications as read:`, error);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'dispute_created':
      case 'dispute_assigned':
      case 'dispute_resolved':
        return (
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'trade_completed':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'trade_refunded':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        );
      case 'moderator_added':
      case 'moderator_removed':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
        );
      case 'system_announcement':
        return (
          <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
            </svg>
          </div>
        );
      case 'perk_created':
        return (
          <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        );
      case 'token_created':
        return (
          <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        );
      case 'chat_message':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        );
      case 'escrow_created':
      case 'escrow_released':
      case 'escrow_release_reminder':
        return (
          <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
        );
      case 'escrow_pending_acceptance':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'escrow_accepted':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'dispute_created':
      case 'dispute_assigned':
        return (
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'dispute_resolved':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'perk_purchased':
      case 'perk_sold':
        return (
          <div className="w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
        );
      case 'trade_reported':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  // Don't render if user is not authenticated
  if (!user) return null;

  const totalUnreadCount = systemUnreadCount + tradeUnreadCount;
  const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;
  const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Real-time notification listener */}
      <RealTimeNotificationListener onNewNotification={loadNotifications} />

      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>

        {/* Combined Unread Count Badge */}
        {totalUnreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              {totalUnreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Mark all read
                </button>
              )}
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('system')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'system'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                System
                {systemUnreadCount > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                    {systemUnreadCount}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('trade')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'trade'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Trades
                {tradeUnreadCount > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                    {tradeUnreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Tab-specific Mark as Read */}
            {currentUnreadCount > 0 && (
              <div className="mt-2 flex justify-end">
                <button
                  onClick={() => handleMarkTabAsRead(activeTab)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Mark {activeTab} as read
                </button>
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2">Loading notifications...</p>
              </div>
            ) : currentNotifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <p>No {activeTab} notifications yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  {activeTab === 'system'
                    ? 'System announcements and updates will appear here'
                    : 'Trade-related notifications will appear here'
                  }
                </p>
              </div>
            ) : (
              currentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <p className={`text-sm font-medium ${
                          !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {notification.title}
                        </p>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-xs text-gray-400">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                        <div className="flex items-center space-x-2">
                          {/* Show user role for trade notifications */}
                          {activeTab === 'trade' && getUserRoleInTrade(notification) && (
                            <span className={`text-xs px-2 py-0.5 rounded ${
                              getUserRoleInTrade(notification) === 'buyer'
                                ? 'text-green-600 bg-green-100'
                                : 'text-purple-600 bg-purple-100'
                            }`}>
                              {getUserRoleInTrade(notification)}
                            </span>
                          )}
                          {/* Show trade ID for trade notifications */}
                          {activeTab === 'trade' && notification.data?.tradeId && (
                            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded">
                              Trade #{notification.data.tradeId}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {(systemNotifications.length > 0 || tradeNotifications.length > 0) && (
            <div className="px-4 py-3 border-t border-gray-200">
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to full notifications page if you have one
                  // window.location.href = '/notifications';
                }}
                className="w-full text-center text-sm text-blue-600 hover:text-blue-800"
              >
                View all {activeTab} notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
