







"use client";
import dynamic from 'next/dynamic';
import { PublicKey, Transaction, Connection, clusterApiUrl, Cluster } from "@solana/web3.js";
import { Heart, Share2 } from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";
import { buyPerk } from "../../../axios/requests";
import { useAppContext } from "../../../contexts/AppContext";
import { useWallet } from "../../../hooks/useWallet";
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import { useRouter } from 'next/navigation';
import { useCongratulationsModal } from '@/hooks/useCongratulationsModal';
import { CongratulationsModal } from '@/components/ui/CongratulationsModal';

// Remove old CongratsModal import - we'll use the new advanced one
// const CongratsModal = dynamic(() => import("../congrats-modal"), {
//   loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
// });

interface ProductCardProps {
  id: string;
  title: string;
  description: string;
  price: number;
  seller: {
    name: string;
    verified: boolean;
  };
  bonusTokens?: number;
  tokenName?: string;
  tokenAddress?: string;
  creatorWallet?: string;
  tokenInfo: any;
  onPurchase: () => Promise<void>;
  isPurchased?: boolean;
  tradeStatus?: string;
  currency?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id = "1",
  title = "Damaris - Sitting Room Chairsssss",
  price = 3,
  description = "An exceptional occasional chair with slim arm rests...",
  seller = { name: "Anas alsharif", verified: true },
  bonusTokens = 30488,
  tokenName = "Anas",
  tokenAddress = "",
  creatorWallet = "",
  tokenInfo,
  onPurchase,
  isPurchased = false,
  tradeStatus
}) => {
  const { state } = useAppContext();
  const { solanaWallet, isConnected } = useWallet();
  const { openModal, closeModal } = useGlobalModal();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Initialize congratulations modal
  const congratulationsModal = useCongratulationsModal({
    onNavigateToChat: (chatRoomId: string) => {
      router.push(`/perks-shop/${id}?openChat=1&chatRoomId=${chatRoomId}`);
    },
    onViewTransaction: (txId: string) => {
      console.log('View transaction:', txId);
      // Could navigate to transaction details page
    },
    onShareSuccess: (type, txId) => {
      console.log('Share success:', type, txId);
      // Could implement sharing functionality
    },
    onContinueShopping: () => {
      router.push('/perks-shop');
    }
  });

  const handleBuyPerk = async () => {
    if (isPurchased) {
      // Generate chatRoomId using the pattern: buyerId-sellerId-perkId
      const userBo = typeof state.userBo === "string" ? JSON.parse(state.userBo) : state.userBo;
      if (userBo?.id && tokenInfo?.sellerId) {
        const chatRoomId = `${userBo.id}-${tokenInfo.sellerId}-${id}`;
        console.log('🔍 [ProductCard] Generated chatRoomId:', chatRoomId);
        router.push(`?openChat=1&chatRoomId=${chatRoomId}`);
      } else {
        console.error('Missing user or seller information for chat:', { userId: userBo?.id, sellerId: tokenInfo?.sellerId });
      }
      return;
    }

    // Prevent multiple rapid clicks
    if (loading) {
      console.log("Purchase already in progress, ignoring click");
      return;
    }

    try {
      setLoading(true);
      const userBo = typeof state.userBo === "string"
        ? JSON.parse(state.userBo)
        : state.userBo;

      if (!userBo?.id) {
        throw new Error("User not authenticated");
      }

      if (!isConnected || !solanaWallet?.address) {
        throw new Error("Wallet not connected");
      }

      // Trigger parent purchase handler (which handles the full escrow flow)
      await onPurchase();

      // Show our new advanced congratulations modal
      congratulationsModal.showPurchaseSuccess(
        'tx_' + Date.now(), // Generate a temporary transaction ID
        `${price} SOL`,
        undefined // No chat room ID for product card purchases
      );
    } catch (error: any) {
      console.error("Purchase failed:", error);

      // Show user-friendly error message
      if (error.message?.includes("Transaction already in progress")) {
        alert("A transaction is already in progress. Please wait for it to complete.");
      } else if (error.message?.includes("Transaction already processed")) {
        alert("This transaction has already been processed. Please refresh the page.");
      } else {
        alert(`Purchase failed: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-[580px] m-auto bg-white rounded-xl relative">
      <div className="p-4">
        {/* Seller Info */}
        <div className="flex items-center mb-2">
          <Image
            src="/images/avatar-placeholder.png"
            alt={seller.name}
            width={39}
            height={39}
            className="rounded-full mr-2 bg-black w-[39px] h-[39px] min-w-[39px] min-h-[39px] max-w-[39px] max-h-[39px]"
          />
          <span className="font-[600] text-[24px] leading-[100%] tracking-[0.02em] font-['IBM_Plex_Sans']">
            {seller.name}
          </span>
          {seller.verified && (
            <Image
              src="/icons/galchka.svg"
              alt="Verified"
              width={20}
              height={20}
              className="ml-2 h-auto"
            />
          )}
        </div>

        {/* Product Title */}
        <h2 className="font-[600] text-[66px] leading-[100%] tracking-[-0.01em] font-['IBM_Plex_Sans'] mb-1 break-words">
          {title}
        </h2>

        {/* Description */}
        <p className="font-[600] text-[18px] leading-[140%] mb-6 text-[rgba(17,17,17,0.7)] font-['IBM_Plex_Sans'] tracking-[0]">
          {description}
        </p>

        {/* Price */}
        <div className="p-4 mb-8 rounded-[24px] border border-[#1111111A] shadow-[0_4px_80px_0_rgba(0,0,0,0.05)]">
          <p className="text-gray-800 text-lg mb-1 font-[600] font-['IBM_Plex_Sans'] tracking-[0]">
            Price
          </p>
          <div className="flex items-center justify-between mb-1">
            <p className="font-[600] text-[40px] leading-[120%] tracking-[-0.01em] text-[#111111] font-['IBM_Plex_Sans']">
              {price} SOL
            </p>

            {/* Buy Button */}
            <button
              className="w-[310px] h-[70px] rounded-[100px] bg-[#FF6600] text-white font-[600] text-[18px] leading-[100%] py-5 px-10 flex items-center justify-center border border-[#FF6600] hover:bg-[#E55A00] hover:border-[#E55A00] transition-all duration-200 font-['IBM_Plex_Sans'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleBuyPerk}
              disabled={loading}
              style={{
                pointerEvents: loading ? 'none' : 'auto' // Prevent any click events when loading
              }}
            >
              {loading ? "Processing..." :
               isPurchased ? "Open Chat" :
               "Buy Perk"}
              <span className="ml-2">→</span>
            </button>
          </div>

          {bonusTokens != 0 && (
            <div>
              <p className="font-[600] text-[20px] leading-[100%] tracking-[0] text-[#22C55E] font-['IBM_Plex_Sans'] align-middle mb-1">
                Bonus
              </p>
              <p className="font-[600] text-[20px] leading-[100%] tracking-[0] text-[#22C55E] font-['IBM_Plex_Sans'] align-middle">
                You get {bonusTokens.toLocaleString()} {tokenName} Token
                airdropped to you
              </p>
            </div>
          )}

          <div className="flex items-center justify-between mt-6 px-5 py-4">
            <button className="flex items-center">
              <Heart size={24} className="mr-2" strokeWidth={1.5} />
              <span className="font-[600] text-[18px] leading-[140%] tracking-[0] font-['IBM_Plex_Sans']">
                Add to Favorites
              </span>
            </button>
            <button className="flex items-center">
              <Share2 size={24} className="mr-2" strokeWidth={1.5} />
              <span className="font-[500] text-[16px] font-['IBM_Plex_Sans']">
                Share
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Advanced Congratulations Modal */}
      <CongratulationsModal {...congratulationsModal.getModalProps()} />
    </div>
  );
};

export default ProductCard;