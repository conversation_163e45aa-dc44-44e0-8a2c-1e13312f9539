"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId,\n            hasTradeId: !!notification.data.tradeId,\n            hasChatRoomId: !!notification.data.chatRoomId,\n            hasPerkId: !!notification.data.perkId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Use getUserTrades to find trades for current user and filter by perkId\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(myUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        // Find active trade for this perk\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(perkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            // Get the most recent trade\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeIdToUse = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    activeTrade = {\n                        id: tradeResponse.data.id,\n                        status: tradeResponse.data.status,\n                        tradeId: tradeResponse.data.id,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to notification data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: notification.data.status || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: notification.data.perkId,\n                        escrowId: notification.data.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, don't close it, just focus it\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, focusing existing modal');\n            existingModal.scrollIntoView({\n                behavior: 'smooth'\n            });\n            return;\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId\n        });\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: createReleaseFunction(notification, tradeIdToUse),\n                onRefund: createRefundFunction(notification, tradeIdToUse),\n                onReport: ()=>{},\n                onAccept: createAcceptFunction(notification, tradeIdToUse),\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 704,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 721,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 731,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 729,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 737,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 745,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 754,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 753,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 764,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 771,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 780,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 779,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 788,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 796,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 805,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 814,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 822,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 852,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 865,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 855,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 875,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 936,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1013,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1012,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 873,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 850,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9cb6f0cec9b4\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWNiNmYwY2VjOWI0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});