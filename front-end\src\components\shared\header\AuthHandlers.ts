
import { claimAirDrop, loginPrivyUser } from "@/axios/requests";
import { APP_CONFIG } from "@/config/environment";
import { showErrorToast, showSuccessToast, TOAST_MESSAGES, createAPIError } from "@/utils/errorHandling";

// Function to check if the token is expired
const isTokenExpired = (token: string): boolean => {
  try {
    // JWT tokens consist of three parts: header.payload.signature
    const payload = token.split('.')[1];
    // Decode the base64 payload
    const decodedPayload = JSON.parse(atob(payload));

    const remainingTimeMs = decodedPayload.exp * 1000 - Date.now();

    // Check if the expiration time is in the past
    return remainingTimeMs < 0;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    // If there's an error parsing the token, consider it expired
    return true;
  }
};

let lastToast = "";

const safeToast = (message: string) => {
  if (lastToast === message) return;
  lastToast = message;
  showSuccessToast(message);
};

let loginInProgress = false;
export const handleLogin = async (
  user: any,
  loginAPI: (data: any, token: string) => void,
  logoutAPI: () => void,
  isManualLogin: boolean = false,
  showAuthenticatorModal?: (userId: string | number) => void,
  setIsAuthenticating?: (loading: boolean) => void
) => {
  if (user != null) {

    if (loginInProgress) return;
    loginInProgress = true;
    setIsAuthenticating?.(true);

    try {
      // Check if this is a manual login by looking at the timestamp of the user object
      // If the user object was just created (within the last 2 seconds), it's likely a manual login
      const isLikelyManualLogin = isManualLogin || (user._lastLoginAt && (Date.now() - user._lastLoginAt < 2000));

      const params = new URLSearchParams(window.location.search);
      const referral = params.get("referral");

      const res = await loginPrivyUser({
        privyId: user.id,
        email: user.email?.address,
        privywallet: user.wallet?.address,
        referralBy: referral,
      });

      if (res.status !== 200) {
        const errorMessage = res.data?.message || res.message || TOAST_MESSAGES.AUTH.LOGIN_FAILED;
        showErrorToast(createAPIError(errorMessage, res.status));
      } else {

        //console.log("is manual login", isLikelyManualLogin);
        // Check if 2FA is required and this is a manual login
        if (res.data && res.data.twoFactorStatus && isLikelyManualLogin && showAuthenticatorModal) {
          //console.log('Manual login with 2FA - showing authenticator modal');
          showAuthenticatorModal(res.data.id);
          return;
        }

        // Otherwise proceed with normal login
        loginAPI(res.data, res.token);
        safeToast(TOAST_MESSAGES.AUTH.LOGIN_SUCCESS);

        const hashParams = new URLSearchParams(window.location.hash.slice(1)); // remove the '#' character
        const rawHash = hashParams.get("c");
        const isLogin = hashParams.get("i");
        if (isLogin == "1") {
          if (rawHash != "") {
            const dataIs = {
              code: rawHash,
              wallet: user.wallet?.address,
              userId: res.data.id,
            };
            // showSuccessToast("Checking for AirDrop, Please wait!");
            const data = await claimAirDrop(dataIs);
            if (data.status == 201) {
              showSuccessToast(data.message);
              window.history.replaceState(null, "", window.location.pathname);
            } else {
              showErrorToast(data.message);
              window.history.replaceState(null, "", window.location.pathname);
            }
          }
        }
        // Check if the current page is not the root page and refresh
        if (window.location.pathname !== '/') {
          window.location.href = '/';  // Redirect to the root path
        }
      }
    } catch (err) {
      console.error('Login error:', err);
    } finally {
      loginInProgress = false;
      setIsAuthenticating?.(false);
    }
  } else {
    logoutAPI();
  }
};

// Function to check token expiration and prompt for re-login if needed
export const checkTokenExpiration = (token: string | null, logoutAPI: () => void, logout: () => void) => {
  if (!token) return;

  if (isTokenExpired(token)) {
    console.log('Token has expired - logging out');
    showErrorToast(TOAST_MESSAGES.AUTH.SESSION_EXPIRED);
    logoutAPI();
    logout();
  }
};

export const checkUserTokens = (setHideCreate: (value: boolean) => void) => {
  setTimeout(() => {
    if (typeof window !== "undefined") {
      const hasSession = localStorage.getItem("userSession");
      const userBoStr = localStorage.getItem("userBo");
      if (hasSession && userBoStr) {
        try {
          const userBo = JSON.parse(userBoStr);
          const allowedTokens = Number(
            APP_CONFIG.MAX_COINS_PER_USER ?? 1
          );
          setHideCreate(userBo.usertokens?.length >= allowedTokens);
        } catch (e) {
          setHideCreate(false);
        }
      } else {
        setHideCreate(false);
      }
    } else {
      setHideCreate(false);
    }
  }, 1000);
};

export const handleClickOutside = (
  event: MouseEvent,
  menuRef: React.RefObject<HTMLDivElement | null>,
  dropdownRef: React.RefObject<HTMLDivElement | null>,
  setMobileMenuOpen: (value: boolean) => void,
  setDropdownOpen: (value: boolean) => void
) => {
  const target = event.target as HTMLElement;
  if (
    target.closest(
      'button[aria-label="Close menu"], button[aria-label="Open menu"]'
    )
  ) {
    return;
  }
  if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
    setMobileMenuOpen(false);
  }
  if (
    dropdownRef.current &&
    !dropdownRef.current.contains(event.target as Node)
  ) {
    setDropdownOpen(false);
  }
};