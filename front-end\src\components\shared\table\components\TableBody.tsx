import { motion, AnimatePresence } from 'framer-motion';
import React from 'react';

import type { TableColumn, TableRow } from '../types';

interface TableBodyProps<T = TableRow> {
  columns: TableColumn<T>[];
  data: T[];
  selectable?: boolean;
  selectedRows?: Set<string | number>;
  onRowSelect?: (rowId: string | number) => void;
  onRowClick?: (row: T, index: number) => void;
}

const TableBody = <T extends TableRow>({
  columns,
  data,
  selectable = false,
  selectedRows = new Set(),
  onRowSelect,
  onRowClick,
}: TableBodyProps<T>) => {
  const getAlignmentClass = (align?: string) => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  const renderCellContent = (
    column: TableColumn<T>,
    row: T,
    rowIndex: number
  ) => {
    if (column.render) {
      return column.render(row[column.key], row, rowIndex);
    }

    const value = row[column.key];

    // Handle HTML content with <br/> tags
    if (typeof value === 'string' && value.includes('<br/>')) {
      return value.split('<br/>').map((part, i, arr) => (
        <React.Fragment key={i}>
          {part}
          {i < arr.length - 1 && <br />}
        </React.Fragment>
      ));
    }

    // Handle null/undefined values
    if (value === null || value === undefined) {
      return <span className="text-gray-400">—</span>;
    }

    return value;
  };

  return (
    <tbody className="bg-white divide-y divide-gray-200">
      <AnimatePresence mode="wait">
        {data.map((row, rowIndex) => {
          const isSelected = selectedRows.has(row.id);
          const isClickable = Boolean(onRowClick);

          return (
            <motion.tr
              key={row.id}
              className={`
                transition-colors duration-200
                ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}
                ${isClickable ? 'cursor-pointer' : ''}
              `}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{
                duration: 0.2,
                delay: rowIndex * 0.02,
              }}
              onClick={() => onRowClick?.(row, rowIndex)}
              whileHover={
                isClickable
                  ? {
                      backgroundColor: isSelected ? '#dbeafe' : '#f9fafb',
                      scale: 1.001,
                    }
                  : undefined
              }
            >
              {/* Selection column */}
              {selectable && (
                <motion.td
                  className="w-12 px-6 py-4"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: rowIndex * 0.02 + 0.1 }}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => onRowSelect?.(row.id)}
                      onClick={(e) => e.stopPropagation()} // Prevent row click
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                      aria-label={`Select row ${rowIndex + 1}`}
                    />
                  </div>
                </motion.td>
              )}

              {/* Data columns */}
              {columns.map((column, colIndex) => (
                <motion.td
                  key={`${row.id}-${column.key}`}
                  className={`px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 ${getAlignmentClass(
                    column.align
                  )}`}
                  style={{ width: column.width }}
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{
                    delay: rowIndex * 0.02 + colIndex * 0.01 + 0.1,
                    duration: 0.2,
                  }}
                >
                  <div
                    className={`${
                      column.align === 'center'
                        ? 'flex justify-center'
                        : column.align === 'right'
                        ? 'flex justify-end'
                        : ''
                    }`}
                  >
                    {renderCellContent(column, row, rowIndex)}
                  </div>
                </motion.td>
              ))}
            </motion.tr>
          );
        })}
      </AnimatePresence>
    </tbody>
  );
};

export { TableBody };
