"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bigint-buffer";
exports.ids = ["vendor-chunks/bigint-buffer"];
exports.modules = {

/***/ "(ssr)/./node_modules/bigint-buffer/dist/node.js":
/*!*************************************************!*\
  !*** ./node_modules/bigint-buffer/dist/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nlet converter;\n{\n    try {\n        converter = __webpack_require__(/*! bindings */ \"(ssr)/./node_modules/bindings/bindings.js\")('bigint_buffer');\n    }\n    catch (e) {\n        console.warn('bigint: Failed to load bindings, pure JS will be used (try npm run rebuild?)');\n    }\n}\n/**\n * Convert a little-endian buffer into a BigInt.\n * @param buf The little-endian buffer to convert\n * @returns A BigInt with the little-endian representation of buf.\n */\nfunction toBigIntLE(buf) {\n    if (converter === undefined) {\n        const reversed = Buffer.from(buf);\n        reversed.reverse();\n        const hex = reversed.toString('hex');\n        if (hex.length === 0) {\n            return BigInt(0);\n        }\n        return BigInt(`0x${hex}`);\n    }\n    return converter.toBigInt(buf, false);\n}\nexports.toBigIntLE = toBigIntLE;\n/**\n * Convert a big-endian buffer into a BigInt\n * @param buf The big-endian buffer to convert.\n * @returns A BigInt with the big-endian representation of buf.\n */\nfunction toBigIntBE(buf) {\n    if (converter === undefined) {\n        const hex = buf.toString('hex');\n        if (hex.length === 0) {\n            return BigInt(0);\n        }\n        return BigInt(`0x${hex}`);\n    }\n    return converter.toBigInt(buf, true);\n}\nexports.toBigIntBE = toBigIntBE;\n/**\n * Convert a BigInt to a little-endian buffer.\n * @param num   The BigInt to convert.\n * @param width The number of bytes that the resulting buffer should be.\n * @returns A little-endian buffer representation of num.\n */\nfunction toBufferLE(num, width) {\n    if (converter === undefined) {\n        const hex = num.toString(16);\n        const buffer = Buffer.from(hex.padStart(width * 2, '0').slice(0, width * 2), 'hex');\n        buffer.reverse();\n        return buffer;\n    }\n    // Allocation is done here, since it is slower using napi in C\n    return converter.fromBigInt(num, Buffer.allocUnsafe(width), false);\n}\nexports.toBufferLE = toBufferLE;\n/**\n * Convert a BigInt to a big-endian buffer.\n * @param num   The BigInt to convert.\n * @param width The number of bytes that the resulting buffer should be.\n * @returns A big-endian buffer representation of num.\n */\nfunction toBufferBE(num, width) {\n    if (converter === undefined) {\n        const hex = num.toString(16);\n        return Buffer.from(hex.padStart(width * 2, '0').slice(0, width * 2), 'hex');\n    }\n    return converter.fromBigInt(num, Buffer.allocUnsafe(width), true);\n}\nexports.toBufferBE = toBufferBE;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bigint-buffer/dist/node.js\n");

/***/ })

};
;