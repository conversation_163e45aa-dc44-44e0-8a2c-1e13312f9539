"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx":
/*!************************************************!*\
  !*** ./src/contexts/ChatModalStateContext.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatModalStateProvider: () => (/* binding */ ChatModalStateProvider),\n/* harmony export */   useChatModalState: () => (/* binding */ useChatModalState)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _SocketProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SocketProvider */ \"(app-pages-browser)/./src/contexts/SocketProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatModalStateProvider,useChatModalState auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst ChatModalStateContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatModalStateProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [chatModalStates, setChatModalStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const { updateModal, getModalById } = (0,_GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal)();\n    const socket = (0,_SocketProvider__WEBPACK_IMPORTED_MODULE_3__.useSocket)();\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // Keep ref in sync with state\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ChatModalStateProvider.useEffect\": ()=>{\n            stateRef.current = chatModalStates;\n        }\n    }[\"ChatModalStateProvider.useEffect\"], [\n        chatModalStates\n    ]);\n    const getChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[getChatModalState]\": (chatRoomId)=>{\n            return stateRef.current.get(chatRoomId);\n        }\n    }[\"ChatModalStateProvider.useCallback[getChatModalState]\"], []);\n    const updateChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateChatModalState]\": (chatRoomId, updates)=>{\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating state for chatRoom \".concat(chatRoomId, \":\"), updates);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[updateChatModalState]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    const currentState = newMap.get(chatRoomId);\n                    if (currentState) {\n                        const updatedState = {\n                            ...currentState,\n                            ...updates,\n                            lastUpdated: Date.now()\n                        };\n                        newMap.set(chatRoomId, updatedState);\n                        console.log(\"✅ [ChatModalStateContext] Updated state for \".concat(chatRoomId, \":\"), updatedState);\n                    } else {\n                        console.log(\"⚠️ [ChatModalStateContext] No state found for chatRoom \".concat(chatRoomId));\n                    }\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"], []);\n    const registerChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[registerChatModal]\": (chatRoomId, initialState)=>{\n            console.log(\"\\uD83D\\uDCDD [ChatModalStateContext] Registering chatRoom \".concat(chatRoomId, \":\"), initialState);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[registerChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.set(chatRoomId, {\n                        ...initialState,\n                        lastUpdated: Date.now()\n                    });\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[registerChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[registerChatModal]\"], []);\n    const unregisterChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (chatRoomId)=>{\n            console.log(\"\\uD83D\\uDDD1️ [ChatModalStateContext] Unregistering chatRoom \".concat(chatRoomId));\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.delete(chatRoomId);\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"], []);\n    const updateModalProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateModalProps]\": (chatRoomId, newProps)=>{\n            const modalId = \"chat-modal-\".concat(chatRoomId);\n            const existingModal = getModalById(modalId);\n            if (!existingModal) {\n                console.log(\"⚠️ [ChatModalStateContext] No modal found with id \".concat(modalId));\n                return false;\n            }\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating modal props for \".concat(modalId, \":\"), newProps);\n            // Update the modal component with new props\n            const updatedComponent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(existingModal.component, {\n                ...existingModal.component.props || {},\n                ...newProps,\n                key: \"\".concat(modalId, \"-\").concat(Date.now()) // Force re-render with new props\n            });\n            return updateModal(modalId, {\n                component: updatedComponent,\n                updateProps: newProps\n            });\n        }\n    }[\"ChatModalStateProvider.useCallback[updateModalProps]\"], [\n        getModalById,\n        updateModal\n    ]);\n    const syncTradeStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[syncTradeStatusUpdate]\": (tradeId, status)=>{\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Syncing trade status update: \".concat(tradeId, \" -> \").concat(status));\n            // Find all chat modals that have this trade and update them\n            stateRef.current.forEach({\n                \"ChatModalStateProvider.useCallback[syncTradeStatusUpdate]\": (state, chatRoomId)=>{\n                    if (state.activeTrade && (state.activeTrade.id === tradeId || state.activeTrade.tradeId === tradeId)) {\n                        console.log(\"\\uD83D\\uDCE1 [ChatModalStateContext] Updating chatRoom \".concat(chatRoomId, \" with new status: \").concat(status));\n                        // Update the state\n                        updateChatModalState(chatRoomId, {\n                            currentTradeStatus: status,\n                            activeTrade: {\n                                ...state.activeTrade,\n                                status\n                            }\n                        });\n                        // Update the modal props to trigger re-render\n                        const modalId = \"chat-modal-\".concat(chatRoomId);\n                        const existingModal = getModalById(modalId);\n                        if (existingModal) {\n                            const updatedProps = {\n                                activeTrade: {\n                                    ...state.activeTrade,\n                                    status\n                                },\n                                currentTradeStatus: status\n                            };\n                            updateModalProps(chatRoomId, updatedProps);\n                        }\n                    }\n                }\n            }[\"ChatModalStateProvider.useCallback[syncTradeStatusUpdate]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[syncTradeStatusUpdate]\"], [\n        updateChatModalState,\n        getModalById,\n        updateModalProps\n    ]);\n    // Listen for trade status updates from socket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModalStateProvider.useEffect\": ()=>{\n            if (!socket) return;\n            const handleTradeStatusUpdate = {\n                \"ChatModalStateProvider.useEffect.handleTradeStatusUpdate\": (data)=>{\n                    console.log('🔔 [ChatModalStateContext] Received trade status update from socket:', data);\n                    syncTradeStatusUpdate(data.tradeId, data.status);\n                }\n            }[\"ChatModalStateProvider.useEffect.handleTradeStatusUpdate\"];\n            const handleEscrowAccepted = {\n                \"ChatModalStateProvider.useEffect.handleEscrowAccepted\": (data)=>{\n                    console.log('🔔 [ChatModalStateContext] Received escrow accepted from socket:', data);\n                    syncTradeStatusUpdate(data.tradeId, 'escrowed');\n                }\n            }[\"ChatModalStateProvider.useEffect.handleEscrowAccepted\"];\n            const handlePerkReleased = {\n                \"ChatModalStateProvider.useEffect.handlePerkReleased\": (data)=>{\n                    console.log('🔔 [ChatModalStateContext] Received perk released from socket:', data);\n                    syncTradeStatusUpdate(data.tradeId, 'completed');\n                }\n            }[\"ChatModalStateProvider.useEffect.handlePerkReleased\"];\n            const handleRefundProcessed = {\n                \"ChatModalStateProvider.useEffect.handleRefundProcessed\": (data)=>{\n                    console.log('🔔 [ChatModalStateContext] Received refund processed from socket:', data);\n                    syncTradeStatusUpdate(data.tradeId, 'refunded');\n                }\n            }[\"ChatModalStateProvider.useEffect.handleRefundProcessed\"];\n            socket.on('tradeStatus', handleTradeStatusUpdate);\n            socket.on('escrowAccepted', handleEscrowAccepted);\n            socket.on('perkReleased', handlePerkReleased);\n            socket.on('refundProcessed', handleRefundProcessed);\n            return ({\n                \"ChatModalStateProvider.useEffect\": ()=>{\n                    socket.off('tradeStatus', handleTradeStatusUpdate);\n                    socket.off('escrowAccepted', handleEscrowAccepted);\n                    socket.off('perkReleased', handlePerkReleased);\n                    socket.off('refundProcessed', handleRefundProcessed);\n                }\n            })[\"ChatModalStateProvider.useEffect\"];\n        }\n    }[\"ChatModalStateProvider.useEffect\"], [\n        socket,\n        syncTradeStatusUpdate\n    ]);\n    const value = {\n        getChatModalState,\n        updateChatModalState,\n        registerChatModal,\n        unregisterChatModal,\n        updateModalProps,\n        syncTradeStatusUpdate\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatModalStateContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\ChatModalStateContext.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModalStateProvider, \"d+lhABC35WwsI9Lwk5XjdmB+nFU=\", false, function() {\n    return [\n        _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal,\n        _SocketProvider__WEBPACK_IMPORTED_MODULE_3__.useSocket\n    ];\n});\n_c = ChatModalStateProvider;\nconst useChatModalState = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatModalStateContext);\n    if (!context) {\n        throw new Error('useChatModalState must be used within a ChatModalStateProvider');\n    }\n    return context;\n};\n_s1(useChatModalState, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatModalStateProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9DaGF0TW9kYWxTdGF0ZUNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVtRztBQUM3QztBQUNUO0FBbUI3QyxNQUFNUyxzQ0FBd0JSLG9EQUFhQSxDQUFtQztBQUV2RSxNQUFNUyx5QkFBa0U7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQzFGLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR1YsK0NBQVFBLENBQThCLElBQUlXO0lBQ3hGLE1BQU0sRUFBRUMsV0FBVyxFQUFFQyxZQUFZLEVBQUUsR0FBR1QsbUVBQWNBO0lBQ3BELE1BQU1VLFNBQVNULDBEQUFTQTtJQUN4QixNQUFNVSxXQUFXYiw2Q0FBTUEsQ0FBOEIsSUFBSVM7SUFFekQsOEJBQThCO0lBQzlCZCxzREFBZTs0Q0FBQztZQUNka0IsU0FBU0MsT0FBTyxHQUFHUDtRQUNyQjsyQ0FBRztRQUFDQTtLQUFnQjtJQUVwQixNQUFNUSxvQkFBb0JoQixrREFBV0E7aUVBQUMsQ0FBQ2lCO1lBQ3JDLE9BQU9ILFNBQVNDLE9BQU8sQ0FBQ0csR0FBRyxDQUFDRDtRQUM5QjtnRUFBRyxFQUFFO0lBRUwsTUFBTUUsdUJBQXVCbkIsa0RBQVdBO29FQUFDLENBQUNpQixZQUFvQkc7WUFDNURDLFFBQVFDLEdBQUcsQ0FBQyxvRUFBcUUsT0FBWEwsWUFBVyxNQUFJRztZQUVyRlg7NEVBQW1CYyxDQUFBQTtvQkFDakIsTUFBTUMsU0FBUyxJQUFJZCxJQUFJYTtvQkFDdkIsTUFBTUUsZUFBZUQsT0FBT04sR0FBRyxDQUFDRDtvQkFFaEMsSUFBSVEsY0FBYzt3QkFDaEIsTUFBTUMsZUFBZTs0QkFDbkIsR0FBR0QsWUFBWTs0QkFDZixHQUFHTCxPQUFPOzRCQUNWTyxhQUFhQyxLQUFLQyxHQUFHO3dCQUN2Qjt3QkFDQUwsT0FBT00sR0FBRyxDQUFDYixZQUFZUzt3QkFDdkJMLFFBQVFDLEdBQUcsQ0FBQywrQ0FBMEQsT0FBWEwsWUFBVyxNQUFJUztvQkFDNUUsT0FBTzt3QkFDTEwsUUFBUUMsR0FBRyxDQUFDLDBEQUFxRSxPQUFYTDtvQkFDeEU7b0JBRUEsT0FBT087Z0JBQ1Q7O1FBQ0Y7bUVBQUcsRUFBRTtJQUVMLE1BQU1PLG9CQUFvQi9CLGtEQUFXQTtpRUFBQyxDQUFDaUIsWUFBb0JlO1lBQ3pEWCxRQUFRQyxHQUFHLENBQUMsNkRBQThELE9BQVhMLFlBQVcsTUFBSWU7WUFDOUV2Qjt5RUFBbUJjLENBQUFBO29CQUNqQixNQUFNQyxTQUFTLElBQUlkLElBQUlhO29CQUN2QkMsT0FBT00sR0FBRyxDQUFDYixZQUFZO3dCQUFFLEdBQUdlLFlBQVk7d0JBQUVMLGFBQWFDLEtBQUtDLEdBQUc7b0JBQUc7b0JBQ2xFLE9BQU9MO2dCQUNUOztRQUNGO2dFQUFHLEVBQUU7SUFFTCxNQUFNUyxzQkFBc0JqQyxrREFBV0E7bUVBQUMsQ0FBQ2lCO1lBQ3ZDSSxRQUFRQyxHQUFHLENBQUMsZ0VBQWlFLE9BQVhMO1lBQ2xFUjsyRUFBbUJjLENBQUFBO29CQUNqQixNQUFNQyxTQUFTLElBQUlkLElBQUlhO29CQUN2QkMsT0FBT1UsTUFBTSxDQUFDakI7b0JBQ2QsT0FBT087Z0JBQ1Q7O1FBQ0Y7a0VBQUcsRUFBRTtJQUVMLE1BQU1XLG1CQUFtQm5DLGtEQUFXQTtnRUFBQyxDQUFDaUIsWUFBb0JtQjtZQUN4RCxNQUFNQyxVQUFVLGNBQXlCLE9BQVhwQjtZQUM5QixNQUFNcUIsZ0JBQWdCMUIsYUFBYXlCO1lBRW5DLElBQUksQ0FBQ0MsZUFBZTtnQkFDbEJqQixRQUFRQyxHQUFHLENBQUMscURBQTZELE9BQVJlO2dCQUNqRSxPQUFPO1lBQ1Q7WUFFQWhCLFFBQVFDLEdBQUcsQ0FBQyxpRUFBK0QsT0FBUmUsU0FBUSxNQUFJRDtZQUUvRSw0Q0FBNEM7WUFDNUMsTUFBTUcsaUNBQW1CM0MseURBQWtCLENBQ3pDMEMsY0FBY0csU0FBUyxFQUN2QjtnQkFDRSxHQUFJLGNBQWVBLFNBQVMsQ0FBd0JDLEtBQUssSUFBSSxDQUFDLENBQUM7Z0JBQy9ELEdBQUdOLFFBQVE7Z0JBQ1hPLEtBQUssR0FBY2YsT0FBWFMsU0FBUSxLQUFjLE9BQVhULEtBQUtDLEdBQUcsSUFBSyxpQ0FBaUM7WUFDbkU7WUFHRixPQUFPbEIsWUFBWTBCLFNBQVM7Z0JBQzFCSSxXQUFXRjtnQkFDWEssYUFBYVI7WUFDZjtRQUNGOytEQUFHO1FBQUN4QjtRQUFjRDtLQUFZO0lBRTlCLE1BQU1rQyx3QkFBd0I3QyxrREFBV0E7cUVBQUMsQ0FBQzhDLFNBQTBCQztZQUNuRTFCLFFBQVFDLEdBQUcsQ0FBQyxxRUFBeUV5QixPQUFkRCxTQUFRLFFBQWEsT0FBUEM7WUFFckYsNERBQTREO1lBQzVEakMsU0FBU0MsT0FBTyxDQUFDaUMsT0FBTzs2RUFBQyxDQUFDQyxPQUFPaEM7b0JBQy9CLElBQUlnQyxNQUFNQyxXQUFXLElBQ2hCRCxDQUFBQSxNQUFNQyxXQUFXLENBQUNDLEVBQUUsS0FBS0wsV0FBV0csTUFBTUMsV0FBVyxDQUFDSixPQUFPLEtBQUtBLE9BQU0sR0FBSTt3QkFDL0V6QixRQUFRQyxHQUFHLENBQUMsMERBQStFeUIsT0FBL0I5QixZQUFXLHNCQUEyQixPQUFQOEI7d0JBRTNGLG1CQUFtQjt3QkFDbkI1QixxQkFBcUJGLFlBQVk7NEJBQy9CbUMsb0JBQW9CTDs0QkFDcEJHLGFBQWE7Z0NBQUUsR0FBR0QsTUFBTUMsV0FBVztnQ0FBRUg7NEJBQU87d0JBQzlDO3dCQUVBLDhDQUE4Qzt3QkFDOUMsTUFBTVYsVUFBVSxjQUF5QixPQUFYcEI7d0JBQzlCLE1BQU1xQixnQkFBZ0IxQixhQUFheUI7d0JBQ25DLElBQUlDLGVBQWU7NEJBQ2pCLE1BQU1lLGVBQWU7Z0NBQ25CSCxhQUFhO29DQUFFLEdBQUdELE1BQU1DLFdBQVc7b0NBQUVIO2dDQUFPO2dDQUM1Q0ssb0JBQW9CTDs0QkFDdEI7NEJBQ0FaLGlCQUFpQmxCLFlBQVlvQzt3QkFDL0I7b0JBQ0Y7Z0JBQ0Y7O1FBQ0Y7b0VBQUc7UUFBQ2xDO1FBQXNCUDtRQUFjdUI7S0FBaUI7SUFFekQsOENBQThDO0lBQzlDakMsZ0RBQVNBOzRDQUFDO1lBQ1IsSUFBSSxDQUFDVyxRQUFRO1lBRWIsTUFBTXlDOzRFQUEwQixDQUFDQztvQkFDL0JsQyxRQUFRQyxHQUFHLENBQUMsd0VBQXdFaUM7b0JBQ3BGVixzQkFBc0JVLEtBQUtULE9BQU8sRUFBRVMsS0FBS1IsTUFBTTtnQkFDakQ7O1lBRUEsTUFBTVM7eUVBQXVCLENBQUNEO29CQUM1QmxDLFFBQVFDLEdBQUcsQ0FBQyxvRUFBb0VpQztvQkFDaEZWLHNCQUFzQlUsS0FBS1QsT0FBTyxFQUFFO2dCQUN0Qzs7WUFFQSxNQUFNVzt1RUFBcUIsQ0FBQ0Y7b0JBQzFCbEMsUUFBUUMsR0FBRyxDQUFDLGtFQUFrRWlDO29CQUM5RVYsc0JBQXNCVSxLQUFLVCxPQUFPLEVBQUU7Z0JBQ3RDOztZQUVBLE1BQU1ZOzBFQUF3QixDQUFDSDtvQkFDN0JsQyxRQUFRQyxHQUFHLENBQUMscUVBQXFFaUM7b0JBQ2pGVixzQkFBc0JVLEtBQUtULE9BQU8sRUFBRTtnQkFDdEM7O1lBRUFqQyxPQUFPOEMsRUFBRSxDQUFDLGVBQWVMO1lBQ3pCekMsT0FBTzhDLEVBQUUsQ0FBQyxrQkFBa0JIO1lBQzVCM0MsT0FBTzhDLEVBQUUsQ0FBQyxnQkFBZ0JGO1lBQzFCNUMsT0FBTzhDLEVBQUUsQ0FBQyxtQkFBbUJEO1lBRTdCO29EQUFPO29CQUNMN0MsT0FBTytDLEdBQUcsQ0FBQyxlQUFlTjtvQkFDMUJ6QyxPQUFPK0MsR0FBRyxDQUFDLGtCQUFrQko7b0JBQzdCM0MsT0FBTytDLEdBQUcsQ0FBQyxnQkFBZ0JIO29CQUMzQjVDLE9BQU8rQyxHQUFHLENBQUMsbUJBQW1CRjtnQkFDaEM7O1FBQ0Y7MkNBQUc7UUFBQzdDO1FBQVFnQztLQUFzQjtJQUVsQyxNQUFNZ0IsUUFBbUM7UUFDdkM3QztRQUNBRztRQUNBWTtRQUNBRTtRQUNBRTtRQUNBVTtJQUNGO0lBRUEscUJBQ0UsOERBQUN4QyxzQkFBc0J5RCxRQUFRO1FBQUNELE9BQU9BO2tCQUNwQ3REOzs7Ozs7QUFHUCxFQUFFO0dBbktXRDs7UUFFMkJILCtEQUFjQTtRQUNyQ0Msc0RBQVNBOzs7S0FIYkU7QUFxS04sTUFBTXlELG9CQUFvQjs7SUFDL0IsTUFBTUMsVUFBVWxFLGlEQUFVQSxDQUFDTztJQUMzQixJQUFJLENBQUMyRCxTQUFTO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0lBTldEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxjb250ZXh0c1xcQ2hhdE1vZGFsU3RhdGVDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlR2xvYmFsTW9kYWwgfSBmcm9tICcuL0dsb2JhbE1vZGFsQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VTb2NrZXQgfSBmcm9tICcuL1NvY2tldFByb3ZpZGVyJztcblxuaW50ZXJmYWNlIENoYXRNb2RhbFN0YXRlIHtcbiAgY2hhdFJvb21JZDogc3RyaW5nO1xuICBhY3RpdmVUcmFkZT86IGFueTtcbiAgY3VycmVudFRyYWRlU3RhdHVzPzogc3RyaW5nO1xuICBpc09wZXJhdGlvbkluUHJvZ3Jlc3M/OiBib29sZWFuO1xuICBsYXN0VXBkYXRlZD86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIENoYXRNb2RhbFN0YXRlQ29udGV4dFR5cGUge1xuICBnZXRDaGF0TW9kYWxTdGF0ZTogKGNoYXRSb29tSWQ6IHN0cmluZykgPT4gQ2hhdE1vZGFsU3RhdGUgfCB1bmRlZmluZWQ7XG4gIHVwZGF0ZUNoYXRNb2RhbFN0YXRlOiAoY2hhdFJvb21JZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPENoYXRNb2RhbFN0YXRlPikgPT4gdm9pZDtcbiAgcmVnaXN0ZXJDaGF0TW9kYWw6IChjaGF0Um9vbUlkOiBzdHJpbmcsIGluaXRpYWxTdGF0ZTogQ2hhdE1vZGFsU3RhdGUpID0+IHZvaWQ7XG4gIHVucmVnaXN0ZXJDaGF0TW9kYWw6IChjaGF0Um9vbUlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIHVwZGF0ZU1vZGFsUHJvcHM6IChjaGF0Um9vbUlkOiBzdHJpbmcsIG5ld1Byb3BzOiBhbnkpID0+IGJvb2xlYW47XG4gIHN5bmNUcmFkZVN0YXR1c1VwZGF0ZTogKHRyYWRlSWQ6IHN0cmluZyB8IG51bWJlciwgc3RhdHVzOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmNvbnN0IENoYXRNb2RhbFN0YXRlQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8Q2hhdE1vZGFsU3RhdGVDb250ZXh0VHlwZSB8IG51bGw+KG51bGwpO1xuXG5leHBvcnQgY29uc3QgQ2hhdE1vZGFsU3RhdGVQcm92aWRlcjogUmVhY3QuRkM8eyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbY2hhdE1vZGFsU3RhdGVzLCBzZXRDaGF0TW9kYWxTdGF0ZXNdID0gdXNlU3RhdGU8TWFwPHN0cmluZywgQ2hhdE1vZGFsU3RhdGU+PihuZXcgTWFwKCkpO1xuICBjb25zdCB7IHVwZGF0ZU1vZGFsLCBnZXRNb2RhbEJ5SWQgfSA9IHVzZUdsb2JhbE1vZGFsKCk7XG4gIGNvbnN0IHNvY2tldCA9IHVzZVNvY2tldCgpO1xuICBjb25zdCBzdGF0ZVJlZiA9IHVzZVJlZjxNYXA8c3RyaW5nLCBDaGF0TW9kYWxTdGF0ZT4+KG5ldyBNYXAoKSk7XG5cbiAgLy8gS2VlcCByZWYgaW4gc3luYyB3aXRoIHN0YXRlXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc3RhdGVSZWYuY3VycmVudCA9IGNoYXRNb2RhbFN0YXRlcztcbiAgfSwgW2NoYXRNb2RhbFN0YXRlc10pO1xuXG4gIGNvbnN0IGdldENoYXRNb2RhbFN0YXRlID0gdXNlQ2FsbGJhY2soKGNoYXRSb29tSWQ6IHN0cmluZyk6IENoYXRNb2RhbFN0YXRlIHwgdW5kZWZpbmVkID0+IHtcbiAgICByZXR1cm4gc3RhdGVSZWYuY3VycmVudC5nZXQoY2hhdFJvb21JZCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB1cGRhdGVDaGF0TW9kYWxTdGF0ZSA9IHVzZUNhbGxiYWNrKChjaGF0Um9vbUlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8Q2hhdE1vZGFsU3RhdGU+KSA9PiB7XG4gICAgY29uc29sZS5sb2coYPCflIQgW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gVXBkYXRpbmcgc3RhdGUgZm9yIGNoYXRSb29tICR7Y2hhdFJvb21JZH06YCwgdXBkYXRlcyk7XG4gICAgXG4gICAgc2V0Q2hhdE1vZGFsU3RhdGVzKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3TWFwID0gbmV3IE1hcChwcmV2KTtcbiAgICAgIGNvbnN0IGN1cnJlbnRTdGF0ZSA9IG5ld01hcC5nZXQoY2hhdFJvb21JZCk7XG4gICAgICBcbiAgICAgIGlmIChjdXJyZW50U3RhdGUpIHtcbiAgICAgICAgY29uc3QgdXBkYXRlZFN0YXRlID0ge1xuICAgICAgICAgIC4uLmN1cnJlbnRTdGF0ZSxcbiAgICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICAgIGxhc3RVcGRhdGVkOiBEYXRlLm5vdygpXG4gICAgICAgIH07XG4gICAgICAgIG5ld01hcC5zZXQoY2hhdFJvb21JZCwgdXBkYXRlZFN0YXRlKTtcbiAgICAgICAgY29uc29sZS5sb2coYOKchSBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBVcGRhdGVkIHN0YXRlIGZvciAke2NoYXRSb29tSWR9OmAsIHVwZGF0ZWRTdGF0ZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZyhg4pqg77iPIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIE5vIHN0YXRlIGZvdW5kIGZvciBjaGF0Um9vbSAke2NoYXRSb29tSWR9YCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHJldHVybiBuZXdNYXA7XG4gICAgfSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCByZWdpc3RlckNoYXRNb2RhbCA9IHVzZUNhbGxiYWNrKChjaGF0Um9vbUlkOiBzdHJpbmcsIGluaXRpYWxTdGF0ZTogQ2hhdE1vZGFsU3RhdGUpID0+IHtcbiAgICBjb25zb2xlLmxvZyhg8J+TnSBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBSZWdpc3RlcmluZyBjaGF0Um9vbSAke2NoYXRSb29tSWR9OmAsIGluaXRpYWxTdGF0ZSk7XG4gICAgc2V0Q2hhdE1vZGFsU3RhdGVzKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3TWFwID0gbmV3IE1hcChwcmV2KTtcbiAgICAgIG5ld01hcC5zZXQoY2hhdFJvb21JZCwgeyAuLi5pbml0aWFsU3RhdGUsIGxhc3RVcGRhdGVkOiBEYXRlLm5vdygpIH0pO1xuICAgICAgcmV0dXJuIG5ld01hcDtcbiAgICB9KTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHVucmVnaXN0ZXJDaGF0TW9kYWwgPSB1c2VDYWxsYmFjaygoY2hhdFJvb21JZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYPCfl5HvuI8gW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gVW5yZWdpc3RlcmluZyBjaGF0Um9vbSAke2NoYXRSb29tSWR9YCk7XG4gICAgc2V0Q2hhdE1vZGFsU3RhdGVzKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3TWFwID0gbmV3IE1hcChwcmV2KTtcbiAgICAgIG5ld01hcC5kZWxldGUoY2hhdFJvb21JZCk7XG4gICAgICByZXR1cm4gbmV3TWFwO1xuICAgIH0pO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgdXBkYXRlTW9kYWxQcm9wcyA9IHVzZUNhbGxiYWNrKChjaGF0Um9vbUlkOiBzdHJpbmcsIG5ld1Byb3BzOiBhbnkpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCBtb2RhbElkID0gYGNoYXQtbW9kYWwtJHtjaGF0Um9vbUlkfWA7XG4gICAgY29uc3QgZXhpc3RpbmdNb2RhbCA9IGdldE1vZGFsQnlJZChtb2RhbElkKTtcbiAgICBcbiAgICBpZiAoIWV4aXN0aW5nTW9kYWwpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDimqDvuI8gW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gTm8gbW9kYWwgZm91bmQgd2l0aCBpZCAke21vZGFsSWR9YCk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYPCflIQgW0NoYXRNb2RhbFN0YXRlQ29udGV4dF0gVXBkYXRpbmcgbW9kYWwgcHJvcHMgZm9yICR7bW9kYWxJZH06YCwgbmV3UHJvcHMpO1xuICAgIFxuICAgIC8vIFVwZGF0ZSB0aGUgbW9kYWwgY29tcG9uZW50IHdpdGggbmV3IHByb3BzXG4gICAgY29uc3QgdXBkYXRlZENvbXBvbmVudCA9IFJlYWN0LmNsb25lRWxlbWVudChcbiAgICAgIGV4aXN0aW5nTW9kYWwuY29tcG9uZW50IGFzIFJlYWN0LlJlYWN0RWxlbWVudCxcbiAgICAgIHtcbiAgICAgICAgLi4uKChleGlzdGluZ01vZGFsLmNvbXBvbmVudCBhcyBSZWFjdC5SZWFjdEVsZW1lbnQpLnByb3BzIHx8IHt9KSxcbiAgICAgICAgLi4ubmV3UHJvcHMsXG4gICAgICAgIGtleTogYCR7bW9kYWxJZH0tJHtEYXRlLm5vdygpfWAgLy8gRm9yY2UgcmUtcmVuZGVyIHdpdGggbmV3IHByb3BzXG4gICAgICB9XG4gICAgKTtcblxuICAgIHJldHVybiB1cGRhdGVNb2RhbChtb2RhbElkLCB7XG4gICAgICBjb21wb25lbnQ6IHVwZGF0ZWRDb21wb25lbnQsXG4gICAgICB1cGRhdGVQcm9wczogbmV3UHJvcHNcbiAgICB9KTtcbiAgfSwgW2dldE1vZGFsQnlJZCwgdXBkYXRlTW9kYWxdKTtcblxuICBjb25zdCBzeW5jVHJhZGVTdGF0dXNVcGRhdGUgPSB1c2VDYWxsYmFjaygodHJhZGVJZDogc3RyaW5nIHwgbnVtYmVyLCBzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKGDwn5SEIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIFN5bmNpbmcgdHJhZGUgc3RhdHVzIHVwZGF0ZTogJHt0cmFkZUlkfSAtPiAke3N0YXR1c31gKTtcblxuICAgIC8vIEZpbmQgYWxsIGNoYXQgbW9kYWxzIHRoYXQgaGF2ZSB0aGlzIHRyYWRlIGFuZCB1cGRhdGUgdGhlbVxuICAgIHN0YXRlUmVmLmN1cnJlbnQuZm9yRWFjaCgoc3RhdGUsIGNoYXRSb29tSWQpID0+IHtcbiAgICAgIGlmIChzdGF0ZS5hY3RpdmVUcmFkZSAmJlxuICAgICAgICAgIChzdGF0ZS5hY3RpdmVUcmFkZS5pZCA9PT0gdHJhZGVJZCB8fCBzdGF0ZS5hY3RpdmVUcmFkZS50cmFkZUlkID09PSB0cmFkZUlkKSkge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+ToSBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBVcGRhdGluZyBjaGF0Um9vbSAke2NoYXRSb29tSWR9IHdpdGggbmV3IHN0YXR1czogJHtzdGF0dXN9YCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIHRoZSBzdGF0ZVxuICAgICAgICB1cGRhdGVDaGF0TW9kYWxTdGF0ZShjaGF0Um9vbUlkLCB7XG4gICAgICAgICAgY3VycmVudFRyYWRlU3RhdHVzOiBzdGF0dXMsXG4gICAgICAgICAgYWN0aXZlVHJhZGU6IHsgLi4uc3RhdGUuYWN0aXZlVHJhZGUsIHN0YXR1cyB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgbW9kYWwgcHJvcHMgdG8gdHJpZ2dlciByZS1yZW5kZXJcbiAgICAgICAgY29uc3QgbW9kYWxJZCA9IGBjaGF0LW1vZGFsLSR7Y2hhdFJvb21JZH1gO1xuICAgICAgICBjb25zdCBleGlzdGluZ01vZGFsID0gZ2V0TW9kYWxCeUlkKG1vZGFsSWQpO1xuICAgICAgICBpZiAoZXhpc3RpbmdNb2RhbCkge1xuICAgICAgICAgIGNvbnN0IHVwZGF0ZWRQcm9wcyA9IHtcbiAgICAgICAgICAgIGFjdGl2ZVRyYWRlOiB7IC4uLnN0YXRlLmFjdGl2ZVRyYWRlLCBzdGF0dXMgfSxcbiAgICAgICAgICAgIGN1cnJlbnRUcmFkZVN0YXR1czogc3RhdHVzXG4gICAgICAgICAgfTtcbiAgICAgICAgICB1cGRhdGVNb2RhbFByb3BzKGNoYXRSb29tSWQsIHVwZGF0ZWRQcm9wcyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgfSwgW3VwZGF0ZUNoYXRNb2RhbFN0YXRlLCBnZXRNb2RhbEJ5SWQsIHVwZGF0ZU1vZGFsUHJvcHNdKTtcblxuICAvLyBMaXN0ZW4gZm9yIHRyYWRlIHN0YXR1cyB1cGRhdGVzIGZyb20gc29ja2V0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFzb2NrZXQpIHJldHVybjtcblxuICAgIGNvbnN0IGhhbmRsZVRyYWRlU3RhdHVzVXBkYXRlID0gKGRhdGE6IHsgdHJhZGVJZDogc3RyaW5nIHwgbnVtYmVyOyBzdGF0dXM6IHN0cmluZyB9KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UlCBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBSZWNlaXZlZCB0cmFkZSBzdGF0dXMgdXBkYXRlIGZyb20gc29ja2V0OicsIGRhdGEpO1xuICAgICAgc3luY1RyYWRlU3RhdHVzVXBkYXRlKGRhdGEudHJhZGVJZCwgZGF0YS5zdGF0dXMpO1xuICAgIH07XG5cbiAgICBjb25zdCBoYW5kbGVFc2Nyb3dBY2NlcHRlZCA9IChkYXRhOiB7IHRyYWRlSWQ6IHN0cmluZyB8IG51bWJlciB9KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UlCBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBSZWNlaXZlZCBlc2Nyb3cgYWNjZXB0ZWQgZnJvbSBzb2NrZXQ6JywgZGF0YSk7XG4gICAgICBzeW5jVHJhZGVTdGF0dXNVcGRhdGUoZGF0YS50cmFkZUlkLCAnZXNjcm93ZWQnKTtcbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlUGVya1JlbGVhc2VkID0gKGRhdGE6IHsgdHJhZGVJZDogc3RyaW5nIHwgbnVtYmVyIH0pID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SUIFtDaGF0TW9kYWxTdGF0ZUNvbnRleHRdIFJlY2VpdmVkIHBlcmsgcmVsZWFzZWQgZnJvbSBzb2NrZXQ6JywgZGF0YSk7XG4gICAgICBzeW5jVHJhZGVTdGF0dXNVcGRhdGUoZGF0YS50cmFkZUlkLCAnY29tcGxldGVkJyk7XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZVJlZnVuZFByb2Nlc3NlZCA9IChkYXRhOiB7IHRyYWRlSWQ6IHN0cmluZyB8IG51bWJlciB9KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UlCBbQ2hhdE1vZGFsU3RhdGVDb250ZXh0XSBSZWNlaXZlZCByZWZ1bmQgcHJvY2Vzc2VkIGZyb20gc29ja2V0OicsIGRhdGEpO1xuICAgICAgc3luY1RyYWRlU3RhdHVzVXBkYXRlKGRhdGEudHJhZGVJZCwgJ3JlZnVuZGVkJyk7XG4gICAgfTtcblxuICAgIHNvY2tldC5vbigndHJhZGVTdGF0dXMnLCBoYW5kbGVUcmFkZVN0YXR1c1VwZGF0ZSk7XG4gICAgc29ja2V0Lm9uKCdlc2Nyb3dBY2NlcHRlZCcsIGhhbmRsZUVzY3Jvd0FjY2VwdGVkKTtcbiAgICBzb2NrZXQub24oJ3BlcmtSZWxlYXNlZCcsIGhhbmRsZVBlcmtSZWxlYXNlZCk7XG4gICAgc29ja2V0Lm9uKCdyZWZ1bmRQcm9jZXNzZWQnLCBoYW5kbGVSZWZ1bmRQcm9jZXNzZWQpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHNvY2tldC5vZmYoJ3RyYWRlU3RhdHVzJywgaGFuZGxlVHJhZGVTdGF0dXNVcGRhdGUpO1xuICAgICAgc29ja2V0Lm9mZignZXNjcm93QWNjZXB0ZWQnLCBoYW5kbGVFc2Nyb3dBY2NlcHRlZCk7XG4gICAgICBzb2NrZXQub2ZmKCdwZXJrUmVsZWFzZWQnLCBoYW5kbGVQZXJrUmVsZWFzZWQpO1xuICAgICAgc29ja2V0Lm9mZigncmVmdW5kUHJvY2Vzc2VkJywgaGFuZGxlUmVmdW5kUHJvY2Vzc2VkKTtcbiAgICB9O1xuICB9LCBbc29ja2V0LCBzeW5jVHJhZGVTdGF0dXNVcGRhdGVdKTtcblxuICBjb25zdCB2YWx1ZTogQ2hhdE1vZGFsU3RhdGVDb250ZXh0VHlwZSA9IHtcbiAgICBnZXRDaGF0TW9kYWxTdGF0ZSxcbiAgICB1cGRhdGVDaGF0TW9kYWxTdGF0ZSxcbiAgICByZWdpc3RlckNoYXRNb2RhbCxcbiAgICB1bnJlZ2lzdGVyQ2hhdE1vZGFsLFxuICAgIHVwZGF0ZU1vZGFsUHJvcHMsXG4gICAgc3luY1RyYWRlU3RhdHVzVXBkYXRlXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2hhdE1vZGFsU3RhdGVDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9DaGF0TW9kYWxTdGF0ZUNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgY29uc3QgdXNlQ2hhdE1vZGFsU3RhdGUgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KENoYXRNb2RhbFN0YXRlQ29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQ2hhdE1vZGFsU3RhdGUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIENoYXRNb2RhbFN0YXRlUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VHbG9iYWxNb2RhbCIsInVzZVNvY2tldCIsIkNoYXRNb2RhbFN0YXRlQ29udGV4dCIsIkNoYXRNb2RhbFN0YXRlUHJvdmlkZXIiLCJjaGlsZHJlbiIsImNoYXRNb2RhbFN0YXRlcyIsInNldENoYXRNb2RhbFN0YXRlcyIsIk1hcCIsInVwZGF0ZU1vZGFsIiwiZ2V0TW9kYWxCeUlkIiwic29ja2V0Iiwic3RhdGVSZWYiLCJjdXJyZW50IiwiZ2V0Q2hhdE1vZGFsU3RhdGUiLCJjaGF0Um9vbUlkIiwiZ2V0IiwidXBkYXRlQ2hhdE1vZGFsU3RhdGUiLCJ1cGRhdGVzIiwiY29uc29sZSIsImxvZyIsInByZXYiLCJuZXdNYXAiLCJjdXJyZW50U3RhdGUiLCJ1cGRhdGVkU3RhdGUiLCJsYXN0VXBkYXRlZCIsIkRhdGUiLCJub3ciLCJzZXQiLCJyZWdpc3RlckNoYXRNb2RhbCIsImluaXRpYWxTdGF0ZSIsInVucmVnaXN0ZXJDaGF0TW9kYWwiLCJkZWxldGUiLCJ1cGRhdGVNb2RhbFByb3BzIiwibmV3UHJvcHMiLCJtb2RhbElkIiwiZXhpc3RpbmdNb2RhbCIsInVwZGF0ZWRDb21wb25lbnQiLCJjbG9uZUVsZW1lbnQiLCJjb21wb25lbnQiLCJwcm9wcyIsImtleSIsInVwZGF0ZVByb3BzIiwic3luY1RyYWRlU3RhdHVzVXBkYXRlIiwidHJhZGVJZCIsInN0YXR1cyIsImZvckVhY2giLCJzdGF0ZSIsImFjdGl2ZVRyYWRlIiwiaWQiLCJjdXJyZW50VHJhZGVTdGF0dXMiLCJ1cGRhdGVkUHJvcHMiLCJoYW5kbGVUcmFkZVN0YXR1c1VwZGF0ZSIsImRhdGEiLCJoYW5kbGVFc2Nyb3dBY2NlcHRlZCIsImhhbmRsZVBlcmtSZWxlYXNlZCIsImhhbmRsZVJlZnVuZFByb2Nlc3NlZCIsIm9uIiwib2ZmIiwidmFsdWUiLCJQcm92aWRlciIsInVzZUNoYXRNb2RhbFN0YXRlIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ac1c9efe90a5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYWMxYzllZmU5MGE1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});