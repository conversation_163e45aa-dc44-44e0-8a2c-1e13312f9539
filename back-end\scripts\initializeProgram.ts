import * as anchor from "@coral-xyz/anchor";
import { PublicKey, Keypair, sendAndConfirmTransaction, Transaction } from "@solana/web3.js";
import { initialize } from "../utilities/funhi-program/initialize";
import { program } from "../utilities/funhi-program/setup";
import * as dotenv from "dotenv";
dotenv.config();

// Example: Load admin keypair from file or env
const ADMIN_KEYPAIR_PATH = process.env.ADMIN_KEYPAIR_PATH || "admin-keypair.json";
const fs = require("fs");
const adminKeypair = Keypair.fromSecretKey(
  new Uint8Array(JSON.parse(fs.readFileSync(ADMIN_KEYPAIR_PATH, "utf8")))
);

async function main() {
  // Set your actual values here or load from config/env
  const initialVirtualTokenReserves = new anchor.BN("1000000000");
  const initialVirtualSolReserves = new anchor.BN("1000000000");
  const initialRealTokenReserves = new anchor.BN("1000000000");
  const tokenTotalSupply = new anchor.BN("10000000000");
  const tradingFeeBps = new anchor.BN("100"); // 1%
  const feeRecipient = new PublicKey(process.env.FEE_RECIPIENT_PUBKEY!);
  const firstBuyFeeSol = new anchor.BN("20000000"); // 0.02 SOL in lamports
  const graduationThreshold = new anchor.BN("100000000");

  const ix = await initialize(
    initialVirtualTokenReserves,
    initialVirtualSolReserves,
    initialRealTokenReserves,
    tokenTotalSupply,
    tradingFeeBps,
    feeRecipient,
    firstBuyFeeSol,
    graduationThreshold
  );

  const tx = new Transaction().add(ix);
  const signature = await sendAndConfirmTransaction(
    program.provider.connection,
    tx,
    [adminKeypair]
  );
  console.log("Program initialized! Tx signature:", signature);
}

main().catch((err) => {
  console.error("Initialization failed:", err);
  process.exit(1);
}); 