/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/process";
exports.ids = ["vendor-chunks/process"];
exports.modules = {

/***/ "(ssr)/./node_modules/process/index.js":
/*!***************************************!*\
  !*** ./node_modules/process/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("// for now just expose the builtin process global from node.js\nmodule.exports = global.process;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvY2Vzcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxwcm9jZXNzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBmb3Igbm93IGp1c3QgZXhwb3NlIHRoZSBidWlsdGluIHByb2Nlc3MgZ2xvYmFsIGZyb20gbm9kZS5qc1xubW9kdWxlLmV4cG9ydHMgPSBnbG9iYWwucHJvY2VzcztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/process/index.js\n");

/***/ })

};
;