/** @format */

const { logError } = require("../utilities/logError");
const dataContext = require("../db");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const speakeasy = require("speakeasy");
const qrcode = require("qrcode");
const config = require("../config/security");


const JWT_SECRET = config.JWT_SECRET;
const expiresIn = "7d";
const expiresIn2FA = "60m";

exports.getUser = async (req, res, next) => {
  try {
    const { id, name } = req.params;

    const user = await dataContext.User.findOne({
      where: { userId: id },
    });

    if (!user) {
      return res.status(200).json({
        message: "success",
        data: null,
      });
    }

    if (user.dataValues.userName !== name) {
      await user.update({ userName: name });
    }

    res.status(200).json({
      message: "success",
      data: user,
    });
  } catch (error) {
    console.error("Error in getUser:", error);
    next(error); // send to centralized error handler
  }
};

exports.addUser = async (req, res, next) => {
  try {
    const { username, email, password } = req.body;

    // Check for existing email
    const existingUser = await dataContext.User.findOne({ where: { email } });
    if (existingUser) {
      const error = new Error("Email already in use, try another email.");
      error.statusCode = 400;
      throw error;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const newUser = await dataContext.User.create({
      username,
      email,
      password: hashedPassword,
    });

    res.status(200).json({
      status: 200,
      message: "User created successfully. Redirecting to login page...",
      data: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        createdAt: newUser.createdAt,
      },
    });
  } catch (error) {
    next(error); // Forward error to centralized error handler
  }
};

exports.loginUserPrivy = async (req, res, next) => {
  try {
    const { privyId, email, privywallet, walletBO, referralBy = "" } = req.body;

    if (!privyId) {
      const error = new Error("privyId is required.");
      error.statusCode = 400;
      throw error;
    }

    let user = await dataContext.User.findOne({ where: { privyId } });

    if (!user) {
      user = await dataContext.User.create({
        privyId,
        email: email || null,
        privywallet: privywallet || null,
        walletBO: walletBO || null,
        referralBy,
      });
    }

    const is2FAEnabled = user.twoFactorStatus === true;
    //const tokenPayload = { id: user.id };
    const tokenPayload = {
      id: user.id,
      privyId: user.privyId,
      privywallet: user.privywallet,
    };
    const tokenOptions = { expiresIn: is2FAEnabled ? expiresIn2FA : expiresIn };
    const token = jwt.sign(tokenPayload, JWT_SECRET, tokenOptions);

    const usertokens = await dataContext.Token.findAll({
      where: { userId: user.id },
    });

    res.status(200).json({
      status: 200,
      message: "Login successful.",
      token,
      data: {
        id: user.id ?? "",
        email: user.email ?? "",
        privyId: user.privyId ?? "",
        privywallet: user.privywallet ?? "",
        walletBO: user.walletBO ?? "",
        createdAt: user.createdAt ?? "",
        updatedAt: user.updatedAt ?? "",
        username: user.username ?? "",
        name: user.name ?? "",
        bio: user.bio ?? "",
        website: user.website ?? "",
        tiktok: user.tiktok ?? "",
        facebook: user.facebook ?? "",
        instagram: user.instagram ?? "",
        accountVerifiedPhoto: user.accountVerifiedPhoto ?? "",
        twoFactorStatus: user.twoFactorStatus ?? "",
        twoFactorData: user.twoFactorData ?? "",
        usertokens,
      },
    });
  } catch (err) {
    next(err); // forward to centralized handler
  }
};

exports.loginUser = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await dataContext.User.findOne({ where: { email } });
    if (!user) {
      const error = new Error(
        "Invalid email or password. If you don't have an account yet, click on Sign Up to create one."
      );
      error.statusCode = 400;
      throw error;
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      const error = new Error(
        "Invalid email or password. If you don't have an account yet, click on Sign Up to create one."
      );
      error.statusCode = 400;
      throw error;
    }

    // Generate token
    const is2FAEnabled = user.twoFactorStatus === true;
    const tokenPayload = {
      id: user.id,
      privyId: user.privyId,
      privywallet: user.privywallet,
    };
    const tokenOptions = {
      expiresIn: is2FAEnabled ? expiresIn2FA : expiresIn,
    };
    const token = jwt.sign(tokenPayload, JWT_SECRET, tokenOptions);

    // Success response
    res.status(200).json({
      status: 200,
      message: "Logged in successfully!",
      token,
      data: {
        id: user.id ?? "",
        username: user.username ?? "",
        email: user.email ?? "",
        name: user.name ?? "",
        bio: user.bio ?? "",
        website: user.website ?? "",
        tiktok: user.tiktok ?? "",
        facebook: user.facebook ?? "",
        instagram: user.instagram ?? "",
        accountVerifiedPhoto: user.accountVerifiedPhoto ?? "",
        twoFactorStatus: user.twoFactorStatus ?? "",
        twoFactorData: user.twoFactorData ?? "",
        createdAt: user.createdAt ?? "",
        updatedAt: user.updatedAt ?? "",
      },
    });
  } catch (err) {
    next(err); // send to centralized error handler
  }
};

exports.changePassword = async (req, res, next) => {
  try {
    const { email, oldPassword, newPassword } = req.body;

    const user = await dataContext.User.findOne({ where: { email } });

    if (!user) {
      const error = new Error("User not found.");
      error.statusCode = 400;
      throw error;
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      const error = new Error("Old password is incorrect.");
      error.statusCode = 400;
      throw error;
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedNewPassword;
    await user.save();

    res.status(200).json({
      status: 200,
      message: "Password changed successfully.",
    });
  } catch (err) {
    next(err); // send to centralized error handler
  }
};

exports.updateProfile = async (req, res, next) => {
  try {
    const {
      userId,
      username,
      name,
      bio,
      website,
      tiktok,
      facebook,
      instagram,
      accountVerifiedPhoto,
      twoFactorStatus,
      twoFactorData,
      password,
      newPassword,
    } = req.body;

    const user = await dataContext.User.findOne({ where: { id: userId } });
    if (!user) {
      const error = new Error("User not found.");
      error.statusCode = 404;
      throw error;
    }

    // Handle password change if requested
    if (password && newPassword) {
      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        const error = new Error(
          "Old password is incorrect. Please enter the correct password."
        );
        error.statusCode = 400;
        throw error;
      }

      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(newPassword, salt);
    }

    // Selectively update profile fields
    if (username) user.username = username;
    if (name) user.name = name;
    if (bio) user.bio = bio;
    if (website) user.website = website;
    if (tiktok) user.tiktok = tiktok;
    if (facebook) user.facebook = facebook;
    if (instagram) user.instagram = instagram;
    if (accountVerifiedPhoto) user.accountVerifiedPhoto = accountVerifiedPhoto;
    if (typeof twoFactorStatus === "boolean")
      user.twoFactorStatus = twoFactorStatus;
    if (twoFactorData) user.twoFactorData = twoFactorData;
    // Fix: allowMessages should be updated even if false
    if (typeof req.body.allowMessages !== 'undefined') user.allowMessages = req.body.allowMessages;

    await user.save();

    const { password: _, ...userData } = user.dataValues;

    res.status(200).json({
      status: 200,
      message: "Profile updated successfully.",
      data: userData,
    });
  } catch (err) {
    next(err); // Use centralized error handler
  }
};

exports.getUserPurchasesSummary = async (req, res) => {
  const { userId } = req.body;

  try {
    // Check if user exists
    const user = await dataContext.User.findOne({ where: { id: userId } });
    if (!user) {
      return res.status(200).json({ status: 404, message: "User not found" });
    }

    // 1) Fetch Purchased Perks
    const purchasedPerks = await dataContext.PerkPurchased.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.Perk,
          as: "perkDetails",
          attributes: [
            "perkId",
            "name",
            "description",
            "price",
            "image",
            "category",
            "isVerified",
          ],
        },
      ],
    });

    // 2) Fetch Purchased Tokens (full list for Transactions)
    const purchasedTokens = await dataContext.TokenPurchased.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.Token,
          as: "tokenDetails",
          attributes: [
            "tokenId",
            "name",
            "ticker",
            "image",
            "price",
            "category",
          ],
        },
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    // 3) Summarize purchaseToken: sum amount by tokenId
    const tokenSummaryMap = {};
    for (const purchase of purchasedTokens) {
      const tokenId = purchase.tokenId;
      if (!tokenSummaryMap[tokenId]) {
        tokenSummaryMap[tokenId] = {
          tokenId,
          tokenDetails: purchase.tokenDetails,
          user: purchase.user,
          totalAmount: 0,
          totalPrice: 0,
          totalDollorPrice: 0,
        };
      }
      if (purchase.buyorsell == 1) {
        tokenSummaryMap[tokenId].totalAmount += purchase.amount;
        tokenSummaryMap[tokenId].totalPrice += purchase.price;
        tokenSummaryMap[tokenId].totalDollorPrice += purchase.dollorPrice;
      } else {
        tokenSummaryMap[tokenId].totalAmount -= purchase.amount;
        tokenSummaryMap[tokenId].totalPrice -= purchase.price;
        tokenSummaryMap[tokenId].totalDollorPrice -= purchase.dollorPrice;
      }
    }
    const purchaseToken = Object.values(tokenSummaryMap);

    // 4) Format Transactions
    const formattedTransactions = purchasedTokens.map((t) => {
      return {
        id: t.id, // token_purchased id
        name: t.tokenDetails?.name || "Token buy", // now dynamic name from token
        date: formatDate(t.createdAt),
        amount: `-$${Number(t.amount).toFixed(2)}`,
        type: t.buyorsell ? "Buy" : "Sell",
        icon: "/icons/shopping-cart.svg",
        buyorsell: t.buyorsell,
        amount: t.amount,
        price: t.price,
        dollorPrice: t.dollorPrice,
        hasH: t.hasH,
        transactionId: t.transactionId,
        purchasedType: t.status,
      };
    });

    // 5) Fetch My Perks where Perk.userId = userId
    // 2) Fetch Purchased Tokens (full list for Transactions)
    const myToken = await dataContext.Token.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
    });

    // 5) Fetch My Perks where Perk.userId = userId
    const myPerks = await dataContext.Perk.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    // 5) Fetch My Perks where Perk.userId = userId
    const myAirDrop = await dataContext.Airdrop.findAll({
      where: { userId },
      order: [["createdAt", "DESC"]],
    });

    // Send response
    res.status(200).json({
      status: 200,
      message: "Purchases summary fetched successfully",
      data: {
        PurchasedPerks: purchasedPerks,
        PurchaseToken: purchaseToken,
        Transactions: formattedTransactions,
        MyPerks: myPerks,
        MyToken: myToken,
        myAirDrop: myAirDrop,
      },
    });
  } catch (error) {
    console.error("Error fetching purchases summary:", error);
    res.status(200).json({
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
    });
  }
};

exports.setup2FA = async (req, res) => {
  try {
    const { userId } = req.body;

    // Find user by ID
    const user = await dataContext.User.findOne({ where: { id: userId } });

    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found." });
    }

    // Check if 2FA is already set up
    if (user.twoFactorStatus) {
      return res.status(400).json({
        status: 400,
        message:
          "Two-factor authentication is already enabled for this account.",
      });
    }

    function truncateAddress(address, startLength = 4, endLength = 4) {
      if (!address || address.length <= startLength + endLength) {
        return address;
      }
      return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
    }

    // Generate a secret key
    const secret = speakeasy.generateSecret({
      name: `FunHi:${
        user.email || truncateAddress(user.privywallet) || user.id
      }`,
    });

    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);

    // Store temporary secret in user record (not enabled yet until verified)
    await user.update({
      twoFactorData: {
        secret: secret.base32,
        tempSecret: secret.base32,
        verified: false,
      },
    });

    res.status(200).json({
      status: 200,
      message: "2FA setup initiated successfully.",
      data: {
        qrCodeUrl,
        secret: secret.base32,
      },
    });
  } catch (error) {
    console.error("2FA Setup Error:", error);
    res.status(500).json({
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
      error: error.message,
    });
  }
};

exports.verify2FA = async (req, res) => {
  try {
    const { userId, token } = req.body;

    // Find user by ID
    const user = await dataContext.User.findOne({ where: { id: userId } });

    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found." });
    }

    // Parse twoFactorData string
    let twoFactorData = {};
    try {
      twoFactorData = JSON.parse(user.twoFactorData || "{}");
    } catch (err) {
      return res.status(500).json({
        status: 500,
        message: "Failed to parse 2FA data.",
      });
    }

    // Check if tempSecret exists
    if (!twoFactorData.tempSecret) {
      return res.status(400).json({
        status: 400,
        message: "Please setup 2FA before verifying.",
        data: user,
      });
    }

    // Verify the token using speakeasy
    const verified = speakeasy.totp.verify({
      secret: twoFactorData.tempSecret,
      encoding: "base32",
      token: token,
      window: 1, // optional: allow +/- 1 step drift
    });

    if (!verified) {
      return res.status(400).json({
        status: 400,
        message: "Invalid verification code. Please try again.",
      });
    }

    // Enable 2FA and save the updated data
    const updatedTwoFactorData = {
      secret: twoFactorData.tempSecret,
      verified: true,
    };

    await user.update({
      twoFactorStatus: true,
      twoFactorData: updatedTwoFactorData,
    });

    res.status(200).json({
      status: 200,
      message: "Two-factor authentication enabled successfully.",
      data: {
        twoFactorStatus: true,
      },
    });
  } catch (error) {
    console.error("2FA Verification Error:", error);
    res.status(500).json({
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
      error: error.message,
    });
  }
};

exports.login2FA = async (req, res) => {
  try {
    const { userId, token } = req.body;

    // Find user by ID
    const user = await dataContext.User.findOne({ where: { id: userId } });

    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found." });
    }

    // Check if 2FA is enabled
    if (!user.twoFactorStatus) {
      return res.status(400).json({
        status: 400,
        message: "Two-factor authentication is not enabled for this account.",
      });
    }

    // Parse twoFactorData string
    let twoFactorData = {};
    try {
      twoFactorData = JSON.parse(user.twoFactorData || "{}");
    } catch (err) {
      return res.status(500).json({
        status: 500,
        message: "Failed to parse 2FA data.",
      });
    }
    // Verify the token
    const verfied = speakeasy.totp.verify({
      secret: twoFactorData.secret,
      encoding: "base32",
      token: token,
    });

    if (!verfied) {
      return res.status(400).json({
        status: 400,
        message: "Invalid verification code. Please try again.",
      });
    }

    // Generate a new JWT token with 2FA verified flag
    const token2FA = jwt.sign(
      {
        id: user.id,
      },
      JWT_SECRET,
      { expiresIn: expiresIn2FA }
    );

    res.status(200).json({
      status: 200,
      message: "Two-factor authentication verified successfully.",
      token: token2FA,
      data: {
        id: user.id ?? "",
        username: user.username ?? "",
        email: user.email ?? "",
        name: user.name ?? "",
        bio: user.bio ?? "",
        website: user.website ?? "",
        tiktok: user.tiktok ?? "",
        facebook: user.facebook ?? "",
        instagram: user.instagram ?? "",
        accountVerifiedPhoto: user.accountVerifiedPhoto ?? "",
        twoFactorStatus: user.twoFactorStatus ?? "",
        twoFactorData: user.twoFactorData ?? "",
        createdAt: user.createdAt ?? "",
        updatedAt: user.updatedAt ?? "",
      },
    });
  } catch (error) {
    console.error("2FA Login Error:", error);
    res.status(500).json({
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
      error: error.message,
    });
  }
};

exports.disable2FA = async (req, res) => {
  try {
    const { userId, token } = req.body;

    // Find user by ID
    const user = await dataContext.User.findOne({ where: { id: userId } });

    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found." });
    }

    // Check if 2FA is enabled
    if (!user.twoFactorStatus) {
      return res.status(400).json({
        status: 400,
        message: "Two-factor authentication is not enabled for this account.",
      });
    }

    // Parse twoFactorData string
    let twoFactorData = {};
    try {
      twoFactorData = JSON.parse(user.twoFactorData || "{}");
    } catch (err) {
      return res.status(500).json({
        status: 500,
        message: "Failed to parse 2FA data.",
      });
    }

    // Verify the token before disabling
    const verified = speakeasy.totp.verify({
      secret: twoFactorData.secret,
      encoding: "base32",
      token: token,
    });

    if (!verified) {
      return res.status(400).json({
        status: 400,
        message: "Invalid verification code. Please try again.",
      });
    }

    // Disable 2FA
    await user.update({
      twoFactorStatus: false,
      twoFactorData: null,
    });

    res.status(200).json({
      status: 200,
      message: "Two-factor authentication disabled successfully.",
      data: {
        twoFactorStatus: false,
      },
    });
  } catch (error) {
    console.error("2FA Disable Error:", error);
    res.status(500).json({
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
      error: error.message,
    });
  }
};

exports.getUserTrades = async (req, res) => {
  try {
    const { userId } = req.params;
    // Find all trades where user is buyer or seller
    const trades = await dataContext.TokenPurchased.findAll({
      where: {
        [dataContext.Sequelize.Op.or]: [
          { userId }, // buyer
          { to: userId }, // seller (if 'to' is seller's userId)
        ],
      },
      order: [['createdAt', 'DESC']],
    });

    // For each trade, add buyerId and sellerId
    const tradesWithUserIds = await Promise.all(trades.map(async (trade) => {
      let sellerId = null;
      if (trade.perkId) {
        const perk = await dataContext.Perk.findByPk(trade.perkId);
        sellerId = perk ? perk.userId : null;
      }
      return {
        ...trade.toJSON(),
        buyerId: trade.userId,
        sellerId: sellerId,
      };
    }));

    res.status(200).json({ status: 200, data: tradesWithUserIds });
  } catch (error) {
    console.error('Error fetching user trades:', error);
    res.status(500).json({ status: 500, message: 'Error fetching user trades' });
  }
};

// Get user by ID only (for /users/:id)
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await dataContext.User.findOne({
      where: { id },
    });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({
      message: "success",
      data: user,
    });
  } catch (error) {
    console.log(error);
    await logError({
      error: error,
      location: "Error ==> controller/user.js -> getUserById",
      errorFile: config.errorFile.controllerError,
      errorReference: "controller/user.js -> getUserById",
    });
    res.status(500).json({
      error: "An unexpected error occurred. Please try again later.",
    });
  }
};

// Follow a user
exports.followUser = async (req, res) => {
  try {
    const followerId = req.body.followerId;
    const followingId = req.params.id;
    if (!followerId || !followingId) {
      return res.status(400).json({ status: 400, message: "Missing followerId or followingId" });
    }
    if (followerId === followingId) {
      return res.status(400).json({ status: 400, message: "You cannot follow yourself." });
    }
    // Check if already following
    const existing = await dataContext.Follow.findOne({ where: { followerId, followingId } });
    if (existing) {
      return res.status(200).json({ status: 200, message: "Already following" });
    }
    await dataContext.Follow.create({ followerId, followingId });
    res.status(201).json({ status: 201, message: "Followed successfully" });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error following user", error: error.message });
  }
};

// Unfollow a user
exports.unfollowUser = async (req, res) => {
  try {
    const followerId = req.body.followerId;
    const followingId = req.params.id;
    if (!followerId || !followingId) {
      return res.status(400).json({ status: 400, message: "Missing followerId or followingId" });
    }
    const deleted = await dataContext.Follow.destroy({ where: { followerId, followingId } });
    if (deleted) {
      res.status(200).json({ status: 200, message: "Unfollowed successfully" });
    } else {
      res.status(404).json({ status: 404, message: "Follow relationship not found" });
    }
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error unfollowing user", error: error.message });
  }
};

// Get followers of a user
exports.getFollowers = async (req, res) => {
  try {
    const userId = req.params.id;
    // Use the followers association to get all users who follow this user
    const user = await dataContext.User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found" });
    }
    const followers = await user.getFollowers({
      attributes: ["id", "username", "name", "email"],
      joinTableAttributes: [],
    });
    res.status(200).json({ status: 200, count: followers.length, followers });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching followers", error: error.message });
  }
};

// Get users this user is following
exports.getFollowing = async (req, res) => {
  try {
    const userId = req.params.id;
    const user = await dataContext.User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found" });
    }
    const following = await user.getFollowing({
      attributes: ["id", "username", "name", "email"],
      joinTableAttributes: [],
    });
    res.status(200).json({ status: 200, count: following.length, following });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching following", error: error.message });
  }
};

// Public Creator Profile Endpoint (now matches getUserPurchasesSummary, but uses userId from params)
exports.getPublicProfile = async (req, res) => {
  // This endpoint always fetches fresh data from the database. No cache is used.
  res.set('Cache-Control', 'no-store');
  const { userId } = req.params;
  try {
    // Check if user exists
    const user = await dataContext.User.findOne({ where: { id: userId } });
    console.log('User found:', user ? user.id : null);
    if (!user) {
      return res.status(200).json({ status: 404, message: "User not found" });
    }

    // 1) Fetch Purchased Perks
    const purchasedPerks = await dataContext.PerkPurchased.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.Perk,
          as: "perkDetails",
          attributes: [
            "perkId",
            "name",
            "description",
            "price",
            "image",
            "category",
            "isVerified",
          ],
        },
      ],
    });

    // 2) Fetch Purchased Tokens (full list for Transactions)
    const purchasedTokens = await dataContext.TokenPurchased.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.Token,
          as: "tokenDetails",
          attributes: [
            "tokenId",
            "name",
            "ticker",
            "image",
            "price",
            "category",
          ],
        },
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    // 3) Summarize purchaseToken: sum amount by tokenId
    const tokenSummaryMap = {};
    for (const purchase of purchasedTokens) {
      const tokenId = purchase.tokenId;
      if (!tokenSummaryMap[tokenId]) {
        tokenSummaryMap[tokenId] = {
          tokenId,
          tokenDetails: purchase.tokenDetails,
          user: purchase.user,
          totalAmount: 0,
          totalPrice: 0,
          totalDollorPrice: 0,
        };
      }
      if (purchase.buyorsell == 1) {
        tokenSummaryMap[tokenId].totalAmount += purchase.amount;
        tokenSummaryMap[tokenId].totalPrice += purchase.price;
        tokenSummaryMap[tokenId].totalDollorPrice += purchase.dollorPrice;
      } else {
        tokenSummaryMap[tokenId].totalAmount -= purchase.amount;
        tokenSummaryMap[tokenId].totalPrice -= purchase.price;
        tokenSummaryMap[tokenId].totalDollorPrice -= purchase.dollorPrice;
      }
    }
    const purchaseToken = Object.values(tokenSummaryMap);

    // 4) Format Transactions
    const formattedTransactions = purchasedTokens.map((t) => {
      return {
        id: t.id, // token_purchased id
        name: t.tokenDetails?.name || "Token buy", // now dynamic name from token
        date: formatDate(t.createdAt),
        amount: `-$${Number(t.amount).toFixed(2)}`,
        type: t.buyorsell ? "Buy" : "Sell",
        icon: "/icons/shopping-cart.svg",
        buyorsell: t.buyorsell,
        amount: t.amount,
        price: t.price,
        dollorPrice: t.dollorPrice,
        hasH: t.hasH,
        transactionId: t.transactionId,
        purchasedType:t.status
      };
    });

    // 5) Fetch My Perks where Perk.userId = userId
    // 2) Fetch Purchased Tokens (full list for Transactions)
    const myToken = await dataContext.Token.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
    });

    // 5) Fetch My Perks where Perk.userId = userId
    const myPerks = await dataContext.Perk.findAll({
      where: { userId },
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    // 5) Fetch My Perks where Perk.userId = userId
    const myAirDrop = await dataContext.Airdrop.findAll({
      where: { userId },
      order: [["createdAt", "DESC"]],
    });


    // Send response
    res.status(200).json({
      status: 200,
      message: "Purchases summary fetched successfully",
      data: {
        PurchasedPerks: purchasedPerks,
        PurchaseToken: purchaseToken,
        Transactions: formattedTransactions,
        MyPerks: myPerks,
        MyToken: myToken,
        myAirDrop: myAirDrop,
        allowMessages: user.allowMessages // Add allowMessages field from user
      },
    });
  } catch (error) {
    console.error("Error fetching purchases summary:", error);
    res.status(200).json({ status: 500, message: 'An unexpected error occurred. Please try again later.' });
  }
};

// Helper to format date as "Apr 18, 2024 | 10:58PM"
function formatDate(date) {
  const options = { month: "short", day: "2-digit", year: "numeric" };
  const datePart = new Date(date).toLocaleDateString("en-US", options);
  const timePart = new Date(date).toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
  return `${datePart} | ${timePart}`;
}
