import { Loader2 } from "lucide-react";
import { motion } from "framer-motion";

// Simple loading component for tab content
export const TabContentLoader = () => (
  <motion.div
    className="flex items-center justify-center py-12"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
  >
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    >
      <Loader2 size={32} className="text-[#FF6600]" />
    </motion.div>
    <span className="ml-2 text-gray-600">Loading...</span>
  </motion.div>
);