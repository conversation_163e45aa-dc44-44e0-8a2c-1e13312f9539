import { Variants } from 'framer-motion';

export const fadeInUp: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
};

// Stagger container for lists
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
};

// Card hover animations
export const cardHover = {
  scale: 1.02,
  y: -5,
  transition: {
    duration: 0.2,
    ease: [0.4, 0.0, 0.2, 1],
  },
};

// Button animations
export const buttonHover = {
  scale: 1.02,
  transition: {
    duration: 0.2,
    ease: [0.4, 0.0, 0.2, 1],
  },
};
