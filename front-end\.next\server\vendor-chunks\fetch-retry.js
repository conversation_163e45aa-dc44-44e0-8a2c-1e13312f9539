"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fetch-retry";
exports.ids = ["vendor-chunks/fetch-retry"];
exports.modules = {

/***/ "(ssr)/./node_modules/fetch-retry/index.js":
/*!*******************************************!*\
  !*** ./node_modules/fetch-retry/index.js ***!
  \*******************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function (fetch, defaults) {\n  defaults = defaults || {};\n  if (typeof fetch !== 'function') {\n    throw new ArgumentError('fetch must be a function');\n  }\n\n  if (typeof defaults !== 'object') {\n    throw new ArgumentError('defaults must be an object');\n  }\n\n  if (defaults.retries !== undefined && !isPositiveInteger(defaults.retries)) {\n    throw new ArgumentError('retries must be a positive integer');\n  }\n\n  if (defaults.retryDelay !== undefined && !isPositiveInteger(defaults.retryDelay) && typeof defaults.retryDelay !== 'function') {\n    throw new ArgumentError('retryDelay must be a positive integer or a function returning a positive integer');\n  }\n\n  if (defaults.retryOn !== undefined && !Array.isArray(defaults.retryOn) && typeof defaults.retryOn !== 'function') {\n    throw new ArgumentError('retryOn property expects an array or function');\n  }\n\n  var baseDefaults = {\n    retries: 3,\n    retryDelay: 1000,\n    retryOn: [],\n  };\n\n  defaults = Object.assign(baseDefaults, defaults);\n\n  return function fetchRetry(input, init) {\n    var retries = defaults.retries;\n    var retryDelay = defaults.retryDelay;\n    var retryOn = defaults.retryOn;\n\n    if (init && init.retries !== undefined) {\n      if (isPositiveInteger(init.retries)) {\n        retries = init.retries;\n      } else {\n        throw new ArgumentError('retries must be a positive integer');\n      }\n    }\n\n    if (init && init.retryDelay !== undefined) {\n      if (isPositiveInteger(init.retryDelay) || (typeof init.retryDelay === 'function')) {\n        retryDelay = init.retryDelay;\n      } else {\n        throw new ArgumentError('retryDelay must be a positive integer or a function returning a positive integer');\n      }\n    }\n\n    if (init && init.retryOn) {\n      if (Array.isArray(init.retryOn) || (typeof init.retryOn === 'function')) {\n        retryOn = init.retryOn;\n      } else {\n        throw new ArgumentError('retryOn property expects an array or function');\n      }\n    }\n\n    // eslint-disable-next-line no-undef\n    return new Promise(function (resolve, reject) {\n      var wrappedFetch = function (attempt) {\n        // As of node 18, this is no longer needed since node comes with native support for fetch:\n        /* istanbul ignore next */\n        var _input =\n          typeof Request !== 'undefined' && input instanceof Request\n            ? input.clone()\n            : input;\n        fetch(_input, init)\n          .then(function (response) {\n            if (Array.isArray(retryOn) && retryOn.indexOf(response.status) === -1) {\n              resolve(response);\n            } else if (typeof retryOn === 'function') {\n              try {\n                // eslint-disable-next-line no-undef\n                return Promise.resolve(retryOn(attempt, null, response))\n                  .then(function (retryOnResponse) {\n                    if(retryOnResponse) {\n                      retry(attempt, null, response);\n                    } else {\n                      resolve(response);\n                    }\n                  }).catch(reject);\n              } catch (error) {\n                reject(error);\n              }\n            } else {\n              if (attempt < retries) {\n                retry(attempt, null, response);\n              } else {\n                resolve(response);\n              }\n            }\n          })\n          .catch(function (error) {\n            if (typeof retryOn === 'function') {\n              try {\n                // eslint-disable-next-line no-undef\n                Promise.resolve(retryOn(attempt, error, null))\n                  .then(function (retryOnResponse) {\n                    if(retryOnResponse) {\n                      retry(attempt, error, null);\n                    } else {\n                      reject(error);\n                    }\n                  })\n                  .catch(function(error) {\n                    reject(error);\n                  });\n              } catch(error) {\n                reject(error);\n              }\n            } else if (attempt < retries) {\n              retry(attempt, error, null);\n            } else {\n              reject(error);\n            }\n          });\n      };\n\n      function retry(attempt, error, response) {\n        var delay = (typeof retryDelay === 'function') ?\n          retryDelay(attempt, error, response) : retryDelay;\n        setTimeout(function () {\n          wrappedFetch(++attempt);\n        }, delay);\n      }\n\n      wrappedFetch(0);\n    });\n  };\n};\n\nfunction isPositiveInteger(value) {\n  return Number.isInteger(value) && value >= 0;\n}\n\nfunction ArgumentError(message) {\n  this.name = 'ArgumentError';\n  this.message = message;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fetch-retry/index.js\n");

/***/ })

};
;