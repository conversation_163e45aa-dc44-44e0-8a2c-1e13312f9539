const { Sequelize, Op } = require('sequelize');
const initModels = require('../model');
const config = require("../config/security");

const sequelize = new Sequelize(
  config.DB.NAME,
  config.DB.USERNAME,
  config.DB.PASSWORD,
  {
    host: config.DB.HOST,
    port: parseInt(config.DB.PORT, 10),
    dialect: config.DB.DIALECT || 'mysql',
    logging: false, // Change to `console.log` if you want verbose output
  }
);

async function initializeDatabase() {
  try {
    await sequelize.authenticate();
    console.log("✅ Database connection established successfully.");
  } catch (error) {
    console.error("❌ Unable to connect to the database:", error);
    process.exit(1);
  }
}

initializeDatabase();

const dataContext = initModels(sequelize);
dataContext.Sequelize = Sequelize;
dataContext.Op = Op;
module.exports = dataContext;
