import { CongratulationType, ActionButton, SuccessMetric } from '@/components/ui/CongratulationsModal';

/**
 * Utility functions to help replace existing alerts and basic success notifications
 * with the new advanced congratulations modal system.
 */

// Quick success notification configurations
export const getQuickSuccessConfig = (
  type: 'purchase' | 'payment' | 'refund' | 'general',
  txId?: string,
  amount?: string
) => {
  const configs = {
    purchase: {
      type: 'purchase_success' as CongratulationType,
      title: 'Purchase Successful! 🎉',
      message: 'Your purchase has been completed and secured in escrow.',
      showConfetti: true,
      autoDismiss: false
    },
    payment: {
      type: 'funds_released' as CongratulationType,
      title: 'Payment Complete! 💰',
      message: 'The payment has been successfully processed.',
      showConfetti: true,
      autoDismiss: true
    },
    refund: {
      type: 'refund_processed' as CongratulationType,
      title: 'Refund Processed! 🔄',
      message: 'Your refund has been processed successfully.',
      showConfetti: false,
      autoDismiss: true
    },
    general: {
      type: 'general_success' as CongratulationType,
      title: 'Success! ✅',
      message: 'Operation completed successfully.',
      showConfetti: false,
      autoDismiss: true
    }
  };

  const config = configs[type];
  
  return {
    ...config,
    transactionId: txId,
    amount: amount
  };
};

// Replace alert() calls with congratulations modal
export const replaceAlertWithCongratulations = (
  message: string,
  showCongratulationsModal: (config: any) => void,
  type: CongratulationType = 'general_success'
) => {
  // Detect success patterns in alert messages
  const isSuccess = /success|complete|done|finished|created|processed/i.test(message);
  
  if (isSuccess) {
    showCongratulationsModal({
      type,
      message,
      showConfetti: type === 'purchase_success' || type === 'funds_released',
      autoDismiss: true,
      autoDismissDelay: 3000
    });
  } else {
    // For non-success messages, fall back to console or error handling
    console.log('Alert message:', message);
  }
};

// Create action buttons for common scenarios
export const createCommonActions = (
  scenario: 'purchase' | 'transaction' | 'refund' | 'dispute',
  callbacks: {
    onContinue?: () => void;
    onViewDetails?: () => void;
    onShare?: () => void;
    onChat?: () => void;
  }
): ActionButton[] => {
  const actions: ActionButton[] = [];

  switch (scenario) {
    case 'purchase':
      if (callbacks.onChat) {
        actions.push({
          label: 'Open Chat',
          onClick: callbacks.onChat,
          variant: 'primary',
          icon: 'chat'
        });
      }
      if (callbacks.onViewDetails) {
        actions.push({
          label: 'View Purchase',
          onClick: callbacks.onViewDetails,
          variant: 'secondary',
          icon: 'view'
        });
      }
      break;

    case 'transaction':
      if (callbacks.onContinue) {
        actions.push({
          label: 'Continue Shopping',
          onClick: callbacks.onContinue,
          variant: 'primary',
          icon: 'shopping'
        });
      }
      if (callbacks.onViewDetails) {
        actions.push({
          label: 'View Transaction',
          onClick: callbacks.onViewDetails,
          variant: 'secondary',
          icon: 'document'
        });
      }
      break;

    case 'refund':
      if (callbacks.onContinue) {
        actions.push({
          label: 'Continue',
          onClick: callbacks.onContinue,
          variant: 'primary'
        });
      }
      if (callbacks.onViewDetails) {
        actions.push({
          label: 'View Refund',
          onClick: callbacks.onViewDetails,
          variant: 'secondary',
          icon: 'search'
        });
      }
      break;

    case 'dispute':
      if (callbacks.onViewDetails) {
        actions.push({
          label: 'View Dispute',
          onClick: callbacks.onViewDetails,
          variant: 'primary',
          icon: 'scale'
        });
      }
      if (callbacks.onContinue) {
        actions.push({
          label: 'Continue',
          onClick: callbacks.onContinue,
          variant: 'secondary'
        });
      }
      break;
  }

  // Always add share option if provided
  if (callbacks.onShare) {
    actions.push({
      label: 'Share',
      onClick: callbacks.onShare,
      variant: 'outline',
      icon: 'share'
    });
  }

  return actions;
};

// Create success metrics for common scenarios
export const createSuccessMetrics = (
  data: {
    amount?: string;
    fee?: string;
    processingTime?: string;
    escrowId?: string;
    status?: string;
  }
): SuccessMetric[] => {
  const metrics: SuccessMetric[] = [];

  if (data.amount) {
    metrics.push({
      label: 'Amount',
      value: data.amount,
      icon: '💰'
    });
  }

  if (data.fee) {
    metrics.push({
      label: 'Network Fee',
      value: data.fee,
      icon: <span>⛽</span>
    });
  }

  if (data.processingTime) {
    metrics.push({
      label: 'Processing Time',
      value: data.processingTime,
      icon: '⏱️'
    });
  }

  if (data.escrowId) {
    metrics.push({
      label: 'Escrow ID',
      value: data.escrowId,
      icon: <span>🔒</span>
    });
  }

  if (data.status) {
    metrics.push({
      label: 'Status',
      value: data.status,
      icon: '✅'
    });
  }

  return metrics;
};

// Migration helper for existing success handlers
export const migrateSuccessHandler = (
  originalHandler: () => void,
  congratulationsConfig: {
    type: CongratulationType;
    title?: string;
    message?: string;
    txId?: string;
    amount?: string;
  },
  showCongratulationsModal: (config: any) => void
) => {
  return () => {
    // Execute original logic
    originalHandler();
    
    // Show congratulations modal
    showCongratulationsModal(congratulationsConfig);
  };
};

// Format transaction ID for display
export const formatTransactionId = (txId: string, maxLength: number = 16): string => {
  if (txId.length <= maxLength) {
    return txId;
  }
  
  const start = Math.floor((maxLength - 3) / 2);
  const end = Math.ceil((maxLength - 3) / 2);
  
  return `${txId.slice(0, start)}...${txId.slice(-end)}`;
};

// Format amount for display
export const formatAmount = (amount: number | string, currency: string = 'SOL'): string => {
  if (typeof amount === 'string') {
    return amount.includes(currency) ? amount : `${amount} ${currency}`;
  }
  
  return `${amount.toFixed(4)} ${currency}`;
};

// Detect congratulations type from operation context
export const detectCongratulationType = (
  operation: string,
  context?: string
): CongratulationType => {
  const operationLower = operation.toLowerCase();
  const contextLower = context?.toLowerCase() || '';

  if (operationLower.includes('buy') || operationLower.includes('purchase')) {
    return 'purchase_success';
  }
  
  if (operationLower.includes('sell') || operationLower.includes('release') || contextLower.includes('payment')) {
    return 'funds_released';
  }
  
  if (operationLower.includes('refund')) {
    return 'refund_processed';
  }
  
  if (operationLower.includes('dispute')) {
    return 'dispute_resolved';
  }
  
  if (operationLower.includes('escrow') && contextLower.includes('create')) {
    return 'escrow_created';
  }
  
  if (operationLower.includes('transaction') || operationLower.includes('complete')) {
    return 'transaction_complete';
  }
  
  return 'general_success';
};

// Example usage patterns
export const USAGE_EXAMPLES = {
  // Replace simple alert
  replaceAlert: `
// Before:
alert('Purchase successful!');

// After:
congratulationsModal.showPurchaseSuccess(txId, amount, chatRoomId);
  `,
  
  // Replace success toast
  replaceToast: `
// Before:
showSuccessToast('Transaction completed');

// After:
congratulationsModal.showTransactionComplete(txId, amount);
  `,
  
  // Custom configuration
  customConfig: `
congratulationsModal.showCongratulations({
  type: 'purchase_success',
  title: 'Welcome to the Community!',
  message: 'Your first purchase was successful.',
  actions: createCommonActions('purchase', {
    onChat: () => openChat(),
    onViewDetails: () => viewPurchase()
  }),
  metrics: createSuccessMetrics({
    amount: '2.5 SOL',
    fee: '0.001 SOL',
    processingTime: '2.3s'
  }),
  showConfetti: true,
  autoDismiss: false
});
  `
};
