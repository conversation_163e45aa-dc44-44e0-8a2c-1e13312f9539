import { motion } from 'framer-motion';
import { MessageCircle, FileText } from 'lucide-react';
import React from 'react';
import { useTranslation } from '../../../hooks/useTranslation';

interface CommentTabProps {
  activeTab: 'comments' | 'about';
  onTabChange: (tab: 'comments' | 'about') => void;
  commentCount: number;
}

export const CommentTab: React.FC<CommentTabProps> = ({
  activeTab,
  onTabChange,
  commentCount,
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex w-full border-b border-gray-200 bg-gray-50">
      <motion.div
        className={`flex-1 h-16 flex justify-center items-center gap-2 cursor-pointer transition-all duration-300 ${
          activeTab === 'comments'
            ? 'bg-white text-gray-900 border-b-2 border-[#F58A38]'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
        }`}
        onClick={() => onTabChange('comments')}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <MessageCircle
          size={20}
          className={activeTab === 'comments' ? 'text-[#F58A38]' : 'text-gray-500'}
        />
        <div className="text-lg font-semibold">
          {t('comments.commentsTab', { count: commentCount })}
        </div>
      </motion.div>

      <motion.div
        className={`flex-1 h-16 flex justify-center items-center gap-2 cursor-pointer transition-all duration-300 ${
          activeTab === 'about'
            ? 'bg-white text-gray-900 border-b-2 border-[#F58A38]'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
        }`}
        onClick={() => onTabChange('about')}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <FileText
          size={20}
          className={activeTab === 'about' ? 'text-[#F58A38]' : 'text-gray-500'}
        />
        <div className="text-lg font-semibold">{t('comments.aboutTab')}</div>
      </motion.div>
    </div>
  );
}; 