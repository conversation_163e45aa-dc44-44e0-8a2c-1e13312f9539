"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-fast-marquee";
exports.ids = ["vendor-chunks/react-fast-marquee"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-fast-marquee/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-fast-marquee/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nfunction ___$insertStyle(css) {\n    if (!css || \"undefined\" === 'undefined') {\n        return;\n    }\n    const style = document.createElement('style');\n    style.setAttribute('type', 'text/css');\n    style.innerHTML = css;\n    document.head.appendChild(style);\n    return css;\n}\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n___$insertStyle(\".rfm-marquee-container {\\n  overflow-x: hidden;\\n  display: flex;\\n  flex-direction: row;\\n  position: relative;\\n  width: var(--width);\\n  transform: var(--transform);\\n}\\n.rfm-marquee-container:hover div {\\n  animation-play-state: var(--pause-on-hover);\\n}\\n.rfm-marquee-container:active div {\\n  animation-play-state: var(--pause-on-click);\\n}\\n\\n.rfm-overlay {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n}\\n.rfm-overlay::before, .rfm-overlay::after {\\n  background: linear-gradient(to right, var(--gradient-color), rgba(255, 255, 255, 0));\\n  content: \\\"\\\";\\n  height: 100%;\\n  position: absolute;\\n  width: var(--gradient-width);\\n  z-index: 2;\\n  pointer-events: none;\\n  touch-action: none;\\n}\\n.rfm-overlay::after {\\n  right: 0;\\n  top: 0;\\n  transform: rotateZ(180deg);\\n}\\n.rfm-overlay::before {\\n  left: 0;\\n  top: 0;\\n}\\n\\n.rfm-marquee {\\n  flex: 0 0 auto;\\n  min-width: var(--min-width);\\n  z-index: 1;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  animation: scroll var(--duration) linear var(--delay) var(--iteration-count);\\n  animation-play-state: var(--play);\\n  animation-delay: var(--delay);\\n  animation-direction: var(--direction);\\n}\\n@keyframes scroll {\\n  0% {\\n    transform: translateX(0%);\\n  }\\n  100% {\\n    transform: translateX(-100%);\\n  }\\n}\\n\\n.rfm-initial-child-container {\\n  flex: 0 0 auto;\\n  display: flex;\\n  min-width: auto;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n\\n.rfm-child {\\n  transform: var(--transform);\\n}\");\nconst Marquee = React.forwardRef(function Marquee({ style = {}, className = \"\", autoFill = false, play = true, pauseOnHover = false, pauseOnClick = false, direction = \"left\", speed = 50, delay = 0, loop = 0, gradient = false, gradientColor = \"white\", gradientWidth = 200, onFinish, onCycleComplete, onMount, children }, ref) {\n    // React Hooks\n    const [containerWidth, setContainerWidth] = React.useState(0);\n    const [marqueeWidth, setMarqueeWidth] = React.useState(0);\n    const [multiplier, setMultiplier] = React.useState(1);\n    const [isMounted, setIsMounted] = React.useState(false);\n    const rootRef = React.useRef(null);\n    const containerRef = ref || rootRef;\n    const marqueeRef = React.useRef(null);\n    // Calculate width of container and marquee and set multiplier\n    const calculateWidth = React.useCallback({\n        \"Marquee.Marquee.useCallback[calculateWidth]\": ()=>{\n            if (marqueeRef.current && containerRef.current) {\n                const containerRect = containerRef.current.getBoundingClientRect();\n                const marqueeRect = marqueeRef.current.getBoundingClientRect();\n                let containerWidth = containerRect.width;\n                let marqueeWidth = marqueeRect.width;\n                // Swap width and height if direction is up or down\n                if (direction === \"up\" || direction === \"down\") {\n                    containerWidth = containerRect.height;\n                    marqueeWidth = marqueeRect.height;\n                }\n                if (autoFill && containerWidth && marqueeWidth) {\n                    setMultiplier(marqueeWidth < containerWidth ? Math.ceil(containerWidth / marqueeWidth) : 1);\n                } else {\n                    setMultiplier(1);\n                }\n                setContainerWidth(containerWidth);\n                setMarqueeWidth(marqueeWidth);\n            }\n        }\n    }[\"Marquee.Marquee.useCallback[calculateWidth]\"], [\n        autoFill,\n        containerRef,\n        direction\n    ]);\n    // Calculate width and multiplier on mount and on window resize\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            if (!isMounted) return;\n            calculateWidth();\n            if (marqueeRef.current && containerRef.current) {\n                const resizeObserver = new ResizeObserver({\n                    \"Marquee.Marquee.useEffect\": ()=>calculateWidth()\n                }[\"Marquee.Marquee.useEffect\"]);\n                resizeObserver.observe(containerRef.current);\n                resizeObserver.observe(marqueeRef.current);\n                return ({\n                    \"Marquee.Marquee.useEffect\": ()=>{\n                        if (!resizeObserver) return;\n                        resizeObserver.disconnect();\n                    }\n                })[\"Marquee.Marquee.useEffect\"];\n            }\n        }\n    }[\"Marquee.Marquee.useEffect\"], [\n        calculateWidth,\n        containerRef,\n        isMounted\n    ]);\n    // Recalculate width when children change\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            calculateWidth();\n        }\n    }[\"Marquee.Marquee.useEffect\"], [\n        calculateWidth,\n        children\n    ]);\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"Marquee.Marquee.useEffect\"], []);\n    // Runs the onMount callback, if it is a function, when Marquee is mounted.\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            if (typeof onMount === \"function\") {\n                onMount();\n            }\n        }\n    }[\"Marquee.Marquee.useEffect\"], []);\n    // Animation duration\n    const duration = React.useMemo({\n        \"Marquee.Marquee.useMemo[duration]\": ()=>{\n            if (autoFill) {\n                return marqueeWidth * multiplier / speed;\n            } else {\n                return marqueeWidth < containerWidth ? containerWidth / speed : marqueeWidth / speed;\n            }\n        }\n    }[\"Marquee.Marquee.useMemo[duration]\"], [\n        autoFill,\n        containerWidth,\n        marqueeWidth,\n        multiplier,\n        speed\n    ]);\n    const containerStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[containerStyle]\": ()=>Object.assign(Object.assign({}, style), {\n                [\"--pause-on-hover\"]: !play || pauseOnHover ? \"paused\" : \"running\",\n                [\"--pause-on-click\"]: !play || pauseOnHover && !pauseOnClick || pauseOnClick ? \"paused\" : \"running\",\n                [\"--width\"]: direction === \"up\" || direction === \"down\" ? `100vh` : \"100%\",\n                [\"--transform\"]: direction === \"up\" ? \"rotate(-90deg)\" : direction === \"down\" ? \"rotate(90deg)\" : \"none\"\n            })\n    }[\"Marquee.Marquee.useMemo[containerStyle]\"], [\n        style,\n        play,\n        pauseOnHover,\n        pauseOnClick,\n        direction\n    ]);\n    const gradientStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[gradientStyle]\": ()=>({\n                [\"--gradient-color\"]: gradientColor,\n                [\"--gradient-width\"]: typeof gradientWidth === \"number\" ? `${gradientWidth}px` : gradientWidth\n            })\n    }[\"Marquee.Marquee.useMemo[gradientStyle]\"], [\n        gradientColor,\n        gradientWidth\n    ]);\n    const marqueeStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[marqueeStyle]\": ()=>({\n                [\"--play\"]: play ? \"running\" : \"paused\",\n                [\"--direction\"]: direction === \"left\" ? \"normal\" : \"reverse\",\n                [\"--duration\"]: `${duration}s`,\n                [\"--delay\"]: `${delay}s`,\n                [\"--iteration-count\"]: !!loop ? `${loop}` : \"infinite\",\n                [\"--min-width\"]: autoFill ? `auto` : \"100%\"\n            })\n    }[\"Marquee.Marquee.useMemo[marqueeStyle]\"], [\n        play,\n        direction,\n        duration,\n        delay,\n        loop,\n        autoFill\n    ]);\n    const childStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[childStyle]\": ()=>({\n                [\"--transform\"]: direction === \"up\" ? \"rotate(90deg)\" : direction === \"down\" ? \"rotate(-90deg)\" : \"none\"\n            })\n    }[\"Marquee.Marquee.useMemo[childStyle]\"], [\n        direction\n    ]);\n    // Render {multiplier} number of children\n    const multiplyChildren = React.useCallback({\n        \"Marquee.Marquee.useCallback[multiplyChildren]\": (multiplier)=>{\n            return [\n                ...Array(Number.isFinite(multiplier) && multiplier >= 0 ? multiplier : 0)\n            ].map({\n                \"Marquee.Marquee.useCallback[multiplyChildren]\": (_, i)=>React__default['default'].createElement(React.Fragment, {\n                        key: i\n                    }, React.Children.map(children, {\n                        \"Marquee.Marquee.useCallback[multiplyChildren]\": (child)=>{\n                            return React__default['default'].createElement(\"div\", {\n                                style: childStyle,\n                                className: \"rfm-child\"\n                            }, child);\n                        }\n                    }[\"Marquee.Marquee.useCallback[multiplyChildren]\"]))\n            }[\"Marquee.Marquee.useCallback[multiplyChildren]\"]);\n        }\n    }[\"Marquee.Marquee.useCallback[multiplyChildren]\"], [\n        childStyle,\n        children\n    ]);\n    return !isMounted ? null : React__default['default'].createElement(\"div\", {\n        ref: containerRef,\n        style: containerStyle,\n        className: \"rfm-marquee-container \" + className\n    }, gradient && React__default['default'].createElement(\"div\", {\n        style: gradientStyle,\n        className: \"rfm-overlay\"\n    }), React__default['default'].createElement(\"div\", {\n        className: \"rfm-marquee\",\n        style: marqueeStyle,\n        onAnimationIteration: onCycleComplete,\n        onAnimationEnd: onFinish\n    }, React__default['default'].createElement(\"div\", {\n        className: \"rfm-initial-child-container\",\n        ref: marqueeRef\n    }, React.Children.map(children, (child)=>{\n        return React__default['default'].createElement(\"div\", {\n            style: childStyle,\n            className: \"rfm-child\"\n        }, child);\n    })), multiplyChildren(multiplier - 1)), React__default['default'].createElement(\"div\", {\n        className: \"rfm-marquee\",\n        style: marqueeStyle\n    }, multiplyChildren(multiplier)));\n});\nexports[\"default\"] = Marquee; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-fast-marquee/dist/index.js\n");

/***/ })

};
;