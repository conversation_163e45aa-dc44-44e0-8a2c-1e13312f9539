"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_EmptyStates_BaseEmptyState_tsx"],{

/***/ "(app-pages-browser)/./src/components/ui/EmptyStates/BaseEmptyState.tsx":
/*!**********************************************************!*\
  !*** ./src/components/ui/EmptyStates/BaseEmptyState.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst EmptyState = (param)=>{\n    let { title, description, icon, action, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"flex flex-col items-center justify-center py-16 px-8 text-center \".concat(className),\n        ..._lib_animations__WEBPACK_IMPORTED_MODULE_1__.fadeInUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"mb-6 p-4 bg-gray-100 rounded-full\",\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    delay: 0.2,\n                    type: 'spring',\n                    stiffness: 200\n                },\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h3, {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.3\n                },\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                className: \"text-gray-600 mb-6 max-w-md\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.4\n                },\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 45,\n                columnNumber: 5\n            }, undefined),\n            action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                className: \"bg-[#FF6600] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a00] transition-colors\",\n                onClick: action.onClick,\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.5\n                },\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: action.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\EmptyStates\\\\BaseEmptyState.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined);\n};\n_c = EmptyState;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmptyState);\nvar _c;\n$RefreshReg$(_c, \"EmptyState\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/EmptyStates/BaseEmptyState.tsx\n"));

/***/ })

}]);