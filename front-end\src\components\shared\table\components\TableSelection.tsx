import { useCallback } from 'react';

interface TableSelectionProps<T = any> {
  selectable: boolean;
  selectedRows: Set<string | number>;
  paginatedData: T[];
  onSelectionChange?: (selectedRows: Set<string | number>) => void;
}

export const useTableSelection = <T extends { id: string | number }>({
  selectable,
  selectedRows,
  paginatedData,
  onSelectionChange,
}: TableSelectionProps<T>) => {
  const handleSelectAll = useCallback(() => {
    if (!selectable || !onSelectionChange) return;

    const allSelected = paginatedData.every((row) => selectedRows.has(row.id));
    const newSelection = new Set(selectedRows);

    if (allSelected) {
      paginatedData.forEach((row) => newSelection.delete(row.id));
    } else {
      paginatedData.forEach((row) => newSelection.add(row.id));
    }

    onSelectionChange(newSelection);
  }, [selectable, onSelectionChange, paginatedData, selectedRows]);

  const handleRowSelect = useCallback(
    (rowId: string | number) => {
      if (!selectable || !onSelectionChange) return;

      const newSelection = new Set(selectedRows);
      if (newSelection.has(rowId)) {
        newSelection.delete(rowId);
      } else {
        newSelection.add(rowId);
      }

      onSelectionChange(newSelection);
    },
    [selectable, onSelectionChange, selectedRows]
  );

  return {
    handleSelectAll,
    handleRowSelect,
  };
}; 