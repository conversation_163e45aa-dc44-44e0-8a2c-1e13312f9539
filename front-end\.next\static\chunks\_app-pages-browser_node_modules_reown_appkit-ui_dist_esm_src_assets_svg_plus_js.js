"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_plus_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plusSvg: () => (/* binding */ plusSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst plusSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"13\"\n  height=\"12\"\n  viewBox=\"0 0 13 12\"\n  fill=\"none\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M0.794373 5.99982C0.794373 5.52643 1.17812 5.14268 1.6515 5.14268H5.643V1.15109C5.643 0.677701 6.02675 0.293946 6.50012 0.293945C6.9735 0.293946 7.35725 0.677701 7.35725 1.15109V5.14268H11.3488C11.8221 5.14268 12.2059 5.52643 12.2059 5.99982C12.2059 6.47321 11.8221 6.85696 11.3488 6.85696H7.35725V10.8486C7.35725 11.3219 6.9735 11.7057 6.50012 11.7057C6.02675 11.7057 5.643 11.3219 5.643 10.8486V6.85696H1.6515C1.17812 6.85696 0.794373 6.47321 0.794373 5.99982Z\"\n  /></svg\n>`;\n//# sourceMappingURL=plus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3BsdXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsZ0JBQWdCLHdDQUFHO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxAcmVvd25cXGFwcGtpdC11aVxcZGlzdFxcZXNtXFxzcmNcXGFzc2V0c1xcc3ZnXFxwbHVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgcGx1c1N2ZyA9IHN2ZyBgPHN2Z1xuICB3aWR0aD1cIjEzXCJcbiAgaGVpZ2h0PVwiMTJcIlxuICB2aWV3Qm94PVwiMCAwIDEzIDEyXCJcbiAgZmlsbD1cIm5vbmVcIlxuPlxuICA8cGF0aFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgIGZpbGwtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGNsaXAtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGQ9XCJNMC43OTQzNzMgNS45OTk4MkMwLjc5NDM3MyA1LjUyNjQzIDEuMTc4MTIgNS4xNDI2OCAxLjY1MTUgNS4xNDI2OEg1LjY0M1YxLjE1MTA5QzUuNjQzIDAuNjc3NzAxIDYuMDI2NzUgMC4yOTM5NDYgNi41MDAxMiAwLjI5Mzk0NUM2Ljk3MzUgMC4yOTM5NDYgNy4zNTcyNSAwLjY3NzcwMSA3LjM1NzI1IDEuMTUxMDlWNS4xNDI2OEgxMS4zNDg4QzExLjgyMjEgNS4xNDI2OCAxMi4yMDU5IDUuNTI2NDMgMTIuMjA1OSA1Ljk5OTgyQzEyLjIwNTkgNi40NzMyMSAxMS44MjIxIDYuODU2OTYgMTEuMzQ4OCA2Ljg1Njk2SDcuMzU3MjVWMTAuODQ4NkM3LjM1NzI1IDExLjMyMTkgNi45NzM1IDExLjcwNTcgNi41MDAxMiAxMS43MDU3QzYuMDI2NzUgMTEuNzA1NyA1LjY0MyAxMS4zMjE5IDUuNjQzIDEwLjg0ODZWNi44NTY5NkgxLjY1MTVDMS4xNzgxMiA2Ljg1Njk2IDAuNzk0MzczIDYuNDczMjEgMC43OTQzNzMgNS45OTk4MlpcIlxuICAvPjwvc3ZnXG4+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBsdXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js\n"));

/***/ })

}]);