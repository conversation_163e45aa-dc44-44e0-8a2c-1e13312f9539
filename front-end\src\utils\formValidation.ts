// Common form validation functions that can be shared across different forms

/**
 * Validates if a name field is not empty
 */
export const validateName = (name: string): string => {
  if (!name.trim()) return "Name is required";
  return "";
};

/**
 * Validates if a name field is not empty and has minimum length
 */
export const validateNameWithMinLength = (name: string, minLength: number = 3): string => {
  if (!name.trim()) return "Name is required";
  if (name.length < minLength) return `Name must be at least ${minLength} characters`;
  return "";
};

/**
 * Validates if a price field is a valid positive number
 */
export const validatePrice = (price: string): string => {
  if (!price.trim()) return "Price is required";
  const numPrice = parseFloat(price);
  if (isNaN(numPrice) || numPrice < 0 || !isFinite(numPrice)) return "Please enter a valid price";
  return "";
};

/**
 * Validates if a URL is properly formatted (optional field)
 */
export const validateUrl = (url: string, fieldName: string = "URL"): string => {
  if (!url) return "";
  const urlPattern = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+(\/[a-zA-Z0-9-_.~:/?#[\]@!$&'()*+,;=]*)?$/;
  if (!urlPattern.test(url)) return `Please enter a valid ${fieldName}`;
  return "";
};

/**
 * Validates if a stock amount is a valid positive integer (when limited stock is enabled)
 */
export const validateStockAmount = (amount: string, limitedStock: boolean): string => {
  if (!limitedStock) return "";
  if (!amount.trim()) return "Stock amount is required";
  const numAmount = parseFloat(amount);
  if (isNaN(numAmount) || numAmount <= 0 || !Number.isInteger(numAmount)) return "Please enter a valid stock amount";
  return "";
};

/**
 * Validates if a ticker symbol meets the requirements
 */
export const validateTicker = (ticker: string): string => {
  if (!ticker.trim()) return "Ticker is required";
  if (ticker.length < 2 || ticker.length > 5)
    return "Ticker must be 2-5 characters";
  if (!/^[A-Z0-9]+$/.test(ticker))
    return "Ticker must contain only uppercase letters and numbers";
  return "";
};

/**
 * Validates social media handles
 */
export const validateSocialHandle = (handle: string, platform: string): string => {
  if (!handle) return "";
  if (platform === "telegram" && !/^@?[a-zA-Z0-9_]{5,}$/.test(handle)) {
    return "Please enter a valid Telegram username";
  }
  if (platform === "twitter" && !/^@?[a-zA-Z0-9_]{1,15}$/.test(handle)) {
    return "Please enter a valid Twitter/X username";
  }
  return "";
};

import { UPLOAD_CONFIG } from '@/config/environment';

/**
 * Validates image file requirements
 */
export const validateImage = (file: File | null, maxSizeMB?: number): string => {
  if (!file) return "Please upload an image";
  
  const maxSize = maxSizeMB ? maxSizeMB * 1024 * 1024 : UPLOAD_CONFIG.MAX_FILE_SIZE;
  if (file.size > maxSize) {
    return `Image must be less than ${Math.round(maxSize / (1024 * 1024))}MB`;
  }
  
  if (!UPLOAD_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
    return `Only ${UPLOAD_CONFIG.ALLOWED_FILE_TYPES.map(type => type.split('/')[1].toUpperCase()).join(', ')} images are allowed`;
  }
  return "";
};

/**
 * Validates if user is logged in
 */
export const validateUserLogin = (userId: number): string => {
  return userId === 0 ? "Please log in first before Creating a token." : "";
};

/**
 * Validates if user is logged in using session token
 */
export const validateUserSession = (): string => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('token');
    return !token ? "Please log in first before proceeding." : "";
  }
  return "Please log in first before proceeding.";
};

/**
 * Checks if there are any errors in the form
 */
export const hasErrors = (errors: any): boolean => {
  return Object.values(errors).some((error: any) => error && error !== "");
};

/**
 * Alternative function name for checking form validity
 */
export const isFormValid = (errors: any): boolean => {
  return !Object.values(errors).some((error: any) => error && error !== "");
}; 