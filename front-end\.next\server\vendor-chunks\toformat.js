/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/toformat";
exports.ids = ["vendor-chunks/toformat"];
exports.modules = {

/***/ "(ssr)/./node_modules/toformat/toFormat.js":
/*!*******************************************!*\
  !*** ./node_modules/toformat/toFormat.js ***!
  \*******************************************/
/***/ ((module) => {

eval("/*\r\n *  toFormat v2.0.0\r\n *  Adds a toFormat instance method to big.js or decimal.js\r\n *  Copyright (c) 2017 Michael Mclaughlin\r\n *  MIT Licence\r\n */\r\n\r\n /*\r\n * Adds a `toFormat` method to `Ctor.prototype` and a `format` object to `Ctor`, where `Ctor` is\r\n * a big number constructor such as `Decimal` (decimal.js) or `Big` (big.js).\r\n */\r\nfunction toFormat(Ctor) {\r\n  'use strict';\r\n\r\n  /*\r\n   *  Returns a string representing the value of this big number in fixed-point notation to `dp`\r\n   *  decimal places using rounding mode `rm`, and formatted according to the properties of the\r\n   * `fmt`, `this.format` and `this.constructor.format` objects, in that order of precedence.\r\n   *\r\n   *  Example:\r\n   *\r\n   *  x = new Decimal('123456789.987654321')\r\n   *\r\n   *  // Add a format object to the constructor...\r\n   *  Decimal.format = {\r\n   *    decimalSeparator: '.',\r\n   *    groupSeparator: ',',\r\n   *    groupSize: 3,\r\n   *    secondaryGroupSize: 0,\r\n   *    fractionGroupSeparator: '',     // '\\xA0' non-breaking space\r\n   *    fractionGroupSize : 0\r\n   *  }\r\n   *\r\n   *  x.toFormat();                // 123,456,789.987654321\r\n   *  x.toFormat(2, 1);            // 123,456,789.98\r\n   *\r\n   *  // And/or add a format object to the big number itself...\r\n   *  x.format = {\r\n   *    decimalSeparator: ',',\r\n   *    groupSeparator: '',\r\n   *  }\r\n   *\r\n   *  x.toFormat();                // 123456789,987654321\r\n   *\r\n   *  format = {\r\n   *    decimalSeparator: '.',\r\n   *    groupSeparator: ' ',\r\n   *    groupSize: 3,\r\n   *    fractionGroupSeparator: ' ',     // '\\xA0' non-breaking space\r\n   *    fractionGroupSize : 5\r\n   *  }\r\n\r\n   *  // And/or pass a format object to the method call.\r\n   *  x.toFormat(format);          // 123 456 789.98765 4321\r\n   *  x.toFormat(4, format);       // 123 456 789.9877\r\n   *  x.toFormat(2, 1, format);    // 123 456 789.98\r\n   *\r\n   *  [dp] {number} Decimal places. Integer.\r\n   *  [rm] {number} Rounding mode. Integer, 0 to 8. (Ignored if using big.js.)\r\n   *  [fmt] {Object} A format object.\r\n   *\r\n   */\r\n  Ctor.prototype.toFormat = function toFormat(dp, rm, fmt) {\r\n\r\n    if (!this.e && this.e !== 0) return this.toString();   // Infinity/NaN\r\n\r\n    var arr, g1, g2, i,\r\n      u,                             // undefined\r\n      nd,                            // number of integer digits\r\n      intd,                          // integer digits\r\n      intp,                          // integer part\r\n      fracp,                         // fraction part\r\n      dsep,                          // decimalSeparator\r\n      gsep,                          // groupSeparator\r\n      gsize,                         // groupSize\r\n      sgsize,                        // secondaryGroupSize\r\n      fgsep,                         // fractionGroupSeparator\r\n      fgsize,                        // fractionGroupSize\r\n      tfmt = this.format || {},\r\n      cfmt = this.constructor.format || {};\r\n\r\n    if (dp != u) {\r\n      if (typeof dp == 'object') {\r\n        fmt = dp;\r\n        dp = u;\r\n      } else if (rm != u) {\r\n        if (typeof rm == 'object') {\r\n          fmt = rm;\r\n          rm = u;\r\n        } else if (typeof fmt != 'object') {\r\n          fmt = {};\r\n        }\r\n      } else {\r\n        fmt = {};\r\n      }\r\n    } else {\r\n      fmt = {};\r\n    }\r\n\r\n    arr = this.toFixed(dp, rm).split('.');\r\n    intp = arr[0];\r\n    fracp = arr[1];\r\n    intd = this.s < 0 ? intp.slice(1) : intp;\r\n    nd = intd.length;\r\n\r\n    dsep = fmt.decimalSeparator;\r\n    if (dsep == u) {\r\n      dsep = tfmt.decimalSeparator;\r\n      if (dsep == u) {\r\n        dsep = cfmt.decimalSeparator;\r\n        if (dsep == u) dsep = '.';\r\n      }\r\n    }\r\n\r\n    gsep = fmt.groupSeparator;\r\n    if (gsep == u) {\r\n      gsep = tfmt.groupSeparator;\r\n      if (gsep == u) gsep = cfmt.groupSeparator;\r\n    }\r\n\r\n    if (gsep) {\r\n      gsize = fmt.groupSize;\r\n      if (gsize == u) {\r\n        gsize = tfmt.groupSize;\r\n        if (gsize == u) {\r\n          gsize = cfmt.groupSize;\r\n          if (gsize == u) gsize = 0;\r\n        }\r\n      }\r\n\r\n      sgsize = fmt.secondaryGroupSize;\r\n      if (sgsize == u) {\r\n        sgsize = tfmt.secondaryGroupSize;\r\n        if (sgsize == u) {\r\n          sgsize = cfmt.secondaryGroupSize;\r\n          if (sgsize == u) sgsize = 0;\r\n        }\r\n      }\r\n\r\n      if (sgsize) {\r\n        g1 = +sgsize;\r\n        g2 = +gsize;\r\n        nd -= g2;\r\n      } else {\r\n        g1 = +gsize;\r\n        g2 = +sgsize;\r\n      }\r\n\r\n      if (g1 > 0 && nd > 0) {\r\n        i = nd % g1 || g1;\r\n        intp = intd.substr(0, i);\r\n        for (; i < nd; i += g1) intp += gsep + intd.substr(i, g1);\r\n        if (g2 > 0) intp += gsep + intd.slice(i);\r\n        if (this.s < 0) intp = '-' + intp;\r\n      }\r\n    }\r\n\r\n    if (fracp) {\r\n      fgsep = fmt.fractionGroupSeparator;\r\n      if (fgsep == u) {\r\n        fgsep = tfmt.fractionGroupSeparator;\r\n        if (fgsep == u) fgsep = cfmt.fractionGroupSeparator;\r\n      }\r\n\r\n      if (fgsep) {\r\n        fgsize = fmt.fractionGroupSize;\r\n        if (fgsize == u) {\r\n          fgsize = tfmt.fractionGroupSize;\r\n          if (fgsize == u) {\r\n            fgsize = cfmt.fractionGroupSize;\r\n            if (fgsize == u) fgsize = 0;\r\n          }\r\n        }\r\n\r\n        fgsize = +fgsize;\r\n\r\n        if (fgsize) {\r\n          fracp = fracp.replace(new RegExp('\\\\d{' + fgsize + '}\\\\B', 'g'), '$&' + fgsep);\r\n        }\r\n      }\r\n\r\n      return intp + dsep + fracp;\r\n    } else {\r\n\r\n      return intp;\r\n    }\r\n  };\r\n\r\n  Ctor.format = {\r\n    decimalSeparator: '.',\r\n    groupSeparator: ',',\r\n    groupSize: 3,\r\n    secondaryGroupSize: 0,\r\n    fractionGroupSeparator: '',\r\n    fractionGroupSize: 0\r\n  };\r\n\r\n  return Ctor;\r\n}\r\n\r\nif ( true && module.exports) module.exports = toFormat;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/toformat/toFormat.js\n");

/***/ })

};
;