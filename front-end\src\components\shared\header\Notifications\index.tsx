import { useEffect, useRef, useState } from "react";
import { getNotifications } from "@/axios/requests";
import { useAppContext } from '@/contexts/AppContext';
import { useTranslation } from '@/hooks/useTranslation';
import { usePrivy } from "@privy-io/react-auth";






interface NotificationModalProps {
  onClose: () => void;
  onOpenChat: (chatRoomId: string, receiverId: number) => void;
}

// Proper notification interface matching the API
interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  data: any;
  priority: string;
  isRead: boolean;
  createdAt: string;
}
const NotificationModal = ({ onClose, onOpenChat }: NotificationModalProps) => {
  const { t } = useTranslation();
  const { user } = usePrivy();

  // Helper function to determine if a notification is trade-related (same as NotificationBell)
  const isTradeNotification = (notification: Notification): boolean => {
    // System notifications (non-trade) - these go to System tab
    const systemTypes = [
      'system_announcement',
      'moderator_added',
      'perk_created',
      'token_created',
      // Moderator-specific notifications (go to moderator dashboard)
      'dispute_created',
      'dispute_assigned'
    ];

    // If it's a system type, it's NOT a trade notification
    if (systemTypes.includes(notification.type)) {
      return false;
    }

    // All escrow, trade, and user dispute notifications go to Trade tab
    const tradeTypes = [
      // Trade and escrow notifications
      'perk_purchased',
      'perk_sold',
      'escrow_created',
      'escrow_released',
      'escrow_release_reminder',
      'trade_completed',
      'trade_refunded',
      'trade_reported',
      'trade_disputed',
      // User dispute notifications (trade-related)
      'dispute_resolved',
      // Chat notifications (trade-related)
      'chat_message'
    ];

    // Return true if it's a trade type
    return tradeTypes.includes(notification.type);
  };

    const [selectedTab, setSelectedTab] = useState<'system' | 'trade'>('system');
    const [systemNotifications, setSystemNotifications] = useState<Notification[]>([]);
    const [tradeNotifications, setTradeNotifications] = useState<Notification[]>([]);
    const modalRef = useRef<HTMLDivElement>(null);

    const { setUnreadSystemNotificationsCount } = useAppContext();

    useEffect(() => {
        const fetchNotifications = async () => {
            if (!user?.id) {
                console.log("No user ID available");
                return;
            }

            try {
                console.log("Fetching notifications for user:", user.id);

                // Fetch all notifications using the same API as NotificationBell
                const response = await getNotifications({ page: 1, pageSize: 50 });

                if (response.status === 200) {
                    const allNotifications = response.data.notifications;
                    console.log("All notifications fetched:", allNotifications);

                    // Separate system and trade notifications using the same logic as NotificationBell
                    const systemNots: Notification[] = [];
                    const tradeNots: Notification[] = [];

                    allNotifications.forEach((notification: Notification) => {
                        if (isTradeNotification(notification)) {
                            tradeNots.push(notification);
                        } else {
                            systemNots.push(notification);
                        }
                    });

                    console.log("Categorized notifications:", {
                        system: systemNots.length,
                        trade: tradeNots.length
                    });

                    setSystemNotifications(systemNots);
                    setTradeNotifications(tradeNots);
                    setUnreadSystemNotificationsCount(systemNots.filter(n => !n.isRead).length);
                }
            } catch (e) {
                console.error("Error fetching notifications:", e);
            }
        };

        fetchNotifications();
    }, [user?.id]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    const getTypeStyles = (type: 'buy' | 'sell' | 'airdrop' | 'burn' | 'general') => {
        switch (type) {
            case 'buy':
                return {
                    bg: 'bg-green-50 hover:bg-green-100',
                    border: 'border-l-4 border-l-green-500',
                    icon: 'text-green-600',
                    typeText: 'text-green-700 bg-green-100'
                };
            case 'sell':
                return {
                    bg: 'bg-red-50 hover:bg-red-100',
                    border: 'border-l-4 border-l-red-500',
                    icon: 'text-red-600',
                    typeText: 'text-red-700 bg-red-100'
                };
            case 'airdrop':
                return {
                    bg: 'bg-blue-50 hover:bg-blue-100',
                    border: 'border-l-4 border-l-blue-500',
                    icon: 'text-blue-600',
                    typeText: 'text-blue-700 bg-blue-100'
                };
            case 'burn':
                return {
                    bg: 'bg-orange-50 hover:bg-orange-100',
                    border: 'border-l-4 border-l-orange-500',
                    icon: 'text-orange-600',
                    typeText: 'text-orange-700 bg-orange-100'
                };
            case 'general':
                return {
                    bg: 'bg-gray-50 hover:bg-gray-100',
                    border: 'border-l-4 border-l-gray-500',
                    icon: 'text-gray-600',
                    typeText: 'text-gray-700 bg-gray-100'
                };
            default:
                return {
                    bg: 'bg-gray-50 hover:bg-gray-100',
                    border: 'border-l-4 border-l-gray-500',
                    icon: 'text-gray-600',
                    typeText: 'text-gray-700 bg-gray-100'
                };
        }
    };

    const formatTimeAgo = (timestamp: Date) => {
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    };

    // Helper to get notifications for the current tab
    let notifications: Notification[] = selectedTab === 'trade' ? tradeNotifications : systemNotifications;

    return (
        <div className="fixed inset-0 flex items-start justify-end bg-black/20 backdrop-blur-sm z-50">
            <div 
                className="flex flex-col bg-white rounded-xl shadow-2xl w-[420px] max-h-[600px] mt-20 mr-6 border border-gray-200 overflow-hidden" 
                ref={modalRef}
            >
                {/* Header with Tabs */}
                <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-[#FF6600] to-[#FF8533]">
                    <div>
                        <h2 className="text-white font-semibold text-lg">{t('navigation.notifications')}</h2>
                        <div className="flex gap-2 mt-2">
                            <button
                                className={`px-3 py-1 rounded-full text-sm font-medium ${selectedTab === 'trade' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'}`}
                                onClick={() => setSelectedTab('trade')}
                            >
                                {t('notifications.trade')}
                            </button>
                            <button
                                className={`px-3 py-1 rounded-full text-sm font-medium ${selectedTab === 'system' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'}`}
                                onClick={() => setSelectedTab('system')}
                            >
                                {t('notifications.system')}
                            </button>
                        </div>
                    </div>
                    <button 
                        onClick={onClose}
                        className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 group"
                    >
                        <svg className="w-4 h-4 text-white group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Notifications List (uses notifications for current tab) */}
                <div className="flex-1 overflow-y-auto max-h-[500px]">
                    {notifications.length === 0 ? (
                        <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                </svg>
                            </div>
                            <h3 className="font-medium text-gray-900 mb-2">{t('notifications.noNotifications')}</h3>
                            <p className="text-sm text-gray-500 text-center">{t('notifications.allCaughtUp')}</p>
                        </div>
                    ) : (
                        <div className="p-4 space-y-3">
                            {notifications.map((notification: Notification, index: number) => {
                                // Map notification types to Message types for styling
                                const messageType = notification.type === 'chat_message' ? 'general' :
                                                  ['perk_purchased', 'perk_sold'].includes(notification.type) ? 'buy' : 'general';
                                const styles = getTypeStyles(messageType);

                                // Get icon based on notification type
                                const getNotificationIcon = (type: string) => {
                                  switch (type) {
                                    case 'chat_message': return '💬';
                                    case 'perk_purchased': return '🛒';
                                    case 'perk_sold': return '💰';
                                    case 'escrow_created': return '🔒';
                                    case 'escrow_released': return '✅';
                                    case 'system_announcement': return '📢';
                                    case 'perk_created': return '🆕';
                                    case 'token_created': return '🪙';
                                    case 'moderator_added': return '👮';
                                    default: return '📋';
                                  }
                                };
                                return (
                                    <div
                                        key={notification.id || `notif-fallback-${index}`}
                                        className={`${styles.bg} ${styles.border} p-4 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-[1.02] cursor-pointer group border border-gray-100`}
                                        style={{
                                            animationDelay: `${index * 100}ms`
                                        }}
                                        onClick={() => {
                                          console.log('Notification clicked:', notification);

                                          // Handle chat notifications using the proper data structure
                                          if (notification.type === 'chat_message' && notification.data?.chatRoomId) {
                                            console.log('Opening chat with chatRoomId:', notification.data.chatRoomId);
                                            const receiverId = notification.data.senderId; // The sender becomes the receiver for the chat
                                            onOpenChat(notification.data.chatRoomId, receiverId);
                                            onClose();
                                          }
                                        }}
                                    >
                                        <div className="flex items-start gap-3">
                                            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-xl ${styles.icon} bg-white/60 group-hover:bg-white/80 transition-all duration-200`}>
                                                {getNotificationIcon(notification.type)}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-gray-900 font-medium text-sm leading-relaxed group-hover:text-gray-800 transition-colors duration-200">
                                                    {notification.title}
                                                </p>
                                                <p className="text-gray-600 text-xs mt-1">
                                                    {notification.message}
                                                </p>
                                                <div className="flex items-center justify-between mt-2">
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${styles.typeText} capitalize`}>
                                                        {notification.type.replace('_', ' ')}
                                                    </span>
                                                    <span className="text-xs text-gray-500 font-medium">
                                                        {formatTimeAgo(new Date(notification.createdAt))}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="w-2 h-2 bg-[#FF6600] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-100 bg-gray-50">
                    <button className="w-full py-2 px-4 bg-[#FF6600] hover:bg-[#E55A00] text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]">
                        {t('notifications.markAllAsRead')}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default NotificationModal;   