export interface Coin {
  tokenDetails: {
    tokenId?: string | number;
    tokenAddress?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface Perk {
  perkId: string;
  name: string;
  image: string;
  price: string;
  soldCount?: number | string;
  [key: string]: any;
}

export interface User {
  id: string;
  username?: string;
  avatarUrl?: string;
  [key: string]: any;
} 