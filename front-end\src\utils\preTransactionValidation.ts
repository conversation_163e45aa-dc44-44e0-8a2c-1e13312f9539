import { <PERSON>Key } from '@solana/web3.js';
import { program } from './sol/setup';
import { EscrowTxData, EscrowError } from './escrow';
import { ErrorCategory } from '@/components/ui/ErrorModals';

// Validation Result Interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  category: ErrorCategory;
  message: string;
  details?: string;
  code?: string;
  additionalInfo?: any;
}

export interface ValidationWarning {
  message: string;
  severity: 'low' | 'medium' | 'high';
}

// Network Health Check
export async function checkNetworkHealth(): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  try {
    const startTime = Date.now();
    const connection = program.provider.connection;
    
    // Test basic connectivity
    const slot = await Promise.race([
      connection.getSlot(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Network timeout')), 10000)
      )
    ]) as number;
    
    const latency = Date.now() - startTime;
    
    // Check if we got a valid response
    if (typeof slot !== 'number' || slot <= 0) {
      return {
        isHealthy: false,
        error: 'Invalid network response'
      };
    }
    
    // Consider network unhealthy if latency is too high
    if (latency > 8000) {
      return {
        isHealthy: false,
        latency,
        error: 'Network latency too high'
      };
    }
    
    return {
      isHealthy: true,
      latency
    };
  } catch (error: any) {
    return {
      isHealthy: false,
      error: error.message || 'Network connection failed'
    };
  }
}

// Wallet Validation
export async function validateWalletConnection(wallet: any): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  // Check if wallet exists
  if (!wallet) {
    errors.push({
      category: 'WALLET_CONNECTION',
      message: 'Wallet not connected',
      details: 'Please connect your wallet to continue',
      code: 'WALLET_NOT_CONNECTED'
    });
    return { isValid: false, errors, warnings };
  }
  
  // Check wallet capabilities
  if (typeof wallet.signTransaction !== 'function') {
    errors.push({
      category: 'WALLET_CONNECTION',
      message: 'Wallet does not support transaction signing',
      details: 'Please use a compatible Solana wallet',
      code: 'WALLET_INCOMPATIBLE'
    });
  }
  
  // Check wallet address
  if (!wallet.address) {
    errors.push({
      category: 'WALLET_CONNECTION',
      message: 'Wallet address not available',
      details: 'Please reconnect your wallet',
      code: 'WALLET_ADDRESS_MISSING'
    });
  } else {
    // Validate address format
    try {
      new PublicKey(wallet.address);
    } catch (error) {
      errors.push({
        category: 'WALLET_CONNECTION',
        message: 'Invalid wallet address format',
        details: 'The wallet address appears to be corrupted',
        code: 'WALLET_ADDRESS_INVALID'
      });
    }
  }
  
  // Check if wallet is locked (if possible)
  try {
    if (wallet.publicKey && !wallet.publicKey.equals) {
      warnings.push({
        message: 'Wallet may be locked or disconnected',
        severity: 'medium'
      });
    }
  } catch (error) {
    // Ignore this check if not supported
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Balance Validation
export async function validateBalance(
  walletAddress: string,
  requiredAmount: number,
  tokenMint?: string
): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  try {
    const connection = program.provider.connection;
    const publicKey = new PublicKey(walletAddress);
    
    if (!tokenMint || tokenMint === 'So11111111111111111111111111111111111111112') {
      // Check SOL balance
      const balance = await connection.getBalance(publicKey);
      const solBalance = balance / 1e9; // Convert lamports to SOL

      console.log(`Balance validation: Current=${solBalance.toFixed(4)} SOL, Required=${requiredAmount.toFixed(4)} SOL`);

      if (solBalance < requiredAmount) {
        const shortfall = requiredAmount - solBalance;
        console.log(`Insufficient funds detected: Shortfall=${shortfall.toFixed(4)} SOL`);

        errors.push({
          category: 'INSUFFICIENT_FUNDS',
          message: 'Insufficient SOL balance',
          details: `You need ${requiredAmount.toFixed(4)} SOL but only have ${solBalance.toFixed(4)} SOL`,
          code: 'INSUFFICIENT_SOL',
          additionalInfo: {
            currentBalance: solBalance,
            requiredAmount: requiredAmount,
            shortfall: shortfall
          }
        });
      } else if (solBalance < requiredAmount + 0.005) {
        // Warn if balance is very close to required amount (reduced threshold)
        warnings.push({
          message: `Low SOL balance. Consider keeping extra SOL for transaction fees.`,
          severity: 'medium'
        });
      }
    } else {
      // Check token balance (implementation would depend on token type)
      // For now, we'll add a placeholder
      warnings.push({
        message: 'Token balance validation not fully implemented',
        severity: 'low'
      });
    }
  } catch (error: any) {
    errors.push({
      category: 'NETWORK_ERROR',
      message: 'Failed to check balance',
      details: error.message,
      code: 'BALANCE_CHECK_FAILED'
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Smart Contract State Validation
export async function validateSmartContractState(
  escrowId?: string
): Promise<ValidationResult> {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  try {
    const connection = program.provider.connection;
    
    // Check if program is deployed and accessible
    const programInfo = await connection.getAccountInfo(program.programId);
    if (!programInfo) {
      errors.push({
        category: 'SMART_CONTRACT_ERROR',
        message: 'Smart contract not found',
        details: 'The escrow program is not deployed or accessible',
        code: 'PROGRAM_NOT_FOUND'
      });
      return { isValid: false, errors, warnings };
    }
    
    // If escrowId is provided, check escrow account state
    if (escrowId) {
      try {
        const escrowIdBN = new (require('@coral-xyz/anchor')).BN(escrowId);
        const [escrowPda] = PublicKey.findProgramAddressSync(
          [Buffer.from("escrow"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
          program.programId
        );
        
        const escrowAccount = await connection.getAccountInfo(escrowPda);
        if (!escrowAccount) {
          errors.push({
            category: 'SMART_CONTRACT_ERROR',
            message: 'Escrow account not found',
            details: `Escrow account for ID ${escrowId} does not exist`,
            code: 'ESCROW_NOT_FOUND'
          });
        }
      } catch (error: any) {
        errors.push({
          category: 'SMART_CONTRACT_ERROR',
          message: 'Failed to validate escrow state',
          details: error.message,
          code: 'ESCROW_VALIDATION_FAILED'
        });
      }
    }
  } catch (error: any) {
    errors.push({
      category: 'NETWORK_ERROR',
      message: 'Failed to connect to smart contract',
      details: error.message,
      code: 'CONTRACT_CONNECTION_FAILED'
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Transaction Data Validation
export function validateTransactionData(
  operation: 'buy' | 'sell' | 'refund' | 'dispute',
  data: any
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  switch (operation) {
    case 'buy':
      if (!data.escrowTxData) {
        errors.push({
          category: 'VALIDATION_ERROR',
          message: 'Missing escrow transaction data',
          code: 'MISSING_ESCROW_DATA'
        });
      } else {
        const escrowData = data.escrowTxData as EscrowTxData;
        
        if (!escrowData.escrowId) {
          errors.push({
            category: 'VALIDATION_ERROR',
            message: 'Missing escrow ID',
            code: 'MISSING_ESCROW_ID'
          });
        }
        
        if (!escrowData.solAmount || Number(escrowData.solAmount) <= 0) {
          errors.push({
            category: 'VALIDATION_ERROR',
            message: 'Invalid SOL amount',
            details: 'SOL amount must be greater than 0',
            code: 'INVALID_SOL_AMOUNT'
          });
        }
        
        if (!escrowData.buyerWallet || !escrowData.sellerWallet) {
          errors.push({
            category: 'VALIDATION_ERROR',
            message: 'Missing wallet addresses',
            code: 'MISSING_WALLET_ADDRESSES'
          });
        }
      }
      break;
      
    case 'sell':
    case 'refund':
      if (!data.escrowId) {
        errors.push({
          category: 'VALIDATION_ERROR',
          message: 'Missing escrow ID',
          code: 'MISSING_ESCROW_ID'
        });
      }
      break;
      
    case 'dispute':
      if (!data.tradeId || !data.reason) {
        errors.push({
          category: 'VALIDATION_ERROR',
          message: 'Missing dispute information',
          details: 'Trade ID and reason are required',
          code: 'MISSING_DISPUTE_INFO'
        });
      }
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Comprehensive Pre-Transaction Validation
export async function validateEscrowOperation(
  operation: 'buy' | 'sell' | 'refund' | 'dispute',
  wallet: any,
  data: any
): Promise<ValidationResult> {
  const allErrors: ValidationError[] = [];
  const allWarnings: ValidationWarning[] = [];
  
  // 1. Check network health
  const networkHealth = await checkNetworkHealth();
  if (!networkHealth.isHealthy) {
    allErrors.push({
      category: 'NETWORK_ERROR',
      message: 'Network connection issues detected',
      details: networkHealth.error,
      code: 'NETWORK_UNHEALTHY'
    });
  } else if (networkHealth.latency && networkHealth.latency > 5000) {
    allWarnings.push({
      message: `High network latency detected (${networkHealth.latency}ms)`,
      severity: 'medium'
    });
  }
  
  // 2. Validate wallet connection
  const walletValidation = await validateWalletConnection(wallet);
  allErrors.push(...walletValidation.errors);
  allWarnings.push(...walletValidation.warnings);
  
  // 3. Validate transaction data
  const dataValidation = validateTransactionData(operation, data);
  allErrors.push(...dataValidation.errors);
  allWarnings.push(...dataValidation.warnings);
  
  // 4. Validate balance (for buy operations)
  if (operation === 'buy' && wallet?.address && data.escrowTxData?.solAmount) {
    const balanceValidation = await validateBalance(
      wallet.address,
      Number(data.escrowTxData.solAmount) + 0.005 // Reduced buffer for fees (0.005 SOL instead of 0.01)
    );
    allErrors.push(...balanceValidation.errors);
    allWarnings.push(...balanceValidation.warnings);
  }
  
  // 5. Validate smart contract state
  const contractValidation = await validateSmartContractState(
    data.escrowTxData?.escrowId || data.escrowId
  );
  allErrors.push(...contractValidation.errors);
  allWarnings.push(...contractValidation.warnings);
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
}
