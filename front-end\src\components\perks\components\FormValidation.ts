import {
  validateName,
  validatePrice,
  validateUrl,
  validateStockAmount,
} from '../../../utils/formValidation';

export const validateFulfillmentLink = (link: string): string => {
  return validateUrl(link, 'fulfillment link');
};

export const validateCategory = (category: string): string => {
  if (!category || category.trim() === '') {
    return 'Please select a category';
  }
  return '';
};


// Re-export the shared functions for backward compatibility
export { validateName, validatePrice, validateStockAmount };
