const anchor = require("@coral-xyz/anchor");
const { getBondingCurvePda } = require("./pdas");
const { program } = require("./setup");
const { LAMPORTS_PER_SOL } = require("@solana/web3.js");

/**
 * Simulates how many tokens you'd receive for a given SOL input with slippage tolerance.
 *
 * @param {PublicKey} mint - Token mint address.
 * @param {number} solInputLamports - Amount of SOL (in lamports).
 * @param {number} slippageTolerance - Decimal (e.g. 0.005 for 0.5%)
 * @returns {Promise<{ estimatedOutput: number, minOutput: number }>}
 */
const getTokenOutputForSolInput = async (
  mint,
  solInputLamports,
  slippageTolerance = 0.005,
) => {
  const bondingCurvePda = getBondingCurvePda(mint);
  const bondingCurve =
    await program.account.bondingCurve.fetch(bondingCurvePda);

  const x = bondingCurve.virtualTokenReserves.toNumber();
  const y = bondingCurve.virtualSolReserves.toNumber();
  let dy = solInputLamports;

  if (dy <= 0 || x === 0 || y === 0) {
    throw new Error("Invalid input or zero reserves.");
  }

  const k = x * y;

  const initialTokenReserves = 1_073_000_191 * 1e6;
  let fee;
  // subtract fees from sol input
  // if it's the first buy a flat fee of 0.02 sol is charged,
  // if it's a subsequent buy 1% trading fee is charged
  if (bondingCurve.virtualTokenReserves.toNumber() == initialTokenReserves) {
    //is first buy
    fee = 0.02 * LAMPORTS_PER_SOL;
    dy -= fee;
  } else {
    fee = 0.01 * dy;
    dy -= fee;
  }
  let newY = y + dy;
  const newX = Math.floor(k / newY);
  const estimatedOutput = x - newX;
  const minOutput = Math.floor(estimatedOutput * (1 - slippageTolerance));

  console.log(
    `Expected tokens: ${estimatedOutput}, Min after slippage: ${minOutput}`,
  );
  return { estimatedOutput, minOutput };
};

/**
 * Simulates how much SOL you'd receive for selling tokens with slippage tolerance.
 *
 * @param {PublicKey} mint - Token mint address.
 * @param {number} tokenInput - Token amount to sell.
 * @param {number} slippageTolerance - Decimal (e.g. 0.005 for 0.5%)
 * @returns {Promise<{ estimatedOutput: number, minOutput: number }>}
 */
const getSolOutputForTokenInput = async (
  mint,
  tokenInput,
  slippageTolerance = 0.005,
) => {
  const bondingCurvePda = getBondingCurvePda(mint);
  const bondingCurve =
    await program.account.bondingCurve.fetch(bondingCurvePda);

  const x = bondingCurve.virtualTokenReserves.toNumber();
  const y = bondingCurve.virtualSolReserves.toNumber();
  const dx = tokenInput;

  if (dx <= 0 || x === 0 || y === 0) {
    throw new Error("Invalid input or zero reserves.");
  }

  const k = x * y;
  const newX = x + dx;
  const newY = Math.floor(k / newX);
  let estimatedOutput = y - newY;
  // 1% fee is charged on all trades
  fee = 0.01 * estimatedOutput;
  estimatedOutput -= fee;
  const minOutput = Math.floor(estimatedOutput * (1 - slippageTolerance));

  console.log(
    `Expected SOL: ${estimatedOutput}, Min after slippage: ${minOutput}`,
  );
  return { estimatedOutput, minOutput };
};

module.exports = {
  getTokenOutputForSolInput,
  getSolOutputForTokenInput,
};
