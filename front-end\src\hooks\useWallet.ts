import { usePrivy, useSolanaWallets } from "@privy-io/react-auth";
import { useCallback, useMemo } from "react";

import { SolanaWallet } from "@/types/api";

export const useWallet = () => {
  //for wallet connect
  const { wallets } = useSolanaWallets();

  //for email login
  const { user: privyUser } = usePrivy();
  const emailWallet = privyUser?.wallet as SolanaWallet | undefined;

  const solanaWallet = useMemo((): SolanaWallet | null => {
    let wallet;
    //for email login
    if ((emailWallet as any)?.connectorType === "embedded") {
      console.log("Embedded wallet detected");
      wallet = emailWallet;
    } else {
      wallet = wallets.find((w) => w.type === "solana");
      if (!wallet || !wallet.signTransaction) {
        return null;
      }
    }

    return wallet as SolanaWallet;
  }, [wallets]);

  const isConnected = useMemo(() => {
    return solanaWallet !== null;
  }, [solanaWallet]);

  const getWalletAddress = useCallback((): string | null => {
    return solanaWallet?.address || null;
  }, [solanaWallet]);

  const signTransaction = useCallback(
    async (transaction: any) => {
      if (!solanaWallet?.signTransaction) {
        throw new Error("Wallet not connected or signTransaction missing");
      }
      return await solanaWallet.signTransaction(transaction);
    },
    [solanaWallet]
  );

  return {
    solanaWallet,
    isConnected,
    getWalletAddress,
    signTransaction,
    wallets,
  };
};
