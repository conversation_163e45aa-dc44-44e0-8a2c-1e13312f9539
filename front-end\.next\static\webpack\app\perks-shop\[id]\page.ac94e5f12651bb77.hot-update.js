"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(signedTransaction.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(escrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(escrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_tokenDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1, _tradeData_tokenDetails1, _tradeData_perkDetails_token1, _tradeData_perkDetails2;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price,\n                    perkDetails: tradeData.perkDetails,\n                    perkTokenMint: tradeData.perkTokenMint\n                });\n                // Use trade data to get the numeric escrowId\n                // Priority: 1. tradeData.escrowId, 2. extra field, 3. original escrowId\n                let numericEscrowId = null;\n                // First try the direct escrowId field from trade data\n                if (tradeData.escrowId) {\n                    // Validate that it's numeric before using it\n                    const escrowIdStr = String(tradeData.escrowId);\n                    if (/^\\d+$/.test(escrowIdStr)) {\n                        numericEscrowId = tradeData.escrowId;\n                    } else {\n                        console.warn('⚠️ Trade escrowId is not numeric:', tradeData.escrowId);\n                    }\n                } else {\n                    // Fall back to extra field\n                    try {\n                        if (tradeData.extra) {\n                            const extraData = JSON.parse(tradeData.extra);\n                            if (extraData.escrowId) {\n                                const escrowIdStr = String(extraData.escrowId);\n                                if (/^\\d+$/.test(escrowIdStr)) {\n                                    numericEscrowId = extraData.escrowId;\n                                } else {\n                                    console.warn('⚠️ Extra escrowId is not numeric:', extraData.escrowId);\n                                }\n                            }\n                        }\n                    } catch (e) {\n                        console.log('Could not parse extra data:', e);\n                    }\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                console.log('🔍 [EscrowAccept] EscrowId resolution:', {\n                    originalEscrowId: escrowId,\n                    tradeDataEscrowId: tradeData.escrowId,\n                    numericEscrowId,\n                    finalCalculatedEscrowId: calculatedEscrowId\n                });\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                // Try multiple sources for perk token mint\n                calculatedPerkMint = ((_tradeData_tokenDetails = tradeData.tokenDetails) === null || _tradeData_tokenDetails === void 0 ? void 0 : _tradeData_tokenDetails.tokenAddress) || ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || tradeData.perkTokenMint || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Perk token mint resolution:', {\n                    'tokenDetails.tokenAddress': (_tradeData_tokenDetails1 = tradeData.tokenDetails) === null || _tradeData_tokenDetails1 === void 0 ? void 0 : _tradeData_tokenDetails1.tokenAddress,\n                    'perkDetails.token.tokenAddress': (_tradeData_perkDetails2 = tradeData.perkDetails) === null || _tradeData_perkDetails2 === void 0 ? void 0 : (_tradeData_perkDetails_token1 = _tradeData_perkDetails2.token) === null || _tradeData_perkDetails_token1 === void 0 ? void 0 : _tradeData_perkDetails_token1.tokenAddress,\n                    'tradeData.perkTokenMint': tradeData.perkTokenMint,\n                    'original perkTokenMint': perkTokenMint,\n                    'final calculatedPerkMint': calculatedPerkMint\n                });\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required but not found. Please ensure the trade has a valid escrowId. TradeId: \".concat(tradeId), \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        if (!calculatedPerkMint) {\n            throw new EscrowError(\"Perk token mint address is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            perkTokenAta: sellerPerkTokenAta,\n            purchase: purchasePda,\n            vault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});