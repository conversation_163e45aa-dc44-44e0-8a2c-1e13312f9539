"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_perks_components_FormFields_tsx";
exports.ids = ["_ssr_src_components_perks_components_FormFields_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/perks/components/FormFields.tsx":
/*!********************************************************!*\
  !*** ./src/components/perks/components/FormFields.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../constants */ \"(ssr)/./src/constants/index.ts\");\n\n\n\n\n\nconst FormFields = ({ formData, errors, handleChange, handleBlur, handleCheckboxChange, handleFileChange, handleDeletePicture, t, validationProgress = {} })=>{\n    // Helper function to get field validation state\n    const getFieldState = (fieldName)=>{\n        const hasError = errors[fieldName];\n        const isValidating = validationProgress[fieldName];\n        const hasValue = formData[fieldName] && String(formData[fieldName]).trim() !== '';\n        return {\n            hasError: !!hasError,\n            isValidating,\n            hasValue,\n            isValid: hasValue && !hasError && !isValidating\n        };\n    };\n    // Helper function to get field styling\n    const getFieldStyling = (fieldName)=>{\n        const state = getFieldState(fieldName);\n        if (state.isValidating) {\n            return 'outline-blue-500 bg-blue-50';\n        } else if (state.hasError) {\n            return 'outline-red-500 bg-red-50';\n        } else if (state.isValid) {\n            return 'outline-green-500 bg-green-50';\n        } else {\n            return 'outline-gray-300';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-5 mx-auto w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('name')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"name\",\n                                    placeholder: t('createPerkForm.name'),\n                                    value: formData.name,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-36 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('description')} inline-flex justify-start items-start gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"description\",\n                                    placeholder: t('createPerkForm.description'),\n                                    value: formData.description,\n                                    onChange: handleChange,\n                                    className: \"w-full mt-4 bg-transparent outline-none resize-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.description\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('price')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"price\",\n                                    placeholder: \"Price\",\n                                    value: formData.price,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    children: \"$\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.price\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('tokenAmount')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    name: \"tokenAmount\",\n                                    placeholder: \"Number of tokens buyers receive (e.g., 1)\",\n                                    value: formData.tokenAmount,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    min: \"1\",\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.tokenAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.tokenAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                className: \"text-red-500 text-sm mt-1 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.tokenAmount\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('category')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    value: formData.category,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: t('createPerkForm.selectCategory')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        _constants__WEBPACK_IMPORTED_MODULE_2__.FORM_CATEGORIES.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                className: \"text-gray-700\",\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('fulfillmentLink')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"fulfillmentLink\",\n                                    placeholder: t('createPerkForm.fulfillmentLink'),\n                                    value: formData.fulfillmentLink,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.fulfillmentLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-zinc-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                        children: \"!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.fulfillmentLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.fulfillmentLink\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('picture')} inline-flex justify-start items-center gap-3 w-full`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    children: formData.picture ? formData.picture.name : t('createPerkForm.uploadPicture')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleDeletePicture,\n                                    className: \"w-24 h-7 bg-red-500 text-white cursor-pointer hover:bg-red-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center\",\n                                        children: t('createPerkForm.clear')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"w-32 md:w-44 h-7 bg-zinc-300 cursor-pointer hover:bg-zinc-400 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center\",\n                                            children: t('createPerkForm.upload')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"picture-upload\",\n                                            type: \"file\",\n                                            className: \"hidden\",\n                                            onChange: handleFileChange,\n                                            accept: \"image/*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.picture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.picture\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"limitedStock\",\n                                name: \"limitedStock\",\n                                checked: formData.limitedStock,\n                                onChange: handleCheckboxChange,\n                                className: \"h-5 w-5 border-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"limitedStock\",\n                                className: \"ml-2 text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                                children: t('createPerkForm.limitedStock')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.limitedStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `h-[52px] w-[192px] rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('stockAmount')} flex items-center justify-between px-4 py-[13px]`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"stockAmount\",\n                                            placeholder: t('createPerkForm.stock'),\n                                            value: formData.stockAmount,\n                                            onChange: handleChange,\n                                            onBlur: handleBlur,\n                                            className: \"w-full bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        validationProgress.stockAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    children: errors.stockAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-1\",\n                                                children: \"⚠\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            errors.stockAmount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormFields);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/perks/components/FormFields.tsx\n");

/***/ })

};
;