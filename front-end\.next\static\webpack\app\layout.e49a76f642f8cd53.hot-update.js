"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && !!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId);\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, notification.data.tradeId // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(notification.data.tradeId, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId)) {\n            console.error('❌ [NotificationBell] No chatRoomId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId: notification.data.chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId\n        });\n        // Fetch real trade details if tradeId exists\n        let activeTrade = undefined;\n        if (notification.data.tradeId) {\n            try {\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', notification.data.tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(notification.data.tradeId));\n                if (tradeResponse.status === 200) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    activeTrade = {\n                        id: tradeResponse.data.id,\n                        status: tradeResponse.data.status,\n                        tradeId: tradeResponse.data.id,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: notification.data.tradeId,\n                    status: 'escrowed',\n                    tradeId: notification.data.tradeId,\n                    from: buyerId,\n                    to: sellerId\n                };\n            }\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(notification.data.chatRoomId);\n        // Close existing modal if it exists to prevent duplicates\n        closeModal(modalId);\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId: notification.data.chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: createReleaseFunction(notification),\n                onRefund: createRefundFunction(notification),\n                onReport: ()=>{},\n                onAccept: createAcceptFunction(notification),\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 641,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 709,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 740,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 778,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 763,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 824,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 761,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 738,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c189ce8e1f5b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzE4OWNlOGUxZjViXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});