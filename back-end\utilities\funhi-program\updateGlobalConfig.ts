require("ts-node/register");
import * as anchor from "@coral-xyz/anchor";
import { PublicKey, TransactionInstruction } from "@solana/web3.js";
import { program } from "./setup";
import { global } from "./pdas";

export const updateGlobalConfig = async (
  authority: PublicKey,
  newAuthority: PublicKey,
  newFeeRecipient: PublicKey,
  newFeeBps: anchor.BN,
  newFirstBuyFeeSol: anchor.BN,
  newInitialVirtualTokenReserves: anchor.BN,
  newInitialVirtualSolReserves: anchor.BN,
  newInitialRealTokenReserves: anchor.BN,
  newTokenTotalSupply: anchor.BN,
  newGraduationThreshold: anchor.BN,
): Promise<TransactionInstruction> => {
  const updateGlobalConfigIx = await program.methods
    .updateGlobalConfig(
      newAuthority,
      newFeeRecipient,
      newFeeBps,
      newFirstBuyFeeSol,
      newInitialVirtualTokenReserves,
      newInitialVirtualSolReserves,
      newInitialRealTokenReserves,
      newTokenTotalSupply,
      newGraduationThreshold,
    )
    .accounts({ authority })
    .instruction();
  return updateGlobalConfigIx;
};
