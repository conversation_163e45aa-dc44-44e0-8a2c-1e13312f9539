const { Model, DataTypes } = require("sequelize");

class User extends Model {
    static initModel(sequelize) {
        return User.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                privyId: {
                    type: DataTypes.STRING(50),
                    allowNull: true,
                },
                privywallet: {
                    type: DataTypes.STRING(150),
                    allowNull: true,
                },
                walletBO: {
                    type: DataTypes.STRING(500),
                    allowNull: true,
                },
                username: {
                    type: DataTypes.STRING(50),
                    allowNull: true,
                },
                email: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                    unique: true,
                    validate: {
                        isEmail: true,
                    },
                },
                password: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                name: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                bio: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
                website: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                tiktok: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                facebook: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                instagram: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                accountVerifiedPhoto: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                twoFactorStatus: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                twoFactorData: {
                    type: DataTypes.JSON,
                    allowNull: true,
                },
                referralBy: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                allowMessages: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'users',
                timestamps: true,
            }
        );
    }
}

module.exports = User;
