const { fn, col, where, Op, literal } = require("sequelize");
const dataContext = require("../db");
const {
  createSOLToken,
  syncBondingCurves,
  checkGraduation,
  transferSplTokenFromAirdrop,
  verifyTransaction,
} = require("../utilities/solana");
const i18n = require('../i18n');

const { isValidPublicKey } = require('../utilities/utils');

exports.getTokens = async (req, res) => {
  try {
    const { search, sort, order, isVerified } = req.query;
    let { page, pageSize } = req.query;

    let sortQuery;
    if (sort && order) {
      const sorts = sort.split(",");
      const orders = order.split(",");
      sortQuery = sorts
        .map((el, index) => {
          if (orders.length > index) {
            return [el, orders[index] === "desc" ? "desc" : "asc"];
          }
          return null;
        })
        .filter(Boolean);
    }

    const query = {
      where: {},
      order: sortQuery,
      subQuery: false,
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email"], // customize this as needed
        },
      ],
    };

    const countQuery = {
      where: {},
    };

    // Search
    if (search) {
      query.where.name = {
        [Op.like]: `%${search}%`,
      };
      countQuery.where.name = {
        [Op.like]: `%${search}%`,
      };
    }

    // Filter by isVerified
    if (isVerified === "true") {
      query.where.isVerified = true;
    } else if (isVerified === "false") {
      query.where.isVerified = false;
    }

    // Pagination
    if (page && pageSize) {
      page = parseInt(page);
      pageSize = parseInt(pageSize);
      query.limit = pageSize;
      query.offset = page * pageSize;
    }

    const tokens = await dataContext.Token.findAll(query);
    const totalCount = await dataContext.Token.count({ where: query.where });

    res.status(200).json({
      message: "success",
      data: {
        totalCount,
        page,
        pageSize,
        tokens,
      },
    });
  } catch (e) {
    res.status(404).json({
      message: "An unexpected error occurred. Please try again later.",
    });
  }
};

exports.syncBondingCurves = async (req, res) => {
  const dataIs = await syncBondingCurves();
  res.status(201).json({
    status: 201,
    message: "sync Bonding Curves.",
    data: dataIs,
  });
};

exports.checkGraduation = async (req, res) => {
  const dataIs = await checkGraduation();
  res.status(201).json({
    status: 201,
    message: "checked Graduation",
    data: dataIs,
  });
};

exports.createAirDrop = async (req, res, next) => {
  try {
    const {
      totalTokens,
      tokensPerLink,
      isPrivate,
      UserID,
      TokenID,
      signature,
      tokenAddress,
    } = req.body;

    // Create new Airdrop
    const newAirdrop = await dataContext.Airdrop.create({
      totalTokens,
      tokensPerLink,
      isPrivate: isPrivate === true || isPrivate === "true",
      UserID,
      TokenID,
      link: Math.random().toString(36).substring(2, 17),
      signature,
      remainingToken: totalTokens,
      tokenAddress,
    });

    // Fetch all airdrops for the user
    const allAirdrops = await dataContext.Airdrop.findAll({
      where: { UserID },
      order: [["createdAt", "DESC"]],
    });

  await dataContext.userLogs.create({
    userId: UserID,
    messageKey: "log.airdrop.created",
    messageVars: JSON.stringify({ totalTokens }),
    type: "AirDrop",
  });

    res.status(201).json({
      status: 201,
      message: "Airdrop created successfully",
      created: newAirdrop,
      allAirdrops,
    });
  } catch (err) {
    next(err);
  }
};

exports.addBurnLogs = async (req, res, next) => {
  try {
    const { userId, tokenId, amount, signature, message } = req.body;

    const coinPrice = 0.0000045515;
    const price_ = coinPrice;
    const dollorPrice_ = parseFloat(amount) * coinPrice;

    const newPurchase = await dataContext.TokenPurchased.create({
      userId,
      tokenId,
      amount,
      price: price_,
      dollorPrice: dollorPrice_,
      hasH: signature,
      status: "Burn",
      buyorsell: 0,
    });

    await dataContext.userLogs.create({
      userId,
      messageKey: "log.token.sold", // or another key if appropriate
      messageVars: JSON.stringify({ amount, tokenName: message }),
      type: "Burn",
    });

    res.status(201).json({
      status: 201,
      message: "Token Burn successfully.",
      data: newPurchase,
    });
  } catch (err) {
    next(err);
  }
};


exports.getUserLogs = async (req, res) => {
  const { userId, type } = req.body;
  const lang = req.headers['accept-language'] || 'en';
  await i18n.changeLanguage(lang);
  // console.log("getUserLogs called with:", { userId, type });
  // console.log("Request body:", req.body);

  if (type == 1) {
    try {
      // For system notifications (type == 1), get ALL recent activity logs (excluding chat messages)
      // This should show system-wide activity, not just for a specific user
      const systemLogs = await dataContext.userLogs.findAll({
        where: {
          type: {
            [Op.ne]: 'chat_message' // Exclude chat messages from system logs
          }
        },
        order: [['createdAt', 'DESC']],
        limit: 20, // Get more logs to ensure we have enough activity
      });
      
      // console.log("System logs found:", systemLogs.length);
      // console.log("System logs details:", systemLogs.map(log => ({
      //   id: log.id,
      //   userId: log.userId,
      //   type: log.type,
      //   message: log.message,
      //   messageDetail: log.messageDetail
      // })));

      // Translate messages before sending
      const translatedLogs = systemLogs.map(log => ({
        ...log.toJSON(),
        message: log.messageKey
          ? i18n.t(log.messageKey, JSON.parse(log.messageVars || '{}'))
          : log.message,
        messageDetail: log.messageDetail
      }));
      return res.status(200).json({
        status: 200,
        message: i18n.t('log.systemLogsFetched', 'Latest system logs fetched successfully.'),
        data: translatedLogs,
      });
    } catch (error) {
      console.error("Get user logs error:", error);
      return res.status(500).json({
        status: 500,
        message: "Internal server error",
      });
    }
  } else {
    try {
      // For trade notifications (type == 0), include both user's own logs AND chat messages
      // First, get user's own activity logs
      const userLogs = await dataContext.userLogs.findAll({
        where: { 
          userId,
          type: {
            [Op.ne]: 'chat_message' // Exclude chat messages from user logs
          }
        },
        order: [['createdAt', 'DESC']],
        limit: 10,
      });

      // Then, get chat messages where user is a participant (buyer or seller)
      const chatLogs = await dataContext.userLogs.findAll({
        where: {
          type: 'chat_message',
          chatRoomId: {
            [Op.ne]: null // Only logs with chatRoomId
          }
        },
        order: [['createdAt', 'DESC']],
        limit: 10,
      });

      // console.log("Chat logs found:", chatLogs.length);
      // console.log("User logs found:", userLogs.length);

      // Filter chat logs to only include those where user is a participant
      const filteredChatLogs = [];
      for (const log of chatLogs) {
        if (log.chatRoomId) {
          // Check if user is buyer or seller in this chat room
          const chatRoom = await dataContext.ChatRoom.findOne({
            where: { chatRoomId: log.chatRoomId }
          });
          
          if (chatRoom && (chatRoom.buyerId == userId || chatRoom.sellerId == userId)) {
            // User is a participant in this chat room
            filteredChatLogs.push(log);
          }
        }
      }

      // console.log("Filtered chat logs for user:", filteredChatLogs.length);

      // Translate messages before sending
      const translatedUserLogs = userLogs.map(log => ({
        ...log.toJSON(),
        message: log.messageKey
          ? i18n.t(log.messageKey, JSON.parse(log.messageVars || '{}'))
          : log.message,
        messageDetail: log.messageDetail
      }));
      const translatedChatLogs = filteredChatLogs.map(log => ({
        ...log.toJSON(),
        message: log.messageKey
          ? i18n.t(log.messageKey, JSON.parse(log.messageVars || '{}'))
          : log.message,
        messageDetail: log.messageDetail
      }));
      // Combine both types of logs, prioritizing newer ones
      const allLogs = [...translatedUserLogs, ...translatedChatLogs];
      allLogs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      // Return top 15 combined logs
      const combinedLogs = allLogs.slice(0, 15);

      return res.status(200).json({
        status: 200,
        message: i18n.t('log.tradeLogsFetched', 'Latest trade logs fetched successfully.'),
        data: combinedLogs,
      });
    } catch (error) {
      console.error("Get user logs error:", error);
      return res.status(500).json({
        status: 500,
        message: "Internal server error",
      });
    }
  }
};

exports.claimAirDrop = async (req, res) => {
  const { wallet, code, userId } = req.body;

  try {
    const { userId, type } = req.body;

    let queryOptions = {
      order: [["createdAt", "DESC"]],
      limit: 10,
    };

    if (type === 0 || type == "0") {
      queryOptions.where = { userId:userId };
    }

    const logs = await dataContext.userLogs.findAll(queryOptions);

    res.status(200).json({
      status: 200,
      message:
        type === "1"
          ? "Latest logs fetched successfully."
          : "Latest user logs fetched successfully.",
      data: logs,
    });
  } catch (err) {
    next(err);
  }
};

exports.claimAirDrop = async (req, res, next) => {
  try {
    const { wallet, code, userId } = req.body;

    const airdrop = await dataContext.Airdrop.findOne({
      where: { link: code },
    });

    if (!airdrop) {
      const error = new Error("Airdrop not found");
      error.statusCode = 404;
      throw error;
    }

    // If wallet is missing or empty, just return the airdrop tokens info
    if (!wallet) {
      return res.status(201).json({
        status: 201,
        message: "Airdrop details",
        claimedAmount: airdrop.tokensPerLink,
      });
    }

    if (airdrop.remainingToken < airdrop.tokensPerLink) {
      const error = new Error("Airdrop tokens exhausted");
      error.statusCode = 400;
      throw error;
    }

    // Check if wallet already claimed
    const alreadyClaimed = await dataContext.AirdropLog.findOne({
      where: { airdropID: airdrop.id, wallet },
    });

    if (alreadyClaimed) {
      const error = new Error("You have already claimed the airdrop.");
      error.statusCode = 409;
      throw error;
    }

    // Proceed with token transfer
    const signature = await transferSplTokenFromAirdrop(
      wallet,
      airdrop.tokenAddress,
      airdrop.tokensPerLink,
      6
    );

    // Log the claim
    await dataContext.AirdropLog.create({
      airdropID: airdrop.id,
      wallet,
      signature,
      tokensPerLink: airdrop.tokensPerLink,
    });

    const coinPrice = 0.0000045515;
    const price_ = coinPrice;
    const dollorPrice_ = parseFloat(airdrop.tokensPerLink) * coinPrice;

    await dataContext.TokenPurchased.create({
      userId,
      tokenId: airdrop.TokenID,
      amount: airdrop.tokensPerLink,
      price: price_,
      dollorPrice: dollorPrice_,
      hasH: signature,
      status: "AirDrop",
      buyorsell: 1,
    });

    await dataContext.userLogs.create({
      userId,
      messageKey: "log.airdrop.received",
      messageVars: JSON.stringify({ amount: airdrop.tokensPerLink }),
      type: "AirDrop",
    });

    // Deduct tokens from airdrop remaining
    airdrop.remainingToken -= airdrop.tokensPerLink;
    await airdrop.save();

    res.status(201).json({
      status: 201,
      message: `Airdrop claim successful, ${airdrop.tokensPerLink} tokens have been sent to your wallet.`,
      claimedAmount: airdrop.tokensPerLink,
      signature,
      remaining: airdrop.remainingToken,
    });
  } catch (err) {
    next(err);
  }
};


exports.addToken = async (req, res, next) => {
  try {
    let {
      name,
      ticker,
      description,
      tokenAddress,
      image,
      category,
      isVerified,
      telegram,
      website,
      twitter,
      userId,
      userWallet,
    } = req.body;

    const defaultCategories = [
      "Art",
      "Music",
      "Photography",
      "Videography",
      "Utility",
    ];
    category =
      category ||
      defaultCategories[Math.floor(Math.random() * defaultCategories.length)];

    // Check if token with same name or ticker already exists
    const existingToken = await dataContext.Token.findOne({
      where: {
        [Op.or]: [{ name }, { ticker }],
      },
    });

    if (!userWallet || !isValidPublicKey(userWallet)) {
      const error = new Error("Invalid user wallet address.");
      error.statusCode = 400; // Bad Request
      throw error;
    }

    if (existingToken) {
      const error = new Error(
        "Token with this name or ticker already exists, Please try any other."
      );
      error.statusCode = 409; // Conflict
      throw error;
    }

    // Create token on SOL blockchain (your external function)
    const isTokenCreated = await createSOLToken(
      name,
      ticker,
      image,
      userWallet
    );

    if (!isTokenCreated?.mintAddress) {
      const error = new Error(
        "Error while creating Token, please contact support."
      );
      error.statusCode = 500;
      throw error;
    }

    // Create new token record
    const newToken = await dataContext.Token.create({
      name,
      userId,
      ticker,
      description,
      tokenAddress: isTokenCreated.mintAddress,
      bondingCurvePda: isTokenCreated.bondingCurvePda,
      graduated: false,
      image:
        image ||
        "https://upload.wikimedia.org/wikipedia/en/b/b9/Solana_logo.png",
      category,
      isVerified,
      telegram,
      website,
      twitter,
      creatorWallet: isTokenCreated.creatorWallet,
    });

    // Log creation
    await dataContext.userLogs.create({
      userId,
      messageKey: "log.token.created",
      messageVars: JSON.stringify({ name }),
      type: "Created Token",
    });

    // Create system notification for token creation (broadcast to all users)
    try {
      // console.log('📢 [TokenController] Creating system notification for token creation:', name);

      // Get creator's username for the notification
      const creator = await dataContext.User.findByPk(userId, {
        attributes: ['username']
      });

      const notificationCount = await dataContext.Notification.createTokenCreationNotification(
        userId,
        name,
        newToken.id,
        newToken.tokenAddress,
        creator?.username || 'Unknown User'
      );

      console.log(`✅ [TokenController] Created token creation notifications for ${notificationCount} users`);
    } catch (error) {
      console.error('❌ [TokenController] Failed to create token creation notification:', error);
      // Don't fail the token creation if notification fails
    }

    res.status(201).json({
      status: 201,
      message: "Token created successfully.",
      data: newToken,
    });
  } catch (err) {
    next(err); // Send to centralized error handler
  }
};

exports.getToken = async (req, res) => {
  try {
    const tokenId = req.params.id;

    if (!tokenId) {
      return res.status(400).json({ message: "Token ID is required." });
    }

    // Fetch token, comments, and perks
    const tokenData = await dataContext.Token.findOne({
      where: { tokenId },
      include: [
        {
          model: dataContext.Comments,
          as: "comments",
          separate: true,
          attributes: [
            "ID",
            "User_ID",
            "UserName",
            "Comment",
            "Stars",
            "createdAt",
            "updatedAt",
            "Avatar",
          ],
          order: [["createdAt", "DESC"]],
        },
      ],
    });

    if (!tokenData) {
      return res.status(404).json({ status: 400, message: "Token not found." });
    }

    const token = tokenData.toJSON();

    // Fetch 3 random perks on same user
    const perks = await dataContext.Perk.findAll({
      where: { userId: token.userId },
      limit: 3,
    });
    token.perks = perks;

    res.status(200).json({
      status: 200,
      message: "Token retrieved successfully.",
      data: token,
    });
  } catch (error) {
    console.error("Error fetching token:", error);
    res.status(500).json({ message: "Something went wrong." });
  }
};

exports.purchaseToken = async (req, res, next) => {
  try {
    const {
      userId,
      tokenId,
      amount,
      price,
      dollorPrice,
      from,
      to,
      extra,
      hasH,
      transactionId,
      status,
      isBuy,
      tokenName,
    } = req.body;

    // Create new purchase
    const newPurchase = await dataContext.TokenPurchased.create({
      userId,
      tokenId,
      amount,
      price,
      dollorPrice,
      from,
      to,
      extra,
      hasH,
      transactionId,
      status,
      buyorsell: isBuy,
      tokenName,
      perkId, // Added perkId
    });

    // Optional: Verify transaction hash on-chain
    // const result = await verifyTransaction(hasH);
    // if (result.status !== "success") {
    //   const error = new Error("Transaction verification failed.");
    //   error.statusCode = 502;
    //   throw error;
    // }

    const action = isBuy == 1 ? "Purchased" : "Sell";
    const message = `${action} ${amount} ${tokenName} tokens`;

    await dataContext.userLogs.create({
      userId,
      messageKey: isBuy == 1 ? "log.token.purchased" : "log.token.sold",
      messageVars: JSON.stringify({ amount, tokenName }),
      type: isBuy == 1 ? "Buy" : "Sell",
    });

    res.status(200).json({
      status: 200,
      message: "Token transaction recorded successfully.",
      data: newPurchase,
    });
  } catch (err) {
    next(err); // pass to centralized error handler
  }
};

exports.postComments = async (req, res) => {
  const { userId, tokenId, username, comment, avatar } = req.body;

  if (!userId || !tokenId || !comment) {
    return res
      .status(200)
      .json({ status: 400, message: "Missing required fields." });
  }

  try {
    // Save the new comment
    await dataContext.Comments.create({
      User_ID: userId,
      Token_ID: tokenId,
      UserName: username,
      Comment: comment,
      Avatar: avatar || null,
      Stars: 1, // default value
    });

    // Fetch all comments for this token, ordered by latest first
    const allComments = await dataContext.Comments.findAll({
      where: { Token_ID: tokenId },
      attributes: [
        "ID",
        "User_ID",
        "UserName",
        "Comment",
        "Stars",
        "createdAt",
        "updatedAt",
        "Avatar",
      ],
      order: [["createdAt", "DESC"]],
      limit: 10,
    });

    res.status(201).json({
      status: 201,
      message: "Comment posted successfully.",
      data: allComments,
    });
  } catch (error) {
    console.error("Error posting comment:", error);
    res
      .status(200)
      .json({
        status: 500,
        message: "An unexpected error occurred. Please try again later.",
      });
  }
};
