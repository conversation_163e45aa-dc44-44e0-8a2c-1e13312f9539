"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import dynamic from 'next/dynamic';
import { useTranslation } from "@/hooks/useTranslation";

const Card = dynamic(() => import('@/components/shared/card').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const Header = dynamic(() => import('@/components/ui/header').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

export default function PerksBought({ purchasedPerks }: { purchasedPerks: any[] }) {
    const [sortedPerks, setSortedPerks] = useState(purchasedPerks);
    const [isRecent, setIsRecent] = useState(true);
    const { t } = useTranslation();

    console.log(purchasedPerks);
    console.log(sortedPerks);

    useEffect(() => {
        setSortedPerks(purchasedPerks);
        handleSort();
      }, [purchasedPerks]);

    const handleSort = () => {
        const sorted = [...sortedPerks].sort((a, b) => {
            return isRecent
                ? new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
                : new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        });
        console.log(sorted);
        setSortedPerks(sorted);
        setIsRecent(!isRecent);
    };

    return (
        <div className="mt-2">
            <Header
                title={t('perksBought.title')}
                sortButtonText={isRecent ? t('perksBought.sortByOldest') : t('perksBought.sortByRecent')}
                handleSort={handleSort}
            />
            <div className="mx-auto mt-4">
                {sortedPerks.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                        {sortedPerks.slice(0, 5).map((coin) => (
                            <Link key={coin.perkDetails.perkId} href={`/perks-shop/${coin.perkDetails.perkId}`}>
                                <Card
                                    id={coin.perkDetails.perkId}
                                    isLive={coin.isLive}
                                    name={coin.perkDetails.name}
                                    price={coin.price}
                                    username={coin.perkDetails.name}
                                    handle={coin.perkDetails.name}
                                    buttonType="view"
                                    soldCount={coin.soldCount}
                                    remainingCount={coin.remainingCount}
                                    timeLeft={coin.updatedAt}
                                    isPurshases={true}
                                    imageUrl={coin.perkDetails.image}
                                />
                            </Link>
                        ))}
                    </div>
                ) : (
                    <div className="text-center text-gray-500 text-lg font-medium py-10">
                        {t('perksBought.noPerks')}
                    </div>
                )}
            </div>
        </div>
    );
}
