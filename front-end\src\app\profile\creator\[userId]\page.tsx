"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { fetchPublicCreatorProfile, fetchFollowers, fetchFollowing, fetchTokenDetails, followUser, unfollowUser, updateProfile, initiateDirectChatRoom } from "@/axios/requests";
import CoinCard from "@/components/profile/CoinCard";
import ProfileHeader from "@/components/profile/ProfileHeader";
import PerksCarousel from "@/components/profile/PerksCarousel";
import PerksList from "@/components/profile/PerksList";
import { Coin, Perk } from "@/components/profile/types";
import { ProductCardSkeleton } from "@/components/ui/LoadingSkeletons";
import { TabContentLoader } from "@/components/ui/TabContentLoader";
import ChatModal from "@/components/shared/chat/ChatModal";
import CustomDropdown from "@/components/ui/CustomDropdown";
import { useTranslation } from '@/hooks/useTranslation';

const CARDS_PER_PAGE = 4;
const CARDS_PER_VIEW = 3;
const SKELETON_COUNT = 2;
const SKELETON_CAROUSEL_COUNT = 3;

const CreatorProfile = () => {
  const params = useParams();
  const userId: string = (params?.userId as string) || "";

  const [activeTab, setActiveTab] = useState("coins");
  const [coins, setCoins] = useState<Coin[]>([]);
  const [perks, setPerks] = useState<Perk[]>([]);
  const [loading, setLoading] = useState(true);
  const [coinDetails, setCoinDetails] = useState<any>(null);
  const [myPerks, setMyPerks] = useState<Perk[]>([]);
  const [page, setPage] = useState(0);
  const cardsPerPage = CARDS_PER_PAGE;
  const totalPages = Math.ceil((myPerks?.length || 0) / cardsPerPage);
  const visiblePerks = myPerks?.slice(page * cardsPerPage, (page + 1) * cardsPerPage) || [];
  const [carouselIndex, setCarouselIndex] = useState(0);
  const cardsPerView = React.useMemo(() => (typeof window !== 'undefined' && window.innerWidth >= 1024 ? CARDS_PER_VIEW : 1), []);
  const maxCarouselIndex = React.useMemo(() => Math.max(0, myPerks.length - cardsPerView), [myPerks.length, cardsPerView]);
  const [followers, setFollowers] = useState<any[]>([]);
  const [following, setFollowing] = useState<any[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFollowedByCreator, setIsFollowedByCreator] = useState(false); // NEW: track if creator follows current user
  const [followLoading, setFollowLoading] = useState(false);
  const [allowMessages, setAllowMessages] = useState(true);
  const [allowMessagesLoading, setAllowMessagesLoading] = useState(false);
  const [perkSort, setPerkSort] = useState('sales');
  const [error, setError] = useState<string | null>(null);
  const [chatModalOpen, setChatModalOpen] = useState(false);
  const [activeChatRoomId, setActiveChatRoomId] = useState<string | null>(null);
  const [myId, setMyId] = useState<string | number | null>(null);

  const { t } = useTranslation();

  const sortedPerks = React.useMemo(() => {
    const list = (perks.length > 0 ? perks : myPerks).slice();
    if (perkSort === 'sales') {
      return list.sort((a, b) => Number(b.soldCount || 0) - Number(a.soldCount || 0));
    } else if (perkSort === 'price') {
      return list.sort((a, b) => Number(b.price) - Number(a.price));
    } else if (perkSort === 'name') {
      return list.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }
    return list;
  }, [perks, myPerks, perkSort]);

  // Not own profile
  const isOwnProfile = false;

  useEffect(() => {
    const fetchData = async () => {
      if (!userId) return;
      setLoading(true);
      setError(null);
      try {
        const res = await fetchPublicCreatorProfile(userId);
        setCoins(res.data?.MyToken || []);
        setMyPerks(res.data?.MyPerks || []);
        console.log("NEw",res.data?.MyPerks)
        // Fetch followers (who follows the creator)
        const followersRes = await fetchFollowers(userId);
        // Fetch following (who the creator follows)
        const followingRes = await fetchFollowing(userId);
        // Get current user id
        const storedUserBo = localStorage.getItem('userBo');
        let myId = null;
        if (storedUserBo) {
          try { myId = JSON.parse(storedUserBo).id; } catch {}
        }
        // Use followersRes.followers as an array
        const followersArr = followersRes.followers || [];
        setFollowers(followersArr);
        setIsFollowing(!!followersArr.find((f: any) => f.id == myId));
        // Check if creator is following current user (using followingRes.following as array)
        const followingArr = followingRes.following || [];
        setFollowing(followingArr);
        setIsFollowedByCreator(!!followingArr.find((f: any) => f.id == myId));
        setAllowMessages(res.data?.allowMessages === true);
        if (res.data?.MyToken && res.data.MyToken.length > 0) {
          const firstCoin = res.data.MyToken[0];
          const id = firstCoin.tokenId || firstCoin.tokenAddress;
          if (id) {
            const detailsRes = await fetchTokenDetails(id.toString());
            setCoinDetails(detailsRes.data);
          }
        } else {
          setCoinDetails(null);
        }
      } catch (e) {
        setCoins([]);
        setPerks([]);
        setCoinDetails(null);
        setFollowers([]);
        setFollowing([]);
        setIsFollowing(false);
        setIsFollowedByCreator(false);
        setAllowMessages(true);
        setError("Failed to load profile data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userId]);

  useEffect(() => {
    const storedUserBo = localStorage.getItem('userBo');
    if (storedUserBo) {
      try {
        setMyId(JSON.parse(storedUserBo).id);
      } catch {}
    }
  }, []);

  // Handler for follow/unfollow
  const handleFollow = async () => {
    if (!userId) return;
    
    // Check if user is logged in
    const token = localStorage.getItem('token');
    const storedUserBo = localStorage.getItem('userBo');
    
    if (!token || !storedUserBo) {
      setError("Please log in to follow this user");
      return;
    }
    
    try {
      const userBo = JSON.parse(storedUserBo);
      const myId = userBo.id;
      
      if (!myId) {
        setError("Please log in to follow this user");
        return;
      }
      
      setFollowLoading(true);
      setIsFollowing(true); // Optimistic update
      await followUser(userId, myId);
      // Refetch followers to confirm
      const followersRes = await fetchFollowers(userId);
      const followersArr = followersRes.followers || [];
      setFollowers(followersArr);
      setIsFollowing(!!followersArr.find((f: any) => f.id == myId));
    } catch (e: any) {
      console.error("Follow error:", e);
      setIsFollowing(false);
      if (e.response?.status === 401) {
        setError("Please log in to follow this user");
      } else {
        setError("Failed to follow user. Please try again.");
      }
    } finally {
      setFollowLoading(false);
    }
  };
  const handleUnfollow = async () => {
    if (!userId) return;
    
    // Check if user is logged in
    const token = localStorage.getItem('token');
    const storedUserBo = localStorage.getItem('userBo');
    
    if (!token || !storedUserBo) {
      setError("Please log in to unfollow this user");
      return;
    }
    
    try {
      const userBo = JSON.parse(storedUserBo);
      const myId = userBo.id;
      
      if (!myId) {
        setError("Please log in to unfollow this user");
        return;
      }
      
      setFollowLoading(true);
      setIsFollowing(false); // Optimistic update
      await unfollowUser(userId, myId);
      // Refetch followers to confirm
      const followersRes = await fetchFollowers(userId);
      const followersArr = followersRes.followers || [];
      setFollowers(followersArr);
      setIsFollowing(!!followersArr.find((f: any) => f.id == myId));
    } catch (e: any) {
      console.error("Unfollow error:", e);
      setIsFollowing(true);
      if (e.response?.status === 401) {
        setError("Please log in to unfollow this user");
      } else {
        setError("Failed to unfollow user. Please try again.");
      }
    } finally {
      setFollowLoading(false);
    }
  };

  const handleAllowMessagesToggle = async () => {
  };

  const handleDirectMessage = async () => {
    console.log("Message button clicked");
    const storedUserBo = localStorage.getItem('userBo');
    if (!storedUserBo) {
      setError("Please log in to message this user");
      return;
    }
    const myId = JSON.parse(storedUserBo).id;
    if (!myId || !userId) {
      setError("Invalid user information");
      return;
    }
    try {
      const res = await initiateDirectChatRoom({ userAId: myId, userBId: userId });
      console.log("API response:", res);
      if (res && res.chatRoomId) {
        console.log("Opening modal with chatRoomId:", res.chatRoomId);
        setActiveChatRoomId(res.chatRoomId);
        setChatModalOpen(true);
      } else {
        setError("Failed to initiate chat");
      }
    } catch (e) {
      setError("Failed to initiate chat");
    }
  };

  return (
    <div className="w-full">
      <div className="flex flex-col items-center justify-center mt-5 w-full">
        <ProfileHeader
          isOwnProfile={isOwnProfile}
          followersCount={followers.length}
          followingCount={following.length}
          isFollowing={isFollowing}
          followLoading={followLoading}
          allowMessages={allowMessages}
          allowMessagesLoading={allowMessagesLoading}
          onFollow={handleFollow}
          onUnfollow={handleUnfollow}
          onAllowMessagesToggle={handleAllowMessagesToggle}
          onMessage={handleDirectMessage} // Pass the handler
        />
      </div>
      <div className="w-full p-4 mt-3">
        <h3 className="text-3xl font-bold text-[#FF6600] text-center mb-4">
          {t('profilePage.profileInformation')}
        </h3>
        <div className="flex justify-center mt-8">
          <div
            className="relative rounded-full p-1 flex"
            style={{ backgroundColor: "#FF6600" }}
          >
            <button
              onClick={() => setActiveTab("coins")}
              className={`relative z-10 py-5 px-12 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === "coins"
                  ? "bg-white text-[#FF6600] "
                  : "bg-transparent text-white "
              }`}
            >
              {t('profilePage.coins')}
            </button>
            <button
              onClick={() => setActiveTab("items")}
              className={`relative z-10 py-5 px-12 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === "items"
                  ? "bg-white text-[#FF6600]"
                  : "bg-transparent text-white "
              }`}
            >
              {t('profilePage.items')}
            </button>
          </div>
        </div>
        <div className="mt-4 w-full">
          {error && (
            <div className="w-full text-center text-red-600 font-semibold py-4">
              {t('profilePage.loadError')}
            </div>
          )}
          {loading ? (
            <div className="grid w-full gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 place-items-center">
              {Array.from({ length: SKELETON_COUNT }).map((_, idx) => (
                <ProductCardSkeleton key={idx} />
              ))}
            </div>
          ) : activeTab === "coins" ? (
            <div
              className="grid w-full gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 place-items-center"
            >
              {coins.length === 0 ? (
                <div className="text-center text-gray-500 py-10 col-span-full">{t('profilePage.noCoinsFound')}</div>
              ) : (
                coins.map((coin: Coin, idx: number) => (
                  <CoinCard key={coin.tokenId || idx} coin={coin} />
                ))
              )}
            </div>
          ) : (
            <div className="w-full">
              <div
                className="w-full rounded-2xl mt-4 mb-4 p-4 bg-[#FF6600]"
              >
                <div className="flex justify-between tems-center mb-4">
                  <div className="gap-2 flex flex-row">
                  <img src={myPerks[0]?.image || "/api/placeholder/40/40"} alt="Avatar" className="w-8 h-8 rounded-full" />
                  <span className="text-white font-semibold">{myPerks[0]?.name || t('profilePage.perkToken')}</span>
                  </div>
                 
                  <div className="min-w-[120px] [&>span]:text-white">
                    {/* Sort by dropdown */}
                    <CustomDropdown
                      label={t('profilePage.sortBy')}
                      options={[
                        { value: "sales", label: t('profilePage.sales') },
                        { value: "price", label: t('profilePage.price') },
                        { value: "name", label: t('profilePage.name') },
                      ]}
                      selectedValue={perkSort}
                      onChange={setPerkSort}
                      className="min-w-[120px] [&>span]:text-white"
                    />
                  </div>
                </div>
                {/* Mobile: vertical list */}
                {loading ? (
                  <div className="flex flex-col gap-6 w-full lg:hidden">
                    {Array.from({ length: SKELETON_COUNT }).map((_, idx) => (
                      <ProductCardSkeleton key={idx} />
                    ))}
                  </div>
                ) : (
                  <PerksList sortedPerks={myPerks} />
                )}
                {/* Desktop: carousel */}
                {loading ? (
                  <div className="relative w-full items-center hidden lg:flex">
                    {Array.from({ length: SKELETON_CAROUSEL_COUNT }).map((_, idx) => (
                      <div className="mx-2" key={idx}>
                        <ProductCardSkeleton />
                      </div>
                    ))}
                  </div>
                ) : (
                  <PerksCarousel
                    sortedPerks={myPerks}
                    carouselIndex={carouselIndex}
                    cardsPerView={cardsPerView}
                    maxCarouselIndex={maxCarouselIndex}
                    setCarouselIndex={setCarouselIndex}
                  />
                )}
              </div>
            </div>
          )}
        </div>
        {!isOwnProfile && isFollowedByCreator && (
          <div className="text-green-600 text-sm mt-2">{t('profilePage.creatorFollowsYou')}</div>
        )}
        {chatModalOpen && activeChatRoomId && myId && (
          <ChatModal
            chatRoomId={activeChatRoomId}
            onClose={() => setChatModalOpen(false)}
            buyerId={myId}
            sellerId={userId}
          />
        )}
      </div>
    </div>
  );
};

export default CreatorProfile; 