"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_header_ProfileDropdown_tsx";
exports.ids = ["_ssr_src_components_shared_header_ProfileDropdown_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/header/ProfileDropdown.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shared/header/ProfileDropdown.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n\n\n\n\n\n\n\nconst ProfileDropdown = ({ dropdownRef, dropdownOpen, toggleDropdown, authenticated, login, logout, handleNavigation, userBalance, walletAddress, isAuthenticating = false })=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    // Helper function to truncate wallet address\n    const truncateAddress = (address)=>{\n        if (!address) return '';\n        return `${address.slice(0, 6)}...${address.slice(-4)}`;\n    };\n    // Copy wallet address to clipboard\n    const copyAddress = async ()=>{\n        if (walletAddress) {\n            try {\n                await navigator.clipboard.writeText(walletAddress);\n            // You can add a toast notification here\n            } catch (err) {\n                console.error('Failed to copy address:', err);\n            }\n        }\n    };\n    // If not authenticated, show connect wallet button instead\n    if (!authenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n            onClick: isAuthenticating ? undefined : login,\n            disabled: isAuthenticating,\n            className: \"hidden lg:flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white cursor-pointer font-medium hover:from-black hover:to-gray-800 transition-all duration-200 rounded-full shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\",\n            whileHover: isAuthenticating ? {} : {\n                scale: 1.02\n            },\n            whileTap: isAuthenticating ? {} : {\n                scale: 0.98\n            },\n            transition: {\n                type: 'spring',\n                stiffness: 400,\n                damping: 17\n            },\n            children: isAuthenticating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Connecting...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden lg:block relative\",\n        ref: dropdownRef,\n        onClick: toggleDropdown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 cursor-pointer bg-gradient-to-r from-[#F8F9FA] to-[#F5F5F5] h-[40px] lg:h-[56px] pr-4 lg:pr-6 pl-2 rounded-full transition-all duration-300 hover:from-gray-100 hover:to-gray-200 shadow-sm hover:shadow-md border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                        className: \"flex items-center justify-center relative\",\n                        onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.SETTINGS),\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        transition: {\n                            type: 'spring',\n                            stiffness: 400,\n                            damping: 17\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-[#E0E0E0] to-[#C4C4C4] w-[36px] h-[36px] lg:w-[44px] lg:h-[44px] rounded-full flex items-center justify-center overflow-hidden ring-2 ring-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: \"/icons/owl.png\",\n                                alt: t('header.profile'),\n                                width: 44,\n                                height: 44,\n                                className: \"w-full h-full object-cover h-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col justify-center min-w-0 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-sm lg:text-base text-gray-800 truncate\",\n                                children: userBalance\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined),\n                            walletAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 font-mono truncate\",\n                                children: truncateAddress(walletAddress)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: `cursor-pointer transition-all duration-300 p-1 rounded-full hover:bg-white/50 ${dropdownOpen ? 'rotate-180 bg-white/50' : ''}`,\n                        whileHover: {\n                            scale: 1.1\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        transition: {\n                            type: 'spring',\n                            stiffness: 400,\n                            damping: 17\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: \"/icons/dropdown.svg\",\n                            alt: t('header.dropdown'),\n                            width: 16,\n                            height: 16,\n                            className: \"lg:w-5 lg:h-5 opacity-70 h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: dropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"absolute top-full mt-2 w-full bg-white rounded-xl shadow-xl z-50 border border-gray-100 overflow-hidden backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    transition: {\n                        type: 'spring',\n                        stiffness: 300,\n                        damping: 25,\n                        duration: 0.2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: [\n                            walletAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 bg-gray-50 border-b border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16,\n                                                        className: \"text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono text-sm text-gray-700\",\n                                                        children: truncateAddress(walletAddress)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                onClick: copyAddress,\n                                                className: \"p-1 hover:bg-gray-200 rounded transition-colors\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: t('balance.availableBalance', {\n                                            amount: parseFloat(userBalance).toLocaleString()\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors\",\n                                onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.PROFILE),\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 18,\n                                        className: \"text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('navigation.profile')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors\",\n                                onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.SETTINGS),\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 18,\n                                        className: \"text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('navigation.settings')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-red-50 cursor-pointer flex items-center gap-3 text-red-600 transition-colors border-t border-gray-100\",\n                                onClick: logout,\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('dashboard.disconnect')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfileDropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/header/ProfileDropdown.tsx\n");

/***/ })

};
;