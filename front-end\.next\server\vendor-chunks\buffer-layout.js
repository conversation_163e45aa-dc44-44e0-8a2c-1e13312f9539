"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-layout";
exports.ids = ["vendor-chunks/buffer-layout"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffer-layout/lib/Layout.js":
/*!**************************************************!*\
  !*** ./node_modules/buffer-layout/lib/Layout.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/* The MIT License (MIT)\n *\n * Copyright 2015-2018 Peter A. Bigot\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * Support for translating between Buffer instances and JavaScript\n * native types.\n *\n * {@link module:Layout~Layout|Layout} is the basis of a class\n * hierarchy that associates property names with sequences of encoded\n * bytes.\n *\n * Layouts are supported for these scalar (numeric) types:\n * * {@link module:Layout~UInt|Unsigned integers in little-endian\n *   format} with {@link module:Layout.u8|8-bit}, {@link\n *   module:Layout.u16|16-bit}, {@link module:Layout.u24|24-bit},\n *   {@link module:Layout.u32|32-bit}, {@link\n *   module:Layout.u40|40-bit}, and {@link module:Layout.u48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~UIntBE|Unsigned integers in big-endian\n *   format} with {@link module:Layout.u16be|16-bit}, {@link\n *   module:Layout.u24be|24-bit}, {@link module:Layout.u32be|32-bit},\n *   {@link module:Layout.u40be|40-bit}, and {@link\n *   module:Layout.u48be|48-bit} representation ranges;\n * * {@link module:Layout~Int|Signed integers in little-endian\n *   format} with {@link module:Layout.s8|8-bit}, {@link\n *   module:Layout.s16|16-bit}, {@link module:Layout.s24|24-bit},\n *   {@link module:Layout.s32|32-bit}, {@link\n *   module:Layout.s40|40-bit}, and {@link module:Layout.s48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~IntBE|Signed integers in big-endian format}\n *   with {@link module:Layout.s16be|16-bit}, {@link\n *   module:Layout.s24be|24-bit}, {@link module:Layout.s32be|32-bit},\n *   {@link module:Layout.s40be|40-bit}, and {@link\n *   module:Layout.s48be|48-bit} representation ranges;\n * * 64-bit integral values that decode to an exact (if magnitude is\n *   less than 2^53) or nearby integral Number in {@link\n *   module:Layout.nu64|unsigned little-endian}, {@link\n *   module:Layout.nu64be|unsigned big-endian}, {@link\n *   module:Layout.ns64|signed little-endian}, and {@link\n *   module:Layout.ns64be|unsigned big-endian} encodings;\n * * 32-bit floating point values with {@link\n *   module:Layout.f32|little-endian} and {@link\n *   module:Layout.f32be|big-endian} representations;\n * * 64-bit floating point values with {@link\n *   module:Layout.f64|little-endian} and {@link\n *   module:Layout.f64be|big-endian} representations;\n * * {@link module:Layout.const|Constants} that take no space in the\n *   encoded expression.\n *\n * and for these aggregate types:\n * * {@link module:Layout.seq|Sequence}s of instances of a {@link\n *   module:Layout~Layout|Layout}, with JavaScript representation as\n *   an Array and constant or data-dependent {@link\n *   module:Layout~Sequence#count|length};\n * * {@link module:Layout.struct|Structure}s that aggregate a\n *   heterogeneous sequence of {@link module:Layout~Layout|Layout}\n *   instances, with JavaScript representation as an Object;\n * * {@link module:Layout.union|Union}s that support multiple {@link\n *   module:Layout~VariantLayout|variant layouts} over a fixed\n *   (padded) or variable (not padded) span of bytes, using an\n *   unsigned integer at the start of the data or a separate {@link\n *   module:Layout.unionLayoutDiscriminator|layout element} to\n *   determine which layout to use when interpreting the buffer\n *   contents;\n * * {@link module:Layout.bits|BitStructure}s that contain a sequence\n *   of individual {@link\n *   module:Layout~BitStructure#addField|BitField}s packed into an 8,\n *   16, 24, or 32-bit unsigned integer starting at the least- or\n *   most-significant bit;\n * * {@link module:Layout.cstr|C strings} of varying length;\n * * {@link module:Layout.blob|Blobs} of fixed- or variable-{@link\n *   module:Layout~Blob#length|length} raw data.\n *\n * All {@link module:Layout~Layout|Layout} instances are immutable\n * after construction, to prevent internal state from becoming\n * inconsistent.\n *\n * @local Layout\n * @local ExternalLayout\n * @local GreedyCount\n * @local OffsetLayout\n * @local UInt\n * @local UIntBE\n * @local Int\n * @local IntBE\n * @local NearUInt64\n * @local NearUInt64BE\n * @local NearInt64\n * @local NearInt64BE\n * @local Float\n * @local FloatBE\n * @local Double\n * @local DoubleBE\n * @local Sequence\n * @local Structure\n * @local UnionDiscriminator\n * @local UnionLayoutDiscriminator\n * @local Union\n * @local VariantLayout\n * @local BitStructure\n * @local BitField\n * @local Boolean\n * @local Blob\n * @local CString\n * @local Constant\n * @local bindConstructorLayout\n * @module Layout\n * @license MIT\n * <AUTHOR> A. Bigot\n * @see {@link https://github.com/pabigot/buffer-layout|buffer-layout on GitHub}\n */\n\n\n\n/**\n * Base class for layout objects.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * Layout#encode|encode} or {@link Layout#decode|decode} functions.\n *\n * @param {Number} span - Initializer for {@link Layout#span|span}.  The\n * parameter must be an integer; a negative value signifies that the\n * span is {@link Layout#getSpan|value-specific}.\n *\n * @param {string} [property] - Initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n */\nclass Layout {\n  constructor(span, property) {\n    if (!Number.isInteger(span)) {\n      throw new TypeError('span must be an integer');\n    }\n\n    /** The span of the layout in bytes.\n     *\n     * Positive values are generally expected.\n     *\n     * Zero will only appear in {@link Constant}s and in {@link\n     * Sequence}s where the {@link Sequence#count|count} is zero.\n     *\n     * A negative value indicates that the span is value-specific, and\n     * must be obtained using {@link Layout#getSpan|getSpan}. */\n    this.span = span;\n\n    /** The property name used when this layout is represented in an\n     * Object.\n     *\n     * Used only for layouts that {@link Layout#decode|decode} to Object\n     * instances.  If left undefined the span of the unnamed layout will\n     * be treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Function to create an Object into which decoded properties will\n   * be written.\n   *\n   * Used only for layouts that {@link Layout#decode|decode} to Object\n   * instances, which means:\n   * * {@link Structure}\n   * * {@link Union}\n   * * {@link VariantLayout}\n   * * {@link BitStructure}\n   *\n   * If left undefined the JavaScript representation of these layouts\n   * will be Object instances.\n   *\n   * See {@link bindConstructorLayout}.\n   */\n  makeDestinationObject() {\n    return {};\n  }\n\n  /**\n   * Decode from a Buffer into an JavaScript value.\n   *\n   * @param {Buffer} b - the buffer from which encoded data is read.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {(Number|Array|Object)} - the value of the decoded data.\n   *\n   * @abstract\n   */\n  decode(b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Encode a JavaScript value into a Buffer.\n   *\n   * @param {(Number|Array|Object)} src - the value to be encoded into\n   * the buffer.  The type accepted depends on the (sub-)type of {@link\n   * Layout}.\n   *\n   * @param {Buffer} b - the buffer into which encoded data will be\n   * written.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {Number} - the number of bytes encoded, including the\n   * space skipped for internal padding, but excluding data such as\n   * {@link Sequence#count|lengths} when stored {@link\n   * ExternalLayout|externally}.  This is the adjustment to `offset`\n   * producing the offset where data for the next layout would be\n   * written.\n   *\n   * @abstract\n   */\n  encode(src, b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Calculate the span of a specific instance of a layout.\n   *\n   * @param {Buffer} b - the buffer that contains an encoded instance.\n   *\n   * @param {Number} [offset] - the offset at which the encoded instance\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @return {Number} - the number of bytes covered by the layout\n   * instance.  If this method is not overridden in a subclass the\n   * definition-time constant {@link Layout#span|span} will be\n   * returned.\n   *\n   * @throws {RangeError} - if the length of the value cannot be\n   * determined.\n   */\n  getSpan(b, offset) {\n    if (0 > this.span) {\n      throw new RangeError('indeterminate span');\n    }\n    return this.span;\n  }\n\n  /**\n   * Replicate the layout using a new property.\n   *\n   * This function must be used to get a structurally-equivalent layout\n   * with a different name since all {@link Layout} instances are\n   * immutable.\n   *\n   * **NOTE** This is a shallow copy.  All fields except {@link\n   * Layout#property|property} are strictly equal to the origin layout.\n   *\n   * @param {String} property - the value for {@link\n   * Layout#property|property} in the replica.\n   *\n   * @returns {Layout} - the copy with {@link Layout#property|property}\n   * set to `property`.\n   */\n  replicate(property) {\n    const rv = Object.create(this.constructor.prototype);\n    Object.assign(rv, this);\n    rv.property = property;\n    return rv;\n  }\n\n  /**\n   * Create an object from layout properties and an array of values.\n   *\n   * **NOTE** This function returns `undefined` if invoked on a layout\n   * that does not return its value as an Object.  Objects are\n   * returned for things that are a {@link Structure}, which includes\n   * {@link VariantLayout|variant layouts} if they are structures, and\n   * excludes {@link Union}s.  If you want this feature for a union\n   * you must use {@link Union.getVariant|getVariant} to select the\n   * desired layout.\n   *\n   * @param {Array} values - an array of values that correspond to the\n   * default order for properties.  As with {@link Layout#decode|decode}\n   * layout elements that have no property name are skipped when\n   * iterating over the array values.  Only the top-level properties are\n   * assigned; arguments are not assigned to properties of contained\n   * layouts.  Any unused values are ignored.\n   *\n   * @return {(Object|undefined)}\n   */\n  fromArray(values) {\n    return undefined;\n  }\n}\nexports.Layout = Layout;\n\n/* Provide text that carries a name (such as for a function that will\n * be throwing an error) annotated with the property of a given layout\n * (such as one for which the value was unacceptable).\n *\n * @ignore */\nfunction nameWithProperty(name, lo) {\n  if (lo.property) {\n    return name + '[' + lo.property + ']';\n  }\n  return name;\n}\nexports.nameWithProperty = nameWithProperty;\n\n/**\n * Augment a class so that instances can be encoded/decoded using a\n * given layout.\n *\n * Calling this function couples `Class` with `layout` in several ways:\n *\n * * `Class.layout_` becomes a static member property equal to `layout`;\n * * `layout.boundConstructor_` becomes a static member property equal\n *    to `Class`;\n * * The {@link Layout#makeDestinationObject|makeDestinationObject()}\n *   property of `layout` is set to a function that returns a `new\n *   Class()`;\n * * `Class.decode(b, offset)` becomes a static member function that\n *   delegates to {@link Layout#decode|layout.decode}.  The\n *   synthesized function may be captured and extended.\n * * `Class.prototype.encode(b, offset)` provides an instance member\n *   function that delegates to {@link Layout#encode|layout.encode}\n *   with `src` set to `this`.  The synthesized function may be\n *   captured and extended, but when the extension is invoked `this`\n *   must be explicitly bound to the instance.\n *\n * @param {class} Class - a JavaScript class with a nullary\n * constructor.\n *\n * @param {Layout} layout - the {@link Layout} instance used to encode\n * instances of `Class`.\n */\nfunction bindConstructorLayout(Class, layout) {\n  if ('function' !== typeof Class) {\n    throw new TypeError('Class must be constructor');\n  }\n  if (Class.hasOwnProperty('layout_')) {\n    throw new Error('Class is already bound to a layout');\n  }\n  if (!(layout && (layout instanceof Layout))) {\n    throw new TypeError('layout must be a Layout');\n  }\n  if (layout.hasOwnProperty('boundConstructor_')) {\n    throw new Error('layout is already bound to a constructor');\n  }\n  Class.layout_ = layout;\n  layout.boundConstructor_ = Class;\n  layout.makeDestinationObject = (() => new Class());\n  Object.defineProperty(Class.prototype, 'encode', {\n    value: function(b, offset) {\n      return layout.encode(this, b, offset);\n    },\n    writable: true,\n  });\n  Object.defineProperty(Class, 'decode', {\n    value: function(b, offset) {\n      return layout.decode(b, offset);\n    },\n    writable: true,\n  });\n}\nexports.bindConstructorLayout = bindConstructorLayout;\n\n/**\n * An object that behaves like a layout but does not consume space\n * within its containing layout.\n *\n * This is primarily used to obtain metadata about a member, such as a\n * {@link OffsetLayout} that can provide data about a {@link\n * Layout#getSpan|value-specific span}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support {@link\n * ExternalLayout#isCount|isCount} or other {@link Layout} functions.\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n * @augments {Layout}\n */\nclass ExternalLayout extends Layout {\n  /**\n   * Return `true` iff the external layout decodes to an unsigned\n   * integer layout.\n   *\n   * In that case it can be used as the source of {@link\n   * Sequence#count|Sequence counts}, {@link Blob#length|Blob lengths},\n   * or as {@link UnionLayoutDiscriminator#layout|external union\n   * discriminators}.\n   *\n   * @abstract\n   */\n  isCount() {\n    throw new Error('ExternalLayout is abstract');\n  }\n}\n\n/**\n * An {@link ExternalLayout} that determines its {@link\n * Layout#decode|value} based on offset into and length of the buffer\n * on which it is invoked.\n *\n * *Factory*: {@link module:Layout.greedy|greedy}\n *\n * @param {Number} [elementSpan] - initializer for {@link\n * GreedyCount#elementSpan|elementSpan}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {ExternalLayout}\n */\nclass GreedyCount extends ExternalLayout {\n  constructor(elementSpan, property) {\n    if (undefined === elementSpan) {\n      elementSpan = 1;\n    }\n    if ((!Number.isInteger(elementSpan)) || (0 >= elementSpan)) {\n      throw new TypeError('elementSpan must be a (positive) integer');\n    }\n    super(-1, property);\n\n    /** The layout for individual elements of the sequence.  The value\n     * must be a positive integer.  If not provided, the value will be\n     * 1. */\n    this.elementSpan = elementSpan;\n  }\n\n  /** @override */\n  isCount() {\n    return true;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rem = b.length - offset;\n    return Math.floor(rem / this.elementSpan);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    return 0;\n  }\n}\n\n/**\n * An {@link ExternalLayout} that supports accessing a {@link Layout}\n * at a fixed offset from the start of another Layout.  The offset may\n * be before, within, or after the base layout.\n *\n * *Factory*: {@link module:Layout.offset|offset}\n *\n * @param {Layout} layout - initializer for {@link\n * OffsetLayout#layout|layout}, modulo `property`.\n *\n * @param {Number} [offset] - Initializes {@link\n * OffsetLayout#offset|offset}.  Defaults to zero.\n *\n * @param {string} [property] - Optional new property name for a\n * {@link Layout#replicate| replica} of `layout` to be used as {@link\n * OffsetLayout#layout|layout}.  If not provided the `layout` is used\n * unchanged.\n *\n * @augments {Layout}\n */\nclass OffsetLayout extends ExternalLayout {\n  constructor(layout, offset, property) {\n    if (!(layout instanceof Layout)) {\n      throw new TypeError('layout must be a Layout');\n    }\n\n    if (undefined === offset) {\n      offset = 0;\n    } else if (!Number.isInteger(offset)) {\n      throw new TypeError('offset must be integer or undefined');\n    }\n\n    super(layout.span, property || layout.property);\n\n    /** The subordinated layout. */\n    this.layout = layout;\n\n    /** The location of {@link OffsetLayout#layout} relative to the\n     * start of another layout.\n     *\n     * The value may be positive or negative, but an error will thrown\n     * if at the point of use it goes outside the span of the Buffer\n     * being accessed.  */\n    this.offset = offset;\n  }\n\n  /** @override */\n  isCount() {\n    return ((this.layout instanceof UInt)\n            || (this.layout instanceof UIntBE));\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.decode(b, offset + this.offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.encode(src, b, offset + this.offset);\n  }\n}\n\n/**\n * Represent an unsigned integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.u8|u8}, {@link\n *  module:Layout.u16|u16}, {@link module:Layout.u24|u24}, {@link\n *  module:Layout.u32|u32}, {@link module:Layout.u40|u40}, {@link\n *  module:Layout.u48|u48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UInt extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent an unsigned integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.u8be|u8be}, {@link\n * module:Layout.u16be|u16be}, {@link module:Layout.u24be|u24be},\n * {@link module:Layout.u32be|u32be}, {@link\n * module:Layout.u40be|u40be}, {@link module:Layout.u48be|u48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UIntBE extends Layout {\n  constructor(span, property) {\n    super( span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.s8|s8}, {@link\n *  module:Layout.s16|s16}, {@link module:Layout.s24|s24}, {@link\n *  module:Layout.s32|s32}, {@link module:Layout.s40|s40}, {@link\n *  module:Layout.s48|s48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Int extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.s8be|s8be}, {@link\n * module:Layout.s16be|s16be}, {@link module:Layout.s24be|s24be},\n * {@link module:Layout.s32be|s32be}, {@link\n * module:Layout.s40be|s40be}, {@link module:Layout.s48be|s48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass IntBE extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\nconst V2E32 = Math.pow(2, 32);\n\n/* True modulus high and low 32-bit words, where low word is always\n * non-negative. */\nfunction divmodInt64(src) {\n  const hi32 = Math.floor(src / V2E32);\n  const lo32 = src - (hi32 * V2E32);\n  return {hi32, lo32};\n}\n/* Reconstruct Number from quotient and non-negative remainder */\nfunction roundedInt64(hi32, lo32) {\n  return hi32 * V2E32 + lo32;\n}\n\n/**\n * Represent an unsigned 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64|nu64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readUInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeUInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent an unsigned 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64be|nu64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readUInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64|ns64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64be|ns64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f32|f32}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Float extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatLE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f32be|f32be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass FloatBE extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatBE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f64|f64}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Double extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleLE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f64be|f64be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass DoubleBE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleBE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a contiguous sequence of a specific layout as an Array.\n *\n * *Factory*: {@link module:Layout.seq|seq}\n *\n * @param {Layout} elementLayout - initializer for {@link\n * Sequence#elementLayout|elementLayout}.\n *\n * @param {(Number|ExternalLayout)} count - initializer for {@link\n * Sequence#count|count}.  The parameter must be either a positive\n * integer or an instance of {@link ExternalLayout}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Sequence extends Layout {\n  constructor(elementLayout, count, property) {\n    if (!(elementLayout instanceof Layout)) {\n      throw new TypeError('elementLayout must be a Layout');\n    }\n    if (!(((count instanceof ExternalLayout) && count.isCount())\n          || (Number.isInteger(count) && (0 <= count)))) {\n      throw new TypeError('count must be non-negative integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n    let span = -1;\n    if ((!(count instanceof ExternalLayout))\n        && (0 < elementLayout.span)) {\n      span = count * elementLayout.span;\n    }\n\n    super(span, property);\n\n    /** The layout for individual elements of the sequence. */\n    this.elementLayout = elementLayout;\n\n    /** The number of elements in the sequence.\n     *\n     * This will be either a non-negative integer or an instance of\n     * {@link ExternalLayout} for which {@link\n     * ExternalLayout#isCount|isCount()} is `true`. */\n    this.count = count;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    if (0 < this.elementLayout.span) {\n      span = count * this.elementLayout.span;\n    } else {\n      let idx = 0;\n      while (idx < count) {\n        span += this.elementLayout.getSpan(b, offset + span);\n        ++idx;\n      }\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rv = [];\n    let i = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    while (i < count) {\n      rv.push(this.elementLayout.decode(b, offset));\n      offset += this.elementLayout.getSpan(b, offset);\n      i += 1;\n    }\n    return rv;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Sequence}.\n   *\n   * **NOTE** If `src` is shorter than {@link Sequence#count|count} then\n   * the unused space in the buffer is left unchanged.  If `src` is\n   * longer than {@link Sequence#count|count} the unneeded elements are\n   * ignored.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const elo = this.elementLayout;\n    const span = src.reduce((span, v) => {\n      return span + elo.encode(v, b, offset + span);\n    }, 0);\n    if (this.count instanceof ExternalLayout) {\n      this.count.encode(src.length, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Represent a contiguous sequence of arbitrary layout elements as an\n * Object.\n *\n * *Factory*: {@link module:Layout.struct|struct}\n *\n * **NOTE** The {@link Layout#span|span} of the structure is variable\n * if any layout in {@link Structure#fields|fields} has a variable\n * span.  When {@link Layout#encode|encoding} we must have a value for\n * all variable-length fields, or we wouldn't be able to figure out\n * how much space to use for storage.  We can only identify the value\n * for a field when it has a {@link Layout#property|property}.  As\n * such, although a structure may contain both unnamed fields and\n * variable-length fields, it cannot contain an unnamed\n * variable-length field.\n *\n * @param {Layout[]} fields - initializer for {@link\n * Structure#fields|fields}.  An error is raised if this contains a\n * variable-length field for which a {@link Layout#property|property}\n * is not defined.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @param {Boolean} [decodePrefixes] - initializer for {@link\n * Structure#decodePrefixes|property}.\n *\n * @throws {Error} - if `fields` contains an unnamed variable-length\n * layout.\n *\n * @augments {Layout}\n */\nclass Structure extends Layout {\n  constructor(fields, property, decodePrefixes) {\n    if (!(Array.isArray(fields)\n          && fields.reduce((acc, v) => acc && (v instanceof Layout), true))) {\n      throw new TypeError('fields must be array of Layout instances');\n    }\n    if (('boolean' === typeof property)\n        && (undefined === decodePrefixes)) {\n      decodePrefixes = property;\n      property = undefined;\n    }\n\n    /* Verify absence of unnamed variable-length fields. */\n    for (const fd of fields) {\n      if ((0 > fd.span)\n          && (undefined === fd.property)) {\n        throw new Error('fields cannot contain unnamed variable-length layout');\n      }\n    }\n\n    let span = -1;\n    try {\n      span = fields.reduce((span, fd) => span + fd.getSpan(), 0);\n    } catch (e) {\n    }\n    super(span, property);\n\n    /** The sequence of {@link Layout} values that comprise the\n     * structure.\n     *\n     * The individual elements need not be the same type, and may be\n     * either scalar or aggregate layouts.  If a member layout leaves\n     * its {@link Layout#property|property} undefined the\n     * corresponding region of the buffer associated with the element\n     * will not be mutated.\n     *\n     * @type {Layout[]} */\n    this.fields = fields;\n\n    /** Control behavior of {@link Layout#decode|decode()} given short\n     * buffers.\n     *\n     * In some situations a structure many be extended with additional\n     * fields over time, with older installations providing only a\n     * prefix of the full structure.  If this property is `true`\n     * decoding will accept those buffers and leave subsequent fields\n     * undefined, as long as the buffer ends at a field boundary.\n     * Defaults to `false`. */\n    this.decodePrefixes = !!decodePrefixes;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    try {\n      span = this.fields.reduce((span, fd) => {\n        const fsp = fd.getSpan(b, offset);\n        offset += fsp;\n        return span + fsp;\n      }, 0);\n    } catch (e) {\n      throw new RangeError('indeterminate span');\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(b, offset);\n      }\n      offset += fd.getSpan(b, offset);\n      if (this.decodePrefixes\n          && (b.length === offset)) {\n        break;\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Structure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the buffer is\n   * left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const firstOffset = offset;\n    let lastOffset = 0;\n    let lastWrote = 0;\n    for (const fd of this.fields) {\n      let span = fd.span;\n      lastWrote = (0 < span) ? span : 0;\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          lastWrote = fd.encode(fv, b, offset);\n          if (0 > span) {\n            /* Read the as-encoded span, which is not necessarily the\n             * same as what we wrote. */\n            span = fd.getSpan(b, offset);\n          }\n        }\n      }\n      lastOffset = offset;\n      offset += span;\n    }\n    /* Use (lastOffset + lastWrote) instead of offset because the last\n     * item may have had a dynamic length and we don't want to include\n     * the padding between it and the end of the space reserved for\n     * it. */\n    return (lastOffset + lastWrote) - firstOffset;\n  }\n\n  /** @override */\n  fromArray(values) {\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if ((undefined !== fd.property)\n          && (0 < values.length)) {\n        dest[fd.property] = values.shift();\n      }\n    }\n    return dest;\n  }\n\n  /**\n   * Get access to the layout of a given property.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Layout} - the layout associated with `property`, or\n   * undefined if there is no such property.\n   */\n  layoutFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n\n  /**\n   * Get the offset of a structure member.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Number} - the offset in bytes to the start of `property`\n   * within the structure, or undefined if `property` is not a field\n   * within the structure.  If the property is a member but follows a\n   * variable-length structure member a negative number will be\n   * returned.\n   */\n  offsetOf(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    let offset = 0;\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return offset;\n      }\n      if (0 > fd.span) {\n        offset = -1;\n      } else if (0 <= offset) {\n        offset += fd.span;\n      }\n    }\n  }\n}\n\n/**\n * An object that can provide a {@link\n * Union#discriminator|discriminator} API for {@link Union}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * UnionDiscriminator#encode|encode} or {@link\n * UnionDiscriminator#decode|decode} functions.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}.\n *\n * @abstract\n */\nclass UnionDiscriminator {\n  constructor(property) {\n    /** The {@link Layout#property|property} to be used when the\n     * discriminator is referenced in isolation (generally when {@link\n     * Union#decode|Union decode} cannot delegate to a specific\n     * variant). */\n    this.property = property;\n  }\n\n  /** Analog to {@link Layout#decode|Layout decode} for union discriminators.\n   *\n   * The implementation of this method need not reference the buffer if\n   * variant information is available through other means. */\n  decode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n\n  /** Analog to {@link Layout#decode|Layout encode} for union discriminators.\n   *\n   * The implementation of this method need not store the value if\n   * variant information is maintained through other means. */\n  encode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n}\n\n/**\n * An object that can provide a {@link\n * UnionDiscriminator|discriminator API} for {@link Union} using an\n * unsigned integral {@link Layout} instance located either inside or\n * outside the union.\n *\n * @param {ExternalLayout} layout - initializes {@link\n * UnionLayoutDiscriminator#layout|layout}.  Must satisfy {@link\n * ExternalLayout#isCount|isCount()}.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}, superseding the property\n * from `layout`, but defaulting to `variant` if neither `property`\n * nor layout provide a property name.\n *\n * @augments {UnionDiscriminator}\n */\nclass UnionLayoutDiscriminator extends UnionDiscriminator {\n  constructor(layout, property) {\n    if (!((layout instanceof ExternalLayout)\n          && layout.isCount())) {\n      throw new TypeError('layout must be an unsigned integer ExternalLayout');\n    }\n\n    super(property || layout.property || 'variant');\n\n    /** The {@link ExternalLayout} used to access the discriminator\n     * value. */\n    this.layout = layout;\n  }\n\n  /** Delegate decoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  decode(b, offset) {\n    return this.layout.decode(b, offset);\n  }\n\n  /** Delegate encoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  encode(src, b, offset) {\n    return this.layout.encode(src, b, offset);\n  }\n}\n\n/**\n * Represent any number of span-compatible layouts.\n *\n * *Factory*: {@link module:Layout.union|union}\n *\n * If the union has a {@link Union#defaultLayout|default layout} that\n * layout must have a non-negative {@link Layout#span|span}.  The span\n * of a fixed-span union includes its {@link\n * Union#discriminator|discriminator} if the variant is a {@link\n * Union#usesPrefixDiscriminator|prefix of the union}, plus the span\n * of its {@link Union#defaultLayout|default layout}.\n *\n * If the union does not have a default layout then the encoded span\n * of the union depends on the encoded span of its variant (which may\n * be fixed or variable).\n *\n * {@link VariantLayout#layout|Variant layout}s are added through\n * {@link Union#addVariant|addVariant}.  If the union has a default\n * layout, the span of the {@link VariantLayout#layout|layout\n * contained by the variant} must not exceed the span of the {@link\n * Union#defaultLayout|default layout} (minus the span of a {@link\n * Union#usesPrefixDiscriminator|prefix disriminator}, if used).  The\n * span of the variant will equal the span of the union itself.\n *\n * The variant for a buffer can only be identified from the {@link\n * Union#discriminator|discriminator} {@link\n * UnionDiscriminator#property|property} (in the case of the {@link\n * Union#defaultLayout|default layout}), or by using {@link\n * Union#getVariant|getVariant} and examining the resulting {@link\n * VariantLayout} instance.\n *\n * A variant compatible with a JavaScript object can be identified\n * using {@link Union#getSourceVariant|getSourceVariant}.\n *\n * @param {(UnionDiscriminator|ExternalLayout|Layout)} discr - How to\n * identify the layout used to interpret the union contents.  The\n * parameter must be an instance of {@link UnionDiscriminator}, an\n * {@link ExternalLayout} that satisfies {@link\n * ExternalLayout#isCount|isCount()}, or {@link UInt} (or {@link\n * UIntBE}).  When a non-external layout element is passed the layout\n * appears at the start of the union.  In all cases the (synthesized)\n * {@link UnionDiscriminator} instance is recorded as {@link\n * Union#discriminator|discriminator}.\n *\n * @param {(Layout|null)} defaultLayout - initializer for {@link\n * Union#defaultLayout|defaultLayout}.  If absent defaults to `null`.\n * If `null` there is no default layout: the union has data-dependent\n * length and attempts to decode or encode unrecognized variants will\n * throw an exception.  A {@link Layout} instance must have a\n * non-negative {@link Layout#span|span}, and if it lacks a {@link\n * Layout#property|property} the {@link\n * Union#defaultLayout|defaultLayout} will be a {@link\n * Layout#replicate|replica} with property `content`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Union extends Layout {\n  constructor(discr, defaultLayout, property) {\n    const upv = ((discr instanceof UInt)\n               || (discr instanceof UIntBE));\n    if (upv) {\n      discr = new UnionLayoutDiscriminator(new OffsetLayout(discr));\n    } else if ((discr instanceof ExternalLayout)\n               && discr.isCount()) {\n      discr = new UnionLayoutDiscriminator(discr);\n    } else if (!(discr instanceof UnionDiscriminator)) {\n      throw new TypeError('discr must be a UnionDiscriminator '\n                          + 'or an unsigned integer layout');\n    }\n    if (undefined === defaultLayout) {\n      defaultLayout = null;\n    }\n    if (!((null === defaultLayout)\n          || (defaultLayout instanceof Layout))) {\n      throw new TypeError('defaultLayout must be null or a Layout');\n    }\n    if (null !== defaultLayout) {\n      if (0 > defaultLayout.span) {\n        throw new Error('defaultLayout must have constant span');\n      }\n      if (undefined === defaultLayout.property) {\n        defaultLayout = defaultLayout.replicate('content');\n      }\n    }\n\n    /* The union span can be estimated only if there's a default\n     * layout.  The union spans its default layout, plus any prefix\n     * variant layout.  By construction both layouts, if present, have\n     * non-negative span. */\n    let span = -1;\n    if (defaultLayout) {\n      span = defaultLayout.span;\n      if ((0 <= span) && upv) {\n        span += discr.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The interface for the discriminator value in isolation.\n     *\n     * This a {@link UnionDiscriminator} either passed to the\n     * constructor or synthesized from the `discr` constructor\n     * argument.  {@link\n     * Union#usesPrefixDiscriminator|usesPrefixDiscriminator} will be\n     * `true` iff the `discr` parameter was a non-offset {@link\n     * Layout} instance. */\n    this.discriminator = discr;\n\n    /** `true` if the {@link Union#discriminator|discriminator} is the\n     * first field in the union.\n     *\n     * If `false` the discriminator is obtained from somewhere\n     * else. */\n    this.usesPrefixDiscriminator = upv;\n\n    /** The layout for non-discriminator content when the value of the\n     * discriminator is not recognized.\n     *\n     * This is the value passed to the constructor.  It is\n     * structurally equivalent to the second component of {@link\n     * Union#layout|layout} but may have a different property\n     * name. */\n    this.defaultLayout = defaultLayout;\n\n    /** A registry of allowed variants.\n     *\n     * The keys are unsigned integers which should be compatible with\n     * {@link Union.discriminator|discriminator}.  The property value\n     * is the corresponding {@link VariantLayout} instances assigned\n     * to this union by {@link Union#addVariant|addVariant}.\n     *\n     * **NOTE** The registry remains mutable so that variants can be\n     * {@link Union#addVariant|added} at any time.  Users should not\n     * manipulate the content of this property. */\n    this.registry = {};\n\n    /* Private variable used when invoking getSourceVariant */\n    let boundGetSourceVariant = this.defaultGetSourceVariant.bind(this);\n\n    /** Function to infer the variant selected by a source object.\n     *\n     * Defaults to {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant} but may\n     * be overridden using {@link\n     * Union#configGetSourceVariant|configGetSourceVariant}.\n     *\n     * @param {Object} src - as with {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * @returns {(undefined|VariantLayout)} The default variant\n     * (`undefined`) or first registered variant that uses a property\n     * available in `src`. */\n    this.getSourceVariant = function(src) {\n      return boundGetSourceVariant(src);\n    };\n\n    /** Function to override the implementation of {@link\n     * Union#getSourceVariant|getSourceVariant}.\n     *\n     * Use this if the desired variant cannot be identified using the\n     * algorithm of {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * **NOTE** The provided function will be invoked bound to this\n     * Union instance, providing local access to {@link\n     * Union#registry|registry}.\n     *\n     * @param {Function} gsv - a function that follows the API of\n     * {@link Union#defaultGetSourceVariant|defaultGetSourceVariant}. */\n    this.configGetSourceVariant = function(gsv) {\n      boundGetSourceVariant = gsv.bind(this);\n    };\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Default layouts always have non-negative span, so we don't have\n     * one and we have to recognize the variant which will in turn\n     * determine the span. */\n    const vlo = this.getVariant(b, offset);\n    if (!vlo) {\n      throw new Error('unable to determine span for unrecognized variant');\n    }\n    return vlo.getSpan(b, offset);\n  }\n\n  /**\n   * Method to infer a registered Union variant compatible with `src`.\n   *\n   * The first satisified rule in the following sequence defines the\n   * return value:\n   * * If `src` has properties matching the Union discriminator and\n   *   the default layout, `undefined` is returned regardless of the\n   *   value of the discriminator property (this ensures the default\n   *   layout will be used);\n   * * If `src` has a property matching the Union discriminator, the\n   *   value of the discriminator identifies a registered variant, and\n   *   either (a) the variant has no layout, or (b) `src` has the\n   *   variant's property, then the variant is returned (because the\n   *   source satisfies the constraints of the variant it identifies);\n   * * If `src` does not have a property matching the Union\n   *   discriminator, but does have a property matching a registered\n   *   variant, then the variant is returned (because the source\n   *   matches a variant without an explicit conflict);\n   * * An error is thrown (because we either can't identify a variant,\n   *   or we were explicitly told the variant but can't satisfy it).\n   *\n   * @param {Object} src - an object presumed to be compatible with\n   * the content of the Union.\n   *\n   * @return {(undefined|VariantLayout)} - as described above.\n   *\n   * @throws {Error} - if `src` cannot be associated with a default or\n   * registered variant.\n   */\n  defaultGetSourceVariant(src) {\n    if (src.hasOwnProperty(this.discriminator.property)) {\n      if (this.defaultLayout\n          && src.hasOwnProperty(this.defaultLayout.property)) {\n        return undefined;\n      }\n      const vlo = this.registry[src[this.discriminator.property]];\n      if (vlo\n          && ((!vlo.layout)\n              || src.hasOwnProperty(vlo.property))) {\n        return vlo;\n      }\n    } else {\n      for (const tag in this.registry) {\n        const vlo = this.registry[tag];\n        if (src.hasOwnProperty(vlo.property)) {\n          return vlo;\n        }\n      }\n    }\n    throw new Error('unable to infer src variant');\n  }\n\n  /** Implement {@link Layout#decode|decode} for {@link Union}.\n   *\n   * If the variant is {@link Union#addVariant|registered} the return\n   * value is an instance of that variant, with no explicit\n   * discriminator.  Otherwise the {@link Union#defaultLayout|default\n   * layout} is used to decode the content. */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let dest;\n    const dlo = this.discriminator;\n    const discr = dlo.decode(b, offset);\n    let clo = this.registry[discr];\n    if (undefined === clo) {\n      let contentOffset = 0;\n      clo = this.defaultLayout;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dest = this.makeDestinationObject();\n      dest[dlo.property] = discr;\n      dest[clo.property] = this.defaultLayout.decode(b, offset + contentOffset);\n    } else {\n      dest = clo.decode(b, offset);\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Union}.\n   *\n   * This API assumes the `src` object is consistent with the union's\n   * {@link Union#defaultLayout|default layout}.  To encode variants\n   * use the appropriate variant-specific {@link VariantLayout#encode}\n   * method. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const vlo = this.getSourceVariant(src);\n    if (undefined === vlo) {\n      const dlo = this.discriminator;\n      const clo = this.defaultLayout;\n      let contentOffset = 0;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dlo.encode(src[dlo.property], b, offset);\n      return contentOffset + clo.encode(src[clo.property], b,\n                                        offset + contentOffset);\n    }\n    return vlo.encode(src, b, offset);\n  }\n\n  /** Register a new variant structure within a union.  The newly\n   * created variant is returned.\n   *\n   * @param {Number} variant - initializer for {@link\n   * VariantLayout#variant|variant}.\n   *\n   * @param {Layout} layout - initializer for {@link\n   * VariantLayout#layout|layout}.\n   *\n   * @param {String} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {VariantLayout} */\n  addVariant(variant, layout, property) {\n    const rv = new VariantLayout(this, variant, layout, property);\n    this.registry[variant] = rv;\n    return rv;\n  }\n\n  /**\n   * Get the layout associated with a registered variant.\n   *\n   * If `vb` does not produce a registered variant the function returns\n   * `undefined`.\n   *\n   * @param {(Number|Buffer)} vb - either the variant number, or a\n   * buffer from which the discriminator is to be read.\n   *\n   * @param {Number} offset - offset into `vb` for the start of the\n   * union.  Used only when `vb` is an instance of {Buffer}.\n   *\n   * @return {({VariantLayout}|undefined)}\n   */\n  getVariant(vb, offset) {\n    let variant = vb;\n    if (Buffer.isBuffer(vb)) {\n      if (undefined === offset) {\n        offset = 0;\n      }\n      variant = this.discriminator.decode(vb, offset);\n    }\n    return this.registry[variant];\n  }\n}\n\n/**\n * Represent a specific variant within a containing union.\n *\n * **NOTE** The {@link Layout#span|span} of the variant may include\n * the span of the {@link Union#discriminator|discriminator} used to\n * identify it, but values read and written using the variant strictly\n * conform to the content of {@link VariantLayout#layout|layout}.\n *\n * **NOTE** User code should not invoke this constructor directly.  Use\n * the union {@link Union#addVariant|addVariant} helper method.\n *\n * @param {Union} union - initializer for {@link\n * VariantLayout#union|union}.\n *\n * @param {Number} variant - initializer for {@link\n * VariantLayout#variant|variant}.\n *\n * @param {Layout} [layout] - initializer for {@link\n * VariantLayout#layout|layout}.  If absent the variant carries no\n * data.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.  Unlike many other layouts, variant\n * layouts normally include a property name so they can be identified\n * within their containing {@link Union}.  The property identifier may\n * be absent only if `layout` is is absent.\n *\n * @augments {Layout}\n */\nclass VariantLayout extends Layout {\n  constructor(union, variant, layout, property) {\n    if (!(union instanceof Union)) {\n      throw new TypeError('union must be a Union');\n    }\n    if ((!Number.isInteger(variant)) || (0 > variant)) {\n      throw new TypeError('variant must be a (non-negative) integer');\n    }\n    if (('string' === typeof layout)\n        && (undefined === property)) {\n      property = layout;\n      layout = null;\n    }\n    if (layout) {\n      if (!(layout instanceof Layout)) {\n        throw new TypeError('layout must be a Layout');\n      }\n      if ((null !== union.defaultLayout)\n          && (0 <= layout.span)\n          && (layout.span > union.defaultLayout.span)) {\n        throw new Error('variant span exceeds span of containing union');\n      }\n      if ('string' !== typeof property) {\n        throw new TypeError('variant must have a String property');\n      }\n    }\n    let span = union.span;\n    if (0 > union.span) {\n      span = layout ? layout.span : 0;\n      if ((0 <= span) && union.usesPrefixDiscriminator) {\n        span += union.discriminator.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The {@link Union} to which this variant belongs. */\n    this.union = union;\n\n    /** The unsigned integral value identifying this variant within\n     * the {@link Union#discriminator|discriminator} of the containing\n     * union. */\n    this.variant = variant;\n\n    /** The {@link Layout} to be used when reading/writing the\n     * non-discriminator part of the {@link\n     * VariantLayout#union|union}.  If `null` the variant carries no\n     * data. */\n    this.layout = layout || null;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      /* Will be equal to the containing union span if that is not\n       * variable. */\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    /* Span is defined solely by the variant (and prefix discriminator) */\n    return contentOffset + this.layout.getSpan(b, offset + contentOffset);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    if (this !== this.union.getVariant(b, offset)) {\n      throw new Error('variant mismatch');\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout) {\n      dest[this.property] = this.layout.decode(b, offset + contentOffset);\n    } else if (this.property) {\n      dest[this.property] = true;\n    } else if (this.union.usesPrefixDiscriminator) {\n      dest[this.union.discriminator.property] = this.variant;\n    }\n    return dest;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout\n        && (!src.hasOwnProperty(this.property))) {\n      throw new TypeError('variant lacks property ' + this.property);\n    }\n    this.union.discriminator.encode(this.variant, b, offset);\n    let span = contentOffset;\n    if (this.layout) {\n      this.layout.encode(src[this.property], b, offset + contentOffset);\n      span += this.layout.getSpan(b, offset + contentOffset);\n      if ((0 <= this.union.span)\n          && (span > this.union.span)) {\n        throw new Error('encoded variant overruns containing union');\n      }\n    }\n    return span;\n  }\n\n  /** Delegate {@link Layout#fromArray|fromArray} to {@link\n   * VariantLayout#layout|layout}. */\n  fromArray(values) {\n    if (this.layout) {\n      return this.layout.fromArray(values);\n    }\n  }\n}\n\n/** JavaScript chose to define bitwise operations as operating on\n * signed 32-bit values in 2's complement form, meaning any integer\n * with bit 31 set is going to look negative.  For right shifts that's\n * not a problem, because `>>>` is a logical shift, but for every\n * other bitwise operator we have to compensate for possible negative\n * results. */\nfunction fixBitwiseResult(v) {\n  if (0 > v) {\n    v += 0x100000000;\n  }\n  return v;\n}\n\n/**\n * Contain a sequence of bit fields as an unsigned integer.\n *\n * *Factory*: {@link module:Layout.bits|bits}\n *\n * This is a container element; within it there are {@link BitField}\n * instances that provide the extracted properties.  The container\n * simply defines the aggregate representation and its bit ordering.\n * The representation is an object containing properties with numeric\n * or {@link Boolean} values.\n *\n * {@link BitField}s are added with the {@link\n * BitStructure#addField|addField} and {@link\n * BitStructure#addBoolean|addBoolean} methods.\n\n * @param {Layout} word - initializer for {@link\n * BitStructure#word|word}.  The parameter must be an instance of\n * {@link UInt} (or {@link UIntBE}) that is no more than 4 bytes wide.\n *\n * @param {bool} [msb] - `true` if the bit numbering starts at the\n * most significant bit of the containing word; `false` (default) if\n * it starts at the least significant bit of the containing word.  If\n * the parameter at this position is a string and `property` is\n * `undefined` the value of this argument will instead be used as the\n * value of `property`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass BitStructure extends Layout {\n  constructor(word, msb, property) {\n    if (!((word instanceof UInt)\n          || (word instanceof UIntBE))) {\n      throw new TypeError('word must be a UInt or UIntBE layout');\n    }\n    if (('string' === typeof msb)\n        && (undefined === property)) {\n      property = msb;\n      msb = undefined;\n    }\n    if (4 < word.span) {\n      throw new RangeError('word cannot exceed 32 bits');\n    }\n    super(word.span, property);\n\n    /** The layout used for the packed value.  {@link BitField}\n     * instances are packed sequentially depending on {@link\n     * BitStructure#msb|msb}. */\n    this.word = word;\n\n    /** Whether the bit sequences are packed starting at the most\n     * significant bit growing down (`true`), or the least significant\n     * bit growing up (`false`).\n     *\n     * **NOTE** Regardless of this value, the least significant bit of\n     * any {@link BitField} value is the least significant bit of the\n     * corresponding section of the packed value. */\n    this.msb = !!msb;\n\n    /** The sequence of {@link BitField} layouts that comprise the\n     * packed structure.\n     *\n     * **NOTE** The array remains mutable to allow fields to be {@link\n     * BitStructure#addField|added} after construction.  Users should\n     * not manipulate the content of this property.*/\n    this.fields = [];\n\n    /* Storage for the value.  Capture a variable instead of using an\n     * instance property because we don't want anything to change the\n     * value without going through the mutator. */\n    let value = 0;\n    this._packedSetValue = function(v) {\n      value = fixBitwiseResult(v);\n      return this;\n    };\n    this._packedGetValue = function() {\n      return value;\n    };\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(value);\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link BitStructure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the packed\n   * value is left unmodified.  Unused bits are also left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          fd.encode(fv);\n        }\n      }\n    }\n    return this.word.encode(this._packedGetValue(), b, offset);\n  }\n\n  /** Register a new bitfield with a containing bit structure.  The\n   * resulting bitfield is returned.\n   *\n   * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {BitField} */\n  addField(bits, property) {\n    const bf = new BitField(this, bits, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /** As with {@link BitStructure#addField|addField} for single-bit\n   * fields with `boolean` value representation.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {Boolean} */\n  addBoolean(property) {\n    // This is my Boolean, not the Javascript one.\n    // eslint-disable-next-line no-new-wrappers\n    const bf = new Boolean(this, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /**\n   * Get access to the bit field for a given property.\n   *\n   * @param {String} property - the bit field of interest.\n   *\n   * @return {BitField} - the field associated with `property`, or\n   * undefined if there is no such property.\n   */\n  fieldFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n}\n\n/**\n * Represent a sequence of bits within a {@link BitStructure}.\n *\n * All bit field values are represented as unsigned integers.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addField|addField} helper\n * method.\n *\n * **NOTE** BitField instances are not instances of {@link Layout}\n * since {@link Layout#span|span} measures 8-bit units.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n */\nclass BitField {\n  constructor(container, bits, property) {\n    if (!(container instanceof BitStructure)) {\n      throw new TypeError('container must be a BitStructure');\n    }\n    if ((!Number.isInteger(bits)) || (0 >= bits)) {\n      throw new TypeError('bits must be positive integer');\n    }\n    const totalBits = 8 * container.span;\n    const usedBits = container.fields.reduce((sum, fd) => sum + fd.bits, 0);\n    if ((bits + usedBits) > totalBits) {\n      throw new Error('bits too long for span remainder ('\n                      + (totalBits - usedBits) + ' of '\n                      + totalBits + ' remain)');\n    }\n\n    /** The {@link BitStructure} instance to which this bit field\n     * belongs. */\n    this.container = container;\n\n    /** The span of this value in bits. */\n    this.bits = bits;\n\n    /** A mask of {@link BitField#bits|bits} bits isolating value bits\n     * that fit within the field.\n     *\n     * That is, it masks a value that has not yet been shifted into\n     * position within its containing packed integer. */\n    this.valueMask = (1 << bits) - 1;\n    if (32 === bits) { // shifted value out of range\n      this.valueMask = 0xFFFFFFFF;\n    }\n\n    /** The offset of the value within the containing packed unsigned\n     * integer.  The least significant bit of the packed value is at\n     * offset zero, regardless of bit ordering used. */\n    this.start = usedBits;\n    if (this.container.msb) {\n      this.start = totalBits - usedBits - bits;\n    }\n\n    /** A mask of {@link BitField#bits|bits} isolating the field value\n     * within the containing packed unsigned integer. */\n    this.wordMask = fixBitwiseResult(this.valueMask << this.start);\n\n    /** The property name used when this bitfield is represented in an\n     * Object.\n     *\n     * Intended to be functionally equivalent to {@link\n     * Layout#property}.\n     *\n     * If left undefined the corresponding span of bits will be\n     * treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field. */\n  decode() {\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(word & this.wordMask);\n    const value = wordValue >>> this.start;\n    return value;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field.\n   *\n   * **NOTE** This is not a specialization of {@link\n   * Layout#encode|Layout.encode} and there is no return value. */\n  encode(value) {\n    if ((!Number.isInteger(value))\n        || (value !== fixBitwiseResult(value & this.valueMask))) {\n      throw new TypeError(nameWithProperty('BitField.encode', this)\n                          + ' value must be integer not exceeding ' + this.valueMask);\n    }\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(value << this.start);\n    this.container._packedSetValue(fixBitwiseResult(word & ~this.wordMask)\n                                   | wordValue);\n  };\n}\n\n/**\n * Represent a single bit within a {@link BitStructure} as a\n * JavaScript boolean.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addBoolean|addBoolean} helper\n * method.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {BitField}\n */\n/* eslint-disable no-extend-native */\nclass Boolean extends BitField {\n  constructor(container, property) {\n    super(container, 1, property);\n  }\n\n  /** Override {@link BitField#decode|decode} for {@link Boolean|Boolean}.\n   *\n   * @returns {boolean} */\n  decode(b, offset) {\n    return !!BitField.prototype.decode.call(this, b, offset);\n  }\n\n  /** @override */\n  encode(value) {\n    if ('boolean' === typeof value) {\n      // BitField requires integer values\n      value = +value;\n    }\n    return BitField.prototype.encode.call(this, value);\n  }\n}\n/* eslint-enable no-extend-native */\n\n/**\n * Contain a fixed-length block of arbitrary data, represented as a\n * Buffer.\n *\n * *Factory*: {@link module:Layout.blob|blob}\n *\n * @param {(Number|ExternalLayout)} length - initializes {@link\n * Blob#length|length}.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Blob extends Layout {\n  constructor(length, property) {\n    if (!(((length instanceof ExternalLayout) && length.isCount())\n          || (Number.isInteger(length) && (0 <= length)))) {\n      throw new TypeError('length must be positive integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n\n    let span = -1;\n    if (!(length instanceof ExternalLayout)) {\n      span = length;\n    }\n    super(span, property);\n\n    /** The number of bytes in the blob.\n     *\n     * This may be a non-negative integer, or an instance of {@link\n     * ExternalLayout} that satisfies {@link\n     * ExternalLayout#isCount|isCount()}. */\n    this.length = length;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return b.slice(offset, offset + span);\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Blob}.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    let span = this.length;\n    if (this.length instanceof ExternalLayout) {\n      span = src.length;\n    }\n    if (!(Buffer.isBuffer(src)\n          && (span === src.length))) {\n      throw new TypeError(nameWithProperty('Blob.encode', this)\n                          + ' requires (length ' + span + ') Buffer as src');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    b.write(src.toString('hex'), offset, span, 'hex');\n    if (this.length instanceof ExternalLayout) {\n      this.length.encode(span, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Contain a `NUL`-terminated UTF8 string.\n *\n * *Factory*: {@link module:Layout.cstr|cstr}\n *\n * **NOTE** Any UTF8 string that incorporates a zero-valued byte will\n * not be correctly decoded by this layout.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass CString extends Layout {\n  constructor(property) {\n    super(-1, property);\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let idx = offset;\n    while ((idx < b.length) && (0 !== b[idx])) {\n      idx += 1;\n    }\n    return 1 + idx - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    return b.slice(offset, offset + span - 1).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    b[offset + span] = 0;\n    return span + 1;\n  }\n}\n\n/**\n * Contain a UTF8 string with implicit length.\n *\n * *Factory*: {@link module:Layout.utf8|utf8}\n *\n * **NOTE** Because the length is implicit in the size of the buffer\n * this layout should be used only in isolation, or in a situation\n * where the length can be expressed by operating on a slice of the\n * containing buffer.\n *\n * @param {Number} [maxSpan] - the maximum length allowed for encoded\n * string content.  If not provided there is no bound on the allowed\n * content.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UTF8 extends Layout {\n  constructor(maxSpan, property) {\n    if (('string' === typeof maxSpan)\n        && (undefined === property)) {\n      property = maxSpan;\n      maxSpan = undefined;\n    }\n    if (undefined === maxSpan) {\n      maxSpan = -1;\n    } else if (!Number.isInteger(maxSpan)) {\n      throw new TypeError('maxSpan must be an integer');\n    }\n\n    super(-1, property);\n\n    /** The maximum span of the layout in bytes.\n     *\n     * Positive values are generally expected.  Zero is abnormal.\n     * Attempts to encode or decode a value that exceeds this length\n     * will throw a `RangeError`.\n     *\n     * A negative value indicates that there is no bound on the length\n     * of the content. */\n    this.maxSpan = maxSpan;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.length - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    return b.slice(offset, offset + span).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    return span;\n  }\n}\n\n/**\n * Contain a constant value.\n *\n * This layout may be used in cases where a JavaScript value can be\n * inferred without an expression in the binary encoding.  An example\n * would be a {@link VariantLayout|variant layout} where the content\n * is implied by the union {@link Union#discriminator|discriminator}.\n *\n * @param {Object|Number|String} value - initializer for {@link\n * Constant#value|value}.  If the value is an object (or array) and\n * the application intends the object to remain unchanged regardless\n * of what is done to values decoded by this layout, the value should\n * be frozen prior passing it to this constructor.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Constant extends Layout {\n  constructor(value, property) {\n    super(0, property);\n\n    /** The value produced by this constant when the layout is {@link\n     * Constant#decode|decoded}.\n     *\n     * Any JavaScript value including `null` and `undefined` is\n     * permitted.\n     *\n     * **WARNING** If `value` passed in the constructor was not\n     * frozen, it is possible for users of decoded values to change\n     * the content of the value. */\n    this.value = value;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    return this.value;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    /* Constants take no space */\n    return 0;\n  }\n}\n\nexports.ExternalLayout = ExternalLayout;\nexports.GreedyCount = GreedyCount;\nexports.OffsetLayout = OffsetLayout;\nexports.UInt = UInt;\nexports.UIntBE = UIntBE;\nexports.Int = Int;\nexports.IntBE = IntBE;\nexports.Float = Float;\nexports.FloatBE = FloatBE;\nexports.Double = Double;\nexports.DoubleBE = DoubleBE;\nexports.Sequence = Sequence;\nexports.Structure = Structure;\nexports.UnionDiscriminator = UnionDiscriminator;\nexports.UnionLayoutDiscriminator = UnionLayoutDiscriminator;\nexports.Union = Union;\nexports.VariantLayout = VariantLayout;\nexports.BitStructure = BitStructure;\nexports.BitField = BitField;\nexports.Boolean = Boolean;\nexports.Blob = Blob;\nexports.CString = CString;\nexports.UTF8 = UTF8;\nexports.Constant = Constant;\n\n/** Factory for {@link GreedyCount}. */\nexports.greedy = ((elementSpan, property) => new GreedyCount(elementSpan, property));\n\n/** Factory for {@link OffsetLayout}. */\nexports.offset = ((layout, offset, property) => new OffsetLayout(layout, offset, property));\n\n/** Factory for {@link UInt|unsigned int layouts} spanning one\n * byte. */\nexports.u8 = (property => new UInt(1, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16 = (property => new UInt(2, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24 = (property => new UInt(3, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32 = (property => new UInt(4, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40 = (property => new UInt(5, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48 = (property => new UInt(6, property));\n\n/** Factory for {@link NearUInt64|little-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64 = (property => new NearUInt64(property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16be = (property => new UIntBE(2, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24be = (property => new UIntBE(3, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32be = (property => new UIntBE(4, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40be = (property => new UIntBE(5, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48be = (property => new UIntBE(6, property));\n\n/** Factory for {@link NearUInt64BE|big-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64be = (property => new NearUInt64BE(property));\n\n/** Factory for {@link Int|signed int layouts} spanning one\n * byte. */\nexports.s8 = (property => new Int(1, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning two bytes. */\nexports.s16 = (property => new Int(2, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning three bytes. */\nexports.s24 = (property => new Int(3, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning four bytes. */\nexports.s32 = (property => new Int(4, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning five bytes. */\nexports.s40 = (property => new Int(5, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning six bytes. */\nexports.s48 = (property => new Int(6, property));\n\n/** Factory for {@link NearInt64|little-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64 = (property => new NearInt64(property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning two bytes. */\nexports.s16be = (property => new IntBE(2, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning three bytes. */\nexports.s24be = (property => new IntBE(3, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning four bytes. */\nexports.s32be = (property => new IntBE(4, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning five bytes. */\nexports.s40be = (property => new IntBE(5, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning six bytes. */\nexports.s48be = (property => new IntBE(6, property));\n\n/** Factory for {@link NearInt64BE|big-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64be = (property => new NearInt64BE(property));\n\n/** Factory for {@link Float|little-endian 32-bit floating point} values. */\nexports.f32 = (property => new Float(property));\n\n/** Factory for {@link FloatBE|big-endian 32-bit floating point} values. */\nexports.f32be = (property => new FloatBE(property));\n\n/** Factory for {@link Double|little-endian 64-bit floating point} values. */\nexports.f64 = (property => new Double(property));\n\n/** Factory for {@link DoubleBE|big-endian 64-bit floating point} values. */\nexports.f64be = (property => new DoubleBE(property));\n\n/** Factory for {@link Structure} values. */\nexports.struct = ((fields, property, decodePrefixes) => new Structure(fields, property, decodePrefixes));\n\n/** Factory for {@link BitStructure} values. */\nexports.bits = ((word, msb, property) => new BitStructure(word, msb, property));\n\n/** Factory for {@link Sequence} values. */\nexports.seq = ((elementLayout, count, property) => new Sequence(elementLayout, count, property));\n\n/** Factory for {@link Union} values. */\nexports.union = ((discr, defaultLayout, property) => new Union(discr, defaultLayout, property));\n\n/** Factory for {@link UnionLayoutDiscriminator} values. */\nexports.unionLayoutDiscriminator = ((layout, property) => new UnionLayoutDiscriminator(layout, property));\n\n/** Factory for {@link Blob} values. */\nexports.blob = ((length, property) => new Blob(length, property));\n\n/** Factory for {@link CString} values. */\nexports.cstr = (property => new CString(property));\n\n/** Factory for {@link UTF8} values. */\nexports.utf8 = ((maxSpan, property) => new UTF8(maxSpan, property));\n\n/** Factory for {@link Constant} values. */\nexports[\"const\"] = ((value, property) => new Constant(value, property));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-layout/lib/Layout.js\n");

/***/ })

};
;