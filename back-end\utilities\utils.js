/** @format */

const { PublicKey, Keypair } = require('@solana/web3.js')
const bs58 = require('bs58');


exports.validateSolAddress = (address) => {
  try {
    let pubkey = new PublicKey(address)
    let isSolana = PublicKey.isOnCurve(pubkey.toBuffer())
    return isSolana
  } catch (error) {
    return false
  }
}




/**
 * logs errors into the error file
 * @param {number} [length=10] - the length of the random string
 * @returns {result} - the random string generated
 */
exports.generateRefLink = (length = 10) => {
  var result = "";
  var characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

exports.isValidPublicKey = (key) => {
  try {
    new PublicKey(key);
    return true;
  } catch {
    return false;
  }
};


exports.createSolanaAddress = () => {
  // Generate a new keypair  
  const keypair = Keypair.generate();

  // Extract the public key and secret key  
  const publicKey = keypair.publicKey.toBase58();
  const secretKey = keypair.secretKey;

  return {
    publicKey,
    secretKey: bs58.default.encode(secretKey) // Store securely!  
  };
}




// ===== Validation Helpers =====
async function validateConnection(connection) {
  try {
    const version = await connection.getVersion();
    if (!version || !version["solana-core"]) {
      throw new Error("Invalid Solana RPC version response");
    }
    return true;
  } catch (error) {
    console.error("Solana connection validation failed:", error.message || error);
    return false;
  }
}

// Example HTTP request validator (customize as needed)
function validateHttpRequest(req, requiredFields = []) {
  if (!req || typeof req !== "object") {
    throw new Error("Invalid request object");
  }
  for (const field of requiredFields) {
    if (!(field in req)) {
      throw new Error(`Missing required field: ${field}`);
    }
    if (req[field] === undefined || req[field] === null || req[field] === "") {
      throw new Error(`Field ${field} cannot be empty`);
    }
  }
  return true;
}

