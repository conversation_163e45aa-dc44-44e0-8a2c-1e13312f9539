import { motion } from 'framer-motion';
import { Bell } from 'lucide-react';
import React from 'react';
import dynamic from 'next/dynamic';
import { useGlobalModal } from '@/contexts/GlobalModalContext';

const NotificationModal = dynamic(() => import('./Notifications').then(mod => ({ default: mod.default })), {
  loading: () => <div/>,
});

const ChatModal = dynamic(() => import('@/components/shared/chat/ChatModal'), {
  loading: () => <div />,
});

interface NotificationButtonProps {
  notifications: number;
}

const NotificationButton: React.FC<NotificationButtonProps> = ({ notifications }) => {
  const { openModal, closeModal } = useGlobalModal();

  const handleOpenModal = () => {
    openModal({
      id: "notification",
      component: React.createElement(NotificationModal, {
        onClose: () => closeModal("notification"),
        onOpenChat: async (chatRoomId: string, receiverId: number) => {
          // Get current user id from localStorage
          const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
          const userBo = userBoStr ? JSON.parse(userBoStr) : null;
          const myUserId = userBo?.id;

          console.log('🔍 [NotificationButton] Opening chat with:', {
            chatRoomId,
            receiverId,
            myUserId
          });

          // Use consistent modal ID to prevent multiple modals
          const modalId = `chat-modal-${chatRoomId}`;

          // Close existing modal if it exists
          closeModal(modalId);

          // For now, we'll use basic implementation since we don't have notification data
          // In the future, this should be unified with NotificationBell logic
          openModal({
            id: modalId,
            component: React.createElement(ChatModal, {
              chatRoomId,
              buyerId: myUserId,
              sellerId: receiverId,
              onClose: () => closeModal(modalId),
              onRelease: () => {},
              onRefund: () => {},
              onReport: () => {},
              activeTrade: undefined // No trade data available from header notifications
            }),
            closeOnBackdropClick: true,
            closeOnEscape: true,
            preventClose: false,
            zIndex: 10000,
            backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
            modalClassName: 'flex flex-col items-start justify-end p-4',
            disableScroll: true,
          });
        }
      }),
      closeOnBackdropClick: true,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
      modalClassName: 'flex flex-col items-start justify-end p-4',
      disableScroll: true,
    });
  };  

  return (
    <>
      <motion.button
        className="relative bg-[#F5F5F5] p-1 lg:p-2 h-[40px] w-[40px] lg:h-[53px] lg:w-[53px] flex items-center justify-center rounded-full transition-all duration-300 hover:bg-gray-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => handleOpenModal()}
      >
        <Bell size={24} className="lg:w-8 lg:h-8 text-gray-700" />
        {notifications > 0 && (
          <motion.span
            className="absolute top-[6px] right-[7px] lg:top-[12px] lg:right-[15px] bg-red-500 text-white rounded-full w-[10px] h-[10px] flex items-center justify-center text-xs"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 500, damping: 15 }}
          />
        )}
      </motion.button>
    </>
  );
};

export default NotificationButton; 