"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_auth_create-coin-form_SubmitButton_tsx";
exports.ids = ["_ssr_src_components_auth_create-coin-form_SubmitButton_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/auth/create-coin-form/SubmitButton.tsx":
/*!***************************************************************!*\
  !*** ./src/components/auth/create-coin-form/SubmitButton.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/components/auth/create-coin-form/constants.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SubmitButton = ({ isSubmitting })=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n        type: \"submit\",\n        disabled: isSubmitting,\n        className: `w-full h-[52px] px-5 py-2 bg-[#FF6600] rounded-sm inline-flex justify-center items-center gap-2.5 mt-3 mb-0 mx-auto transition-all duration-200 ${isSubmitting ? \"opacity-70 cursor-not-allowed\" : \"hover:bg-[#e55a00]\"}`,\n        variants: _constants__WEBPACK_IMPORTED_MODULE_2__.formFieldVariants,\n        whileHover: !isSubmitting ? {\n            scale: 1.01\n        } : {},\n        whileTap: !isSubmitting ? {\n            scale: 0.99\n        } : {},\n        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    animate: {\n                        rotate: 360\n                    },\n                    transition: {\n                        duration: 1,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\SubmitButton.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\SubmitButton.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, undefined),\n                t('createCoinForm.processing')\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n            children: t('createCoinForm.createMyCoin')\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\SubmitButton.tsx\",\n            lineNumber: 43,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\SubmitButton.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubmitButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/create-coin-form/SubmitButton.tsx\n");

/***/ })

};
;