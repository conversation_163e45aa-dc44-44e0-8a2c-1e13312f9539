"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000, wallet = arguments.length > 4 ? arguments[4] : void 0, originalTransaction = arguments.length > 5 ? arguments[5] : void 0;\n    let lastError;\n    let currentSignedTx = signedTransaction;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(currentSignedTx.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Handle blockhash not found error by refreshing blockhash and re-signing\n            if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Blockhash not found')) && wallet && originalTransaction && attempt < maxRetries) {\n                console.log(\"\\uD83D\\uDD04 Blockhash expired, getting fresh blockhash and re-signing...\");\n                try {\n                    // Get fresh blockhash\n                    const { blockhash } = await connection.getLatestBlockhash('finalized');\n                    originalTransaction.recentBlockhash = blockhash;\n                    // Re-sign the transaction with fresh blockhash\n                    currentSignedTx = await wallet.signTransaction(originalTransaction);\n                    console.log(\"✅ Transaction re-signed with fresh blockhash\");\n                    continue;\n                } catch (resignError) {\n                    console.error(\"❌ Failed to re-sign transaction:\", resignError);\n                // Fall through to normal retry logic\n                }\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(escrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(escrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_tokenDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1, _tradeData_tokenDetails1, _tradeData_perkDetails_token1, _tradeData_perkDetails2;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price,\n                    perkDetails: tradeData.perkDetails,\n                    perkTokenMint: tradeData.perkTokenMint\n                });\n                // Use trade data to get the numeric escrowId\n                // Priority: 1. tradeData.escrowId, 2. extra field, 3. original escrowId\n                let numericEscrowId = null;\n                // First try the direct escrowId field from trade data\n                if (tradeData.escrowId) {\n                    // Validate that it's numeric before using it\n                    const escrowIdStr = String(tradeData.escrowId);\n                    if (/^\\d+$/.test(escrowIdStr)) {\n                        numericEscrowId = tradeData.escrowId;\n                    } else {\n                        console.warn('⚠️ Trade escrowId is not numeric:', tradeData.escrowId);\n                    }\n                } else {\n                    // Fall back to extra field\n                    try {\n                        if (tradeData.extra) {\n                            const extraData = JSON.parse(tradeData.extra);\n                            if (extraData.escrowId) {\n                                const escrowIdStr = String(extraData.escrowId);\n                                if (/^\\d+$/.test(escrowIdStr)) {\n                                    numericEscrowId = extraData.escrowId;\n                                } else {\n                                    console.warn('⚠️ Extra escrowId is not numeric:', extraData.escrowId);\n                                }\n                            }\n                        }\n                    } catch (e) {\n                        console.log('Could not parse extra data:', e);\n                    }\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                console.log('🔍 [EscrowAccept] EscrowId resolution:', {\n                    originalEscrowId: escrowId,\n                    tradeDataEscrowId: tradeData.escrowId,\n                    numericEscrowId,\n                    finalCalculatedEscrowId: calculatedEscrowId\n                });\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                // Try multiple sources for perk token mint\n                calculatedPerkMint = ((_tradeData_tokenDetails = tradeData.tokenDetails) === null || _tradeData_tokenDetails === void 0 ? void 0 : _tradeData_tokenDetails.tokenAddress) || ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || tradeData.perkTokenMint || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Perk token mint resolution:', {\n                    'tokenDetails.tokenAddress': (_tradeData_tokenDetails1 = tradeData.tokenDetails) === null || _tradeData_tokenDetails1 === void 0 ? void 0 : _tradeData_tokenDetails1.tokenAddress,\n                    'perkDetails.token.tokenAddress': (_tradeData_perkDetails2 = tradeData.perkDetails) === null || _tradeData_perkDetails2 === void 0 ? void 0 : (_tradeData_perkDetails_token1 = _tradeData_perkDetails2.token) === null || _tradeData_perkDetails_token1 === void 0 ? void 0 : _tradeData_perkDetails_token1.tokenAddress,\n                    'tradeData.perkTokenMint': tradeData.perkTokenMint,\n                    'original perkTokenMint': perkTokenMint,\n                    'final calculatedPerkMint': calculatedPerkMint\n                });\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required but not found. Please ensure the trade has a valid escrowId. TradeId: \".concat(tradeId), \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        if (!calculatedPerkMint) {\n            throw new EscrowError(\"Perk token mint address is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            perkTokenAta: sellerPerkTokenAta,\n            purchase: purchasePda,\n            vault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey; // Buyer pays the fees and is the signer\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});