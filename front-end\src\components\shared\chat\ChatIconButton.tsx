import React from "react";
import { MessageCircle } from "lucide-react";

interface ChatIconButtonProps {
  unreadCount: number;
  onClick: () => void;
}

const ChatIconButton: React.FC<ChatIconButtonProps> = ({ unreadCount, onClick }) => (
  <button
    onClick={onClick}
    className="fixed left-8 bottom-8 z-50 bg-orange-500 hover:bg-orange-600 rounded-full w-16 h-16 flex items-center justify-center shadow-lg transition-colors duration-200"
    aria-label="Open chat"
  >
    <MessageCircle className="w-8 h-8 text-white" />
    {unreadCount > 0 && (
      <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
        {unreadCount}
      </span>
    )}
  </button>
);

export default ChatIconButton; 