const jwt = require("jsonwebtoken");
const dataContext = require("../db");
const config = require("../config/security");

const authenticate = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const JWT_SECRET = config.JWT_SECRET;

  if (!JWT_SECRET) {
    return res.status(500).json({ status: 500, message: "Server error: JWT secret not configured." });
  }

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ status: 401, message: "No token provided. Please log in." });
  }

  const token = authHeader.split(" ")[1];

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    req.user = decoded; // contains: id, privyId, privywallet (from loginUserPrivy)

    next();
  } catch (err) {
    return res.status(401).json({ status: 401, message: "Invalid or expired token. Please log in." });
  }
};


// Middleware to check if 2FA is required
const require2FA = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const JWT_SECRET = config.JWT_SECRET;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(200).json({ status: 401, message: "No token provided, Please login" });
  }
  
  const token = authHeader.split(" ")[1];
  const { userId } = req.body;

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    if (decoded.id != userId) {
      return res
        .status(200)
        .json({ status: 401, message: "Invalid or expired token, Please login" });
    }

    // Check if user has 2FA enabled
    const user = await dataContext.User.findOne({ where: { id: decoded.id } });
    
    if (!user) {
      return res.status(404).json({ status: 404, message: "User not found" });
    }

    // If 2FA is enabled but not verified in this session
    if (user.twoFactorStatus && !decoded.twoFactorVerified) {
      return res.status(200).json({ 
        status: 403, 
        message: "Two-factor authentication required",
        requireTwoFactor: true
      });
    }
    
    req.user = decoded;
    next();
  } catch (err) {
    return res
      .status(200)
      .json({ status: 401, message: "Invalid or expired token, Please login" });
  }
};

module.exports = {
  authenticate,
  require2FA
};
