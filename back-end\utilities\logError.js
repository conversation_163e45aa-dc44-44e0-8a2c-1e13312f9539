/** @format */

const date = require("date-and-time");
/**
 * logs errors into the error file
 *
 * @format
 * @param {error} object - the error object
 * @param {location} string - a string representation of where the error occured
 * @param {other} [string=""] - other information you might want to add
 * @param {errorFile} [string=config.errorFile.default] - the file name to store the error in
 * @param {errorReference} [string=""] - also a reference to where the error occurred
 */

const logError = ({
  error,
  location,
  others = "",
  errorFile = config.errorFile.default,
  errorReference = "",
}) => {
  return new Promise(async (resolve, reject) => {
    try {
      let fs = require("fs");
      let logStream = fs.createWriteStream(errorFile, {
        flags: "a",
      });
      logStream.write(
        "Time: " +
          date.format(new Date(Date.now()), "YYYY/MM/DD HH:mm:ss") +
          "\n"
      );
      logStream.write("Error: " + (error.error || error) + "\n");
      logStream.write("Location: " + (error.location || location) + "\n");
      logStream.write("Others: " + (error.others || others) + "\n");
      logStream.write(
        "Error reference: " + (error.errorReference || errorReference) + "\n"
      );
      logStream.write("\n");
      logStream.end("\n");
      logStream.on("finish", resolve);
    } catch (error) {
      console.log(error, "Error ==> utilities/utils - logError");
      reject();
    }
  });
};

module.exports = { logError };
