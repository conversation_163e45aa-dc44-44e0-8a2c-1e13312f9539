# FunHi Frontend Application

A modern React application for creating, buying, and selling digital assets.

## Features

- Browse coins with filtering by category, search, and verification status
- View detailed coin information 
- Buy coins
- Live notification ticker
- Responsive design for all devices

## Project Structure

The codebase is organized with a clear separation of concerns:

- `src/components/`: React components organized by type
  - `shared/`: Common components like Header, Card, and Filters
  - `ui/`: Basic UI elements like RunningLine
- `src/hooks/`: Custom React hooks for reusable logic
  - `useCoins.ts`: Hook for fetching and managing coins data
  - `useFilters.ts`: Hook for managing filter state
- `src/contexts/`: React contexts for global state management
  - `AppContext.tsx`: Global application state
- `src/lib/`: Utility functions and API handlers
  - `api.ts`: API service with mockup functions (replace with real API later)
- `src/constants/`: Application constants and configuration

## Getting Started

### Prerequisites

- Node.js 14+ and npm/yarn

### Installation

1. Clone the repository
2. Install dependencies:
```
npm install
# or
yarn install
```

3. Start the development server:
```
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Development Guidelines

### Adding New Components

1. Create a new folder under `src/components/` with an appropriate category
2. Add an `index.tsx` file with your component
3. Ensure you're using TypeScript interfaces for props
4. Add appropriate documentation

### Adding New API Endpoints

1. Add the endpoint to `src/constants/index.ts` in the `API_ENDPOINTS` object
2. Create a new function in `src/lib/api.ts` to handle the request
3. Update or create hooks as needed

### Styling

- This project uses Tailwind CSS for styling
- Follow the existing styling patterns
- Use the defined color scheme and spacing

## Future Improvements

- Implement real API integration
- Add authentication system
- Create user dashboard
- Add wallet integration for real transactions
- Add more detailed analytics and charts

## License

This project is licensed under the MIT License - see the LICENSE file for details.
