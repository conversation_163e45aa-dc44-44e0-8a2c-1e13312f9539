"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_auth_create-coin-form_ImageUpload_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/image.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Image)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1m3agn\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"9\",\n            r: \"2\",\n            key: \"af1f0g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n            key: \"1xmnt7\"\n        }\n    ]\n];\nconst Image = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"image\", __iconNode);\n //# sourceMappingURL=image.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/upload.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Upload)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"17 8 12 3 7 8\",\n            key: \"t8dd8p\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"3\",\n            y2: \"15\",\n            key: \"widbto\"\n        }\n    ]\n];\nconst Upload = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"upload\", __iconNode);\n //# sourceMappingURL=upload.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/create-coin-form/ImageUpload.tsx":
/*!**************************************************************!*\
  !*** ./src/components/auth/create-coin-form/ImageUpload.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(app-pages-browser)/./src/components/auth/create-coin-form/constants.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ImageUpload = (param)=>{\n    let { picture, previewUrl, error, isSubmitting, onFileChange, onRemoveImage } = param;\n    _s();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const inputClass = \"self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200\";\n    const inputErrorClass = \"self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"\".concat(error ? inputErrorClass : inputClass, \" relative\"),\n                variants: _constants__WEBPACK_IMPORTED_MODULE_2__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center gap-3\",\n                        children: previewUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: previewUrl,\n                                    alt: \"Preview\",\n                                    width: 40,\n                                    height: 40,\n                                    className: \"w-10 h-10 rounded object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700 flex-1 truncate\",\n                                    children: picture === null || picture === void 0 ? void 0 : picture.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    type: \"button\",\n                                    onClick: onRemoveImage,\n                                    className: \"text-red-500 hover:text-red-700 p-1\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 text-gray-400 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('createCoinForm.uploadPicture')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    !previewUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.label, {\n                        className: \"px-4 py-2 bg-[#F58A38] text-white rounded cursor-pointer hover:bg-[#e55a00] transition-colors flex items-center gap-2\",\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined),\n                            t('createCoinForm.upload'),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                className: \"hidden\",\n                                onChange: onFileChange,\n                                accept: \"image/*\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\ImageUpload.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ImageUpload, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = ImageUpload;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUpload);\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/create-coin-form/ImageUpload.tsx\n"));

/***/ })

}]);