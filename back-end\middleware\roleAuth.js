const dataContext = require("../model");

/**
 * Middleware to check if user is a moderator
 */
const requireModerator = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Check if user is an active moderator
    const moderator = await dataContext.Moderator.findOne({
      where: { userId, isActive: true }
    });

    if (!moderator) {
      return res.status(403).json({
        status: 403,
        message: 'Access denied. Moderator privileges required.'
      });
    }

    // Add moderator info to request
    req.moderator = moderator;
    next();
  } catch (error) {
    console.error('Moderator check error:', error);
    res.status(500).json({
      status: 500,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user is an admin
 * For now, we'll use a simple check - in production, you'd have a proper admin role system
 */
const requireAdmin = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // TODO: Implement proper admin role checking
    // For now, we'll check if user is in a list of admin IDs or has admin flag
    const user = await dataContext.User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({
        status: 404,
        message: 'User not found'
      });
    }

    // Check if user has admin privileges
    // This is a placeholder - implement your admin logic here
    const isAdmin = user.email && (
      user.email.includes('admin@') || 
      user.email === '<EMAIL>' ||
      // Add other admin identification logic
      false
    );

    if (!isAdmin) {
      return res.status(403).json({
        status: 403,
        message: 'Access denied. Administrator privileges required.'
      });
    }

    req.isAdmin = true;
    next();
  } catch (error) {
    console.error('Admin check error:', error);
    res.status(500).json({
      status: 500,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user can access a specific trade
 * User can access if they are buyer, seller, or moderator
 */
const requireTradeAccess = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const tradeId = req.params.tradeId || req.body.tradeId;

    if (!tradeId) {
      return res.status(400).json({
        status: 400,
        message: 'Trade ID is required'
      });
    }

    // Get the trade with related data
    const trade = await dataContext.TokenPurchased.findByPk(tradeId, {
      include: [
        { model: dataContext.User, as: 'user' },
        { 
          model: dataContext.Perk, 
          as: 'perkDetails',
          include: [{ model: dataContext.User, as: 'user' }]
        }
      ]
    });

    if (!trade) {
      return res.status(404).json({
        status: 404,
        message: 'Trade not found'
      });
    }

    const buyerId = trade.userId;
    const sellerId = trade.perkDetails.userId;

    // Check if user is buyer or seller
    if (userId === buyerId || userId === sellerId) {
      req.trade = trade;
      req.userRole = userId === buyerId ? 'buyer' : 'seller';
      return next();
    }

    // Check if user is a moderator
    const moderator = await dataContext.Moderator.findOne({
      where: { userId, isActive: true }
    });

    if (moderator) {
      req.trade = trade;
      req.userRole = 'moderator';
      req.moderator = moderator;
      return next();
    }

    // User has no access to this trade
    return res.status(403).json({
      status: 403,
      message: 'Access denied. You do not have permission to access this trade.'
    });

  } catch (error) {
    console.error('Trade access check error:', error);
    res.status(500).json({
      status: 500,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user can initiate dispute for a trade
 * Only buyer or seller can initiate disputes
 */
const requireDisputeInitiationAccess = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { tradeId, initiatorRole } = req.body;

    if (!tradeId || !initiatorRole) {
      return res.status(400).json({
        status: 400,
        message: 'Trade ID and initiator role are required'
      });
    }

    // Get the trade
    const trade = await dataContext.TokenPurchased.findByPk(tradeId, {
      include: [
        { model: dataContext.User, as: 'user' },
        { 
          model: dataContext.Perk, 
          as: 'perkDetails',
          include: [{ model: dataContext.User, as: 'user' }]
        }
      ]
    });

    if (!trade) {
      return res.status(404).json({
        status: 404,
        message: 'Trade not found'
      });
    }

    const buyerId = trade.userId;
    const sellerId = trade.perkDetails.userId;

    // Verify user role matches their actual position in the trade
    if (initiatorRole === 'buyer' && userId !== buyerId) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized as the buyer for this trade'
      });
    }

    if (initiatorRole === 'seller' && userId !== sellerId) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized as the seller for this trade'
      });
    }

    req.trade = trade;
    req.userRole = initiatorRole;
    next();

  } catch (error) {
    console.error('Dispute initiation access check error:', error);
    res.status(500).json({
      status: 500,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user can perform refund
 * Only moderators should be able to initiate refunds
 */
const requireRefundAccess = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Check if user is an active moderator
    const moderator = await dataContext.Moderator.findOne({
      where: { userId, isActive: true }
    });

    if (!moderator) {
      return res.status(403).json({
        status: 403,
        message: 'Access denied. Only moderators can initiate refunds.'
      });
    }

    req.moderator = moderator;
    next();

  } catch (error) {
    console.error('Refund access check error:', error);
    res.status(500).json({
      status: 500,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  requireModerator,
  requireAdmin,
  requireTradeAccess,
  requireDisputeInitiationAccess,
  requireRefundAccess
};
