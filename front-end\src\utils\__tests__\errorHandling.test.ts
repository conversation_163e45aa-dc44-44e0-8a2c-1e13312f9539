import {
  AppError,
  ValidationError,
  AuthenticationError,
  NetworkError,
  WalletError,
  FormError,
  APIError,
  TOAST_MESSAGES,
  showErrorToast,
  showSuccessToast,
  showWarningToast,
  showInfoToast,
  createAuthError,
  createWalletError,
  createFormError,
  createAPIError,
  createNetworkError,
  handleAPIResponse,
  handleFormValidation,
} from '../errorHandling'

// Mock react-hot-toast
jest.mock('react-hot-toast', () => {
  const mockToast = jest.fn();
  mockToast.error = jest.fn();
  mockToast.success = jest.fn();
  mockToast.loading = jest.fn();
  mockToast.dismiss = jest.fn();
  
  return {
    toast: mockToast,
    __esModule: true,
  };
})

// Mock environment config
jest.mock('@/config/environment', () => ({
  isProduction: jest.fn(() => false),
}))

import { toast } from 'react-hot-toast'

describe('Error Handling System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    console.error = jest.fn()
    console.log = jest.fn()
  })

  describe('AppError', () => {
    it('should create AppError with default values', () => {
      const error = new AppError('Test message', 'TEST_CODE')
      
      expect(error.message).toBe('Test message')
      expect(error.code).toBe('TEST_CODE')
      expect(error.status).toBe(500)
      expect(error.retryable).toBe(true)
      expect(error.userFriendly).toBe(true)
      expect(error.name).toBe('AppError')
      expect(error.timestamp).toBeInstanceOf(Date)
    })

    it('should create AppError with custom values', () => {
      const error = new AppError('Custom message', 'CUSTOM_CODE', 400, false, false)
      
      expect(error.status).toBe(400)
      expect(error.retryable).toBe(false)
      expect(error.userFriendly).toBe(false)
    })

    it('should serialize to JSON correctly', () => {
      const error = new AppError('Test message', 'TEST_CODE', 404)
      const json = error.toJSON()
      
      expect(json).toEqual({
        message: 'Test message',
        code: 'TEST_CODE',
        statusCode: 404,
        timestamp: error.timestamp,
        retryable: true,
        userFriendly: true,
      })
    })
  })

  describe('ValidationError', () => {
    it('should create ValidationError with default values', () => {
      const error = new ValidationError('Invalid input')
      
      expect(error.message).toBe('Invalid input')
      expect(error.code).toBe('VALIDATION_ERROR')
      expect(error.status).toBe(400)
      expect(error.retryable).toBe(false)
      expect(error.userFriendly).toBe(true)
      expect(error.name).toBe('ValidationError')
      expect(error.field).toBeUndefined()
    })

    it('should create ValidationError with field', () => {
      const error = new ValidationError('Email is required', 'email')
      
      expect(error.field).toBe('email')
    })
  })

  describe('AuthenticationError', () => {
    it('should create AuthenticationError with default message', () => {
      const error = new AuthenticationError()
      
      expect(error.message).toBe('Authentication failed')
      expect(error.code).toBe('AUTH_ERROR')
      expect(error.status).toBe(401)
      expect(error.retryable).toBe(false)
      expect(error.name).toBe('AuthenticationError')
    })

    it('should create AuthenticationError with custom message', () => {
      const error = new AuthenticationError('Login required')
      
      expect(error.message).toBe('Login required')
    })
  })

  describe('NetworkError', () => {
    it('should create NetworkError with default message', () => {
      const error = new NetworkError()
      
      expect(error.message).toBe('Network error occurred')
      expect(error.code).toBe('NETWORK_ERROR')
      expect(error.status).toBe(0)
      expect(error.retryable).toBe(true)
      expect(error.name).toBe('NetworkError')
    })

    it('should create NetworkError with custom message', () => {
      const error = new NetworkError('Connection timeout')
      
      expect(error.message).toBe('Connection timeout')
    })
  })

  describe('WalletError', () => {
    it('should create WalletError with default message', () => {
      const error = new WalletError()
      
      expect(error.message).toBe('Wallet operation failed')
      expect(error.code).toBe('WALLET_ERROR')
      expect(error.status).toBe(400)
      expect(error.retryable).toBe(true)
      expect(error.name).toBe('WalletError')
    })
  })

  describe('FormError', () => {
    it('should create FormError with field', () => {
      const error = new FormError('Invalid price', 'price')
      
      expect(error.message).toBe('Invalid price')
      expect(error.code).toBe('FORM_ERROR')
      expect(error.status).toBe(400)
      expect(error.field).toBe('price')
      expect(error.name).toBe('FormError')
    })
  })

  describe('APIError', () => {
    it('should create APIError with default status', () => {
      const error = new APIError('Server error')
      
      expect(error.message).toBe('Server error')
      expect(error.code).toBe('API_ERROR')
      expect(error.status).toBe(500)
      expect(error.name).toBe('APIError')
    })

    it('should create APIError with custom status', () => {
      const error = new APIError('Bad request', 400)
      
      expect(error.status).toBe(400)
    })
  })

  describe('TOAST_MESSAGES', () => {
    it('should contain all required message categories', () => {
      expect(TOAST_MESSAGES).toHaveProperty('AUTH')
      expect(TOAST_MESSAGES).toHaveProperty('WALLET')
      expect(TOAST_MESSAGES).toHaveProperty('FORM')
      expect(TOAST_MESSAGES).toHaveProperty('TOKEN')
      expect(TOAST_MESSAGES).toHaveProperty('PERK')
      expect(TOAST_MESSAGES).toHaveProperty('PROFILE')
      expect(TOAST_MESSAGES).toHaveProperty('COMMENT')
      expect(TOAST_MESSAGES).toHaveProperty('AIRDROP')
      expect(TOAST_MESSAGES).toHaveProperty('NETWORK')
      expect(TOAST_MESSAGES).toHaveProperty('DATA')
      expect(TOAST_MESSAGES).toHaveProperty('GENERAL')
    })

    it('should contain specific auth messages', () => {
      expect(TOAST_MESSAGES.AUTH).toHaveProperty('LOGIN_SUCCESS')
      expect(TOAST_MESSAGES.AUTH).toHaveProperty('LOGIN_FAILED')
      expect(TOAST_MESSAGES.AUTH).toHaveProperty('LOGIN_REQUIRED')
      expect(TOAST_MESSAGES.AUTH).toHaveProperty('SESSION_EXPIRED')
      expect(TOAST_MESSAGES.AUTH).toHaveProperty('TWO_FACTOR_SUCCESS')
    })

    it('should contain specific wallet messages', () => {
      expect(TOAST_MESSAGES.WALLET).toHaveProperty('CONNECT_REQUIRED')
      expect(TOAST_MESSAGES.WALLET).toHaveProperty('CONNECTION_FAILED')
      expect(TOAST_MESSAGES.WALLET).toHaveProperty('TRANSACTION_FAILED')
      expect(TOAST_MESSAGES.WALLET).toHaveProperty('INSUFFICIENT_FUNDS')
    })

    it('should have user-friendly messages', () => {
      expect(TOAST_MESSAGES.AUTH.LOGIN_SUCCESS).toBe('Successfully logged in! Welcome back.')
      expect(TOAST_MESSAGES.WALLET.CONNECT_REQUIRED).toBe('Please connect your Solana wallet to continue.')
      expect(TOAST_MESSAGES.FORM.VALIDATION_FAILED).toBe('Please check your input and fix any errors before submitting.')
    })
  })

  describe('showErrorToast', () => {
    it('should show toast for AppError with specific message', () => {
      const error = new AppError('Custom error message', 'CUSTOM_ERROR')
      
      showErrorToast(error)
      
      expect(toast.error).toHaveBeenCalledWith(
        'Custom error message',
        expect.objectContaining({
          duration: 5000,
          position: 'top-right',
        })
      )
    })

    it('should show toast for generic Error', () => {
      const error = new Error('Generic error')
      
      showErrorToast(error)
      
      expect(toast.error).toHaveBeenCalledWith(
        TOAST_MESSAGES.GENERAL.UNEXPECTED_ERROR,
        expect.objectContaining({
          duration: 5000,
          position: 'top-right',
        })
      )
    })

    it('should show toast for unknown error', () => {
      showErrorToast('String error')
      
      expect(toast.error).toHaveBeenCalledWith(
        TOAST_MESSAGES.GENERAL.UNEXPECTED_ERROR,
        expect.objectContaining({
          duration: 5000,
          position: 'top-right',
        })
      )
    })

    it('should use mapped message for error codes', () => {
      const error = new NetworkError()
      
      showErrorToast(error)
      
      expect(toast.error).toHaveBeenCalledWith(
        TOAST_MESSAGES.NETWORK.CONNECTION_FAILED,
        expect.objectContaining({
          duration: 5000,
          position: 'top-right',
        })
      )
    })

    it('should log error to console', () => {
      const error = new Error('Test error')
      
      showErrorToast(error, 'test context')
      
      expect(console.error).toHaveBeenCalledWith(
        'Application Error:',
        expect.objectContaining({
          context: 'test context',
          error: expect.objectContaining({
            message: 'Test error',
            name: 'Error',
          }),
        })
      )
    })
  })

  describe('showSuccessToast', () => {
    it('should show success toast with correct options', () => {
      showSuccessToast('Operation successful')
      
      expect(toast.success).toHaveBeenCalledWith(
        'Operation successful',
        expect.objectContaining({
          duration: 3000,
          position: 'top-right',
        })
      )
    })
  })

  describe('showWarningToast', () => {
    it('should show warning toast with custom styling', () => {
      const mockToast = toast as any
      mockToast.mockImplementation = jest.fn()
      
      showWarningToast('Warning message')
      
      expect(toast).toHaveBeenCalledWith(
        'Warning message',
        expect.objectContaining({
          duration: 4000,
          position: 'top-right',
          style: expect.objectContaining({
            background: '#FEF3C7',
            color: '#92400E',
            border: '1px solid #F59E0B',
          }),
        })
      )
    })
  })

  describe('showInfoToast', () => {
    it('should show info toast with custom styling', () => {
      const mockToast = toast as any
      mockToast.mockImplementation = jest.fn()
      
      showInfoToast('Info message')
      
      expect(toast).toHaveBeenCalledWith(
        'Info message',
        expect.objectContaining({
          duration: 3000,
          position: 'top-right',
          style: expect.objectContaining({
            background: '#DBEAFE',
            color: '#1E40AF',
            border: '1px solid #3B82F6',
          }),
        })
      )
    })
  })

  describe('Error Creation Utilities', () => {
    it('should create AuthenticationError with default message', () => {
      const error = createAuthError()
      
      expect(error).toBeInstanceOf(AuthenticationError)
      expect(error.message).toBe(TOAST_MESSAGES.AUTH.LOGIN_REQUIRED)
    })

    it('should create AuthenticationError with custom message', () => {
      const error = createAuthError('Custom auth error')
      
      expect(error.message).toBe('Custom auth error')
    })

    it('should create WalletError with default message', () => {
      const error = createWalletError()
      
      expect(error).toBeInstanceOf(WalletError)
      expect(error.message).toBe(TOAST_MESSAGES.WALLET.CONNECTION_FAILED)
    })

    it('should create FormError with field', () => {
      const error = createFormError('Invalid input', 'email')
      
      expect(error).toBeInstanceOf(FormError)
      expect(error.field).toBe('email')
    })

    it('should create APIError with status', () => {
      const error = createAPIError('Not found', 404)
      
      expect(error).toBeInstanceOf(APIError)
      expect(error.status).toBe(404)
    })

    it('should create NetworkError', () => {
      const error = createNetworkError('Connection timeout')
      
      expect(error).toBeInstanceOf(NetworkError)
      expect(error.message).toBe('Connection timeout')
    })
  })

  describe('handleAPIResponse', () => {
    it('should return response for successful status codes', () => {
      const response = { status: 200, data: { success: true } }
      
      const result = handleAPIResponse(response, 'Success message')
      
      expect(result).toBe(response)
      expect(toast.success).toHaveBeenCalledWith(
        'Success message',
        expect.objectContaining({
          duration: 3000,
          position: 'top-right',
        })
      )
    })

    it('should not show success toast if no message provided', () => {
      const response = { status: 201, data: { success: true } }
      
      const result = handleAPIResponse(response)
      
      expect(result).toBe(response)
      expect(toast.success).not.toHaveBeenCalled()
    })

    it('should throw APIError for error status codes', () => {
      const response = {
        status: 400,
        data: { message: 'Bad request' },
        message: 'API error',
      }
      
      expect(() => handleAPIResponse(response)).toThrow(APIError)
      expect(() => handleAPIResponse(response)).toThrow('Bad request')
    })

    it('should use fallback error message', () => {
      const response = { status: 500 }
      
      expect(() => handleAPIResponse(response)).toThrow(TOAST_MESSAGES.NETWORK.SERVER_ERROR)
    })
  })

  describe('handleFormValidation', () => {
    it('should not throw for form with no errors', () => {
      const errors = { name: '', email: '', price: '' }
      
      expect(() => handleFormValidation(errors)).not.toThrow()
    })

    it('should throw FormError for form with errors', () => {
      const errors = { name: 'Required', email: '', price: 'Invalid' }
      
      expect(() => handleFormValidation(errors)).toThrow(FormError)
      expect(() => handleFormValidation(errors)).toThrow('Required')
    })

    it('should include field name in FormError', () => {
      const errors = { email: 'Invalid email', price: '' }
      
      try {
        handleFormValidation(errors)
      } catch (error) {
        expect(error).toBeInstanceOf(FormError)
        expect((error as FormError).field).toBe('email')
      }
    })
  })

  describe('Edge Cases', () => {
    it('should handle null and undefined errors', () => {
      showErrorToast(null)
      showErrorToast(undefined)
      
      expect(toast.error).toHaveBeenCalledTimes(2)
      expect(toast.error).toHaveBeenCalledWith(
        TOAST_MESSAGES.GENERAL.UNEXPECTED_ERROR,
        expect.any(Object)
      )
    })

    it('should handle circular reference in error objects', () => {
      const circularError: any = { message: 'Circular error' }
      circularError.self = circularError
      
      expect(() => showErrorToast(circularError)).not.toThrow()
    })

    it('should handle very long error messages', () => {
      const longMessage = 'a'.repeat(10000)
      const error = new AppError(longMessage, 'LONG_ERROR')
      
      showErrorToast(error)
      
      expect(toast.error).toHaveBeenCalledWith(longMessage, expect.any(Object))
    })

    it('should handle errors with special characters', () => {
      const specialMessage = 'Error with special chars: 中文 🚀 @#$%^&*()'
      const error = new AppError(specialMessage, 'SPECIAL_ERROR')
      
      showErrorToast(error)
      
      expect(toast.error).toHaveBeenCalledWith(specialMessage, expect.any(Object))
    })
  })

  describe('Production Mode', () => {
    it('should log to production error service in production', () => {
      const { isProduction } = require('@/config/environment')
      isProduction.mockReturnValue(true)
      
      const error = new Error('Production error')
      
      showErrorToast(error)
      
      expect(console.log).toHaveBeenCalledWith(
        'Production error logging:',
        expect.any(Object)
      )
    })
  })
}) 