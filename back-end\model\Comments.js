const { Model, DataTypes } = require("sequelize");

class Comments extends Model {
    static initModel(sequelize) {
        return Comments.init(
            {
                ID: {
                    primaryKey: true,
                    allowNull: false,
                    autoIncrement: true,
                    type: DataTypes.BIGINT,
                },
                Token_ID: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'tokens',
                        key: 'tokenId',
                    },
                    onDelete: 'CASCADE',
                    onUpdate: 'CASCADE',
                },
                User_ID: {
                    type: DataTypes.BIGINT,
                    allowNull: true, // you can make this false if required
                },
                UserName: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                },
                Avatar: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                },
                Comment: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                Stars: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    validate: {
                        min: 1,
                        max: 5,
                    },
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'comments',
                timestamps: true,
            }
        );
    }

    static associate(models) {
        Comments.belongsTo(models.Tokens, {
            foreignKey: 'Token_ID',
            targetKey: 'tokenId',
            as: 'token',
        });
    }
}

module.exports = Comments;
