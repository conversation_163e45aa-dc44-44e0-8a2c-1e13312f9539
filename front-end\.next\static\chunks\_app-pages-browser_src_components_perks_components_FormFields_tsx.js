"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_perks_components_FormFields_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLGlCQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBLENBQUU7WUFBQSxFQUFHLGVBQWdCO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhN0Usa0JBQWMsa0VBQWlCLGlCQUFnQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/perks/components/FormFields.tsx":
/*!********************************************************!*\
  !*** ./src/components/perks/components/FormFields.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n\n\n\n\n\nconst FormFields = (param)=>{\n    let { formData, errors, handleChange, handleBlur, handleCheckboxChange, handleFileChange, handleDeletePicture, t, validationProgress = {} } = param;\n    // Helper function to get field validation state\n    const getFieldState = (fieldName)=>{\n        const hasError = errors[fieldName];\n        const isValidating = validationProgress[fieldName];\n        const hasValue = formData[fieldName] && String(formData[fieldName]).trim() !== '';\n        return {\n            hasError: !!hasError,\n            isValidating,\n            hasValue,\n            isValid: hasValue && !hasError && !isValidating\n        };\n    };\n    // Helper function to get field styling\n    const getFieldStyling = (fieldName)=>{\n        const state = getFieldState(fieldName);\n        if (state.isValidating) {\n            return 'outline-blue-500 bg-blue-50';\n        } else if (state.hasError) {\n            return 'outline-red-500 bg-red-50';\n        } else if (state.isValid) {\n            return 'outline-green-500 bg-green-50';\n        } else {\n            return 'outline-gray-300';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-5 mx-auto w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('name'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"name\",\n                                    placeholder: t('createPerkForm.name'),\n                                    value: formData.name,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-36 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('description'), \" inline-flex justify-start items-start gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"description\",\n                                    placeholder: t('createPerkForm.description'),\n                                    value: formData.description,\n                                    onChange: handleChange,\n                                    className: \"w-full mt-4 bg-transparent outline-none resize-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.description\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('price'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"price\",\n                                    placeholder: \"Price\",\n                                    value: formData.price,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    children: \"$\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.price\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('tokenAmount'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    name: \"tokenAmount\",\n                                    placeholder: \"Number of tokens buyers receive (e.g., 1)\",\n                                    value: formData.tokenAmount,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    min: \"1\",\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.tokenAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.tokenAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                className: \"text-red-500 text-sm mt-1 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.tokenAmount\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('category'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    value: formData.category,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: t('createPerkForm.selectCategory')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        _constants__WEBPACK_IMPORTED_MODULE_2__.FORM_CATEGORIES.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                className: \"text-gray-700\",\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('fulfillmentLink'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"fulfillmentLink\",\n                                    placeholder: t('createPerkForm.fulfillmentLink'),\n                                    value: formData.fulfillmentLink,\n                                    onChange: handleChange,\n                                    onBlur: handleBlur,\n                                    className: \"flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 relative\",\n                                    children: validationProgress.fulfillmentLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-zinc-300 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                        children: \"!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.fulfillmentLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.fulfillmentLink\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('picture'), \" inline-flex justify-start items-center gap-3 w-full\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                    children: formData.picture ? formData.picture.name : t('createPerkForm.uploadPicture')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleDeletePicture,\n                                    className: \"w-24 h-7 bg-red-500 text-white cursor-pointer hover:bg-red-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center\",\n                                        children: t('createPerkForm.clear')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"w-32 md:w-44 h-7 bg-zinc-300 cursor-pointer hover:bg-zinc-400 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center\",\n                                            children: t('createPerkForm.upload')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"picture-upload\",\n                                            type: \"file\",\n                                            className: \"hidden\",\n                                            onChange: handleFileChange,\n                                            accept: \"image/*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: errors.picture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10\n                                },\n                                className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.picture\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"limitedStock\",\n                                name: \"limitedStock\",\n                                checked: formData.limitedStock,\n                                onChange: handleCheckboxChange,\n                                className: \"h-5 w-5 border-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"limitedStock\",\n                                className: \"ml-2 text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal\",\n                                children: t('createPerkForm.limitedStock')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.limitedStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-[52px] w-[192px] rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 \".concat(getFieldStyling('stockAmount'), \" flex items-center justify-between px-4 py-[13px]\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"stockAmount\",\n                                            placeholder: t('createPerkForm.stock'),\n                                            value: formData.stockAmount,\n                                            onChange: handleChange,\n                                            onBlur: handleBlur,\n                                            className: \"w-full bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        validationProgress.stockAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    children: errors.stockAmount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        className: \"text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-1\",\n                                                children: \"⚠\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            errors.stockAmount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\components\\\\FormFields.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FormFields;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormFields);\nvar _c;\n$RefreshReg$(_c, \"FormFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/perks/components/FormFields.tsx\n"));

/***/ })

}]);