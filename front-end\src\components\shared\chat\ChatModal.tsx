// "use client";
// import React, { useState, useEffect, useCallback, useRef } from "react";
// import { ArrowLeft, MoreVertical, <PERSON><PERSON>he<PERSON>, Send, X } from "lucide-react";
// import { usePrivy } from "@privy-io/react-auth";
// import { useChatSocket } from "@/hooks/useChatSocket";
// import axios from "axios";
// import dayjs from 'dayjs';
// import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';
// import { useTranslation } from '@/hooks/useTranslation';

// interface ChatModalProps {
//   chatRoomId: string;
//   buyerId: string | number;
//   sellerId: string | number;
//   onClose: () => void;
// }
// const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8081";
// const ChatModal: React.FC<ChatModalProps> = ({ chatRoomId, buyerId, sellerId, onClose }) => {
//   const { t, isRTL } = useTranslation();
//   // All hooks at the top
//   const { user } = usePrivy();
//   const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();
//   const { setOpenChatTradeId } = useAppContext();
//   const [messages, setMessages] = useState<any[]>([]);
//   const [input, setInput] = useState("");
//   const messagesEndRef = useRef<HTMLDivElement>(null);
//   const [currentStatus, setCurrentStatus] = useState<string | null>(null);
//   const [releaseDeadline, setReleaseDeadline] = useState<Date | null>(null);
//   const [timeLeft, setTimeLeft] = useState<string | null>(null);
//   const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);
//   const notificationAudioRef = useRef<HTMLAudioElement | null>(null);

//   // Get userBo.id from localStorage for consistent sender check
//   const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
//   const userBo = userBoStr ? JSON.parse(userBoStr) : null;
//   const myUserId = userBo?.id;

//   // Fetch receiver info (email) when receiverId changes
//   useEffect(() => {
//     async function fetchReceiver() {
//       try {
//         const res = await axios.get(`${API_BASE}/users/${sellerId}`);
//         // Try to extract email from possible locations
//         let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);
//         let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);
//         let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);
        
//         setReceiverInfo({ name, email, wallet });
//       } catch (err) {
//         setReceiverInfo(null);
//       }
//     }
//     if (sellerId) fetchReceiver();
//   }, [sellerId]);

//   // Fetch message history on mount or when chatRoomId changes
//   useEffect(() => {
//     async function fetchMessages() {
//       try {
//         const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);
//         // Replace messages state with fetched history (no merge)
//         setMessages((res.data.data || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()));
//       } catch (err) {
//         // handle error
//       }
//     }
//     fetchMessages();
//   }, [chatRoomId]);

//   // Fetch release deadline on mount or when tradeId changes
//   useEffect(() => {
//     async function fetchDeadline() {
//       try {
//         const res = await axios.get(`${API_BASE}/messages/${chatRoomId}/release-deadline`);
//         const deadline = new Date(res.data.releaseDeadline);
//         setReleaseDeadline(deadline);
//       } catch (err) {
//         // handle error
//       }
//     }
//     fetchDeadline();
//   }, [chatRoomId]);

//   // Timer countdown (only updates local state)
//   useEffect(() => {
//     if (!releaseDeadline) return;
//     const interval = setInterval(() => {
//       const now = new Date();
//       const diff = releaseDeadline.getTime() - now.getTime();
//       if (diff <= 0) {
//         setTimeLeft(t('chat.autoReleaseInProgress'));
//         clearInterval(interval);
//       } else {
//         // Format as translation string
//         const days = Math.floor(diff / (1000 * 60 * 60 * 24));
//         const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
//         const minutes = Math.floor((diff / (1000 * 60)) % 60);
//         const seconds = Math.floor((diff / 1000) % 60);
//         setTimeLeft(t('chat.timeFormat', { days, hours, minutes, seconds }));
//       }
//     }, 1000);
//     return () => clearInterval(interval);
//   }, [releaseDeadline, t]);

//   // Handle incoming real-time messages (deduplicate by id, tempId, and content)
//   const handleMessage = useCallback((msg: any) => {
//     setMessages(prev => {
//       // If message already exists (by id, tempId, or identical content+createdAt+senderId), skip
//       if (prev.some(m =>
//         (m.id && msg.id && m.id === msg.id) ||
//         (m.tempId && msg.tempId && m.tempId === msg.tempId) ||
//         (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)
//       )) {
//         return prev;
//       }
//       // Play notification sound if the current user is the receiver
//       const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
//       const userBo = userBoStr ? JSON.parse(userBoStr) : null;
//       if (userBo && msg.receiverId === userBo.id && notificationAudioRef.current) {
//         notificationAudioRef.current.currentTime = 0;
//         notificationAudioRef.current.play();
//       }
//       return [...prev, msg].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
//     });
//   }, []);
//   const handleTradeStatus = useCallback((data: { status: string }) => setCurrentStatus(data.status), []);

//   // Setup socket
//   const { sendMessage, release, report, authenticated, joinedRoom, tradeStatus } = useChatSocket({
//     chatRoomId, // Pass chatRoomId to useChatSocket
//     userId: user?.id || user?.wallet?.address || "unknown",
//     wallet: user?.wallet?.address || "",
//     onMessage: handleMessage,
//     onTradeStatus: handleTradeStatus,
//   });

//   // Scroll to bottom on new message
//   useEffect(() => {
//     messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
//   }, [messages]);

//   // Send message handler (optimistic update)
//   const handleSend = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (!input.trim()) return;
//     const userBoStr = localStorage.getItem("userBo");
//     const userBo = userBoStr ? JSON.parse(userBoStr) : null;
//     if (!userBo?.id) {
//       alert(t('chat.userIdNotFound'));
//       return;
//     }
//     sendMessage({
//       chatRoomId,
//       senderId: userBo.id,
//       receiverId: userBo.id === buyerId ? sellerId : buyerId,
//       message: input,
//     });
//     setInput("");
//   };

//   // Helper: format time
//   const formatTime = (dateStr: string) => {
//     const date = new Date(dateStr);
//     return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
//   };

//   // Helper: format date for separator
//   const formatDateSeparator = (date: Date) => {
//     const today = dayjs().startOf('day');
//     const msgDay = dayjs(date).startOf('day');
//     if (msgDay.isSame(today)) return t('chat.today');
//     if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');
//     return msgDay.format('D MMM YYYY');
//   };

//   // Helper to get initials from email or name
//   const getInitials = (email?: string, name?: string) => {
//     if (name && name.trim()) return name.slice(0, 2).toUpperCase();
//     if (email && email.trim()) return email.slice(0, 2).toUpperCase();
//     return "?";
//   };

//   // Helper to format wallet address (first 4 + last 4 characters)
//   const formatWalletAddress = (wallet?: string) => {
//     if (!wallet || wallet.length < 8) return wallet;
//     return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;
//   };

//   // Helper to get display name with priority: name > email > formatted wallet
//   const getDisplayName = () => {
//     if (receiverInfo?.name && receiverInfo.name.trim()) {
//       return receiverInfo.name;
//     }
//     if (receiverInfo?.email && receiverInfo.email.trim()) {
//       return receiverInfo.email;
//     }
//     if (receiverInfo?.wallet && receiverInfo.wallet.trim()) {
//       return formatWalletAddress(receiverInfo.wallet);
//     }
//     return t('chat.user');
//   };

//   // Show status in the UI and disable buttons if released or reported
//   const isActionDisabled = currentStatus === 'released' || currentStatus === 'reported';

//   // useEffect to set openChatTradeId and clear unread chat messages for this chatRoomId
//   useEffect(() => {
//     setOpenChatTradeId(chatRoomId);
//     removeUnreadChatMessagesForTrade(chatRoomId);
//     return () => {
//       setOpenChatTradeId(null);
//     };
//   }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade]);

//   // Only after all hooks, do conditional returns
//   if (!user?.wallet?.address) {
//     return (
//       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
//         <div className="w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center">
//           <p className="mb-4 text-gray-700 text-center">{t('chat.connectWalletToChat')}</p>
//           {/* You can trigger Privy login here if needed */}
//         </div>
//       </div>
//     );
//   }
//   if (!authenticated || !joinedRoom) {
//     return (
//       <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
//         <div className="w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center">
//           <p className="mb-4 text-gray-700 text-center">{t('chat.connectingToChat')}</p>
//         </div>
//       </div>
//     );
//   }


//   return (
//     <div
//       className={`fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300 ${isRTL ? 'rtl' : 'ltr'}`}
//     >
//       {/* Notification sound */}
//       <audio ref={notificationAudioRef} src="/sounds/notification.mp3" preload="auto" />
//         {/* Close button */}
//         <button
//           className="absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1"
//           onClick={onClose}
//         >
//           <X className="w-5 h-5 text-gray-700" />
//         </button>
//         {/* Header */}
//         {/* <div
//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}
//         >
//           <button
//             className={isRTL ? 'ml-3' : 'mr-3'}
//             onClick={onClose}
//             style={isRTL ? { marginLeft: '12px', marginRight: 0 } : { marginRight: '12px', marginLeft: 0 }}
//           >
//             <ArrowLeft className="w-6 h-6 text-gray-700" />
//           </button> */}
//           <div
//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}
//         >
//           <button className={isRTL ? 'ml-3' : 'mr-3'} onClick={onClose}>
//             <ArrowLeft className="w-6 h-6 text-gray-700" />
//           </button>
//           {/* <div
//             className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}
//             style={isRTL ? { direction: 'rtl', textAlign: 'right', alignItems: 'flex-end' } : { direction: 'ltr', textAlign: 'left', alignItems: 'flex-start' }}
//           >
//             <div className="relative">
//               <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
//                 <span className="text-white font-semibold text-sm">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>
//               </div>
//               <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
//             </div>
//             <div className="flex flex-col items-start" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>
//               <span className="font-semibold text-gray-900 text-sm">{getDisplayName()}</span>
//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (
//                 <span className="text-xs text-gray-500">{receiverInfo.email}</span>
//               )}
//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (
//                 <span className="text-xs text-gray-400 font-mono">{formatWalletAddress(receiverInfo.wallet)}</span>
//               )}
//             </div>
//           </div> */}
//           <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`} style={isRTL ? { direction: 'rtl' } : {}}>
//             <div className="relative">
//               <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
//                 <span className="text-white font-semibold text-sm">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>
//               </div>
//               <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
//             </div>
//             <div className="flex flex-col items-start" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>
//               <span className="font-semibold text-gray-900 text-sm">{getDisplayName()}</span>
//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (
//                 <span className="text-xs text-gray-500">{receiverInfo.email}</span>
//               )}
//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (
//                 <span className="text-xs text-gray-400 font-mono">{formatWalletAddress(receiverInfo.wallet)}</span>
//               )}
//             </div>
//           </div>
          
//         </div>
//         {/* Messages Container */}
//         <div className="flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto" style={{ maxHeight: 400 }}>
//           {(() => {
//             let lastDate: string | null = null;
//             return messages.map((msg, idx) => {
//               const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';
//               const showDate = msgDate && msgDate !== lastDate;
//               lastDate = msgDate;
//               return (
//                 <React.Fragment key={msg.id ? `id-${msg.id}` : msg.tempId ? `temp-${msg.tempId}` : `fallback-${msg.senderId}-${msg.createdAt}-${idx}` }>
//                   {showDate && (
//                     <div className="flex justify-center my-2">
//                       <span className="bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow">{msgDate}</span>
//                     </div>
//                   )}
//                   <div className={`flex flex-col ${msg.senderId === myUserId ? "items-end" : "items-start"}`}>
//                     <div
//                       className={
//                         msg.senderId === myUserId
//                           ? "bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow"
//                           : "bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow"
//                       }
//                     >
//                       <p className="text-sm leading-relaxed whitespace-pre-line">{msg.message || msg.text}</p>
//                     </div>
//                     <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? "mr-2" : "ml-2"}`}>
//                       <span className="text-xs text-gray-400">
//                         {msg.createdAt ? formatTime(msg.createdAt) : msg.time || ""}
//                       </span>
//                       {msg.senderId === myUserId && <CheckCheck className="w-4 h-4 text-green-500" />}
//                     </div>
//                   </div>
//                 </React.Fragment>
//               );
//             });
//           })()}
//           <div ref={messagesEndRef} />
//         </div>

//         {/* Bottom Section */}
//         {/* Auto-release Info */}
//         <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
//           <p className="text-xs text-gray-500 text-center leading-relaxed">
//             {timeLeft ? (
//               <>{t('chat.autoReleaseIn', { time: timeLeft })}</>
//             ) : (
//               <>{t('chat.loadingAutoRelease')}</>
//             )}<br />
//             {t('chat.reportTrade')}
//           </p>
//         </div>

//         {/* Action Buttons */}
//         <div className="px-4 py-3 space-y-2">
//           <button
//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//             onClick={release}
//             disabled={isActionDisabled}
//           >
//             {t('chat.iGotTheItem')}
//           </button>
//           <button
//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//             onClick={report}
//             disabled={isActionDisabled}
//           >
//             {t('chat.reportTrade')}
//           </button>
//           {currentStatus && (
//             <div className="text-center text-xs text-gray-500 mt-2">
//               {t('chat.tradeStatus')} <span className="font-semibold">{currentStatus}</span>
//             </div>
//           )}
//         </div>

//         {/* Message Input */}
//         <form className="px-4 py-3 bg-orange-50" onSubmit={handleSend}>
//           <div className="flex items-center gap-2">
//             <input
//               type="text"
//               placeholder={t('chat.typeMessage')}
//               className="flex-1 bg-orange-100 rounded-full px-4 py-2 text-sm outline-none border border-transparent focus:border-orange-300 placeholder:text-gray-500"
//               value={input}
//               onChange={e => setInput(e.target.value)}
//               disabled={!authenticated || !joinedRoom}
//             />
//             <button type="submit" className="bg-orange-500 p-2 rounded-full hover:bg-orange-600 transition-colors" disabled={!authenticated || !joinedRoom}>
//               <Send className="w-5 h-5 text-white" />
//             </button>
//           </div>
//         </form>
//     </div>
//   );
// };

// export default ChatModal; 




"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  ArrowLeft,
  CheckCheck,
  Send,
  DollarSign,
  AlertTriangle,
  Flag
} from "lucide-react";
import { usePrivy } from "@privy-io/react-auth";
import { useChatSocket } from "@/hooks/useChatSocket";
import { useWallet } from "@/hooks/useWallet";
import axios from "axios";
import dayjs from 'dayjs';
import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';
import { useTranslation } from '@/hooks/useTranslation';
import { canInitiateDispute, getDisputeStatusInfo } from '@/utils/escrow';
import { useChatModalState } from '@/contexts/ChatModalStateContext';

interface ChatModalProps {
  chatRoomId: string;
  buyerId: string | number;
  sellerId: string | number;
  onClose: () => void;
  onRelease: () => void;
  onRefund: () => void;
  onReport: () => void;
  onAccept?: () => void;
  onInitiateDispute?: (role: 'buyer' | 'seller') => void;
  activeTrade?: any;
}

const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8081";

const ChatModal: React.FC<ChatModalProps> = ({
  chatRoomId,
  buyerId,
  sellerId,
  onClose,
  onRelease,
  onRefund,
  onReport,
  onAccept,
  onInitiateDispute,
  activeTrade
}) => {
  const { t, isRTL } = useTranslation();

  // Debug props received
  console.log('🎯 [ChatModal] Props received:', {
    chatRoomId,
    buyerId,
    sellerId,
    hasOnRelease: !!onRelease,
    hasOnAccept: !!onAccept,
    hasOnReport: !!onReport,
    hasActiveTrade: !!activeTrade,
    activeTrade: activeTrade ? {
      id: activeTrade.id,
      status: activeTrade.status,
      tradeId: activeTrade.tradeId,
      from: activeTrade.from,
      to: activeTrade.to
    } : null
  });
  const { user } = usePrivy();
  const { solanaWallet, isConnected, getWalletAddress } = useWallet();
  const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();
  const { setOpenChatTradeId } = useAppContext();
  const { registerChatModal, unregisterChatModal, updateChatModalState, getChatModalState } = useChatModalState();
  const [messages, setMessages] = useState<any[]>([]);
  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);
  const notificationAudioRef = useRef<HTMLAudioElement | null>(null);
  const [currentTradeStatus, setCurrentTradeStatus] = useState<string | null>(activeTrade?.status || null);
  const [isOperationInProgress, setIsOperationInProgress] = useState(false);

  // Request notification permission when component mounts
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('🔔 [ChatModal] Notification permission:', permission);
      });
    }
  }, []);

  // Get user ID
  const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
  const userBo = userBoStr ? JSON.parse(userBoStr) : null;
  const myUserId = userBo?.id;
  
  // Determine user role - ensure type consistency
  const buyerIdNum = typeof buyerId === 'string' ? parseInt(buyerId) : buyerId;
  const sellerIdNum = typeof sellerId === 'string' ? parseInt(sellerId) : sellerId;
  const myUserIdNum = typeof myUserId === 'string' ? parseInt(myUserId) : myUserId;

  const isBuyer = myUserIdNum === buyerIdNum;
  const isSeller = myUserIdNum === sellerIdNum;

  // Use current trade status (which can be updated via Socket.IO) or fallback to activeTrade status
  const tradeStatus = currentTradeStatus || activeTrade?.status;
  const isEscrowed = tradeStatus === 'escrowed';
  const isPendingAcceptance = tradeStatus === 'pending_acceptance';
  const isEscrowReleased = tradeStatus === 'completed' || tradeStatus === 'released';
  const isUserAuthorized = user && (isBuyer || isSeller);

  // Trade action buttons should only show when escrow is active (not released)
  const shouldShowTradeActions = (isEscrowed || isPendingAcceptance) && !isEscrowReleased;

  // Seller can accept when escrow is pending acceptance
  const canSellerAccept = isPendingAcceptance && isSeller && onAccept;

  // Buyer can only release after seller has accepted
  const canBuyerRelease = isEscrowed && !isPendingAcceptance && isBuyer;

  // Debug logging
  console.log('🔍 [ChatModal] User role debugging:', {
    myUserId,
    myUserIdNum,
    buyerId,
    buyerIdNum,
    sellerId,
    sellerIdNum,
    isBuyer,
    isSeller,
    activeTrade: activeTrade ? {
      id: activeTrade.id,
      status: activeTrade.status,
      tradeId: activeTrade.tradeId
    } : null,
    tradeStatus,
    currentTradeStatus,
    isEscrowed,
    isPendingAcceptance,
    isEscrowReleased,
    shouldShowTradeActions,
    canSellerAccept,
    canBuyerRelease,
    isUserAuthorized,
    onAcceptExists: !!onAccept,
    onReleaseExists: !!onRelease,
    onReportExists: !!onReport,
    hasActiveTrade: !!activeTrade,
    activeTradeStatus: activeTrade?.status
  });

  // Chat should be enabled if user is authorized (buyer or seller), regardless of escrow status
  const canChat = isUserAuthorized && isConnected && solanaWallet;

  // Enhanced dispute button logic
  const canShowDisputeButton = (userRole: 'buyer' | 'seller') => {
    if (!activeTrade || !onInitiateDispute) return false;

    // Use the enhanced canInitiateDispute function with dispute status
    const disputeCheck = canInitiateDispute(
      activeTrade.status,
      activeTrade.createdAt,
      2, // 2 days deadline
      activeTrade.disputeStatus
    );

    // Debug logging for dispute button visibility
    console.log(`🔍 [ChatModal] Dispute button check for ${userRole}:`, {
      tradeStatus: activeTrade.status,
      disputeStatus: activeTrade.disputeStatus,
      canDispute: disputeCheck.canDispute,
      reason: disputeCheck.reason,
      createdAt: activeTrade.createdAt
    });

    return disputeCheck.canDispute;
  };

  // Fetch receiver info
  useEffect(() => {
    async function fetchReceiver() {
      try {
        const targetId = isBuyer ? sellerId : buyerId;
        const res = await axios.get(`${API_BASE}/users/${targetId}`);
        
        let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);
        let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);
        let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);
        
        setReceiverInfo({ name, email, wallet });
      } catch (err) {
        setReceiverInfo(null);
      }
    }
    fetchReceiver();
  }, [buyerId, sellerId, isBuyer]);

  // Fetch messages
  useEffect(() => {
    async function fetchMessages() {
      try {
        const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);
        setMessages((res.data.data || []).sort((a: any, b: any) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        ));
      } catch (err) {
        console.error("Error fetching messages:", err);
      }
    }
    fetchMessages();
  }, [chatRoomId]);

  // Handle incoming messages
  const handleMessage = useCallback((msg: any) => {
    console.log('📨 [ChatModal] Received message:', msg, 'myUserId:', myUserId);

    setMessages(prev => {
      // Check for duplicates
      const isDuplicate = prev.some(m =>
        (m.id && msg.id && m.id === msg.id) ||
        (m.tempId && msg.tempId && m.tempId === msg.tempId) ||
        (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)
      );

      if (isDuplicate) {
        console.log('🔄 [ChatModal] Duplicate message, skipping');
        return prev;
      }

      // Play notification sound for received messages (not sent by me)
      if (msg.senderId !== myUserId && notificationAudioRef.current) {
        console.log('🔊 [ChatModal] Playing notification sound');
        try {
          notificationAudioRef.current.currentTime = 0;
          notificationAudioRef.current.play().catch(e => {
            console.log('🔇 [ChatModal] Could not play notification sound (user interaction required):', e);
            // Fallback: show browser notification if audio fails
            if ('Notification' in window && Notification.permission === 'granted') {
              new Notification('New Message', {
                body: msg.message,
                icon: '/images/funhi-logo.png'
              });
            }
          });
        } catch (error) {
          console.log('🔇 [ChatModal] Audio play error:', error);
        }
      }

      const newMessages = [...prev, msg].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      console.log('✅ [ChatModal] Added new message, total messages:', newMessages.length);
      return newMessages;
    });
  }, [myUserId]);

  // Handle trade status updates
  const handleTradeStatusUpdate = useCallback((data: { tradeId: string | number; status: string }) => {
    console.log("[ChatModal] Trade status update received:", data);
    if (data.tradeId === activeTrade?.id || data.tradeId === activeTrade?.tradeId) {
      setCurrentTradeStatus(data.status);
      console.log(`[ChatModal] Updated trade status to: ${data.status}`);

      // Update the state context
      updateChatModalState(chatRoomId, {
        currentTradeStatus: data.status,
        activeTrade: { ...activeTrade, status: data.status }
      });
    }
  }, [activeTrade?.id, activeTrade?.tradeId, activeTrade, chatRoomId, updateChatModalState]);



  // Get the correct wallet address
  const walletAddress = getWalletAddress();

  console.log('🔍 [ChatModal] Wallet info:', {
    privyWallet: user?.wallet?.address,
    useWalletAddress: walletAddress,
    isConnected,
    chatRoomId
  });



  // Setup socket
  const { sendMessage, authenticated, joinedRoom } = useChatSocket({
    chatRoomId,
    userId: user?.id || myUserId || "unknown",
    wallet: walletAddress || "",
    onMessage: handleMessage,
    onTradeStatus: handleTradeStatusUpdate,
  });

  // Scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Set chat as open and register with state context
  useEffect(() => {
    setOpenChatTradeId(chatRoomId);
    removeUnreadChatMessagesForTrade(chatRoomId);

    // Register this chat modal with the state context
    registerChatModal(chatRoomId, {
      chatRoomId,
      activeTrade,
      currentTradeStatus: activeTrade?.status || null,
      isOperationInProgress: false
    });

    return () => {
      setOpenChatTradeId(null);
      unregisterChatModal(chatRoomId);
    };
  }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade, registerChatModal, unregisterChatModal, activeTrade]);

  // Fetch latest trade status when modal opens
  useEffect(() => {
    const fetchLatestTradeStatus = async () => {
      const tradeId = activeTrade?.id || activeTrade?.tradeId;
      if (tradeId) {
        try {
          console.log('🔄 [ChatModal] Fetching latest trade status for tradeId:', tradeId);
          const { getTradeDetails } = await import('../../../axios/requests');
          const tradeResponse = await getTradeDetails(Number(tradeId));

          if (tradeResponse.status === 200 && tradeResponse.data.status !== currentTradeStatus) {
            console.log(`🔄 [ChatModal] Trade status updated: ${currentTradeStatus} → ${tradeResponse.data.status}`);
            setCurrentTradeStatus(tradeResponse.data.status);

            // Update the state context with the latest status
            updateChatModalState(chatRoomId, {
              currentTradeStatus: tradeResponse.data.status,
              activeTrade: { ...activeTrade, status: tradeResponse.data.status }
            });
          }
        } catch (error) {
          console.error('❌ [ChatModal] Failed to fetch latest trade status:', error);
        }
      } else {
        console.log('⚠️ [ChatModal] No trade ID available for status fetch');
      }
    };

    fetchLatestTradeStatus();
  }, [activeTrade?.id, activeTrade?.tradeId, chatRoomId, updateChatModalState, activeTrade, currentTradeStatus]); // Run when either ID changes

  // Send message
  const handleSend = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || !canChat) return;

    const receiverId = isBuyer ? sellerId : buyerId;

    console.log('🔍 [ChatModal] Sending message:', {
      chatRoomId,
      senderId: myUserId,
      receiverId,
      message: input.trim(),
      canChat,
      isEscrowed
    });

    sendMessage({
      chatRoomId,
      senderId: myUserId,
      receiverId,
      message: input.trim(),
    });

    setInput("");
  };

  // Enhanced action handlers that update status immediately and send system messages
  const handleReleaseWithStatusUpdate = useCallback(async () => {
    if (!onRelease) return;

    try {
      setIsOperationInProgress(true);
      console.log('🔄 [ChatModal] Starting escrow release...');

      // Call the original release function
      await onRelease();

      // Update status immediately for better UX
      setCurrentTradeStatus('released');
      console.log('✅ [ChatModal] Escrow released, status updated to released');

      // Send a system message to the chat
      const receiverId = isBuyer ? sellerId : buyerId;
      sendMessage({
        chatRoomId,
        senderId: myUserId,
        receiverId,
        message: `🎉 Escrow has been released! Trade completed successfully.`,
      });

    } catch (error) {
      console.error('❌ [ChatModal] Release failed:', error);
    } finally {
      setIsOperationInProgress(false);
    }
  }, [onRelease, sendMessage, chatRoomId, myUserId, isBuyer, sellerId, buyerId]);

  const handleAcceptWithStatusUpdate = useCallback(async () => {
    if (!onAccept) return;

    try {
      setIsOperationInProgress(true);
      console.log('🔄 [ChatModal] Starting escrow accept...');

      // Call the original accept function
      await onAccept();

      // Update status immediately for better UX
      setCurrentTradeStatus('escrowed');
      console.log('✅ [ChatModal] Escrow accepted, status updated to escrowed');

      // Send a system message to the chat
      const receiverId = isBuyer ? sellerId : buyerId;
      sendMessage({
        chatRoomId,
        senderId: myUserId,
        receiverId,
        message: `✅ Escrow accepted! Buyer can now release funds when ready.`,
      });

    } catch (error) {
      console.error('❌ [ChatModal] Accept failed:', error);
    } finally {
      setIsOperationInProgress(false);
    }
  }, [onAccept, sendMessage, chatRoomId, myUserId, isBuyer, sellerId, buyerId]);

  // Format time
  const formatTime = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Format date separator
  const formatDateSeparator = (date: Date) => {
    const today = dayjs().startOf('day');
    const msgDay = dayjs(date).startOf('day');
    
    if (msgDay.isSame(today)) return t('chat.today');
    if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');
    return msgDay.format('D MMM YYYY');
  };

  // Get display name
  const getDisplayName = () => {
    if (receiverInfo?.name) return receiverInfo.name;
    if (receiverInfo?.email) return receiverInfo.email;
    if (receiverInfo?.wallet) {
      const wallet = receiverInfo.wallet;
      return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;
    }
    return t('chat.user');
  };

  // Wallet formatting
  const formatWalletAddress = (wallet?: string) => {
    if (!wallet || wallet.length < 8) return wallet;
    return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;
  };

  if (!user?.wallet?.address) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
        <div className="w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center">
          <p className="mb-4 text-gray-700 text-center">{t('chat.connectWalletToChat')}</p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  if (!authenticated || !joinedRoom) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
        <div className="w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center">
          <p className="mb-4 text-gray-700 text-center">{t('chat.connectingToChat')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300">
      <audio ref={notificationAudioRef} src="/sounds/notification.mp3" preload="auto" />
      
      {/* Header */}
      <div className="flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl">
        <button
          onClick={onClose}
          className="p-2 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 transition-all duration-200"
        >
          <ArrowLeft className="w-5 h-5 text-gray-700" />
        </button>
        <div className="flex items-center gap-3 ml-3">
          <div className="relative">
            <div className="w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {receiverInfo?.name?.slice(0, 2).toUpperCase() ||
                 receiverInfo?.email?.slice(0, 2).toUpperCase() ||
                 "?"}
              </span>
            </div>
            <span className="absolute bottom-0 right-0 w-3 h-3 bg-emerald-500 border-2 border-white rounded-full"></span>
          </div>
          <div className="flex flex-col items-start">
            <span className="font-semibold text-gray-900 text-sm">{getDisplayName()}</span>
            {receiverInfo?.email && (
              <span className="text-xs text-gray-500">{receiverInfo.email}</span>
            )}
            {receiverInfo?.wallet && (
              <span className="text-xs text-gray-400 font-mono">
                {formatWalletAddress(receiverInfo.wallet)}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Trade Status Indicator */}
      <div className={`px-4 py-2 border-b ${
        isEscrowReleased
          ? 'bg-green-50 border-green-100'
          : isEscrowed
            ? 'bg-orange-50 border-orange-100'
            : isPendingAcceptance
              ? 'bg-blue-50 border-blue-100'
              : 'bg-amber-50 border-amber-100'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              isEscrowReleased
                ? 'bg-green-600'
                : isEscrowed
                  ? 'bg-[#FF6600]'
                  : isPendingAcceptance
                    ? 'bg-blue-500'
                    : 'bg-amber-500'
            }`}></div>
            <span className="text-sm font-medium text-slate-700">
              {isEscrowReleased
                ? 'Trade Completed'
                : isEscrowed
                  ? 'Escrow Active'
                  : isPendingAcceptance
                    ? 'Awaiting Seller Acceptance'
                    : 'Pre-Purchase Chat'
              }
            </span>
          </div>
          <span className="text-xs text-slate-500">
            {isEscrowReleased
              ? 'Funds released successfully'
              : isEscrowed
                ? 'Funds secured on-chain'
                : isPendingAcceptance
                  ? 'Seller needs to accept escrow'
                : 'Discussing before purchase'
            }
          </span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto max-h-[400px]">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {isEscrowed ? 'Escrow Chat Started' : 'Start the Conversation'}
            </h3>
            <p className="text-sm text-gray-600 max-w-xs">
              {isEscrowed
                ? 'Your funds are secured. Chat with the other party about the trade details.'
                : 'Discuss the details before making a purchase. Ask questions and clarify expectations.'
              }
            </p>
          </div>
        ) : (
          messages.map((msg, idx) => {
          const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';
          const showDate = idx === 0 || msgDate !== formatDateSeparator(new Date(messages[idx-1].createdAt));
          
          return (
            <React.Fragment key={msg.id || `msg-${idx}`}>
              {showDate && (
                <div className="flex justify-center my-2">
                  <span className="bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow">
                    {msgDate}
                  </span>
                </div>
              )}
              <div className={`flex flex-col ${msg.senderId === myUserId ? "items-end" : "items-start"}`}>
                <div className={
                  msg.senderId === myUserId
                    ? "bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow"
                    : "bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow"
                }>
                  <p className="text-sm leading-relaxed whitespace-pre-line">{msg.message}</p>
                </div>
                <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? "mr-2" : "ml-2"}`}>
                  <span className="text-xs text-gray-400">
                    {msg.createdAt ? formatTime(msg.createdAt) : ""}
                  </span>
                  {msg.senderId === myUserId && <CheckCheck className="w-4 h-4 text-green-500" />}
                </div>
              </div>
            </React.Fragment>
          );
        }))}
        <div ref={messagesEndRef} />
      </div>

      {/* Trade Status and Actions */}
      {activeTrade && (
        <div className="px-4 py-3 border-t border-gray-200">
          {/* Trade Status Display */}
          <div className="mb-3">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Trade Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${
                (currentTradeStatus || activeTrade.status) === 'escrowed' ? 'bg-blue-100 text-blue-800' :
                (currentTradeStatus || activeTrade.status) === 'completed' ? 'bg-green-100 text-green-800' :
                (currentTradeStatus || activeTrade.status) === 'released' ? 'bg-green-100 text-green-800' :
                (currentTradeStatus || activeTrade.status) === 'pending_acceptance' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {currentTradeStatus || activeTrade.status}
              </span>
            </div>

            {/* Dispute Status */}
            {activeTrade.disputeStatus && activeTrade.disputeStatus !== 'none' && (
              <div className="flex justify-between items-center text-sm mt-1">
                <span className="text-gray-600">Dispute:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  getDisputeStatusInfo(activeTrade.disputeStatus).bgColor
                } ${getDisputeStatusInfo(activeTrade.disputeStatus).color}`}>
                  {getDisputeStatusInfo(activeTrade.disputeStatus).label}
                </span>
              </div>
            )}
          </div>

          {/* Action Buttons - Only show when escrow is active and not released */}
          {shouldShowTradeActions && isConnected && solanaWallet && isUserAuthorized && (
            <div className="space-y-3 p-4 bg-slate-50 rounded-lg">
              {/* Buyer Actions */}
              {isBuyer && (
                <>
                  {/* Release Escrow Button - BUYER ONLY - Only show when seller has accepted */}
                  {canBuyerRelease && (
                    <button
                      className="w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleReleaseWithStatusUpdate}
                      disabled={!isConnected || isOperationInProgress}
                    >
                      {isOperationInProgress ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <DollarSign className="w-4 h-4" />
                      )}
                      {isOperationInProgress ? 'Releasing...' : t('chat.releaseFunds')}
                    </button>
                  )}

                  {/* Waiting for Seller Acceptance Message */}
                  {isPendingAcceptance && (
                    <div className="w-full p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm text-blue-700 font-medium">
                          Waiting for seller to accept escrow
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 text-center mt-1">
                        You'll be able to release funds once the seller accepts
                      </p>
                    </div>
                  )}

                  {/* Report Trade Button */}
                  <button
                    className="w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={onReport}
                    disabled={!isConnected}
                  >
                    <Flag className="w-4 h-4" />
                    {t('chat.reportTrade')}
                  </button>

                  {/* Dispute Button for Buyer */}
                  {canShowDisputeButton('buyer') && (
                    <button
                      className="w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => onInitiateDispute?.('buyer')}
                      disabled={!isConnected}
                    >
                      <AlertTriangle className="w-4 h-4" />
                      Initiate Dispute
                    </button>
                  )}
                </>
              )}

              {/* Seller Actions */}
              {isSeller && (
                <>
                  {/* Accept Escrow Button - SELLER ONLY - Only show when pending acceptance */}
                  {canSellerAccept && (
                    <button
                      className="w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleAcceptWithStatusUpdate}
                      disabled={!isConnected || isOperationInProgress}
                    >
                      {isOperationInProgress ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <CheckCheck className="w-4 h-4" />
                      )}
                      {isOperationInProgress ? 'Accepting...' : 'Accept Escrow'}
                    </button>
                  )}

                  {/* Escrow Accepted Message */}
                  {isEscrowed && !isPendingAcceptance && (
                    <div className="w-full p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-center space-x-2">
                        <CheckCheck className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-green-700 font-medium">
                          Escrow accepted
                        </span>
                      </div>
                      <p className="text-xs text-green-600 text-center mt-1">
                        Waiting for buyer to release funds
                      </p>
                    </div>
                  )}

                  {/* Report Trade Button */}
                  <button
                    className="w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={onReport}
                    disabled={!isConnected}
                  >
                    <Flag className="w-4 h-4" />
                    {t('chat.reportTrade')}
                  </button>

                  {/* Dispute Button for Seller */}
                  {canShowDisputeButton('seller') && (
                    <button
                      className="w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => onInitiateDispute?.('seller')}
                      disabled={!isConnected}
                    >
                      <AlertTriangle className="w-4 h-4" />
                      Initiate Dispute
                    </button>
                  )}

                  {/* Dispute Status Information */}
                  {!canShowDisputeButton('seller') && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-xs text-blue-800 text-center">
                        {activeTrade?.disputeStatus && activeTrade.disputeStatus !== 'none'
                          ? `Dispute status: ${activeTrade.disputeStatus}`
                          : 'Dispute option available within 2 days of escrow creation'
                        }
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {/* Escrow Completed Status */}
          {isEscrowReleased && isUserAuthorized && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-center space-x-2">
                <CheckCheck className="w-5 h-5 text-green-600" />
                <div className="text-center">
                  <p className="text-sm font-medium text-green-800">Trade Completed</p>
                  <p className="text-xs text-green-700">
                    Escrow has been released. This trade is now complete.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Wallet Connection Warning */}
          {isEscrowed && isUserAuthorized && (!isConnected || !solanaWallet) && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-yellow-800">Wallet Connection Required</p>
                  <p className="text-sm text-yellow-700">
                    Please connect your Solana wallet to perform trade actions like releasing funds or initiating disputes.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* User Authorization Warning */}
          {isEscrowed && (!user || (!isBuyer && !isSeller)) && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-red-800">Access Restricted</p>
                  <p className="text-sm text-red-700">
                    Only the buyer and seller can perform trade actions.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Dispute Information */}
          {activeTrade.disputeStatus === 'open' && (
            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-xs text-yellow-800">
                A dispute has been initiated. A moderator will review this case shortly.
              </p>
            </div>
          )}

          {activeTrade.disputeStatus === 'resolved' && (
            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-xs text-green-800">
                The dispute for this trade has been resolved by a moderator.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Message Input */}
      <form className="px-4 py-3 bg-slate-50 border-t border-slate-200" onSubmit={handleSend}>
        <div className="flex items-center gap-3">
          <input
            type="text"
            placeholder={canChat ? t('chat.typeMessage') : 'Connect wallet to chat...'}
            className="flex-1 bg-white rounded-lg px-4 py-3 text-sm outline-none border border-slate-300 focus:border-[#FF6600] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 placeholder:text-slate-500 transition-all duration-200"
            value={input}
            onChange={e => setInput(e.target.value)}
            disabled={!canChat}
          />
          <button
            type="submit"
            className={`p-3 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm ${
              canChat && input.trim()
                ? 'bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2'
                : 'bg-slate-300 cursor-not-allowed opacity-50'
            }`}
            disabled={!input.trim() || !canChat}
          >
            <Send className="w-4 h-4 text-white" />
          </button>
        </div>
        {!canChat && (
          <div className="mt-2 text-xs text-gray-600 text-center">
            {!isUserAuthorized
              ? 'Only buyer and seller can chat'
              : !isConnected
                ? 'Please connect your wallet to chat'
                : 'Wallet connection required'
            }
          </div>
        )}
      </form>
    </div>
  );
};

export default ChatModal;