'use client';

import {
  Co<PERSON>,
  <PERSON>Bag,
  FileText,
  TrendingUp,
  MessageCircle,
  Gift,
  Flame,
} from 'lucide-react';
import React from 'react';
import { AppError } from '@/utils/errorHandling';
import { useTranslation } from '@/hooks/useTranslation';
import dynamic from 'next/dynamic';

const EmptyState = dynamic(() => import('./EmptyStates/BaseEmptyState').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

export const NoCoinsEmpty = ({
  onCreateCoin,
}: {
  onCreateCoin?: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<Coins size={48} className="text-gray-400" />}
      title={`${t('common.noResults')}`}
      description={t('common.tryAnotherSearch')}
      action={
        onCreateCoin
          ? {
              label: t('common.createToken'),
              onClick: onCreateCoin,
            }
          : undefined
      }
    />
  );
};

export const NoPerksEmpty = ({
  onCreatePerk,
}: {
  onCreatePerk?: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<Gift size={48} className="text-gray-400" />}
      title={t('table.noPerksAvailable')}
      description={t('table.noPerksAvailableDescription')}
      action={
        onCreatePerk
          ? {
              label: t('table.createPerk'),
              onClick: onCreatePerk,
            }
          : undefined
      }
    />
  );
};

export const NoTransactionsEmpty = () => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<FileText size={48} className="text-gray-400" />}
      title={t('table.noTransactionsYet')}
      description={t('table.noTransactionsYetDescription')}
    />
  );
};

export const NoCommentsEmpty = () => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<MessageCircle size={48} className="text-gray-400" />}
      title={t('table.noCommentsYet')}
      description={t('table.noCommentsYetDescription')}
    />
  );
};

export const NoReviewsEmpty = () => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<MessageCircle size={48} className="text-gray-400" />}
      title={t('table.noReviewsYet')}
      description={t('table.noReviewsYetDescription')}
    />
  );
};

export const NoAirdropsEmpty = ({
  onCreateAirdrop,
}: {
  onCreateAirdrop?: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<Gift size={48} className="text-gray-400" />}
      title={t('table.noAirdropsCreated')}
      description={t('table.noAirdropsCreatedDescription')}
      action={
        onCreateAirdrop
          ? {
              label: t('table.createAirdrop'),
              onClick: onCreateAirdrop,
            }
          : undefined
      }
    />
  );
};

export const NoBurnsEmpty = ({
  onCreateBurn,
}: {
  onCreateBurn?: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<Flame size={48} className="text-gray-400" />}
      title={t('table.noBurnsCreated')}
      description={t('table.noBurnsCreatedDescription')}
      action={
        onCreateBurn
          ? {
              label: t('table.burnTokens'),
              onClick: onCreateBurn,
            }
          : undefined
      }
    />
  );
};

export const NoTokensPortfolioEmpty = () => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<TrendingUp size={48} className="text-gray-400" />}
      title={t('table.portfolioEmpty')}
      description={t('table.portfolioEmptyDescription')}
    />
  );
};

export const NoPerksBoughtEmpty = () => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<ShoppingBag size={48} className="text-gray-400" />}
      title={t('table.noPerksPurchased')}
      description={t('table.noPerksPurchasedDescription')}
    />
  );
};

export const SearchEmptyState = ({ searchTerm }: { searchTerm: string }) => {
  const { t } = useTranslation();
  return (
    <EmptyState
      icon={<Coins size={48} className="text-gray-400" />}
      title={`${t('common.noResults')} "${searchTerm}"`}
      description={t('common.tryAnotherSearch')}
    />
  );
};

interface ErrorStateProps {
  onRetry?: () => void;
  error?: Error | null;
  errorInfo?: React.ErrorInfo | null;
  title?: string;
  description?: string;
  showTechnicalDetails?: boolean;
}

export const ErrorState = ({
  onRetry,
  error,
  errorInfo,
  title = 'Something went wrong',
  description = 'We encountered an error while loading the data. Please try again.',
  showTechnicalDetails = false
}: ErrorStateProps) => {
  const [showDetails, setShowDetails] = React.useState(showTechnicalDetails);

  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  const errorCode = error instanceof AppError ? error.code : undefined;
  const errorStatus = error instanceof AppError ? error.status : undefined;

  const getErrorSeverity = () => {
    if (error instanceof AppError) {
      if (error.status >= 500) return 'error';
      if (error.status >= 400) return 'warning';
      return 'info';
    }
    return 'error';
  };

  const severity = getErrorSeverity();
  const severityColors = {
    error: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600'
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg">
      <div className={`text-lg font-semibold mb-2 ${severityColors[severity]}`}>
        {title}
      </div>
      <div className={`text-sm mb-4 text-center ${severityColors[severity]}`}>
        {description}
      </div>
      
      {error && (
        <div className="w-full max-w-md mb-4">
          <div className={`text-sm font-medium mb-1 ${severityColors[severity]}`}>
            Error Details:
          </div>
          <div className="bg-white p-3 rounded border border-red-200">
            <div className={`text-sm mb-1 ${severityColors[severity]}`}>
              {errorMessage}
            </div>
            {errorCode && (
              <div className="text-gray-600 text-xs mb-1">
                Code: {errorCode}
              </div>
            )}
            {errorStatus && (
              <div className="text-gray-600 text-xs mb-1">
                Status: {errorStatus}
              </div>
            )}
          </div>
        </div>
      )}

      {errorInfo && (
        <div className="w-full max-w-md mb-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className={`text-sm hover:opacity-80 mb-2 ${severityColors[severity]}`}
          >
            {showDetails ? 'Hide Technical Details' : 'Show Technical Details'}
          </button>
          
          {showDetails && (
            <div className="bg-white p-3 rounded border border-red-200 overflow-auto max-h-40">
              <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                {errorInfo.componentStack}
              </pre>
            </div>
          )}
        </div>
      )}

      <div className="flex gap-4">
        {onRetry && (
          <button
            onClick={onRetry}
            className={`px-4 py-2 text-white rounded hover:opacity-90 transition-colors ${
              severity === 'error' ? 'bg-red-600' :
              severity === 'warning' ? 'bg-yellow-600' :
              'bg-blue-600'
            }`}
          >
            Try Again
          </button>
        )}
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
};
