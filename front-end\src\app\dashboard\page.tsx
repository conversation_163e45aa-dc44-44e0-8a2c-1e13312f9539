'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import LinkBox from '@/components/shared/linkBox';

import { ROUTES } from '@/constants';
import { fadeInUp, staggerContainer } from '@/lib/animations';
import { getUserPurchasesSummary } from '../../axios/requests';
import { useAppContext } from '../../contexts/AppContext';
import { useTranslation } from '@/hooks/useTranslation';
import { tabData } from './constants';
import { PurchaseDataType } from './types';
import { showErrorToast, TOAST_MESSAGES, createAPIError } from '@/utils/errorHandling';
import dynamic from 'next/dynamic';

const PageTransition = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.PageTransition })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const AnimatedWrapper = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.AnimatedWrapper })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const DashboardSkeleton = dynamic(() => import('@/components/ui/LoadingSkeletons').then(mod => ({ default: mod.DashboardSkeleton })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const TabContentLoader = dynamic(() => import('@/components/ui/TabContentLoader').then(mod => ({ default: mod.TabContentLoader })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const NoTokensPortfolioEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoTokensPortfolioEmpty })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const NoTransactionsEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoTransactionsEmpty })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const NoPerksBoughtEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoPerksBoughtEmpty })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const MyProfile = dynamic(() => import('@/components/shared/my-profile').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const MyCoin = dynamic(() => import('@/components/shared/my-coin').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const PerksBought = dynamic(() => import('@/components/shared/perks-bought').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const TransactionHistory = dynamic(() => import('@/components/shared/transactionHistory').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const Tabs = dynamic(() => import('@/components/shared/tabs').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


export default function Home() {
  const { state } = useAppContext();
  const router = useRouter();
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(t('tabs.myPortfolio'));
  const [userID, setUserID] = useState(0);
  const [purchaseData, setPurchaseData] = useState<PurchaseDataType | null>(
    null
  );
  const [initialLoading, setInitialLoading] = useState(true);
  const [tabLoading, setTabLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize user data parsing to avoid repeated JSON parsing
  const userBo = useMemo(() => {
    if (!state?.userBo) return null;
    return typeof state.userBo === 'string' ? JSON.parse(state.userBo) : state.userBo;
  }, [state?.userBo]);

  // Memoize user ID to prevent unnecessary re-fetches
  const userId = useMemo(() => userBo?.id, [userBo]);

  const fetchPurchaseData = useCallback(async (userId: number, isTabChange = false) => {
    try {
      if (isTabChange) {
        setTabLoading(true);
      } else {
        setInitialLoading(true);
      }
      setError(null);

      const response = await getUserPurchasesSummary({ userId });
      setPurchaseData(response.data);
    } catch (error: any) {
      console.log('Error fetching purchase summary:', error);
      
      // Handle different types of errors with specific messages
      let errorMessage = TOAST_MESSAGES.DATA.FETCH_FAILED;
      
      if (error?.response?.status === 401) {
        errorMessage = TOAST_MESSAGES.AUTH.LOGIN_REQUIRED;
      } else if (error?.response?.status >= 500) {
        errorMessage = TOAST_MESSAGES.NETWORK.SERVER_ERROR;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
      showErrorToast(createAPIError(errorMessage, error?.response?.status));
    } finally {
      if (isTabChange) {
        setTabLoading(false);
      } else {
        setInitialLoading(false);
      }
    }
  }, []);

  useEffect(() => {
    if (!state?.userBo) {
      if (!localStorage.getItem('token')) {
        router.push(ROUTES.HOME);
        return;
      }
      return;
    }

    if (userId) {
      setUserID(userId);
      fetchPurchaseData(userId, false);
    }
  }, [userId, router, fetchPurchaseData]);

  const onTabChange = useCallback(async (tabName: string) => {
    setSelectedTab(tabName);

    // Only refresh data if we need fresh data for specific tabs
    if (tabName === 'My Transactions' || tabName === 'Perks Bought') {
      if (!purchaseData || !userId) return;
      await fetchPurchaseData(userId, true);
    }
  }, [purchaseData, userId, fetchPurchaseData]);

  // Memoize empty state calculations
  const isEmptyStates = useMemo(() => {
    if (!purchaseData) return {};
    
    return {
      'My Portfolio': purchaseData.PurchaseToken?.length === 0,
      'My Transactions': purchaseData.Transactions?.length === 0,
      'Perks Bought': purchaseData.PurchasedPerks?.length === 0,
      'My Coin': purchaseData.MyToken?.length === 0,
    };
  }, [purchaseData]);

  // Memoize tab content to prevent unnecessary re-renders
  const getActiveTabContent = useCallback((name: string) => {
    if (!purchaseData) return null;

    if (tabLoading) {
      return <TabContentLoader />;
    }

    const content = {
      'My Portfolio': (
        <AnimatePresence mode="wait">
          {isEmptyStates['My Portfolio'] ? (
            <motion.div key="empty-portfolio" {...fadeInUp}>
              <NoTokensPortfolioEmpty />
            </motion.div>
          ) : (
            <motion.div key="portfolio" {...fadeInUp}>
              <MyProfile purchasedTokens={purchaseData.PurchaseToken || []} />
            </motion.div>
          )}
        </AnimatePresence>
      ),
      'My Transactions': (
        <AnimatePresence mode="wait">
          {isEmptyStates['My Transactions'] ? (
            <motion.div key="empty-transactions" {...fadeInUp}>
              <NoTransactionsEmpty />
            </motion.div>
          ) : (
            <motion.div key="transactions" {...fadeInUp}>
              <TransactionHistory
                transactions={purchaseData.Transactions || []}
              />
            </motion.div>
          )}
        </AnimatePresence>
      ),
      'My Coin': (
        <motion.div key="my-coin" {...fadeInUp}>
          <MyCoin
            myPerks={purchaseData.MyPerks || []}
            myTokens={purchaseData.MyToken || []}
            myAirDrop={purchaseData.myAirDrop || []}
          />
        </motion.div>
      ),
      'Perks Bought': (
        <AnimatePresence mode="wait">
          {isEmptyStates['Perks Bought'] ? (
            <motion.div key="empty-perks" {...fadeInUp}>
              <NoPerksBoughtEmpty />
            </motion.div>
          ) : (
            <motion.div key="perks-bought" {...fadeInUp}>
              <PerksBought purchasedPerks={purchaseData.PurchasedPerks || []} />
            </motion.div>
          )}
        </AnimatePresence>
      ),
    };

    return content[name as keyof typeof content] || null;
  }, [purchaseData, tabLoading, isEmptyStates]);

  // Memoize current tab content
  const currentTabContent = useMemo(() => 
    getActiveTabContent(selectedTab), [getActiveTabContent, selectedTab]);

  if (initialLoading) {
    return (
      <PageTransition>
        <div className="max-w-[1902px] px-0 md:px-6 lg:px-8 mx-auto py-3 sm:py-4 md:py-6 lg:py-8">
          <DashboardSkeleton />
        </div>
      </PageTransition>
    );
  }

  if (error) {
    return (
      <PageTransition>
        <div className="max-w-[1902px] px-0 md:px-6 lg:px-8 mx-auto py-3 sm:py-4 md:py-6 lg:py-8">
          <div className="flex flex-col items-center justify-center py-16">
            <motion.div
              className="text-red-500 mb-4"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 200 }}
            >
              <Loader2 size={48} />
            </motion.div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {t('ui.errorLoadingDashboard')}
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <motion.button
              className="bg-[#FF6600] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a00] transition-colors"
              onClick={() => window.location.reload()}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t('ui.tryAgain')}
            </motion.button>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="max-w-[1902px] px-0 md:px-6 lg:px-8 mx-auto py-3 sm:py-4 md:py-6 lg:py-8">
        <motion.div
          className="mx-auto mt-1 sm:mt-2"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          <AnimatedWrapper delay={0.1}>
            <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-semibold text-center my-4 sm:my-6 md:my-8 lg:my-12">
              {t('navigation.dashboard')}
            </div>
          </AnimatedWrapper>

          <AnimatedWrapper delay={0.2}>
            {/* <LinkBox link={`fun-hi-front-end.vercel.app/?referral=${userID}`} /> */}
            <LinkBox link={`${window.location.origin}/?referral=${userID}`} />
          </AnimatedWrapper>

          <AnimatedWrapper delay={0.3}>
            <div className="mt-6 sm:mt-8 md:mt-12 lg:mt-15">
              <Tabs
                tabData={tabData}
                onTabChange={onTabChange}
                selectedTab={selectedTab}
                loading={tabLoading}
              />
            </div>
          </AnimatedWrapper>

          <motion.div
            key={selectedTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="min-h-[400px] mt-4 sm:mt-6 md:mt-9"
          >
            {currentTabContent}
          </motion.div>
        </motion.div>
      </div>
    </PageTransition>
  );
}
