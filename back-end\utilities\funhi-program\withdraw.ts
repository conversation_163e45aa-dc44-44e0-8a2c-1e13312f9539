require("ts-node/register");
import { PublicKey, TransactionInstruction } from "@solana/web3.js";
import { program } from "./setup";
import { TOKEN_PROGRAM_ID } from "@solana/spl-token";

export const withdraw = async (
  mint: PublicKey,
  authority: PublicKey,
): Promise<TransactionInstruction> => {
  const withdrawIx = await program.methods
    .withdraw()
    .accounts({
      authority,
      mint,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .instruction();
  return withdrawIx;
};
