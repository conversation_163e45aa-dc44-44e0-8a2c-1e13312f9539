{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/locales/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^\\/locales(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}