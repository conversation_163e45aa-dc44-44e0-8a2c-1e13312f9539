/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Cconstants%5C%5C%5C%5Clayout.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22100%5C%22%2C%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22IbmFont%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cchat%5C%5CChatNotificationListener.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cfooter%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5CI18nInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CrunningLine%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22AppProvider%22%2C%22UnreadChatMessagesProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CChatModalStateContext.tsx%22%2C%22ids%22%3A%5B%22ChatModalStateProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CGlobalModalContext.tsx%22%2C%22ids%22%3A%5B%22GlobalModalProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CPrivyContext.tsx%22%2C%22ids%22%3A%5B%22PrivyContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CSocketProvider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Cconstants%5C%5C%5C%5Clayout.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22100%5C%22%2C%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22IbmFont%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cchat%5C%5CChatNotificationListener.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cfooter%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5CI18nInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CrunningLine%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22AppProvider%22%2C%22UnreadChatMessagesProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CChatModalStateContext.tsx%22%2C%22ids%22%3A%5B%22ChatModalStateProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CGlobalModalContext.tsx%22%2C%22ids%22%3A%5B%22GlobalModalProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CPrivyContext.tsx%22%2C%22ids%22%3A%5B%22PrivyContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CSocketProvider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\constants\\\\layout.ts\",\"import\":\"IBM_Plex_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-sans\",\"display\":\"swap\",\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"IbmFont\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\constants\\\\\\\\layout.ts\\\",\\\"import\\\":\\\"IBM_Plex_Sans\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"display\\\":\\\"swap\\\",\\\"weight\\\":[\\\"100\\\",\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"IbmFont\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shared/chat/ChatNotificationListener.tsx */ \"(app-pages-browser)/./src/components/shared/chat/ChatNotificationListener.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shared/footer/index.tsx */ \"(app-pages-browser)/./src/components/shared/footer/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shared/header/index.tsx */ \"(app-pages-browser)/./src/components/shared/header/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shared/I18nInitializer.tsx */ \"(app-pages-browser)/./src/components/shared/I18nInitializer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/runningLine/index.tsx */ \"(app-pages-browser)/./src/components/ui/runningLine/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AppContext.tsx */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ChatModalStateContext.tsx */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/GlobalModalContext.tsx */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/PrivyContext.tsx */ \"(app-pages-browser)/./src/contexts/PrivyContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SocketProvider.tsx */ \"(app-pages-browser)/./src/contexts/SocketProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Cconstants%5C%5C%5C%5Clayout.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22100%5C%22%2C%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22IbmFont%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cchat%5C%5CChatNotificationListener.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cfooter%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cshared%5C%5CI18nInitializer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CrunningLine%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22AppProvider%22%2C%22UnreadChatMessagesProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CChatModalStateContext.tsx%22%2C%22ids%22%3A%5B%22ChatModalStateProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CGlobalModalContext.tsx%22%2C%22ids%22%3A%5B%22GlobalModalProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CPrivyContext.tsx%22%2C%22ids%22%3A%5B%22PrivyContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Ccontexts%5C%5CSocketProvider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Calrahma%5C%5CDesktop%5C%5CAhmed%20Barakat%5C%5CfunHi-project%5C%5CfunHi-project%5C%5Cfront-end%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"596f6af5c305\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTk2ZjZhZjVjMzA1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});