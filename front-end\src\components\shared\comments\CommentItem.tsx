import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';

interface CommentItemProps {
  comment: {
    id: string;
    user: {
      name: string;
      avatar: string;
    };
    content: string;
    createdAt: string;
  };
}

export const CommentItem: React.FC<CommentItemProps> = ({ comment }) => (
  <motion.div
    className="flex items-start space-x-4 p-4 bg-white rounded-lg border border-gray-100"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex-shrink-0">
      <Image
        src={comment.user.avatar}
        alt={comment.user.name}
        width={40}
        height={40}
        className="rounded-full"
      />
    </div>
    <div className="flex-1 min-w-0">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-semibold text-gray-900">
          {comment.user.name}
        </h4>
        <span className="text-xs text-gray-500">
          {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
        </span>
      </div>
      <p className="mt-1 text-sm text-gray-700">{comment.content}</p>
    </div>
  </motion.div>
); 