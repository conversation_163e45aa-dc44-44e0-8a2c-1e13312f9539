"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { usePrivy } from "@privy-io/react-auth";
import { Cluster, clusterApiUrl, Connection, PublicKey } from "@solana/web3.js";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X } from "lucide-react";
import { Toaster } from "react-hot-toast";
import axios from "axios";

import { ROUTES } from "@/constants";
import { useAppContext } from "@/contexts/AppContext";
import { useUnreadChatMessages } from "@/contexts/AppContext";

import {
  handleLogin,
  checkUserTokens,
  handleClickOutside,
  checkTokenExpiration,
} from "./AuthHandlers";
import {
  SOLANA_CONFIG,
  AUTH_CONFIG,
  FEATURE_FLAGS,
} from "@/config/environment";
import CreateCoinForm from "@/components/auth/create-coin-form";
import CreatePerkForm from "@/components/shared/create-perk";
import NotificationBell from "@/components/shared/notifications/NotificationBell";
import { useGlobalModal } from "@/contexts/GlobalModalContext";
import { useTranslation } from "@/hooks/useTranslation";
import LanguageSwitcher from "../LanguageSwitcher";
import ChatModal from "../chat/ChatModal";

// Dynamically import heavy components and modals
const SignupModal = dynamic(() => import("@/components/auth/signup-modal"), {
  loading: () => (
    <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
  ),
});

const TwoFactorAuthModal = dynamic(
  () => import("@/components/auth/two-factor-auth-modal"),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const CreateCoinPage = dynamic(() => import("../create-coin"), {
  loading: () => (
    <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
  ),
});

const Logo = dynamic(() => import("./Logo"), {
  loading: () => <div className="animate-pulse h-8 bg-gray-200 rounded"></div>,
});

const MobileMenu = dynamic(() => import("./MobileMenu"), {
  loading: () => (
    <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>
  ),
});

const NavigationItems = dynamic(() => import("./NavigationItems"), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded"></div>,
});

const NotificationButton = dynamic(() => import("./NotificationButton"), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded"></div>,
});

const ProfileDropdown = dynamic(() => import("./ProfileDropdown"), {
  loading: () => (
    <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>
  ),
});

const Header: React.FC = () => {
  // All useState hooks first
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openCreateCoinModal, setOpenCreateCoinModal] = useState(false);
  const [openCreatePerkModal, setOpenCreatePerkModal] = useState(false);
  const [openSignupModal, setOpenSignupModal] = useState(false);
 
  const [chatOpen, setChatOpen] = useState(false);
  const [chatTradeId, setChatTradeId] = useState<number | null>(null);
  const [chatReceiverId, setChatReceiverId] = useState<number | null>(null);
  const [chatRoomId, setChatRoomId] = useState<string | null>(null);
  const [chatBuyerId, setChatBuyerId] = useState<string | number | null>(null);
  const [chatSellerId, setChatSellerId] = useState<string | number | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hideCreate, setHideCreate] = useState(false);
  const [userBalance, setUserBalance] = useState(0);
  const [twoFactorUserId, setTwoFactorUserId] = useState<string | number>("");
  const [isManualLogin, setIsManualLogin] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { openModal, closeModal, isModalOpen } = useGlobalModal();


  // All useRef hooks
  const menuRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // All useRouter/usePathname hooks
  const pathname = usePathname();
  const router = useRouter();

  // All context hooks
  const { user: privyUser } = usePrivy();
  const { state, logout: contextLogout, login: contextLogin } = useAppContext();
  const { unreadChatMessages } = useUnreadChatMessages();
  const { login, logout, authenticated, user } = usePrivy();

  // All useCallback hooks
  const loginAPI = useCallback(contextLogin, [contextLogin]);
  const logoutAPI = useCallback(contextLogout, [contextLogout]);

  // Translation hook last to ensure consistent order
  const { t } = useTranslation();

  const walletAddress = privyUser?.wallet?.address.toString();
  // Function to show the 2FA authenticator modal
  const showAuthenticatorModal = useCallback((userId: string | number) => {
    setTwoFactorUserId(userId);
    handleOpenModal("two-factor", <TwoFactorAuthModal onClose={() => closeModal("two-factor")} userId={twoFactorUserId} />);
  }, []);

  // Handle manual login with Privy
  const handleManualLogin = useCallback(() => {
    setIsManualLogin(true);
    login();
  }, [login]);

  // When Privy user changes, handle login
  useEffect(() => {
    if (user) {
      handleLogin(
        user,
        loginAPI,
        logoutAPI,
        isManualLogin,
        showAuthenticatorModal,
        setIsAuthenticating
      );

      // Reset manual login flag after handling
      if (isManualLogin) {
        setTimeout(() => {
          setIsManualLogin(false);
        }, 3000);
      }
    }
  }, [user]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const referral = urlParams.get("referral");
    if (referral != undefined) {
      console.log("user referral");
      handleManualLogin();
    }
  }, []);

  useEffect(() => {
    const getBalance = async () => {
      if (!privyUser?.wallet?.address) return;

      const connection = new Connection(
        SOLANA_CONFIG.CONNECTION_URL ||
          clusterApiUrl(SOLANA_CONFIG.CLUSTER as Cluster),
        "confirmed"
      );
      const pubkey = new PublicKey(privyUser.wallet.address);
      const balanceLamports = await connection.getBalance(pubkey);
      const balanceSOL = balanceLamports / 1_000_000_000;
      setUserBalance(balanceSOL);

      console.log("SOL Balance:", balanceSOL);
    };

    getBalance();
  }, [privyUser]);

  useEffect(() => {
    checkUserTokens(setHideCreate);
  }, [user, loginAPI, logoutAPI, logout, login]);

  // Check for token expiration based on configured interval
  useEffect(() => {
    const token = localStorage.getItem("token");

    // Initial check
    checkTokenExpiration(token, logoutAPI, logout);

    // Set up periodic check based on token refresh interval
    const checkInterval = setInterval(() => {
      const currentToken = localStorage.getItem("token");
      checkTokenExpiration(currentToken, logoutAPI, logout);
    }, AUTH_CONFIG.TOKEN_REFRESH_INTERVAL * 60 * 1000); // Convert minutes to milliseconds

    return () => clearInterval(checkInterval);
  }, []);

  useEffect(() => {
    const clickHandler = (event: MouseEvent) =>
      handleClickOutside(
        event,
        menuRef,
        dropdownRef,
        setMobileMenuOpen,
        setDropdownOpen
      );

    document.addEventListener("mousedown", clickHandler);
    return () => document.removeEventListener("mousedown", clickHandler);
  }, []);

  useEffect(() => {
    setMobileMenuOpen(false);
    setDropdownOpen(false);
  }, [pathname]);

  const isActiveRoute = (route: string) => pathname === route;

  const handleNavigation = useCallback(
    (route: string) => {
      router.push(route);
      setMobileMenuOpen(false);
    },
    [router]
  );

  const handleProfileClick = useCallback(() => {
    const hasSession = localStorage.getItem("userSession");
    if (hasSession) {
      handleNavigation(ROUTES.SETTINGS);
    } else {
      // Manual login - will show 2FA if needed
      setTimeout(() => handleManualLogin(), 100);
    }
  }, [handleNavigation, handleManualLogin]);

  const handleCreateTokenClick = useCallback(() => {
    const hasSession = localStorage.getItem("userSession");
    if (hasSession) {
      handleOpenModal("create-coin", <CreateCoinForm onClose={() => closeModal("create-coin")} />);
    } else {
      // Manual login - will show 2FA if needed
      setTimeout(() => handleManualLogin(), 100);
    }
  }, [handleManualLogin]);

  const handleCreatePerkClick = useCallback(() => {
    const hasSession = localStorage.getItem("userSession");
    if (hasSession) {
      handleOpenModal("create-perk", <CreatePerkForm onClose={() => closeModal("create-perk")} />);
    } else {
      // Manual login - will show 2FA if needed
      setTimeout(() => handleManualLogin(), 100);
    }
  }, [handleManualLogin]);

  const handleLogoutClick = useCallback(() => {
    localStorage.removeItem("userSession");
    logout();
    setDropdownOpen(false);
  }, []);

  const handleSuccessfulLogin = useCallback(() => {
    localStorage.setItem("userSession", "active");
  }, []);

  const toggleDropdown = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setDropdownOpen(!dropdownOpen);
    },
    [dropdownOpen]
  );

  const handleOpenChatFromNotification = async (chatRoomId: string, receiverId: number) => {
    setChatRoomId(chatRoomId);
    setChatReceiverId(receiverId);
    setChatOpen(true);
    
    // Fetch chat room info
    try {
      const res = await axios.get(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messages/chat-room-info/${chatRoomId}`);
      const info = res.data.data;
      setChatBuyerId(info.buyerId);
      setChatSellerId(info.sellerId);
    } catch (err) {
      console.error('Failed to fetch chat room info:', err);
      
      // If the chat room doesn't exist, try to create it
      if (chatRoomId.startsWith('trade-')) {
        const tradeId = chatRoomId.replace('trade-', '');
        try {
          // Get current user info
          const userBoStr = localStorage.getItem("userBo");
          const userBo = userBoStr ? JSON.parse(userBoStr) : null;
          
          if (userBo) {
            // Try to create a chat room for this trade
            const createRes = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messages/initiate`, {
              buyerId: userBo.id,
              sellerId: receiverId,
              perkId: 1 // Default perk ID, you might want to get this from the trade
            });
            
            if (createRes.data.status === 200) {
              // Update the chat room ID with the created one
              setChatRoomId(createRes.data.chatRoomId);
              setChatBuyerId(userBo.id);
              setChatSellerId(receiverId);
            } else {
              // Fallback values
              setChatBuyerId(userBo.id);
              setChatSellerId(receiverId);
            }
          } else {
            // Fallback values
            setChatBuyerId(1);
            setChatSellerId(receiverId);
          }
        } catch (createErr) {
          console.error('Failed to create chat room:', createErr);
          // Use fallback values
          setChatBuyerId(1);
          setChatSellerId(receiverId);
        }
      } else {
        // Use fallback values for other cases
        setChatBuyerId(1);
        setChatSellerId(receiverId);
      }
    }
  };

  // Calculate total notifications: unread chat + unread system notifications
  const totalNotifications = unreadChatMessages.length + (state.unreadSystemNotificationsCount || 0);

  const handleOpenModal = (modalId: string, component: React.ReactNode) => {
    const newModalId = openModal({
      id: modalId,
      component: component,
      onClose: () => {
        closeModal(modalId);
      },
      closeOnBackdropClick: true,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
      modalClassName: 'flex flex-col items-center justify-center p-4',
      disableScroll: true
    });
    return newModalId;
  };

  return (
    <motion.header
      className="w-full bg-[#FF6600] sticky top-0 z-40"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="max-w-[1902px] w-[100%] m-auto border-b border-[#E6E6E6] px-3 lg:px-6 py-3 lg:py-4 flex items-center justify-between relative">
        <AnimatePresence>
          {isModalOpen("signup") && (
            <SignupModal
              onClose={() => closeModal("signup")}
              onSuccessfulLogin={handleSuccessfulLogin}
            />
          )}
        </AnimatePresence>

        <Logo />
        <Toaster position="top-right" reverseOrder={false} />

        <NavigationItems
          isActiveRoute={isActiveRoute}
          handleNavItemClick={(item) => {
            if (item.requiresAuth) {
              const hasSession = localStorage.getItem("userSession");
              if (hasSession) {
                handleNavigation(item.route);
              } else {
                // Manual login - will show 2FA if needed
                setTimeout(() => handleManualLogin(), 100);
              }
            } else {
              handleNavigation(item.route);
            }
          }}
          hideCreate={false}
          handleCreateTokenClick={handleCreateTokenClick}
          handleCreatePerkClick={handleCreatePerkClick}
        />

        <AnimatePresence>
          {mobileMenuOpen && (
            <MobileMenu
              menuRef={menuRef}
              isActiveRoute={isActiveRoute}
              handleNavItemClick={(item) => {
                if (item.requiresAuth) {
                  const hasSession = localStorage.getItem("userSession");
                  if (hasSession) {
                    handleNavigation(item.route);
                  } else {
                    // Manual login - will show 2FA if needed
                    setTimeout(() => handleManualLogin(), 100);
                  }
                } else {
                  handleNavigation(item.route);
                }
              }}
              hideCreate={hideCreate}
              handleCreateTokenClick={handleCreateTokenClick}
              handleCreatePerkClick={handleCreatePerkClick}
              authenticated={authenticated}
              login={handleManualLogin}
              logout={logout}
            />
          )}
        </AnimatePresence>

        <div className="flex items-center gap-1.5 lg:gap-3">
          <LanguageSwitcher />
        <div className="flex items-center gap-2 lg:gap-4">
          {FEATURE_FLAGS.ENABLE_NOTIFICATIONS && <NotificationBell />}

          <ProfileDropdown
            dropdownRef={dropdownRef as React.RefObject<HTMLDivElement>}
            dropdownOpen={dropdownOpen}
            toggleDropdown={toggleDropdown}
            authenticated={authenticated}
            login={handleManualLogin}
            logout={logout}
            handleNavigation={handleNavigation}
            userBalance={userBalance.toString()}
            walletAddress={walletAddress}
            isAuthenticating={isAuthenticating}
          />

          <motion.button
            className="lg:hidden p-1.5"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label={mobileMenuOpen ? t('common.close') : t('common.menu')}
            whileTap={{ scale: 0.95 }}
          >
            {mobileMenuOpen ? (
              <X size={20} className="text-white" />
            ) : (
              <Menu size={20} className="text-white" />
            )}
          </motion.button>

          <motion.button
            className="lg:hidden bg-[#C4C4C4] p-1 h-[36px] w-[36px] flex items-center justify-center rounded-full relative transition-all duration-300 hover:bg-gray-300"
            onClick={handleProfileClick}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="w-[34px] h-[34px] rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src="/icons/owl.png"
                alt="Profile"
                width={34}
                height={34}
                className="w-full h-full object-cover h-auto"
              />
            </div>
          </motion.button>
        </div>
        {chatOpen && chatRoomId && (
          <ChatModal
            chatRoomId={chatRoomId}
            buyerId={chatBuyerId || 1}
            sellerId={chatSellerId || chatReceiverId || 2}
            onClose={() => setChatOpen(false)}
            onRelease={() => {}}
            onRefund={() => {}}
            onReport={() => {}}
          />
        )}
      </div>
      </div>
    </motion.header>
  );
};

export default Header;
