import React, { useState } from 'react';

type TokenSettingsProps = {
  tokenSettings: {
    value: {
      website: string;
      tiktok: string;
      facebook: string;
      instagram: string;
    };
    errors: {
      website: string;
      tiktok: string;
      facebook: string;
      instagram: string;
    };
  };
  setTokenSettings: React.Dispatch<React.SetStateAction<{
    value: {
      website: string;
      tiktok: string;
      facebook: string;
      instagram: string;
    };
    errors: {
      website: string;
      tiktok: string;
      facebook: string;
      instagram: string;
    };
  }>>;
};

const TokenSettingsSection = ({ tokenSettings, setTokenSettings }: TokenSettingsProps) => {

  // Validation functions
  const validateWebsite = (url: string) => {
    if (!url) return true;
    const urlPattern = /^(https?:\/\/)?([\w\-]+\.)+[a-z]{2,}([\/\w\-._~:?#[\]@!$&'()*+,;=]*)*\/?$/;
    return urlPattern.test(url);
  };

  const validateTikTok = (url: string) => {
    if (!url) return true;
    const tikTokPattern = /^(https?:\/\/)?(www\.)?tiktok\.com\/(@[\w.]{2,24})$|^@[\w.]{2,24}$/;
    return tikTokPattern.test(url);
  };

  const validateFacebook = (url: string) => {
    if (!url) return true;
    const facebookPattern = /^(https?:\/\/)?(www\.)?facebook\.com\/[A-Za-z0-9.]{5,}$/i;
    return facebookPattern.test(url);
  };

  const validateInstagram = (url: string) => {
    if (!url) return true;
    const usernamePattern = /^[a-zA-Z0-9_](?!.*\.\.)[a-zA-Z0-9_.]{0,28}[a-zA-Z0-9_]$/;
    if (url.startsWith('@')) {
      return usernamePattern.test(url.slice(1));
    }
    const urlPattern = /^(https?:\/\/)?(www\.)?instagram\.com\/([a-zA-Z0-9_](?!.*\.\.)[a-zA-Z0-9_.]{0,28}[a-zA-Z0-9_])\/?$/;
    return urlPattern.test(url);
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTokenSettings(prev => ({ ...prev, value: { ...prev.value, [name]: value } }));
    // Clear errors when typing
    if (tokenSettings.errors[name as keyof typeof tokenSettings.errors]) {
      setTokenSettings(prev => ({ ...prev, errors: { ...prev.errors, [name]: '' } }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'website' && value && !validateWebsite(value)) {
      setTokenSettings(prev => ({ ...prev, errors: { ...prev.errors, website: 'Please enter a valid website URL (e.g., example.com)' } }));
    } else if (name === 'tiktok' && value && !validateTikTok(value)) {
      setTokenSettings(prev => ({ ...prev, errors: { ...prev.errors, tiktok: 'Please enter a valid TikTok username (e.g., @username or tiktok.com/@username)' } }));
    } else if (name === 'facebook' && value && !validateFacebook(value)) {
      setTokenSettings(prev => ({ ...prev, errors: { ...prev.errors, facebook: 'Please enter a valid Facebook URL (e.g., facebook.com/username)' } }));
    } else if (name === 'instagram' && value && !validateInstagram(value)) {
      setTokenSettings(prev => ({ ...prev, errors: { ...prev.errors, instagram: 'Please enter a valid Instagram username (e.g., @username or instagram.com/username)' } }));
    }
  };

  return (
    <div>
      <h2 className="font-['IBM_Plex_Sans'] font-semibold text-[48px] text-[#17181A] mb-6 leading-[48px] md:leading-[normal]">Optional token settings</h2>

      <div className="flex flex-wrap gap-6">
        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] text-[#17181A] mb-4">Website</label>
          <input
            name="website"
            type="text"
            value={tokenSettings.value.website}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full h-[54px] border ${tokenSettings.errors.website ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 font-['IBM_Plex_Sans']`}
            placeholder="example.com"
          />
          {tokenSettings.errors.website && <p className="mt-1 text-red-500 text-sm">{tokenSettings.errors.website}</p>}
        </div>

        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] text-[#17181A] mb-4">Tiktok</label>
          <input
            name="tiktok"
            type="text"
            value={tokenSettings.value.tiktok}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full h-[54px] border ${tokenSettings.errors.tiktok ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 font-['IBM_Plex_Sans']`}
            placeholder="@username or tiktok.com/@username"
          />
          {tokenSettings.errors.tiktok && <p className="mt-1 text-red-500 text-sm">{tokenSettings.errors.tiktok}</p>}
        </div>
      </div>

      <div className="flex flex-wrap gap-6 mt-6">
        {/* Facebook Field */}
        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] text-[#17181A] mb-4">Facebook</label>
          <input
            name="facebook"
            type="text"
            value={tokenSettings.value.facebook}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full h-[54px] border ${tokenSettings.errors.facebook ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 font-['IBM_Plex_Sans']`}
            placeholder="facebook.com/username"
          />
          {tokenSettings.errors.facebook && <p className="mt-1 text-red-500 text-sm">{tokenSettings.errors.facebook}</p>}
        </div>

        {/* Instagram Field */}
        <div className="flex-1 min-w-[290px]">
          <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] text-[#17181A] mb-4">Instagram</label>
          <input
            name="instagram"
            type="text"
            value={tokenSettings.value.instagram}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full h-[54px] border ${tokenSettings.errors.instagram ? 'border-red-500' : 'border-[#D9E1E7]'} rounded-[10px] px-4 font-['IBM_Plex_Sans']`}
            placeholder="@username or instagram.com/username"
          />
          {tokenSettings.errors.instagram && <p className="mt-1 text-red-500 text-sm">{tokenSettings.errors.instagram}</p>}
        </div>
      </div>
    </div>
  );
};

export default TokenSettingsSection;
