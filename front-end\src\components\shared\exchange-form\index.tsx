"use client";

import { motion } from "framer-motion";
import dynamic from "next/dynamic";
import React from "react";
import { useExchangeForm } from "@/hooks/useExchangeForm";

import type { ExchangeFormProps } from "./types";
import { useTranslation } from '../../../hooks/useTranslation';

const TokenInput = dynamic(() => import("./components/TokenInput").then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const SwapButton = dynamic(() => import("./components/SwapButton").then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const RefreshButton = dynamic(() => import("./components/RefreshButton").then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const ActionButton = dynamic(() => import("./components/ActionButton").then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const ExchangeForm: React.FC<ExchangeFormProps> = ({
  fromOptions = [],
  toOptions = [],
  classname = "",
}) => {
  const { t } = useTranslation();
  const {
    fromToken,
    toToken,
    fromAmount,
    toAmount,
    isLoading,
    isRefreshing,
    setFromToken,
    setToToken,
    handleFromAmountChange,
    handleToAmountChange,
    handleSwapTokens,
    handleBuyTokens,
    refreshTokenBalances,
  } = useExchangeForm({ fromOptions, toOptions });

  return (
    <motion.div
      className={`flex flex-col items-center ${classname}`}
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: 0.1,
            delayChildren: 0.1,
          },
        },
      }}
      initial="hidden"
      animate="visible"
    >
      {fromOptions.length === 0 && (
        <motion.p
          className="text-red-500 mb-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          {t('exchangeForm.noTokensFrom')}
        </motion.p>
      )}
      {toOptions.length === 0 && (
        <motion.p
          className="text-red-500 mb-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          {t('exchangeForm.noTokensTo')}
        </motion.p>
      )}

      {fromToken && (
        <TokenInput
          label={t('exchangeForm.from')}
          token={fromToken}
          amount={fromAmount}
          setAmount={handleFromAmountChange}
          onTokenChange={setFromToken}
          tokenOptions={fromOptions}
          loading={isLoading || isRefreshing}
        />
      )}

      <SwapButton onClick={handleSwapTokens} disabled={isLoading} isLoading={isLoading} />

      {toToken && (
        <TokenInput
          label={t('exchangeForm.to')}
          token={toToken}
          amount={toAmount}
          setAmount={handleToAmountChange}
          onTokenChange={setToToken}
          tokenOptions={toOptions}
          badge="Fun hi"
          showSymbolInAvailable={true}
          loading={isLoading || isRefreshing}
        />
      )}

      <motion.div
        className="mt-9 w-full space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex justify-between items-center">
          <RefreshButton
            onClick={refreshTokenBalances}
            isRefreshing={isRefreshing}
            isLoading={isLoading}
          />
        </div>

        <ActionButton
          onClick={handleBuyTokens}
          disabled={
            isLoading || !fromToken || !toToken || parseFloat(toAmount) === 0
          }
          isLoading={isLoading}
        />
      </motion.div>
    </motion.div>
  );
};

export default ExchangeForm;
