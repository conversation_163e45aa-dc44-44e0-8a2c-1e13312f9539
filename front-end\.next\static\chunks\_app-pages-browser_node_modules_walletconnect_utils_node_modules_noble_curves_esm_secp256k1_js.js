"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_utils_node_modules_noble_curves_esm_secp256k1_js"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC91dGlscy9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEM7QUFDcUI7QUFDUDtBQUN4RDtBQUNPO0FBQ1A7QUFDQTtBQUNBLGdDQUFnQyx3REFBSSxZQUFZLGdFQUFXO0FBQzNELG1CQUFtQjtBQUNuQjtBQUNBO0FBQ087QUFDUCw2QkFBNkIscUVBQVcsR0FBRywrQkFBK0I7QUFDMUUsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXEB3YWxsZXRjb25uZWN0XFx1dGlsc1xcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGN1cnZlc1xcZXNtXFxfc2hvcnR3X3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0aWVzIGZvciBzaG9ydCB3ZWllcnN0cmFzcyBjdXJ2ZXMsIGNvbWJpbmVkIHdpdGggbm9ibGUtaGFzaGVzLlxuICogQG1vZHVsZVxuICovXG4vKiEgbm9ibGUtY3VydmVzIC0gTUlUIExpY2Vuc2UgKGMpIDIwMjIgUGF1bCBNaWxsZXIgKHBhdWxtaWxsci5jb20pICovXG5pbXBvcnQgeyBobWFjIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy9obWFjJztcbmltcG9ydCB7IGNvbmNhdEJ5dGVzLCByYW5kb21CeXRlcyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvdXRpbHMnO1xuaW1wb3J0IHsgd2VpZXJzdHJhc3MgfSBmcm9tICcuL2Fic3RyYWN0L3dlaWVyc3RyYXNzLmpzJztcbi8qKiBjb25uZWN0cyBub2JsZS1jdXJ2ZXMgdG8gbm9ibGUtaGFzaGVzICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0SGFzaChoYXNoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaGFzaCxcbiAgICAgICAgaG1hYzogKGtleSwgLi4ubXNncykgPT4gaG1hYyhoYXNoLCBrZXksIGNvbmNhdEJ5dGVzKC4uLm1zZ3MpKSxcbiAgICAgICAgcmFuZG9tQnl0ZXMsXG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDdXJ2ZShjdXJ2ZURlZiwgZGVmSGFzaCkge1xuICAgIGNvbnN0IGNyZWF0ZSA9IChoYXNoKSA9PiB3ZWllcnN0cmFzcyh7IC4uLmN1cnZlRGVmLCAuLi5nZXRIYXNoKGhhc2gpIH0pO1xuICAgIHJldHVybiB7IC4uLmNyZWF0ZShkZWZIYXNoKSwgY3JlYXRlIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1fc2hvcnR3X3V0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: () => (/* binding */ pippenger),\n/* harmony export */   precomputeMSMUnsafe: () => (/* binding */ precomputeMSMUnsafe),\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const COEFF = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xNum, xDen, yNum, yDen] = COEFF.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        x = field.div(xNum, xDen); // xNum / xDen\n        y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n        return { x: x, y: y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, def) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    return {\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options });\n            const u0 = Point.fromAffine(mapToCurve(u[0]));\n            const u1 = Point.fromAffine(mapToCurve(u[1]));\n            const P = u0.add(u1).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options });\n            const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('mapToCurve: expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('mapToCurve: expected array of bigints');\n            const P = Point.fromAffine(mapToCurve(scalars)).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpLegendre: () => (/* binding */ FpLegendre),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nfunction FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nfunction FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aInRange: () => (/* binding */ aInRange),\n/* harmony export */   abool: () => (/* binding */ abool),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   memoized: () => (/* binding */ memoized),\n/* harmony export */   notImplemented: () => (/* binding */ notImplemented),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   DERErr: () => (/* binding */ DERErr),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = _utils_js__WEBPACK_IMPORTED_MODULE_0__;\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded((len.length / 2) | 128) : '';\n            const t = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return b2n(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes(data);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(key))\n                key = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        if (!Fp.eql(left, right))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(tail);\n                if (!_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    const numToNByteStr = (num) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(item);\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\"\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = _utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 short weierstrass curve and ECDSA signatures over it.\n *\n * @example\n * import { secp256k1 } from '@noble/curves/secp256k1';\n *\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7),\n    Fp: Fpk1, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\n/** secp256k1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\n/** secp256k1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        const { view, buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = SHA256_IV[0] | 0;\n        this.B = SHA256_IV[1] | 0;\n        this.C = SHA256_IV[2] | 0;\n        this.D = SHA256_IV[3] | 0;\n        this.E = SHA256_IV[4] | 0;\n        this.F = SHA256_IV[5] | 0;\n        this.G = SHA256_IV[6] | 0;\n        this.H = SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/** SHA2-256 hash function */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA256());\n/** SHA2-224 hash function */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA224());\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js\n"));

/***/ })

}]);