"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePrivy } from "@privy-io/react-auth";
import { useAppContext } from "@/contexts/AppContext";
import { 
  getModeratorDashboard, 
  getDisputes, 
  assignDispute, 
  resolveDispute 
} from "@/axios/requests";

interface DisputeStats {
  open: number;
  assigned: number;
  resolved_buyer: number;
  resolved_seller: number;
  resolved_split: number;
}

interface ModeratorInfo {
  id: number;
  username: string;
  reputationScore: number;
  totalDisputesHandled: number;
  totalDisputesResolved: number;
  currentActiveDisputes: number;
  maxConcurrentDisputes: number;
  averageResolutionHours: number;
}

interface Dispute {
  id: number;
  tradeId: number;
  initiatorRole: 'buyer' | 'seller';
  reason: string;
  status: string;
  createdAt: string;
  trade: {
    id: number;
    price: number;
    user: { username: string; privywallet: string };
    perkDetails: {
      name: string;
      user: { username: string; privywallet: string };
    };
  };
  initiator: { username: string };
  moderator?: { username: string };
}

interface DashboardData {
  moderator: ModeratorInfo;
  disputeStats: DisputeStats;
  activeDisputes: Dispute[];
  recentResolutions: Dispute[];
}

export default function ModeratorDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [allDisputes, setAllDisputes] = useState<Dispute[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'active' | 'all' | 'resolved'>('overview');
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null);
  const [resolutionModal, setResolutionModal] = useState(false);
  const [buyerPercentage, setBuyerPercentage] = useState(50);
  const [moderatorNotes, setModeratorNotes] = useState('');

  const { user } = usePrivy();
  const { state } = useAppContext();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.push('/');
      return;
    }
    loadDashboardData();
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [dashboardResponse, disputesResponse] = await Promise.all([
        getModeratorDashboard(),
        getDisputes({ page: 1, pageSize: 50 })
      ]);

      if (dashboardResponse.status === 200) {
        setDashboardData(dashboardResponse.data);
      }

      if (disputesResponse.status === 200) {
        setAllDisputes(disputesResponse.data.disputes);
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignDispute = async (disputeId: number) => {
    try {
      const response = await assignDispute(disputeId);
      if (response.status === 200) {
        await loadDashboardData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to assign dispute:', error);
    }
  };

  const handleResolveDispute = async () => {
    if (!selectedDispute) return;

    try {
      const sellerPercentage = 100 - buyerPercentage;
      let resolution = 'resolved_split';
      
      if (buyerPercentage === 100) resolution = 'resolved_buyer';
      else if (buyerPercentage === 0) resolution = 'resolved_seller';

      const response = await resolveDispute(selectedDispute.id, {
        buyerPercentage,
        sellerPercentage,
        moderatorNotes,
        resolution
      });

      if (response.status === 200) {
        setResolutionModal(false);
        setSelectedDispute(null);
        setBuyerPercentage(50);
        setModeratorNotes('');
        await loadDashboardData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to resolve dispute:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-yellow-100 text-yellow-800';
      case 'assigned': return 'bg-blue-100 text-blue-800';
      case 'resolved_buyer': return 'bg-green-100 text-green-800';
      case 'resolved_seller': return 'bg-purple-100 text-purple-800';
      case 'resolved_split': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have moderator privileges.</p>
        </div>
      </div>
    );
  }

  const { moderator, disputeStats, activeDisputes, recentResolutions } = dashboardData;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Moderator Dashboard</h1>
          <p className="text-gray-600">Welcome back, {moderator.username}</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Reputation Score</h3>
            <p className="text-2xl font-bold text-blue-600">{moderator.reputationScore}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Active Disputes</h3>
            <p className="text-2xl font-bold text-yellow-600">
              {moderator.currentActiveDisputes}/{moderator.maxConcurrentDisputes}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Resolved</h3>
            <p className="text-2xl font-bold text-green-600">{moderator.totalDisputesResolved}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Avg Resolution Time</h3>
            <p className="text-2xl font-bold text-purple-600">{moderator.averageResolutionHours}h</p>
          </div>
        </div>

        {/* Dispute Stats */}
        <div className="bg-white rounded-lg shadow mb-8 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">System Dispute Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{disputeStats.open || 0}</p>
              <p className="text-sm text-gray-500">Open</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{disputeStats.assigned || 0}</p>
              <p className="text-sm text-gray-500">Assigned</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{disputeStats.resolved_buyer || 0}</p>
              <p className="text-sm text-gray-500">Buyer Won</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{disputeStats.resolved_seller || 0}</p>
              <p className="text-sm text-gray-500">Seller Won</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-600">{disputeStats.resolved_split || 0}</p>
              <p className="text-sm text-gray-500">Split</p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { key: 'overview', label: 'Overview' },
                { key: 'active', label: `My Active (${activeDisputes.length})` },
                { key: 'all', label: 'All Open Disputes' },
                { key: 'resolved', label: 'Recent Resolutions' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    selectedTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {selectedTab === 'overview' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                      onClick={() => setSelectedTab('all')}
                      className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
                    >
                      <h4 className="font-medium text-gray-900">Review Open Disputes</h4>
                      <p className="text-sm text-gray-500">Assign yourself to new disputes</p>
                    </button>
                    <button
                      onClick={() => setSelectedTab('active')}
                      className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
                    >
                      <h4 className="font-medium text-gray-900">Resolve Active Cases</h4>
                      <p className="text-sm text-gray-500">Work on your assigned disputes</p>
                    </button>
                    <button
                      onClick={() => setSelectedTab('resolved')}
                      className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
                    >
                      <h4 className="font-medium text-gray-900">Review History</h4>
                      <p className="text-sm text-gray-500">See your recent resolutions</p>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {selectedTab === 'active' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">My Active Disputes</h3>
                {activeDisputes.length === 0 ? (
                  <p className="text-gray-500">No active disputes assigned to you.</p>
                ) : (
                  <div className="space-y-4">
                    {activeDisputes.map((dispute) => (
                      <div key={dispute.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {dispute.trade.perkDetails.name}
                            </h4>
                            <p className="text-sm text-gray-500">
                              Trade #{dispute.tradeId} • ${dispute.trade.price}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(dispute.status)}`}>
                            {dispute.status}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-gray-500">Buyer</p>
                            <p className="text-sm font-medium">{dispute.trade.user.username}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Seller</p>
                            <p className="text-sm font-medium">{dispute.trade.perkDetails.user.username}</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mb-3">
                          <span className="font-medium">Dispute reason:</span> {dispute.reason}
                        </p>
                        <div className="flex justify-between items-center">
                          <p className="text-xs text-gray-500">
                            Initiated by {dispute.initiatorRole} • {formatDate(dispute.createdAt)}
                          </p>
                          <button
                            onClick={() => {
                              setSelectedDispute(dispute);
                              setResolutionModal(true);
                            }}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                          >
                            Resolve
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'all' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">All Open Disputes</h3>
                {allDisputes.filter(d => d.status === 'open').length === 0 ? (
                  <p className="text-gray-500">No open disputes available.</p>
                ) : (
                  <div className="space-y-4">
                    {allDisputes.filter(d => d.status === 'open').map((dispute) => (
                      <div key={dispute.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {dispute.trade.perkDetails.name}
                            </h4>
                            <p className="text-sm text-gray-500">
                              Trade #{dispute.tradeId} • ${dispute.trade.price}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(dispute.status)}`}>
                            {dispute.status}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-gray-500">Buyer</p>
                            <p className="text-sm font-medium">{dispute.trade.user.username}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Seller</p>
                            <p className="text-sm font-medium">{dispute.trade.perkDetails.user.username}</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mb-3">
                          <span className="font-medium">Dispute reason:</span> {dispute.reason}
                        </p>
                        <div className="flex justify-between items-center">
                          <p className="text-xs text-gray-500">
                            Initiated by {dispute.initiatorRole} • {formatDate(dispute.createdAt)}
                          </p>
                          <button
                            onClick={() => handleAssignDispute(dispute.id)}
                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                            disabled={moderator.currentActiveDisputes >= moderator.maxConcurrentDisputes}
                          >
                            {moderator.currentActiveDisputes >= moderator.maxConcurrentDisputes
                              ? 'At Capacity'
                              : 'Assign to Me'
                            }
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'resolved' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Resolutions</h3>
                {recentResolutions.length === 0 ? (
                  <p className="text-gray-500">No recent resolutions.</p>
                ) : (
                  <div className="space-y-4">
                    {recentResolutions.map((dispute) => (
                      <div key={dispute.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {dispute.trade.perkDetails.name}
                            </h4>
                            <p className="text-sm text-gray-500">
                              Trade #{dispute.tradeId} • ${dispute.trade.price}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(dispute.status)}`}>
                            {dispute.status.replace('resolved_', '').replace('_', ' ')}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-gray-500">Buyer</p>
                            <p className="text-sm font-medium">{dispute.trade.user.username}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Seller</p>
                            <p className="text-sm font-medium">{dispute.trade.perkDetails.user.username}</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mb-3">
                          <span className="font-medium">Original dispute:</span> {dispute.reason}
                        </p>
                        <p className="text-xs text-gray-500">
                          Resolved • {formatDate(dispute.createdAt)}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Resolution Modal */}
        {resolutionModal && selectedDispute && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Resolve Dispute</h2>

              <div className="mb-4">
                <h3 className="font-medium text-gray-900">{selectedDispute.trade.perkDetails.name}</h3>
                <p className="text-sm text-gray-500">Trade #{selectedDispute.tradeId} • ${selectedDispute.trade.price}</p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fund Distribution
                </label>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Buyer gets:</span>
                    <span className="font-medium">{buyerPercentage}%</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={buyerPercentage}
                    onChange={(e) => setBuyerPercentage(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Seller gets:</span>
                    <span className="font-medium">{100 - buyerPercentage}%</span>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Resolution Notes
                </label>
                <textarea
                  value={moderatorNotes}
                  onChange={(e) => setModeratorNotes(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md"
                  rows={3}
                  placeholder="Explain your decision..."
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setResolutionModal(false);
                    setSelectedDispute(null);
                    setBuyerPercentage(50);
                    setModeratorNotes('');
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleResolveDispute}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Resolve Dispute
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
