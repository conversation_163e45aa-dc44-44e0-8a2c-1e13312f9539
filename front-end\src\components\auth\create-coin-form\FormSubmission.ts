import { addToken } from '../../../axios/requests';
import { FormData } from './FormValidation';

interface SubmissionResult {
  success: boolean;
  message: string;
}

export const submitForm = async (
  formData: FormData,
  walletAddress: string,
  userId: number,
  imageUrl: string
): Promise<SubmissionResult> => {
  try {
    // Get userId from localStorage if available
    let submissionUserId = userId;
    if (typeof window !== 'undefined' && !submissionUserId) {
      const storedUserBo = localStorage.getItem('userBo');
      if (storedUserBo) {
        try {
          const parsedUserBo = JSON.parse(storedUserBo);
          submissionUserId = parsedUserBo.id;
        } catch (error) {
          console.error('Error parsing stored userBo:', error);
        }
      }
    }

    const newToken = {
      name: formData.name,
      ticker: formData.ticker,
      description: formData.description,
      tokenAddress: '',
      image: imageUrl,
      telegram: formData.telegram,
      website: formData.website,
      twitter: formData.twitter,
      category: formData.category,
      userId: submissionUserId,
      userWallet: walletAddress,
    };

    console.log(newToken);

    const response = await addToken(newToken);

    if (response.status === 201) {
      return {
        success: true,
        message: 'Token created successfully!',
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Something went wrong',
      };
    }
  } catch (error) {
    console.error('Error creating token:', error);
    return {
      success: false,
      message: 'Failed to create token. Please try again.',
    };
  }
};
