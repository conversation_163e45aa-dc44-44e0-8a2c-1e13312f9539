//import * as anchor from "@coral-xyz/anchor";
//import { Program } from "@coral-xyz/anchor";
//import { Funhi } from "../target/types/funhi";
//import {
//  Keypair,
//  PublicKey,
//  SystemProgram,
//  LAMPORTS_PER_SOL,
//} from "@solana/web3.js";
//import { expect } from "chai";
//import {
//  ASSOCIATED_TOKEN_PROGRAM_ID,
//  getAssociatedTokenAddressSync,
//  getAccount,
//  TOKEN_PROGRAM_ID,
//} from "@solana/spl-token";
//
//// Helper functions (no changes needed here)
//async function getTokenBalance(
//  provider: anchor.Provider,
//  ata: PublicKey
//): Promise<number> {
//  try {
//    const account = await getAccount(provider.connection, ata);
//    return Number(account.amount);
//  } catch (e) {
//    return 0;
//  }
//}
//
//async function getSolBalance(
//  provider: anchor.Provider,
//  address: PublicKey
//): Promise<number> {
//  try {
//    const balance = await provider.connection.getBalance(address);
//    return Number(balance / 1e9);
//  } catch (e) {
//    return 0;
//  }
//}
//
//describe("funhi", () => {
//  const provider = anchor.AnchorProvider.env();
//  anchor.setProvider(provider);
//
//  const program = anchor.workspace.funhi as Program<Funhi>;
//  const authority = (provider.wallet as anchor.Wallet).payer;
//
//  const feeRecipient = Keypair.generate();
//  const creator = Keypair.generate();
//  const buyer = Keypair.generate();
//
//  const initialVirtualTokenReserves = new anchor.BN(1_073_000_191);
//  const initialVirtualSolReserves = new anchor.BN(30 * LAMPORTS_PER_SOL);
//  const tokenTotalSupply = new anchor.BN(1_000_000_000 * 1e6);
//  const creatorVaultAmount = new anchor.BN(10_000_000 * 1e6);
//  const initialRealTokenReserves = tokenTotalSupply.sub(creatorVaultAmount);
//
//  const graduationThreshold = new anchor.BN(85 * LAMPORTS_PER_SOL);
//  const tradingFeeBasisPoints = new anchor.BN(100);
//  const firstBuyFeeSol = new anchor.BN(0.02 * LAMPORTS_PER_SOL);
//
//  let global: PublicKey;
//  const mint = Keypair.generate();
//  let bondingCurve: PublicKey;
//  let creatorVault: PublicKey;
//  let bondingCurveAta: PublicKey;
//  let creatorVaultAta: PublicKey;
//
//  before(async () => {
//    const tx = new anchor.web3.Transaction().add(
//      SystemProgram.transfer({
//        fromPubkey: authority.publicKey,
//        toPubkey: buyer.publicKey,
//        lamports: 2 * LAMPORTS_PER_SOL,
//      })
//    );
//    await provider.sendAndConfirm(tx);
//
//    [global] = PublicKey.findProgramAddressSync(
//      [Buffer.from("global")],
//      program.programId
//    );
//    // Use mint.publicKey for derivations
//    [bondingCurve] = PublicKey.findProgramAddressSync(
//      [Buffer.from("bonding_curve"), mint.publicKey.toBuffer()],
//      program.programId
//    );
//    [creatorVault] = PublicKey.findProgramAddressSync(
//      [
//        Buffer.from("creator_vault"),
//        creator.publicKey.toBuffer(),
//        mint.publicKey.toBuffer(),
//      ],
//      program.programId
//    );
//    bondingCurveAta = getAssociatedTokenAddressSync(
//      mint.publicKey,
//      bondingCurve,
//      true
//    );
//    creatorVaultAta = getAssociatedTokenAddressSync(
//      mint.publicKey,
//      creatorVault,
//      true
//    );
//  });
//
//  it("Is initialized!", async () => {
//    await program.methods
//      .initialize(
//        initialVirtualTokenReserves,
//        initialVirtualSolReserves,
//        initialRealTokenReserves,
//        tokenTotalSupply,
//        tradingFeeBasisPoints,
//        feeRecipient.publicKey,
//        firstBuyFeeSol,
//        graduationThreshold
//      )
//      .rpc();
//
//    const globalData = await program.account.global.fetch(global);
//    expect(globalData.authority.toBase58()).to.eq(
//      authority.publicKey.toBase58()
//    );
//  });
//
//  it("Creates a new token, bonding curve, and creator vault", async () => {
//    const tokenName = "Test";
//    const tokenSymbol = "TEST";
//    const tokenUri = "https://test.com/token.json";
//
//    await program.methods
//      .create(tokenName, tokenSymbol, tokenUri)
//      .accounts({
//        signer: authority.publicKey,
//        creator: creator.publicKey,
//        feeRecipient: feeRecipient.publicKey,
//        global,
//        mint: mint.publicKey,
//        bondingCurve,
//        bondingCurveAta,
//        creatorVault,
//        creatorVaultAta,
//        tokenProgram: TOKEN_PROGRAM_ID,
//        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
//        tokenMetadataProgram: new PublicKey(
//          "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
//        ),
//      })
//      .signers([mint])
//      .rpc();
//
//    const bondingCurveData = await program.account.bondingCurve.fetch(
//      bondingCurve
//    );
//    expect(bondingCurveData.mint.toBase58()).to.eq(mint.publicKey.toBase58());
//  });
//
//  it("Allows a user to buy tokens", async () => {
//    const solAmountToBuy = new anchor.BN(1 * LAMPORTS_PER_SOL);
//    const minTokenOutput = new anchor.BN(0);
//
//    const buyerAta = getAssociatedTokenAddressSync(
//      mint.publicKey,
//      buyer.publicKey
//    );
//
//    await program.methods
//      .buy(solAmountToBuy, minTokenOutput)
//      .accounts({
//        signer: buyer.publicKey,
//        bondingCurve,
//        bondingCurveAta,
//        userAta: buyerAta,
//        mint: mint.publicKey,
//        creator: creator.publicKey,
//        tokenProgram: TOKEN_PROGRAM_ID,
//        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
//        global,
//        feeRecipient: feeRecipient.publicKey,
//      })
//      .signers([buyer])
//      .rpc();
//
//    const buyerTokenBalance = await getTokenBalance(provider, buyerAta);
//    expect(buyerTokenBalance).to.be.gt(0);
//  });
//
//  it("Allows a user to sell tokens", async () => {
//    const buyerAta = getAssociatedTokenAddressSync(
//      mint.publicKey,
//      buyer.publicKey
//    );
//    const initialTokenBalance = await getTokenBalance(provider, buyerAta);
//    const tokenAmountToSell = new anchor.BN(
//      Math.floor(initialTokenBalance / 2)
//    );
//    const minSolOutput = new anchor.BN(0);
//
//    const initialSolBalance = await provider.connection.getBalance(
//      buyer.publicKey
//    );
//
//    await program.methods
//      .sell(tokenAmountToSell, minSolOutput)
//      .accounts({
//        signer: buyer.publicKey,
//        bondingCurve,
//        bondingCurveAta,
//        userAta: buyerAta,
//        mint: mint.publicKey,
//        creator: creator.publicKey,
//        global,
//        feeRecipient: feeRecipient.publicKey,
//        tokenProgram: TOKEN_PROGRAM_ID,
//      })
//      .signers([buyer])
//      .rpc();
//
//    const finalTokenBalance = await getTokenBalance(provider, buyerAta);
//    const finalSolBalance = await provider.connection.getBalance(
//      buyer.publicKey
//    );
//
//    expect(finalTokenBalance).to.be.lt(initialTokenBalance);
//    expect(finalSolBalance).to.be.gt(
//      initialSolBalance - LAMPORTS_PER_SOL * 0.01
//    );
//  });
//});
