import { FLOAT } from "html2canvas/dist/types/css/property-descriptors/float";
import axios from "axios";
import {
  AppError,
  NetworkError,
  AuthenticationError,
  ValidationError,
} from "@/utils/errorHandling";
import { API_CONFIG } from "@/config/environment";

export const loginUser = async (loginData: any) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/login`,
      loginData
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 401) {
        throw new AuthenticationError(
          error.response.data.message || "Invalid credentials"
        );
      }
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid login data"
        );
      }
      throw new AppError(
        error.response.data.message || "Login failed",
        "LOGIN_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const loginPrivyUser = async (loginData: any) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/loginPrivy`,
      loginData
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 401) {
        throw new AuthenticationError(
          error.response.data.message || "Invalid credentials"
        );
      }
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid login data"
        );
      }
      throw new AppError(
        error.response.data.message || "Privy login failed",
        "PRIVY_LOGIN_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const signupUser = async (signupData: any) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/addUser`,
      signupData
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid signup data"
        );
      }
      if (error.response.status === 409) {
        throw new AppError(
          error.response.data.message || "User already exists",
          "USER_EXISTS",
          409
        );
      }
      throw new AppError(
        error.response.data.message || "Signup failed",
        "SIGNUP_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const updateProfile = async (updateData: any) => {
  try {
    const token = localStorage.getItem("token");

    const response = await axios.put(
      `${API_CONFIG.BASE_URL}/users/updateUser`,
      updateData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error: any) {
    console.error("Failed to update profile:", error);
    if (error.response) {
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid website URL"
        );
      }
      if (error.response.status === 404) {
        throw new AppError(
          error.response.data.message || "Profile not found",
          "PROFILE_NOT_FOUND",
          404
        );
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "PROFILE_UPDATE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const fetchTokens = async ({
  search = "",
  sort = "desc",
  order = "",
  isVerified = "",
  page = 0,
  pageSize = 10,
}) => {
  try {
    if (sort == "newest") {
      sort = "createdAt";
    } else {
    }
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/tokens/getAll`,
      {
        params: {
          search,
          sort,
          order,
          isVerified,
          page,
          pageSize,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Failed to fetch tokens:", error);
    throw error;
  }
};

export const addToken = async (tokenData: any) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/addToken`,
      tokenData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Failed to add token:", error);
    throw error;
  }
};

export async function fetchTokenDetails(tokenId: string) {
  try {
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/tokens/${tokenId}`
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to fetch token",
        "TOKEN_FETCH_ERROR",
        400
      );
    }
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 404) {
        throw new AppError("Token not found", "TOKEN_NOT_FOUND", 404);
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "TOKEN_FETCH_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
}

export const fetchPerks = async ({
  search = "",
  sort = "desc",
  order = "",
  isVerified = "",
  page = 0,
  pageSize = 10,
}) => {
  try {
    sort = "createdAt";
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/perks/getAll`,
      {
        params: {
          search,
          sort,
          order,
          isVerified,
          page,
          pageSize,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Failed to fetch tokens:", error);
    throw error;
  }
};

export const addPerk = async (perkData: any) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/addPerk`,
      perkData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to add perk:", error);
    if (error.response) {
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid price format"
        );
      }
      if (error.response.status === 401) {
        throw new AuthenticationError(
          error.response.data.message || "Authorization required"
        );
      }
      if (error.response.status === 500) {
        throw new AppError(
          error.response.data.message || "Server error",
          "SERVER_ERROR",
          500
        );
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "PERK_CREATE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export async function fetchPerkDetails(id: string, userId: any) {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/get/${id}`,
      { userId: userId }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Failed to fetch perk");
    }
  } catch (error: any) {
    console.error("Error fetching perk:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

// export async function buyPerk(data: {
//   userId: string;
//   perkId: string;
//   amount: number;
//   price: number;
// }) {
//   try {
//     const token = localStorage.getItem("token");
//     const response = await axios.post(
//       `${API_CONFIG.BASE_URL}/perks/buy`,
//       data,
//       {
//         headers: {
//           Authorization: `Bearer ${token}`,
//         },
//       }
//     );

//     if (response.data.status === 200 || response.data.status === 201) {
//       return response.data;
//     } else {
//       throw new AppError(
//         response.data.message || "Failed to buy perk",
//         "PERK_PURCHASE_ERROR",
//         response.data.status || 500
//       );
//     }
//   } catch (error: any) {
//     console.error("Error buying perk:", error);
//     if (error.response) {
//       if (error.response.status === 400) {
//         throw new AppError(
//           error.response.data.message || "Insufficient funds",
//           "INSUFFICIENT_FUNDS",
//           400
//         );
//       }
//       if (error.response.status === 404) {
//         throw new AppError(
//           error.response.data.message || "Perk out of stock",
//           "OUT_OF_STOCK",
//           404
//         );
//       }
//       throw new AppError(
//         error.response.data?.message || error.message,
//         "PERK_PURCHASE_ERROR",
//         error.response.status
//       );
//     } else if (error.request) {
//       throw new NetworkError("No response from server");
//     } else {
//       throw new AppError(
//         error.message || "An unexpected error occurred",
//         "UNKNOWN_ERROR",
//         500
//       );
//     }
//   }
// }

export async function addAirDrop(data: any) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/createAirDrop`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 201) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Failed to buy perk");
    }
  } catch (error: any) {
    console.error("Error buying perk:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function addBurnLogs(data: any) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/BurnLogs`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 201 || response.data.status === 202) {
      return response.data; // Successful purchase response
    } else {
      throw new Error(response.data.message || "Failed to buy perk");
    }
  } catch (error: any) {
    console.error("Error buying perk:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function getUserLogs(data: any, langOverride?: string) {
  try {
    // Get language from context/hook or use override
    let lang = langOverride;
    if (!lang && typeof window !== 'undefined') {
      try {
        lang = localStorage.getItem('i18nextLng') || 'en';
      } catch {}
    }
    lang = lang || 'en';
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/getUserLogs`,
      data
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Failed to get Logs");
    }
  } catch (error: any) {
    console.error("Error getting user logs:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function claimAirDrop(data: any) {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/claimAirDrop`,
      data
    );

    if (response.data.status === 201 || response.data.status === 202) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to claim airdrop",
        "AIRDROP_CLAIM_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error claiming airdrop:", error);
    if (error.response) {
      if (error.response.status === 400) {
        throw new AppError(
          error.response.data.message || "Airdrop already claimed",
          "AIRDROP_ALREADY_CLAIMED",
          400
        );
      }
      if (error.response.status === 410) {
        throw new AppError(
          error.response.data.message || "Airdrop expired",
          "AIRDROP_EXPIRED",
          410
        );
      }
      if (error.response.status === 404) {
        throw new AppError(
          error.response.data.message || "Invalid airdrop code",
          "INVALID_AIRDROP_CODE",
          404
        );
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "AIRDROP_CLAIM_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
}

export async function buyToken(data: {
  userId: number;
  tokenId: any;
  amount: FLOAT;
  price: number;
  dollorPrice: number;
  hasH: string;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/buy`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200 || response.data.status === 201) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Failed to buy token");
    }
  } catch (error: any) {
    console.error("Error buying token:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function getUserPurchasesSummary(data: { userId: number }) {
  try {
    const token = localStorage.getItem("token");

    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/getUserPurchasesSummary`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to fetch purchases summary",
        "PURCHASES_FETCH_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error fetching purchases summary:", error);
    if (error.response) {
      if (error.response.status === 404) {
        throw new AppError(
          error.response.data.message || "User not found",
          "USER_NOT_FOUND",
          404
        );
      }
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid user ID"
        );
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "PURCHASES_FETCH_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
}

export async function postTokenComment(data: any) {
  try {
    const token = localStorage.getItem("token");
    if (!token) {
      throw new AuthenticationError("Authentication required");
    }

    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/tokens/postComments`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 201) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to post comment",
        "COMMENT_POST_ERROR",
        400
      );
    }
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 401) {
        throw new AuthenticationError("Authentication required");
      }
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid comment data"
        );
      }
      throw new AppError(
        error.response.data?.message || error.message,
        "COMMENT_POST_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
}

export async function postPerkReviews(data: any) {
  try {
    const token = localStorage.getItem("token");

    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/postReviews`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 201) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Failed to post comment");
    }
  } catch (error: any) {
    console.error("Error posting comment:", error);
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function getUserTrades(userId: string) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/trades`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.data;
  } catch (error: any) {
    if (error.response && error.response.status === 404) {
      // No trades found for this user
      return [];
    }
    console.error("Failed to fetch user trades:", error);
    throw error;
  }
}

export async function getMessagesByTrade(tradeId: string) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/messages/${tradeId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.data;
  } catch (error: any) {
    console.error("Failed to fetch messages for trade:", error);
    throw error;
  }
}

export async function getUserById(userId: string | number) {
  try {
    const token = localStorage.getItem("token");
    // The backend expects both id and name, but name can be any string
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/info`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.data;
  } catch (error: any) {
    console.error("Failed to fetch user info:", error);
    throw error;
  }
}

export async function initiateChatRoom({ buyerId, sellerId, perkId }: { buyerId: string | number, sellerId: string | number, perkId: string | number }) {
  const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messages/initiate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ buyerId, sellerId, perkId }),
  });
  return response.json();
}

export async function initiateDirectChatRoom({ userAId, userBId }: { userAId: string | number, userBId: string | number }) {
  const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messages/initiateDirect`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ userAId, userBId }),
  });
  return response.json();
}

export async function fetchFollowers(userId: string | number) {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/followers`
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch followers:", error);
    throw error;
  }
}

export async function fetchFollowing(userId: string | number) {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/following`
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch following:", error);
    throw error;
  }
}

export async function followUser(userId: string | number, followerId: string | number) {
  try {
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/follow`,
      { followerId }
    );
    return response.data;
  } catch (error) {
    console.error("Failed to follow user:", error);
    throw error;
  }
}

export async function unfollowUser(userId: string | number, followerId: string | number) {
  try {
    
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/${userId}/unfollow`,
      { followerId }
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to unfollow user:", error);
    throw error;
  }
}

export async function fetchPublicCreatorProfile(userId: string | number) {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/public-profile/${userId}`
    );
    return response.data;
  } catch (error) {
    console.error("Failed to fetch public creator profile:", error);
    throw error;
  }
}
// 2FA API Endpoints

/**
 * Initiates the 2FA setup process by generating a secret key and QR code
 */
export const setup2FA = async (data: { userId: string | number }) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/2fa/setup`,
      data,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to setup 2FA",
        "2FA_SETUP_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

/**
 * Verifies the 2FA setup by checking the provided token against the generated secret
 */
export const verify2FASetup = async (data: { userId: string | number, token: string }) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/2fa/verify`,
      data,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to verify 2FA setup",
        "2FA_VERIFY_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};


export async function buyPerk(data: {
  userId: number;
  perkId: string;
  price: number;
  buyerWallet: string | undefined;
  sellerWallet: string;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/buy`,
      {
        userId: data.userId,
        perkId: data.perkId,
        price: data.price,
        buyerWallet: data.buyerWallet,
        sellerWallet: data.sellerWallet
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to create escrow",
        "ESCROW_CREATION_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error creating perk escrow:", error);
    
    if (error.response) {
      // Handle specific error cases
      if (error.response.status === 400) {
        throw new AppError(
          error.response.data.message || "Invalid request parameters",
          "INVALID_REQUEST",
          400
        );
      }
      if (error.response.status === 404) {
        throw new AppError(
          error.response.data.message || "Perk or seller token not found",
          "NOT_FOUND",
          404
        );
      }
      if (error.response.status === 500) {
        throw new AppError(
          error.response.data.message || "Escrow creation failed on-chain",
          "ON_CHAIN_ERROR",
          500
        );
      }
      
      throw new AppError(
        error.response.data?.message || error.message,
        "ESCROW_CREATION_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
}

// Confirm escrow transaction after signing
export async function confirmEscrow(data: {
  tradeId: string;
  txId: string;
  escrowId?: string;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/confirmEscrow`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to confirm escrow",
        "CONFIRM_ESCROW_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error confirming escrow:", error);
    throw handleEscrowError(error, "confirm");
  }
}

// Initiate pre-purchase chat
export async function initiatePerkChat(data: {
  userId: number;
  perkId: number;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/initiateChat`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to initiate chat",
        "INITIATE_CHAT_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error initiating chat:", error);
    throw handleEscrowError(error, "initiate chat");
  }
}

// Report trade for dispute resolution
export async function reportTrade(data: {
  tradeId: string;
  reportReason: string;
  reportDetails?: string;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/report`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to report trade",
        "REPORT_TRADE_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error reporting trade:", error);
    throw handleEscrowError(error, "report trade");
  }
}

// Additional functions for release and refund
export async function releasePerk(data: {
  tradeId: string;
  sellerWallet: string | undefined;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/releasePerk`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to release funds",
        "RELEASE_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error releasing perk funds:", error);
    throw handleEscrowError(error, "release");
  }
}

export async function refundPerk(data: {
  tradeId: string;
  buyerWallet: string | undefined;
}) {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/perks/refundPerk`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.status === 200) {
      return response.data;
    } else {
      throw new AppError(
        response.data.message || "Failed to process refund",
        "REFUND_ERROR",
        response.data.status || 500
      );
    }
  } catch (error: any) {
    console.error("Error refunding perk:", error);
    throw handleEscrowError(error, "refund");
  }
}

// Helper function for error handling
function handleEscrowError(error: any, action: string) {
  if (error.response) {
    if (error.response.status === 400) {
      return new AppError(
        error.response.data.message || `Invalid trade state for ${action}`,
        "INVALID_TRADE_STATE",
        400
      );
    }
    if (error.response.status === 404) {
      return new AppError(
        error.response.data.message || `Trade not found for ${action}`,
        "TRADE_NOT_FOUND",
        404
      );
    }
    if (error.response.status === 500) {
      return new AppError(
        error.response.data.message || `On-chain ${action} failed`,
        "ON_CHAIN_ERROR",
        500
      );
    }
    
    return new AppError(
      error.response.data?.message || error.message,
      `${action.toUpperCase()}_ERROR`,
      error.response.status
    );
  } else if (error.request) {
    return new NetworkError("No response from server");
  } else {
    return new AppError(
      error.message || "An unexpected error occurred",
      "UNKNOWN_ERROR",
      500
    );
  }
}

/**
 * Verifies the 2FA token during login
 */
export const verify2FALogin = async (data: { userId: string | number, token: string }) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/users/2fa/login`,
      data
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 400) {
        throw new ValidationError(
          error.response.data.message || "Invalid 2FA token"
        );
      }
      if (error.response.status === 401) {
        throw new AuthenticationError(
          error.response.data.message || "2FA token expired"
        );
      }
      throw new AppError(
        error.response.data.message || "Failed to verify 2FA login",
        "2FA_LOGIN_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

/**
 * Disables 2FA for a user
 */
export const disable2FA = async (data: { userId: string | number, token: string }) => {
  try {
    const response = await axios.delete(
      `${API_CONFIG.BASE_URL}/users/2fa/disable`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        data
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to disable 2FA",
        "2FA_DISABLE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

// ==================== DISPUTE MANAGEMENT ====================

export const initiateDispute = async (disputeData: {
  tradeId: number;
  reason: string;
  initiatorRole: 'buyer' | 'seller';
}) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/disputes/initiate`,
      disputeData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to initiate dispute",
        "DISPUTE_INITIATE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const getDisputes = async (params?: {
  status?: string;
  page?: number;
  pageSize?: number;
}) => {
  try {
    const token = localStorage.getItem("token");
    const queryParams = new URLSearchParams();

    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());

    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/disputes?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get disputes",
        "GET_DISPUTES_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const getDisputeDetails = async (disputeId: number) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/disputes/${disputeId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get dispute details",
        "GET_DISPUTE_DETAILS_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const assignDispute = async (disputeId: number) => {
  try {
    const token = localStorage.getItem("authToken");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/disputes/${disputeId}/assign`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to assign dispute",
        "ASSIGN_DISPUTE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const resolveDispute = async (disputeId: number, resolutionData: {
  buyerPercentage: number;
  sellerPercentage: number;
  moderatorNotes: string;
  resolution: string;
}) => {
  try {
    const token = localStorage.getItem("authToken");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/disputes/${disputeId}/resolve`,
      resolutionData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to resolve dispute",
        "RESOLVE_DISPUTE_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

// ==================== MODERATOR MANAGEMENT ====================

export const getModeratorDashboard = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/moderators/dashboard`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get moderator dashboard",
        "GET_MODERATOR_DASHBOARD_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const getModerators = async (params?: {
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}) => {
  try {
    const token = localStorage.getItem("token");
    const queryParams = new URLSearchParams();

    if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString());
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());

    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/moderators?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get moderators",
        "GET_MODERATORS_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const addModerator = async (moderatorData: {
  userId: number;
  specializations?: string[];
  maxConcurrentDisputes?: number;
}) => {
  try {
    const token = localStorage.getItem("authToken");
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/moderators/add`,
      moderatorData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to add moderator",
        "ADD_MODERATOR_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const updateModeratorStatus = async (moderatorId: number, isActive: boolean) => {
  try {
    const token = localStorage.getItem("authToken");
    const response = await axios.patch(
      `${API_CONFIG.BASE_URL}/moderators/${moderatorId}/status`,
      { isActive },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to update moderator status",
        "UPDATE_MODERATOR_STATUS_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const getTradeDetails = async (tradeId: number) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/perks/trade/${tradeId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get trade details",
        "GET_TRADE_DETAILS_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

// ==================== NOTIFICATION MANAGEMENT ====================

export const getNotifications = async (params?: {
  page?: number;
  pageSize?: number;
  unreadOnly?: boolean;
}) => {
  try {
    const token = localStorage.getItem("token");
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params?.unreadOnly !== undefined) queryParams.append('unreadOnly', params.unreadOnly.toString());

    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/notifications?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get notifications",
        "GET_NOTIFICATIONS_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const getUnreadNotificationCount = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}/notifications/unread-count`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to get unread count",
        "GET_UNREAD_COUNT_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const markNotificationAsRead = async (notificationId: number) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.patch(
      `${API_CONFIG.BASE_URL}/notifications/${notificationId}/read`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to mark notification as read",
        "MARK_NOTIFICATION_READ_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};

export const markAllNotificationsAsRead = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.patch(
      `${API_CONFIG.BASE_URL}/notifications/mark-all-read`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new AppError(
        error.response.data.message || "Failed to mark all notifications as read",
        "MARK_ALL_NOTIFICATIONS_READ_ERROR",
        error.response.status
      );
    } else if (error.request) {
      throw new NetworkError("No response from server");
    } else {
      throw new AppError(
        error.message || "An unexpected error occurred",
        "UNKNOWN_ERROR",
        500
      );
    }
  }
};
