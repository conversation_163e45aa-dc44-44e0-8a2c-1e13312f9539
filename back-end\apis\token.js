const { Router } = require("express");
const {
  getTokens,
  addToken,
  getToken,
  purchaseToken,
  postComments,
  syncBondingCurves,
  checkGraduation,
  createAirDrop,
  claimAirDrop,
  addBurnLogs,
  getUserLogs,
} = require("../controller/token");
const { query,body  } = require('express-validator');
const { authenticate } = require("../middleware/authenticate");
const { validate } = require("../middleware/validator");

const router = Router();

router.get("/syncBondingCurves", syncBondingCurves);
router.get("/checkGraduation", checkGraduation);
router.get(
  "/getAll",
  [
    query("sort").optional().isString(),//
    query("order").optional().isString(),
    query("page").optional().isInt({ min: 0 }),//
    query("pageSize").optional().isInt({ min: 1 }),//
  ],
  validate,
  getTokens
);
router.get("/:id", getToken);
router.post(
  "/buy",
  [
    body("userId").notEmpty().withMessage("User ID is required"),
    body("tokenId").notEmpty().withMessage("Token ID is required"),
    body("amount").notEmpty().withMessage("Amount is required"),
    body("price").notEmpty().withMessage("Price is required"),
    body("dollorPrice").notEmpty().withMessage("Dollar Price is required"),
    body("hasH").notEmpty().withMessage("Transaction hash is required"),
  ],
  validate,
  purchaseToken
);
router.post("/postComments", authenticate, postComments);

router.post(
  "/addToken",
  [
    body("name").notEmpty().withMessage("Token name is required"),
    body("ticker").notEmpty().withMessage("Ticker is required"),
    body("description").notEmpty().withMessage("Description is required"),
    body("userId").notEmpty().withMessage("User ID is required"),
    // Optional: you can add more validations for URLs, category, etc.
  ],
  validate,
  addToken
);

router.post(
  "/createAirDrop",
  [
    body("totalTokens")
      .isInt({ gt: 0 })
      .withMessage("Total tokens must be a positive integer"),
    body("tokensPerLink")
      .isInt({ gt: 0 })
      .withMessage("Tokens per link must be a positive integer"),
    body("isPrivate")
      .optional()
      .isBoolean()
      .withMessage("isPrivate must be a boolean"),
    body("UserID").notEmpty().withMessage("UserID is required"),
    body("TokenID").notEmpty().withMessage("TokenID is required"),
    body("signature").notEmpty().withMessage("Signature is required"),
    body("tokenAddress").notEmpty().withMessage("Token address is required"),
  ],
  validate,
  createAirDrop
);
router.post(
  "/claimAirDrop",
  [
    body("code").notEmpty().withMessage("Airdrop code is required"),
  ],
  validate,
  claimAirDrop
);
router.post(
  "/BurnLogs",
  [
    body("userId").notEmpty().withMessage("User ID is required"),
    body("tokenId").notEmpty().withMessage("Token ID is required"),
    body("amount")
      .isFloat({ gt: 0 })
      .withMessage("Amount must be a positive number"),
    body("signature").notEmpty().withMessage("Signature is required"),
    body("message").notEmpty().withMessage("Message is required"),
  ],
  validate,
  addBurnLogs
);
router.post(
  "/getUserLogs",
  [
    body("type").isInt({ min: 0, max: 1 }).withMessage("Type must be 0 or 1"),
    body("userId")
      .if(body("type").equals("0"))
      .notEmpty()
      .withMessage("User ID is required when type is 0"),
  ],
  validate,
  getUserLogs
);

module.exports = router;
