{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "download": "Download", "upload": "Upload", "view": "View", "add": "Add", "remove": "Remove", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "menu": "<PERSON><PERSON>", "copy": "Copy", "paste": "Paste", "select": "Select", "all": "All", "none": "None", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "noResults": "No results found", "tryAnotherSearch": "Try adjusting your search terms or browse all available tokens and perks.", "createToken": "Create Token"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up", "coins": "Coins", "perks": "Perks", "perksShop": "Perks Shop", "airdrop": "Airdrop", "chat": "Cha<PERSON>", "notifications": "Notifications", "create": "Create", "buy": "Buy", "sell": "<PERSON>ll", "swap": "<PERSON><PERSON><PERSON>", "trade": "Trade", "history": "History", "analytics": "Analytics", "createYourToken": "Create your token"}, "header": {"create": "Create", "createPerk": "Create Perk", "perk": "Perk", "connectWallet": "Connect Wallet", "disconnect": "Disconnect", "balance": "Balance", "dropdown": "Dropdown", "profile": "Profile", "wallet": "Wallet", "copyAddress": "Copy Address", "userBalance": "User Balance", "settings": "Settings", "logout": "Logout"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "email": "Email", "password": "Password", "username": "Username", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signInWith": "Sign in with", "signUpWith": "Sign up with", "or": "or", "invalidCredentials": "Invalid credentials", "accountCreated": "Account created successfully", "passwordReset": "Password reset email sent", "verificationRequired": "Please verify your email"}, "dashboard": {"welcome": "Welcome", "totalBalance": "Total Balance", "availableBalance": "Available Balance", "totalPerks": "Total Perks", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "connectWallet": "Connect Wallet", "disconnect": "Disconnect", "createCoin": "Create Coin", "buyPerk": "Buy Perk", "collectAirdrop": "Collect Airdrop", "viewTransactions": "View Transactions"}, "coins": {"createCoin": "Create Coin", "coinName": "Coin Name", "coinSymbol": "Coin Symbol", "initialSupply": "Initial Supply", "description": "Description", "price": "Price", "marketCap": "Market Cap", "volume": "Volume", "change24h": "24h Change", "buy": "Buy", "sell": "<PERSON>ll", "swap": "<PERSON><PERSON><PERSON>", "details": "Details", "chart": "Chart", "trades": "Trades", "comments": "Comments", "reviews": "Reviews", "tokenNotFound": "Token Not Found", "tokenNotFoundDescription": "The token you're looking for doesn't exist or has been removed.", "backToHome": "Back to Home", "backToCoins": "Back to Coins", "priceChart": "Price Chart", "trade": "Trade", "availablePerks": "Available Perks", "viewAllPerks": "View All Perks"}, "perks": {"perksShop": "Perks Shop", "createPerk": "Create Perk", "perkName": "Perk Name", "perkDescription": "Perk Description", "price": "Price", "category": "Category", "duration": "Duration", "buyPerk": "Buy Perk", "myPerks": "My Perks", "availablePerks": "Available Perks", "purchasedPerks": "Purchased <PERSON><PERSON>", "perkDetails": "Perk Details", "perkBenefits": "Perk Benefits", "perkTerms": "Terms & Conditions"}, "airdrop": {"collectAirdrop": "Collect Airdrop", "availableAirdrops": "Available Airdrops", "airdroppedTokens": "Airdropped Tokens", "claimAirdrop": "<PERSON><PERSON><PERSON> Airdrop", "airdropHistory": "Airdrop History", "airdropDetails": "Airdrop Details", "eligibility": "Eligibility", "claimAmount": "<PERSON><PERSON><PERSON>", "claimDate": "Claim Date", "validatingAirdrop": "Validating Airdrop", "pleaseWaitWhileValidatingCode": "Please wait while we validate your airdrop code: {airdropCode}", "invalidAirdropCode": "Invalid Airdrop Code", "invalidCodeMessage": "The airdrop code {airdropCode} is not valid or has already been claimed.", "close": "Close", "airdropAvailable": "Airdrop Available!", "youHaveTokensAvailable": "You have {tokenAmount} tokens available to claim", "connectWalletToClaim": "Connect your wallet to claim your airdrop tokens", "connectWalletAndClaim": "Connect Wallet & Claim", "claiming": "Claiming...", "claim": "<PERSON><PERSON><PERSON>", "tokens": "Tokens", "airdropCode": "Airdrop Code"}, "chat": {"messages": "Messages", "newMessage": "New Message", "sendMessage": "Send Message", "typeMessage": "Type your message...", "online": "Online", "offline": "Offline", "typing": "typing...", "messageSent": "Message sent", "messageReceived": "Message received", "connectWalletToChat": "Connect your wallet to chat.", "connectingToChat": "Connecting to chat...", "autoReleaseIn": "Auto-release in {{time}}", "autoReleaseInProgress": "Auto-release in progress", "timeFormat": "{{days}}d {{hours}}h {{minutes}}m {{seconds}}s", "userIdNotFound": "User ID not found. Please re-login.", "today": "Today", "yesterday": "Yesterday", "user": "User", "iGotTheItem": "I got the item", "tradeStatus": "Trade status:", "loadingAutoRelease": "Loading auto-release timer...", "reportTrade": "Report this trade"}, "profile": {"personalInfo": "Personal Information", "security": "Security", "preferences": "Preferences", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "accountSettings": "Account <PERSON><PERSON>", "changePassword": "Change Password", "deleteAccount": "Delete Account", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone", "address": "Address", "bio": "Bio", "avatar": "Avatar", "updateProfile": "Update Profile"}, "settings": {"general": "General", "appearance": "Appearance", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "autoMode": "Auto Mode", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "twoFactorAuth": "Two-Factor Authentication", "sessionTimeout": "Session Timeout"}, "ui": {"trendingCoins": "Trending Coins", "trendingPerks": "Trending Perks", "showingCoins": "Showing {{count}} coins", "showingPerks": "Showing {{count}} perks", "saveAndContinue": "Save and continue", "saving": "Saving...", "openSignupModal": "Open Signup Modal", "errorLoadingDashboard": "Error Loading Dashboard", "tryAgain": "Try Again", "justNow": "Just now", "purchased": "Purchased", "viewCoin": "View Coin"}, "categories": {"all": "All", "art": "Art", "music": "Music", "photography": "Photography", "videography": "Videography", "utility": "Utility"}, "sort": {"marketCap": "Market Cap", "price": "Price", "newest": "Newest"}, "filters": {"verified": "Verified", "notVerified": "Not Verified", "applyingFilters": "Applying filters..."}, "tabs": {"myPortfolio": "My Portfolio", "myTransactions": "My Transactions", "myCoin": "My Coin", "perksBought": "<PERSON>ks Bought"}, "errors": {"somethingWentWrong": "Something went wrong", "networkError": "Network error", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not found", "serverError": "Server error", "validationError": "Validation error", "invalidInput": "Invalid input", "requiredField": "This field is required", "invalidEmail": "Invalid email address", "passwordTooShort": "Password is too short", "passwordsDoNotMatch": "Passwords do not match", "failedToFetchTokenDetails": "Failed to fetch token details", "errorFetchingTokenData": "Error fetching token data"}, "success": {"operationCompleted": "Operation completed successfully", "dataSaved": "Data saved successfully", "itemCreated": "Item created successfully", "itemUpdated": "Item updated successfully", "itemDeleted": "Item deleted successfully", "passwordChanged": "Password changed successfully", "profileUpdated": "Profile updated successfully", "airdropClaimed": "Airdrop claimed successfully", "perkPurchased": "Perk purchased successfully"}, "congrats": {"title": "Congratulations!", "unlockedTokens": "You just unlocked {count} ANS tokens", "claimNow": "Claim now", "tokensRemaining": "Tokens Remaining: {count}"}, "verifyAccount": {"title": "Verify your account and token", "uploadSelfie": "Upload a selfie like below and add the word", "addToBio": "\"funhi\" to the bio of one of your social media accounts. Then contact us here"}, "balance": {"availableBalance": "Available Balance :  Coins", "loading": "Loading...", "totalBurntCoins": "Total Burnt coins"}, "notifications": {"trade": "Trade", "system": "System Notifications", "noNotifications": "No notifications", "allCaughtUp": "You're all caught up!", "markAllAsRead": "Mark all as read"}, "reviews": {"title": "Reviews", "mustHavePerk": "You should have this perk to Review", "writeAReview": "Write a review...", "post": "Post"}, "comments": {"loginToComment": "<PERSON>gin to comment", "writeAComment": "Write a comment...", "aboutThisToken": "About this token", "commentsTab": "Comments ({{count}})", "aboutTab": "About", "pleaseLogin": "Please login!", "thanksForComment": "Thanks for posting your comment.", "failedToPost": "Failed to post comment. Please try again.", "noDescription": "No description available."}, "table": {"noDataAvailable": "No data available", "noTransactionsFound": "No transactions found", "noTransactionsAvailable": "No transactions available at the moment.", "noFilteredTransactions": "No {filterType} transactions available.", "noCoinsCreated": "No coins created yet", "noCoinsCreatedDescription": "You haven't created any coins yet. Create your first token to start managing your coin ecosystem.", "createYourFirstToken": "Create Your First Token", "noPerksAvailable": "No perks available", "noPerksAvailableDescription": "There are no perks available right now. Be the first to create an exciting perk for your community!", "createPerk": "Create Perk", "noTransactionsYet": "No transactions yet", "noTransactionsYetDescription": "You haven't made any transactions yet. Start buying or selling tokens to see your transaction history here.", "noCommentsYet": "No comments yet", "noCommentsYetDescription": "Be the first to share your thoughts about this token. Start the conversation!", "noReviewsYet": "No reviews yet", "noReviewsYetDescription": "This perk hasn't been reviewed yet. Purchase it to be the first to leave a review!", "noAirdropsCreated": "No airdrops created", "noAirdropsCreatedDescription": "You haven't created any airdrops yet. Create your first airdrop to reward your community!", "createAirdrop": "Create Airdrop", "noBurnsCreated": "No burns created", "noBurnsCreatedDescription": "You haven't burned any tokens yet. Burn tokens to reduce supply and potentially increase value.", "burnTokens": "<PERSON>", "portfolioEmpty": "Your portfolio is empty", "portfolioEmptyDescription": "You don't own any tokens yet. Start investing in tokens to build your portfolio and track your performance.", "noPerksPurchased": "No perks purchased", "noPerksPurchasedDescription": "You haven't purchased any perks yet. Explore the perks marketplace to find exclusive offers and rewards."}, "createCoinForm": {"createCoin": "Create Coin", "connectWallet": "Please connect your Solana wallet...", "name": "Name", "ticker": "Ticker", "selectCategory": "Select a category", "description": "Description", "telegram": "Telegram (optional)", "website": "Website (optional)", "twitter": "X (optional)", "showMoreOptions": "Show more options", "hideMoreOptions": "Hide more options", "uploadPicture": "Upload picture", "upload": "Upload", "processing": "Processing...", "createMyCoin": "Create my Coin", "coinCreatedSuccess": "Coin Created Successfully!", "coinCreatedProcessing": "Your coin has been submitted and is being processed.", "tokenCreatedSuccess": "<PERSON><PERSON> created successfully!", "somethingWentWrong": "Something went wrong", "failedToCreateToken": "Failed to create token. Please try again.", "pleaseSelectCategory": "Please select a category", "nameRequired": "Name is required", "nameMinLength": "Name must be at least {{minLength}} characters", "priceRequired": "Price is required", "validPrice": "Please enter a valid price", "validField": "Please enter a valid {{fieldName}}", "stockAmountRequired": "Stock amount is required", "validStockAmount": "Please enter a valid stock amount", "tickerRequired": "Ticker is required", "tickerLength": "Ticker must be 2-5 characters", "tickerFormat": "Ticker must contain only uppercase letters and numbers", "validTelegram": "Please enter a valid Telegram username", "validTwitter": "Please enter a valid Twitter/X username", "uploadImage": "Please upload an image", "imageMaxSize": "Image must be less than {{maxSizeMB}}MB", "imageType": "Only JPEG, PNG and GIF images are allowed", "loginFirst": "Please log in first before Creating a token."}, "authModal": {"signinTitle": "Sign in to your account", "signinSubtitle": "Welcome back! Please enter your details.", "signupTitle": "Create your account", "signupSubtitle": "Let's create an account and start a wonderful journey", "username": "Username", "email": "Email", "password": "Password", "passwordRequired": "Password is required.", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "success": "Success!", "invalidEmail": "Please enter a valid email address", "passwordPolicy": "Password must be at least 8 characters and include uppercase, lowercase, number, and special character", "agreeTerms": "You must agree to the Terms, Privacy Policy and Fees.", "submit": "Submit", "signin": "Sign In", "signup": "Sign Up", "signupButton": "Sign Up", "termsLabel": "I agree to all Term, Privacy Policy and Fees", "facebook": "Facebook", "alreadyHaveAccount": "Already have an account?", "login": "<PERSON><PERSON>", "google": "Google", "dontHaveAccount": "Don't have an account yet?", "signupLink": "Sign Up", "orLoginWith": "Or log in with"}, "createPerkForm": {"formTitle": "Create Perk", "name": "Name", "description": "Description", "price": "Price", "currency": "$", "selectCategory": "Select a category", "category": "Category", "fulfillmentLink": "Fulfillment link", "uploadPicture": "Upload picture", "upload": "Upload", "clear": "Clear", "limitedStock": "Limited stock", "stock": "Stock", "submit": "Create my perk", "processing": "Processing...", "successTitle": "Success!", "successMessage": "<PERSON>k Created Successfully!", "successDescription": "Your perk has been submitted and is being processed.", "errorSelectCategory": "Please select a category", "errorUploadPicture": "Please upload an image for your perk", "errorSomethingWentWrong": "Something went wrong"}, "airDrops": {"connectWallet": "Please connect your Solana wallet", "wrongWallet": "Solana wallet not connected or the wrong wallet is being used. Please log out and log in again.", "createSuccess": "Airdrop created successfully!", "createFail": "Failed to create airdrop. Please try again.", "formTitle": "Create Airdrop", "formButton": "Create Airdrop", "aboutTitle": "About Airdrops", "aboutDescription": "A great way to grow your community. Share this link with your loyal fans and they get tokens! You can create as many air drops as you want as long as you have tokens available.", "tableTitle": "Airdrops", "colLink": "Link", "colTotalTokens": "Total tokens", "colPerLink": "Per Link"}, "availablePerks": {"title": "Available perks", "noPerks": "No perks available", "noPerksDescription": "Check back later for exciting perks and rewards!", "sortBy": "Sort by: {{sort}}", "sortBestSelling": "Best selling", "sortNewest": "Newest", "sortLowToHigh": "Price: Low to High", "sortHighToLow": "Price: High to Low", "viewAll": "View All Perks ({{count}} more)"}, "burns": {"wrongWallet": "Solana wallet not connected or the wrong wallet is being used. Please log out and log in again.", "burnSuccess": "Successfully burned {{count}} tokens!", "formTitle": "Burn coins", "formButton": "Burn coins", "formPlaceholder": "Total token to Burn", "aboutTitle": "About <PERSON>", "aboutDescription": "A great way to grow your community. Share this link with your loyal fans and they get tokens! You can create as many air drops as you want as long as you have tokens available.", "tableTitle": "<PERSON>", "colLink": "Link", "colTotalTokens": "Total tokens", "colRemaining": "Remaining"}, "creatorWithdraw": {"button": "Withdraw Unlocked Tokens", "tooltipTitle": "Daily Token Unlock", "tooltipDescription": "1% of the $1M tokens issued to the creator unlock daily. Your wallet will display the available amount to withdraw."}, "detail": {"breadcrumb": "Detail", "productImage": "Product Image"}, "exchangeForm": {"noTokensFrom": "No tokens available for \"From\".", "noTokensTo": "No tokens available for \"To\".", "from": "From", "to": "To", "available": "Available:", "loading": "Loading...", "processing": "Processing...", "swap": "<PERSON><PERSON><PERSON>", "refreshBalances": "Refresh Balances"}, "footer": {"about": "About US", "howItWorks": "How it works", "privacy": "Privacy", "copyright": "© 2024 FUNHi. All rights reserved."}, "linkBox": {"title": "Share your affiliate link and earn 50% fees", "copy": "Copy", "copied": "Copied!"}, "myProfile": {"totalBalance": "Total Balance", "sortBy": "Sort by :", "low24h": "24H Low", "high24h": "24H High", "noTokens": "No tokens available to display."}, "password": {"sectionTitle": "Password", "oldPassword": "Old password", "newPassword": "New password", "repeatNewPassword": "Repeat new password", "inputPlaceholder": "Input text", "validationStrong": "Password must be at least 8 characters and include uppercase, lowercase, number, and special character", "validationMatch": "Passwords do not match"}, "perksSection": {"myPerks": "My Perks", "createPerk": "Create perk", "noPerks": "No perks found. Create your first perk!"}, "perksBought": {"title": "My Perks Purchases", "sortByOldest": "Sort by Oldest", "sortByRecent": "Sort by Recent", "noPerks": "No perks purchased yet."}, "profileDetails": {"name": "Name", "email": "Email", "bio": "Bio", "inputPlaceholder": "Input text", "validationName": "Name must be 2-50 characters and can only contain letters, spaces, hyphens and apostrophes", "validationEmail": "Please enter a valid email"}, "securitySection": {"title": "Security", "twoFactor": "Two-factor authentication (2FA) settings", "googleAuth": "Google Authenticator", "googleAuthDesc": "Random time-bound passcode generated by the app.", "activate": "Activate"}, "sidebar": {"profileDetails": "Profile Details", "security": "Security"}, "productCard": {"loginToBuy": "Please log in before purchasing perks.", "noTokenLinked": "No token is linked with this Perk", "connectWallet": "Please connect your Solana wallet", "onChainFailed": "On-chain transaction failed, but you may still proceed with the purchase.", "buyFailed": "Failed to purchase perk. Please try again.", "purchaseSuccess": "You have successfully purchased the perk and will receive your tokens shortly.", "buy": "Buy", "buyers": "Buyers", "verified": "Verified", "claimed": "Claimed!", "price": "Price", "buyPerk": "Buy Perk", "bonus": "Bonus", "bonusAirdrop": "Bonus Airdrop", "addToFavorites": "Add to Favorites", "share": "Share"}, "tokenHeader": {"tokenActive": "Token · Active", "currentPrice": "Current Price", "marketCap": "Market Cap", "createdBy": "Created by", "creator": "Creator", "tokenCreatorProfile": "Token Creator Profile", "performance": "Performance", "gain": "<PERSON><PERSON>", "loss": "Loss", "24h": "24H", "7d": "7D", "1m": "1M", "allTime": "All Time"}, "tokenStats": {"marketValue": "Market Value", "low24h": "24H Low", "high24h": "24H High", "volume": "Volume", "return24h": "24H Return", "tradingPair": "Trading Pair", "activeTrading": "Active Trading"}, "tradeList": {"yourTrades": "Your Trades", "noTradesFound": "No trades found.", "perk": "Perk", "status": "Status", "chat": "Cha<PERSON>"}, "profileHeader": {"profileDetails": "Profile Details", "profileAvatar": "Profile avatar", "followers": "followers", "following": "following", "unfollow": "Unfollow", "follow": "Follow", "unfollowUser": "Unfollow user", "followUser": "Follow user", "sendMessage": "Send message", "message": "Message", "notAcceptingMessages": "This user is not accepting messages.", "sendMessageDisabled": "Send message (disabled)", "allowOthersToMessageMe": "Allow others to message me", "rejectMessage": "Reject Message", "acceptMessage": "Accept Message"}, "coinCard": {"coinCard": "Coin card", "coinImage": "Coin image", "coin": "Coin", "symbol": "Symbol", "marketCap": "Market cap", "price": "Price", "holders": "Holders", "priceChart": "Price Chart"}, "profilePage": {"profileInformation": "Profile information", "coins": "Coins", "items": "Items", "loadError": "Failed to load profile data. Please try again later.", "noCoinsFound": "No coins found.", "perkToken": "Perk Token", "sortBy": "Sort by", "sales": "Sales", "price": "Price", "name": "Name", "creatorFollowsYou": "This creator follows you"}}