/** @format */

const { updateSolToken } = require("../utilities/solana");
const { getAllTokens, setTokenPriceInUsd } = require("../db/token");
const fetch = (...args) =>
  import("node-fetch").then(({ default: fetch }) => fetch(...args));

const updateSolPrice = async ({ tokenId, name }) => {

  try {
    const response = await fetch("https://api.livecoinwatch.com/coins/single", {
      method: "POST",
      headers: {
        "content-type": "application/json",
        "x-api-key": "9eb4a396-d9d8-45d9-b959-f6398c435935",
      },
      body: JSON.stringify({
        currency: "USD",
        code: name.toUpperCase(),
        meta: true,
      }),
    });
    const data = await response.json()

    if (!isNaN(data.rate ? data.rate : 0) && (data.rate ? data.rate : 0) > 0) {
      let price = typeof data.rate == "number" ? data.rate : null;
      await setTokenPriceInUsd({ tokenId, price });

      return;
    }
  } catch (error) {
    console.log(error, "Error coming from price api....");
  }
};


exports.updateSolTokenPrice = async function () {
  const tokens = await getAllTokens();  
  for (let token of tokens) {
    await updateSolPrice({
      tokenId: token.tokenId,
      name: token.name
    })
  }
  console.log("SOL TOKEN PRICE HAS BEEN UPDATED");
}
