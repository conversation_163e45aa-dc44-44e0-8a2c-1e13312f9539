'use client';
import { useEffect, useRef } from 'react';
import { useSocket } from '@/contexts/SocketProvider';
import { usePrivy } from "@privy-io/react-auth";

interface RealTimeNotificationListenerProps {
  onNewNotification?: () => void; // Callback to refresh notifications
}

const RealTimeNotificationListener = ({ onNewNotification }: RealTimeNotificationListenerProps) => {
  const socket = useSocket();
  const { user } = usePrivy();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (!socket || !user?.id) return;

    const handleNewNotification = (notificationData: any) => {
      console.log('🔔 [RealTimeNotificationListener] New notification received:', notificationData);

      // Only process if this notification is for the current user
      if (notificationData.receiverId === parseInt(user.id as string)) {
        // Play notification sound
        if (audioRef.current) {
          console.log('🔊 [RealTimeNotificationListener] Playing notification sound');
          try {
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch(e => {
              console.log('🔇 [RealTimeNotificationListener] Could not play notification sound:', e);
              // Fallback: show browser notification if audio fails
              if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('New Message', {
                  body: notificationData.message || 'You have a new message',
                  icon: '/images/funhi-logo.png'
                });
              }
            });
          } catch (error) {
            console.log('🔇 [RealTimeNotificationListener] Audio play error:', error);
          }
        }

        // Trigger notification refresh callback
        if (onNewNotification) {
          console.log('🔄 [RealTimeNotificationListener] Triggering notification refresh');
          onNewNotification();
        }
      }
    };

    const handleSystemNotification = (notificationData: any) => {
      console.log('📢 [RealTimeNotificationListener] System notification received:', notificationData);

      // System notifications are for all users, so always process them
      // Play notification sound for system notifications
      if (audioRef.current) {
        console.log('🔊 [RealTimeNotificationListener] Playing system notification sound');
        try {
          audioRef.current.currentTime = 0;
          audioRef.current.play().catch(e => {
            console.log('🔇 [RealTimeNotificationListener] Could not play system notification sound:', e);
            // Fallback: show browser notification if audio fails
            if ('Notification' in window && Notification.permission === 'granted') {
              new Notification(notificationData.title || 'System Notification', {
                body: notificationData.message || 'You have a new system notification',
                icon: '/images/funhi-logo.png'
              });
            }
          });
        } catch (error) {
          console.log('🔇 [RealTimeNotificationListener] System audio play error:', error);
        }
      }

      // Trigger notification refresh callback
      if (onNewNotification) {
        console.log('🔄 [RealTimeNotificationListener] Triggering notification refresh for system notification');
        onNewNotification();
      }
    };

    // Listen for new notification events
    socket.on('newNotification', handleNewNotification);
    socket.on('systemNotification', handleSystemNotification);

    return () => {
      socket.off('newNotification', handleNewNotification);
      socket.off('systemNotification', handleSystemNotification);
    };
  }, [socket, user?.id, onNewNotification]);

  // Request notification permission on component mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('🔔 [RealTimeNotificationListener] Notification permission:', permission);
      });
    }
  }, []);

  return (
    <audio 
      ref={audioRef} 
      src="/sounds/notification.mp3" 
      preload="auto" 
      style={{ display: 'none' }} 
    />
  );
};

export default RealTimeNotificationListener;
