'use client';

import { useTranslation } from '@/hooks/useTranslation';

const LanguageSwitcher = () => {
  const { currentLanguage, changeLanguage } = useTranslation();

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
    { code: 'ch', name: '中文', flag: '🇨🇳' },
  ];

  return (
    <div className="relative inline-block text-left">
      <div className="flex items-center space-x-2">
        <select
          value={currentLanguage}
          onChange={(e) => changeLanguage(e.target.value)}
          className="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white"
        >
          {languages.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.flag} {lang.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default LanguageSwitcher; 