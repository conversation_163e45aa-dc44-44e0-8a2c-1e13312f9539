const dataContext = require('./index')
const colors = require("colors");
async function migrate() {
    console.log(colors.america('Start migration job...'))
    await dataContext.User.sync({ alter: true });
    await dataContext.Token.sync({ alter: true });
    await dataContext.Perk.sync({ alter: true });
    await dataContext.Comments.sync({ alter: true });
    await dataContext.Reviews.sync({ alter: true });
    await dataContext.PerkPurchased.sync({ alter: true });
    await dataContext.TokenPurchased.sync({ alter: true });
    await dataContext.Airdrop.sync({ alter: true });
    await dataContext.AirdropLog.sync({ alter: true });
    await dataContext.userLogs.sync({ alter: true });
    await dataContext.Message.sync({ alter: true });
    await dataContext.ChatRoom.sync({ alter: true });
    await dataContext.Follow.sync({ alter: true });
    await dataContext.Dispute.sync({ alter: true });
    await dataContext.Moderator.sync({ alter: true });
    await dataContext.Notification.sync({ alter: true });
    console.log(colors.america('Migration job is finished...'))

    
}
migrate();