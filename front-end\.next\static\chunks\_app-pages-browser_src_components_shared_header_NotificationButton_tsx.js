"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_header_NotificationButton_tsx"],{

/***/ "(app-pages-browser)/./src/components/shared/header/NotificationButton.tsx":
/*!*************************************************************!*\
  !*** ./src/components/shared/header/NotificationButton.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst NotificationModal = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_shared_header_Notifications_index_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./Notifications */ \"(app-pages-browser)/./src/components/shared/header/Notifications/index.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\shared\\\\header\\\\NotificationButton.tsx -> \" + \"./Notifications\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 8,\n            columnNumber: 18\n        }, undefined)\n});\n_c = NotificationModal;\nconst ChatModal = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\shared\\\\header\\\\NotificationButton.tsx -> \" + \"@/components/shared/chat/ChatModal\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 12,\n            columnNumber: 18\n        }, undefined)\n});\n_c1 = ChatModal;\nconst NotificationButton = (param)=>{\n    let { notifications } = param;\n    _s();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const handleOpenModal = ()=>{\n        openModal({\n            id: \"notification\",\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(NotificationModal, {\n                onClose: ()=>closeModal(\"notification\"),\n                onOpenChat: async (chatRoomId, receiverId)=>{\n                    // Get current user id from localStorage\n                    const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n                    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n                    const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n                    console.log('🔍 [NotificationButton] Opening chat with:', {\n                        chatRoomId,\n                        receiverId,\n                        myUserId\n                    });\n                    // Use consistent modal ID to prevent multiple modals\n                    const modalId = \"chat-modal-\".concat(chatRoomId);\n                    // Close existing modal if it exists\n                    closeModal(modalId);\n                    // For now, we'll use basic implementation since we don't have notification data\n                    // In the future, this should be unified with NotificationBell logic\n                    openModal({\n                        id: modalId,\n                        component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ChatModal, {\n                            chatRoomId,\n                            buyerId: myUserId,\n                            sellerId: receiverId,\n                            onClose: ()=>closeModal(modalId),\n                            onRelease: ()=>{},\n                            onRefund: ()=>{},\n                            onReport: ()=>{},\n                            activeTrade: undefined // No trade data available from header notifications\n                        }),\n                        closeOnBackdropClick: true,\n                        closeOnEscape: true,\n                        preventClose: false,\n                        zIndex: 10000,\n                        backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',\n                        modalClassName: 'flex flex-col items-start justify-end p-4',\n                        disableScroll: true\n                    });\n                }\n            }),\n            closeOnBackdropClick: true,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',\n            modalClassName: 'flex flex-col items-start justify-end p-4',\n            disableScroll: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n            className: \"relative bg-[#F5F5F5] p-1 lg:p-2 h-[40px] w-[40px] lg:h-[53px] lg:w-[53px] flex items-center justify-center rounded-full transition-all duration-300 hover:bg-gray-200\",\n            whileHover: {\n                scale: 1.05\n            },\n            whileTap: {\n                scale: 0.95\n            },\n            onClick: ()=>handleOpenModal(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: 24,\n                    className: \"lg:w-8 lg:h-8 text-gray-700\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                    className: \"absolute top-[6px] right-[7px] lg:top-[12px] lg:right-[15px] bg-red-500 text-white rounded-full w-[10px] h-[10px] flex items-center justify-center text-xs\",\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 500,\n                        damping: 15\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(NotificationButton, \"NhFfHKE5HBDGg9H6/modni1Q4nI=\", false, function() {\n    return [\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal\n    ];\n});\n_c2 = NotificationButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationButton);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NotificationModal\");\n$RefreshReg$(_c1, \"ChatModal\");\n$RefreshReg$(_c2, \"NotificationButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/header/NotificationButton.tsx\n"));

/***/ })

}]);