'use client';

import { motion, TargetAndTransition, VariantLabels } from 'framer-motion';
import { <PERSON><PERSON>ir<PERSON>, Heart } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import { Coin } from '@/hooks/useCoins';
import { cardHover, fadeInUp } from '@/lib/animations';
import { useTranslation } from '@/hooks/useTranslation';

interface CardProps extends Partial<Coin> {
  onBuyClick?: () => void;
  buttonType?: 'buy' | 'view';
  borderless?: boolean;
  href?: string;
  backgroundColor?: string;
  isVerified?: boolean;
}

const Card: React.FC<CardProps> = ({
  id,
  imageUrl = '/images/placeholder.png',
  timeLeft = '2h : 4m : 32s',
  isLive = true,
  name,
  username = 'User',
  handle = '@user',
  price = '0.00 USD',
  soldCount = 0,
  remainingCount = 0,
  onBuyClick,
  buttonType = 'buy',
  borderless = false,
  href,
  backgroundColor,
  isPurshases = false,
  isVerified = false,
}) => {
  const handleBuyClick = (e: React.MouseEvent) => {
    if (onBuyClick) {
      e.preventDefault();
      onBuyClick();
    }
  };

  const { t } = useTranslation();

  const CardContent = () => (
    <motion.div
      className={`relative rounded-xl ${
        !borderless ? 'border border-[#FF6600]' : ''
      } transition-all duration-300 ease-in-out cursor-pointer overflow-hidden group`}
      style={{ backgroundColor: backgroundColor || 'white' }}
      whileHover={cardHover as unknown as (VariantLabels | TargetAndTransition)}
      variants={fadeInUp}
    >
      <div className="p-4 flex flex-col gap-5">
        <motion.div
          className="w-[100%] aspect-[1.55/1] rounded-lg overflow-hidden relative"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <div className="w-[100%] aspect-[1.55/1] relative bg-stone-300">
            <Image
              src={imageUrl}
              alt={name || 'Item Image'}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <motion.div
              className="absolute bottom-4 left-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-white text-2xl font-semibold leading-none drop-shadow-lg">
                {name || 'ITEM NAME'}
              </div>
            </motion.div>
          </div>

          <motion.div
            className="w-auto flex absolute right-[28px] top-[24px] items-center justify-center gap-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            {timeLeft && !isPurshases && (
              <motion.div
                className="h-7 bg-white/30 rounded-lg backdrop-blur-[20px] flex items-center justify-center px-3"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-white text-xs font-semibold leading-normal">
                  {timeLeft}
                </div>
              </motion.div>
            )}
            {isLive && !isPurshases && (
              <motion.div
                className="w-7 h-7 bg-white/30 rounded-[50px] backdrop-blur-[1.50px] flex items-center justify-center"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Heart size={16} className="text-white" />
              </motion.div>
            )}
          </motion.div>
        </motion.div>

        <div className="w-[100%] h-[120px] flex flex-col gap-4">
          <div className="w-[100%] h-14">
            <div className="w-[100%] h-14 flex justify-between items-center">
              <div className="flex items-center gap-4">
                <motion.div
                  className="w-14 h-14 bg-stone-300 rounded-full relative overflow-hidden"
                  whileHover={{ scale: 1.05 }}
                >
                  <Image
                    src={imageUrl}
                    alt={username || 'User Avatar'}
                    fill
                    className="rounded-full object-cover"
                  />
                </motion.div>
                <div className="flex flex-col">
                  <div className="text-neutral-900 text-base font-medium font-['Poppins'] capitalize flex items-center gap-2">
                    <span
                      className="block overflow-hidden text-ellipsis whitespace-nowrap max-w-[100px] sm:max-w-[70px] md:max-w-[90px] lg:max-w-[40px] xl:max-w-[50px]"
                      title={username}
                    >
                      {username}
                    </span>
                    {isVerified && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4, type: 'spring' }}
                      >
                        <CheckCircle size={16} className="text-green-500" />
                      </motion.div>
                    )}
                  </div>
                  <div className="text-neutral-400 text-sm font-semibold">
                    {handle}
                  </div>
                </div>
              </div>
              <div>
                {buttonType === 'buy' ? (
                  <motion.button
                    onClick={handleBuyClick}
                    className="h-7 rounded-[40px] bg-[#3BB266] text-white transition-all duration-300 ease-in-out hover:bg-[#F58A38]"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="text-white text-xs font-medium font-['Poppins'] capitalize cursor-pointer whitespace-nowrap text-center px-[27px]">
                      {t('navigation.buy')}
                    </div>
                  </motion.button>
                ) : (
                  <motion.button
                    className="h-7 rounded-[40px] transition-colors bg-[#FF6600] hover:bg-[#FF6600]/90"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="text-white text-xs font-medium font-['Poppins'] capitalize cursor-pointer whitespace-nowrap px-[12px] md:px-[16px] py-[5px]">
                      {t('ui.viewCoin')}
                    </div>
                  </motion.button>
                )}
              </div>
            </div>
          </div>

          <motion.div
            className="flex justify-between items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div>
              <div className="text-neutral-400 text-sm font-semibold">
                {t('coins.price')}
              </div>
              <div>
                <div className="text-black text-base font-medium font-['Poppins']">
                  {price}
                </div>
              </div>
            </div>

            {isPurshases ? (
              <div>
                <div className="text-right text-neutral-400 text-sm font-semibold">
                  {t('ui.purchased')}
                </div>
                <div className="text-right text-black text-base font-medium font-['Poppins']">
                  {timeLeft ? getTimePassed(timeLeft) : t('ui.justNow')}
                </div>
              </div>
            ) : (
              <div>
                <div className="text-right text-neutral-400 text-sm font-semibold">
                  {remainingCount > 0 ? 'Remaining' : 'Made before'}
                </div>
                <div className="text-right text-black text-base font-medium font-['Poppins']">
                  {remainingCount > 0
                    ? `${remainingCount} Coins`
                    : `${soldCount} Min`}
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );

  if (href) {
    return (
      <Link href={href}>
        <CardContent />
      </Link>
    );
  }

  return <CardContent />;

  function getTimePassed(timestamp: string | number) {
    const now = new Date().getTime();
    const time = new Date(timestamp).getTime();

    const diffMs = now - time;
    let diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) diffMins = 1;

    if (diffMins < 60) {
      return `${diffMins} min ago`;
    } else if (diffMins < 1440) {
      const hours = Math.floor(diffMins / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  }
};

export default Card;
