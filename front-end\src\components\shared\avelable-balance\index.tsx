"use client";

import React, { useState } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';
import { showErrorToast, showSuccessToast } from '@/utils/errorHandling';

interface AirDropFormData {
  totalTokens: string;
  tokensPerLink: string;
  isPrivate: boolean;
}

interface FormErrors {
  totalTokens: string;
  tokensPerLink: string;
  general: string;
}

interface CreateAirdropFormProps {
  title?: string;
  availableBalance?: number;
  componentType?: 'airdrop' | 'burn'; // To determine if this is for airdrops or burns
  totalTokensPlaceholder?: string;
  tokensPerLinkPlaceholder?: string;
  buttonText?: string;
  onSubmit?: (formData: AirDropFormData) => void;
  showSubmitButton?: boolean;
  className?: string;
  isLoading?: boolean;
}

const CreateAirdropForm: React.FC<CreateAirdropFormProps> = ({
  title = "Create Airdrop",
  availableBalance = 28322299,
  componentType = 'airdrop',
  totalTokensPlaceholder = "Total tokens in airdrop",
  tokensPerLinkPlaceholder = "Number of tokens to giveaway per link",
  buttonText = "Create Airdrop",
  onSubmit,
  showSubmitButton = true,
  className = "",
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<AirDropFormData>({
    totalTokens: '',
    tokensPerLink: '',
    isPrivate: true
  });
  const [errors, setErrors] = useState<FormErrors>({
    totalTokens: '',
    tokensPerLink: '',
    general: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Validation functions
  const validateTotalTokens = (value: string): string => {
    console.log('Validating totalTokens:', value);
    if (!value || !value.trim()) {
      console.log('Total tokens is empty');
      return 'Total tokens is required';
    }
    if (!/^\d+$/.test(value)) {
      console.log('Total tokens is not numeric');
      return 'Total tokens must be a number';
    }
    const numValue = parseInt(value);
    if (numValue <= 0) {
      console.log('Total tokens is zero or negative');
      return 'Total tokens must be greater than 0';
    }
    if (numValue > availableBalance) {
      console.log('Total tokens exceeds balance');
      return `Total tokens cannot exceed available balance (${availableBalance.toLocaleString()})`;
    }
    console.log('Total tokens is valid');
    return '';
  };

  const validateTokensPerLink = (value: string): string => {
    console.log('Validating tokensPerLink:', value, 'componentType:', componentType);
    if (componentType === 'burn') {
      console.log('Burn mode - tokens per link not required');
      return ''; // Not required for burns
    }
    if (!value || !value.trim()) {
      console.log('Tokens per link is empty');
      return 'Tokens per link is required';
    }
    if (!/^\d+$/.test(value)) {
      console.log('Tokens per link is not numeric');
      return 'Tokens per link must be a number';
    }
    const numValue = parseInt(value);
    if (numValue <= 0) {
      console.log('Tokens per link is zero or negative');
      return 'Tokens per link must be greater than 0';
    }
    if (formData.totalTokens && numValue > parseInt(formData.totalTokens)) {
      console.log('Tokens per link exceeds total tokens');
      return 'Tokens per link cannot exceed total tokens';
    }
    console.log('Tokens per link is valid');
    return '';
  };

  // Enhanced validation for submit - validates all fields regardless of user interaction
  const validateFormOnSubmit = (): FormErrors => {
    const totalTokensError = validateTotalTokens(formData.totalTokens);
    const tokensPerLinkError = validateTokensPerLink(formData.tokensPerLink);
    
    console.log('Validating form:', {
      totalTokens: formData.totalTokens,
      tokensPerLink: formData.tokensPerLink,
      totalTokensError,
      tokensPerLinkError
    });
    
    return {
      totalTokens: totalTokensError,
      tokensPerLink: tokensPerLinkError,
      general: ''
    };
  };

  const validateForm = (): FormErrors => {
    return {
      totalTokens: validateTotalTokens(formData.totalTokens),
      tokensPerLink: validateTokensPerLink(formData.tokensPerLink),
      general: ''
    };
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    // Clear general error when user starts typing
    if (errors.general) {
      setErrors(prev => ({ ...prev, general: '' }));
    }
    
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      // Only allow numbers for token inputs
      if (name === 'totalTokens' || name === 'tokensPerLink') {
        if (value === '' || /^\d+$/.test(value)) {
          setFormData(prev => ({
            ...prev,
            [name]: value
          }));
          
          // Real-time validation
          setTimeout(() => {
            const fieldError = name === 'totalTokens' 
              ? validateTotalTokens(value)
              : validateTokensPerLink(value);
            
            setErrors(prev => ({ ...prev, [name]: fieldError }));
          }, 300);
        }
      } else {
        setFormData(prev => ({
          ...prev,
          [name]: value
        }));
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Force validation and show all errors
    const newErrors = validateFormOnSubmit();
    console.log('Submit clicked - validation errors:', newErrors);
    
    // Set errors immediately
    setErrors(newErrors);
    
    // Check for validation errors
    const hasErrors = Object.values(newErrors).some(error => error !== '');
    if (hasErrors) {
      // Show the first error message as a toast
      const firstError = Object.values(newErrors).find(error => error !== '');
      if (firstError) {
        showErrorToast(firstError);
      }
      
      // Force re-render to show errors
      setTimeout(() => {
        setErrors({...newErrors});
      }, 100);
      
      return;
    }
    
    // Only proceed if no validation errors
    setIsSubmitting(true);
  
    if (componentType === 'burn' && !formData.tokensPerLink) {
      setFormData(prev => ({
        ...prev,
        tokensPerLink: '0'
      }));
    }
  
    try {
      if (onSubmit) {
        await onSubmit(formData);
        showSuccessToast(`${componentType === 'burn' ? 'Burn' : 'Airdrop'} created successfully!`);
      } else {
        await new Promise((resolve) => setTimeout(resolve, 1500));
        showSuccessToast(`${componentType === 'burn' ? 'Burn' : 'Airdrop'} created successfully!`);
      }
    } catch (err: any) {
      const errorMessage = err?.message || `Failed to create ${componentType}`;
      showErrorToast(errorMessage);
      setErrors(prev => ({ ...prev, general: errorMessage }));
      console.error("Error in form submit:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to get field styling
  const getFieldStyling = (fieldName: keyof FormErrors) => {
    const hasError = errors[fieldName];
    const hasValue = formData[fieldName as keyof AirDropFormData] && 
                    String(formData[fieldName as keyof AirDropFormData]).trim() !== '';
    
    if (hasError) {
      return 'border-red-500 bg-red-50 focus:ring-red-500';
    } else if (hasValue) {
      return 'border-green-500 bg-green-50 focus:ring-green-500';
    } else {
      return 'border-gray-300 focus:ring-black';
    }
  };

  // Determine privacy text based on component type
  const getPrivacyText = (isPrivate: boolean) => {
    if (componentType === 'burn') {
      return isPrivate 
        ? 'private - only people with your link' 
        : 'public - we will notify the entire site about this burn';
    }
    return isPrivate 
      ? 'private - only people with your link' 
      : 'public - we will notify the entire site';
  };

  return (
    <div className={`max-w-[459px] m-auto lg:m-0 ${className}`}>
      {title && (
        <h1 className="text-5xl font-semibold font-['IBM_Plex_Sans'] text-gray-900 leading-[72px] text-[#0C141D] mb-4">{title}</h1>
      )}
      
      <div className="mb-6">
        <p className="text-gray-500 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-[#667085]">
          {t('balance.availableBalance', { amount: isLoading ? t('balance.loading') : availableBalance.toLocaleString() })}
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Total Tokens Field */}
        <div className="relative">
          <input
            type="text"
            name="totalTokens"
            value={formData.totalTokens}
            onChange={handleInputChange}
            placeholder={componentType === 'burn' ? t('balance.totalBurntCoins') : totalTokensPlaceholder}
            className={`w-full h-[52px] p-4 border rounded focus:outline-none focus:ring-1 transition-all duration-200 text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-[#98A2B3] ${getFieldStyling('totalTokens')}`}
            required
          />
          <AnimatePresence>
            {errors.totalTokens && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.totalTokens}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {componentType !== 'burn' && (
          <>
            {/* Tokens Per Link Field */}
            <div className="relative">
              <input
                type="text"
                name="tokensPerLink"
                value={formData.tokensPerLink}
                onChange={handleInputChange}
                placeholder={tokensPerLinkPlaceholder}
                className={`w-full h-[52px] p-4 border rounded focus:outline-none focus:ring-1 transition-all duration-200 text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-[#98A2B3] ${getFieldStyling('tokensPerLink')}`}
                required
              />
              <AnimatePresence>
                {errors.tokensPerLink && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
                  >
                    <span className="mr-1">⚠</span>
                    {errors.tokensPerLink}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            
            <div className="flex space-x-6">
              <div className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  id="private"
                  name="isPrivate"
                  checked={formData.isPrivate}
                  onChange={handleInputChange}
                  className="h-5 w-5 border-2 border-black mr-2 cursor-pointer"
                />
                <label htmlFor="private" className="cursor-pointer text-neutral-900 text-sm font-semibold font-['IBM_Plex_Sans'] leading-snug text-[#161616]">Private</label>
              </div>
              
              <div className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  id="public"
                  name="isPrivate"
                  checked={!formData.isPrivate}
                  onChange={(e) => setFormData(prev => ({ ...prev, isPrivate: !e.target.checked }))}
                  className="h-5 w-5 border-2 border-black mr-2 cursor-pointer"
                />
                <label htmlFor="public" className="cursor-pointer text-neutral-900 text-sm font-semibold font-['IBM_Plex_Sans'] leading-snug text-[#161616]">Public</label>
              </div>
            </div>
            
            <div className="text-sm text-gray-600 font-['IBM_Plex_Sans']">
              <p>{getPrivacyText(formData.isPrivate)}</p>
            </div>
          </>
        )}
        
        {/* General Error Display */}
        <AnimatePresence>
          {errors.general && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 border border-red-200 rounded-lg p-3"
            >
              <div className="flex items-center">
                <span className="text-red-500 mr-2">⚠</span>
                <p className="text-red-600 font-semibold text-base">
                  {errors.general}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Debug: Show validation status */}
        {(errors.totalTokens || errors.tokensPerLink) && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-yellow-800 text-sm">
              Validation Errors: {errors.totalTokens || errors.tokensPerLink}
            </p>
          </div>
        )}
        
        {showSubmitButton && buttonText && (
          <button
            type="submit"
            disabled={isSubmitting || isValidating || !formData.totalTokens.trim() || (componentType !== 'burn' && !formData.tokensPerLink.trim())}
            className={`px-6 py-3 text-base font-medium font-['Poppins'] leading-normal text-[#FFFFFF] transition-all duration-200 ${
              isSubmitting || isValidating || !formData.totalTokens.trim() || (componentType !== 'burn' && !formData.tokensPerLink.trim())
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-black hover:bg-gray-800'
            }`}
          >
            <div className="flex items-center justify-center">
              {isSubmitting ? 'Processing...' : isValidating ? 'Validating...' : buttonText}
              {(isSubmitting || isValidating) && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              )}
            </div>
          </button>
        )}
      </form>
    </div>
  );
};

// Export both components for use in different contexts
export { CreateAirdropForm };