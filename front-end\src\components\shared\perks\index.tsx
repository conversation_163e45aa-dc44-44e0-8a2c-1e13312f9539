"use client";

import Link from "next/link";
import React from "react";
import dynamic from 'next/dynamic';
import CreateCoinForm from "@/components/auth/create-coin-form";
import { useGlobalModal } from "@/contexts/GlobalModalContext";
import { useTranslation } from "@/hooks/useTranslation";

const Card = dynamic(() => import('../card').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const CreatePerkPage = dynamic(() => import('@/components/shared/create-perk').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

interface PerkType {
  time: string | undefined;
  user: any;
  perkId: number;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isVerified: boolean;
}

type PerksProps = {
  myPerks: PerkType[];
};

const Perks: React.FC<PerksProps> = ({ myPerks }) => {
    const { t } = useTranslation();

const { openModal, closeModal, isModalOpen } = useGlobalModal();
  const handleBuyCoin = (perk: PerkType) => {
    // Add your buy logic here
    console.log("Buy clicked for:", perk);
  };

  const openCreateCoinModal = () => {
    const modalId = openModal({
      id: "create-perk",
      component: React.createElement(CreateCoinForm, {
        onClose: () => {
          closeModal(modalId);
        }
      }),
      onClose: () => {
        closeModal(modalId);
      },
      closeOnBackdropClick: true,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
      modalClassName: 'flex flex-col items-center justify-center p-4',
      disableScroll: true
    });
  };

  return (
    <>
      {isModalOpen("create-perk") && (
        <CreatePerkPage onClose={() => closeModal("create-perk")} />
      )}
      <div className="relative mt-18 ">
        <div className="flex flex-row gap-15 items-center mb-8">
          <h2 className="text-neutral-800 text-4xl font-semibold font-['IBM_Plex_Sans']">
            {t('perksSection.myPerks')}
          </h2>

          <div
            className="w-52 h-14 bg-black flex items-center justify-center cursor-pointer"
            onClick={openCreateCoinModal}
          >
            <span className="text-white text-2xl font-medium font-['Poppins']">
              {t('perksSection.createPerk')}
            </span>
          </div>
        </div>

        {/* ✅ No Data Message */}
        {myPerks.length === 0 ? (
          <div className="text-center text-gray-500 text-xl mt-10">
            {t('perksSection.noPerks')}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
            {myPerks.map((perk) => (

              <Link
                href={`/perks-shop/${perk.perkId}`}
                key={`coin-${perk.perkId}`}
              >
                <Card
                  key={perk.perkId}
                  id={perk.perkId.toString()}
                  isLive={perk.isVerified}
                  name={perk.name}
                  price={`${perk.price} USD`}
                  username={perk.user?.username || "Unknown"}
                  handle={perk.user?.username || "@unknown"}
                  buttonType="view"
                  soldCount={0}
                  remainingCount={100}
                  timeLeft={""}
                  imageUrl={perk.image}
                  onBuyClick={() => handleBuyCoin(perk)}
                />
              </Link>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default Perks;
