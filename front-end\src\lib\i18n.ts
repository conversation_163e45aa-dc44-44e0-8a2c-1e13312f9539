import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Create a singleton instance
const createI18nInstance = () => {
  if (i18n.isInitialized) {
    return i18n;
  }

  i18n
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      fallbackLng: 'en',
      debug: false, // Disable debug for production
      
      interpolation: {
        escapeValue: false,
      },
      
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },
      
      supportedLngs: ['en', 'ar', 'ch'],
      
      ns: ['translation'],
      defaultNS: 'translation',
      
      react: {
        useSuspense: false,
      },
      
      // Backend configuration for loading external files
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
        crossDomain: false,
        withCredentials: false,
        requestOptions: {
          mode: 'cors',
          credentials: 'same-origin',
          cache: 'default'
        }
      },
      
      // Ensure initialization is complete before rendering
      initImmediate: false,
      
      // Preload all languages
      preload: ['en', 'ar', 'ch'],
    });

  return i18n;
};

// Initialize the instance
const i18nInstance = createI18nInstance();

// Preload all translation files
const preloadTranslations = async () => {
  try {
    const languages = ['en', 'ar', 'ch'];
    for (const lang of languages) {
      const response = await fetch(`/locales/${lang}/translation.json`);
      if (response.ok) {
        const translations = await response.json();
        i18nInstance.addResourceBundle(lang, 'translation', translations, true, true);
        console.log(`Loaded translations for ${lang}`);
      } else {
        console.error(`Failed to load translations for ${lang}`);
      }
    }
  } catch (error) {
    console.error('Error preloading translations:', error);
  }
};

// Preload translations when the module is loaded
if (typeof window !== 'undefined') {
  preloadTranslations();
}

export default i18nInstance; 