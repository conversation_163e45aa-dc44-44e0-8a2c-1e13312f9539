"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_auth_create-coin-form_FormFields_tsx";
exports.ids = ["_ssr_src_components_auth_create-coin-form_FormFields_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/auth/create-coin-form/FormFields.tsx":
/*!*************************************************************!*\
  !*** ./src/components/auth/create-coin-form/FormFields.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,DollarSign!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,DollarSign!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/components/auth/create-coin-form/constants.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst FormFields = ({ formData, errors, isSubmitting, showOptions, onToggleOptions, onChange, onBlur })=>{\n    const inputClass = 'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200';\n    const inputErrorClass = 'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4';\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex flex-col justify-start items-start gap-5 w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.name ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    placeholder: t('createCoinForm.name'),\n                    value: formData.name,\n                    onChange: onChange,\n                    onBlur: onBlur,\n                    className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                    required: true,\n                    disabled: isSubmitting\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.ticker ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        name: \"ticker\",\n                        placeholder: t('createCoinForm.ticker'),\n                        value: formData.ticker,\n                        onChange: onChange,\n                        onBlur: onBlur,\n                        className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                        required: true,\n                        disabled: isSubmitting\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 20,\n                        className: \"text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            errors.ticker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.ticker\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.category ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        name: \"category\",\n                        value: formData.category,\n                        onChange: onChange,\n                        onBlur: onBlur,\n                        className: \"flex-1 bg-transparent outline-none w-full text-gray-700\",\n                        required: true,\n                        disabled: isSubmitting,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                className: \"text-gray-400\",\n                                children: t('createCoinForm.selectCategory')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            _constants__WEBPACK_IMPORTED_MODULE_2__.FORM_CATEGORIES.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: category,\n                                    className: \"text-gray-700\",\n                                    children: category\n                                }, category, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 20,\n                        className: \"text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: t('createCoinForm.pleaseSelectCategory')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"self-stretch h-36 py-0 pl-4 pr-4 relative rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-start gap-3 w-full mx-auto focus-within:outline-[#F58A38] transition-all duration-200\",\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    name: \"description\",\n                    placeholder: t('createCoinForm.description'),\n                    value: formData.description,\n                    onChange: onChange,\n                    onBlur: onBlur,\n                    className: \"flex-1 bg-transparent outline-none w-full pt-4 resize-none text-gray-700 placeholder-gray-400\",\n                    required: true,\n                    disabled: isSubmitting\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"w-full mx-auto flex items-center\",\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                    type: \"button\",\n                    onClick: onToggleOptions,\n                    className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed focus:outline-none flex items-center gap-2 hover:text-[#F58A38] transition-colors\",\n                    whileHover: {\n                        scale: 1.01\n                    },\n                    disabled: isSubmitting,\n                    children: [\n                        showOptions ? t('createCoinForm.hideMoreOptions') : t('createCoinForm.showMoreOptions'),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                rotate: showOptions ? 180 : 0\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            showOptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"w-full space-y-5\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.2\n                },\n                children: [\n                    {\n                        name: 'telegram',\n                        placeholder: t('createCoinForm.telegram'),\n                        error: errors.telegram\n                    },\n                    {\n                        name: 'website',\n                        placeholder: t('createCoinForm.website'),\n                        error: errors.website\n                    },\n                    {\n                        name: 'twitter',\n                        placeholder: t('createCoinForm.twitter'),\n                        error: errors.twitter\n                    }\n                ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: field.error ? inputErrorClass : inputClass,\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.15\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: field.name,\n                                    placeholder: field.placeholder,\n                                    value: formData[field.name],\n                                    onChange: onChange,\n                                    onBlur: onBlur,\n                                    className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined),\n                            field.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"text-red-500 text-sm w-full mt-1\",\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                transition: {\n                                    duration: 0.15\n                                },\n                                children: field.error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, field.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, undefined),\n            errors.api && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                className: \"text-red-500 font-semibold text-base w-full mx-auto\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.api\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormFields);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/create-coin-form/FormFields.tsx\n");

/***/ })

};
;