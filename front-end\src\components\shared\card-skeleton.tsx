"use client";

import React from "react";

interface CardSkeletonProps {
  buttonType?: "buy" | "view";
  borderless?: boolean;
  showPurchased?: boolean;
}

const CardSkeleton: React.FC<CardSkeletonProps> = ({
  buttonType = "buy",
  borderless = false,
  showPurchased = false,
}) => {
  return (
    <div
      className={`relative rounded-xl ${
        !borderless ? "border border-[#FF6600]" : ""
      } transition-all duration-500 ease-in-out`}
    >
      <div className="p-4 flex flex-col gap-5">
        <div className="w-[100%] aspect-[1.55/1] rounded-lg overflow-hidden">
          <div className="w-[100%] aspect-[1.55/1] relative bg-gray-200 animate-pulse">
            <div className="absolute bottom-4 left-4">
              <div className="h-7 w-32 bg-gray-300 rounded-md animate-pulse"></div>
            </div>
          </div>

          <div className="w-auto flex absolute right-[28px] top-[24px] items-center justify-center gap-2">
            {!showPurchased && (
              <>
                <div className="h-7 w-20 bg-white/30 rounded-lg backdrop-blur-[20px] animate-pulse"></div>
                <div className="w-7 h-7 bg-white/30 rounded-[50px] backdrop-blur-[1.50px] animate-pulse"></div>
              </>
            )}
          </div>
        </div>

        <div className="w-[100%] h-[120px] flex flex-col gap-4">
          <div className="w-[100%] h-14">
            <div className="w-[100%] h-14 flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="w-14 h-14 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="flex flex-col">
                  <div className="w-24 h-5 bg-gray-200 rounded-md animate-pulse mb-2"></div>
                  <div className="w-16 h-4 bg-gray-100 rounded-md animate-pulse"></div>
                </div>
              </div>
              <div>
                <div 
                  className={`h-7 rounded-[40px] ${
                    buttonType === "buy" ? "w-16 bg-gray-200" : "w-24 bg-gray-200"
                  } animate-pulse`}
                ></div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <div className="w-10 h-4 bg-gray-100 rounded-md animate-pulse mb-2"></div>
              <div className="w-16 h-5 bg-gray-200 rounded-md animate-pulse"></div>
            </div>

            <div>
              <div className="w-20 h-4 bg-gray-100 rounded-md animate-pulse mb-2"></div>
              <div className="w-24 h-5 bg-gray-200 rounded-md animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardSkeleton;
