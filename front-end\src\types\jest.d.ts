/// <reference types="jest" />

declare global {
  var jest: typeof import('jest');
  var expect: typeof import('expect');
  var describe: typeof import('jest').describe;
  var it: typeof import('jest').it;
  var test: typeof import('jest').test;
  var beforeEach: typeof import('jest').beforeEach;
  var afterEach: typeof import('jest').afterEach;
  var beforeAll: typeof import('jest').beforeAll;
  var afterAll: typeof import('jest').afterAll;
}

// React Testing Library types
declare module '@testing-library/react' {
  export function render(ui: React.ReactElement, options?: any): any;
  export function renderHook(hook: () => any, options?: any): any;
  export const screen: {
    getByText: (text: string | RegExp) => HTMLElement;
    getByTestId: (id: string) => HTMLElement;
    getByRole: (role: string, options?: any) => HTMLElement;
    queryByTestId: (id: string) => HTMLElement | null;
    queryByText: (text: string | RegExp) => HTMLElement | null;
    [key: string]: any;
  };
  export function fireEvent(element: HTMLElement, event?: any): void;
  export namespace fireEvent {
    export function click(element: HTMLElement): void;
    export function change(element: HTMLElement, options: any): void;
    export function blur(element: HTMLElement): void;
    export function focus(element: HTMLElement): void;
  }
  export function waitFor(callback: () => void, options?: any): Promise<void>;
  export function act(callback: () => void | Promise<void>): Promise<void>;
}

declare module '@testing-library/user-event' {
  const userEvent: {
    setup: () => any;
    type: (element: HTMLElement, text: string) => Promise<void>;
    click: (element: HTMLElement) => Promise<void>;
    upload: (element: HTMLInputElement, file: File | File[]) => Promise<void>;
    selectOptions: (element: HTMLSelectElement, values: string | string[]) => Promise<void>;
    [key: string]: any;
  };
  export default userEvent;
}

export {};
