'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, DollarSign } from 'lucide-react';
import { FORM_CATEGORIES } from '../../../constants';
import { formFieldVariants } from './constants';
import { useTranslation } from '@/hooks/useTranslation';

interface FormFieldsProps {
  formData: {
    name: string;
    ticker: string;
    description: string;
    picture: File | null;
    telegram: string;
    website: string;
    twitter: string;
    category: string;
  };
  errors: {
    name: string;
    ticker: string;
    description: string;
    picture: string;
    telegram: string;
    website: string;
    twitter: string;
    category: string;
    api: string;
  };
  isSubmitting: boolean;
  showOptions: boolean;
  onToggleOptions: () => void;
  onChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  onBlur: (
    e: React.FocusEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
}

const FormFields: React.FC<FormFieldsProps> = ({
  formData,
  errors,
  isSubmitting,
  showOptions,
  onToggleOptions,
  onChange,
  onBlur,
}) => {
  const inputClass =
    'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200';
  const inputErrorClass =
    'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4';
  const { t } = useTranslation();

  return (
    <div className="inline-flex flex-col justify-start items-start gap-5 w-full">
      {/* Name Field */}
      <motion.div
        className={errors.name ? inputErrorClass : inputClass}
        variants={formFieldVariants}
      >
        <input
          type="text"
          name="name"
          placeholder={t('createCoinForm.name')}
          value={formData.name}
          onChange={onChange}
          onBlur={onBlur}
          className="flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400"
          required
          disabled={isSubmitting}
        />
      </motion.div>
      {errors.name && (
        <motion.div
          className="text-red-500 text-sm w-full"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.15 }}
        >
          {errors.name}
        </motion.div>
      )}

      {/* Ticker Field */}
      <motion.div
        className={errors.ticker ? inputErrorClass : inputClass}
        variants={formFieldVariants}
      >
        <input
          type="text"
          name="ticker"
          placeholder={t('createCoinForm.ticker')}
          value={formData.ticker}
          onChange={onChange}
          onBlur={onBlur}
          className="flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400"
          required
          disabled={isSubmitting}
        />
        <DollarSign size={20} className="text-gray-400" />
      </motion.div>
      {errors.ticker && (
        <motion.div
          className="text-red-500 text-sm w-full"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.15 }}
        >
          {errors.ticker}
        </motion.div>
      )}

      {/* Category Field */}
      <motion.div
        className={errors.category ? inputErrorClass : inputClass}
        variants={formFieldVariants}
      >
        <select
          name="category"
          value={formData.category}
          onChange={onChange}
          onBlur={onBlur}
          className="flex-1 bg-transparent outline-none w-full text-gray-700"
          required
          disabled={isSubmitting}
        >
          <option value="" disabled className="text-gray-400">
            {t('createCoinForm.selectCategory')}
          </option>
          {FORM_CATEGORIES.map((category) => (
            <option key={category} value={category} className="text-gray-700">
              {category}
            </option>
          ))}
        </select>
        <ChevronDown size={20} className="text-gray-400" />
      </motion.div>
      {errors.category && (
        <motion.div
          className="text-red-500 text-sm w-full"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.15 }}
        >
          {t('createCoinForm.pleaseSelectCategory')}
        </motion.div>
      )}

      {/* Description Field */}
      <motion.div
        className="self-stretch h-36 py-0 pl-4 pr-4 relative rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-start gap-3 w-full mx-auto focus-within:outline-[#F58A38] transition-all duration-200"
        variants={formFieldVariants}
      >
        <textarea
          name="description"
          placeholder={t('createCoinForm.description')}
          value={formData.description}
          onChange={onChange}
          onBlur={onBlur}
          className="flex-1 bg-transparent outline-none w-full pt-4 resize-none text-gray-700 placeholder-gray-400"
          required
          disabled={isSubmitting}
        />
      </motion.div>

      {/* More Options Toggle */}
      <motion.div
        className="w-full mx-auto flex items-center"
        variants={formFieldVariants}
      >
        <motion.button
          type="button"
          onClick={onToggleOptions}
          className="justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed focus:outline-none flex items-center gap-2 hover:text-[#F58A38] transition-colors"
          whileHover={{ scale: 1.01 }}
          disabled={isSubmitting}
        >
          {showOptions ? t('createCoinForm.hideMoreOptions') : t('createCoinForm.showMoreOptions')}
          <motion.div
            animate={{ rotate: showOptions ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown size={20} />
          </motion.div>
        </motion.button>
      </motion.div>

      {/* Optional Fields */}
      {showOptions && (
        <motion.div
          className="w-full space-y-5"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
        >
          {[
            {
              name: 'telegram',
              placeholder: t('createCoinForm.telegram'),
              error: errors.telegram,
            },
            {
              name: 'website',
              placeholder: t('createCoinForm.website'),
              error: errors.website,
            },
            {
              name: 'twitter',
              placeholder: t('createCoinForm.twitter'),
              error: errors.twitter,
            },
          ].map((field) => (
            <div key={field.name}>
              <motion.div
                className={field.error ? inputErrorClass : inputClass}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.15 }}
              >
                <input
                  type="text"
                  name={field.name}
                  placeholder={field.placeholder}
                  value={
                    formData[field.name as keyof typeof formData] as string
                  }
                  onChange={onChange}
                  onBlur={onBlur}
                  className="flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400"
                  disabled={isSubmitting}
                />
              </motion.div>
              {field.error && (
                <motion.div
                  className="text-red-500 text-sm w-full mt-1"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.15 }}
                >
                  {field.error}
                </motion.div>
              )}
            </div>
          ))}
        </motion.div>
      )}

      {/* API Error */}
      {errors.api && (
        <motion.p
          className="text-red-500 font-semibold text-base w-full mx-auto"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.15 }}
        >
          {errors.api}
        </motion.p>
      )}
    </div>
  );
};

export default FormFields;
