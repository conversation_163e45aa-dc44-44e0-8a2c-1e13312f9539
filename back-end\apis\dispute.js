const { Router } = require("express");
const {
  initiateDispute,
  getDisputes,
  assignDispute,
  resolveDispute,
  getDisputeDetails
} = require("../controller/dispute");
const { body, param, query } = require('express-validator');
const { validate } = require("../middleware/validator");
const { authenticate } = require("../middleware/authenticate");
const {
  requireModerator,
  requireDisputeInitiationAccess,
  requireTradeAccess
} = require("../middleware/roleAuth");

const router = Router();

// Initiate a dispute
router.post(
  "/initiate",
  authenticate,
  [
    body("tradeId")
      .isInt({ min: 1 })
      .withMessage("Trade ID must be a positive integer"),
    body("reason")
      .isLength({ min: 10, max: 500 })
      .withMessage("Reason must be between 10 and 500 characters"),
    body("initiatorRole")
      .isIn(['buyer', 'seller'])
      .withMessage("Initiator role must be buyer or seller"),
  ],
  validate,
  requireDisputeInitiationAccess,
  initiateDispute
);

// Get all disputes (moderators only)
router.get(
  "/",
  authenticate,
  requireModerator,
  [
    query("status")
      .optional()
      .isIn(['open', 'assigned', 'resolved_buyer', 'resolved_seller', 'resolved_split'])
      .withMessage("Invalid status"),
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("pageSize")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Page size must be between 1 and 100"),
  ],
  validate,
  getDisputes
);

// Get dispute details
router.get(
  "/:disputeId",
  authenticate,
  [
    param("disputeId")
      .isInt({ min: 1 })
      .withMessage("Dispute ID must be a positive integer"),
  ],
  validate,
  getDisputeDetails
);

// Assign dispute to moderator
router.post(
  "/:disputeId/assign",
  authenticate,
  requireModerator,
  [
    param("disputeId")
      .isInt({ min: 1 })
      .withMessage("Dispute ID must be a positive integer"),
  ],
  validate,
  assignDispute
);

// Resolve dispute
router.post(
  "/:disputeId/resolve",
  authenticate,
  requireModerator,
  [
    param("disputeId")
      .isInt({ min: 1 })
      .withMessage("Dispute ID must be a positive integer"),
    body("buyerPercentage")
      .isInt({ min: 0, max: 100 })
      .withMessage("Buyer percentage must be between 0 and 100"),
    body("sellerPercentage")
      .isInt({ min: 0, max: 100 })
      .withMessage("Seller percentage must be between 0 and 100"),
    body("resolution")
      .isIn(['resolved_buyer', 'resolved_seller', 'resolved_split'])
      .withMessage("Invalid resolution type"),
    body("moderatorNotes")
      .optional()
      .isLength({ max: 1000 })
      .withMessage("Moderator notes must be less than 1000 characters"),
  ],
  validate,
  resolveDispute
);

module.exports = router;
