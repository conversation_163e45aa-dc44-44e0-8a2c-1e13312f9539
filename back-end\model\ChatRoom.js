const { Model, DataTypes } = require("sequelize");

class Chat<PERSON><PERSON> extends Model {
    static initModel(sequelize) {
        return ChatRoom.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                chatRoomId: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                    unique: true,
                },
                buyerId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                sellerId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                perkId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'chat_rooms',
                timestamps: true,
            }
        );
    }
}

module.exports = ChatRoom; 