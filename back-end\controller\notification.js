const dataContext = require("../db");

/**
 * Get notifications for the authenticated user
 */
exports.getNotifications = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page = 1, pageSize = 20, unreadOnly = false } = req.query;

    const notifications = await dataContext.Notification.getUserNotifications(userId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      unreadOnly: unreadOnly === 'true'
    });

    // Debug: Log notification types being returned
    const typeCounts = {};
    notifications.rows.forEach(n => {
      typeCounts[n.type] = (typeCounts[n.type] || 0) + 1;
    });
    // console.log('📊 [NotificationController] Returning notifications:', {
    //   userId,
    //   totalCount: notifications.count,
    //   typeCounts,
    //   recentTypes: notifications.rows.slice(0, 5).map(n => ({ id: n.id, type: n.type, title: n.title }))
    // });

    res.status(200).json({
      status: 200,
      message: 'Notifications retrieved successfully',
      data: {
        notifications: notifications.rows,
        pagination: {
          total: notifications.count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(notifications.count / pageSize)
        }
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get unread notification count for the authenticated user
 */
exports.getUnreadCount = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const count = await dataContext.Notification.getUnreadCount(userId);

    res.status(200).json({
      status: 200,
      message: 'Unread count retrieved successfully',
      data: { unreadCount: count }
    });

  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Mark a notification as read
 */
exports.markAsRead = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { notificationId } = req.params;

    const notification = await dataContext.Notification.findOne({
      where: { id: notificationId, userId }
    });

    if (!notification) {
      return res.status(404).json({ 
        status: 404, 
        message: 'Notification not found' 
      });
    }

    await notification.markAsRead();

    res.status(200).json({
      status: 200,
      message: 'Notification marked as read',
      data: { notificationId }
    });

  } catch (error) {
    console.error('Mark as read error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Mark all notifications as read for the authenticated user
 */
exports.markAllAsRead = async (req, res, next) => {
  try {
    const userId = req.user.id;

    await dataContext.Notification.update(
      { isRead: true },
      { 
        where: { 
          userId, 
          isRead: false 
        } 
      }
    );

    res.status(200).json({
      status: 200,
      message: 'All notifications marked as read'
    });

  } catch (error) {
    console.error('Mark all as read error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Delete a notification
 */
exports.deleteNotification = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { notificationId } = req.params;

    const notification = await dataContext.Notification.findOne({
      where: { id: notificationId, userId }
    });

    if (!notification) {
      return res.status(404).json({ 
        status: 404, 
        message: 'Notification not found' 
      });
    }

    await notification.destroy();

    res.status(200).json({
      status: 200,
      message: 'Notification deleted successfully',
      data: { notificationId }
    });

  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Create a system announcement (admin only)
 */
exports.createAnnouncement = async (req, res, next) => {
  try {
    const { title, message, priority = 'medium', targetRole = 'all', expiresAt } = req.body;

    let targetUsers = [];

    if (targetRole === 'moderators') {
      // Send to all active moderators
      const moderators = await dataContext.Moderator.findAll({
        where: { isActive: true },
        include: [{ model: dataContext.User, as: 'user' }]
      });
      targetUsers = moderators.map(mod => mod.userId);
    } else if (targetRole === 'all') {
      // Send to all users (increased limit for system announcements)
      const users = await dataContext.User.findAll({
        attributes: ['id'],
        limit: 5000 // Increased limit for system-wide announcements
      });
      targetUsers = users.map(user => user.id);
    }

    if (targetUsers.length === 0) {
      return res.status(400).json({
        status: 400,
        message: 'No target users found'
      });
    }

    const notifications = targetUsers.map(userId => ({
      userId,
      type: 'system_announcement',
      title,
      message,
      priority,
      expiresAt: expiresAt ? new Date(expiresAt) : null
    }));

    await dataContext.Notification.bulkCreate(notifications);

    // Broadcast announcement via Socket.IO for real-time updates
    if (global.broadcastSystemNotification) {
      global.broadcastSystemNotification({
        type: 'system_announcement',
        title,
        message,
        priority,
        targetRole,
        timestamp: new Date().toISOString()
      });
    }

    res.status(201).json({
      status: 201,
      message: 'Announcement sent successfully',
      data: {
        recipientCount: targetUsers.length,
        targetRole
      }
    });

  } catch (error) {
    console.error('Create announcement error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Create a system-wide notification for all users (admin only)
 */
exports.createSystemNotification = async (req, res, next) => {
  try {
    const { type, title, message, priority = 'medium', actionUrl, data = {} } = req.body;

    // Validate notification type
    const validSystemTypes = ['system_announcement', 'perk_created', 'token_created', 'moderator_added'];
    if (!validSystemTypes.includes(type)) {
      return res.status(400).json({
        status: 400,
        message: 'Invalid notification type for system notifications'
      });
    }

    const notificationCount = await dataContext.Notification.createSystemNotificationForAllUsers(
      type,
      title,
      message,
      data,
      priority,
      actionUrl
    );

    // Note: Broadcasting is already handled in createSystemNotificationForAllUsers function

    res.status(201).json({
      status: 201,
      message: 'System notification sent successfully',
      data: {
        recipientCount: notificationCount,
        type,
        title
      }
    });

  } catch (error) {
    console.error('Create system notification error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};
