import React from 'react';

interface TokenDataProps {
    amount: string;
    price: string;
    tokenName: string;
    percentChange: string;
    showPositive?: boolean;
}

const TokenData: React.FC<TokenDataProps> = ({
    amount,
    price,
    tokenName,
    percentChange,
    showPositive = true
}) => (
    <div className="p-4 sm:p-5 md:p-6 bg-slate-100 rounded-xl sm:rounded-2xl flex flex-col justify-start items-start gap-4 sm:gap-5 md:gap-6 w-full">
        <div className="flex flex-col justify-start items-start gap-2 sm:gap-3 md:gap-4">
            <div className="flex justify-start items-center gap-2">
                <div className="text-zinc-950 font-medium text-2xl sm:text-2xl md:text-3xl">{amount}</div>
            </div>
            <div className="text-zinc-950 font-medium text-2xl sm:text-2xl md:text-3xl">{price}</div>
        </div>
        <div className="flex justify-between items-center w-full">
            <div className="text-zinc-800 font-medium text-lg sm:text-xl">{tokenName}</div>
            <div className={`font-bold text-xl sm:text-2xl ${showPositive ? 'text-[#3BB266]' : 'text-red-500'}`}>
                {showPositive ? '+' : ''}{percentChange}
            </div>
        </div>
    </div>
);

export default TokenData;