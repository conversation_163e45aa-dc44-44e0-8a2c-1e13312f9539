import { IbmFont } from '@/constants/layout';
import { BASE_METADATA, STRUCTURED_DATA } from '@/constants/seo';
import Footer from '@/components/shared/footer';
import Header from '@/components/shared/header';
import RunningLine from '@/components/ui/runningLine';
import { AppProvider } from '@/contexts/AppContext';
import { PrivyContextProvider } from '@/contexts/PrivyContext';
import { GlobalModalProvider } from '@/contexts/GlobalModalContext';
import '@/styles/globals.css';
import React from 'react';
import { SocketProvider } from '@/contexts/SocketProvider';
import { UnreadChatMessagesProvider } from '@/contexts/AppContext';
import ChatNotificationListener from '@/components/shared/chat/ChatNotificationListener';
import I18nInitializer from '@/components/shared/I18nInitializer';
import type { Metadata } from 'next';

export const metadata: Metadata = BASE_METADATA;

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        {/* Structured Data for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(STRUCTURED_DATA.organization)
          }}
        />
        {/* Structured Data for Website */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(STRUCTURED_DATA.website)
          }}
        />
      </head>
      <body className={`${IbmFont.className} antialiased`}>
        <PrivyContextProvider>
          <AppProvider>
            <SocketProvider>
              <UnreadChatMessagesProvider>
                <GlobalModalProvider>
                  <I18nInitializer />
                  <ChatNotificationListener />
                  <Header />
                  <RunningLine />
                  <div className="max-w-[1902px] px-4 mx-auto pb-4 md:px-8 md:pb-8">
                    {children}
                  </div>
                  <Footer />
                  {/* <ChatPanelWrapper /> */}
                </GlobalModalProvider>
              </UnreadChatMessagesProvider>
            </SocketProvider>
          </AppProvider>
        </PrivyContextProvider>
      </body>
    </html>
  );
}
