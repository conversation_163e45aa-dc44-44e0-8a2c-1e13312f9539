import Image from "next/image";
import React, { useState } from "react";
import { useTranslation } from '@/hooks/useTranslation';

const VerifyAccountSection = () => {
  const { t } = useTranslation();
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setUploadedImage(imageUrl);
    }
  };

  return (
    <div>
      <h2 className="font-['IBM_Plex_Sans'] font-semibold text-[48px] leading-[48px] md:leading-[36px] text-[#17181A] mb-6">
        {t('verifyAccount.title')}
      </h2>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Left side - Example image */}
        <div className="flex-1">
          <div className="font-['IBM_Plex_Sans'] font-semibold text-[20px] text-[#000000] mb-6 max-w-[600px]">
            <p>{t('verifyAccount.uploadSelfie')}</p>
            <p>{t('verifyAccount.addToBio')}</p>
            <p>
              <a href="#" className="text-[#525FEB] hover:underline">
                {t('common.contactUsHere', 'contact us here')}
              </a>
            </p>
          </div>
          <div className="bg-blue-100 p-4 rounded flex items-center justify-center h-[300px]">
            <div className="flex flex-col items-center justify-center text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-20 w-20 text-blue-500 mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <p className="text-lg font-medium text-gray-700">
                Example Selfie
              </p>
              <p className="text-sm text-gray-600 mt-2">
                Hold a piece of paper with "funhi" written on it
              </p>
            </div>
          </div>
        </div>

        {/* Right side - Upload area */}
        <div className="flex-1 mt-3">
          <div className="flex-1  mt-6 mb-6">
            <div className=" h-[54px] border border-[#D9E1E7] rounded-[10px] flex items-center justify-between px-4">
              <label className="bg-gray-200 text-[#809FB8] px-4 py-2 rounded cursor-pointer hover:bg-gray-300 rounded-[10px]">
                Upload
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleImageUpload}
                />
              </label>
            </div>
          </div>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center h-[300px]">
            {uploadedImage ? (
              <div className="relative w-full h-full">
                <Image
                  src={uploadedImage}
                  alt="Your uploaded picture"
                  fill
                  className="object-contain"
                />
                <button
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                  onClick={() => setUploadedImage(null)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ) : (
              <>
                <p className="text-center text-gray-700 font-medium mb-4">
                  Your picture
                </p>

              </>
            )}
          </div>
          <p className="font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#000000] mt-2 text-center">
            Image will always appear on your profile as proof of fans
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerifyAccountSection;
