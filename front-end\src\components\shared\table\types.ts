export interface TableColumn<T = any> {
  key: string;
  header: string;
  align?: 'left' | 'center' | 'right';
  width?: string;
  sortable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
}

export interface TableRow {
  id: string | number;
  [key: string]: any;
}

export interface TableProps<T = TableRow> {
  title?: string;
  columns: TableColumn<T>[];
  data: T[];
  className?: string;

  // Pagination
  currentPage?: number;
  totalPages?: number;
  rowsPerPage?: number;
  onPageChange?: (page: number) => void;

  // States
  loading?: boolean;
  error?: string | null;

  // Empty state
  emptyMessage?: string | null;
  emptyIcon?: React.ReactNode;

  // Styling
  variant?: 'default' | 'minimal' | 'bordered';
  size?: 'sm' | 'md' | 'lg';

  // Interactions
  onRowClick?: (row: T, index: number) => void;
  selectable?: boolean;
  selectedRows?: Set<string | number>;
  onSelectionChange?: (selectedRows: Set<string | number>) => void;
} 