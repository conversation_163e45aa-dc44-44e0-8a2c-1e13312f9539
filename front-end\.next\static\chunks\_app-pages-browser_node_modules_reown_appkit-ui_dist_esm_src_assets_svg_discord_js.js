"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_discord_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   discordSvg: () => (/* binding */ discordSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst discordSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#5865F2\" />\n      <path\n        fill=\"#fff\"\n        fill-rule=\"evenodd\"\n        d=\"M25.71 28.15C30.25 28 32 25.02 32 25.02c0-6.61-2.96-11.98-2.96-11.98-2.96-2.22-5.77-2.15-5.77-2.15l-.29.32c3.5 1.07 5.12 2.61 5.12 2.61a16.75 16.75 0 0 0-10.34-1.93l-.35.04a15.43 15.43 0 0 0-5.88 1.9s1.71-1.63 5.4-2.7l-.2-.24s-2.81-.07-5.77 2.15c0 0-2.96 5.37-2.96 11.98 0 0 1.73 2.98 6.27 3.13l1.37-1.7c-2.6-.79-3.6-2.43-3.6-2.43l.***********.***********.08.05a17.25 17.25 0 0 0 4.52 1.58 14.4 14.4 0 0 0 8.3-.86c.72-.27 1.52-.66 2.37-1.21 0 0-1.03 1.68-3.72 ********** 1.35 1.67 1.35 1.67Zm-9.55-9.6c-1.17 0-2.1 1.03-2.1 2.28 0 1.25.95 2.28 2.1 2.28 1.17 0 2.1-1.03 2.1-2.28.01-1.25-.93-2.28-2.1-2.28Zm7.5 0c-1.17 0-2.1 1.03-2.1 2.28 0 1.25.95 2.28 2.1 2.28 1.17 0 2.1-1.03 2.1-2.28 0-1.25-.93-2.28-2.1-2.28Z\"\n        clip-rule=\"evenodd\"\n      />\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=discord.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2Rpc2NvcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsbUJBQW1CLHdDQUFHO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXEByZW93blxcYXBwa2l0LXVpXFxkaXN0XFxlc21cXHNyY1xcYXNzZXRzXFxzdmdcXGRpc2NvcmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBkaXNjb3JkU3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCA0MCA0MFwiPlxuICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2EpXCI+XG4gICAgPGcgY2xpcC1wYXRoPVwidXJsKCNiKVwiPlxuICAgICAgPGNpcmNsZSBjeD1cIjIwXCIgY3k9XCIxOS44OVwiIHI9XCIyMFwiIGZpbGw9XCIjNTg2NUYyXCIgLz5cbiAgICAgIDxwYXRoXG4gICAgICAgIGZpbGw9XCIjZmZmXCJcbiAgICAgICAgZmlsbC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgICAgIGQ9XCJNMjUuNzEgMjguMTVDMzAuMjUgMjggMzIgMjUuMDIgMzIgMjUuMDJjMC02LjYxLTIuOTYtMTEuOTgtMi45Ni0xMS45OC0yLjk2LTIuMjItNS43Ny0yLjE1LTUuNzctMi4xNWwtLjI5LjMyYzMuNSAxLjA3IDUuMTIgMi42MSA1LjEyIDIuNjFhMTYuNzUgMTYuNzUgMCAwIDAtMTAuMzQtMS45M2wtLjM1LjA0YTE1LjQzIDE1LjQzIDAgMCAwLTUuODggMS45czEuNzEtMS42MyA1LjQtMi43bC0uMi0uMjRzLTIuODEtLjA3LTUuNzcgMi4xNWMwIDAtMi45NiA1LjM3LTIuOTYgMTEuOTggMCAwIDEuNzMgMi45OCA2LjI3IDMuMTNsMS4zNy0xLjdjLTIuNi0uNzktMy42LTIuNDMtMy42LTIuNDNsLjU4LjM1LjA5LjA2LjA4LjA0LjAyLjAxLjA4LjA1YTE3LjI1IDE3LjI1IDAgMCAwIDQuNTIgMS41OCAxNC40IDE0LjQgMCAwIDAgOC4zLS44NmMuNzItLjI3IDEuNTItLjY2IDIuMzctMS4yMSAwIDAtMS4wMyAxLjY4LTMuNzIgMi40NC42MS43OCAxLjM1IDEuNjcgMS4zNSAxLjY3Wm0tOS41NS05LjZjLTEuMTcgMC0yLjEgMS4wMy0yLjEgMi4yOCAwIDEuMjUuOTUgMi4yOCAyLjEgMi4yOCAxLjE3IDAgMi4xLTEuMDMgMi4xLTIuMjguMDEtMS4yNS0uOTMtMi4yOC0yLjEtMi4yOFptNy41IDBjLTEuMTcgMC0yLjEgMS4wMy0yLjEgMi4yOCAwIDEuMjUuOTUgMi4yOCAyLjEgMi4yOCAxLjE3IDAgMi4xLTEuMDMgMi4xLTIuMjggMC0xLjI1LS45My0yLjI4LTIuMS0yLjI4WlwiXG4gICAgICAgIGNsaXAtcnVsZT1cImV2ZW5vZGRcIlxuICAgICAgLz5cbiAgICA8L2c+XG4gIDwvZz5cbiAgPGRlZnM+XG4gICAgPGNsaXBQYXRoIGlkPVwiYVwiPjxyZWN0IHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIGZpbGw9XCIjZmZmXCIgcng9XCIyMFwiIC8+PC9jbGlwUGF0aD5cbiAgICA8Y2xpcFBhdGggaWQ9XCJiXCI+PHBhdGggZmlsbD1cIiNmZmZcIiBkPVwiTTAgMGg0MHY0MEgwelwiIC8+PC9jbGlwUGF0aD5cbiAgPC9kZWZzPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpc2NvcmQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js\n"));

/***/ })

}]);