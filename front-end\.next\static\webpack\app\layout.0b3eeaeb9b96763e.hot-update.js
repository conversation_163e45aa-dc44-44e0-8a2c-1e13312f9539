"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ChatModalStateContext */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { updateModalProps, getChatModalState } = (0,_contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber1 = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber1) return 'buyer';\n        if (notification.data.sellerId === userIdNumber1) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Unified function to normalize notification data and determine user roles\n    const normalizeNotificationData = async (notification, currentUserId)=>{\n        console.log('🔍 [NotificationBell] Normalizing notification data:', {\n            type: notification.type,\n            data: notification.data\n        });\n        if (!notification.data) {\n            throw new Error('Notification data is missing');\n        }\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        let tradeId = notification.data.tradeId;\n        let chatRoomId = notification.data.chatRoomId;\n        let perkId = notification.data.perkId;\n        // Handle different notification types with unified logic\n        switch(notification.type){\n            case 'perk_purchased':\n            case 'escrow_created':\n                // Current user is buyer, notification contains seller info\n                buyerId = currentUserId;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n                break;\n            case 'perk_sold':\n            case 'escrow_pending_acceptance':\n            case 'escrow_accepted':\n                // Current user is seller, notification contains buyer info\n                sellerId = currentUserId;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n                break;\n            case 'escrow_released':\n            case 'trade_completed':\n                // Could be either buyer or seller, determine from notification data\n                if (notification.data.buyerId === currentUserId) {\n                    buyerId = currentUserId;\n                    sellerId = notification.data.sellerId;\n                } else {\n                    sellerId = currentUserId;\n                    buyerId = notification.data.buyerId;\n                }\n                break;\n            case 'chat_message':\n                // Determine roles from sender/receiver\n                if (notification.data.senderId === currentUserId) {\n                    // I sent the message, I could be buyer or seller\n                    if (notification.data.buyerId === currentUserId) {\n                        buyerId = currentUserId;\n                        sellerId = notification.data.sellerId || notification.data.receiverId;\n                    } else {\n                        sellerId = currentUserId;\n                        buyerId = notification.data.buyerId || notification.data.receiverId;\n                    }\n                } else {\n                    // I received the message\n                    if (notification.data.receiverId === currentUserId) {\n                        if (notification.data.buyerId === currentUserId) {\n                            buyerId = currentUserId;\n                            sellerId = notification.data.sellerId || notification.data.senderId;\n                        } else {\n                            sellerId = currentUserId;\n                            buyerId = notification.data.buyerId || notification.data.senderId;\n                        }\n                    }\n                }\n                break;\n            default:\n                // Fallback logic for other types\n                if (!buyerId || !sellerId) {\n                    if (notification.data.buyerId === currentUserId) {\n                        buyerId = currentUserId;\n                        sellerId = notification.data.sellerId || notification.data.receiverId;\n                    } else {\n                        sellerId = currentUserId;\n                        buyerId = notification.data.buyerId || notification.data.senderId;\n                    }\n                }\n        }\n        // Generate chatRoomId if not provided\n        if (!chatRoomId && buyerId && sellerId && perkId) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId:', chatRoomId);\n        }\n        // Try to find tradeId if not provided\n        if (!tradeId && chatRoomId) {\n            try {\n                const chatRoomParts = chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const extractedPerkId = chatRoomParts[2];\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(userIdNumber));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(extractedPerkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeId = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found tradeId from chatRoomId:', tradeId);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find tradeId from chatRoomId:', error);\n            }\n        }\n        return {\n            buyerId,\n            sellerId,\n            tradeId,\n            chatRoomId,\n            perkId: perkId || notification.data.perkId\n        };\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber1 = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber1; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber1) {\n                buyerId = userIdNumber1;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber1;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber: userIdNumber1\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId,\n            hasTradeId: !!notification.data.tradeId,\n            hasChatRoomId: !!notification.data.chatRoomId,\n            hasPerkId: !!notification.data.perkId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Use getUserTrades to find trades for current user and filter by perkId\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(myUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        // Find active trade for this perk\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(perkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            // Get the most recent trade\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeIdToUse = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                var _tradeResponse_data;\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data,\n                    tradeDataId: (_tradeResponse_data = tradeResponse.data) === null || _tradeResponse_data === void 0 ? void 0 : _tradeResponse_data.id,\n                    tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    // Ensure we have a valid ID - use tradeIdToUse as fallback\n                    const validId = tradeResponse.data.id || tradeIdToUse;\n                    activeTrade = {\n                        id: validId,\n                        status: tradeResponse.data.status,\n                        tradeId: validId,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to notification data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: notification.data.status || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: notification.data.perkId,\n                        escrowId: notification.data.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, update it instead of creating new one\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, updating with new state');\n            // Update the existing modal with new props\n            const newProps = {\n                activeTrade,\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onRelease: tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n                    alert('Cannot release escrow: Trade information not available');\n                },\n                onRefund: tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n                    alert('Cannot refund escrow: Trade information not available');\n                },\n                onAccept: tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n                    alert('Cannot accept escrow: Trade information not available');\n                }\n            };\n            // Update modal props through the state context\n            const updated = updateModalProps(chatRoomId, newProps);\n            if (updated) {\n                console.log('✅ [NotificationBell] Successfully updated existing chat modal');\n                existingModal.scrollIntoView({\n                    behavior: 'smooth'\n                });\n                setIsOpen(false);\n                return;\n            } else {\n                console.log('⚠️ [NotificationBell] Failed to update modal, will create new one');\n            }\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            walletConnected: isConnected,\n            hasWallet: !!(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address)\n        });\n        // Ensure we always have functional action functions\n        const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n            alert('Cannot release escrow: Trade information not available');\n        };\n        const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n            alert('Cannot accept escrow: Trade information not available');\n        };\n        const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n            alert('Cannot refund escrow: Trade information not available');\n        };\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: safeOnRelease,\n                onRefund: safeOnRefund,\n                onReport: ()=>{\n                    console.log('🔍 [NotificationBell] Report function called');\n                // TODO: Implement report functionality\n                },\n                onAccept: safeOnAccept,\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 883,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 882,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 891,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 890,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 889,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 899,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 898,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 897,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 908,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 907,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 906,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 915,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 923,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 922,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 930,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 939,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 938,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 950,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 948,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 958,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 957,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 956,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 966,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 965,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 964,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 974,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 973,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 982,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 981,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 990,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 999,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1007,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 1006,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1037,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1045,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1044,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1040,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1062,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1110,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1125,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1123,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1130,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1129,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1132,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1128,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1172,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1169,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1165,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 1151,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1142,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1121,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1198,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1197,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 1058,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 1035,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"QdiDVwBej9/cSe8ZkWZlFhrudmc=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4dfcc7530279\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGRmY2M3NTMwMjc5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});