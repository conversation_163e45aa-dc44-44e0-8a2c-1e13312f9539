import React, { useState, useEffect } from "react";
import Image from "next/image";
import { setup2FA, verify2FASetup, disable2FA } from "@/axios/requests";
import { useAppContext } from "@/contexts/AppContext";
import { showErrorToast, showSuccessToast } from "@/utils/errorHandling";

const TwoFactorSetup: React.FC = () => {
  const { state, updateUserBo } = useAppContext();
  const [isEnabled, setIsEnabled] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [isPendingVerification, setIsPendingVerification] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState("");
  const [secret, setSecret] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [disableCode, setDisableCode] = useState("");
  const [showDisableForm, setShowDisableForm] = useState(false);
  const showLogs = false;

  const printLogs = (message: string, data?: any) => {
    if (showLogs) {
      console.log(message, data || "");
    }
  };

  // Check if 2FA is already enabled or pending verification
  useEffect(() => {
    if (!state.userBo) return;

    printLogs("Checking 2FA status:", {
      twoFactorStatus: state.userBo.twoFactorStatus,
      twoFactorData: state.userBo.twoFactorData,
    });

    // Check if user has 2FA fully enabled
    if (state.userBo.twoFactorStatus) {
      printLogs("2FA is enabled");
      setIsEnabled(true);
      setIsPendingVerification(false);
      return;
    }

    // Check if user has started 2FA setup but not completed verification
    if (
      state.userBo.twoFactorData &&
      state.userBo.twoFactorData.verified === false
    ) {
      printLogs("2FA setup started but not verified", {
        qrCodeUrl: state.userBo.twoFactorData.qrCodeUrl,
        secret: state.userBo.twoFactorData.secret,
      });

      setIsPendingVerification(true);
      setQrCodeUrl(state.userBo.twoFactorData.qrCodeUrl || "");
      setSecret(state.userBo.twoFactorData.secret || "");
      return;
    }
  }, [state.userBo]);

  const startSetup = async () => {
    setIsLoading(true);

    try {
      const userId = state.userBo?.id;
      if (!userId) {
        showErrorToast("You must be logged in to set up 2FA");
        return;
      }

      printLogs("Starting 2FA setup for user ID:", userId);
      const response = await setup2FA({ userId });
      //console.log('2FA setup response:', response);

      if (response.status === 200) {
        printLogs("Setting QR code URL:", response.data.qrCodeUrl);
        printLogs("Setting secret:", response.data.secret);

        setQrCodeUrl(response.data.qrCodeUrl);
        setSecret(response.data.secret);
        setIsPendingVerification(true);
        setIsSettingUp(false);
        showSuccessToast(
          "2FA setup initiated. Scan the QR code with Google Authenticator app"
        );
      } else {
        showErrorToast(response.message || "Failed to set up 2FA");
      }
    } catch (error: any) {
      console.error("2FA setup error:", error);
      showErrorToast(error.message || "An error occurred while setting up 2FA");
    } finally {
      setIsLoading(false);
    }

    
  };

  const verifyAndEnable = async () => {
    if (verificationCode.length !== 6) {
      showErrorToast("Please enter a valid 6-digit verification code");
      return;
    }

    setIsLoading(true);

    try {
      const userId = state.userBo?.id;
      if (!userId) {
        showErrorToast("You must be logged in to verify 2FA");
        return;
      }

      printLogs("Verifying 2FA setup for user ID:", userId);
      const response = await verify2FASetup({
        userId,
        token: verificationCode,
      });

      printLogs("2FA verification response:", response);

      if (response.status === 200) {
        // Update user data with 2FA enabled
        if (state.userBo) {
          // If the API returns the updated user data with twoFactorData, use it
          if (response.data && response.data.twoFactorData) {
            printLogs("Updating userBo with API response data:", {
              twoFactorStatus: true,
              twoFactorData: response.data.twoFactorData,
            });
            updateUserBo({
              ...state.userBo,
              twoFactorStatus: true,
              twoFactorData: response.data.twoFactorData,
            });
          } else {
            // Otherwise construct it from our local state
            printLogs("Updating userBo with local state data:", {
              twoFactorStatus: true,
              twoFactorData: {
                verified: true,
                secret: secret || "",
                qrCodeUrl: qrCodeUrl || "",
              },
            });
            updateUserBo({
              ...state.userBo,
              twoFactorStatus: true,
              twoFactorData: {
                ...(state.userBo.twoFactorData || {}),
                verified: true,
                secret: secret || "",
                qrCodeUrl: qrCodeUrl || "",
              },
            });
          }

          // Log the updated userBo in localStorage
          setTimeout(() => {
            const storedUserBo = localStorage.getItem("userBo");
            printLogs(
              "Updated userBo in localStorage:",
              storedUserBo ? JSON.parse(storedUserBo) : null
            );
          }, 100);
        }
        setIsEnabled(true);
        setIsPendingVerification(false);
        setVerificationCode("");
        showSuccessToast("Two-factor authentication enabled successfully!");
      } else {
        showErrorToast(response.message || "Invalid verification code");
      }
    } catch (error: any) {
      showErrorToast(error.message || "Failed to verify 2FA code");
      console.error("2FA verification error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisable = async () => {
    if (disableCode.length !== 6) {
      showErrorToast("Please enter a valid 6-digit verification code");
      return;
    }

    setIsLoading(true);

    try {
      const userId = state.userBo?.id;
      if (!userId) {
        showErrorToast("You must be logged in to disable 2FA");
        return;
      }

      printLogs("Disabling 2FA for user ID:", userId);
      const response = await disable2FA({
        userId,
        token: disableCode,
      });

      printLogs("2FA disable response:", response);

      if (response.status === 200) {
        // Update user data with 2FA disabled
        if (state.userBo) {
          updateUserBo({
            ...state.userBo,
            twoFactorStatus: false,
            twoFactorData: null,
          });

          // Log the updated userBo in localStorage
          setTimeout(() => {
            const storedUserBo = localStorage.getItem("userBo");
            printLogs(
              "Updated userBo in localStorage after disable:",
              storedUserBo ? JSON.parse(storedUserBo) : null
            );
          }, 100);
        }
        setIsEnabled(false);
        setIsPendingVerification(false);
        setShowDisableForm(false);
        setDisableCode("");
        showSuccessToast("Two-factor authentication disabled successfully");
      } else {
        showErrorToast(response.message || "Invalid verification code");
      }
    } catch (error: any) {
      showErrorToast(error.message || "Failed to disable 2FA");
      console.error("2FA disable error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderSetupButton = () => (
    <button
      onClick={startSetup}
      disabled={isLoading}
      className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
    >
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          Setting up...
        </>
      ) : (
        "Set up Google Authenticator"
      )}
    </button>
  );

  const renderVerificationForm = () => (
    <div className="space-y-6">
      <div className="p-4 bg-yellow-50 text-yellow-700 rounded-lg">
        <h3 className="font-semibold mb-2">Complete 2FA Setup:</h3>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Scan the QR code below with Google Authenticator app</li>
          <li>Enter the 6-digit verification code from the app</li>
          <li>Click "Verify and Enable" to complete the setup</li>
        </ol>
      </div>

      <div className="flex justify-center my-6">
        {qrCodeUrl && (
          <div className="p-4 bg-white border-2 border-gray-200 rounded-lg">
            <Image
              src={qrCodeUrl}
              alt="QR Code for Google Authenticator"
              width={200}
              height={200}
              className="w-[200px] h-[200px]"
            />
          </div>
        )}
      </div>

      <div className="mt-4">
        <p className="text-sm text-gray-600 mb-2">
          If you can't scan the QR code, use this secret key instead:
        </p>
        <div className="p-3 bg-gray-100 rounded font-mono text-sm mb-4 break-all">
          {secret}
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="verificationCode"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Verification Code
          </label>
          <input
            id="verificationCode"
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={6}
            value={verificationCode}
            onChange={(e) =>
              setVerificationCode(e.target.value.replace(/[^0-9]/g, ""))
            }
            placeholder="Enter 6-digit code"
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        <button
          type="button"
          onClick={verifyAndEnable}
          disabled={isLoading || verificationCode.length !== 6}
          className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Verifying...
            </>
          ) : (
            "Verify and Enable"
          )}
        </button>
      </div>
    </div>
  );

  const renderEnabledState = () => (
    <div className="space-y-6">
      <div className="p-4 bg-green-50 text-green-700 rounded-lg">
        <p className="font-medium">Two-factor authentication is enabled.</p>
        <p className="text-sm mt-1">
          Your account is protected with Google Authenticator.
        </p>
      </div>

      {!showDisableForm ? (
        <button
          onClick={() => setShowDisableForm(true)}
          className="w-full py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Disable Two-Factor Authentication
        </button>
      ) : (
        <div className="space-y-4">
          <div>
            <label
              htmlFor="disableCode"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Enter your verification code to disable 2FA
            </label>
            <input
              id="disableCode"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={6}
              value={disableCode}
              onChange={(e) =>
                setDisableCode(e.target.value.replace(/[^0-9]/g, ""))
              }
              placeholder="Enter 6-digit code"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div className="flex space-x-4">
            <button
              type="button"
              onClick={() => setShowDisableForm(false)}
              className="flex-1 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleDisable}
              disabled={isLoading || disableCode.length !== 6}
              className="flex-1 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Disabling...
                </>
              ) : (
                "Confirm Disable"
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-4">Google Authenticator</h2>
      <p className="text-gray-600 mb-6">
        Secure your account with time-based one-time passcodes generated by
        Google Authenticator.
      </p>

      {isEnabled
        ? renderEnabledState()
        : isPendingVerification
        ? renderVerificationForm()
        : renderSetupButton()}
    </div>
  );
};

export default TwoFactorSetup;
