import Image from 'next/image';
import React from 'react';

const Header = ({
  title,
  sortButtonText,
  handleSort,
}: {
  title: string;
  sortButtonText: string;
  handleSort?: () => void;
}) => (
  <div className="w-full mb-14 h-max relative bg-white flex justify-between">
    <div className={'text-black text-3xl font-semibold leading-10'}>
      {title}
    </div>
    <div
      className={`flex h-max px-4 py-2 bg-black ${handleSort ? 'rounded-lg' : ''
        } outline outline-[0.50px] outline-offset-[-0.50px] outline-blue-950 flex items-center gap-2.5 cursor-pointer`}
      onClick={() => handleSort && handleSort()}
    >
      <div className="text-white text-base font-semibold leading-relaxed">
        {sortButtonText}
      </div>
      {handleSort && (
        <div className="w-5 h-5 relative">
          <Image
            src="/icons/arrow-down.svg"
            alt="Sort"
            width={20}
            height={20}
            className="text-white"
          />
        </div>
      )}
    </div>
  </div>
);

export default Header;
