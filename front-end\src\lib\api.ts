
// Simulate network delay for mock data
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Fetch user notifications
 */
export async function fetchNotifications(): Promise<string[]> {

  // Mock notifications
  return [
    "User2 Created airdrop - User442 Burnt 24,442 of RAY coins",
    "User109 Sold 1,200 MEME coins - Price increased by 10%",
    "New perks available for COIN holders - Check them out!"
  ];
}
