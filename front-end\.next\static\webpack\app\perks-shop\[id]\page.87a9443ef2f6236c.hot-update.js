"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(signedTransaction.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price\n                });\n                // Use trade data to get the numeric escrowId\n                // First try to get it from the extra field, then fall back to original escrowId\n                let numericEscrowId = null;\n                try {\n                    if (tradeData.extra) {\n                        const extraData = JSON.parse(tradeData.extra);\n                        numericEscrowId = extraData.escrowId;\n                    }\n                } catch (e) {\n                    console.log('Could not parse extra data:', e);\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                calculatedPerkMint = ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr1 = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr1)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr1, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr1);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr1);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr1);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr1,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr1);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr1, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr1,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});