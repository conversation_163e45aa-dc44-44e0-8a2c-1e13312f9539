"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superstruct";
exports.ids = ["vendor-chunks/superstruct"];
exports.modules = {

/***/ "(ssr)/./node_modules/superstruct/lib/index.es.js":
/*!**************************************************!*\
  !*** ./node_modules/superstruct/lib/index.es.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct),\n/* harmony export */   StructError: () => (/* binding */ StructError),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   defaulted: () => (/* binding */ defaulted),\n/* harmony export */   define: () => (/* binding */ define),\n/* harmony export */   deprecated: () => (/* binding */ deprecated),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   enums: () => (/* binding */ enums),\n/* harmony export */   func: () => (/* binding */ func),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mask: () => (/* binding */ mask),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonempty: () => (/* binding */ nonempty),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   pattern: () => (/* binding */ pattern),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   refine: () => (/* binding */ refine),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   struct: () => (/* binding */ struct),\n/* harmony export */   trimmed: () => (/* binding */ trimmed),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   type: () => (/* binding */ type),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n  constructor(failure, failures) {\n    let cached;\n    const {\n      message,\n      ...rest\n    } = failure;\n    const {\n      path\n    } = failure;\n    const msg = path.length === 0 ? message : \"At path: \" + path.join('.') + \" -- \" + message;\n    super(msg);\n    this.value = void 0;\n    this.key = void 0;\n    this.type = void 0;\n    this.refinement = void 0;\n    this.path = void 0;\n    this.branch = void 0;\n    this.failures = void 0;\n    Object.assign(this, rest);\n    this.name = this.constructor.name;\n\n    this.failures = () => {\n      var _cached;\n\n      return (_cached = cached) != null ? _cached : cached = [failure, ...failures()];\n    };\n  }\n\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n  return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\n\n\nfunction isObject(x) {\n  return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\n\nfunction isPlainObject(x) {\n  if (Object.prototype.toString.call(x) !== '[object Object]') {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(x);\n  return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\n\nfunction print(value) {\n  return typeof value === 'string' ? JSON.stringify(value) : \"\" + value;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\n\nfunction shiftIterator(input) {\n  const {\n    done,\n    value\n  } = input.next();\n  return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\n\nfunction toFailure(result, context, struct, value) {\n  if (result === true) {\n    return;\n  } else if (result === false) {\n    result = {};\n  } else if (typeof result === 'string') {\n    result = {\n      message: result\n    };\n  }\n\n  const {\n    path,\n    branch\n  } = context;\n  const {\n    type\n  } = struct;\n  const {\n    refinement,\n    message = \"Expected a value of type `\" + type + \"`\" + (refinement ? \" with refinement `\" + refinement + \"`\" : '') + \", but received: `\" + print(value) + \"`\"\n  } = result;\n  return {\n    value,\n    type,\n    refinement,\n    key: path[path.length - 1],\n    path,\n    branch,\n    ...result,\n    message\n  };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\n\nfunction* toFailures(result, context, struct, value) {\n  if (!isIterable(result)) {\n    result = [result];\n  }\n\n  for (const r of result) {\n    const failure = toFailure(r, context, struct, value);\n\n    if (failure) {\n      yield failure;\n    }\n  }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\n\nfunction* run(value, struct, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    path = [],\n    branch = [value],\n    coerce = false,\n    mask = false\n  } = options;\n  const ctx = {\n    path,\n    branch\n  };\n\n  if (coerce) {\n    value = struct.coercer(value, ctx);\n\n    if (mask && struct.type !== 'type' && isObject(struct.schema) && isObject(value) && !Array.isArray(value)) {\n      for (const key in value) {\n        if (struct.schema[key] === undefined) {\n          delete value[key];\n        }\n      }\n    }\n  }\n\n  let valid = true;\n\n  for (const failure of struct.validator(value, ctx)) {\n    valid = false;\n    yield [failure, undefined];\n  }\n\n  for (let [k, v, s] of struct.entries(value, ctx)) {\n    const ts = run(v, s, {\n      path: k === undefined ? path : [...path, k],\n      branch: k === undefined ? branch : [...branch, v],\n      coerce,\n      mask\n    });\n\n    for (const t of ts) {\n      if (t[0]) {\n        valid = false;\n        yield [t[0], undefined];\n      } else if (coerce) {\n        v = t[1];\n\n        if (k === undefined) {\n          value = v;\n        } else if (value instanceof Map) {\n          value.set(k, v);\n        } else if (value instanceof Set) {\n          value.add(v);\n        } else if (isObject(value)) {\n          value[k] = v;\n        }\n      }\n    }\n  }\n\n  if (valid) {\n    for (const failure of struct.refiner(value, ctx)) {\n      valid = false;\n      yield [failure, undefined];\n    }\n  }\n\n  if (valid) {\n    yield [undefined, value];\n  }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\n\nclass Struct {\n  constructor(props) {\n    this.TYPE = void 0;\n    this.type = void 0;\n    this.schema = void 0;\n    this.coercer = void 0;\n    this.validator = void 0;\n    this.refiner = void 0;\n    this.entries = void 0;\n    const {\n      type,\n      schema,\n      validator,\n      refiner,\n      coercer = value => value,\n      entries = function* () {}\n    } = props;\n    this.type = type;\n    this.schema = schema;\n    this.entries = entries;\n    this.coercer = coercer;\n\n    if (validator) {\n      this.validator = (value, context) => {\n        const result = validator(value, context);\n        return toFailures(result, context, this, value);\n      };\n    } else {\n      this.validator = () => [];\n    }\n\n    if (refiner) {\n      this.refiner = (value, context) => {\n        const result = refiner(value, context);\n        return toFailures(result, context, this, value);\n      };\n    } else {\n      this.refiner = () => [];\n    }\n  }\n  /**\n   * Assert that a value passes the struct's validation, throwing if it doesn't.\n   */\n\n\n  assert(value) {\n    return assert(value, this);\n  }\n  /**\n   * Create a value with the struct's coercion logic, then validate it.\n   */\n\n\n  create(value) {\n    return create(value, this);\n  }\n  /**\n   * Check if a value passes the struct's validation.\n   */\n\n\n  is(value) {\n    return is(value, this);\n  }\n  /**\n   * Mask a value, coercing and validating it, but returning only the subset of\n   * properties defined by the struct's schema.\n   */\n\n\n  mask(value) {\n    return mask(value, this);\n  }\n  /**\n   * Validate a value with the struct's validation logic, returning a tuple\n   * representing the result.\n   *\n   * You may optionally pass `true` for the `withCoercion` argument to coerce\n   * the value before attempting to validate it. If you do, the result will\n   * contain the coerced result when successful.\n   */\n\n\n  validate(value, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    return validate(value, this, options);\n  }\n\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\n\nfunction assert(value, struct) {\n  const result = validate(value, struct);\n\n  if (result[0]) {\n    throw result[0];\n  }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\n\nfunction create(value, struct) {\n  const result = validate(value, struct, {\n    coerce: true\n  });\n\n  if (result[0]) {\n    throw result[0];\n  } else {\n    return result[1];\n  }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\n\nfunction mask(value, struct) {\n  const result = validate(value, struct, {\n    coerce: true,\n    mask: true\n  });\n\n  if (result[0]) {\n    throw result[0];\n  } else {\n    return result[1];\n  }\n}\n/**\n * Check if a value passes a struct.\n */\n\nfunction is(value, struct) {\n  const result = validate(value, struct);\n  return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\n\nfunction validate(value, struct, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const tuples = run(value, struct, options);\n  const tuple = shiftIterator(tuples);\n\n  if (tuple[0]) {\n    const error = new StructError(tuple[0], function* () {\n      for (const t of tuples) {\n        if (t[0]) {\n          yield t[0];\n        }\n      }\n    });\n    return [error, undefined];\n  } else {\n    const v = tuple[1];\n    return [undefined, v];\n  }\n}\n\nfunction assign() {\n  for (var _len = arguments.length, Structs = new Array(_len), _key = 0; _key < _len; _key++) {\n    Structs[_key] = arguments[_key];\n  }\n\n  const isType = Structs[0].type === 'type';\n  const schemas = Structs.map(s => s.schema);\n  const schema = Object.assign({}, ...schemas);\n  return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\n\nfunction define(name, validator) {\n  return new Struct({\n    type: name,\n    schema: null,\n    validator\n  });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\n\nfunction deprecated(struct, log) {\n  return new Struct({ ...struct,\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n\n    validator(value, ctx) {\n      if (value === undefined) {\n        return true;\n      } else {\n        log(value, ctx);\n        return struct.validator(value, ctx);\n      }\n    }\n\n  });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\n\nfunction dynamic(fn) {\n  return new Struct({\n    type: 'dynamic',\n    schema: null,\n\n    *entries(value, ctx) {\n      const struct = fn(value, ctx);\n      yield* struct.entries(value, ctx);\n    },\n\n    validator(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.validator(value, ctx);\n    },\n\n    coercer(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.coercer(value, ctx);\n    },\n\n    refiner(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.refiner(value, ctx);\n    }\n\n  });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\n\nfunction lazy(fn) {\n  let struct;\n  return new Struct({\n    type: 'lazy',\n    schema: null,\n\n    *entries(value, ctx) {\n      var _struct;\n\n      (_struct = struct) != null ? _struct : struct = fn();\n      yield* struct.entries(value, ctx);\n    },\n\n    validator(value, ctx) {\n      var _struct2;\n\n      (_struct2 = struct) != null ? _struct2 : struct = fn();\n      return struct.validator(value, ctx);\n    },\n\n    coercer(value, ctx) {\n      var _struct3;\n\n      (_struct3 = struct) != null ? _struct3 : struct = fn();\n      return struct.coercer(value, ctx);\n    },\n\n    refiner(value, ctx) {\n      var _struct4;\n\n      (_struct4 = struct) != null ? _struct4 : struct = fn();\n      return struct.refiner(value, ctx);\n    }\n\n  });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\n\nfunction omit(struct, keys) {\n  const {\n    schema\n  } = struct;\n  const subschema = { ...schema\n  };\n\n  for (const key of keys) {\n    delete subschema[key];\n  }\n\n  switch (struct.type) {\n    case 'type':\n      return type(subschema);\n\n    default:\n      return object(subschema);\n  }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\n\nfunction partial(struct) {\n  const schema = struct instanceof Struct ? { ...struct.schema\n  } : { ...struct\n  };\n\n  for (const key in schema) {\n    schema[key] = optional(schema[key]);\n  }\n\n  return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\n\nfunction pick(struct, keys) {\n  const {\n    schema\n  } = struct;\n  const subschema = {};\n\n  for (const key of keys) {\n    subschema[key] = schema[key];\n  }\n\n  return object(subschema);\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\n\nfunction struct(name, validator) {\n  console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n  return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\n\nfunction any() {\n  return define('any', () => true);\n}\nfunction array(Element) {\n  return new Struct({\n    type: 'array',\n    schema: Element,\n\n    *entries(value) {\n      if (Element && Array.isArray(value)) {\n        for (const [i, v] of value.entries()) {\n          yield [i, v, Element];\n        }\n      }\n    },\n\n    coercer(value) {\n      return Array.isArray(value) ? value.slice() : value;\n    },\n\n    validator(value) {\n      return Array.isArray(value) || \"Expected an array value, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a bigint.\n */\n\nfunction bigint() {\n  return define('bigint', value => {\n    return typeof value === 'bigint';\n  });\n}\n/**\n * Ensure that a value is a boolean.\n */\n\nfunction boolean() {\n  return define('boolean', value => {\n    return typeof value === 'boolean';\n  });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\n\nfunction date() {\n  return define('date', value => {\n    return value instanceof Date && !isNaN(value.getTime()) || \"Expected a valid `Date` object, but received: \" + print(value);\n  });\n}\nfunction enums(values) {\n  const schema = {};\n  const description = values.map(v => print(v)).join();\n\n  for (const key of values) {\n    schema[key] = key;\n  }\n\n  return new Struct({\n    type: 'enums',\n    schema,\n\n    validator(value) {\n      return values.includes(value) || \"Expected one of `\" + description + \"`, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a function.\n */\n\nfunction func() {\n  return define('func', value => {\n    return typeof value === 'function' || \"Expected a function, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\n\nfunction instance(Class) {\n  return define('instance', value => {\n    return value instanceof Class || \"Expected a `\" + Class.name + \"` instance, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is an integer.\n */\n\nfunction integer() {\n  return define('integer', value => {\n    return typeof value === 'number' && !isNaN(value) && Number.isInteger(value) || \"Expected an integer, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\n\nfunction intersection(Structs) {\n  return new Struct({\n    type: 'intersection',\n    schema: null,\n\n    *entries(value, ctx) {\n      for (const S of Structs) {\n        yield* S.entries(value, ctx);\n      }\n    },\n\n    *validator(value, ctx) {\n      for (const S of Structs) {\n        yield* S.validator(value, ctx);\n      }\n    },\n\n    *refiner(value, ctx) {\n      for (const S of Structs) {\n        yield* S.refiner(value, ctx);\n      }\n    }\n\n  });\n}\nfunction literal(constant) {\n  const description = print(constant);\n  const t = typeof constant;\n  return new Struct({\n    type: 'literal',\n    schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n\n    validator(value) {\n      return value === constant || \"Expected the literal `\" + description + \"`, but received: \" + print(value);\n    }\n\n  });\n}\nfunction map(Key, Value) {\n  return new Struct({\n    type: 'map',\n    schema: null,\n\n    *entries(value) {\n      if (Key && Value && value instanceof Map) {\n        for (const [k, v] of value.entries()) {\n          yield [k, k, Key];\n          yield [k, v, Value];\n        }\n      }\n    },\n\n    coercer(value) {\n      return value instanceof Map ? new Map(value) : value;\n    },\n\n    validator(value) {\n      return value instanceof Map || \"Expected a `Map` object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that no value ever passes validation.\n */\n\nfunction never() {\n  return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\n\nfunction nullable(struct) {\n  return new Struct({ ...struct,\n    validator: (value, ctx) => value === null || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === null || struct.refiner(value, ctx)\n  });\n}\n/**\n * Ensure that a value is a number.\n */\n\nfunction number() {\n  return define('number', value => {\n    return typeof value === 'number' && !isNaN(value) || \"Expected a number, but received: \" + print(value);\n  });\n}\nfunction object(schema) {\n  const knowns = schema ? Object.keys(schema) : [];\n  const Never = never();\n  return new Struct({\n    type: 'object',\n    schema: schema ? schema : null,\n\n    *entries(value) {\n      if (schema && isObject(value)) {\n        const unknowns = new Set(Object.keys(value));\n\n        for (const key of knowns) {\n          unknowns.delete(key);\n          yield [key, value[key], schema[key]];\n        }\n\n        for (const key of unknowns) {\n          yield [key, value[key], Never];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    },\n\n    coercer(value) {\n      return isObject(value) ? { ...value\n      } : value;\n    }\n\n  });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\n\nfunction optional(struct) {\n  return new Struct({ ...struct,\n    validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx)\n  });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\n\nfunction record(Key, Value) {\n  return new Struct({\n    type: 'record',\n    schema: null,\n\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k in value) {\n          const v = value[k];\n          yield [k, k, Key];\n          yield [k, v, Value];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\n\nfunction regexp() {\n  return define('regexp', value => {\n    return value instanceof RegExp;\n  });\n}\nfunction set(Element) {\n  return new Struct({\n    type: 'set',\n    schema: null,\n\n    *entries(value) {\n      if (Element && value instanceof Set) {\n        for (const v of value) {\n          yield [v, v, Element];\n        }\n      }\n    },\n\n    coercer(value) {\n      return value instanceof Set ? new Set(value) : value;\n    },\n\n    validator(value) {\n      return value instanceof Set || \"Expected a `Set` object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a string.\n */\n\nfunction string() {\n  return define('string', value => {\n    return typeof value === 'string' || \"Expected a string, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\n\nfunction tuple(Structs) {\n  const Never = never();\n  return new Struct({\n    type: 'tuple',\n    schema: null,\n\n    *entries(value) {\n      if (Array.isArray(value)) {\n        const length = Math.max(Structs.length, value.length);\n\n        for (let i = 0; i < length; i++) {\n          yield [i, value[i], Structs[i] || Never];\n        }\n      }\n    },\n\n    validator(value) {\n      return Array.isArray(value) || \"Expected an array, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\n\nfunction type(schema) {\n  const keys = Object.keys(schema);\n  return new Struct({\n    type: 'type',\n    schema,\n\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k of keys) {\n          yield [k, value[k], schema[k]];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\n\nfunction union(Structs) {\n  const description = Structs.map(s => s.type).join(' | ');\n  return new Struct({\n    type: 'union',\n    schema: null,\n\n    coercer(value, ctx) {\n      const firstMatch = Structs.find(s => {\n        const [e] = s.validate(value, {\n          coerce: true\n        });\n        return !e;\n      }) || unknown();\n      return firstMatch.coercer(value, ctx);\n    },\n\n    validator(value, ctx) {\n      const failures = [];\n\n      for (const S of Structs) {\n        const [...tuples] = run(value, S, ctx);\n        const [first] = tuples;\n\n        if (!first[0]) {\n          return [];\n        } else {\n          for (const [failure] of tuples) {\n            if (failure) {\n              failures.push(failure);\n            }\n          }\n        }\n      }\n\n      return [\"Expected the value to satisfy a union of `\" + description + \"`, but received: \" + print(value), ...failures];\n    }\n\n  });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\n\nfunction unknown() {\n  return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction coerce(struct, condition, coercer) {\n  return new Struct({ ...struct,\n    coercer: (value, ctx) => {\n      return is(value, condition) ? struct.coercer(coercer(value, ctx), ctx) : struct.coercer(value, ctx);\n    }\n  });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction defaulted(struct, fallback, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  return coerce(struct, unknown(), x => {\n    const f = typeof fallback === 'function' ? fallback() : fallback;\n\n    if (x === undefined) {\n      return f;\n    }\n\n    if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n      const ret = { ...x\n      };\n      let changed = false;\n\n      for (const key in f) {\n        if (ret[key] === undefined) {\n          ret[key] = f[key];\n          changed = true;\n        }\n      }\n\n      if (changed) {\n        return ret;\n      }\n    }\n\n    return x;\n  });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction trimmed(struct) {\n  return coerce(struct, string(), x => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\n\nfunction empty(struct) {\n  return refine(struct, 'empty', value => {\n    const size = getSize(value);\n    return size === 0 || \"Expected an empty \" + struct.type + \" but received one with a size of `\" + size + \"`\";\n  });\n}\n\nfunction getSize(value) {\n  if (value instanceof Map || value instanceof Set) {\n    return value.size;\n  } else {\n    return value.length;\n  }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\n\n\nfunction max(struct, threshold, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    exclusive\n  } = options;\n  return refine(struct, 'max', value => {\n    return exclusive ? value < threshold : value <= threshold || \"Expected a \" + struct.type + \" less than \" + (exclusive ? '' : 'or equal to ') + threshold + \" but received `\" + value + \"`\";\n  });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\n\nfunction min(struct, threshold, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    exclusive\n  } = options;\n  return refine(struct, 'min', value => {\n    return exclusive ? value > threshold : value >= threshold || \"Expected a \" + struct.type + \" greater than \" + (exclusive ? '' : 'or equal to ') + threshold + \" but received `\" + value + \"`\";\n  });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\n\nfunction nonempty(struct) {\n  return refine(struct, 'nonempty', value => {\n    const size = getSize(value);\n    return size > 0 || \"Expected a nonempty \" + struct.type + \" but received an empty one\";\n  });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\n\nfunction pattern(struct, regexp) {\n  return refine(struct, 'pattern', value => {\n    return regexp.test(value) || \"Expected a \" + struct.type + \" matching `/\" + regexp.source + \"/` but received \\\"\" + value + \"\\\"\";\n  });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\n\nfunction size(struct, min, max) {\n  if (max === void 0) {\n    max = min;\n  }\n\n  const expected = \"Expected a \" + struct.type;\n  const of = min === max ? \"of `\" + min + \"`\" : \"between `\" + min + \"` and `\" + max + \"`\";\n  return refine(struct, 'size', value => {\n    if (typeof value === 'number' || value instanceof Date) {\n      return min <= value && value <= max || expected + \" \" + of + \" but received `\" + value + \"`\";\n    } else if (value instanceof Map || value instanceof Set) {\n      const {\n        size\n      } = value;\n      return min <= size && size <= max || expected + \" with a size \" + of + \" but received one with a size of `\" + size + \"`\";\n    } else {\n      const {\n        length\n      } = value;\n      return min <= length && length <= max || expected + \" with a length \" + of + \" but received one with a length of `\" + length + \"`\";\n    }\n  });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\n\nfunction refine(struct, name, refiner) {\n  return new Struct({ ...struct,\n\n    *refiner(value, ctx) {\n      yield* struct.refiner(value, ctx);\n      const result = refiner(value, ctx);\n      const failures = toFailures(result, ctx, struct, value);\n\n      for (const failure of failures) {\n        yield { ...failure,\n          refinement: name\n        };\n      }\n    }\n\n  });\n}\n\n\n//# sourceMappingURL=index.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superstruct/lib/index.es.js\n");

/***/ })

};
;