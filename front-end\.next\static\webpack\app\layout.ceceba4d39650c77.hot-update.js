"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ChatModalStateContext */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { updateModalProps, getChatModalState } = (0,_contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Unified function to normalize notification data and determine user roles\n    const normalizeNotificationData = async (notification, currentUserId)=>{\n        console.log('🔍 [NotificationBell] Normalizing notification data:', {\n            type: notification.type,\n            data: notification.data\n        });\n        if (!notification.data) {\n            throw new Error('Notification data is missing');\n        }\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        let tradeId = notification.data.tradeId;\n        let chatRoomId = notification.data.chatRoomId;\n        let perkId = notification.data.perkId;\n        // Handle different notification types with unified logic\n        switch(notification.type){\n            case 'perk_purchased':\n            case 'escrow_created':\n                // Current user is buyer, notification contains seller info\n                buyerId = currentUserId;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n                break;\n            case 'perk_sold':\n            case 'escrow_pending_acceptance':\n            case 'escrow_accepted':\n                // Current user is seller, notification contains buyer info\n                sellerId = currentUserId;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n                break;\n            case 'escrow_released':\n            case 'trade_completed':\n                // Could be either buyer or seller, determine from notification data\n                if (notification.data.buyerId === currentUserId) {\n                    buyerId = currentUserId;\n                    sellerId = notification.data.sellerId;\n                } else {\n                    sellerId = currentUserId;\n                    buyerId = notification.data.buyerId;\n                }\n                break;\n            case 'chat_message':\n                // Determine roles from sender/receiver\n                if (notification.data.senderId === currentUserId) {\n                    // I sent the message, I could be buyer or seller\n                    if (notification.data.buyerId === currentUserId) {\n                        buyerId = currentUserId;\n                        sellerId = notification.data.sellerId || notification.data.receiverId;\n                    } else {\n                        sellerId = currentUserId;\n                        buyerId = notification.data.buyerId || notification.data.receiverId;\n                    }\n                } else {\n                    // I received the message\n                    if (notification.data.receiverId === currentUserId) {\n                        if (notification.data.buyerId === currentUserId) {\n                            buyerId = currentUserId;\n                            sellerId = notification.data.sellerId || notification.data.senderId;\n                        } else {\n                            sellerId = currentUserId;\n                            buyerId = notification.data.buyerId || notification.data.senderId;\n                        }\n                    }\n                }\n                break;\n            default:\n                // Fallback logic for other types\n                if (!buyerId || !sellerId) {\n                    if (notification.data.buyerId === currentUserId) {\n                        buyerId = currentUserId;\n                        sellerId = notification.data.sellerId || notification.data.receiverId;\n                    } else {\n                        sellerId = currentUserId;\n                        buyerId = notification.data.buyerId || notification.data.senderId;\n                    }\n                }\n        }\n        // Generate chatRoomId if not provided\n        if (!chatRoomId && buyerId && sellerId && perkId) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId:', chatRoomId);\n        }\n        // Try to find tradeId if not provided\n        if (!tradeId && chatRoomId) {\n            try {\n                const chatRoomParts = chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const extractedPerkId = chatRoomParts[2];\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(currentUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(extractedPerkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeId = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found tradeId from chatRoomId:', tradeId);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find tradeId from chatRoomId:', error);\n            }\n        }\n        return {\n            buyerId,\n            sellerId,\n            tradeId,\n            chatRoomId,\n            perkId: perkId || notification.data.perkId\n        };\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        console.log('🔍 [NotificationBell] Opening chat modal for notification:', notification);\n        if (!(user === null || user === void 0 ? void 0 : user.id)) {\n            console.error('❌ [NotificationBell] User not authenticated');\n            return;\n        }\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        const userIdNumber = parseInt(user.id);\n        if (!myUserId) {\n            console.error('❌ [NotificationBell] User ID not found');\n            return;\n        }\n        // Use the unified normalization function\n        let normalizedData;\n        try {\n            normalizedData = await normalizeNotificationData(notification, userIdNumber);\n        } catch (error) {\n            console.error('❌ [NotificationBell] Failed to normalize notification data:', error);\n            return;\n        }\n        const { buyerId, sellerId, tradeId, chatRoomId, perkId } = normalizedData;\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with normalized data:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            tradeId,\n            perkId,\n            myUserId,\n            userIdNumber\n        });\n        // Fetch real trade details if tradeId exists\n        let activeTrade = undefined;\n        let tradeIdToUse = tradeId;\n        if (tradeIdToUse) {\n            try {\n                var _tradeResponse_data;\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data,\n                    tradeDataId: (_tradeResponse_data = tradeResponse.data) === null || _tradeResponse_data === void 0 ? void 0 : _tradeResponse_data.id,\n                    tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    // Ensure we have a valid ID - use tradeIdToUse as fallback\n                    const validId = tradeResponse.data.id || tradeIdToUse;\n                    activeTrade = {\n                        id: validId,\n                        status: tradeResponse.data.status,\n                        tradeId: validId,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    var _notification_data, _notification_data1;\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to normalized data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.status) || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: perkId,\n                        escrowId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, update it instead of creating new one\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, updating with new state');\n            // Update the existing modal with new props\n            const newProps = {\n                activeTrade,\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onRelease: tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n                    alert('Cannot release escrow: Trade information not available');\n                },\n                onRefund: tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n                    alert('Cannot refund escrow: Trade information not available');\n                },\n                onAccept: tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n                    alert('Cannot accept escrow: Trade information not available');\n                }\n            };\n            // Update modal props through the state context\n            const updated = updateModalProps(chatRoomId, newProps);\n            if (updated) {\n                console.log('✅ [NotificationBell] Successfully updated existing chat modal');\n                existingModal.scrollIntoView({\n                    behavior: 'smooth'\n                });\n                setIsOpen(false);\n                return;\n            } else {\n                console.log('⚠️ [NotificationBell] Failed to update modal, will create new one');\n            }\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            walletConnected: isConnected,\n            hasWallet: !!(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address)\n        });\n        // Ensure we always have functional action functions\n        const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n            alert('Cannot release escrow: Trade information not available');\n        };\n        const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n            alert('Cannot accept escrow: Trade information not available');\n        };\n        const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n            alert('Cannot refund escrow: Trade information not available');\n        };\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: safeOnRelease,\n                onRefund: safeOnRefund,\n                onReport: ()=>{\n                    console.log('🔍 [NotificationBell] Report function called');\n                // TODO: Implement report functionality\n                },\n                onAccept: safeOnAccept,\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 821,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 819,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 828,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 835,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 845,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 853,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 852,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 868,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 878,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 877,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 876,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 886,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 896,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 894,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 902,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 920,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 929,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 928,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 936,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 945,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 944,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 975,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 982,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 978,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1000,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 999,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 998,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1091,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1090,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1110,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1087,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1080,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1059,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1135,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 996,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 973,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"QdiDVwBej9/cSe8ZkWZlFhrudmc=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"177ccb6a0c7e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTc3Y2NiNmEwYzdlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});