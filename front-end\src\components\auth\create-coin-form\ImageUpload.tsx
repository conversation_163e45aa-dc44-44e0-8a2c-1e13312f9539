"use client";

import { motion } from "framer-motion";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

import { formFieldVariants } from "./constants";
import { useTranslation } from '@/hooks/useTranslation';

interface ImageUploadProps {
  picture: File | null;
  previewUrl: string | null;
  error: string;
  isSubmitting: boolean;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveImage: () => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  picture,
  previewUrl,
  error,
  isSubmitting,
  onFileChange,
  onRemoveImage,
}) => {
  const { t } = useTranslation();
  const inputClass =
    "self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200";
  const inputErrorClass =
    "self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4";

  return (
    <>
      <motion.div
        className={`${error ? inputErrorClass : inputClass} relative`}
        variants={formFieldVariants}
      >
        <div className="flex-1 flex items-center gap-3">
          {previewUrl ? (
            <div className="flex items-center gap-3 flex-1">
              <Image
                src={previewUrl}
                alt="Preview"
                width={40}
                height={40}
                className="w-10 h-10 rounded object-cover"
              />
              <span className="text-gray-700 flex-1 truncate">
                {picture?.name}
              </span>
              <motion.button
                type="button"
                onClick={onRemoveImage}
                className="text-red-500 hover:text-red-700 p-1"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <X size={16} />
              </motion.button>
            </div>
          ) : (
            <div className="flex-1 text-gray-400 flex items-center gap-2">
              <ImageIcon size={20} />
              <span>{t('createCoinForm.uploadPicture')}</span>
            </div>
          )}
        </div>
        {!previewUrl && (
          <motion.label
            className="px-4 py-2 bg-[#F58A38] text-white rounded cursor-pointer hover:bg-[#e55a00] transition-colors flex items-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Upload size={16} />
            {t('createCoinForm.upload')}
            <input
              type="file"
              className="hidden"
              onChange={onFileChange}
              accept="image/*"
              disabled={isSubmitting}
            />
          </motion.label>
        )}
      </motion.div>
      {error && (
        <motion.div
          className="text-red-500 text-sm w-full"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.15 }}
        >
          {error}
        </motion.div>
      )}
    </>
  );
};

export default ImageUpload; 