"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_perks_create-perk-form_tsx"],{

/***/ "(app-pages-browser)/./src/components/perks/components/FormHandlers.ts":
/*!*********************************************************!*\
  !*** ./src/components/perks/components/FormHandlers.ts ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFormHandlers: () => (/* binding */ useFormHandlers)\n/* harmony export */ });\n/* harmony import */ var _FormValidation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FormValidation */ \"(app-pages-browser)/./src/components/perks/components/FormValidation.ts\");\n/* harmony import */ var _hooks_useGenericFormHandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../hooks/useGenericFormHandler */ \"(app-pages-browser)/./src/hooks/useGenericFormHandler.ts\");\n/* harmony import */ var _hooks_useFileUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../hooks/useFileUpload */ \"(app-pages-browser)/./src/hooks/useFileUpload.ts\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n\n\n\n\nconst useFormHandlers = function(formData, setFormData, errors, setErrors) {\n    let fileInputId = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'picture-upload';\n    // Define validation rules for the form\n    const validationRules = {\n        name: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateName)(value),\n        price: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validatePrice)(value),\n        fulfillmentLink: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateFulfillmentLink)(value),\n        stockAmount: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateStockAmount)(value, formData.limitedStock),\n        category: (value)=>(0,_FormValidation__WEBPACK_IMPORTED_MODULE_0__.validateCategory)(value)\n    };\n    // Use generic form handler with type assertions for compatibility\n    const { handleChange, handleCheckboxChange, handleBlur } = (0,_hooks_useGenericFormHandler__WEBPACK_IMPORTED_MODULE_1__.useGenericFormHandler)({\n        formData,\n        setFormData,\n        errors: errors,\n        setErrors: setErrors,\n        validationRules\n    });\n    // Use file upload handler with type assertions for compatibility\n    const { handleFileChange, handleDeleteFile } = (0,_hooks_useFileUpload__WEBPACK_IMPORTED_MODULE_2__.useFileUpload)({\n        formData,\n        setFormData,\n        errors: errors,\n        setErrors: setErrors,\n        fieldName: 'picture',\n        errorFieldName: 'picture',\n        options: {\n            fileInputId,\n            maxSize: _config_environment__WEBPACK_IMPORTED_MODULE_3__.UPLOAD_CONFIG.MAX_FILE_SIZE,\n            allowedTypes: _config_environment__WEBPACK_IMPORTED_MODULE_3__.UPLOAD_CONFIG.ALLOWED_FILE_TYPES\n        }\n    });\n    // Maintain backward compatibility by renaming the delete function\n    const handleDeletePicture = handleDeleteFile;\n    return {\n        handleChange,\n        handleCheckboxChange,\n        handleBlur,\n        handleFileChange,\n        handleDeletePicture\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/perks/components/FormHandlers.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/perks/components/FormSubmission.ts":
/*!***********************************************************!*\
  !*** ./src/components/perks/components/FormSubmission.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFormSubmission: () => (/* binding */ useFormSubmission)\n/* harmony export */ });\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(app-pages-browser)/./src/utils/helpers.ts\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n\n\n\nconst useFormSubmission = (formData, userID, onClose, setSubmitted, setIsSubmitting, setFormData, validateForm, hasErrors, t)=>{\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const newErrors = validateForm(formData, userID);\n        if (!hasErrors(newErrors)) {\n            setIsSubmitting(true);\n            let imageUrl = '';\n            if (formData.picture) {\n                try {\n                    imageUrl = await (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.uploadToPinata)(formData.picture);\n                } catch (error) {\n                    console.error('Image upload failed:', error);\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.FORM.IMAGE_UPLOAD_FAILED));\n                    setIsSubmitting(false);\n                    return newErrors;\n                }\n            }\n            // Get userId from localStorage if available\n            let submissionUserId = userID;\n            if ( true && !submissionUserId) {\n                const storedUserBo = localStorage.getItem('userBo');\n                if (storedUserBo) {\n                    try {\n                        const parsedUserBo = JSON.parse(storedUserBo);\n                        submissionUserId = parsedUserBo.id;\n                    } catch (error) {\n                        console.error('Error parsing stored userBo:', error);\n                        (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.AUTH.LOGIN_REQUIRED));\n                        setIsSubmitting(false);\n                        return newErrors;\n                    }\n                }\n            }\n            const newPerk = {\n                name: formData.name,\n                description: formData.description,\n                price: formData.price,\n                fulfillmentLink: formData.fulfillmentLink,\n                image: imageUrl,\n                isLimited: formData.limitedStock,\n                stockAmount: parseInt(formData.stockAmount),\n                tokenAmount: parseInt(formData.tokenAmount) || 1,\n                category: formData.category,\n                userId: submissionUserId\n            };\n            try {\n                const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_0__.addPerk)(newPerk);\n                if (response.status === 201) {\n                    setSubmitted(true);\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showSuccessToast)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_SUCCESS);\n                    // Reset form after 2 seconds\n                    setTimeout(()=>{\n                        setFormData({\n                            name: '',\n                            description: '',\n                            price: '',\n                            fulfillmentLink: '',\n                            picture: null,\n                            limitedStock: false,\n                            stockAmount: '',\n                            tokenAmount: '1',\n                            category: ''\n                        });\n                        setSubmitted(false);\n                        onClose();\n                    }, 2000);\n                } else {\n                    var _response_data;\n                    const errorMessage = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(errorMessage, response.status));\n                }\n            } catch (error) {\n                var _error_response, _error_response1, _error_response2;\n                console.error('Perk creation failed:', error);\n                // Handle different types of errors with specific messages\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.AUTH.LOGIN_REQUIRED));\n                } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 400) {\n                    var _error_response_data, _error_response3;\n                    const errorMessage = ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data = _error_response3.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.FORM.VALIDATION_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createFormError)(errorMessage));\n                } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) >= 500) {\n                    var _error_response4;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.NETWORK.SERVER_ERROR, (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status));\n                } else {\n                    var _error_response_data1, _error_response5;\n                    const errorMessage = ((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : (_error_response_data1 = _error_response5.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || _utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.TOAST_MESSAGES.PERK.CREATE_FAILED;\n                    (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.showErrorToast)((0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_2__.createAPIError)(errorMessage));\n                }\n            } finally{\n                setIsSubmitting(false);\n            }\n        }\n        return newErrors;\n    };\n    return {\n        handleSubmit\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/perks/components/FormSubmission.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/perks/components/FormValidation.ts":
/*!***********************************************************!*\
  !*** ./src/components/perks/components/FormValidation.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateCategory: () => (/* binding */ validateCategory),\n/* harmony export */   validateFulfillmentLink: () => (/* binding */ validateFulfillmentLink),\n/* harmony export */   validateName: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateName),\n/* harmony export */   validatePrice: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validatePrice),\n/* harmony export */   validateStockAmount: () => (/* reexport safe */ _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateStockAmount)\n/* harmony export */ });\n/* harmony import */ var _utils_formValidation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/formValidation */ \"(app-pages-browser)/./src/utils/formValidation.ts\");\n\nconst validateFulfillmentLink = (link)=>{\n    return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_0__.validateUrl)(link, 'fulfillment link');\n};\nconst validateCategory = (category)=>{\n    if (!category || category.trim() === '') {\n        return 'Please select a category';\n    }\n    return '';\n};\n// Re-export the shared functions for backward compatibility\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3BlcmtzL2NvbXBvbmVudHMvRm9ybVZhbGlkYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBS3VDO0FBRWhDLE1BQU1JLDBCQUEwQixDQUFDQztJQUN0QyxPQUFPSCxrRUFBV0EsQ0FBQ0csTUFBTTtBQUMzQixFQUFFO0FBRUssTUFBTUMsbUJBQW1CLENBQUNDO0lBQy9CLElBQUksQ0FBQ0EsWUFBWUEsU0FBU0MsSUFBSSxPQUFPLElBQUk7UUFDdkMsT0FBTztJQUNUO0lBQ0EsT0FBTztBQUNULEVBQUU7QUFHRiw0REFBNEQ7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXHNyY1xcY29tcG9uZW50c1xccGVya3NcXGNvbXBvbmVudHNcXEZvcm1WYWxpZGF0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgdmFsaWRhdGVOYW1lLFxyXG4gIHZhbGlkYXRlUHJpY2UsXHJcbiAgdmFsaWRhdGVVcmwsXHJcbiAgdmFsaWRhdGVTdG9ja0Ftb3VudCxcclxufSBmcm9tICcuLi8uLi8uLi91dGlscy9mb3JtVmFsaWRhdGlvbic7XHJcblxyXG5leHBvcnQgY29uc3QgdmFsaWRhdGVGdWxmaWxsbWVudExpbmsgPSAobGluazogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICByZXR1cm4gdmFsaWRhdGVVcmwobGluaywgJ2Z1bGZpbGxtZW50IGxpbmsnKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUNhdGVnb3J5ID0gKGNhdGVnb3J5OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gIGlmICghY2F0ZWdvcnkgfHwgY2F0ZWdvcnkudHJpbSgpID09PSAnJykge1xyXG4gICAgcmV0dXJuICdQbGVhc2Ugc2VsZWN0IGEgY2F0ZWdvcnknO1xyXG4gIH1cclxuICByZXR1cm4gJyc7XHJcbn07XHJcblxyXG5cclxuLy8gUmUtZXhwb3J0IHRoZSBzaGFyZWQgZnVuY3Rpb25zIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XHJcbmV4cG9ydCB7IHZhbGlkYXRlTmFtZSwgdmFsaWRhdGVQcmljZSwgdmFsaWRhdGVTdG9ja0Ftb3VudCB9O1xyXG4iXSwibmFtZXMiOlsidmFsaWRhdGVOYW1lIiwidmFsaWRhdGVQcmljZSIsInZhbGlkYXRlVXJsIiwidmFsaWRhdGVTdG9ja0Ftb3VudCIsInZhbGlkYXRlRnVsZmlsbG1lbnRMaW5rIiwibGluayIsInZhbGlkYXRlQ2F0ZWdvcnkiLCJjYXRlZ29yeSIsInRyaW0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/perks/components/FormValidation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/perks/create-perk-form.tsx":
/*!***************************************************!*\
  !*** ./src/components/perks/create-perk-form.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _utils_formValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/formValidation */ \"(app-pages-browser)/./src/utils/formValidation.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* harmony import */ var _components_FormHandlers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/FormHandlers */ \"(app-pages-browser)/./src/components/perks/components/FormHandlers.ts\");\n/* harmony import */ var _components_FormSubmission__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/FormSubmission */ \"(app-pages-browser)/./src/components/perks/components/FormSubmission.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst FormFields = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_perks_components_FormFields_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/FormFields */ \"(app-pages-browser)/./src/components/perks/components/FormFields.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\perks\\\\create-perk-form.tsx -> \" + \"./components/FormFields\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse h-96 bg-gray-200 rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n            lineNumber: 28,\n            columnNumber: 18\n        }, undefined)\n});\n_c = FormFields;\n// Validation functions using shared utilities\nconst validateFulfillmentLink = (link)=>{\n    return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(link, 'fulfillment link');\n};\nconst validateCategory = (category)=>{\n    if (!category || category.trim() === '') {\n        return 'Please select a category';\n    }\n    return '';\n};\nconst validateForm = (formData, userId)=>{\n    return {\n        name: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(formData.name),\n        description: '',\n        price: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(formData.price),\n        fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),\n        picture: !formData.picture ? 'Please upload an image for your perk' : '',\n        stockAmount: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(formData.stockAmount, formData.limitedStock),\n        category: validateCategory(formData.category),\n        api: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUserLogin)(userId)\n    };\n};\nconst CreatePerkForm = (param)=>{\n    let { onClose } = param;\n    _s();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { state } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__.useAppContext)();\n    const boxRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: '',\n        description: '',\n        price: '',\n        fulfillmentLink: '',\n        picture: null,\n        limitedStock: false,\n        stockAmount: '',\n        tokenAmount: '1',\n        category: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: '',\n        description: '',\n        price: '',\n        fulfillmentLink: '',\n        picture: '',\n        stockAmount: '',\n        tokenAmount: '',\n        category: '',\n        api: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validationProgress, setValidationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const userBo = typeof state.userBo === 'string' ? JSON.parse(state.userBo) : state.userBo;\n    const userID = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n    // Validation functions using shared utilities\n    const validateFulfillmentLink = (link)=>{\n        return (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(link, t('createPerkForm.fulfillmentLink'));\n    };\n    const validateCategory = (category)=>{\n        if (!category || category.trim() === '') {\n            return t('createPerkForm.errorSelectCategory');\n        }\n        return '';\n    };\n    const validateTokenAmount = (tokenAmount)=>{\n        if (!tokenAmount || tokenAmount.trim() === '') {\n            return 'Token amount is required';\n        }\n        const amount = parseInt(tokenAmount);\n        if (isNaN(amount) || amount < 1) {\n            return 'Token amount must be a positive number';\n        }\n        return '';\n    };\n    const validateForm = (formData, userId)=>{\n        return {\n            name: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(formData.name),\n            description: '',\n            price: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(formData.price),\n            fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),\n            picture: !formData.picture ? t('createPerkForm.errorUploadPicture') : '',\n            stockAmount: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(formData.stockAmount, formData.limitedStock),\n            tokenAmount: validateTokenAmount(formData.tokenAmount),\n            category: validateCategory(formData.category),\n            api: (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateUserLogin)(userId)\n        };\n    };\n    const { handleSubmit } = (0,_components_FormSubmission__WEBPACK_IMPORTED_MODULE_9__.useFormSubmission)(formData, userID, onClose, setSubmitted, setIsSubmitting, setFormData, validateForm, _utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors, t);\n    const { handleChange, handleCheckboxChange, handleBlur, handleFileChange, handleDeletePicture } = (0,_components_FormHandlers__WEBPACK_IMPORTED_MODULE_8__.useFormHandlers)(formData, setFormData, errors, setErrors);\n    // Enhanced form submission with better error handling\n    const handleFormSubmit = async (e)=>{\n        e.preventDefault();\n        setIsValidating(true);\n        setValidationProgress({});\n        // Validate form\n        const newErrors = validateForm(formData, userID);\n        setErrors(newErrors);\n        // Check for validation errors\n        if ((0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(newErrors)) {\n            setIsValidating(false);\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.showErrorToast)('Please fix the errors before submitting');\n            return;\n        }\n        setIsValidating(false);\n        // Proceed with submission\n        try {\n            await handleSubmit(e);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            let errorMessage = 'Failed to create perk';\n            if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.ValidationError) {\n                errorMessage = error.message;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.message\n                    }));\n            } else if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.NetworkError) {\n                errorMessage = 'Network error. Please check your connection and try again.';\n            } else if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.response.data.message\n                    }));\n            } else if (error.error) {\n                errorMessage = error.error;\n                setErrors((prev)=>({\n                        ...prev,\n                        api: error.error\n                    }));\n            } else {\n                setErrors((prev)=>({\n                        ...prev,\n                        api: 'Something went wrong. Please try again.'\n                    }));\n            }\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_7__.showErrorToast)(errorMessage);\n        }\n    };\n    // Enhanced field validation with progress tracking\n    const handleFieldValidation = (fieldName, value)=>{\n        setValidationProgress((prev)=>({\n                ...prev,\n                [fieldName]: true\n            }));\n        // Simulate validation delay for better UX\n        setTimeout(()=>{\n            let fieldError = '';\n            switch(fieldName){\n                case 'name':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateName)(value);\n                    break;\n                case 'price':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validatePrice)(value);\n                    break;\n                case 'fulfillmentLink':\n                    fieldError = validateFulfillmentLink(value);\n                    break;\n                case 'category':\n                    fieldError = validateCategory(value);\n                    break;\n                case 'stockAmount':\n                    fieldError = (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.validateStockAmount)(value, formData.limitedStock);\n                    break;\n                default:\n                    fieldError = '';\n            }\n            setErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: fieldError\n                }));\n            setValidationProgress((prev)=>({\n                    ...prev,\n                    [fieldName]: false\n                }));\n        }, 300);\n    };\n    // Enhanced change handler with validation\n    const handleEnhancedChange = (e)=>{\n        handleChange(e);\n        // Clear API error when user starts typing\n        if (errors.api) {\n            setErrors((prev)=>({\n                    ...prev,\n                    api: ''\n                }));\n        }\n        // Validate field after change\n        handleFieldValidation(e.target.name, e.target.value);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"CreatePerkForm.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"CreatePerkForm.useEffect.handleClickOutside\": (event)=>{\n                    if (boxRef.current && !boxRef.current.contains(event.target)) {\n                        onClose();\n                    }\n                }\n            }[\"CreatePerkForm.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"CreatePerkForm.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"CreatePerkForm.useEffect\"];\n        }\n    }[\"CreatePerkForm.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        className: \"w-[563px] max-w-full mx-auto bg-white rounded-[24px] md:rounded-[48px] p-4 md:p-10 pt-6 flex flex-col relative\",\n        ref: boxRef,\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.9\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"block lg:hidden absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                onClick: onClose,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: '/icons/close.svg',\n                    alt: t('common.close'),\n                    width: 24,\n                    height: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            submitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                className: \"flex flex-col items-center gap-7 py-10\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px]\",\n                        children: t('createPerkForm.successTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.svg, {\n                        className: \"w-24 h-24 text-green-500 mx-auto\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 0.2,\n                            type: \"spring\",\n                            stiffness: 200\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold font-['IBM_Plex_Sans']\",\n                        children: t('createPerkForm.successMessage')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 font-['IBM_Plex_Sans']\",\n                        children: t('createPerkForm.successDescription')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleFormSubmit,\n                className: \"flex flex-col w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mb-8\",\n                        children: t('createPerkForm.formTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormFields, {\n                        formData: formData,\n                        errors: errors,\n                        handleChange: handleEnhancedChange,\n                        handleBlur: handleBlur,\n                        handleCheckboxChange: handleCheckboxChange,\n                        handleFileChange: handleFileChange,\n                        handleDeletePicture: handleDeletePicture,\n                        t: t,\n                        validationProgress: validationProgress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: errors.api && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500 mr-2\",\n                                        children: \"⚠\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 font-semibold text-base\",\n                                        children: errors.api\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, undefined),\n                    isValidating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        className: \"flex items-center justify-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Validating form...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isSubmitting || isValidating || (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(errors),\n                        className: \"w-full h-12 px-5 py-2 rounded-sm inline-flex justify-center items-center gap-2.5 mt-8 mx-auto transition-all duration-200 \".concat(isSubmitting || isValidating || (0,_utils_formValidation__WEBPACK_IMPORTED_MODULE_5__.hasErrors)(errors) ? 'bg-gray-400 cursor-not-allowed' : 'bg-black hover:bg-gray-800'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed\",\n                                children: isSubmitting ? 'Creating perk...' : isValidating ? 'Validating...' : 'Create my perk'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined),\n                            (isSubmitting || isValidating) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\perks\\\\create-perk-form.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePerkForm, \"hj/L2S2lEiePYwOlSVJBzx/U9Jc=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_4__.useAppContext,\n        _components_FormSubmission__WEBPACK_IMPORTED_MODULE_9__.useFormSubmission,\n        _components_FormHandlers__WEBPACK_IMPORTED_MODULE_8__.useFormHandlers\n    ];\n});\n_c1 = CreatePerkForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CreatePerkForm);\nvar _c, _c1;\n$RefreshReg$(_c, \"FormFields\");\n$RefreshReg$(_c1, \"CreatePerkForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/perks/create-perk-form.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useFileUpload.ts":
/*!************************************!*\
  !*** ./src/hooks/useFileUpload.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFileUpload: () => (/* binding */ useFileUpload)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n\n\nconst useFileUpload = (param)=>{\n    let { formData, setFormData, errors, setErrors, fieldName, errorFieldName, options = {} } = param;\n    const { maxSize = _config_environment__WEBPACK_IMPORTED_MODULE_1__.UPLOAD_CONFIG.MAX_FILE_SIZE, allowedTypes = _config_environment__WEBPACK_IMPORTED_MODULE_1__.UPLOAD_CONFIG.ALLOWED_FILE_TYPES, fileInputId = \"file-upload\" } = options;\n    const errorKey = errorFieldName || String(fieldName);\n    const validateFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[validateFile]\": (file)=>{\n            if (file.size > maxSize) {\n                return \"File must be less than \".concat(Math.round(maxSize / (1024 * 1024)), \"MB\");\n            }\n            if (!allowedTypes.includes(file.type)) {\n                return \"Only \".concat(allowedTypes.map({\n                    \"useFileUpload.useCallback[validateFile]\": (type)=>type.split(\"/\")[1].toUpperCase()\n                }[\"useFileUpload.useCallback[validateFile]\"]).join(\", \"), \" files are allowed\");\n            }\n            return \"\";\n        }\n    }[\"useFileUpload.useCallback[validateFile]\"], [\n        maxSize,\n        allowedTypes\n    ]);\n    const handleFileChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[handleFileChange]\": (e)=>{\n            if (e.target.files && e.target.files[0]) {\n                const file = e.target.files[0];\n                // Update form data\n                setFormData({\n                    \"useFileUpload.useCallback[handleFileChange]\": (prev)=>({\n                            ...prev,\n                            [fieldName]: file\n                        })\n                }[\"useFileUpload.useCallback[handleFileChange]\"]);\n                // Validate file\n                const error = validateFile(file);\n                setErrors({\n                    \"useFileUpload.useCallback[handleFileChange]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: error\n                        })\n                }[\"useFileUpload.useCallback[handleFileChange]\"]);\n            }\n        }\n    }[\"useFileUpload.useCallback[handleFileChange]\"], [\n        fieldName,\n        errorKey\n    ]);\n    const handleDeleteFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[handleDeleteFile]\": ()=>{\n            // Clear form data\n            setFormData({\n                \"useFileUpload.useCallback[handleDeleteFile]\": (prev)=>({\n                        ...prev,\n                        [fieldName]: null\n                    })\n            }[\"useFileUpload.useCallback[handleDeleteFile]\"]);\n            // Clear error\n            setErrors({\n                \"useFileUpload.useCallback[handleDeleteFile]\": (prev)=>({\n                        ...prev,\n                        [errorKey]: \"\"\n                    })\n            }[\"useFileUpload.useCallback[handleDeleteFile]\"]);\n            // Reset file input\n            const fileInput = document.getElementById(fileInputId);\n            if (fileInput) {\n                fileInput.value = \"\";\n            }\n        }\n    }[\"useFileUpload.useCallback[handleDeleteFile]\"], [\n        fieldName,\n        errorKey,\n        fileInputId\n    ]);\n    const setFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileUpload.useCallback[setFile]\": (file)=>{\n            setFormData({\n                \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                        ...prev,\n                        [fieldName]: file\n                    })\n            }[\"useFileUpload.useCallback[setFile]\"]);\n            if (file) {\n                const error = validateFile(file);\n                setErrors({\n                    \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: error\n                        })\n                }[\"useFileUpload.useCallback[setFile]\"]);\n            } else {\n                setErrors({\n                    \"useFileUpload.useCallback[setFile]\": (prev)=>({\n                            ...prev,\n                            [errorKey]: \"\"\n                        })\n                }[\"useFileUpload.useCallback[setFile]\"]);\n            }\n        }\n    }[\"useFileUpload.useCallback[setFile]\"], [\n        fieldName,\n        errorKey\n    ]);\n    return {\n        handleFileChange,\n        handleDeleteFile,\n        setFile,\n        validateFile\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useFileUpload.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useGenericFormHandler.ts":
/*!********************************************!*\
  !*** ./src/hooks/useGenericFormHandler.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGenericFormHandler: () => (/* binding */ useGenericFormHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useGenericFormHandler = (param)=>{\n    let { formData, setFormData, errors, setErrors, validationRules = {} } = param;\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleChange]\": (e)=>{\n            const { name, value } = e.target;\n            setFormData({\n                \"useGenericFormHandler.useCallback[handleChange]\": (prev)=>({\n                        ...prev,\n                        [name]: value\n                    })\n            }[\"useGenericFormHandler.useCallback[handleChange]\"]);\n            // Clear error when user starts typing\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleChange]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[handleChange]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleChange]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const handleCheckboxChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (e)=>{\n            const { name, checked } = e.target;\n            setFormData({\n                \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (prev)=>({\n                        ...prev,\n                        [name]: checked\n                    })\n            }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"]);\n            // Clear error when user changes checkbox\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleCheckboxChange]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleCheckboxChange]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[handleBlur]\": (e)=>{\n            const { name, value } = e.target;\n            const fieldName = name;\n            // Run validation if rule exists\n            const validationRule = validationRules[fieldName];\n            if (validationRule) {\n                const error = validationRule(value, formData);\n                setErrors({\n                    \"useGenericFormHandler.useCallback[handleBlur]\": (prev)=>({\n                            ...prev,\n                            [name]: error\n                        })\n                }[\"useGenericFormHandler.useCallback[handleBlur]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[handleBlur]\"], [\n        validationRules,\n        formData,\n        setErrors\n    ]);\n    const setFieldValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[setFieldValue]\": (name, value)=>{\n            setFormData({\n                \"useGenericFormHandler.useCallback[setFieldValue]\": (prev)=>({\n                        ...prev,\n                        [name]: value\n                    })\n            }[\"useGenericFormHandler.useCallback[setFieldValue]\"]);\n            // Clear error when programmatically setting value\n            if (errors[name]) {\n                setErrors({\n                    \"useGenericFormHandler.useCallback[setFieldValue]\": (prev)=>({\n                            ...prev,\n                            [name]: ''\n                        })\n                }[\"useGenericFormHandler.useCallback[setFieldValue]\"]);\n            }\n        }\n    }[\"useGenericFormHandler.useCallback[setFieldValue]\"], [\n        setFormData,\n        errors,\n        setErrors\n    ]);\n    const validateField = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[validateField]\": (name, value)=>{\n            const validationRule = validationRules[name];\n            if (validationRule) {\n                return validationRule(value, formData);\n            }\n            return '';\n        }\n    }[\"useGenericFormHandler.useCallback[validateField]\"], [\n        validationRules,\n        formData\n    ]);\n    const clearFieldError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGenericFormHandler.useCallback[clearFieldError]\": (name)=>{\n            setErrors({\n                \"useGenericFormHandler.useCallback[clearFieldError]\": (prev)=>({\n                        ...prev,\n                        [name]: ''\n                    })\n            }[\"useGenericFormHandler.useCallback[clearFieldError]\"]);\n        }\n    }[\"useGenericFormHandler.useCallback[clearFieldError]\"], [\n        setErrors\n    ]);\n    return {\n        handleChange,\n        handleCheckboxChange,\n        handleBlur,\n        setFieldValue,\n        validateField,\n        clearFieldError\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useGenericFormHandler.ts\n"));

/***/ })

}]);