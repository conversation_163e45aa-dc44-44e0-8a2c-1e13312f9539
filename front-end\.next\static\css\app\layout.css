/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\constants\\layout.ts","import":"IBM_Plex_Sans","arguments":[{"subsets":["latin"],"variable":"--font-geist-sans","display":"swap","weight":["100","200","300","400","500","600","700"]}],"variableName":"IbmFont"} ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/7b19b489dc6743ba-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/28793f5c5e3d822d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c9c3823090ec8b55-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b3bf17a9041d9433-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2801417b65625cf5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/26d4368bf94c0ec4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'IBM Plex Sans Fallback';src: local("Arial");ascent-override: 101.32%;descent-override: 27.18%;line-gap-override: 0.00%;size-adjust: 101.17%
}.__className_67235b {font-family: 'IBM Plex Sans', 'IBM Plex Sans Fallback';font-style: normal
}.__variable_67235b {--font-geist-sans: 'IBM Plex Sans', 'IBM Plex Sans Fallback'
}

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.0.17 | MIT License | https://tailwindcss.com */
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(0.971 0.013 17.38);
    --color-red-100: oklch(0.936 0.032 17.717);
    --color-red-200: oklch(0.885 0.062 18.334);
    --color-red-300: oklch(0.808 0.114 19.571);
    --color-red-400: oklch(0.704 0.191 22.216);
    --color-red-500: oklch(0.637 0.237 25.331);
    --color-red-600: oklch(0.577 0.245 27.325);
    --color-red-700: oklch(0.505 0.213 27.518);
    --color-red-800: oklch(0.444 0.177 26.899);
    --color-orange-50: oklch(0.98 0.016 73.684);
    --color-orange-100: oklch(0.954 0.038 75.164);
    --color-orange-200: oklch(0.901 0.076 70.697);
    --color-orange-300: oklch(0.837 0.128 66.29);
    --color-orange-400: oklch(0.75 0.183 55.934);
    --color-orange-500: oklch(0.705 0.213 47.604);
    --color-orange-600: oklch(0.646 0.222 41.116);
    --color-orange-700: oklch(0.553 0.195 38.402);
    --color-orange-800: oklch(0.47 0.157 37.304);
    --color-amber-50: oklch(0.987 0.022 95.277);
    --color-amber-100: oklch(0.962 0.059 95.617);
    --color-amber-200: oklch(0.924 0.12 95.746);
    --color-amber-500: oklch(0.769 0.188 70.08);
    --color-amber-600: oklch(0.666 0.179 58.318);
    --color-amber-700: oklch(0.555 0.163 48.998);
    --color-amber-900: oklch(0.414 0.112 45.904);
    --color-yellow-50: oklch(0.987 0.026 102.212);
    --color-yellow-100: oklch(0.973 0.071 103.193);
    --color-yellow-200: oklch(0.945 0.129 101.54);
    --color-yellow-400: oklch(0.852 0.199 91.936);
    --color-yellow-500: oklch(0.795 0.184 86.047);
    --color-yellow-600: oklch(0.681 0.162 75.834);
    --color-yellow-700: oklch(0.554 0.135 66.442);
    --color-yellow-800: oklch(0.476 0.114 61.907);
    --color-green-50: oklch(0.982 0.018 155.826);
    --color-green-100: oklch(0.962 0.044 156.743);
    --color-green-200: oklch(0.925 0.084 155.995);
    --color-green-400: oklch(0.792 0.209 151.711);
    --color-green-500: oklch(0.723 0.219 149.579);
    --color-green-600: oklch(0.627 0.194 149.214);
    --color-green-700: oklch(0.527 0.154 150.069);
    --color-green-800: oklch(0.448 0.119 151.328);
    --color-emerald-100: oklch(0.95 0.052 163.051);
    --color-emerald-500: oklch(0.696 0.17 162.48);
    --color-emerald-600: oklch(0.596 0.145 163.225);
    --color-teal-400: oklch(0.777 0.152 181.912);
    --color-teal-600: oklch(0.6 0.118 184.704);
    --color-cyan-100: oklch(0.956 0.045 203.388);
    --color-cyan-400: oklch(0.789 0.154 211.53);
    --color-cyan-600: oklch(0.609 0.126 221.723);
    --color-blue-50: oklch(0.97 0.014 254.604);
    --color-blue-100: oklch(0.932 0.032 255.585);
    --color-blue-200: oklch(0.882 0.059 254.128);
    --color-blue-300: oklch(0.809 0.105 251.813);
    --color-blue-400: oklch(0.707 0.165 254.624);
    --color-blue-500: oklch(0.623 0.214 259.815);
    --color-blue-600: oklch(0.546 0.245 262.881);
    --color-blue-700: oklch(0.488 0.243 264.376);
    --color-blue-800: oklch(0.424 0.199 265.638);
    --color-blue-900: oklch(0.379 0.146 265.522);
    --color-blue-950: oklch(0.282 0.091 267.935);
    --color-indigo-100: oklch(0.93 0.034 272.788);
    --color-indigo-400: oklch(0.673 0.182 276.935);
    --color-indigo-600: oklch(0.511 0.262 276.966);
    --color-purple-100: oklch(0.946 0.033 307.174);
    --color-purple-400: oklch(0.714 0.203 305.504);
    --color-purple-500: oklch(0.627 0.265 303.9);
    --color-purple-600: oklch(0.558 0.288 302.321);
    --color-purple-700: oklch(0.496 0.265 301.924);
    --color-purple-800: oklch(0.438 0.218 303.724);
    --color-pink-400: oklch(0.718 0.202 349.761);
    --color-pink-600: oklch(0.592 0.249 0.584);
    --color-slate-50: oklch(0.984 0.003 247.858);
    --color-slate-100: oklch(0.968 0.007 247.896);
    --color-slate-200: oklch(0.929 0.013 255.508);
    --color-slate-300: oklch(0.869 0.022 252.894);
    --color-slate-400: oklch(0.704 0.04 256.788);
    --color-slate-500: oklch(0.554 0.046 257.417);
    --color-slate-600: oklch(0.446 0.043 257.281);
    --color-slate-700: oklch(0.372 0.044 257.287);
    --color-slate-900: oklch(0.208 0.042 265.755);
    --color-gray-50: oklch(0.985 0.002 247.839);
    --color-gray-100: oklch(0.967 0.003 264.542);
    --color-gray-200: oklch(0.928 0.006 264.531);
    --color-gray-300: oklch(0.872 0.01 258.338);
    --color-gray-400: oklch(0.707 0.022 261.325);
    --color-gray-500: oklch(0.551 0.027 264.364);
    --color-gray-600: oklch(0.446 0.03 256.802);
    --color-gray-700: oklch(0.373 0.034 259.733);
    --color-gray-800: oklch(0.278 0.033 256.848);
    --color-gray-900: oklch(0.21 0.034 264.665);
    --color-zinc-300: oklch(0.871 0.006 286.286);
    --color-zinc-400: oklch(0.705 0.015 286.067);
    --color-zinc-800: oklch(0.274 0.006 286.033);
    --color-zinc-950: oklch(0.141 0.005 285.823);
    --color-neutral-200: oklch(0.922 0 0);
    --color-neutral-400: oklch(0.708 0 0);
    --color-neutral-800: oklch(0.269 0 0);
    --color-neutral-900: oklch(0.205 0 0);
    --color-stone-300: oklch(0.869 0.005 56.366);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-wide: 0.025em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-2xl: 1rem;
    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --blur-sm: 8px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: color-mix(in oklab, currentColor 50%, transparent);
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .visible {
    visibility: visible;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-2 {
    inset: calc(var(--spacing) * 2);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .-top-10 {
    top: calc(var(--spacing) * -10);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-8 {
    top: calc(var(--spacing) * 8);
  }
  .top-\[6px\] {
    top: 6px;
  }
  .top-\[24px\] {
    top: 24px;
  }
  .top-full {
    top: 100%;
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .-right-5 {
    right: calc(var(--spacing) * -5);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-\[7px\] {
    right: 7px;
  }
  .right-\[28px\] {
    right: 28px;
  }
  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }
  .-bottom-5 {
    bottom: calc(var(--spacing) * -5);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .bottom-full {
    bottom: 100%;
  }
  .-left-10 {
    left: calc(var(--spacing) * -10);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-8 {
    left: calc(var(--spacing) * 8);
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-10 {
    z-index: 10;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .m-auto {
    margin: auto;
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-6 {
    margin-inline: calc(var(--spacing) * 6);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-9 {
    margin-top: calc(var(--spacing) * 9);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-18 {
    margin-top: calc(var(--spacing) * 18);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-auto {
    margin-top: auto;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .-mb-px {
    margin-bottom: -1px;
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-7 {
    margin-bottom: calc(var(--spacing) * 7);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-9 {
    margin-bottom: calc(var(--spacing) * 9);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-14 {
    margin-bottom: calc(var(--spacing) * 14);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-\[28px\] {
    margin-bottom: 28px;
  }
  .mb-\[30px\] {
    margin-bottom: 30px;
  }
  .mb-\[31px\] {
    margin-bottom: 31px;
  }
  .ml-0\.5 {
    margin-left: calc(var(--spacing) * 0.5);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }
  .ml-auto {
    margin-left: auto;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-\[1\.55\/1\] {
    aspect-ratio: 1.55/1;
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-36 {
    height: calc(var(--spacing) * 36);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-56 {
    height: calc(var(--spacing) * 56);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-98 {
    height: calc(var(--spacing) * 98);
  }
  .h-\[10px\] {
    height: 10px;
  }
  .h-\[16px\] {
    height: 16px;
  }
  .h-\[31px\] {
    height: 31px;
  }
  .h-\[34px\] {
    height: 34px;
  }
  .h-\[36px\] {
    height: 36px;
  }
  .h-\[39px\] {
    height: 39px;
  }
  .h-\[40px\] {
    height: 40px;
  }
  .h-\[44px\] {
    height: 44px;
  }
  .h-\[52px\] {
    height: 52px;
  }
  .h-\[54px\] {
    height: 54px;
  }
  .h-\[63px\] {
    height: 63px;
  }
  .h-\[67px\] {
    height: 67px;
  }
  .h-\[70px\] {
    height: 70px;
  }
  .h-\[76px\] {
    height: 76px;
  }
  .h-\[78px\] {
    height: 78px;
  }
  .h-\[120px\] {
    height: 120px;
  }
  .h-\[160px\] {
    height: 160px;
  }
  .h-\[200px\] {
    height: 200px;
  }
  .h-\[280px\] {
    height: 280px;
  }
  .h-\[300px\] {
    height: 300px;
  }
  .h-\[500px\] {
    height: 500px;
  }
  .h-\[600px\] {
    height: 600px;
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-max {
    height: max-content;
  }
  .h-px {
    height: 1px;
  }
  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-\[39px\] {
    max-height: 39px;
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .max-h-\[300px\] {
    max-height: 300px;
  }
  .max-h-\[400px\] {
    max-height: 400px;
  }
  .max-h-\[500px\] {
    max-height: 500px;
  }
  .max-h-\[600px\] {
    max-height: 600px;
  }
  .max-h-full {
    max-height: 100%;
  }
  .min-h-\[39px\] {
    min-height: 39px;
  }
  .min-h-\[120px\] {
    min-height: 120px;
  }
  .min-h-\[200px\] {
    min-height: 200px;
  }
  .min-h-\[400px\] {
    min-height: 400px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\/6 {
    width: calc(5/6 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\[10px\] {
    width: 10px;
  }
  .w-\[34px\] {
    width: 34px;
  }
  .w-\[36px\] {
    width: 36px;
  }
  .w-\[39px\] {
    width: 39px;
  }
  .w-\[40px\] {
    width: 40px;
  }
  .w-\[62px\] {
    width: 62px;
  }
  .w-\[68px\] {
    width: 68px;
  }
  .w-\[100\%\] {
    width: 100%;
  }
  .w-\[192px\] {
    width: 192px;
  }
  .w-\[200px\] {
    width: 200px;
  }
  .w-\[310px\] {
    width: 310px;
  }
  .w-\[400px\] {
    width: 400px;
  }
  .w-\[420px\] {
    width: 420px;
  }
  .w-\[563px\] {
    width: 563px;
  }
  .w-\[565px\] {
    width: 565px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\[39px\] {
    max-width: 39px;
  }
  .max-w-\[80px\] {
    max-width: 80px;
  }
  .max-w-\[100px\] {
    max-width: 100px;
  }
  .max-w-\[459px\] {
    max-width: 459px;
  }
  .max-w-\[580px\] {
    max-width: 580px;
  }
  .max-w-\[600px\] {
    max-width: 600px;
  }
  .max-w-\[697px\] {
    max-width: 697px;
  }
  .max-w-\[705px\] {
    max-width: 705px;
  }
  .max-w-\[800px\] {
    max-width: 800px;
  }
  .max-w-\[1745px\] {
    max-width: 1745px;
  }
  .max-w-\[1840px\] {
    max-width: 1840px;
  }
  .max-w-\[1902px\] {
    max-width: 1902px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-12 {
    min-width: calc(var(--spacing) * 12);
  }
  .min-w-\[39px\] {
    min-width: 39px;
  }
  .min-w-\[80px\] {
    min-width: 80px;
  }
  .min-w-\[120px\] {
    min-width: 120px;
  }
  .min-w-\[200px\] {
    min-width: 200px;
  }
  .min-w-\[250px\] {
    min-width: 250px;
  }
  .min-w-\[290px\] {
    min-width: 290px;
  }
  .min-w-\[300px\] {
    min-width: 300px;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-\[675\] {
    flex: 675;
  }
  .flex-\[792\] {
    flex: 792;
  }
  .flex-\[880\] {
    flex: 880;
  }
  .flex-\[975\] {
    flex: 975;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .place-items-center {
    place-items: center;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-7 {
    gap: calc(var(--spacing) * 7);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-15 {
    gap: calc(var(--spacing) * 15);
  }
  .space-y-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-12 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-y-3 {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(3px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(3px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-\[\#D9E1E7\] {
    :where(& > :not(:last-child)) {
      border-color: #D9E1E7;
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .self-stretch {
    align-self: stretch;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-\[10px\] {
    border-radius: 10px;
  }
  .rounded-\[12px\] {
    border-radius: 12px;
  }
  .rounded-\[20px\] {
    border-radius: 20px;
  }
  .rounded-\[24px\] {
    border-radius: 24px;
  }
  .rounded-\[40px\] {
    border-radius: 40px;
  }
  .rounded-\[50px\] {
    border-radius: 50px;
  }
  .rounded-\[100px\] {
    border-radius: 100px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius);
  }
  .rounded-md {
    border-radius: calc(var(--radius) - 2px);
  }
  .rounded-sm {
    border-radius: calc(var(--radius) - 4px);
  }
  .rounded-xl {
    border-radius: calc(var(--radius) + 4px);
  }
  .rounded-t-2xl {
    border-top-left-radius: var(--radius-2xl);
    border-top-right-radius: var(--radius-2xl);
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
  }
  .rounded-l-md {
    border-top-left-radius: calc(var(--radius) - 2px);
    border-bottom-left-radius: calc(var(--radius) - 2px);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
  }
  .rounded-r-md {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px);
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
  }
  .rounded-br-\[10px\] {
    border-bottom-right-radius: 10px;
  }
  .rounded-br-sm {
    border-bottom-right-radius: calc(var(--radius) - 4px);
  }
  .rounded-bl-\[10px\] {
    border-bottom-left-radius: 10px;
  }
  .rounded-bl-sm {
    border-bottom-left-radius: calc(var(--radius) - 4px);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-\[1px\] {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-8 {
    border-top-style: var(--tw-border-style);
    border-top-width: 8px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-r-8 {
    border-right-style: var(--tw-border-style);
    border-right-width: 8px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-l-8 {
    border-left-style: var(--tw-border-style);
    border-left-width: 8px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-\[\#000000\] {
    border-color: #000000;
  }
  .border-\[\#8BB7A2\] {
    border-color: #8BB7A2;
  }
  .border-\[\#98A2B3\] {
    border-color: #98A2B3;
  }
  .border-\[\#514141\] {
    border-color: #514141;
  }
  .border-\[\#1111111A\] {
    border-color: #1111111A;
  }
  .border-\[\#D0D5DD\] {
    border-color: #D0D5DD;
  }
  .border-\[\#D9E1E7\] {
    border-color: #D9E1E7;
  }
  .border-\[\#E6E6E6\] {
    border-color: #E6E6E6;
  }
  .border-\[\#E7D4A3\] {
    border-color: #E7D4A3;
  }
  .border-\[\#F58A38\] {
    border-color: #F58A38;
  }
  .border-\[\#F58A38\]\/20 {
    border-color: color-mix(in oklab, #F58A38 20%, transparent);
  }
  .border-\[\#F58A38\]\/30 {
    border-color: color-mix(in oklab, #F58A38 30%, transparent);
  }
  .border-\[\#FF6600\] {
    border-color: #FF6600;
  }
  .border-amber-100 {
    border-color: var(--color-amber-100);
  }
  .border-amber-200 {
    border-color: var(--color-amber-200);
  }
  .border-amber-500 {
    border-color: var(--color-amber-500);
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-blue-100 {
    border-color: var(--color-blue-100);
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-100 {
    border-color: var(--color-green-100);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-400 {
    border-color: var(--color-green-400);
  }
  .border-green-500 {
    border-color: var(--color-green-500);
  }
  .border-orange-100 {
    border-color: var(--color-orange-100);
  }
  .border-orange-200 {
    border-color: var(--color-orange-200);
  }
  .border-orange-300 {
    border-color: var(--color-orange-300);
  }
  .border-orange-400 {
    border-color: var(--color-orange-400);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-slate-200 {
    border-color: var(--color-slate-200);
  }
  .border-slate-300 {
    border-color: var(--color-slate-300);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .border-t-blue-600 {
    border-top-color: var(--color-blue-600);
  }
  .border-t-gray-900 {
    border-top-color: var(--color-gray-900);
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .border-r-transparent {
    border-right-color: transparent;
  }
  .border-b-gray-400 {
    border-bottom-color: var(--color-gray-400);
  }
  .border-l-blue-500 {
    border-left-color: var(--color-blue-500);
  }
  .border-l-gray-500 {
    border-left-color: var(--color-gray-500);
  }
  .border-l-green-500 {
    border-left-color: var(--color-green-500);
  }
  .border-l-orange-500 {
    border-left-color: var(--color-orange-500);
  }
  .border-l-red-500 {
    border-left-color: var(--color-red-500);
  }
  .border-l-transparent {
    border-left-color: transparent;
  }
  .bg-\[\#000000c7\] {
    background-color: #000000c7;
  }
  .bg-\[\#3BB266\] {
    background-color: #3BB266;
  }
  .bg-\[\#C4C4C4\] {
    background-color: #C4C4C4;
  }
  .bg-\[\#F2F4F7\] {
    background-color: #F2F4F7;
  }
  .bg-\[\#F5F5F5\] {
    background-color: #F5F5F5;
  }
  .bg-\[\#F58A38\] {
    background-color: #F58A38;
  }
  .bg-\[\#F58A38\]\/10 {
    background-color: color-mix(in oklab, #F58A38 10%, transparent);
  }
  .bg-\[\#FF6600\] {
    background-color: #FF6600;
  }
  .bg-\[\#FF6600\]\/10 {
    background-color: color-mix(in oklab, #FF6600 10%, transparent);
  }
  .bg-\[\#FF6600\]\/20 {
    background-color: color-mix(in oklab, #FF6600 20%, transparent);
  }
  .bg-\[\#FFF7F1\] {
    background-color: #FFF7F1;
  }
  .bg-\[\#FFF7F1\]\/30 {
    background-color: color-mix(in oklab, #FFF7F1 30%, transparent);
  }
  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }
  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }
  .bg-amber-600 {
    background-color: var(--color-amber-600);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/10 {
    background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
  }
  .bg-black\/20 {
    background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
  }
  .bg-black\/30 {
    background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
  }
  .bg-black\/50 {
    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-cyan-100 {
    background-color: var(--color-cyan-100);
  }
  .bg-emerald-100 {
    background-color: var(--color-emerald-100);
  }
  .bg-emerald-500 {
    background-color: var(--color-emerald-500);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }
  .bg-neutral-200 {
    background-color: var(--color-neutral-200);
  }
  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }
  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-50\/30 {
    background-color: color-mix(in oklab, var(--color-red-50) 30%, transparent);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }
  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }
  .bg-slate-300 {
    background-color: var(--color-slate-300);
  }
  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }
  .bg-slate-900 {
    background-color: var(--color-slate-900);
  }
  .bg-stone-300 {
    background-color: var(--color-stone-300);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/20 {
    background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
  }
  .bg-white\/30 {
    background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
  }
  .bg-white\/50 {
    background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
  }
  .bg-white\/60 {
    background-color: color-mix(in oklab, var(--color-white) 60%, transparent);
  }
  .bg-white\/70 {
    background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }
  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }
  .bg-zinc-300 {
    background-color: var(--color-zinc-300);
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-\[\#E0E0E0\] {
    --tw-gradient-from: #E0E0E0;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#F8F9FA\] {
    --tw-gradient-from: #F8F9FA;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#FF6600\] {
    --tw-gradient-from: #FF6600;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#FFF7F1\] {
    --tw-gradient-from: #FFF7F1;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-black\/70 {
    --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-400 {
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-cyan-400 {
    --tw-gradient-from: var(--color-cyan-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-200 {
    --tw-gradient-from: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-400 {
    --tw-gradient-from: var(--color-green-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-400 {
    --tw-gradient-from: var(--color-indigo-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-100 {
    --tw-gradient-from: var(--color-orange-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-400 {
    --tw-gradient-from: var(--color-orange-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-pink-400 {
    --tw-gradient-from: var(--color-pink-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-400 {
    --tw-gradient-from: var(--color-red-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-teal-400 {
    --tw-gradient-from: var(--color-teal-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-400 {
    --tw-gradient-from: var(--color-yellow-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-\[\#FFF9F5\] {
    --tw-gradient-via: #FFF9F5;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-black\/10 {
    --tw-gradient-via: color-mix(in oklab, var(--color-black) 10%, transparent);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-gray-50 {
    --tw-gradient-via: var(--color-gray-50);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-gray-300 {
    --tw-gradient-via: var(--color-gray-300);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-\[\#C4C4C4\] {
    --tw-gradient-to: #C4C4C4;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#F5F5F5\] {
    --tw-gradient-to: #F5F5F5;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#F58A38\] {
    --tw-gradient-to: #F58A38;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#FF8533\] {
    --tw-gradient-to: #FF8533;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#FFF7F1\] {
    --tw-gradient-to: #FFF7F1;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#e55a00\] {
    --tw-gradient-to: #e55a00;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-black {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-cyan-600 {
    --tw-gradient-to: var(--color-cyan-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-50 {
    --tw-gradient-to: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-200 {
    --tw-gradient-to: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-green-600 {
    --tw-gradient-to: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-600 {
    --tw-gradient-to: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-200 {
    --tw-gradient-to: var(--color-orange-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-600 {
    --tw-gradient-to: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-600 {
    --tw-gradient-to: var(--color-pink-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-600 {
    --tw-gradient-to: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-600 {
    --tw-gradient-to: var(--color-teal-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-yellow-600 {
    --tw-gradient-to: var(--color-yellow-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-\[length\:200\%_100\%\] {
    background-size: 200% 100%;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-12 {
    padding: calc(var(--spacing) * 12);
  }
  .p-\[18px\] {
    padding: 18px;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }
  .px-24 {
    padding-inline: calc(var(--spacing) * 24);
  }
  .px-\[12px\] {
    padding-inline: 12px;
  }
  .px-\[20px\] {
    padding-inline: 20px;
  }
  .px-\[27px\] {
    padding-inline: 27px;
  }
  .px-\[50px\] {
    padding-inline: 50px;
  }
  .px-\[99px\] {
    padding-inline: 99px;
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-\[2px\] {
    padding-block: 2px;
  }
  .py-\[5px\] {
    padding-block: 5px;
  }
  .py-\[12px\] {
    padding-block: 12px;
  }
  .py-\[13px\] {
    padding-block: 13px;
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-\[\'IBM_Plex_Sans\'\] {
    font-family: 'IBM Plex Sans';
  }
  .font-\[\'Inter\'\] {
    font-family: 'Inter';
  }
  .font-\[\'Poppins\'\] {
    font-family: 'Poppins';
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[11px\] {
    font-size: 11px;
  }
  .text-\[12px\] {
    font-size: 12px;
  }
  .text-\[14px\] {
    font-size: 14px;
  }
  .text-\[16px\] {
    font-size: 16px;
  }
  .text-\[18px\] {
    font-size: 18px;
  }
  .text-\[20px\] {
    font-size: 20px;
  }
  .text-\[24px\] {
    font-size: 24px;
  }
  .text-\[30px\] {
    font-size: 30px;
  }
  .text-\[32px\] {
    font-size: 32px;
  }
  .text-\[40px\] {
    font-size: 40px;
  }
  .text-\[48px\] {
    font-size: 48px;
  }
  .text-\[66px\] {
    font-size: 66px;
  }
  .leading-7 {
    --tw-leading: calc(var(--spacing) * 7);
    line-height: calc(var(--spacing) * 7);
  }
  .leading-10 {
    --tw-leading: calc(var(--spacing) * 10);
    line-height: calc(var(--spacing) * 10);
  }
  .leading-\[19px\] {
    --tw-leading: 19px;
    line-height: 19px;
  }
  .leading-\[22px\] {
    --tw-leading: 22px;
    line-height: 22px;
  }
  .leading-\[24px\] {
    --tw-leading: 24px;
    line-height: 24px;
  }
  .leading-\[28px\] {
    --tw-leading: 28px;
    line-height: 28px;
  }
  .leading-\[30px\] {
    --tw-leading: 30px;
    line-height: 30px;
  }
  .leading-\[36px\] {
    --tw-leading: 36px;
    line-height: 36px;
  }
  .leading-\[48px\] {
    --tw-leading: 48px;
    line-height: 48px;
  }
  .leading-\[72px\] {
    --tw-leading: 72px;
    line-height: 72px;
  }
  .leading-\[100\%\] {
    --tw-leading: 100%;
    line-height: 100%;
  }
  .leading-\[120\%\] {
    --tw-leading: 120%;
    line-height: 120%;
  }
  .leading-\[140\%\] {
    --tw-leading: 140%;
    line-height: 140%;
  }
  .leading-\[150\%\] {
    --tw-leading: 150%;
    line-height: 150%;
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-snug {
    --tw-leading: var(--leading-snug);
    line-height: var(--leading-snug);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-\[500\] {
    --tw-font-weight: 500;
    font-weight: 500;
  }
  .font-\[600\] {
    --tw-font-weight: 600;
    font-weight: 600;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-\[-0\.01em\] {
    --tw-tracking: -0.01em;
    letter-spacing: -0.01em;
  }
  .tracking-\[0\.02em\] {
    --tw-tracking: 0.02em;
    letter-spacing: 0.02em;
  }
  .tracking-\[0\] {
    --tw-tracking: 0;
    letter-spacing: 0;
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .break-all {
    word-break: break-all;
  }
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-line {
    white-space: pre-line;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-\[\#0C141D\] {
    color: #0C141D;
  }
  .text-\[\#000000\] {
    color: #000000;
  }
  .text-\[\#3BB266\] {
    color: #3BB266;
  }
  .text-\[\#8BB7A2\] {
    color: #8BB7A2;
  }
  .text-\[\#22C55E\] {
    color: #22C55E;
  }
  .text-\[\#090A0B\] {
    color: #090A0B;
  }
  .text-\[\#98A2B3\] {
    color: #98A2B3;
  }
  .text-\[\#525FEB\] {
    color: #525FEB;
  }
  .text-\[\#809FB8\] {
    color: #809FB8;
  }
  .text-\[\#17181A\] {
    color: #17181A;
  }
  .text-\[\#111111\] {
    color: #111111;
  }
  .text-\[\#161616\] {
    color: #161616;
  }
  .text-\[\#262626\] {
    color: #262626;
  }
  .text-\[\#514141\] {
    color: #514141;
  }
  .text-\[\#667085\] {
    color: #667085;
  }
  .text-\[\#788691\] {
    color: #788691;
  }
  .text-\[\#A68952\] {
    color: #A68952;
  }
  .text-\[\#F58A38\] {
    color: #F58A38;
  }
  .text-\[\#F58A38\]\/40 {
    color: color-mix(in oklab, #F58A38 40%, transparent);
  }
  .text-\[\#FF6600\] {
    color: #FF6600;
  }
  .text-\[\#FFFFFF\] {
    color: #FFFFFF;
  }
  .text-\[rgba\(17\,17\,17\,0\.7\)\] {
    color: rgba(17,17,17,0.7);
  }
  .text-amber-600 {
    color: var(--color-amber-600);
  }
  .text-amber-700 {
    color: var(--color-amber-700);
  }
  .text-amber-900 {
    color: var(--color-amber-900);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-blue-900 {
    color: var(--color-blue-900);
  }
  .text-cyan-600 {
    color: var(--color-cyan-600);
  }
  .text-emerald-600 {
    color: var(--color-emerald-600);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-neutral-400 {
    color: var(--color-neutral-400);
  }
  .text-neutral-800 {
    color: var(--color-neutral-800);
  }
  .text-neutral-900 {
    color: var(--color-neutral-900);
  }
  .text-orange-500 {
    color: var(--color-orange-500);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-orange-700 {
    color: var(--color-orange-700);
  }
  .text-orange-800 {
    color: var(--color-orange-800);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-slate-400 {
    color: var(--color-slate-400);
  }
  .text-slate-500 {
    color: var(--color-slate-500);
  }
  .text-slate-600 {
    color: var(--color-slate-600);
  }
  .text-slate-700 {
    color: var(--color-slate-700);
  }
  .text-slate-900 {
    color: var(--color-slate-900);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white\/90 {
    color: color-mix(in oklab, var(--color-white) 90%, transparent);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .text-zinc-800 {
    color: var(--color-zinc-800);
  }
  .text-zinc-950 {
    color: var(--color-zinc-950);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .placeholder-gray-400 {
    &::placeholder {
      color: var(--color-gray-400);
    }
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-90 {
    opacity: 90%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0_4px_80px_0_rgba\(0\,0\,0\,0\.05\)\] {
    --tw-shadow: 0 4px 80px 0 var(--tw-shadow-color, rgba(0,0,0,0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-blue-200 {
    --tw-ring-color: var(--color-blue-200);
  }
  .ring-white {
    --tw-ring-color: var(--color-white);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .outline-1 {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .outline-\[0\.50px\] {
    outline-style: var(--tw-outline-style);
    outline-width: 0.50px;
  }
  .outline-offset-\[-0\.50px\] {
    outline-offset: -0.50px;
  }
  .outline-offset-\[-1px\] {
    outline-offset: -1px;
  }
  .outline-blue-500 {
    outline-color: var(--color-blue-500);
  }
  .outline-blue-950 {
    outline-color: var(--color-blue-950);
  }
  .outline-gray-300 {
    outline-color: var(--color-gray-300);
  }
  .outline-green-500 {
    outline-color: var(--color-green-500);
  }
  .outline-red-500 {
    outline-color: var(--color-red-500);
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-lg {
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-\[1\.50px\] {
    --tw-backdrop-blur: blur(1.50px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-\[20px\] {
    --tw-backdrop-blur: blur(20px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .running {
    animation-play-state: running;
  }
  .group-open\:rotate-180 {
    &:is(:where(.group):is([open], :popover-open, :open) *) {
      rotate: 180deg;
    }
  }
  .group-hover\:scale-105 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-110 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:bg-gray-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .group-hover\:bg-white\/80 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }
  .group-hover\:text-\[\#FF6600\] {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: #FF6600;
      }
    }
  }
  .group-hover\:text-gray-800 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-hover\:brightness-0 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-brightness: brightness(0%);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .group-hover\:invert {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-invert: invert(100%);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .placeholder\:text-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .placeholder\:text-slate-500 {
    &::placeholder {
      color: var(--color-slate-500);
    }
  }
  .focus-within\:outline-\[\#F58A38\] {
    &:focus-within {
      outline-color: #F58A38;
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-\[1\.02\] {
    &:hover {
      @media (hover: hover) {
        scale: 1.02;
      }
    }
  }
  .hover\:border-\[\#E55A00\] {
    &:hover {
      @media (hover: hover) {
        border-color: #E55A00;
      }
    }
  }
  .hover\:border-\[\#F58A38\] {
    &:hover {
      @media (hover: hover) {
        border-color: #F58A38;
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-\[\#E55A00\] {
    &:hover {
      @media (hover: hover) {
        background-color: #E55A00;
      }
    }
  }
  .hover\:bg-\[\#F58A38\] {
    &:hover {
      @media (hover: hover) {
        background-color: #F58A38;
      }
    }
  }
  .hover\:bg-\[\#FF6600\] {
    &:hover {
      @media (hover: hover) {
        background-color: #FF6600;
      }
    }
  }
  .hover\:bg-\[\#FF6600\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #FF6600 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#FFF7F1\] {
    &:hover {
      @media (hover: hover) {
        background-color: #FFF7F1;
      }
    }
  }
  .hover\:bg-\[\#e55a00\] {
    &:hover {
      @media (hover: hover) {
        background-color: #e55a00;
      }
    }
  }
  .hover\:bg-amber-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-700);
      }
    }
  }
  .hover\:bg-black {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-black);
      }
    }
  }
  .hover\:bg-black\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
      }
    }
  }
  .hover\:bg-black\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
  }
  .hover\:bg-blue-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-100);
      }
    }
  }
  .hover\:bg-blue-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-200);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-300 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-gray-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-800);
      }
    }
  }
  .hover\:bg-green-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-100);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-orange-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-100);
      }
    }
  }
  .hover\:bg-orange-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-500);
      }
    }
  }
  .hover\:bg-orange-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-600);
      }
    }
  }
  .hover\:bg-purple-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-600);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-slate-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-50);
      }
    }
  }
  .hover\:bg-slate-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-100);
      }
    }
  }
  .hover\:bg-slate-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-200);
      }
    }
  }
  .hover\:bg-slate-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-700);
      }
    }
  }
  .hover\:bg-white\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }
  .hover\:bg-white\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }
  .hover\:bg-yellow-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-700);
      }
    }
  }
  .hover\:bg-zinc-400 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-zinc-400);
      }
    }
  }
  .hover\:from-\[\#e55a00\] {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: #e55a00;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-black {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-black);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-gray-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-gray-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-\[\#cc5200\] {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: #cc5200;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-gray-200 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-gray-200);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-gray-800 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-gray-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:text-\[\#F58A38\] {
    &:hover {
      @media (hover: hover) {
        color: #F58A38;
      }
    }
  }
  .hover\:text-\[\#FF6600\] {
    &:hover {
      @media (hover: hover) {
        color: #FF6600;
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-red-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-slate-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-slate-600);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:opacity-80 {
    &:hover {
      @media (hover: hover) {
        opacity: 80%;
      }
    }
  }
  .hover\:opacity-90 {
    &:hover {
      @media (hover: hover) {
        opacity: 90%;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:border-\[\#FF6600\] {
    &:focus {
      border-color: #FF6600;
    }
  }
  .focus\:border-blue-500 {
    &:focus {
      border-color: var(--color-blue-500);
    }
  }
  .focus\:border-orange-300 {
    &:focus {
      border-color: var(--color-orange-300);
    }
  }
  .focus\:border-red-500 {
    &:focus {
      border-color: var(--color-red-500);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:bg-white {
    &:focus {
      background-color: var(--color-white);
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-\[\#F58A38\] {
    &:focus {
      --tw-ring-color: #F58A38;
    }
  }
  .focus\:ring-\[\#FF6600\] {
    &:focus {
      --tw-ring-color: #FF6600;
    }
  }
  .focus\:ring-amber-500 {
    &:focus {
      --tw-ring-color: var(--color-amber-500);
    }
  }
  .focus\:ring-black {
    &:focus {
      --tw-ring-color: var(--color-black);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-gray-500 {
    &:focus {
      --tw-ring-color: var(--color-gray-500);
    }
  }
  .focus\:ring-green-500 {
    &:focus {
      --tw-ring-color: var(--color-green-500);
    }
  }
  .focus\:ring-red-500 {
    &:focus {
      --tw-ring-color: var(--color-red-500);
    }
  }
  .focus\:ring-slate-500 {
    &:focus {
      --tw-ring-color: var(--color-slate-500);
    }
  }
  .focus\:ring-offset-0 {
    &:focus {
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-\[0\.98\] {
    &:active {
      scale: 0.98;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:bg-gray-400 {
    &:disabled {
      background-color: var(--color-gray-400);
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .disabled\:hover\:bg-\[\#FF6600\] {
    &:disabled {
      &:hover {
        @media (hover: hover) {
          background-color: #FF6600;
        }
      }
    }
  }
  .disabled\:hover\:bg-white {
    &:disabled {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-white);
        }
      }
    }
  }
  .disabled\:hover\:text-gray-500 {
    &:disabled {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-500);
        }
      }
    }
  }
  .max-2xl\:justify-between {
    @media (width < 96rem) {
      justify-content: space-between;
    }
  }
  .max-md\:justify-between {
    @media (width < 48rem) {
      justify-content: space-between;
    }
  }
  .sm\:my-6 {
    @media (width >= 40rem) {
      margin-block: calc(var(--spacing) * 6);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-2 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 2);
    }
  }
  .sm\:mt-3 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 3);
    }
  }
  .sm\:mt-4 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .sm\:mt-6 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .sm\:mt-8 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .sm\:mb-0 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:h-3 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 3);
    }
  }
  .sm\:h-30 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 30);
    }
  }
  .sm\:w-3 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 3);
    }
  }
  .sm\:w-30 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 30);
    }
  }
  .sm\:max-w-\[70px\] {
    @media (width >= 40rem) {
      max-width: 70px;
    }
  }
  .sm\:max-w-\[120px\] {
    @media (width >= 40rem) {
      max-width: 120px;
    }
  }
  .sm\:min-w-\[150px\] {
    @media (width >= 40rem) {
      min-width: 150px;
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:items-end {
    @media (width >= 40rem) {
      align-items: flex-end;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:gap-3 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 3);
    }
  }
  .sm\:gap-4 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .sm\:gap-5 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 5);
    }
  }
  .sm\:gap-12 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .sm\:space-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:rounded-2xl {
    @media (width >= 40rem) {
      border-radius: var(--radius-2xl);
    }
  }
  .sm\:p-4 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .sm\:p-5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .sm\:px-4 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:py-2 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .sm\:py-4 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .sm\:text-2xl {
    @media (width >= 40rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-4xl {
    @media (width >= 40rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xl {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .sm\:text-xs {
    @media (width >= 40rem) {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .sm\:text-\[18px\] {
    @media (width >= 40rem) {
      font-size: 18px;
    }
  }
  .sm\:text-\[28px\] {
    @media (width >= 40rem) {
      font-size: 28px;
    }
  }
  .md\:top-8 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 8);
    }
  }
  .md\:right-8 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 8);
    }
  }
  .md\:my-8 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 8);
    }
  }
  .md\:mt-4 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .md\:mt-9 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 9);
    }
  }
  .md\:mt-12 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 12);
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:w-44 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 44);
    }
  }
  .md\:w-\[18\.66\%\] {
    @media (width >= 48rem) {
      width: 18.66%;
    }
  }
  .md\:w-\[68\%\] {
    @media (width >= 48rem) {
      width: 68%;
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:max-w-96 {
    @media (width >= 48rem) {
      max-width: calc(var(--spacing) * 96);
    }
  }
  .md\:max-w-\[90px\] {
    @media (width >= 48rem) {
      max-width: 90px;
    }
  }
  .md\:max-w-\[150px\] {
    @media (width >= 48rem) {
      max-width: 150px;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-5 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-4 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-5 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-6 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:gap-8 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .md\:gap-12 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .md\:gap-16 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .md\:rounded-\[48px\] {
    @media (width >= 48rem) {
      border-radius: 48px;
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:p-10 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 10);
    }
  }
  .md\:p-12 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 12);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .md\:px-6 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:px-8 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\:px-13 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 13);
    }
  }
  .md\:px-\[16px\] {
    @media (width >= 48rem) {
      padding-inline: 16px;
    }
  }
  .md\:py-6 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 6);
    }
  }
  .md\:pt-8 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 8);
    }
  }
  .md\:pr-\[110px\] {
    @media (width >= 48rem) {
      padding-right: 110px;
    }
  }
  .md\:pb-8 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }
  .md\:pb-\[97px\] {
    @media (width >= 48rem) {
      padding-bottom: 97px;
    }
  }
  .md\:pl-\[110px\] {
    @media (width >= 48rem) {
      padding-left: 110px;
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-\[20px\] {
    @media (width >= 48rem) {
      font-size: 20px;
    }
  }
  .md\:text-\[32px\] {
    @media (width >= 48rem) {
      font-size: 32px;
    }
  }
  .md\:leading-\[27px\] {
    @media (width >= 48rem) {
      --tw-leading: 27px;
      line-height: 27px;
    }
  }
  .md\:leading-\[36px\] {
    @media (width >= 48rem) {
      --tw-leading: 36px;
      line-height: 36px;
    }
  }
  .md\:leading-\[normal\] {
    @media (width >= 48rem) {
      --tw-leading: normal;
      line-height: normal;
    }
  }
  .md\:max-2xl\:w-\[30\%\] {
    @media (width >= 48rem) {
      @media (width < 96rem) {
        width: 30%;
      }
    }
  }
  .lg\:top-\[12px\] {
    @media (width >= 64rem) {
      top: 12px;
    }
  }
  .lg\:right-\[15px\] {
    @media (width >= 64rem) {
      right: 15px;
    }
  }
  .lg\:m-0 {
    @media (width >= 64rem) {
      margin: calc(var(--spacing) * 0);
    }
  }
  .lg\:my-12 {
    @media (width >= 64rem) {
      margin-block: calc(var(--spacing) * 12);
    }
  }
  .lg\:mt-15 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 15);
    }
  }
  .lg\:mt-16 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 16);
    }
  }
  .lg\:mt-52 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 52);
    }
  }
  .lg\:ml-8 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 8);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:inline {
    @media (width >= 64rem) {
      display: inline;
    }
  }
  .lg\:h-5 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 5);
    }
  }
  .lg\:h-8 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 8);
    }
  }
  .lg\:h-\[44px\] {
    @media (width >= 64rem) {
      height: 44px;
    }
  }
  .lg\:h-\[46px\] {
    @media (width >= 64rem) {
      height: 46px;
    }
  }
  .lg\:h-\[53px\] {
    @media (width >= 64rem) {
      height: 53px;
    }
  }
  .lg\:h-\[56px\] {
    @media (width >= 64rem) {
      height: 56px;
    }
  }
  .lg\:w-5 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 5);
    }
  }
  .lg\:w-8 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 8);
    }
  }
  .lg\:w-\[44px\] {
    @media (width >= 64rem) {
      width: 44px;
    }
  }
  .lg\:w-\[53px\] {
    @media (width >= 64rem) {
      width: 53px;
    }
  }
  .lg\:w-\[113px\] {
    @media (width >= 64rem) {
      width: 113px;
    }
  }
  .lg\:max-w-\[40px\] {
    @media (width >= 64rem) {
      max-width: 40px;
    }
  }
  .lg\:max-w-\[200px\] {
    @media (width >= 64rem) {
      max-width: 200px;
    }
  }
  .lg\:flex-1 {
    @media (width >= 64rem) {
      flex: 1;
    }
  }
  .lg\:basis-\[675px\] {
    @media (width >= 64rem) {
      flex-basis: 675px;
    }
  }
  .lg\:basis-\[792px\] {
    @media (width >= 64rem) {
      flex-basis: 792px;
    }
  }
  .lg\:basis-\[880px\] {
    @media (width >= 64rem) {
      flex-basis: 880px;
    }
  }
  .lg\:basis-\[975px\] {
    @media (width >= 64rem) {
      flex-basis: 975px;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-5 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:items-start {
    @media (width >= 64rem) {
      align-items: flex-start;
    }
  }
  .lg\:justify-between {
    @media (width >= 64rem) {
      justify-content: space-between;
    }
  }
  .lg\:justify-end {
    @media (width >= 64rem) {
      justify-content: flex-end;
    }
  }
  .lg\:justify-start {
    @media (width >= 64rem) {
      justify-content: flex-start;
    }
  }
  .lg\:gap-3 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 3);
    }
  }
  .lg\:gap-4 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .lg\:gap-6 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .lg\:gap-16 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .lg\:space-y-0 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .lg\:p-2 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 2);
    }
  }
  .lg\:px-6 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:py-4 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .lg\:py-8 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 8);
    }
  }
  .lg\:pr-6 {
    @media (width >= 64rem) {
      padding-right: calc(var(--spacing) * 6);
    }
  }
  .lg\:text-2xl {
    @media (width >= 64rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .lg\:text-4xl {
    @media (width >= 64rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .lg\:text-6xl {
    @media (width >= 64rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .lg\:text-base {
    @media (width >= 64rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:text-lg {
    @media (width >= 64rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:text-xl {
    @media (width >= 64rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .xl\:col-span-1 {
    @media (width >= 80rem) {
      grid-column: span 1 / span 1;
    }
  }
  .xl\:col-span-2 {
    @media (width >= 80rem) {
      grid-column: span 2 / span 2;
    }
  }
  .xl\:mt-8 {
    @media (width >= 80rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .xl\:mt-18 {
    @media (width >= 80rem) {
      margin-top: calc(var(--spacing) * 18);
    }
  }
  .xl\:mt-32 {
    @media (width >= 80rem) {
      margin-top: calc(var(--spacing) * 32);
    }
  }
  .xl\:inline {
    @media (width >= 80rem) {
      display: inline;
    }
  }
  .xl\:w-\[55\.42\%\] {
    @media (width >= 80rem) {
      width: 55.42%;
    }
  }
  .xl\:max-w-\[50px\] {
    @media (width >= 80rem) {
      max-width: 50px;
    }
  }
  .xl\:grid-cols-3 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-4 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .xl\:flex-row {
    @media (width >= 80rem) {
      flex-direction: row;
    }
  }
  .xl\:gap-3 {
    @media (width >= 80rem) {
      gap: calc(var(--spacing) * 3);
    }
  }
  .xl\:gap-8 {
    @media (width >= 80rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .xl\:px-6 {
    @media (width >= 80rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .xl\:text-2xl {
    @media (width >= 80rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .xl\:text-3xl {
    @media (width >= 80rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .xl\:text-7xl {
    @media (width >= 80rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .xl\:text-xl {
    @media (width >= 80rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .\32 xl\:grid-cols-4 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .\32 xl\:grid-cols-5 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .\32 xl\:gap-4 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .\32 xl\:gap-18 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 18);
    }
  }
  .\32 xl\:gap-36 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 36);
    }
  }
  .\32 xl\:text-2xl {
    @media (width >= 96rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .\32 xl\:text-3xl {
    @media (width >= 96rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .\32 xl\:text-4xl {
    @media (width >= 96rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .dark\:border-gray-600 {
    &:is(.dark *) {
      border-color: var(--color-gray-600);
    }
  }
  .dark\:bg-gray-800 {
    &:is(.dark *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:text-white {
    &:is(.dark *) {
      color: var(--color-white);
    }
  }
  .\[\&\>span\]\:text-white {
    &>span {
      color: var(--color-white);
    }
  }
}
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}
@layer base {
  * {
    border-color: var(--border);
    outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
  [dir="rtl"] {
    direction: rtl;
  }
  [dir="rtl"] .text-left {
    text-align: right;
  }
  [dir="rtl"] .text-right {
    text-align: left;
  }
  [dir="rtl"] .ml-auto {
    margin-left: unset;
    margin-right: auto;
  }
  [dir="rtl"] .mr-auto {
    margin-right: unset;
    margin-left: auto;
  }
  [dir="rtl"] .pl-4 {
    padding-left: unset;
    padding-right: 1rem;
  }
  [dir="rtl"] .pr-4 {
    padding-right: unset;
    padding-left: 1rem;
  }
  [dir="rtl"] .border-l {
    border-left: unset;
    border-right: 1px solid;
  }
  [dir="rtl"] .border-r {
    border-right: unset;
    border-left: 1px solid;
  }
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraLightItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
}
button {
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}
div[class^='hidden lg:flex items-center gap-4'] a {
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}
div[class^='hidden lg:flex items-center gap-4'] a:hover img {
  filter: brightness(0) invert(1) !important;
  opacity: 1 !important;
}
div[class^='hidden lg:flex items-center gap-4'] a:hover {
  background-color: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
}
div[class^='hidden lg:flex items-center gap-4'] a:not(.bg-black):hover span {
  color: white !important;
}
div[class^='hidden lg:flex items-center gap-4'] div {
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}
div[class^='hidden lg:flex items-center gap-4'] div:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.hidden.md\:flex.w-full div.bg-neutral-200:hover {
  background-color: #d0d0d0 !important;
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}
div.bg-neutral-200:hover {
  background-color: #d0d0d0 !important;
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}
.hidden.md\:flex.w-full div.bg-black:hover {
  box-shadow: none;
}
button:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  filter: brightness(90%);
}
button.bg-black:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  background-color: #333333;
  filter: none;
}
button.bg-white:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-gray-100:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-slate-100:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#fff']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#f']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  background-color: #e6e6e6;
  filter: none;
}
button.bg-blue-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-blue-600:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='ff']:hover {
  filter: none;
  background-color: #2563eb;
}
button.bg-green-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-\[\#3BB266\]:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#3BB266']:not([class*='menu']):not(
    [class*='notification']
  ):not([class*='profile']):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='bb']:hover {
  filter: none;
  background-color: #309854;
}
button.bg-red-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-red-600:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='f']:hover[class*='3']:hover {
  filter: none;
  background-color: #f58a38;
}
button:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):active {
  transform: scale(0.98);
}
button[class*='menu'],
button[class*='notification'],
button[class*='profile'],
button[class*='toggle'],
button[class*='nav-'],
button[class*='header-'] {
  transition: none !important;
  transform: none !important;
  filter: none !important;
}
.w-\[740px\].h-16.bg-white,
.w-\[581px\].h-\[67px\].bg-white {
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease !important;
}
.w-\[740px\].h-16.bg-white:hover,
.w-\[581px\].h-\[67px\].bg-white:hover {
  background-color: black !important;
}
div.w-\[740px\].h-16.bg-white,
div.w-\[581px\].h-\[67px\].bg-white {
  color: black;
}
div.w-\[740px\].h-16.bg-white:hover,
div.w-\[581px\].h-\[67px\].bg-white:hover {
  color: white !important;
}
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
  }
  70% {
    box-shadow: 0 0 5px 7px rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}
.w-\[740px\].h-16.bg-white:hover,
.w-\[581px\].h-\[67px\].bg-white:hover {
  animation: pulse-glow 2s infinite;
}
button.bg-black.text-white.px-6.py-3.text-base.font-medium {
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  box-shadow: none;
}
button.bg-black.text-white.px-6.py-3.text-base.font-medium:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
}
button.bg-black.text-white.px-6.py-3.text-base.font-medium::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}
button.bg-black.text-white.px-6.py-3.text-base.font-medium:hover::after {
  left: 100%;
}
div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}
div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}
div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer:hover::after {
  left: 100%;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

