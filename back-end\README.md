# FunHi BankEnd Application


## Getting Started

### 1: Prerequisites

- [Node.js](https://nodejs.org/) (v14 or above)
- [PostgreSQL](https://www.postgresql.org/)
- [Yarn](https://yarnpkg.com/) or `npm`

### 2: Installation

1. Clone the repository
2. Install dependencies:
```
npm install
# or
yarn install
```

### 3: Setup database:
  - 1: Install PostgreSQL (https://postgresapp.com/downloads.html (for Mac OS))
  - 2: Create some DB (https://eggerapps.at/postico/ (for Mac OS))

### 4: Go ahead set env...

   - cd /FunHi-Back-End
   - cp .env.example .env
   - Set required credential in .env


### 5: Migrate Database: Run migration to set base SQL schema
```
npm run db:migrate
```

### 6: Start the development server:
```
npm run dev:server
# or
npm run start:server
```

### 7: Server is running on localhost:8080



## License

This project is licensed under the MIT License - see the LICENSE file for details.
