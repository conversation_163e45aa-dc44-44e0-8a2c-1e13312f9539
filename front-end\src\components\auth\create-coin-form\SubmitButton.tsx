"use client";

import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import React from "react";
import { formFieldVariants } from "./constants";
import { useTranslation } from '@/hooks/useTranslation';

interface SubmitButtonProps {
  isSubmitting: boolean;
}

const SubmitButton: React.FC<SubmitButtonProps> = ({ isSubmitting }) => {
  const { t } = useTranslation();
  return (
    <motion.button
      type="submit"
      disabled={isSubmitting}
      className={`w-full h-[52px] px-5 py-2 bg-[#FF6600] rounded-sm inline-flex justify-center items-center gap-2.5 mt-3 mb-0 mx-auto transition-all duration-200 ${
        isSubmitting
          ? "opacity-70 cursor-not-allowed"
          : "hover:bg-[#e55a00]"
      }`}
      variants={formFieldVariants}
      whileHover={!isSubmitting ? { scale: 1.01 } : {}}
      whileTap={!isSubmitting ? { scale: 0.99 } : {}}
    >
      {isSubmitting ? (
        <>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            <Loader2 size={20} />
          </motion.div>
          {t('createCoinForm.processing')}
        </>
      ) : (
        <div className="text-center justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
          {t('createCoinForm.createMyCoin')}
        </div>
      )}
    </motion.button>
  );
};

export default SubmitButton; 