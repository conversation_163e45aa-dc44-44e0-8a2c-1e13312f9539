const _User = require("./User");
const _Token = require("./Tokens");
const _Perk = require("./Perks");
const _Comments = require("./Comments");
const _Reviews = require("./Reviews");
const _PerkPurchased = require("./PerkPurchased");
const _TokenPurchased = require("./TokenPurchased");
const _Airdrop = require("./Airdrop");
const _AirdropLog = require("./AirDropLogs");
const _userLogs = require("./userLogs");
const _Follow = require("./Follow");
const Message = require("./Message");
const ChatRoom = require("./ChatRoom");
const _Dispute = require("./Dispute");
const _Moderator = require("./Moderator");
const _Notification = require("./Notification");







function initModels(sequelize) {
  const User = _User.initModel(sequelize);
  const Token = _Token.initModel(sequelize);
  const Perk = _Perk.initModel(sequelize);
  const Comments = _Comments.initModel(sequelize);
  const Reviews = _Reviews.initModel(sequelize);
  const PerkPurchased = _PerkPurchased.initModel(sequelize);
  const TokenPurchased = _TokenPurchased.initModel(sequelize);
  const Airdrop = _Airdrop.initModel(sequelize);
  const AirdropLog = _AirdropLog.initModel(sequelize);
  const userLogs = _userLogs.initModel(sequelize);
  const Follow = _Follow.initModel(sequelize);
  const MessageModel = Message.initModel(sequelize);
  const ChatRoomModel = ChatRoom.initModel(sequelize);
  const Dispute = _Dispute.initModel(sequelize);
  const Moderator = _Moderator.initModel(sequelize);
  const Notification = _Notification.initModel(sequelize);

  

  // Existing associations
  Token.hasMany(Comments, { foreignKey: "Token_ID", as: "comments" });
  Comments.belongsTo(Token, { foreignKey: "Token_ID", as: "token" });

  User.hasMany(Token, { foreignKey: "userId", as: "tokens" });
  Token.belongsTo(User, { foreignKey: "userId", as: "user" });

  User.hasMany(Perk, { foreignKey: "userId", as: "perks" });
  Perk.belongsTo(User, { foreignKey: "userId", as: "user" });

  Perk.hasMany(Reviews, { foreignKey: "Perk_ID", as: "reviews" });
  Reviews.belongsTo(Perk, { foreignKey: "Perk_ID", as: "token" });

  // ✅ New associations for PerkPurchased
  User.hasMany(PerkPurchased, { foreignKey: "userId", as: "purchasedPerks" });
  PerkPurchased.belongsTo(User, { foreignKey: "userId", as: "user" });

  Perk.hasMany(PerkPurchased, { foreignKey: "perkId", as: "purchases" });
  PerkPurchased.belongsTo(Perk, { foreignKey: "perkId", as: "perkDetails" });

  // ✅ New TokenPurchased associations
  User.hasMany(TokenPurchased, { foreignKey: "userId", as: "purchasedTokens" });
  TokenPurchased.belongsTo(User, { foreignKey: "userId", as: "user" });

  Token.hasMany(TokenPurchased, { foreignKey: "tokenId", as: "purchases" });
  TokenPurchased.belongsTo(Token, { foreignKey: "tokenId", as: "tokenDetails" });

  // ✅ TokenPurchased to Perk association (for escrow trades)
  Perk.hasMany(TokenPurchased, { foreignKey: "perkId", as: "trades" });
  TokenPurchased.belongsTo(Perk, { foreignKey: "perkId", as: "perkDetails" });

  // Followers/Following associations
  User.belongsToMany(User, {
    through: Follow,
    as: 'followers',
    foreignKey: 'followingId',
    otherKey: 'followerId',
  });
  User.belongsToMany(User, {
    through: Follow,
    as: 'following',
    foreignKey: 'followerId',
    otherKey: 'followingId',
  });

  // ✅ Dispute associations
  TokenPurchased.hasOne(Dispute, { foreignKey: "tradeId", as: "dispute" });
  Dispute.belongsTo(TokenPurchased, { foreignKey: "tradeId", as: "trade" });

  User.hasMany(Dispute, { foreignKey: "initiatorId", as: "initiatedDisputes" });
  Dispute.belongsTo(User, { foreignKey: "initiatorId", as: "initiator" });

  User.hasMany(Dispute, { foreignKey: "moderatorId", as: "moderatedDisputes" });
  Dispute.belongsTo(User, { foreignKey: "moderatorId", as: "moderator" });

  // ✅ Moderator associations
  User.hasOne(Moderator, { foreignKey: "userId", as: "moderatorProfile" });
  Moderator.belongsTo(User, { foreignKey: "userId", as: "user" });

  User.hasMany(Moderator, { foreignKey: "addedBy", as: "addedModerators" });
  Moderator.belongsTo(User, { foreignKey: "addedBy", as: "addedByUser" });

  // ✅ Notification associations
  User.hasMany(Notification, { foreignKey: "userId", as: "notifications" });
  Notification.belongsTo(User, { foreignKey: "userId", as: "user" });


  return {
    User,
    Token,
    Perk,
    Comments,
    Reviews,
    PerkPurchased,
    TokenPurchased,
    Airdrop,
    AirdropLog,
    userLogs,
    Message: MessageModel,
    ChatRoom: ChatRoomModel,
    Follow, // Add Follow to returned models
    Dispute,
    Moderator,
    Notification
  };
}

module.exports = initModels;
