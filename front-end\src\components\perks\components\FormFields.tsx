import React from 'react';
import { ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { FORM_CATEGORIES } from '../../../constants';

interface FormFieldsProps {
  formData: {
    name: string;
    description: string;
    price: string;
    fulfillmentLink: string;
    picture: File | null;
    limitedStock: boolean;
    stockAmount: string;
    tokenAmount: string;
    category: string;
  };
  errors: {
    name: string;
    description: string;
    price: string;
    fulfillmentLink: string;
    picture: string;
    stockAmount: string;
    tokenAmount: string;
    category: string;
  };
  handleChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  handleBlur: (
    e: React.FocusEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  handleCheckboxChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleDeletePicture: () => void;
  t: (key: string) => string;
  validationProgress?: { [key: string]: boolean };
}

const FormFields: React.FC<FormFieldsProps> = ({
  formData,
  errors,
  handleChange,
  handleBlur,
  handleCheckboxChange,
  handleFileChange,
  handleDeletePicture,
  t,
  validationProgress = {},
}) => {
  // Helper function to get field validation state
  const getFieldState = (fieldName: string) => {
    const hasError = errors[fieldName as keyof typeof errors];
    const isValidating = validationProgress[fieldName];
    const hasValue = formData[fieldName as keyof typeof formData] && 
                    String(formData[fieldName as keyof typeof formData]).trim() !== '';
    
    return {
      hasError: !!hasError,
      isValidating,
      hasValue,
      isValid: hasValue && !hasError && !isValidating
    };
  };

  // Helper function to get field styling
  const getFieldStyling = (fieldName: string) => {
    const state = getFieldState(fieldName);
    
    if (state.isValidating) {
      return 'outline-blue-500 bg-blue-50';
    } else if (state.hasError) {
      return 'outline-red-500 bg-red-50';
    } else if (state.isValid) {
      return 'outline-green-500 bg-green-50';
    } else {
      return 'outline-gray-300';
    }
  };

  return (
    <div className="flex flex-col gap-5 mx-auto w-full">
      {/* Name Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('name')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <input
              type="text"
              name="name"
              placeholder={t('createPerkForm.name')}
              value={formData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              className="flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
              required
            />
            <div className="w-6 h-6 relative">
              {validationProgress.name && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
          </div>
          <AnimatePresence>
            {errors.name && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.name}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Description Field */}
      <div>
        <div className="relative">
          <div
            className={`h-36 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('description')} inline-flex justify-start items-start gap-3 w-full`}
          >
            <textarea
              name="description"
 placeholder={t('createPerkForm.description')}
               value={formData.description}
              onChange={handleChange}
              className="w-full mt-4 bg-transparent outline-none resize-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
              required
            />
            <div className="w-6 h-6 relative" />
          </div>
          <AnimatePresence>
            {errors.description && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.description}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Price Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('price')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <input
              type="text"
              name="price"
              placeholder="Price"
              value={formData.price}
              onChange={handleChange}
              onBlur={handleBlur}
              className="flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
              required
            />
            <div className="w-6 h-6 relative">
              {validationProgress.price && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
            <div className="justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
              $
            </div>
          </div>
          <AnimatePresence>
            {errors.price && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.price}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Token Amount Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('tokenAmount')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <input
              type="number"
              name="tokenAmount"
              placeholder="Number of tokens buyers receive (e.g., 1)"
              value={formData.tokenAmount}
              onChange={handleChange}
              onBlur={handleBlur}
              min="1"
              className="flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
              required
            />
            <div className="w-6 h-6 relative">
              {validationProgress.tokenAmount && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
          </div>
          <AnimatePresence>
            {errors.tokenAmount && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="text-red-500 text-sm mt-1 flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.tokenAmount}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Category Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('category')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              onBlur={handleBlur}
              className="flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
              required
            >
              <option value="" disabled>
               {t('createPerkForm.selectCategory')}
              </option>
              {FORM_CATEGORIES.map((category) => (
                <option key={category} value={category} className="text-gray-700">
                  {category}
                </option>
              ))}
            </select>
            <div className="w-6 h-6 relative">
              {validationProgress.category && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </div>
          <AnimatePresence>
            {errors.category && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.category}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Fulfillment Link Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('fulfillmentLink')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <input
              type="text"
              name="fulfillmentLink"
            placeholder={t('createPerkForm.fulfillmentLink')}
              value={formData.fulfillmentLink}
              onChange={handleChange}
              onBlur={handleBlur}
              className="flex-1 bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
            />
            <div className="w-6 h-6 relative">
              {validationProgress.fulfillmentLink && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
            <div className="w-7 h-7 bg-zinc-300 rounded-full flex items-center justify-center">
              <div className="justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
                !
              </div>
            </div>
          </div>
          <AnimatePresence>
            {errors.fulfillmentLink && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.fulfillmentLink}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Upload Picture Field */}
      <div>
        <div className="relative">
          <div
            className={`h-12 px-4 relative rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('picture')} inline-flex justify-start items-center gap-3 w-full`}
          >
            <div className="flex-1 text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
            {formData.picture ? formData.picture.name : t('createPerkForm.uploadPicture')}
            </div>
            {formData.picture ? (
              <button
                type="button"
                onClick={handleDeletePicture}
                className="w-24 h-7 bg-red-500 text-white cursor-pointer hover:bg-red-600 transition-colors"
              >
                <div className="justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center">
                                  {t('createPerkForm.clear')}

                </div>
              </button>
            ) : (
              <label className="w-32 md:w-44 h-7 bg-zinc-300 cursor-pointer hover:bg-zinc-400 transition-colors">
                <div className="justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed text-center">
                   {t('createPerkForm.upload')}
                </div>
                <input
                  id="picture-upload"
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="image/*"
                />
              </label>
            )}
          </div>
          <AnimatePresence>
            {errors.picture && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
              >
                <span className="mr-1">⚠</span>
                {errors.picture}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Limited Stock and Stock Field */}
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="limitedStock"
            name="limitedStock"
            checked={formData.limitedStock}
            onChange={handleCheckboxChange}
            className="h-5 w-5 border-2 border-black"
          />
          <label
            htmlFor="limitedStock"
            className="ml-2 text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal"
          >
            {t('createPerkForm.limitedStock')}
          </label>
        </div>

        {formData.limitedStock && (
          <div className="flex flex-col items-center">
            <div className="relative">
              <div
                className={`h-[52px] w-[192px] rounded outline outline-1 outline-offset-[-1px] transition-all duration-200 ${getFieldStyling('stockAmount')} flex items-center justify-between px-4 py-[13px]`}
              >
                <input
                  type="text"
                  name="stockAmount"
                placeholder={t('createPerkForm.stock')}
                  value={formData.stockAmount}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className="w-full bg-transparent outline-none text-gray-400 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed"
                />
                {validationProgress.stockAmount && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                )}
              </div>
              <AnimatePresence>
                {errors.stockAmount && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-1 font-['IBM_Plex_Sans'] flex items-center"
                  >
                    <span className="mr-1">⚠</span>
                    {errors.stockAmount}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FormFields;
