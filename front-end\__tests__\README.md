# Testing Documentation

This document provides comprehensive information about the automated test suite for the FunHi Front-End application.

## Overview

The test suite focuses on **edge cases** and **critical functionality** for:
- ✅ Form validation and user interactions
- ✅ API error handling and responses  
- ✅ Custom hooks and state management
- ✅ Component user flows
- ✅ Error handling system

## Test Structure

```
src/
├── utils/__tests__/
│   ├── formValidation.test.ts      # Form validation edge cases
│   └── errorHandling.test.ts       # Error handling system tests
├── axios/__tests__/
│   └── requests.test.ts            # API interaction tests
├── hooks/__tests__/
│   └── useCreateCoinForm.test.tsx  # Custom hook tests
└── components/__tests__/
    └── create-perk-form.test.tsx   # Component interaction tests
```

## Running Tests

### Quick Start
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Targeted Testing
```bash
# Test specific areas
npm run test:forms      # Form-related tests
npm run test:utils      # Utility function tests  
npm run test:hooks      # Custom hook tests
npm run test:api        # API interaction tests

# Test specific files
npm test formValidation
npm test errorHandling
npm test useCreateCoinForm
```

### CI/CD Testing
```bash
# Run tests for continuous integration
npm run test:ci
```

## Test Categories

### 1. Form Validation Tests (`src/utils/__tests__/formValidation.test.ts`)

Tests comprehensive edge cases for form validation functions:

**Name Validation:**
- Empty/whitespace-only names
- Special characters and emojis
- Unicode characters
- Very long names (1000+ chars)

**Price Validation:**
- Negative prices
- Non-numeric inputs
- Scientific notation
- Edge cases like Infinity/NaN
- Zero values and decimals

**URL Validation:**
- Various URL formats (http/https/no protocol)
- URLs with paths and query parameters  
- Invalid formats and special characters
- Very long URLs

**Ticker Validation:**
- Length boundaries (2-5 characters)
- Case sensitivity requirements
- Special characters and numbers

**Social Media Validation:**
- Platform-specific rules (Telegram/Twitter)
- Handle length limits
- Character restrictions

**Image Validation:**
- File size limits (5MB default, custom limits)
- File type restrictions
- Edge case of exact size limits

### 2. Error Handling Tests (`src/utils/__tests__/errorHandling.test.ts`)

Tests the enhanced error handling system:

**Error Classes:**
- AppError, ValidationError, AuthenticationError
- NetworkError, WalletError, FormError, APIError
- Proper inheritance and serialization

**Toast Messages:**
- Comprehensive message categories
- User-friendly messaging
- Consistent error formatting

**Error Creation Utilities:**
- Factory functions for different error types
- Default vs custom messages
- Error code mapping

**Edge Cases:**
- Null/undefined errors
- Circular references
- Very long error messages
- Special characters in errors
- Production vs development logging

### 3. API Interaction Tests (`src/axios/__tests__/requests.test.ts`)

Tests API interactions with comprehensive error scenarios:

**Authentication APIs:**
- Login/signup with valid data
- 400 validation errors
- 401 authentication failures
- 409 user exists conflicts
- Network timeouts and failures

**CRUD Operations:**
- Creating perks/tokens
- Profile updates
- Purchase operations
- 2FA setup and verification

**Error Response Handling:**
- Different HTTP status codes
- Malformed responses
- Empty response bodies
- Large payloads
- Concurrent requests

**Edge Cases:**
- Special characters in data
- Very large payloads (100KB+)
- Timeout errors
- Connection failures

### 4. Custom Hook Tests (`src/hooks/__tests__/useCreateCoinForm.test.tsx`)

Tests complex form hook behavior:

**State Management:**
- Initial state setup
- Form data updates
- Error state handling
- UI state toggles

**Form Validation:**
- Real-time validation on blur
- Error clearing on input
- Field-specific validation rules

**File Upload:**
- Valid/invalid file handling
- File size validation
- Preview generation
- File removal

**Form Submission:**
- Successful submissions
- Validation failures
- Wallet connection checks
- Image upload during submission
- API error responses

**Edge Cases:**
- Rapid successive submissions
- Component unmount during submission
- Large file uploads
- Special characters in input

### 5. Component Interaction Tests (`src/components/__tests__/create-perk-form.test.tsx`)

Tests user interactions and UI behavior:

**Form Rendering:**
- All form fields present
- Conditional field display
- Mobile responsiveness

**User Interactions:**
- Typing in form fields
- File uploads and deletion
- Checkbox toggling
- Category selection

**Form Submission Flow:**
- Complete form submission
- Success message display
- Loading states
- Form reset after submission

**Error Scenarios:**
- API errors (400, 401, 500)
- Image upload failures
- Validation errors
- Network failures

**Accessibility:**
- Proper form labels
- Error message display
- Keyboard navigation
- Screen reader compatibility

## Coverage Goals

The test suite maintains high coverage standards:

- **Branches:** 70%+ coverage
- **Functions:** 70%+ coverage  
- **Lines:** 70%+ coverage
- **Statements:** 70%+ coverage

Critical areas maintain **90%+** coverage:
- Form validation utilities
- Error handling system
- API request functions

## Best Practices

### Writing New Tests

1. **Follow the AAA Pattern:**
   ```typescript
   // Arrange
   const mockData = { name: 'Test' }
   
   // Act  
   const result = validateName(mockData.name)
   
   // Assert
   expect(result).toBe('')
   ```

2. **Test Edge Cases:**
   ```typescript
   it('should handle very long input', () => {
     const longInput = 'a'.repeat(10000)
     expect(validateName(longInput)).toBe('')
   })
   
   it('should handle special characters', () => {
     const specialInput = 'Test 中文 🚀 @#$%'
     expect(validateName(specialInput)).toBe('')
   })
   ```

3. **Mock External Dependencies:**
   ```typescript
   jest.mock('@/utils/helpers', () => ({
     uploadToPinata: jest.fn()
   }))
   ```

4. **Test Error Boundaries:**
   ```typescript
   it('should handle API errors gracefully', async () => {
     mockAPI.mockRejectedValue(new Error('Network error'))
     
     await expect(apiCall()).rejects.toThrow('Network error')
   })
   ```

### Naming Conventions

- **Test files:** `*.test.ts` or `*.test.tsx`
- **Test descriptions:** Clear, descriptive "should..." statements
- **Mock data:** Prefix with `mock` (e.g., `mockUserData`)
- **Test IDs:** Use `data-testid` for reliable element selection

### Common Patterns

**Testing Async Operations:**
```typescript
it('should handle async submission', async () => {
  await act(async () => {
    await result.current.handleSubmit(mockEvent)
  })
  
  await waitFor(() => {
    expect(mockAPI).toHaveBeenCalled()
  })
})
```

**Testing User Interactions:**
```typescript
it('should update on user input', async () => {
  const user = userEvent.setup()
  
  await user.type(screen.getByTestId('input'), 'test value')
  
  expect(screen.getByTestId('input')).toHaveValue('test value')
})
```

**Testing Error States:**
```typescript
it('should show error message', async () => {
  mockAPI.mockRejectedValue(new Error('API Error'))
  
  await user.click(screen.getByRole('button'))
  
  expect(showErrorToast).toHaveBeenCalledWith('API Error')
})
```

## Continuous Integration

Tests run automatically on:
- **Pull Requests:** Full test suite + coverage report
- **Main Branch:** All tests must pass before merge
- **Nightly Builds:** Extended test suite with performance tests

## Debugging Tests

### Common Issues

1. **Async Testing:** Use `waitFor` and `act` properly
2. **Mock Cleanup:** Always call `jest.clearAllMocks()`
3. **DOM Cleanup:** Use `cleanup()` after each test
4. **Timer Issues:** Mock timers with `jest.useFakeTimers()`

### Debug Commands

```bash
# Run specific test with verbose output
npm test -- --verbose formValidation

# Debug test with breakpoints
npm test -- --inspect-brk formValidation

# Run failed tests only
npm test -- --onlyFailures
```

## Contributing

When adding new features:

1. **Write tests first** (TDD approach)
2. **Cover edge cases** and error scenarios
3. **Test user interactions** thoroughly
4. **Maintain coverage** above threshold
5. **Update documentation** for new test patterns

For questions or test-related issues, please refer to the main project documentation or create an issue. 