"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lit";
exports.ids = ["vendor-chunks/@lit"];
exports.modules = {

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/css-tag.js":
/*!************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/css-tag.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* binding */ CSSResult),\n/* harmony export */   adoptStyles: () => (/* binding */ adoptStyles),\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   getCompatibleStyle: () => (/* binding */ getCompatibleStyle),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* binding */ supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* binding */ unsafeCSS)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nconst supportsAdoptingStyleSheets = global.ShadowRoot &&\n    (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n    'adoptedStyleSheets' in Document.prototype &&\n    'replace' in CSSStyleSheet.prototype;\nconst constructionToken = Symbol();\nconst cssTagCache = new WeakMap();\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nclass CSSResult {\n    constructor(cssText, strings, safeToken) {\n        // This property needs to remain unminified.\n        this['_$cssResult$'] = true;\n        if (safeToken !== constructionToken) {\n            throw new Error('CSSResult is not constructable. Use `unsafeCSS` or `css` instead.');\n        }\n        this.cssText = cssText;\n        this._strings = strings;\n    }\n    // This is a getter so that it's lazy. In practice, this means stylesheets\n    // are not created until the first element instance is made.\n    get styleSheet() {\n        // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n        // constructable.\n        let styleSheet = this._styleSheet;\n        const strings = this._strings;\n        if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n            const cacheable = strings !== undefined && strings.length === 1;\n            if (cacheable) {\n                styleSheet = cssTagCache.get(strings);\n            }\n            if (styleSheet === undefined) {\n                (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(this.cssText);\n                if (cacheable) {\n                    cssTagCache.set(strings, styleSheet);\n                }\n            }\n        }\n        return styleSheet;\n    }\n    toString() {\n        return this.cssText;\n    }\n}\nconst textFromCSSResult = (value) => {\n    // This property needs to remain unminified.\n    if (value['_$cssResult$'] === true) {\n        return value.cssText;\n    }\n    else if (typeof value === 'number') {\n        return value;\n    }\n    else {\n        throw new Error(`Value passed to 'css' function must be a 'css' function result: ` +\n            `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n            `to ensure page security.`);\n    }\n};\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nconst unsafeCSS = (value) => new CSSResult(typeof value === 'string' ? value : String(value), undefined, constructionToken);\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nconst css = (strings, ...values) => {\n    const cssText = strings.length === 1\n        ? strings[0]\n        : values.reduce((acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1], strings[0]);\n    return new CSSResult(cssText, strings, constructionToken);\n};\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic the native feature](https://developer.mozilla.org/en-US/docs/Web/API/ShadowRoot/adoptedStyleSheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nconst adoptStyles = (renderRoot, styles) => {\n    if (supportsAdoptingStyleSheets) {\n        renderRoot.adoptedStyleSheets = styles.map((s) => s instanceof CSSStyleSheet ? s : s.styleSheet);\n    }\n    else {\n        for (const s of styles) {\n            const style = document.createElement('style');\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const nonce = global['litNonce'];\n            if (nonce !== undefined) {\n                style.setAttribute('nonce', nonce);\n            }\n            style.textContent = s.cssText;\n            renderRoot.appendChild(style);\n        }\n    }\n};\nconst cssResultFromStyleSheet = (sheet) => {\n    let cssText = '';\n    for (const rule of sheet.cssRules) {\n        cssText += rule.cssText;\n    }\n    return unsafeCSS(cssText);\n};\nconst getCompatibleStyle = supportsAdoptingStyleSheets ||\n    (global.CSSStyleSheet === undefined)\n    ? (s) => s\n    : (s) => s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n\n\n//# sourceMappingURL=css-tag.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxpdC9yZWFjdGl2ZS1lbGVtZW50L25vZGUvZGV2ZWxvcG1lbnQvY3NzLXRhZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLGVBQWU7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksNkJBQTZCO0FBQ2pDO0FBQ0E7QUFDQSw0REFBNEQ7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVtRztBQUNuRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcQGxpdFxccmVhY3RpdmUtZWxlbWVudFxcbm9kZVxcZGV2ZWxvcG1lbnRcXGNzcy10YWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMTkgR29vZ2xlIExMQ1xuICogU1BEWC1MaWNlbnNlLUlkZW50aWZpZXI6IEJTRC0zLUNsYXVzZVxuICovXG4vLyBBbGxvd3MgbWluaWZpZXJzIHRvIHJlbmFtZSByZWZlcmVuY2VzIHRvIGdsb2JhbFRoaXNcbmNvbnN0IGdsb2JhbCA9IGdsb2JhbFRoaXM7XG4vKipcbiAqIFdoZXRoZXIgdGhlIGN1cnJlbnQgYnJvd3NlciBzdXBwb3J0cyBgYWRvcHRlZFN0eWxlU2hlZXRzYC5cbiAqL1xuY29uc3Qgc3VwcG9ydHNBZG9wdGluZ1N0eWxlU2hlZXRzID0gZ2xvYmFsLlNoYWRvd1Jvb3QgJiZcbiAgICAoZ2xvYmFsLlNoYWR5Q1NTID09PSB1bmRlZmluZWQgfHwgZ2xvYmFsLlNoYWR5Q1NTLm5hdGl2ZVNoYWRvdykgJiZcbiAgICAnYWRvcHRlZFN0eWxlU2hlZXRzJyBpbiBEb2N1bWVudC5wcm90b3R5cGUgJiZcbiAgICAncmVwbGFjZScgaW4gQ1NTU3R5bGVTaGVldC5wcm90b3R5cGU7XG5jb25zdCBjb25zdHJ1Y3Rpb25Ub2tlbiA9IFN5bWJvbCgpO1xuY29uc3QgY3NzVGFnQ2FjaGUgPSBuZXcgV2Vha01hcCgpO1xuLyoqXG4gKiBBIGNvbnRhaW5lciBmb3IgYSBzdHJpbmcgb2YgQ1NTIHRleHQsIHRoYXQgbWF5IGJlIHVzZWQgdG8gY3JlYXRlIGEgQ1NTU3R5bGVTaGVldC5cbiAqXG4gKiBDU1NSZXN1bHQgaXMgdGhlIHJldHVybiB2YWx1ZSBvZiBgY3NzYC10YWdnZWQgdGVtcGxhdGUgbGl0ZXJhbHMgYW5kXG4gKiBgdW5zYWZlQ1NTKClgLiBJbiBvcmRlciB0byBlbnN1cmUgdGhhdCBDU1NSZXN1bHRzIGFyZSBvbmx5IGNyZWF0ZWQgdmlhIHRoZVxuICogYGNzc2AgdGFnIGFuZCBgdW5zYWZlQ1NTKClgLCBDU1NSZXN1bHQgY2Fubm90IGJlIGNvbnN0cnVjdGVkIGRpcmVjdGx5LlxuICovXG5jbGFzcyBDU1NSZXN1bHQge1xuICAgIGNvbnN0cnVjdG9yKGNzc1RleHQsIHN0cmluZ3MsIHNhZmVUb2tlbikge1xuICAgICAgICAvLyBUaGlzIHByb3BlcnR5IG5lZWRzIHRvIHJlbWFpbiB1bm1pbmlmaWVkLlxuICAgICAgICB0aGlzWydfJGNzc1Jlc3VsdCQnXSA9IHRydWU7XG4gICAgICAgIGlmIChzYWZlVG9rZW4gIT09IGNvbnN0cnVjdGlvblRva2VuKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NTU1Jlc3VsdCBpcyBub3QgY29uc3RydWN0YWJsZS4gVXNlIGB1bnNhZmVDU1NgIG9yIGBjc3NgIGluc3RlYWQuJyk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jc3NUZXh0ID0gY3NzVGV4dDtcbiAgICAgICAgdGhpcy5fc3RyaW5ncyA9IHN0cmluZ3M7XG4gICAgfVxuICAgIC8vIFRoaXMgaXMgYSBnZXR0ZXIgc28gdGhhdCBpdCdzIGxhenkuIEluIHByYWN0aWNlLCB0aGlzIG1lYW5zIHN0eWxlc2hlZXRzXG4gICAgLy8gYXJlIG5vdCBjcmVhdGVkIHVudGlsIHRoZSBmaXJzdCBlbGVtZW50IGluc3RhbmNlIGlzIG1hZGUuXG4gICAgZ2V0IHN0eWxlU2hlZXQoKSB7XG4gICAgICAgIC8vIElmIGBzdXBwb3J0c0Fkb3B0aW5nU3R5bGVTaGVldHNgIGlzIHRydWUgdGhlbiB3ZSBhc3N1bWUgQ1NTU3R5bGVTaGVldCBpc1xuICAgICAgICAvLyBjb25zdHJ1Y3RhYmxlLlxuICAgICAgICBsZXQgc3R5bGVTaGVldCA9IHRoaXMuX3N0eWxlU2hlZXQ7XG4gICAgICAgIGNvbnN0IHN0cmluZ3MgPSB0aGlzLl9zdHJpbmdzO1xuICAgICAgICBpZiAoc3VwcG9ydHNBZG9wdGluZ1N0eWxlU2hlZXRzICYmIHN0eWxlU2hlZXQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgY29uc3QgY2FjaGVhYmxlID0gc3RyaW5ncyAhPT0gdW5kZWZpbmVkICYmIHN0cmluZ3MubGVuZ3RoID09PSAxO1xuICAgICAgICAgICAgaWYgKGNhY2hlYWJsZSkge1xuICAgICAgICAgICAgICAgIHN0eWxlU2hlZXQgPSBjc3NUYWdDYWNoZS5nZXQoc3RyaW5ncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoc3R5bGVTaGVldCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgKHRoaXMuX3N0eWxlU2hlZXQgPSBzdHlsZVNoZWV0ID0gbmV3IENTU1N0eWxlU2hlZXQoKSkucmVwbGFjZVN5bmModGhpcy5jc3NUZXh0KTtcbiAgICAgICAgICAgICAgICBpZiAoY2FjaGVhYmxlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNzc1RhZ0NhY2hlLnNldChzdHJpbmdzLCBzdHlsZVNoZWV0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHN0eWxlU2hlZXQ7XG4gICAgfVxuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jc3NUZXh0O1xuICAgIH1cbn1cbmNvbnN0IHRleHRGcm9tQ1NTUmVzdWx0ID0gKHZhbHVlKSA9PiB7XG4gICAgLy8gVGhpcyBwcm9wZXJ0eSBuZWVkcyB0byByZW1haW4gdW5taW5pZmllZC5cbiAgICBpZiAodmFsdWVbJ18kY3NzUmVzdWx0JCddID09PSB0cnVlKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZS5jc3NUZXh0O1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVmFsdWUgcGFzc2VkIHRvICdjc3MnIGZ1bmN0aW9uIG11c3QgYmUgYSAnY3NzJyBmdW5jdGlvbiByZXN1bHQ6IGAgK1xuICAgICAgICAgICAgYCR7dmFsdWV9LiBVc2UgJ3Vuc2FmZUNTUycgdG8gcGFzcyBub24tbGl0ZXJhbCB2YWx1ZXMsIGJ1dCB0YWtlIGNhcmUgYCArXG4gICAgICAgICAgICBgdG8gZW5zdXJlIHBhZ2Ugc2VjdXJpdHkuYCk7XG4gICAgfVxufTtcbi8qKlxuICogV3JhcCBhIHZhbHVlIGZvciBpbnRlcnBvbGF0aW9uIGluIGEge0BsaW5rY29kZSBjc3N9IHRhZ2dlZCB0ZW1wbGF0ZSBsaXRlcmFsLlxuICpcbiAqIFRoaXMgaXMgdW5zYWZlIGJlY2F1c2UgdW50cnVzdGVkIENTUyB0ZXh0IGNhbiBiZSB1c2VkIHRvIHBob25lIGhvbWVcbiAqIG9yIGV4ZmlsdHJhdGUgZGF0YSB0byBhbiBhdHRhY2tlciBjb250cm9sbGVkIHNpdGUuIFRha2UgY2FyZSB0byBvbmx5IHVzZVxuICogdGhpcyB3aXRoIHRydXN0ZWQgaW5wdXQuXG4gKi9cbmNvbnN0IHVuc2FmZUNTUyA9ICh2YWx1ZSkgPT4gbmV3IENTU1Jlc3VsdCh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnID8gdmFsdWUgOiBTdHJpbmcodmFsdWUpLCB1bmRlZmluZWQsIGNvbnN0cnVjdGlvblRva2VuKTtcbi8qKlxuICogQSB0ZW1wbGF0ZSBsaXRlcmFsIHRhZyB3aGljaCBjYW4gYmUgdXNlZCB3aXRoIExpdEVsZW1lbnQnc1xuICoge0BsaW5rY29kZSBMaXRFbGVtZW50LnN0eWxlc30gcHJvcGVydHkgdG8gc2V0IGVsZW1lbnQgc3R5bGVzLlxuICpcbiAqIEZvciBzZWN1cml0eSByZWFzb25zLCBvbmx5IGxpdGVyYWwgc3RyaW5nIHZhbHVlcyBhbmQgbnVtYmVyIG1heSBiZSB1c2VkIGluXG4gKiBlbWJlZGRlZCBleHByZXNzaW9ucy4gVG8gaW5jb3Jwb3JhdGUgbm9uLWxpdGVyYWwgdmFsdWVzIHtAbGlua2NvZGUgdW5zYWZlQ1NTfVxuICogbWF5IGJlIHVzZWQgaW5zaWRlIGFuIGV4cHJlc3Npb24uXG4gKi9cbmNvbnN0IGNzcyA9IChzdHJpbmdzLCAuLi52YWx1ZXMpID0+IHtcbiAgICBjb25zdCBjc3NUZXh0ID0gc3RyaW5ncy5sZW5ndGggPT09IDFcbiAgICAgICAgPyBzdHJpbmdzWzBdXG4gICAgICAgIDogdmFsdWVzLnJlZHVjZSgoYWNjLCB2LCBpZHgpID0+IGFjYyArIHRleHRGcm9tQ1NTUmVzdWx0KHYpICsgc3RyaW5nc1tpZHggKyAxXSwgc3RyaW5nc1swXSk7XG4gICAgcmV0dXJuIG5ldyBDU1NSZXN1bHQoY3NzVGV4dCwgc3RyaW5ncywgY29uc3RydWN0aW9uVG9rZW4pO1xufTtcbi8qKlxuICogQXBwbGllcyB0aGUgZ2l2ZW4gc3R5bGVzIHRvIGEgYHNoYWRvd1Jvb3RgLiBXaGVuIFNoYWRvdyBET00gaXNcbiAqIGF2YWlsYWJsZSBidXQgYGFkb3B0ZWRTdHlsZVNoZWV0c2AgaXMgbm90LCBzdHlsZXMgYXJlIGFwcGVuZGVkIHRvIHRoZVxuICogYHNoYWRvd1Jvb3RgIHRvIFttaW1pYyB0aGUgbmF0aXZlIGZlYXR1cmVdKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9TaGFkb3dSb290L2Fkb3B0ZWRTdHlsZVNoZWV0cykuXG4gKiBOb3RlLCB3aGVuIHNoaW1taW5nIGlzIHVzZWQsIGFueSBzdHlsZXMgdGhhdCBhcmUgc3Vic2VxdWVudGx5IHBsYWNlZCBpbnRvXG4gKiB0aGUgc2hhZG93Um9vdCBzaG91bGQgYmUgcGxhY2VkICpiZWZvcmUqIGFueSBzaGltbWVkIGFkb3B0ZWQgc3R5bGVzLiBUaGlzXG4gKiB3aWxsIG1hdGNoIHNwZWMgYmVoYXZpb3IgdGhhdCBnaXZlcyBhZG9wdGVkIHNoZWV0cyBwcmVjZWRlbmNlIG92ZXIgc3R5bGVzIGluXG4gKiBzaGFkb3dSb290LlxuICovXG5jb25zdCBhZG9wdFN0eWxlcyA9IChyZW5kZXJSb290LCBzdHlsZXMpID0+IHtcbiAgICBpZiAoc3VwcG9ydHNBZG9wdGluZ1N0eWxlU2hlZXRzKSB7XG4gICAgICAgIHJlbmRlclJvb3QuYWRvcHRlZFN0eWxlU2hlZXRzID0gc3R5bGVzLm1hcCgocykgPT4gcyBpbnN0YW5jZW9mIENTU1N0eWxlU2hlZXQgPyBzIDogcy5zdHlsZVNoZWV0KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGZvciAoY29uc3QgcyBvZiBzdHlsZXMpIHtcbiAgICAgICAgICAgIGNvbnN0IHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnc3R5bGUnKTtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgICAgICAgICBjb25zdCBub25jZSA9IGdsb2JhbFsnbGl0Tm9uY2UnXTtcbiAgICAgICAgICAgIGlmIChub25jZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgc3R5bGUuc2V0QXR0cmlidXRlKCdub25jZScsIG5vbmNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN0eWxlLnRleHRDb250ZW50ID0gcy5jc3NUZXh0O1xuICAgICAgICAgICAgcmVuZGVyUm9vdC5hcHBlbmRDaGlsZChzdHlsZSk7XG4gICAgICAgIH1cbiAgICB9XG59O1xuY29uc3QgY3NzUmVzdWx0RnJvbVN0eWxlU2hlZXQgPSAoc2hlZXQpID0+IHtcbiAgICBsZXQgY3NzVGV4dCA9ICcnO1xuICAgIGZvciAoY29uc3QgcnVsZSBvZiBzaGVldC5jc3NSdWxlcykge1xuICAgICAgICBjc3NUZXh0ICs9IHJ1bGUuY3NzVGV4dDtcbiAgICB9XG4gICAgcmV0dXJuIHVuc2FmZUNTUyhjc3NUZXh0KTtcbn07XG5jb25zdCBnZXRDb21wYXRpYmxlU3R5bGUgPSBzdXBwb3J0c0Fkb3B0aW5nU3R5bGVTaGVldHMgfHxcbiAgICAoZ2xvYmFsLkNTU1N0eWxlU2hlZXQgPT09IHVuZGVmaW5lZClcbiAgICA/IChzKSA9PiBzXG4gICAgOiAocykgPT4gcyBpbnN0YW5jZW9mIENTU1N0eWxlU2hlZXQgPyBjc3NSZXN1bHRGcm9tU3R5bGVTaGVldChzKSA6IHM7XG5cbmV4cG9ydCB7IENTU1Jlc3VsdCwgYWRvcHRTdHlsZXMsIGNzcywgZ2V0Q29tcGF0aWJsZVN0eWxlLCBzdXBwb3J0c0Fkb3B0aW5nU3R5bGVTaGVldHMsIHVuc2FmZUNTUyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3NzLXRhZy5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/css-tag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/base.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   desc: () => (/* binding */ desc)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Wraps up a few best practices when returning a property descriptor from a\n * decorator.\n *\n * Marks the defined property as configurable, and enumerable, and handles\n * the case where we have a busted Reflect.decorate zombiefill (e.g. in Angular\n * apps).\n *\n * @internal\n */\nconst desc = (obj, name, descriptor) => {\n    // For backwards compatibility, we keep them configurable and enumerable.\n    descriptor.configurable = true;\n    descriptor.enumerable = true;\n    if (\n    // We check for Reflect.decorate each time, in case the zombiefill\n    // is applied via lazy loading some Angular code.\n    Reflect.decorate &&\n        typeof name !== 'object') {\n        // If we're called as a legacy decorator, and Reflect.decorate is present\n        // then we have no guarantees that the returned descriptor will be\n        // defined on the class, so we must apply it directly ourselves.\n        Object.defineProperty(obj, name, descriptor);\n    }\n    return descriptor;\n};\n\n\n//# sourceMappingURL=base.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/custom-element.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/custom-element.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customElement: () => (/* binding */ customElement)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nconst customElement = (tagName) => (classOrTarget, context) => {\n    if (context !== undefined) {\n        context.addInitializer(() => {\n            customElements.define(tagName, classOrTarget);\n        });\n    }\n    else {\n        customElements.define(tagName, classOrTarget);\n    }\n};\n\n\n//# sourceMappingURL=custom-element.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxpdC9yZWFjdGl2ZS1lbGVtZW50L25vZGUvZGV2ZWxvcG1lbnQvZGVjb3JhdG9ycy9jdXN0b20tZWxlbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV5QjtBQUN6QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcQGxpdFxccmVhY3RpdmUtZWxlbWVudFxcbm9kZVxcZGV2ZWxvcG1lbnRcXGRlY29yYXRvcnNcXGN1c3RvbS1lbGVtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDE3IEdvb2dsZSBMTENcbiAqIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBCU0QtMy1DbGF1c2VcbiAqL1xuLyoqXG4gKiBDbGFzcyBkZWNvcmF0b3IgZmFjdG9yeSB0aGF0IGRlZmluZXMgdGhlIGRlY29yYXRlZCBjbGFzcyBhcyBhIGN1c3RvbSBlbGVtZW50LlxuICpcbiAqIGBgYGpzXG4gKiBAY3VzdG9tRWxlbWVudCgnbXktZWxlbWVudCcpXG4gKiBjbGFzcyBNeUVsZW1lbnQgZXh0ZW5kcyBMaXRFbGVtZW50IHtcbiAqICAgcmVuZGVyKCkge1xuICogICAgIHJldHVybiBodG1sYGA7XG4gKiAgIH1cbiAqIH1cbiAqIGBgYFxuICogQGNhdGVnb3J5IERlY29yYXRvclxuICogQHBhcmFtIHRhZ05hbWUgVGhlIHRhZyBuYW1lIG9mIHRoZSBjdXN0b20gZWxlbWVudCB0byBkZWZpbmUuXG4gKi9cbmNvbnN0IGN1c3RvbUVsZW1lbnQgPSAodGFnTmFtZSkgPT4gKGNsYXNzT3JUYXJnZXQsIGNvbnRleHQpID0+IHtcbiAgICBpZiAoY29udGV4dCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnRleHQuYWRkSW5pdGlhbGl6ZXIoKCkgPT4ge1xuICAgICAgICAgICAgY3VzdG9tRWxlbWVudHMuZGVmaW5lKHRhZ05hbWUsIGNsYXNzT3JUYXJnZXQpO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGN1c3RvbUVsZW1lbnRzLmRlZmluZSh0YWdOYW1lLCBjbGFzc09yVGFyZ2V0KTtcbiAgICB9XG59O1xuXG5leHBvcnQgeyBjdXN0b21FbGVtZW50IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jdXN0b20tZWxlbWVudC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/custom-element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/event-options.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/event-options.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventOptions: () => (/* binding */ eventOptions)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction eventOptions(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return ((protoOrValue, nameOrContext) => {\n        const method = typeof protoOrValue === 'function'\n            ? protoOrValue\n            : protoOrValue[nameOrContext];\n        Object.assign(method, options);\n    });\n}\n\n\n//# sourceMappingURL=event-options.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/event-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/property.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/property.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   property: () => (/* binding */ property),\n/* harmony export */   standardProperty: () => (/* binding */ standardProperty)\n/* harmony export */ });\n/* harmony import */ var _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../reactive-element.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/reactive-element.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nlet issueWarning;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    globalThis.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!globalThis.litIssuedWarnings.has(warning) &&\n            !globalThis.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            globalThis.litIssuedWarnings.add(warning);\n        }\n    };\n}\nconst legacyProperty = (options, proto, name) => {\n    const hasOwnProperty = proto.hasOwnProperty(name);\n    proto.constructor.createProperty(name, options);\n    // For accessors (which have a descriptor on the prototype) we need to\n    // return a descriptor, otherwise TypeScript overwrites the descriptor we\n    // define in createProperty() with the original descriptor. We don't do this\n    // for fields, which don't have a descriptor, because this could overwrite\n    // descriptor defined by other decorators.\n    return hasOwnProperty\n        ? Object.getOwnPropertyDescriptor(proto, name)\n        : undefined;\n};\n// This is duplicated from a similar variable in reactive-element.ts, but\n// actually makes sense to have this default defined with the decorator, so\n// that different decorators could have different defaults.\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__.defaultConverter,\n    reflect: false,\n    hasChanged: _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__.notEqual,\n};\n/**\n * Wraps a class accessor or setter so that `requestUpdate()` is called with the\n * property name and old value when the accessor is set.\n */\nconst standardProperty = (options = defaultPropertyDeclaration, target, context) => {\n    const { kind, metadata } = context;\n    if (metadata == null) {\n        issueWarning('missing-class-metadata', `The class ${target} is missing decorator metadata. This ` +\n            `could mean that you're using a compiler that supports decorators ` +\n            `but doesn't support decorator metadata, such as TypeScript 5.1. ` +\n            `Please update your compiler.`);\n    }\n    // Store the property options\n    let properties = globalThis.litPropertyMetadata.get(metadata);\n    if (properties === undefined) {\n        globalThis.litPropertyMetadata.set(metadata, (properties = new Map()));\n    }\n    if (kind === 'setter') {\n        options = Object.create(options);\n        options.wrapped = true;\n    }\n    properties.set(context.name, options);\n    if (kind === 'accessor') {\n        // Standard decorators cannot dynamically modify the class, so we can't\n        // replace a field with accessors. The user must use the new `accessor`\n        // keyword instead.\n        const { name } = context;\n        return {\n            set(v) {\n                const oldValue = target.get.call(this);\n                target.set.call(this, v);\n                this.requestUpdate(name, oldValue, options);\n            },\n            init(v) {\n                if (v !== undefined) {\n                    this._$changeProperty(name, undefined, options, v);\n                }\n                return v;\n            },\n        };\n    }\n    else if (kind === 'setter') {\n        const { name } = context;\n        return function (value) {\n            const oldValue = this[name];\n            target.call(this, value);\n            this.requestUpdate(name, oldValue, options);\n        };\n    }\n    throw new Error(`Unsupported decorator location: ${kind}`);\n};\n/**\n * A class field or accessor decorator which creates a reactive property that\n * reflects a corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nfunction property(options) {\n    return (protoOrTarget, nameOrContext\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    ) => {\n        return (typeof nameOrContext === 'object'\n            ? standardProperty(options, protoOrTarget, nameOrContext)\n            : legacyProperty(options, protoOrTarget, nameOrContext));\n    };\n}\n\n\n//# sourceMappingURL=property.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/property.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-all.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/query-all.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAll: () => (/* binding */ queryAll)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Shared fragment used to generate empty NodeLists when a render root is\n// undefined\nlet fragment;\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction queryAll(selector) {\n    return ((obj, name) => {\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const container = this.renderRoot ?? (fragment ??= document.createDocumentFragment());\n                return container.querySelectorAll(selector);\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-all.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-all.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedElements: () => (/* binding */ queryAssignedElements)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nfunction queryAssignedElements(options) {\n    return ((obj, name) => {\n        const { slot, selector } = options ?? {};\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const slotEl = this.renderRoot?.querySelector(slotSelector);\n                const elements = slotEl?.assignedElements(options) ?? [];\n                return (selector === undefined\n                    ? elements\n                    : elements.filter((node) => node.matches(selector)));\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-assigned-elements.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedNodes: () => (/* binding */ queryAssignedNodes)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given `slot`.\n *\n * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes({slot: 'list', flatten: true})\n *   listItems!: Array<Node>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>`. Use the\n * queryAssignedElements decorator to list only elements, and optionally filter\n * the element list using a CSS selector.\n *\n * @category Decorator\n */\nfunction queryAssignedNodes(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return ((obj, name) => {\n        const { slot } = options ?? {};\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const slotEl = this.renderRoot?.querySelector(slotSelector);\n                return (slotEl?.assignedNodes(options) ?? []);\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-assigned-nodes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-async.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/query-async.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAsync: () => (/* binding */ queryAsync)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nfunction queryAsync(selector) {\n    return ((obj, name) => {\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            async get() {\n                await this.updateComplete;\n                return this.renderRoot?.querySelector(selector) ?? null;\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-async.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/query.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nlet issueWarning;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    globalThis.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += code\n            ? ` See https://lit.dev/msg/${code} for more information.`\n            : '';\n        if (!globalThis.litIssuedWarnings.has(warning) &&\n            !globalThis.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            globalThis.litIssuedWarnings.add(warning);\n        }\n    };\n}\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction query(selector, cache) {\n    return ((protoOrTarget, nameOrContext, descriptor) => {\n        const doQuery = (el) => {\n            const result = (el.renderRoot?.querySelector(selector) ?? null);\n            if (result === null && cache && !el.hasUpdated) {\n                const name = typeof nameOrContext === 'object'\n                    ? nameOrContext.name\n                    : nameOrContext;\n                issueWarning('', `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n                    `flag set for selector '${selector}' has been accessed before ` +\n                    `the first update and returned null. This is expected if the ` +\n                    `renderRoot tree has not been provided beforehand (e.g. via ` +\n                    `Declarative Shadow DOM). Therefore the value hasn't been cached.`);\n            }\n            // TODO: if we want to allow users to assert that the query will never\n            // return null, we need a new option and to throw here if the result\n            // is null.\n            return result;\n        };\n        if (cache) {\n            // Accessors to wrap from either:\n            //   1. The decorator target, in the case of standard decorators\n            //   2. The property descriptor, in the case of experimental decorators\n            //      on auto-accessors.\n            //   3. Functions that access our own cache-key property on the instance,\n            //      in the case of experimental decorators on fields.\n            const { get, set } = typeof nameOrContext === 'object'\n                ? protoOrTarget\n                : descriptor ??\n                    (() => {\n                        const key = Symbol(`${String(nameOrContext)} (@query() cache)`)\n                            ;\n                        return {\n                            get() {\n                                return this[key];\n                            },\n                            set(v) {\n                                this[key] = v;\n                            },\n                        };\n                    })();\n            return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(protoOrTarget, nameOrContext, {\n                get() {\n                    let result = get.call(this);\n                    if (result === undefined) {\n                        result = doQuery(this);\n                        if (result !== null || this.hasUpdated) {\n                            set.call(this, result);\n                        }\n                    }\n                    return result;\n                },\n            });\n        }\n        else {\n            // This object works as the return type for both standard and\n            // experimental decorators.\n            return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(protoOrTarget, nameOrContext, {\n                get() {\n                    return doQuery(this);\n                },\n            });\n        }\n    });\n}\n\n\n//# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/state.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/decorators/state.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   state: () => (/* binding */ state)\n/* harmony export */ });\n/* harmony import */ var _property_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./property.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/property.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nfunction state(options) {\n    return (0,_property_js__WEBPACK_IMPORTED_MODULE_0__.property)({\n        ...options,\n        // Add both `state` and `attribute` because we found a third party\n        // controller that is keying off of PropertyOptions.state to determine\n        // whether a field is a private internal property or not.\n        state: true,\n        attribute: false,\n    });\n}\n\n\n//# sourceMappingURL=state.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/decorators/state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit/reactive-element/node/development/reactive-element.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@lit/reactive-element/node/development/reactive-element.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.CSSResult),\n/* harmony export */   ReactiveElement: () => (/* binding */ ReactiveElement),\n/* harmony export */   adoptStyles: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.adoptStyles),\n/* harmony export */   css: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.css),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter),\n/* harmony export */   getCompatibleStyle: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle),\n/* harmony export */   notEqual: () => (/* binding */ notEqual),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.unsafeCSS)\n/* harmony export */ });\n/* harmony import */ var _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lit-labs/ssr-dom-shim */ \"(ssr)/./node_modules/@lit-labs/ssr-dom-shim/index.js\");\n/* harmony import */ var _css_tag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./css-tag.js */ \"(ssr)/./node_modules/@lit/reactive-element/node/development/css-tag.js\");\n\n\n\n\n// TODO (justinfagnani): Add `hasOwn` here when we ship ES2022\nconst { is, defineProperty, getOwnPropertyDescriptor, getOwnPropertyNames, getOwnPropertySymbols, getPrototypeOf, } = Object;\n// Lets a minifier replace globalThis references with a minified name\nconst global = globalThis;\n{\n    global.customElements ??= _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__.customElements;\n}\nlet issueWarning;\nconst trustedTypes = global\n    .trustedTypes;\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nconst polyfillSupport = global.reactiveElementPolyfillSupportDevMode\n    ;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    global.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!global.litIssuedWarnings.has(warning) &&\n            !global.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            global.litIssuedWarnings.add(warning);\n        }\n    };\n    queueMicrotask(() => {\n        issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n        // Issue polyfill support warning.\n        if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n            issueWarning('polyfill-support-missing', `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n                `the \\`polyfill-support\\` module has not been loaded.`);\n        }\n    });\n}\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    ;\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = (prop, _obj) => prop;\nconst defaultConverter = {\n    toAttribute(value, type) {\n        switch (type) {\n            case Boolean:\n                value = value ? emptyStringForBooleanAttribute : null;\n                break;\n            case Object:\n            case Array:\n                // if the value is `null` or `undefined` pass this through\n                // to allow removing/no change behavior.\n                value = value == null ? value : JSON.stringify(value);\n                break;\n        }\n        return value;\n    },\n    fromAttribute(value, type) {\n        let fromValue = value;\n        switch (type) {\n            case Boolean:\n                fromValue = value !== null;\n                break;\n            case Number:\n                fromValue = value === null ? null : Number(value);\n                break;\n            case Object:\n            case Array:\n                // Do *not* generate exception when invalid JSON is set as elements\n                // don't normally complain on being mis-configured.\n                // TODO(sorvell): Do generate exception in *dev mode*.\n                try {\n                    // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n                    fromValue = JSON.parse(value);\n                }\n                catch (e) {\n                    fromValue = null;\n                }\n                break;\n        }\n        return fromValue;\n    },\n};\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nconst notEqual = (value, old) => !is(value, old);\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: defaultConverter,\n    reflect: false,\n    useDefault: false,\n    hasChanged: notEqual,\n};\n// Ensure metadata is enabled. TypeScript does not polyfill\n// Symbol.metadata, so we must ensure that it exists.\nSymbol.metadata ??= Symbol('metadata');\n// Map from a class's metadata object to property options\n// Note that we must use nullish-coalescing assignment so that we only use one\n// map even if we load multiple version of this module.\nglobal.litPropertyMetadata ??= new WeakMap();\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclasses to render updates as desired.\n * @noInheritDoc\n */\nclass ReactiveElement\n// In the Node build, this `extends` clause will be substituted with\n// `(globalThis.HTMLElement ?? HTMLElement)`.\n//\n// This way, we will first prefer any global `HTMLElement` polyfill that the\n// user has assigned, and then fall back to the `HTMLElement` shim which has\n// been imported (see note at the top of this file about how this import is\n// generated by Rollup). Note that the `HTMLElement` variable has been\n// shadowed by this import, so it no longer refers to the global.\n extends (globalThis.HTMLElement ?? _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__.HTMLElement) {\n    /**\n     * Adds an initializer function to the class that is called during instance\n     * construction.\n     *\n     * This is useful for code that runs against a `ReactiveElement`\n     * subclass, such as a decorator, that needs to do work for each\n     * instance, such as setting up a `ReactiveController`.\n     *\n     * ```ts\n     * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n     *   target.addInitializer((instance: ReactiveElement) => {\n     *     // This is run during construction of the element\n     *     new MyController(instance);\n     *   });\n     * }\n     * ```\n     *\n     * Decorating a field will then cause each instance to run an initializer\n     * that adds a controller:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   @myDecorator foo;\n     * }\n     * ```\n     *\n     * Initializers are stored per-constructor. Adding an initializer to a\n     * subclass does not add it to a superclass. Since initializers are run in\n     * constructors, initializers will run in order of the class hierarchy,\n     * starting with superclasses and progressing to the instance's class.\n     *\n     * @nocollapse\n     */\n    static addInitializer(initializer) {\n        this.__prepare();\n        (this._initializers ??= []).push(initializer);\n    }\n    /**\n     * Returns a list of attributes corresponding to the registered properties.\n     * @nocollapse\n     * @category attributes\n     */\n    static get observedAttributes() {\n        // Ensure we've created all properties\n        this.finalize();\n        // this.__attributeToPropertyMap is only undefined after finalize() in\n        // ReactiveElement itself. ReactiveElement.observedAttributes is only\n        // accessed with ReactiveElement as the receiver when a subclass or mixin\n        // calls super.observedAttributes\n        return (this.__attributeToPropertyMap && [...this.__attributeToPropertyMap.keys()]);\n    }\n    /**\n     * Creates a property accessor on the element prototype if one does not exist\n     * and stores a {@linkcode PropertyDeclaration} for the property with the\n     * given options. The property setter calls the property's `hasChanged`\n     * property option or uses a strict identity check to determine whether or not\n     * to request an update.\n     *\n     * This method may be overridden to customize properties; however,\n     * when doing so, it's important to call `super.createProperty` to ensure\n     * the property is setup correctly. This method calls\n     * `getPropertyDescriptor` internally to get a descriptor to install.\n     * To customize what properties do when they are get or set, override\n     * `getPropertyDescriptor`. To customize the options for a property,\n     * implement `createProperty` like this:\n     *\n     * ```ts\n     * static createProperty(name, options) {\n     *   options = Object.assign(options, {myOption: true});\n     *   super.createProperty(name, options);\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static createProperty(name, options = defaultPropertyDeclaration) {\n        // If this is a state property, force the attribute to false.\n        if (options.state) {\n            options.attribute = false;\n        }\n        this.__prepare();\n        // Whether this property is wrapping accessors.\n        // Helps control the initial value change and reflection logic.\n        if (this.prototype.hasOwnProperty(name)) {\n            options = Object.create(options);\n            options.wrapped = true;\n        }\n        this.elementProperties.set(name, options);\n        if (!options.noAccessor) {\n            const key = // Use Symbol.for in dev mode to make it easier to maintain state\n                    // when doing HMR.\n                    Symbol.for(`${String(name)} (@property() cache)`)\n                ;\n            const descriptor = this.getPropertyDescriptor(name, key, options);\n            if (descriptor !== undefined) {\n                defineProperty(this.prototype, name, descriptor);\n            }\n        }\n    }\n    /**\n     * Returns a property descriptor to be defined on the given named property.\n     * If no descriptor is returned, the property will not become an accessor.\n     * For example,\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   static getPropertyDescriptor(name, key, options) {\n     *     const defaultDescriptor =\n     *         super.getPropertyDescriptor(name, key, options);\n     *     const setter = defaultDescriptor.set;\n     *     return {\n     *       get: defaultDescriptor.get,\n     *       set(value) {\n     *         setter.call(this, value);\n     *         // custom action.\n     *       },\n     *       configurable: true,\n     *       enumerable: true\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static getPropertyDescriptor(name, key, options) {\n        const { get, set } = getOwnPropertyDescriptor(this.prototype, name) ?? {\n            get() {\n                return this[key];\n            },\n            set(v) {\n                this[key] = v;\n            },\n        };\n        if (get == null) {\n            if ('value' in (getOwnPropertyDescriptor(this.prototype, name) ?? {})) {\n                throw new Error(`Field ${JSON.stringify(String(name))} on ` +\n                    `${this.name} was declared as a reactive property ` +\n                    `but it's actually declared as a value on the prototype. ` +\n                    `Usually this is due to using @property or @state on a method.`);\n            }\n            issueWarning('reactive-property-without-getter', `Field ${JSON.stringify(String(name))} on ` +\n                `${this.name} was declared as a reactive property ` +\n                `but it does not have a getter. This will be an error in a ` +\n                `future version of Lit.`);\n        }\n        return {\n            get,\n            set(value) {\n                const oldValue = get?.call(this);\n                set?.call(this, value);\n                this.requestUpdate(name, oldValue, options);\n            },\n            configurable: true,\n            enumerable: true,\n        };\n    }\n    /**\n     * Returns the property options associated with the given property.\n     * These options are defined with a `PropertyDeclaration` via the `properties`\n     * object or the `@property` decorator and are registered in\n     * `createProperty(...)`.\n     *\n     * Note, this method should be considered \"final\" and not overridden. To\n     * customize the options for a given property, override\n     * {@linkcode createProperty}.\n     *\n     * @nocollapse\n     * @final\n     * @category properties\n     */\n    static getPropertyOptions(name) {\n        return this.elementProperties.get(name) ?? defaultPropertyDeclaration;\n    }\n    /**\n     * Initializes static own properties of the class used in bookkeeping\n     * for element properties, initializers, etc.\n     *\n     * Can be called multiple times by code that needs to ensure these\n     * properties exist before using them.\n     *\n     * This method ensures the superclass is finalized so that inherited\n     * property metadata can be copied down.\n     * @nocollapse\n     */\n    static __prepare() {\n        if (this.hasOwnProperty(JSCompiler_renameProperty('elementProperties'))) {\n            // Already prepared\n            return;\n        }\n        // Finalize any superclasses\n        const superCtor = getPrototypeOf(this);\n        superCtor.finalize();\n        // Create own set of initializers for this class if any exist on the\n        // superclass and copy them down. Note, for a small perf boost, avoid\n        // creating initializers unless needed.\n        if (superCtor._initializers !== undefined) {\n            this._initializers = [...superCtor._initializers];\n        }\n        // Initialize elementProperties from the superclass\n        this.elementProperties = new Map(superCtor.elementProperties);\n    }\n    /**\n     * Finishes setting up the class so that it's ready to be registered\n     * as a custom element and instantiated.\n     *\n     * This method is called by the ReactiveElement.observedAttributes getter.\n     * If you override the observedAttributes getter, you must either call\n     * super.observedAttributes to trigger finalization, or call finalize()\n     * yourself.\n     *\n     * @nocollapse\n     */\n    static finalize() {\n        if (this.hasOwnProperty(JSCompiler_renameProperty('finalized'))) {\n            return;\n        }\n        this.finalized = true;\n        this.__prepare();\n        // Create properties from the static properties block:\n        if (this.hasOwnProperty(JSCompiler_renameProperty('properties'))) {\n            const props = this.properties;\n            const propKeys = [\n                ...getOwnPropertyNames(props),\n                ...getOwnPropertySymbols(props),\n            ];\n            for (const p of propKeys) {\n                this.createProperty(p, props[p]);\n            }\n        }\n        // Create properties from standard decorator metadata:\n        const metadata = this[Symbol.metadata];\n        if (metadata !== null) {\n            const properties = litPropertyMetadata.get(metadata);\n            if (properties !== undefined) {\n                for (const [p, options] of properties) {\n                    this.elementProperties.set(p, options);\n                }\n            }\n        }\n        // Create the attribute-to-property map\n        this.__attributeToPropertyMap = new Map();\n        for (const [p, options] of this.elementProperties) {\n            const attr = this.__attributeNameForProperty(p, options);\n            if (attr !== undefined) {\n                this.__attributeToPropertyMap.set(attr, p);\n            }\n        }\n        this.elementStyles = this.finalizeStyles(this.styles);\n        {\n            if (this.hasOwnProperty('createProperty')) {\n                issueWarning('no-override-create-property', 'Overriding ReactiveElement.createProperty() is deprecated. ' +\n                    'The override will not be called with standard decorators');\n            }\n            if (this.hasOwnProperty('getPropertyDescriptor')) {\n                issueWarning('no-override-get-property-descriptor', 'Overriding ReactiveElement.getPropertyDescriptor() is deprecated. ' +\n                    'The override will not be called with standard decorators');\n            }\n        }\n    }\n    /**\n     * Takes the styles the user supplied via the `static styles` property and\n     * returns the array of styles to apply to the element.\n     * Override this method to integrate into a style management system.\n     *\n     * Styles are deduplicated preserving the _last_ instance in the list. This\n     * is a performance optimization to avoid duplicated styles that can occur\n     * especially when composing via subclassing. The last item is kept to try\n     * to preserve the cascade order with the assumption that it's most important\n     * that last added styles override previous styles.\n     *\n     * @nocollapse\n     * @category styles\n     */\n    static finalizeStyles(styles) {\n        const elementStyles = [];\n        if (Array.isArray(styles)) {\n            // Dedupe the flattened array in reverse order to preserve the last items.\n            // Casting to Array<unknown> works around TS error that\n            // appears to come from trying to flatten a type CSSResultArray.\n            const set = new Set(styles.flat(Infinity).reverse());\n            // Then preserve original order by adding the set items in reverse order.\n            for (const s of set) {\n                elementStyles.unshift((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle)(s));\n            }\n        }\n        else if (styles !== undefined) {\n            elementStyles.push((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle)(styles));\n        }\n        return elementStyles;\n    }\n    /**\n     * Returns the property name for the given attribute `name`.\n     * @nocollapse\n     */\n    static __attributeNameForProperty(name, options) {\n        const attribute = options.attribute;\n        return attribute === false\n            ? undefined\n            : typeof attribute === 'string'\n                ? attribute\n                : typeof name === 'string'\n                    ? name.toLowerCase()\n                    : undefined;\n    }\n    constructor() {\n        super();\n        this.__instanceProperties = undefined;\n        /**\n         * True if there is a pending update as a result of calling `requestUpdate()`.\n         * Should only be read.\n         * @category updates\n         */\n        this.isUpdatePending = false;\n        /**\n         * Is set to `true` after the first update. The element code cannot assume\n         * that `renderRoot` exists before the element `hasUpdated`.\n         * @category updates\n         */\n        this.hasUpdated = false;\n        /**\n         * Name of currently reflecting property\n         */\n        this.__reflectingProperty = null;\n        this.__initialize();\n    }\n    /**\n     * Internal only override point for customizing work done when elements\n     * are constructed.\n     */\n    __initialize() {\n        this.__updatePromise = new Promise((res) => (this.enableUpdating = res));\n        this._$changedProperties = new Map();\n        // This enqueues a microtask that must run before the first update, so it\n        // must be called before requestUpdate()\n        this.__saveInstanceProperties();\n        // ensures first update will be caught by an early access of\n        // `updateComplete`\n        this.requestUpdate();\n        this.constructor._initializers?.forEach((i) => i(this));\n    }\n    /**\n     * Registers a `ReactiveController` to participate in the element's reactive\n     * update cycle. The element automatically calls into any registered\n     * controllers during its lifecycle callbacks.\n     *\n     * If the element is connected when `addController()` is called, the\n     * controller's `hostConnected()` callback will be immediately called.\n     * @category controllers\n     */\n    addController(controller) {\n        (this.__controllers ??= new Set()).add(controller);\n        // If a controller is added after the element has been connected,\n        // call hostConnected. Note, re-using existence of `renderRoot` here\n        // (which is set in connectedCallback) to avoid the need to track a\n        // first connected state.\n        if (this.renderRoot !== undefined && this.isConnected) {\n            controller.hostConnected?.();\n        }\n    }\n    /**\n     * Removes a `ReactiveController` from the element.\n     * @category controllers\n     */\n    removeController(controller) {\n        this.__controllers?.delete(controller);\n    }\n    /**\n     * Fixes any properties set on the instance before upgrade time.\n     * Otherwise these would shadow the accessor and break these properties.\n     * The properties are stored in a Map which is played back after the\n     * constructor runs.\n     */\n    __saveInstanceProperties() {\n        const instanceProperties = new Map();\n        const elementProperties = this.constructor\n            .elementProperties;\n        for (const p of elementProperties.keys()) {\n            if (this.hasOwnProperty(p)) {\n                instanceProperties.set(p, this[p]);\n                delete this[p];\n            }\n        }\n        if (instanceProperties.size > 0) {\n            this.__instanceProperties = instanceProperties;\n        }\n    }\n    /**\n     * Returns the node into which the element should render and by default\n     * creates and returns an open shadowRoot. Implement to customize where the\n     * element's DOM is rendered. For example, to render into the element's\n     * childNodes, return `this`.\n     *\n     * @return Returns a node into which to render.\n     * @category rendering\n     */\n    createRenderRoot() {\n        const renderRoot = this.shadowRoot ??\n            this.attachShadow(this.constructor.shadowRootOptions);\n        (0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.adoptStyles)(renderRoot, this.constructor.elementStyles);\n        return renderRoot;\n    }\n    /**\n     * On first connection, creates the element's renderRoot, sets up\n     * element styling, and enables updating.\n     * @category lifecycle\n     */\n    connectedCallback() {\n        // Create renderRoot before controllers `hostConnected`\n        this.renderRoot ??=\n            this.createRenderRoot();\n        this.enableUpdating(true);\n        this.__controllers?.forEach((c) => c.hostConnected?.());\n    }\n    /**\n     * Note, this method should be considered final and not overridden. It is\n     * overridden on the element instance with a function that triggers the first\n     * update.\n     * @category updates\n     */\n    enableUpdating(_requestedUpdate) { }\n    /**\n     * Allows for `super.disconnectedCallback()` in extensions while\n     * reserving the possibility of making non-breaking feature additions\n     * when disconnecting at some point in the future.\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        this.__controllers?.forEach((c) => c.hostDisconnected?.());\n    }\n    /**\n     * Synchronizes property values when attributes change.\n     *\n     * Specifically, when an attribute is set, the corresponding property is set.\n     * You should rarely need to implement this callback. If this method is\n     * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n     * called.\n     *\n     * See [responding to attribute changes](https://developer.mozilla.org/en-US/docs/Web/API/Web_components/Using_custom_elements#responding_to_attribute_changes)\n     * on MDN for more information about the `attributeChangedCallback`.\n     * @category attributes\n     */\n    attributeChangedCallback(name, _old, value) {\n        this._$attributeToProperty(name, value);\n    }\n    __propertyToAttribute(name, value) {\n        const elemProperties = this.constructor.elementProperties;\n        const options = elemProperties.get(name);\n        const attr = this.constructor.__attributeNameForProperty(name, options);\n        if (attr !== undefined && options.reflect === true) {\n            const converter = options.converter?.toAttribute !==\n                undefined\n                ? options.converter\n                : defaultConverter;\n            const attrValue = converter.toAttribute(value, options.type);\n            if (this.constructor.enabledWarnings.includes('migration') &&\n                attrValue === undefined) {\n                issueWarning('undefined-attribute-value', `The attribute value for the ${name} property is ` +\n                    `undefined on element ${this.localName}. The attribute will be ` +\n                    `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n                    `the attribute would not have changed.`);\n            }\n            // Track if the property is being reflected to avoid\n            // setting the property again via `attributeChangedCallback`. Note:\n            // 1. this takes advantage of the fact that the callback is synchronous.\n            // 2. will behave incorrectly if multiple attributes are in the reaction\n            // stack at time of calling. However, since we process attributes\n            // in `update` this should not be possible (or an extreme corner case\n            // that we'd like to discover).\n            // mark state reflecting\n            this.__reflectingProperty = name;\n            if (attrValue == null) {\n                this.removeAttribute(attr);\n            }\n            else {\n                this.setAttribute(attr, attrValue);\n            }\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /** @internal */\n    _$attributeToProperty(name, value) {\n        const ctor = this.constructor;\n        // Note, hint this as an `AttributeMap` so closure clearly understands\n        // the type; it has issues with tracking types through statics\n        const propName = ctor.__attributeToPropertyMap.get(name);\n        // Use tracking info to avoid reflecting a property value to an attribute\n        // if it was just set because the attribute changed.\n        if (propName !== undefined && this.__reflectingProperty !== propName) {\n            const options = ctor.getPropertyOptions(propName);\n            const converter = typeof options.converter === 'function'\n                ? { fromAttribute: options.converter }\n                : options.converter?.fromAttribute !== undefined\n                    ? options.converter\n                    : defaultConverter;\n            // mark state reflecting\n            this.__reflectingProperty = propName;\n            this[propName] =\n                converter.fromAttribute(value, options.type) ??\n                    this.__defaultValues?.get(propName) ??\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    null;\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /**\n     * Requests an update which is processed asynchronously. This should be called\n     * when an element should update based on some state not triggered by setting\n     * a reactive property. In this case, pass no arguments. It should also be\n     * called when manually implementing a property setter. In this case, pass the\n     * property `name` and `oldValue` to ensure that any configured property\n     * options are honored.\n     *\n     * @param name name of requesting property\n     * @param oldValue old value of requesting property\n     * @param options property options to use instead of the previously\n     *     configured options\n     * @category updates\n     */\n    requestUpdate(name, oldValue, options) {\n        // If we have a property key, perform property update steps.\n        if (name !== undefined) {\n            if (name instanceof Event) {\n                issueWarning(``, `The requestUpdate() method was called with an Event as the property name. This is probably a mistake caused by binding this.requestUpdate as an event listener. Instead bind a function that will call it with no arguments: () => this.requestUpdate()`);\n            }\n            const ctor = this.constructor;\n            const newValue = this[name];\n            options ??= ctor.getPropertyOptions(name);\n            const changed = (options.hasChanged ?? notEqual)(newValue, oldValue) ||\n                // When there is no change, check a corner case that can occur when\n                // 1. there's a initial value which was not reflected\n                // 2. the property is subsequently set to this value.\n                // For example, `prop: {useDefault: true, reflect: true}`\n                // and el.prop = 'foo'. This should be considered a change if the\n                // attribute is not set because we will now reflect the property to the attribute.\n                (options.useDefault &&\n                    options.reflect &&\n                    newValue === this.__defaultValues?.get(name) &&\n                    !this.hasAttribute(ctor.__attributeNameForProperty(name, options)));\n            if (changed) {\n                this._$changeProperty(name, oldValue, options);\n            }\n            else {\n                // Abort the request if the property should not be considered changed.\n                return;\n            }\n        }\n        if (this.isUpdatePending === false) {\n            this.__updatePromise = this.__enqueueUpdate();\n        }\n    }\n    /**\n     * @internal\n     */\n    _$changeProperty(name, oldValue, { useDefault, reflect, wrapped }, initializeValue) {\n        // Record default value when useDefault is used. This allows us to\n        // restore this value when the attribute is removed.\n        if (useDefault && !(this.__defaultValues ??= new Map()).has(name)) {\n            this.__defaultValues.set(name, initializeValue ?? oldValue ?? this[name]);\n            // if this is not wrapping an accessor, it must be an initial setting\n            // and in this case we do not want to record the change or reflect.\n            if (wrapped !== true || initializeValue !== undefined) {\n                return;\n            }\n        }\n        // TODO (justinfagnani): Create a benchmark of Map.has() + Map.set(\n        // vs just Map.set()\n        if (!this._$changedProperties.has(name)) {\n            // On the initial change, the old value should be `undefined`, except\n            // with `useDefault`\n            if (!this.hasUpdated && !useDefault) {\n                oldValue = undefined;\n            }\n            this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `__reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (reflect === true && this.__reflectingProperty !== name) {\n            (this.__reflectingProperties ??= new Set()).add(name);\n        }\n    }\n    /**\n     * Sets up the element to asynchronously update.\n     */\n    async __enqueueUpdate() {\n        this.isUpdatePending = true;\n        try {\n            // Ensure any previous update has resolved before updating.\n            // This `await` also ensures that property changes are batched.\n            await this.__updatePromise;\n        }\n        catch (e) {\n            // Refire any previous errors async so they do not disrupt the update\n            // cycle. Errors are refired so developers have a chance to observe\n            // them, and this can be done by implementing\n            // `window.onunhandledrejection`.\n            Promise.reject(e);\n        }\n        const result = this.scheduleUpdate();\n        // If `scheduleUpdate` returns a Promise, we await it. This is done to\n        // enable coordinating updates with a scheduler. Note, the result is\n        // checked to avoid delaying an additional microtask unless we need to.\n        if (result != null) {\n            await result;\n        }\n        return !this.isUpdatePending;\n    }\n    /**\n     * Schedules an element update. You can override this method to change the\n     * timing of updates by returning a Promise. The update will await the\n     * returned Promise, and you should resolve the Promise to allow the update\n     * to proceed. If this method is overridden, `super.scheduleUpdate()`\n     * must be called.\n     *\n     * For instance, to schedule updates to occur just before the next frame:\n     *\n     * ```ts\n     * override protected async scheduleUpdate(): Promise<unknown> {\n     *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n     *   super.scheduleUpdate();\n     * }\n     * ```\n     * @category updates\n     */\n    scheduleUpdate() {\n        const result = this.performUpdate();\n        if (this.constructor.enabledWarnings.includes('async-perform-update') &&\n            typeof result?.then ===\n                'function') {\n            issueWarning('async-perform-update', `Element ${this.localName} returned a Promise from performUpdate(). ` +\n                `This behavior is deprecated and will be removed in a future ` +\n                `version of ReactiveElement.`);\n        }\n        return result;\n    }\n    /**\n     * Performs an element update. Note, if an exception is thrown during the\n     * update, `firstUpdated` and `updated` will not be called.\n     *\n     * Call `performUpdate()` to immediately process a pending update. This should\n     * generally not be needed, but it can be done in rare cases when you need to\n     * update synchronously.\n     *\n     * @category updates\n     */\n    performUpdate() {\n        // Abort any update if one is not pending when this is called.\n        // This can happen if `performUpdate` is called early to \"flush\"\n        // the update.\n        if (!this.isUpdatePending) {\n            return;\n        }\n        debugLogEvent?.({ kind: 'update' });\n        if (!this.hasUpdated) {\n            // Create renderRoot before first update. This occurs in `connectedCallback`\n            // but is done here to support out of tree calls to `enableUpdating`/`performUpdate`.\n            this.renderRoot ??=\n                this.createRenderRoot();\n            {\n                // Produce warning if any reactive properties on the prototype are\n                // shadowed by class fields. Instance fields set before upgrade are\n                // deleted by this point, so any own property is caused by class field\n                // initialization in the constructor.\n                const ctor = this.constructor;\n                const shadowedProperties = [...ctor.elementProperties.keys()].filter((p) => this.hasOwnProperty(p) && p in getPrototypeOf(this));\n                if (shadowedProperties.length) {\n                    throw new Error(`The following properties on element ${this.localName} will not ` +\n                        `trigger updates as expected because they are set using class ` +\n                        `fields: ${shadowedProperties.join(', ')}. ` +\n                        `Native class fields and some compiled output will overwrite ` +\n                        `accessors used for detecting changes. See ` +\n                        `https://lit.dev/msg/class-field-shadowing ` +\n                        `for more information.`);\n                }\n            }\n            // Mixin instance properties once, if they exist.\n            if (this.__instanceProperties) {\n                // TODO (justinfagnani): should we use the stored value? Could a new value\n                // have been set since we stored the own property value?\n                for (const [p, value] of this.__instanceProperties) {\n                    this[p] = value;\n                }\n                this.__instanceProperties = undefined;\n            }\n            // Trigger initial value reflection and populate the initial\n            // `changedProperties` map, but only for the case of properties created\n            // via `createProperty` on accessors, which will not have already\n            // populated the `changedProperties` map since they are not set.\n            // We can't know if these accessors had initializers, so we just set\n            // them anyway - a difference from experimental decorators on fields and\n            // standard decorators on auto-accessors.\n            // For context see:\n            // https://github.com/lit/lit/pull/4183#issuecomment-1711959635\n            const elementProperties = this.constructor\n                .elementProperties;\n            if (elementProperties.size > 0) {\n                for (const [p, options] of elementProperties) {\n                    const { wrapped } = options;\n                    const value = this[p];\n                    if (wrapped === true &&\n                        !this._$changedProperties.has(p) &&\n                        value !== undefined) {\n                        this._$changeProperty(p, undefined, options, value);\n                    }\n                }\n            }\n        }\n        let shouldUpdate = false;\n        const changedProperties = this._$changedProperties;\n        try {\n            shouldUpdate = this.shouldUpdate(changedProperties);\n            if (shouldUpdate) {\n                this.willUpdate(changedProperties);\n                this.__controllers?.forEach((c) => c.hostUpdate?.());\n                this.update(changedProperties);\n            }\n            else {\n                this.__markUpdated();\n            }\n        }\n        catch (e) {\n            // Prevent `firstUpdated` and `updated` from running when there's an\n            // update exception.\n            shouldUpdate = false;\n            // Ensure element can accept additional updates after an exception.\n            this.__markUpdated();\n            throw e;\n        }\n        // The update is no longer considered pending and further updates are now allowed.\n        if (shouldUpdate) {\n            this._$didUpdate(changedProperties);\n        }\n    }\n    /**\n     * Invoked before `update()` to compute values needed during the update.\n     *\n     * Implement `willUpdate` to compute property values that depend on other\n     * properties and are used in the rest of the update process.\n     *\n     * ```ts\n     * willUpdate(changedProperties) {\n     *   // only need to check changed properties for an expensive computation.\n     *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n     *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n     *   }\n     * }\n     *\n     * render() {\n     *   return html`SHA: ${this.sha}`;\n     * }\n     * ```\n     *\n     * @category updates\n     */\n    willUpdate(_changedProperties) { }\n    // Note, this is an override point for polyfill-support.\n    // @internal\n    _$didUpdate(changedProperties) {\n        this.__controllers?.forEach((c) => c.hostUpdated?.());\n        if (!this.hasUpdated) {\n            this.hasUpdated = true;\n            this.firstUpdated(changedProperties);\n        }\n        this.updated(changedProperties);\n        if (this.isUpdatePending &&\n            this.constructor.enabledWarnings.includes('change-in-update')) {\n            issueWarning('change-in-update', `Element ${this.localName} scheduled an update ` +\n                `(generally because a property was set) ` +\n                `after an update completed, causing a new update to be scheduled. ` +\n                `This is inefficient and should be avoided unless the next update ` +\n                `can only be scheduled as a side effect of the previous update.`);\n        }\n    }\n    __markUpdated() {\n        this._$changedProperties = new Map();\n        this.isUpdatePending = false;\n    }\n    /**\n     * Returns a Promise that resolves when the element has completed updating.\n     * The Promise value is a boolean that is `true` if the element completed the\n     * update without triggering another update. The Promise result is `false` if\n     * a property was set inside `updated()`. If the Promise is rejected, an\n     * exception was thrown during the update.\n     *\n     * To await additional asynchronous work, override the `getUpdateComplete`\n     * method. For example, it is sometimes useful to await a rendered element\n     * before fulfilling this Promise. To do this, first await\n     * `super.getUpdateComplete()`, then any subsequent state.\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    get updateComplete() {\n        return this.getUpdateComplete();\n    }\n    /**\n     * Override point for the `updateComplete` promise.\n     *\n     * It is not safe to override the `updateComplete` getter directly due to a\n     * limitation in TypeScript which means it is not possible to call a\n     * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n     * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n     * This method should be overridden instead. For example:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   override async getUpdateComplete() {\n     *     const result = await super.getUpdateComplete();\n     *     await this._myChild.updateComplete;\n     *     return result;\n     *   }\n     * }\n     * ```\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    getUpdateComplete() {\n        return this.__updatePromise;\n    }\n    /**\n     * Controls whether or not `update()` should be called when the element requests\n     * an update. By default, this method always returns `true`, but this can be\n     * customized to control when to update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    shouldUpdate(_changedProperties) {\n        return true;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes.\n     * It can be overridden to render and keep updated element DOM.\n     * Setting properties inside this method will *not* trigger\n     * another update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(_changedProperties) {\n        // The forEach() expression will only run when __reflectingProperties is\n        // defined, and it returns undefined, setting __reflectingProperties to\n        // undefined\n        this.__reflectingProperties &&= this.__reflectingProperties.forEach((p) => this.__propertyToAttribute(p, this[p]));\n        this.__markUpdated();\n    }\n    /**\n     * Invoked whenever the element is updated. Implement to perform\n     * post-updating tasks via DOM APIs, for example, focusing an element.\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    updated(_changedProperties) { }\n    /**\n     * Invoked when the element is first updated. Implement to perform one time\n     * work on the element after update.\n     *\n     * ```ts\n     * firstUpdated() {\n     *   this.renderRoot.getElementById('my-text-area').focus();\n     * }\n     * ```\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    firstUpdated(_changedProperties) { }\n}\n/**\n * Memoized list of all element styles.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category styles\n */\nReactiveElement.elementStyles = [];\n/**\n * Options used when calling `attachShadow`. Set this property to customize\n * the options for the shadowRoot; for example, to create a closed\n * shadowRoot: `{mode: 'closed'}`.\n *\n * Note, these options are used in `createRenderRoot`. If this method\n * is customized, options should be respected if possible.\n * @nocollapse\n * @category rendering\n */\nReactiveElement.shadowRootOptions = { mode: 'open' };\n// Assigned here to work around a jscompiler bug with static fields\n// when compiling to ES5.\n// https://github.com/google/closure-compiler/issues/3177\nReactiveElement[JSCompiler_renameProperty('elementProperties')] = new Map();\nReactiveElement[JSCompiler_renameProperty('finalized')] = new Map();\n// Apply polyfills if available\npolyfillSupport?.({ ReactiveElement });\n// Dev mode warnings...\n{\n    // Default warning set.\n    ReactiveElement.enabledWarnings = [\n        'change-in-update',\n        'async-perform-update',\n    ];\n    const ensureOwnWarnings = function (ctor) {\n        if (!ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings'))) {\n            ctor.enabledWarnings = ctor.enabledWarnings.slice();\n        }\n    };\n    ReactiveElement.enableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        if (!this.enabledWarnings.includes(warning)) {\n            this.enabledWarnings.push(warning);\n        }\n    };\n    ReactiveElement.disableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        const i = this.enabledWarnings.indexOf(warning);\n        if (i >= 0) {\n            this.enabledWarnings.splice(i, 1);\n        }\n    };\n}\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('2.1.0');\nif (global.reactiveElementVersions.length > 1) {\n    queueMicrotask(() => {\n        issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n            `is not recommended.`);\n    });\n}\n\n\n//# sourceMappingURL=reactive-element.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit/reactive-element/node/development/reactive-element.js\n");

/***/ })

};
;