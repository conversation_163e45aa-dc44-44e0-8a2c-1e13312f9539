import React from "react";
import { ChevronRight } from "lucide-react";
import { Perk } from "./types";
import { motion, AnimatePresence } from "framer-motion";

interface PerksCarouselProps {
  sortedPerks: Perk[];
  carouselIndex: number;
  cardsPerView: number;
  maxCarouselIndex: number;
  setCarouselIndex: React.Dispatch<React.SetStateAction<number>>;
}

const PerksCarousel: React.FC<PerksCarouselProps> = ({
  sortedPerks,
  carouselIndex,
  cardsPerView,
  maxCarouselIndex,
  setCarouselIndex,
}) => (
  <div className="relative w-full items-center hidden lg:flex">
    {/* Left Chevron */}
    <button
      className="absolute left-0 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-500 transition-colors z-10 disabled:opacity-50 disabled:cursor-not-allowed"
      onClick={() => setCarouselIndex(i => Math.max(0, i - cardsPerView))}
      disabled={carouselIndex === 0}
      aria-label="Previous perks"
    >
      <ChevronRight className="w-6 h-6  rotate-180 text-[#FF6600]" />
    </button>
    <div className="flex gap-6 w-full ">
      <AnimatePresence initial={false} mode="wait">
        {sortedPerks.slice(carouselIndex, carouselIndex + 3).map((perk, idx) => (
          <motion.div
            key={perk.perkId || idx}
            className="bg-white rounded-2xl shadow-lg p-4 flex-shrink-0 w-80 "
            aria-label={`Perk card: ${perk.name}`}
            tabIndex={0}
            initial={{ opacity: 0, x: carouselIndex > 0 ? 40 : -40 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: carouselIndex > 0 ? -40 : 40 }}
            transition={{ duration: 0.3 }}
          >
            <img
              src={perk.image}
              alt={perk.name}
              className="w-full h-40 object-cover rounded-xl mb-3"
              aria-label={`Perk image: ${perk.name}`}
            />
            <div className="font-bold text-base mb-1">{perk.name}</div>
            <div className="flex justify-between text-xs text-gray-600 mt-2">
              <div>
                <div className="text-gray-500">Price</div>
                <div className="font-semibold">{perk.price}</div>
              </div>
              <div className="text-right">
                <div className="text-gray-500">Sales</div>
                <div className="font-semibold">{perk.soldCount ?? '-'} </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
    {/* Right Chevron */}
    <button
      className="absolute right-0 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-500 transition-colors z-10 disabled:opacity-50 disabled:cursor-not-allowed"
      onClick={() => setCarouselIndex(i => Math.min(maxCarouselIndex, i + cardsPerView))}
      disabled={carouselIndex >= maxCarouselIndex || sortedPerks.length <= cardsPerView}
      aria-label="Next perks"
    >
      <ChevronRight className="w-6 h-6 text-[#FF6600]" />
    </button>
  </div>
);

export default PerksCarousel; 