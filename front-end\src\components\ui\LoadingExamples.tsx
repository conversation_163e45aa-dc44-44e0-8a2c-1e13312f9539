"use client";

import React, { useState } from 'react';
import { 
  LoadingSpinner, 
  TokenDetailsLoading, 
  CompactTokenLoading, 
  PageLoading,
  PulseLoading,
  SkeletonCard,
  EscrowLoadingModal,
  InlineLoading,
  NetworkStatus,
  WalletStatus
} from './LoadingComponents';

/**
 * Loading Components Usage Examples
 * 
 * This file demonstrates how to use the various professional loading components
 * that replace simple "Loading..." text throughout the application.
 */

export const LoadingExamplesDemo: React.FC = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <div className="space-y-12 p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Professional Loading Components
        </h1>

        {/* Basic Spinner Examples */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Basic Spinners</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <LoadingSpinner size="sm" color="primary" />
              <p className="text-sm text-gray-600 mt-2">Small</p>
            </div>
            <div className="text-center">
              <LoadingSpinner size="md" color="success" />
              <p className="text-sm text-gray-600 mt-2">Medium</p>
            </div>
            <div className="text-center">
              <LoadingSpinner size="lg" color="warning" />
              <p className="text-sm text-gray-600 mt-2">Large</p>
            </div>
            <div className="text-center">
              <LoadingSpinner size="xl" color="danger" />
              <p className="text-sm text-gray-600 mt-2">Extra Large</p>
            </div>
          </div>
        </section>

        {/* Token Details Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Token Details Loading</h2>
          <div className="border rounded-lg">
            <TokenDetailsLoading 
              message="Loading perk details..."
              size="md"
              className="py-16"
            />
          </div>
        </section>

        {/* Compact Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Compact Loading</h2>
          <div className="border rounded-lg">
            <CompactTokenLoading 
              message="Loading token data..."
              className="py-8"
            />
          </div>
        </section>

        {/* Inline Loading Examples */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Inline Loading</h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <InlineLoading operation="buy" size="sm" />
              <span className="text-gray-600">Buy operation in progress</span>
            </div>
            <div className="flex items-center space-x-4">
              <InlineLoading operation="sell" size="md" />
              <span className="text-gray-600">Sell operation in progress</span>
            </div>
          </div>
        </section>

        {/* Status Indicators */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Status Indicators</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Network Status</h3>
              <div className="flex space-x-4">
                <NetworkStatus isConnected={true} isHealthy={true} />
                <NetworkStatus isConnected={true} isHealthy={false} />
                <NetworkStatus isConnected={false} isHealthy={false} />
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Wallet Status</h3>
              <div className="flex space-x-4">
                <WalletStatus 
                  isConnected={true} 
                  address="7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU" 
                  balance={4.2567} 
                />
                <WalletStatus isConnected={false} />
              </div>
            </div>
          </div>
        </section>

        {/* Skeleton Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Skeleton Loading</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SkeletonCard showImage={true} showButton={true} />
            <div className="space-y-4">
              <PulseLoading lines={3} showAvatar={true} />
              <PulseLoading lines={2} showAvatar={false} />
            </div>
          </div>
        </section>

        {/* Modal Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold mb-4">Modal Loading</h2>
          <button
            onClick={() => setShowModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Show Loading Modal
          </button>
          
          <EscrowLoadingModal
            isOpen={showModal}
            operation="buy"
            currentStep={2}
            onCancel={() => setShowModal(false)}
            canCancel={true}
          />
        </section>

        {/* Usage Guidelines */}
        <section className="bg-blue-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-900">Usage Guidelines</h2>
          <div className="space-y-3 text-blue-800">
            <div>
              <strong>TokenDetailsLoading:</strong> Use for full-page loading states when loading perk/token details
            </div>
            <div>
              <strong>CompactTokenLoading:</strong> Use for smaller sections or components loading token data
            </div>
            <div>
              <strong>PageLoading:</strong> Use for general full-page loading states
            </div>
            <div>
              <strong>InlineLoading:</strong> Use for button loading states and inline operations
            </div>
            <div>
              <strong>SkeletonCard/PulseLoading:</strong> Use for content placeholders while data loads
            </div>
            <div>
              <strong>EscrowLoadingModal:</strong> Use for escrow operations that require user attention
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default LoadingExamplesDemo;
