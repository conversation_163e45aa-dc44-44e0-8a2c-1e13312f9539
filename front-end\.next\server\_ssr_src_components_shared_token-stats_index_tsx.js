"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_token-stats_index_tsx";
exports.ids = ["_ssr_src_components_shared_token-stats_index_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/token-stats/index.tsx":
/*!*****************************************************!*\
  !*** ./src/components/shared/token-stats/index.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,DollarSign,TrendingDown,TrendingUp,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_LoadingSkeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/LoadingSkeletons */ \"(ssr)/./src/components/ui/LoadingSkeletons.tsx\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/animations */ \"(ssr)/./src/lib/animations.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst TokenStats = ({ className, symbols = [\n    'ANAS/USDTaaa'\n], creator = 'By Anas Sharif', tokenImage = '/images/placeholder.png', stats, loading = false })=>{\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSymbol, setSelectedSymbol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(symbols[0]);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const formatPercentage = (value)=>{\n        return value.toFixed(2) + '%';\n    };\n    const formatVolume = (value)=>{\n        if (value >= 1000000) {\n            return (value / 1000000).toFixed(1) + 'M';\n        } else if (value >= 1000) {\n            return (value / 1000).toFixed(1) + 'K';\n        }\n        return value.toString();\n    };\n    const statsDisplay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TokenStats.useMemo[statsDisplay]\": ()=>[\n                {\n                    label: t('tokenStats.marketValue'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    color: 'text-[#FF6600]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.low24h'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    color: 'text-red-600',\n                    bgColor: 'bg-red-50',\n                    borderColor: 'border-red-200'\n                },\n                {\n                    label: t('tokenStats.high24h'),\n                    value: stats.usd.toFixed(4),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    color: 'text-[#F58A38]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.volume'),\n                    value: formatVolume(stats.volume24h),\n                    icon: _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    color: 'text-[#FF6600]',\n                    bgColor: 'bg-[#FFF7F1]',\n                    borderColor: 'border-[#F58A38]/30'\n                },\n                {\n                    label: t('tokenStats.return24h'),\n                    value: formatPercentage(stats.return24h),\n                    icon: stats.return24h >= 0 ? _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    color: stats.return24h >= 0 ? 'text-[#F58A38]' : 'text-red-600',\n                    bgColor: stats.return24h >= 0 ? 'bg-[#FFF7F1]' : 'bg-red-50',\n                    borderColor: stats.return24h >= 0 ? 'border-[#F58A38]/30' : 'border-red-200'\n                }\n            ]\n    }[\"TokenStats.useMemo[statsDisplay]\"], [\n        stats.usd,\n        stats.volume24h,\n        stats.return24h\n    ]);\n    const statsValueMaxLength = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TokenStats.useMemo[statsValueMaxLength]\": ()=>{\n            return Math.max(...statsDisplay.map({\n                \"TokenStats.useMemo[statsValueMaxLength]\": (stat)=>stat.value.length\n            }[\"TokenStats.useMemo[statsValueMaxLength]\"]));\n        }\n    }[\"TokenStats.useMemo[statsValueMaxLength]\"], [\n        statsDisplay\n    ]);\n    const toggleDropdown = ()=>{\n        setIsDropdownOpen(!isDropdownOpen);\n    };\n    const handleSelectSymbol = (newSymbol)=>{\n        setSelectedSymbol(newSymbol);\n        setIsDropdownOpen(false);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSkeletons__WEBPACK_IMPORTED_MODULE_2__.StatsSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n            lineNumber: 117,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        className: `w-full bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden ${className}`,\n        ..._lib_animations__WEBPACK_IMPORTED_MODULE_3__.fadeInUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"flex items-center space-x-4\",\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"h-12 w-12 rounded-xl bg-gradient-to-br from-[#FF6600] to-[#F58A38] flex items-center justify-center shadow-lg overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            type: 'spring',\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 24,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h3, {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: selectedSymbol\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                            className: \"flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-lg hover:bg-gray-100\",\n                                                            onClick: toggleDropdown,\n                                                            whileHover: {\n                                                                scale: 1.05\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                animate: {\n                                                                    rotate: isDropdownOpen ? 180 : 0\n                                                                },\n                                                                transition: {\n                                                                    duration: 0.3\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    size: 18\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                                            children: isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                className: \"absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-xl z-50 border border-gray-200 overflow-hidden\",\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    y: -10,\n                                                                    scale: 0.95\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    y: 0,\n                                                                    scale: 1\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    y: -10,\n                                                                    scale: 0.95\n                                                                },\n                                                                transition: {\n                                                                    duration: 0.2\n                                                                },\n                                                                children: symbols.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                        className: \"flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                                        onClick: ()=>handleSelectSymbol(symbol),\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -10\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: index * 0.05\n                                                                        },\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 rounded-lg bg-gray-100 mr-3 flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    size: 16,\n                                                                                    className: \"text-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                    lineNumber: 191,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: symbol\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                        lineNumber: 194,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: t('tokenStats.tradingPair')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                        lineNumber: 197,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                                lineNumber: 193,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"flex items-center space-x-2 mt-1\",\n                                            initial: {\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_DollarSign_TrendingDown_TrendingUp_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: creator\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"flex items-center space-x-2\",\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1 bg-[#FFF7F1] rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-[#FF6600]\",\n                                        children: t('tokenStats.activeTrading')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-[#F58A38] rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: `grid gap-4 ${statsValueMaxLength > 10 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4' : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5'}`,\n                    variants: _lib_animations__WEBPACK_IMPORTED_MODULE_3__.staggerContainer,\n                    initial: \"initial\",\n                    animate: \"animate\",\n                    children: statsDisplay.map((stat, index)=>{\n                        const IconComponent = stat.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: `relative group ${stat.bgColor} ${stat.borderColor} border rounded-xl p-4 transition-all duration-300 hover:shadow-md cursor-pointer`,\n                            variants: {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    transition: {\n                                        delay: 0.5 + index * 0.1\n                                    }\n                                }\n                            },\n                            whileHover: {\n                                scale: 1.03,\n                                y: -2\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-8 h-8 ${stat.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                size: 16,\n                                                className: stat.color\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                            className: `text-lg font-bold ${stat.color} group-hover:scale-105 transition-transform duration-200`,\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-white/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\token-stats\\\\index.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TokenStats);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/token-stats/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSkeletons.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/LoadingSkeletons.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommentsSkeleton: () => (/* binding */ CommentsSkeleton),\n/* harmony export */   DashboardSkeleton: () => (/* binding */ DashboardSkeleton),\n/* harmony export */   ProductCardSkeleton: () => (/* binding */ ProductCardSkeleton),\n/* harmony export */   StatsSkeleton: () => (/* binding */ StatsSkeleton),\n/* harmony export */   TableSkeleton: () => (/* binding */ TableSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TableSkeleton,StatsSkeleton,CommentsSkeleton,ProductCardSkeleton,DashboardSkeleton auto */ \n\n\n// Animation variants\nconst shimmer = {\n    animate: {\n        backgroundPosition: [\n            '200% 0',\n            '-200% 0'\n        ]\n    },\n    transition: {\n        duration: 2,\n        ease: 'linear',\n        repeat: Infinity\n    }\n};\nconst pulse = {\n    animate: {\n        opacity: [\n            0.6,\n            1,\n            0.6\n        ]\n    },\n    transition: {\n        duration: 1.5,\n        ease: 'easeInOut',\n        repeat: Infinity\n    }\n};\nconst wave = {\n    animate: {\n        backgroundPosition: [\n            '-200% 0',\n            '200% 0'\n        ]\n    },\n    transition: {\n        duration: 1.5,\n        ease: 'linear',\n        repeat: Infinity\n    }\n};\n// Base skeleton component with improved accessibility\nconst SkeletonBase = ({ className = '', children, variant = 'rectangular', width, height, animation = 'shimmer', 'data-testid': dataTestId, 'aria-label': ariaLabel })=>{\n    const getAnimationVariant = ()=>{\n        switch(animation){\n            case 'pulse':\n                return pulse;\n            case 'wave':\n                return wave;\n            case 'shimmer':\n            default:\n                return shimmer;\n        }\n    };\n    const getVariantClasses = ()=>{\n        switch(variant){\n            case 'circular':\n                return 'rounded-full';\n            case 'text':\n                return 'rounded';\n            case 'rectangular':\n            default:\n                return 'rounded';\n        }\n    };\n    const style = {\n        width: width,\n        height: height\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: `bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] ${getVariantClasses()} ${className}`,\n        style: style,\n        ...getAnimationVariant(),\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        role: \"status\",\n        \"aria-live\": \"polite\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n// Table skeleton with improved structure\nconst TableSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-5 bg-neutral-200 rounded-2xl\",\n        \"data-testid\": props['data-testid'] || 'table-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading table data\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-8 w-48 mb-6 rounded\",\n                \"aria-label\": \"Loading table title\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 118,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 3\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-32 rounded\",\n                                \"aria-label\": `Loading row ${i + 1} column 1`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": `Loading row ${i + 1} column 2`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-16 rounded\",\n                                \"aria-label\": `Loading row ${i + 1} column 3`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-8 w-20 rounded\",\n                                \"aria-label\": `Loading row ${i + 1} action`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 119,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 112,\n        columnNumber: 3\n    }, undefined);\n// Header skeleton with improved accessibility\nconst HeaderSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center mb-8\",\n        \"data-testid\": props['data-testid'] || 'header-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading page header\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-12 w-64 rounded\",\n                        \"aria-label\": \"Loading page title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-6 w-48 rounded\",\n                        \"aria-label\": \"Loading page subtitle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 140,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-10 w-32 rounded\",\n                \"aria-label\": \"Loading header action\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 144,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 134,\n        columnNumber: 3\n    }, undefined);\n// Stats skeleton with improved structure\nconst StatsSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border p-6\",\n        \"data-testid\": props['data-testid'] || 'stats-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading statistics\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-12 h-12 rounded-full mr-4\",\n                        \"aria-label\": \"Loading stats icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-5 w-32 rounded\",\n                                \"aria-label\": \"Loading stats title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading stats subtitle\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 156,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-5 gap-4\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 5\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-16 rounded mb-2\",\n                                \"aria-label\": `Loading stat ${i + 1} label`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-5 w-20 rounded\",\n                                \"aria-label\": `Loading stat ${i + 1} value`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 167,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined);\n// Comments skeleton with improved structure\nconst CommentsSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        \"data-testid\": props['data-testid'] || 'comments-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading comments\",\n        children: Array.from({\n            length: 3\n        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-14 h-14 rounded-full\",\n                        \"aria-label\": `Loading comment ${i + 1} avatar`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": `Loading comment ${i + 1} author`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-full rounded\",\n                                \"aria-label\": `Loading comment ${i + 1} content`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-3 w-20 rounded\",\n                                \"aria-label\": `Loading comment ${i + 1} timestamp`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, i, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 180,\n        columnNumber: 3\n    }, undefined);\nconst ProductCardSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-md p-6\",\n        \"data-testid\": props['data-testid'] || 'product-card-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading product card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        variant: \"circular\",\n                        className: \"w-10 h-10 rounded-full mr-3\",\n                        \"aria-label\": \"Loading product avatar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-4 w-24 rounded\",\n                                \"aria-label\": \"Loading product name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-3 w-16 rounded\",\n                                \"aria-label\": \"Loading product category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 209,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                className: \"h-16 w-full rounded mb-4\",\n                \"aria-label\": \"Loading product image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 220,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                role: \"presentation\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-full rounded\",\n                        \"aria-label\": \"Loading product description line 1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-3/4 rounded\",\n                        \"aria-label\": \"Loading product description line 2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-4 w-1/2 rounded\",\n                        \"aria-label\": \"Loading product description line 3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 221,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 border rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-6 w-20 rounded mb-3\",\n                        \"aria-label\": \"Loading price label\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-8 w-24 rounded\",\n                                \"aria-label\": \"Loading price\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                                className: \"h-10 w-32 rounded-full\",\n                                \"aria-label\": \"Loading action button\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 226,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 203,\n        columnNumber: 3\n    }, undefined);\n// Dashboard skeleton with improved structure\nconst DashboardSkeleton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        \"data-testid\": props['data-testid'] || 'dashboard-skeleton',\n        role: \"status\",\n        \"aria-label\": \"Loading dashboard\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 244,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                role: \"presentation\",\n                children: Array.from({\n                    length: 6\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonBase, {\n                        className: \"h-32 rounded-lg\",\n                        \"aria-label\": `Loading dashboard card ${i + 1}`\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 245,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n                lineNumber: 254,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\LoadingSkeletons.tsx\",\n        lineNumber: 238,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSkeletons.tsx\n");

/***/ })

};
;