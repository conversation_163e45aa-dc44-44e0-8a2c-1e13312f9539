"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_header_MobileMenu_tsx";
exports.ids = ["_ssr_src_components_shared_header_MobileMenu_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/header/MobileMenu.tsx":
/*!*****************************************************!*\
  !*** ./src/components/shared/header/MobileMenu.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ \"(ssr)/./src/constants/index.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n\n\n\n\n\n\nconst MobileMenu = ({ menuRef, isActiveRoute, handleNavItemClick, hideCreate, handleCreateTokenClick, handleCreatePerkClick, authenticated, login, logout })=>{\n    const { t, isReady } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const navigationItems = [\n        {\n            label: t('navigation.dashboard'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.DASHBOARD,\n            icon: \"/icons/dashboard.svg\",\n            activeIcon: \"/icons/dashboard.svg\",\n            requiresAuth: true\n        },\n        {\n            label: t('navigation.perks'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.PERKS,\n            icon: \"/icons/perks.svg\",\n            activeIcon: \"/icons/perks.svg\",\n            requiresAuth: false\n        },\n        {\n            label: t('navigation.coins'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.HOME,\n            icon: \"/icons/coin.svg\",\n            activeIcon: \"/icons/coin.svg\",\n            requiresAuth: false\n        }\n    ];\n    return !isReady ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        ref: menuRef,\n        className: \"absolute top-full left-0 right-0 z-50 lg:hidden flex flex-col bg-[#F5F5F5] border-b border-[#E6E6E6] overflow-hidden shadow-lg\",\n        initial: {\n            height: 0,\n            opacity: 0\n        },\n        animate: {\n            height: \"auto\",\n            opacity: 1\n        },\n        exit: {\n            height: 0,\n            opacity: 0\n        },\n        transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-3 flex flex-col gap-2\",\n            children: [\n                navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: `flex items-center gap-2 p-2.5 text-sm font-medium cursor-pointer rounded-lg transition-colors ${isActiveRoute(item.route) ? \"bg-black text-white\" : \"hover:bg-gray-200\"}`,\n                        onClick: ()=>handleNavItemClick(item),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: isActiveRoute(item.route) ? item.activeIcon : item.icon,\n                                alt: item.label,\n                                width: 20,\n                                height: 20,\n                                className: `${isActiveRoute(item.route) ? \"invert\" : \"\"} h-auto`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, undefined),\n                            item.label\n                        ]\n                    }, item.label, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)),\n                !hideCreate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            className: \"flex items-center p-2.5 text-sm font-medium cursor-pointer rounded-lg hover:bg-gray-200\",\n                            onClick: handleCreateTokenClick,\n                            children: t('header.createYourToken')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.45\n                            },\n                            className: \"flex items-center p-2.5 text-sm font-medium cursor-pointer rounded-lg hover:bg-gray-200\",\n                            onClick: handleCreatePerkClick,\n                            children: t('header.createPerk')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-300 mt-1 pt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        children: !authenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: login,\n                            className: \"flex items-center p-2.5 text-sm font-medium cursor-pointer bg-black text-white rounded-lg m-1 hover:bg-gray-800 transition-colors\",\n                            children: t('auth.login')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 17\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-2.5 text-sm font-medium cursor-pointer bg-black text-white rounded-lg m-1 hover:bg-gray-800 transition-colors\",\n                            onClick: logout,\n                            children: t('navigation.logout')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n            lineNumber: 67,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\MobileMenu.tsx\",\n        lineNumber: 59,\n        columnNumber: 7\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenu);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zaGFyZWQvaGVhZGVyL01vYmlsZU1lbnUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBdUM7QUFDUjtBQUNMO0FBRVc7QUFDbUI7QUFjeEQsTUFBTUssYUFBd0MsQ0FBQyxFQUM3Q0MsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLGtCQUFrQixFQUNsQkMsVUFBVSxFQUNWQyxzQkFBc0IsRUFDdEJDLHFCQUFxQixFQUNyQkMsYUFBYSxFQUNiQyxLQUFLLEVBQ0xDLE1BQU0sRUFDUDtJQUNDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFQyxPQUFPLEVBQUUsR0FBR1oscUVBQWNBO0lBRXJDLE1BQU1hLGtCQUFrQjtRQUN0QjtZQUNFQyxPQUFPSCxFQUFFO1lBQ1RJLE9BQU9oQiw4Q0FBTUEsQ0FBQ2lCLFNBQVM7WUFDdkJDLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRUwsT0FBT0gsRUFBRTtZQUNUSSxPQUFPaEIsOENBQU1BLENBQUNxQixLQUFLO1lBQ25CSCxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsY0FBYztRQUNoQjtRQUNBO1lBQ0VMLE9BQU9ILEVBQUU7WUFDVEksT0FBT2hCLDhDQUFNQSxDQUFDc0IsSUFBSTtZQUNsQkosTUFBTTtZQUNOQyxZQUFZO1lBQ1pDLGNBQWM7UUFDaEI7S0FDRDtJQUVELE9BQ0UsQ0FBQ1AsVUFBVSxxQkFDVCw4REFBQ2hCLCtFQUFNQSxDQUFDMEIsR0FBRztRQUNUQyxLQUFLckI7UUFDTHNCLFdBQVU7UUFDVkMsU0FBUztZQUFFQyxRQUFRO1lBQUdDLFNBQVM7UUFBRTtRQUNqQ0MsU0FBUztZQUFFRixRQUFRO1lBQVFDLFNBQVM7UUFBRTtRQUN0Q0UsTUFBTTtZQUFFSCxRQUFRO1lBQUdDLFNBQVM7UUFBRTtRQUM5QkcsWUFBWTtZQUFFQyxVQUFVO1lBQUtDLE1BQU07UUFBWTtrQkFFL0MsNEVBQUNWO1lBQUlFLFdBQVU7O2dCQUNaWCxnQkFBZ0JvQixHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQzFCLDhEQUFDdkMsK0VBQU1BLENBQUMwQixHQUFHO3dCQUVURyxTQUFTOzRCQUFFRSxTQUFTOzRCQUFHUyxHQUFHLENBQUM7d0JBQUc7d0JBQzlCUixTQUFTOzRCQUFFRCxTQUFTOzRCQUFHUyxHQUFHO3dCQUFFO3dCQUM1Qk4sWUFBWTs0QkFBRU8sT0FBT0YsUUFBUTt3QkFBSTt3QkFDakNYLFdBQVcsQ0FBQyw4RkFBOEYsRUFDeEdyQixjQUFjK0IsS0FBS25CLEtBQUssSUFDcEIsd0JBQ0EscUJBQ0o7d0JBQ0Z1QixTQUFTLElBQU1sQyxtQkFBbUI4Qjs7MENBRWxDLDhEQUFDckMsa0RBQUtBO2dDQUNKMEMsS0FBS3BDLGNBQWMrQixLQUFLbkIsS0FBSyxJQUFJbUIsS0FBS2hCLFVBQVUsR0FBR2dCLEtBQUtqQixJQUFJO2dDQUM1RHVCLEtBQUtOLEtBQUtwQixLQUFLO2dDQUNmMkIsT0FBTztnQ0FDUGYsUUFBUTtnQ0FDUkYsV0FBVyxHQUFHckIsY0FBYytCLEtBQUtuQixLQUFLLElBQUksV0FBVyxHQUFHLE9BQU8sQ0FBQzs7Ozs7OzRCQUVqRW1CLEtBQUtwQixLQUFLOzt1QkFsQk5vQixLQUFLcEIsS0FBSzs7Ozs7Z0JBcUJsQixDQUFDVCw0QkFDQTs7c0NBQ0UsOERBQUNULCtFQUFNQSxDQUFDMEIsR0FBRzs0QkFDVEcsU0FBUztnQ0FBRUUsU0FBUztnQ0FBR1MsR0FBRyxDQUFDOzRCQUFHOzRCQUM5QlIsU0FBUztnQ0FBRUQsU0FBUztnQ0FBR1MsR0FBRzs0QkFBRTs0QkFDNUJOLFlBQVk7Z0NBQUVPLE9BQU87NEJBQUk7NEJBQ3pCYixXQUFVOzRCQUNWYyxTQUFTaEM7c0NBRVJLLEVBQUU7Ozs7OztzQ0FFTCw4REFBQ2YsK0VBQU1BLENBQUMwQixHQUFHOzRCQUNURyxTQUFTO2dDQUFFRSxTQUFTO2dDQUFHUyxHQUFHLENBQUM7NEJBQUc7NEJBQzlCUixTQUFTO2dDQUFFRCxTQUFTO2dDQUFHUyxHQUFHOzRCQUFFOzRCQUM1Qk4sWUFBWTtnQ0FBRU8sT0FBTzs0QkFBSzs0QkFDMUJiLFdBQVU7NEJBQ1ZjLFNBQVMvQjtzQ0FFUkksRUFBRTs7Ozs7Ozs7OEJBS1QsOERBQUNXO29CQUFJRSxXQUFVOzhCQUNiLDRFQUFDNUIsK0VBQU1BLENBQUMwQixHQUFHO3dCQUNURyxTQUFTOzRCQUFFRSxTQUFTOzRCQUFHUyxHQUFHLENBQUM7d0JBQUc7d0JBQzlCUixTQUFTOzRCQUFFRCxTQUFTOzRCQUFHUyxHQUFHO3dCQUFFO3dCQUM1Qk4sWUFBWTs0QkFBRU8sT0FBTzt3QkFBSTtrQ0FFeEIsQ0FBQzdCLDhCQUNBLDhEQUFDYzs0QkFDQ2dCLFNBQVM3Qjs0QkFDVGUsV0FBVTtzQ0FFVGIsRUFBRTs7Ozs7c0RBR0wsOERBQUNXOzRCQUNDRSxXQUFVOzRCQUNWYyxTQUFTNUI7c0NBRVJDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNyQjtBQUVBLGlFQUFlVixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxjb21wb25lbnRzXFxzaGFyZWRcXGhlYWRlclxcTW9iaWxlTWVudS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5pbXBvcnQgeyBST1VURVMgfSBmcm9tICdAL2NvbnN0YW50cyc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlVHJhbnNsYXRpb24nO1xuXG5pbnRlcmZhY2UgTW9iaWxlTWVudVByb3BzIHtcbiAgbWVudVJlZjogUmVhY3QuUmVmT2JqZWN0PEhUTUxEaXZFbGVtZW50IHwgbnVsbD47XG4gIGlzQWN0aXZlUm91dGU6IChyb3V0ZTogc3RyaW5nKSA9PiBib29sZWFuO1xuICBoYW5kbGVOYXZJdGVtQ2xpY2s6IChpdGVtOiBhbnkpID0+IHZvaWQ7XG4gIGhpZGVDcmVhdGU6IGJvb2xlYW47XG4gIGhhbmRsZUNyZWF0ZVRva2VuQ2xpY2s6ICgpID0+IHZvaWQ7XG4gIGhhbmRsZUNyZWF0ZVBlcmtDbGljazogKCkgPT4gdm9pZDtcbiAgYXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgbG9naW46ICgpID0+IHZvaWQ7XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgTW9iaWxlTWVudTogUmVhY3QuRkM8TW9iaWxlTWVudVByb3BzPiA9ICh7XG4gIG1lbnVSZWYsXG4gIGlzQWN0aXZlUm91dGUsXG4gIGhhbmRsZU5hdkl0ZW1DbGljayxcbiAgaGlkZUNyZWF0ZSxcbiAgaGFuZGxlQ3JlYXRlVG9rZW5DbGljayxcbiAgaGFuZGxlQ3JlYXRlUGVya0NsaWNrLFxuICBhdXRoZW50aWNhdGVkLFxuICBsb2dpbixcbiAgbG9nb3V0LFxufSkgPT4ge1xuICBjb25zdCB7IHQsIGlzUmVhZHkgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XG5cbiAgY29uc3QgbmF2aWdhdGlvbkl0ZW1zID0gW1xuICAgIHtcbiAgICAgIGxhYmVsOiB0KCduYXZpZ2F0aW9uLmRhc2hib2FyZCcpLFxuICAgICAgcm91dGU6IFJPVVRFUy5EQVNIQk9BUkQsXG4gICAgICBpY29uOiBcIi9pY29ucy9kYXNoYm9hcmQuc3ZnXCIsXG4gICAgICBhY3RpdmVJY29uOiBcIi9pY29ucy9kYXNoYm9hcmQuc3ZnXCIsXG4gICAgICByZXF1aXJlc0F1dGg6IHRydWUsXG4gICAgfSxcbiAgICB7XG4gICAgICBsYWJlbDogdCgnbmF2aWdhdGlvbi5wZXJrcycpLFxuICAgICAgcm91dGU6IFJPVVRFUy5QRVJLUyxcbiAgICAgIGljb246IFwiL2ljb25zL3BlcmtzLnN2Z1wiLFxuICAgICAgYWN0aXZlSWNvbjogXCIvaWNvbnMvcGVya3Muc3ZnXCIsXG4gICAgICByZXF1aXJlc0F1dGg6IGZhbHNlLFxuICAgIH0sXG4gICAge1xuICAgICAgbGFiZWw6IHQoJ25hdmlnYXRpb24uY29pbnMnKSxcbiAgICAgIHJvdXRlOiBST1VURVMuSE9NRSxcbiAgICAgIGljb246IFwiL2ljb25zL2NvaW4uc3ZnXCIsXG4gICAgICBhY3RpdmVJY29uOiBcIi9pY29ucy9jb2luLnN2Z1wiLFxuICAgICAgcmVxdWlyZXNBdXRoOiBmYWxzZSxcbiAgICB9LFxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgIWlzUmVhZHkgPyBudWxsIDogKFxuICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgcmVmPXttZW51UmVmfVxuICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgcmlnaHQtMCB6LTUwIGxnOmhpZGRlbiBmbGV4IGZsZXgtY29sIGJnLVsjRjVGNUY1XSBib3JkZXItYiBib3JkZXItWyNFNkU2RTZdIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbGdcIlxuICAgICAgICBpbml0aWFsPXt7IGhlaWdodDogMCwgb3BhY2l0eTogMCB9fVxuICAgICAgICBhbmltYXRlPXt7IGhlaWdodDogXCJhdXRvXCIsIG9wYWNpdHk6IDEgfX1cbiAgICAgICAgZXhpdD17eyBoZWlnaHQ6IDAsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBlYXNlOiBcImVhc2VJbk91dFwiIH19XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGZsZXggZmxleC1jb2wgZ2FwLTJcIj5cbiAgICAgICAgICB7bmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHAtMi41IHRleHQtc20gZm9udC1tZWRpdW0gY3Vyc29yLXBvaW50ZXIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGlzQWN0aXZlUm91dGUoaXRlbS5yb3V0ZSlcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1ibGFjayB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIDogXCJob3ZlcjpiZy1ncmF5LTIwMFwiXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOYXZJdGVtQ2xpY2soaXRlbSl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz17aXNBY3RpdmVSb3V0ZShpdGVtLnJvdXRlKSA/IGl0ZW0uYWN0aXZlSWNvbiA6IGl0ZW0uaWNvbn1cbiAgICAgICAgICAgICAgICBhbHQ9e2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgd2lkdGg9ezIwfVxuICAgICAgICAgICAgICAgIGhlaWdodD17MjB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtpc0FjdGl2ZVJvdXRlKGl0ZW0ucm91dGUpID8gXCJpbnZlcnRcIiA6IFwiXCJ9IGgtYXV0b2B9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICAgIHshaGlkZUNyZWF0ZSAmJiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC40IH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0yLjUgdGV4dC1zbSBmb250LW1lZGl1bSBjdXJzb3ItcG9pbnRlciByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcmVhdGVUb2tlbkNsaWNrfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3QoJ2hlYWRlci5jcmVhdGVZb3VyVG9rZW4nKX1cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC40NSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMi41IHRleHQtc20gZm9udC1tZWRpdW0gY3Vyc29yLXBvaW50ZXIgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTIwMFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlUGVya0NsaWNrfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3QoJ2hlYWRlci5jcmVhdGVQZXJrJyl9XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTMwMCBtdC0xIHB0LTJcIj5cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjUgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgeyFhdXRoZW50aWNhdGVkID8gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2xvZ2lufVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0yLjUgdGV4dC1zbSBmb250LW1lZGl1bSBjdXJzb3ItcG9pbnRlciBiZy1ibGFjayB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgbS0xIGhvdmVyOmJnLWdyYXktODAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dCgnYXV0aC5sb2dpbicpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMi41IHRleHQtc20gZm9udC1tZWRpdW0gY3Vyc29yLXBvaW50ZXIgYmctYmxhY2sgdGV4dC13aGl0ZSByb3VuZGVkLWxnIG0tMSBob3ZlcjpiZy1ncmF5LTgwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2dvdXR9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3QoJ25hdmlnYXRpb24ubG9nb3V0Jyl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tb3Rpb24uZGl2PlxuICAgIClcbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IE1vYmlsZU1lbnU7ICJdLCJuYW1lcyI6WyJtb3Rpb24iLCJJbWFnZSIsIlJlYWN0IiwiUk9VVEVTIiwidXNlVHJhbnNsYXRpb24iLCJNb2JpbGVNZW51IiwibWVudVJlZiIsImlzQWN0aXZlUm91dGUiLCJoYW5kbGVOYXZJdGVtQ2xpY2siLCJoaWRlQ3JlYXRlIiwiaGFuZGxlQ3JlYXRlVG9rZW5DbGljayIsImhhbmRsZUNyZWF0ZVBlcmtDbGljayIsImF1dGhlbnRpY2F0ZWQiLCJsb2dpbiIsImxvZ291dCIsInQiLCJpc1JlYWR5IiwibmF2aWdhdGlvbkl0ZW1zIiwibGFiZWwiLCJyb3V0ZSIsIkRBU0hCT0FSRCIsImljb24iLCJhY3RpdmVJY29uIiwicmVxdWlyZXNBdXRoIiwiUEVSS1MiLCJIT01FIiwiZGl2IiwicmVmIiwiY2xhc3NOYW1lIiwiaW5pdGlhbCIsImhlaWdodCIsIm9wYWNpdHkiLCJhbmltYXRlIiwiZXhpdCIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImVhc2UiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJ4IiwiZGVsYXkiLCJvbkNsaWNrIiwic3JjIiwiYWx0Iiwid2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/header/MobileMenu.tsx\n");

/***/ })

};
;