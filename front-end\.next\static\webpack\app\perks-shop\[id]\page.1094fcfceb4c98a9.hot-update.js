"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(signedTransaction.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(escrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(escrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price\n                });\n                // Use trade data to get the numeric escrowId\n                // First try to get it from the extra field, then fall back to original escrowId\n                let numericEscrowId = null;\n                try {\n                    if (tradeData.extra) {\n                        const extraData = JSON.parse(tradeData.extra);\n                        numericEscrowId = extraData.escrowId;\n                    }\n                } catch (e) {\n                    console.log('Could not parse extra data:', e);\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                calculatedPerkMint = ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9lc2Nyb3cudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUM4SDtBQUMzSTtBQUNOO0FBQ2E7QUFDUjtBQUUzQyw2Q0FBNkM7QUFDdEMsTUFBTVUsb0JBQW9CRCxvREFBUUE7SUFDdkMsWUFBWUUsT0FBZSxFQUFFQyxPQUFlLGNBQWMsRUFBRUMsU0FBaUIsR0FBRyxDQUFFO1FBQ2hGLEtBQUssQ0FBQ0YsU0FBU0MsTUFBTUM7UUFDckIsSUFBSSxDQUFDQyxJQUFJLEdBQUc7SUFDZDtBQUNGO0FBRU8sTUFBTUMseUJBQXlCTDtJQUNwQyxZQUFZQyxPQUFlLEVBQUUsSUFBb0IsQ0FBRTtRQUNqRCxLQUFLLENBQUNBLFNBQVMscUJBQXFCLFdBREZLLE9BQUFBO1FBRWxDLElBQUksQ0FBQ0YsSUFBSSxHQUFHO0lBQ2Q7QUFDRjtBQUVPLE1BQU1HLCtCQUErQlA7SUFDMUMsWUFBWUMsVUFBa0IsZ0RBQWdELENBQUU7UUFDOUUsS0FBSyxDQUFDQSxTQUFTLHNCQUFzQjtRQUNyQyxJQUFJLENBQUNHLElBQUksR0FBRztJQUNkO0FBQ0Y7QUFFTyxNQUFNSSxrQ0FBa0NSO0lBQzdDLFlBQVlDLFVBQWtCLDhDQUE4QyxDQUFFO1FBQzVFLEtBQUssQ0FBQ0EsU0FBUyx5QkFBeUI7UUFDeEMsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDZDtBQUNGO0FBRU8sTUFBTUssd0JBQXdCVDtJQUNuQyxZQUFZQyxPQUFlLEVBQUUsYUFBMEIsQ0FBRTtRQUN2RCxLQUFLLENBQUNBLFNBQVMsb0JBQW9CLFdBRERTLGdCQUFBQTtRQUVsQyxJQUFJLENBQUNOLElBQUksR0FBRztJQUNkO0FBQ0Y7QUFxQkEscURBQXFEO0FBQ3JELE1BQU1PLG9CQUFvQixJQUFJQztBQU05QixrRUFBa0U7QUFDbEUsTUFBTUMsMEJBQTBCLElBQUlEO0FBRXBDLHlEQUF5RDtBQUN6RCxNQUFNRSx5QkFBeUI7SUFDN0IsTUFBTUMsaUJBQWlCQyxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLO0lBQzdDLEtBQUssTUFBTSxDQUFDQyxLQUFLQyxNQUFNLElBQUlSLGtCQUFrQlMsT0FBTyxHQUFJO1FBQ3RELElBQUlELE1BQU1FLFNBQVMsR0FBR04sZ0JBQWdCO1lBQ3BDSixrQkFBa0JXLE1BQU0sQ0FBQ0o7UUFDM0I7SUFDRjtBQUNGO0FBRUEsd0RBQXdEO0FBQ3hELE1BQU1LLDJCQUEyQixlQUMvQkMsWUFDQUM7UUFDQUMsOEVBQXFCLEdBQ3JCQyw4RUFBcUI7SUFFckIsSUFBSUM7SUFFSixJQUFLLElBQUlDLFVBQVUsR0FBR0EsV0FBV0gsWUFBWUcsVUFBVztRQUN0RCxJQUFJO1lBQ0ZDLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBcUNMLE9BQVhHLFNBQVEsS0FBYyxPQUFYSDtZQUVqRCxNQUFNcEIsT0FBTyxNQUFNa0IsV0FBV1Esa0JBQWtCLENBQUNQLGtCQUFrQlEsU0FBUyxJQUFJO2dCQUM5RUMsZUFBZTtnQkFDZkMscUJBQXFCO2dCQUNyQlQsWUFBWSxFQUFFLHdEQUF3RDtZQUN4RTtZQUVBSSxRQUFRQyxHQUFHLENBQUMsOENBQXNELE9BQVJGLFNBQVEsTUFBSXZCO1lBQ3RFLE9BQU9BO1FBRVQsRUFBRSxPQUFPOEIsT0FBWTtnQkFLZkEsZ0JBQ0ZBLGlCQUNBQSxpQkFDQUEsaUJBQ0FBO1lBUkZSLFlBQVlRO1lBQ1pOLFFBQVFNLEtBQUssQ0FBQyx5QkFBaUMsT0FBUlAsU0FBUSxhQUFXTyxNQUFNbkMsT0FBTztZQUV2RSxxREFBcUQ7WUFDckQsSUFBSW1DLEVBQUFBLGlCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMscUNBQUFBLGVBQWVDLFFBQVEsQ0FBQyxxREFDMUJELGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsdUNBQ3hCRCxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLGdDQUN4QkQsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyx1QkFDeEJELGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMscUJBQ3hCRCxNQUFNbEMsSUFBSSxLQUFLLFFBQ2ZrQyxNQUFNbEMsSUFBSSxLQUFLLENBQUMsT0FBTztnQkFDdkI0QixRQUFRQyxHQUFHLENBQUU7Z0JBQ2IsTUFBTUs7WUFDUjtZQUVBLDhCQUE4QjtZQUM5QixJQUFJUCxXQUFXSCxZQUFZO2dCQUN6QkksUUFBUUMsR0FBRyxDQUFFO2dCQUNiO1lBQ0Y7WUFFQSx1QkFBdUI7WUFDdkJELFFBQVFDLEdBQUcsQ0FBQyxhQUF3QixPQUFYSixZQUFXO1lBQ3BDLE1BQU0sSUFBSVcsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU1o7WUFDakRBLGNBQWMsS0FBSyxzQkFBc0I7UUFDM0M7SUFDRjtJQUVBLE1BQU1DO0FBQ1I7QUFFQSx1RUFBdUU7QUFDdkUsTUFBTWEsMEJBQTBCLGVBQzlCakIsWUFDQWtCO1FBQ0FDLCtFQUFzQixHQUN0QmhCLDhFQUFxQjtJQUVyQixJQUFLLElBQUlpQixJQUFJLEdBQUdBLElBQUlELGFBQWFDLElBQUs7UUFDcEMsSUFBSTtZQUNGLE1BQU16QyxTQUFTLE1BQU1xQixXQUFXcUIsa0JBQWtCLENBQUNIO1lBRW5ELElBQUl2QyxtQkFBQUEsNkJBQUFBLE9BQVEyQyxLQUFLLEVBQUU7Z0JBQ2pCLE1BQU1DLHFCQUFxQjVDLE9BQU8yQyxLQUFLLENBQUNDLGtCQUFrQjtnQkFFMUQsSUFBSUEsdUJBQXVCLGVBQWVBLHVCQUF1QixhQUFhO29CQUM1RSxJQUFJNUMsT0FBTzJDLEtBQUssQ0FBQ0UsR0FBRyxFQUFFO3dCQUNwQixPQUFPOzRCQUNMQyxTQUFTOzRCQUNUYixPQUFPLHVCQUF3RCxPQUFqQ2MsS0FBS0MsU0FBUyxDQUFDaEQsT0FBTzJDLEtBQUssQ0FBQ0UsR0FBRzs0QkFDN0REO3dCQUNGO29CQUNGO29CQUNBLE9BQU87d0JBQUVFLFNBQVM7d0JBQU1GO29CQUFtQjtnQkFDN0M7Z0JBRUEsa0NBQWtDO2dCQUNsQyxJQUFJSCxJQUFJRCxjQUFjLEdBQUc7b0JBQ3ZCYixRQUFRQyxHQUFHLENBQUMsZUFBc0RhLE9BQXZDRixXQUFVLCtCQUFzQ0MsT0FBVEMsSUFBSSxHQUFFLEtBQWUsT0FBWkQ7b0JBQzNFLE1BQU0sSUFBSUwsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU1o7b0JBQ2pEO2dCQUNGO1lBQ0Y7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSWlCLElBQUlELGNBQWMsR0FBRztnQkFDdkJiLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBNkRhLE9BQXRCRixXQUFVLGNBQXFCQyxPQUFUQyxJQUFJLEdBQUUsS0FBZSxPQUFaRDtnQkFDbEYsTUFBTSxJQUFJTCxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTWjtZQUNuRDtRQUVGLEVBQUUsT0FBT1MsT0FBTztZQUNkTixRQUFRQyxHQUFHLENBQUMsV0FBaUIsT0FBTmEsSUFBSSxHQUFFLHlDQUF1Q1I7WUFDcEUsSUFBSVEsTUFBTUQsY0FBYyxHQUFHO2dCQUN6QixPQUFPO29CQUFFTSxTQUFTO29CQUFPYixPQUFPLHVDQUE2QyxPQUFOQTtnQkFBUTtZQUNqRjtZQUNBLE1BQU0sSUFBSUUsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU1o7UUFDbkQ7SUFDRjtJQUVBLE9BQU87UUFBRXNCLFNBQVM7UUFBT2IsT0FBTztJQUFxQztBQUN2RTtBQUVBLG1DQUFtQztBQUNuQyxNQUFNZ0Isd0JBQXdCLE9BQU81QjtJQUNuQyxJQUFJO1FBQ0YsTUFBTTZCLFlBQVlyQyxLQUFLQyxHQUFHO1FBQzFCLE1BQU1xQyxPQUFPLE1BQU05QixXQUFXK0IsT0FBTztRQUNyQyxNQUFNQyxlQUFleEMsS0FBS0MsR0FBRyxLQUFLb0M7UUFFbEMsSUFBSUcsZUFBZSxPQUFPO1lBQ3hCLE9BQU87Z0JBQUVDLFNBQVM7Z0JBQU9yQixPQUFPO1lBQW9DO1FBQ3RFO1FBRUEsSUFBSSxPQUFPa0IsU0FBUyxZQUFZQSxRQUFRLEdBQUc7WUFDekMsT0FBTztnQkFBRUcsU0FBUztnQkFBT3JCLE9BQU87WUFBd0M7UUFDMUU7UUFFQSxPQUFPO1lBQUVxQixTQUFTO1FBQUs7SUFDekIsRUFBRSxPQUFPckIsT0FBTztRQUNkLE9BQU87WUFBRXFCLFNBQVM7WUFBT3JCLE9BQU8sbUNBQXlDLE9BQU5BO1FBQVE7SUFDN0U7QUFDRjtBQUVBLDZCQUE2QjtBQUM3QixNQUFNc0IsMkJBQTJCLENBQUNDO0lBQ2hDLElBQUksQ0FBQ0EsUUFBUTtRQUNYLE9BQU87WUFBRUMsT0FBTztZQUFPeEIsT0FBTztRQUFrRTtJQUNsRztJQUVBLElBQUksT0FBT3VCLE9BQU9FLGVBQWUsS0FBSyxZQUFZO1FBQ2hELE9BQU87WUFBRUQsT0FBTztZQUFPeEIsT0FBTztRQUErRTtJQUMvRztJQUVBLElBQUksQ0FBQ3VCLE9BQU9HLE9BQU8sRUFBRTtRQUNuQixPQUFPO1lBQUVGLE9BQU87WUFBT3hCLE9BQU87UUFBOEQ7SUFDOUY7SUFFQSxrREFBa0Q7SUFDbEQsSUFBSTtRQUNGLElBQUk5QyxzREFBU0EsQ0FBQ3FFLE9BQU9HLE9BQU87SUFDOUIsRUFBRSxPQUFPMUIsT0FBTztRQUNkLE9BQU87WUFBRXdCLE9BQU87WUFBT3hCLE9BQU87UUFBaUM7SUFDakU7SUFFQSxPQUFPO1FBQUV3QixPQUFPO0lBQUs7QUFDdkI7QUFjQTs7Q0FFQyxHQUNNLGVBQWVHLDJCQUNwQkMsWUFBMEIsRUFDMUJMLE1BQVc7SUFFWCxJQUFJO1FBQ0YsNkJBQTZCO1FBQzdCLE1BQU1NLG1CQUFtQlAseUJBQXlCQztRQUNsRCxJQUFJLENBQUNNLGlCQUFpQkwsS0FBSyxFQUFFO1lBQzNCLE1BQU0sSUFBSTVELFlBQVlpRSxpQkFBaUI3QixLQUFLLEVBQUc7UUFDakQ7UUFFQSxtQ0FBbUM7UUFDbkMsSUFBSSxDQUFDNEIsY0FBYztZQUNqQixNQUFNLElBQUloRSxZQUFZLHVDQUF1QztRQUMvRDtRQUVBLElBQUksQ0FBQ2dFLGFBQWFFLFFBQVEsRUFBRTtZQUMxQixNQUFNLElBQUlsRSxZQUFZLHlCQUF5QjtRQUNqRDtRQUVBLElBQUksQ0FBQ2dFLGFBQWFHLFNBQVMsSUFBSUMsT0FBT0osYUFBYUcsU0FBUyxLQUFLLEdBQUc7WUFDbEUsTUFBTSxJQUFJbkUsWUFBWSxnQ0FBZ0M7UUFDeEQ7UUFFQSw0Q0FBNEM7UUFDNUMsTUFBTXdCLGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7UUFDOUMsTUFBTThDLG1CQUFtQixNQUFNbEIsc0JBQXNCNUI7UUFDckQsSUFBSSxDQUFDOEMsaUJBQWlCYixPQUFPLEVBQUU7WUFDN0IsTUFBTSxJQUFJekQsWUFBWSw2QkFBb0QsT0FBdkJzRSxpQkFBaUJsQyxLQUFLLEdBQUk7UUFDL0U7UUFFQSxzREFBc0Q7UUFDdEQsTUFBTW1DLGlCQUFpQixHQUE0QlosT0FBekJLLGFBQWFFLFFBQVEsRUFBQyxLQUFrQixPQUFmUCxPQUFPRyxPQUFPLEVBQUM7UUFFbEUsd0RBQXdEO1FBQ3hELE1BQU1VLGNBQWMzRCx3QkFBd0I0RCxHQUFHLENBQUNGO1FBQ2hELE1BQU10RCxNQUFNRCxLQUFLQyxHQUFHO1FBQ3BCLElBQUl1RCxlQUFlLE1BQU9BLGNBQWUsTUFBTTtZQUM3QyxNQUFNLElBQUloRSwwQkFBMEI7UUFDdEM7UUFDQUssd0JBQXdCNkQsR0FBRyxDQUFDSCxnQkFBZ0J0RDtRQUU1Qyx5Q0FBeUM7UUFDekMsSUFBSTBELEtBQUtDLE1BQU0sS0FBSyxLQUFLO1lBQ3ZCLE1BQU03RCxpQkFBaUJDLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUs7WUFDN0MsS0FBSyxNQUFNLENBQUNDLEtBQUtDLE1BQU0sSUFBSVIsa0JBQWtCUyxPQUFPLEdBQUk7Z0JBQ3RELElBQUlELE1BQU1FLFNBQVMsR0FBR04sZ0JBQWdCO29CQUNwQ0osa0JBQWtCVyxNQUFNLENBQUNKO2dCQUMzQjtZQUNGO1lBQ0EsNkJBQTZCO1lBQzdCLEtBQUssTUFBTSxDQUFDQSxLQUFLRyxVQUFVLElBQUlSLHdCQUF3Qk8sT0FBTyxHQUFJO2dCQUNoRSxJQUFJQyxZQUFZTixnQkFBZ0I7b0JBQzlCRix3QkFBd0JTLE1BQU0sQ0FBQ0o7Z0JBQ2pDO1lBQ0Y7UUFDRjtRQUVBLDJEQUEyRDtRQUMzRCxNQUFNMkQsZ0JBQWdCbEUsa0JBQWtCOEQsR0FBRyxDQUFDRjtRQUM1QyxJQUFJTSxlQUFlO1lBQ2pCLElBQUlBLGNBQWMxRSxNQUFNLEtBQUssV0FBVztnQkFDdEMsTUFBTSxJQUFJMkUsTUFBTTtZQUNsQjtZQUNBLElBQUlELGNBQWMxRSxNQUFNLEtBQUssZUFBZTBFLGNBQWN2RSxJQUFJLEVBQUU7Z0JBQzlELGlFQUFpRTtnQkFDakUsTUFBTVMsaUJBQWlCQyxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLO2dCQUM3QyxJQUFJNEQsY0FBY3hELFNBQVMsR0FBR04sZ0JBQWdCO29CQUM1Q2UsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQzhDLGNBQWN2RSxJQUFJO29CQUNwRSxPQUFPdUUsY0FBY3ZFLElBQUk7Z0JBQzNCO1lBQ0Y7UUFDRjtRQUVBLHNGQUFzRjtRQUN0RixJQUFJO1lBQ0Z3QixRQUFRQyxHQUFHLENBQUMscURBQXFEaUMsYUFBYUUsUUFBUTtZQUN0RixNQUFNMUMsYUFBYTNCLCtDQUFPQSxDQUFDd0UsUUFBUSxDQUFDN0MsVUFBVTtZQUM5QyxNQUFNdUQsY0FBYyxJQUFJekYsc0RBQVNBLENBQUNxRSxPQUFPRyxPQUFPO1lBRWhELDJEQUEyRDtZQUMzRCxNQUFNa0IsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ29FLGFBQWFFLFFBQVE7WUFDdEQsTUFBTSxDQUFDZ0IsWUFBWSxHQUFHNUYsc0RBQVNBLENBQUM2RixzQkFBc0IsQ0FDcEQ7Z0JBQUNDLE1BQU1BLENBQUNDLElBQUksQ0FBQztnQkFBYUwsV0FBV00sV0FBVyxDQUFDRixNQUFNQSxFQUFFLE1BQU07YUFBRyxFQUNsRXZGLCtDQUFPQSxDQUFDMEYsU0FBUztZQUduQiwrQ0FBK0M7WUFDL0MsTUFBTUMsa0JBQWtCLE1BQU1oRSxXQUFXaUUsY0FBYyxDQUFDUDtZQUN4RCxJQUFJTSxpQkFBaUI7Z0JBQ25CMUQsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLDBEQUEwRDtnQkFDMUQsTUFBTTJELGFBQWEsTUFBTWxFLFdBQVdtRSx1QkFBdUIsQ0FBQ1osYUFBYTtvQkFBRWEsT0FBTztnQkFBRztnQkFDckYsS0FBSyxNQUFNQyxPQUFPSCxXQUFZO29CQUM1Qiw2REFBNkQ7b0JBQzdELE1BQU1JLGdCQUFnQjlFLEtBQUtDLEdBQUcsS0FBSyxLQUFLLEtBQUs7b0JBQzdDLElBQUk0RSxJQUFJRSxTQUFTLElBQUlGLElBQUlFLFNBQVMsR0FBRyxPQUFPRCxlQUFlO3dCQUN6RCxpQ0FBaUM7d0JBQ2pDbkYsa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7NEJBQ3BDcEUsUUFBUTs0QkFDUmtCLFdBQVdMLEtBQUtDLEdBQUc7NEJBQ25CWCxNQUFNdUYsSUFBSW5ELFNBQVM7d0JBQ3JCO3dCQUNBLE9BQU9tRCxJQUFJbkQsU0FBUztvQkFDdEI7Z0JBQ0Y7Z0JBRUEsNkRBQTZEO2dCQUM3RCxNQUFNc0Qsa0JBQWtCLFlBQWtDLE9BQXRCaEMsYUFBYUUsUUFBUTtnQkFDekR2RCxrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjtvQkFDcENwRSxRQUFRO29CQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztvQkFDbkJYLE1BQU0wRjtnQkFDUjtnQkFDQSxPQUFPQTtZQUNUO1FBQ0YsRUFBRSxPQUFPQyxZQUFZO1lBQ25CbkUsUUFBUUMsR0FBRyxDQUFDLGtEQUFrRGtFO1FBQzlELDJDQUEyQztRQUM3QztRQUVBLDhCQUE4QjtRQUM5QnRGLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztRQUNyQjtRQUVBLE1BQU0sRUFDSmlELFFBQVEsRUFDUkMsU0FBUyxFQUNUK0IsZUFBZSxFQUNmaEIsV0FBVyxFQUNYaUIsUUFBUSxFQUNSQyxpQkFBaUIsRUFDakJDLGFBQWEsRUFDYkMsV0FBVyxFQUNaLEdBQUd0QztRQUVKbEMsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2lDO1FBRWhELG9DQUFvQztRQUNwQyxNQUFNdUMsY0FBY0MsT0FBT3RDO1FBQzNCLElBQUksQ0FBQyxRQUFRdUMsSUFBSSxDQUFDRixjQUFjO1lBQzlCLE1BQU0sSUFBSXZHLFlBQVksNkJBQXNDLE9BQVRrRSxVQUFTLDhCQUE0QjtRQUMxRjtRQUVBLHlDQUF5QztRQUN6QyxNQUFNd0MsZUFBZSxNQUFNbEYsV0FBV21GLFVBQVUsQ0FBQyxJQUFJckgsc0RBQVNBLENBQUNnSDtRQUMvRHhFLFFBQVFDLEdBQUcsQ0FBQyw2QkFBa0UyRSxPQUFyQ0EsZUFBZSxZQUFjLFVBQXFCLE9BQWJBLGNBQWE7UUFDM0Y1RSxRQUFRQyxHQUFHLENBQUMsb0JBQThEb0MsT0FBMUNDLE9BQU9ELGFBQWEsWUFBYyxVQUFrQixPQUFWQSxXQUFVO1FBRXBGLGlCQUFpQjtRQUNqQixNQUFNYSxhQUFhLElBQUlwRixpREFBUyxDQUFDMkc7UUFDakMsTUFBTUssY0FBYyxJQUFJaEgsaURBQVMsQ0FBQ3VFO1FBQ2xDLE1BQU0wQyxvQkFBb0IsSUFBSWpILGlEQUFTLENBQUNzRztRQUV4QyxxQkFBcUI7UUFDckIsTUFBTW5CLGNBQWMsSUFBSXpGLHNEQUFTQSxDQUFDZ0g7UUFDbEMsTUFBTVEsMEJBQTBCLElBQUl4SCxzREFBU0EsQ0FBQzhHO1FBQzlDLE1BQU1XLHNCQUFzQixJQUFJekgsc0RBQVNBLENBQUMrRztRQUMxQyxNQUFNVyxvQkFBb0IsSUFBSTFILHNEQUFTQSxDQUFDNEY7UUFDeEMsTUFBTStCLGlCQUFpQixJQUFJM0gsc0RBQVNBLENBQUM2RztRQUVyQyw0QkFBNEI7UUFDNUIsTUFBTWUsb0JBQW9CM0gsZ0ZBQTZCQSxDQUNyRHVILHlCQUNBL0I7UUFHRixtREFBbUQ7UUFDbkQsTUFBTW9DLGVBQWUsTUFBTTNGLFdBQVdpRSxjQUFjLENBQUN5QjtRQUVyRCxJQUFJRSxrQkFBa0IsRUFBRTtRQUV4QiwrQkFBK0I7UUFDL0IsTUFBTUMsZUFBZVAsd0JBQXdCUSxNQUFNLENBQUMsSUFBSWhJLHNEQUFTQSxDQUFDO1FBRWxFLElBQUksQ0FBQzZILGNBQWM7WUFDakJyRixRQUFRQyxHQUFHLENBQUMsMkJBQTJCbUYsa0JBQWtCSyxRQUFRO1lBRWpFLDhDQUE4QztZQUM5QyxNQUFNQyxjQUFjOUgsMEZBQXVDQSxDQUN6RHFGLGFBQ0FtQyxtQkFDQW5DLGFBQ0ErQix3QkFBd0IsT0FBTzs7WUFHakNNLGdCQUFnQkssSUFBSSxDQUFDRDtRQUN2QjtRQUVBLHdFQUF3RTtRQUN4RSxJQUFJSCxjQUFjO1lBQ2hCdkYsUUFBUUMsR0FBRyxDQUFDO1lBRVosNEVBQTRFO1lBQzVFLG9DQUFvQztZQUNwQyxNQUFNMkYsaUJBQWlCdEQsT0FBT0Q7WUFDOUIsTUFBTXdELGdCQUFnQixNQUFNbkcsV0FBV29HLGlDQUFpQyxDQUFDLE1BQU0scUJBQXFCO1lBQ3BHLE1BQU1DLG9CQUFvQkgsaUJBQWlCQztZQUUzQzdGLFFBQVFDLEdBQUcsQ0FBQyx5QkFBd0MsT0FBZjJGLGdCQUFlO1lBQ3BENUYsUUFBUUMsR0FBRyxDQUFDLG1CQUFpQyxPQUFkNEYsZUFBYztZQUM3QzdGLFFBQVFDLEdBQUcsQ0FBQyx3QkFBMEMsT0FBbEI4RixtQkFBa0I7WUFFdEQscUNBQXFDO1lBQ3JDLElBQUluQixlQUFlbUIsb0JBQW9CLE1BQU07Z0JBQzNDLE1BQU0sSUFBSS9DLE1BQU0sdUNBQXFHNEIsT0FBOUQsQ0FBQ21CLG9CQUFvQixJQUFHLElBQUssWUFBYyxxQkFBZ0QsT0FBN0JuQixlQUFlLFlBQWM7WUFDcEo7WUFFQSw0REFBNEQ7WUFDNUQsTUFBTW9CLGFBQWFsSSxpRUFBeUIsQ0FBQ3FJLFFBQVEsQ0FBQztnQkFDcERDLFlBQVluRDtnQkFDWm9ELFVBQVVqQjtnQkFDVmtCLFVBQVVQO1lBQ1o7WUFFQVQsZ0JBQWdCSyxJQUFJLENBQUNLO1lBRXJCLHdFQUF3RTtZQUN4RSxNQUFNTyxTQUFTMUksOEVBQTJCQSxDQUFDdUg7WUFDM0NFLGdCQUFnQkssSUFBSSxDQUFDWTtZQUVyQnZHLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU11RyxLQUFLLE1BQU16SSwrQ0FBT0EsQ0FBQzBJLE9BQU8sQ0FDN0JDLFNBQVMsQ0FBQ3hELFlBQVk0QixhQUFhQyxtQkFDbkM0QixRQUFRLENBQUM7WUFDUkMsUUFBUTNEO1lBQ1JxQixtQkFBbUJVO1lBQ25CVCxlQUFlVTtZQUNmNEIsa0JBQWtCekI7WUFDbEIwQixVQUFVNUI7WUFDVjZCLE9BQU81QjtZQUNQNkIsd0JBQXdCdEosMEVBQTJCQTtZQUNuRHVKLGNBQWN0SiwrREFBZ0JBO1lBQzlCdUosZUFBZXBKLGlFQUF5QixDQUFDMkYsU0FBUztRQUNwRCxHQUNDNkIsZUFBZSxDQUFDQSxpQkFDaEI2QixXQUFXO1FBRWQsdURBQXVEO1FBQ3ZELE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxvQkFBb0IsRUFBRSxHQUFHLE1BQU0zSCxXQUFXNEgsa0JBQWtCLENBQUM7UUFDaEZkLEdBQUdlLGVBQWUsR0FBR0g7UUFDckJaLEdBQUdnQixRQUFRLEdBQUd2RTtRQUVkLGtFQUFrRTtRQUNsRSxNQUFNd0Usa0JBQWtCLElBQUkzSiwwRUFBa0MsQ0FBQztZQUM3RDZKLE1BQU0sRUFBRTtZQUNSbEUsV0FBVyxJQUFJakcsc0RBQVNBLENBQUM7WUFDekJvSyxNQUFNdEUsTUFBTUEsQ0FBQ0MsSUFBSSxDQUFDLGNBQXVDckUsT0FBekJnRCxhQUFhRSxRQUFRLEVBQUMsS0FBYyxPQUFYbEQsS0FBS0MsR0FBRyxLQUFNO1FBQ3pFO1FBQ0FxSCxHQUFHcUIsR0FBRyxDQUFDSjtRQUVQekgsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQjtZQUNyQ21DLFVBQVVGLGFBQWFFLFFBQVE7WUFDL0JnRixXQUFXQSxVQUFVVSxLQUFLLENBQUMsR0FBRyxLQUFLO1lBQ25DVDtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVcsTUFBTWxHLE9BQU9FLGVBQWUsQ0FBQ3lFO1FBRTlDeEcsUUFBUUMsR0FBRyxDQUFDO1FBRVosd0NBQXdDO1FBQ3hDLE1BQU16QixPQUFPLE1BQU1pQix5QkFBeUJDLFlBQVlxSSxVQUFVLEdBQUc7UUFFckUvSCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCekI7UUFFbkMsdUNBQXVDO1FBQ3ZDLE1BQU13SixlQUFlLE1BQU10SSxXQUFXdUksa0JBQWtCLENBQUM7WUFDdkRySCxXQUFXcEM7WUFDWDRJLFdBQVdaLEdBQUdlLGVBQWU7WUFDN0JGLHNCQUFzQkE7UUFDeEIsR0FBRztRQUVILElBQUlXLGFBQWFoSCxLQUFLLENBQUNFLEdBQUcsRUFBRTtZQUMxQixNQUFNLElBQUk4QixNQUFNLHVCQUE4QyxPQUF2QmdGLGFBQWFoSCxLQUFLLENBQUNFLEdBQUc7UUFDL0Q7UUFFQWxCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUN6QjtRQUVuRCxnQ0FBZ0M7UUFDaENLLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNuQlg7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNLElBQUlnQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBRWpELE9BQU9qQztJQUVULEVBQUUsT0FBTzhCLE9BQVk7WUFLZEEsZ0JBbUJEQSxpQkF3REFBLGlCQUlBQSxpQkFBZ0RBLGlCQUloREEsaUJBQWtEQSxpQkFJbERBLGlCQUE0Q0EsaUJBSTVDQSxpQkFBNENBLGtCQUk1Q0E7UUFuR0pOLFFBQVFNLEtBQUssQ0FBQyxrQ0FBa0NBO1FBRWhELCtEQUErRDtRQUMvRCx1Q0FBdUM7UUFDdkMsSUFBSSxHQUFDQSxpQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHFDQUFBQSxlQUFlQyxRQUFRLENBQUMsaURBQWdEO1lBQzNFLE1BQU1rQyxpQkFBaUIsR0FBNEJaLE9BQXpCSyxhQUFhRSxRQUFRLEVBQUMsS0FBa0IsT0FBZlAsT0FBT0csT0FBTyxFQUFDO1lBQ2xFbkQsa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7Z0JBQ3BDcEUsUUFBUTtnQkFDUmtCLFdBQVdMLEtBQUtDLEdBQUc7WUFDckI7UUFDRjtRQUVBLGtEQUFrRDtRQUNsRCxJQUFJbUIsTUFBTTRILElBQUksRUFBRTtZQUNkbEksUUFBUU0sS0FBSyxDQUFDLHFCQUFxQkEsTUFBTTRILElBQUk7UUFDL0M7UUFFQSw4Q0FBOEM7UUFDOUMsSUFBSTVILGlCQUFpQnBDLGFBQWE7WUFDaEMsTUFBTW9DO1FBQ1I7UUFFQSwyREFBMkQ7UUFDM0QsS0FBSUEsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyxnREFBZ0Q7WUFDMUUsTUFBTWtDLGlCQUFpQixHQUE0QlosT0FBekJLLGFBQWFFLFFBQVEsRUFBQyxLQUFrQixPQUFmUCxPQUFPRyxPQUFPLEVBQUM7WUFFbEVoQyxRQUFRQyxHQUFHLENBQUM7WUFFWiwwRkFBMEY7WUFDMUYsSUFBSTtnQkFDRixNQUFNUCxhQUFhM0IsK0NBQU9BLENBQUN3RSxRQUFRLENBQUM3QyxVQUFVO2dCQUM5QyxNQUFNd0QsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ29FLGFBQWFFLFFBQVE7Z0JBQ3RELE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO29CQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7b0JBQWFMLFdBQVdNLFdBQVcsQ0FBQ0YsTUFBTUEsRUFBRSxNQUFNO2lCQUFHLEVBQ2xFdkYsK0NBQU9BLENBQUMwRixTQUFTO2dCQUduQixNQUFNQyxrQkFBa0IsTUFBTWhFLFdBQVdpRSxjQUFjLENBQUNQO2dCQUN4RCxJQUFJTSxpQkFBaUI7b0JBQ25CMUQsUUFBUUMsR0FBRyxDQUFDO29CQUVaLDBEQUEwRDtvQkFDMUQsTUFBTWdELGNBQWMsSUFBSXpGLHNEQUFTQSxDQUFDcUUsT0FBT0csT0FBTztvQkFDaEQsTUFBTTRCLGFBQWEsTUFBTWxFLFdBQVdtRSx1QkFBdUIsQ0FBQ1osYUFBYTt3QkFBRWEsT0FBTztvQkFBRztvQkFFckYsSUFBSXFFLFlBQVk7b0JBQ2hCLEtBQUssTUFBTXBFLE9BQU9ILFdBQVk7d0JBQzVCLDREQUE0RDt3QkFDNUQsTUFBTTNFLGlCQUFpQkMsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSzt3QkFDN0MsSUFBSTRFLElBQUlFLFNBQVMsSUFBSUYsSUFBSUUsU0FBUyxHQUFHLE9BQU9oRixnQkFBZ0I7NEJBQzFEa0osWUFBWXBFLElBQUluRCxTQUFTOzRCQUN6Qjt3QkFDRjtvQkFDRjtvQkFFQSxJQUFJLENBQUN1SCxXQUFXO3dCQUNkQSxZQUFZLGNBQXVDakosT0FBekJnRCxhQUFhRSxRQUFRLEVBQUMsS0FBYyxPQUFYbEQsS0FBS0MsR0FBRztvQkFDN0Q7b0JBRUEsaUNBQWlDO29CQUNqQ04sa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7d0JBQ3BDcEUsUUFBUTt3QkFDUmtCLFdBQVdMLEtBQUtDLEdBQUc7d0JBQ25CWCxNQUFNMko7b0JBQ1I7b0JBRUFuSSxRQUFRQyxHQUFHLENBQUMsMkNBQTJDa0k7b0JBQ3ZELE9BQU9BO2dCQUNULE9BQU87b0JBQ0xuSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixFQUFFLE9BQU9rRSxZQUFZO2dCQUNuQm5FLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NrRTtZQUNwRDtZQUVBLE1BQU0sSUFBSXpGLDBCQUEwQjtRQUN0QztRQUVBLDJDQUEyQztRQUMzQyxLQUFJNEIsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQywwQkFBMEI7WUFDcEQsTUFBTSxJQUFJckMsWUFBWSw4RUFBOEU7UUFDdEc7UUFFQSxJQUFJb0MsRUFBQUEsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQywyQkFBd0JELGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsd0JBQXVCO1lBQ2pHLE1BQU0sSUFBSTlCLHVCQUF1QjtRQUNuQztRQUVBLElBQUk2QixFQUFBQSxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLDZCQUEwQkQsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyx5QkFBd0I7WUFDcEcsTUFBTSxJQUFJaEMsaUJBQWlCO1FBQzdCO1FBRUEsSUFBSStCLEVBQUFBLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsdUJBQW9CRCxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLHFCQUFvQkQsTUFBTWxDLElBQUksS0FBSyxNQUFNO1lBQy9HLE1BQU0sSUFBSUYsWUFBWSxvQ0FBb0M7UUFDNUQ7UUFFQSxJQUFJb0MsRUFBQUEsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyx1QkFBb0JELG1CQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsdUNBQUFBLGlCQUFlQyxRQUFRLENBQUMscUJBQW9CO1lBQzFGLE1BQU0sSUFBSXJDLFlBQVksbUZBQW1GO1FBQzNHO1FBRUEsS0FBSW9DLG1CQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsdUNBQUFBLGlCQUFlQyxRQUFRLENBQUMsc0JBQXNCO1lBQ2hELE1BQU0sSUFBSWhDLGlCQUFpQjtRQUM3QjtRQUVBLG9CQUFvQjtRQUNwQixJQUFJK0IsTUFBTWxDLElBQUksSUFBSSxPQUFPa0MsTUFBTWxDLElBQUksS0FBSyxVQUFVO1lBQ2hELElBQUlrQyxNQUFNbEMsSUFBSSxLQUFLLENBQUMsT0FBTztnQkFDekIsTUFBTSxJQUFJRixZQUFZLHlDQUF5QztZQUNqRTtZQUNBLElBQUlvQyxNQUFNbEMsSUFBSSxLQUFLLENBQUMsT0FBTztnQkFDekIsTUFBTSxJQUFJTSwwQkFBMEI7WUFDdEM7UUFDRjtRQUVBLDJCQUEyQjtRQUMzQixJQUFJNEIsTUFBTWhDLElBQUksS0FBSywwQkFBMEJnQyxNQUFNaEMsSUFBSSxLQUFLLG9CQUFvQjtZQUM5RSxNQUFNLElBQUlLLGdCQUFnQixrQ0FBZ0QsT0FBZDJCLE1BQU1uQyxPQUFPLEdBQUltQztRQUMvRTtRQUVBLDhCQUE4QjtRQUM5QixNQUFNLElBQUlwQyxZQUFZb0MsTUFBTW5DLE9BQU8sSUFBSSw4REFBOEQ7SUFDdkc7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZWlLLDhCQUNwQmhHLFFBQWdCLEVBQ2hCaUcsbUJBQTJCLEVBQzNCakUsZUFBdUIsRUFDdkJFLGlCQUF5QixFQUN6QkMsYUFBcUIsRUFDckIrRCxZQUFvQixFQUNwQnpHLE1BQVcsRUFDWDBHLE9BQXlCO0lBRXpCLElBQUk7UUFDRiw2QkFBNkI7UUFDN0IsTUFBTXBHLG1CQUFtQlAseUJBQXlCQztRQUNsRCxJQUFJLENBQUNNLGlCQUFpQkwsS0FBSyxFQUFFO1lBQzNCLE1BQU0sSUFBSTVELFlBQVlpRSxpQkFBaUI3QixLQUFLLEVBQUc7UUFDakQ7UUFFQSxvRUFBb0U7UUFDcEUsSUFBSWtJLDJCQUEyQkg7UUFDL0IsSUFBSUksdUJBQXVCckU7UUFDM0IsSUFBSXNFLHlCQUF5QnBFO1FBQzdCLElBQUlxRSxxQkFBcUJwRTtRQUN6QixJQUFJcUUscUJBQXFCeEc7UUFFekIsSUFBSW1HLFNBQVM7WUFDWCxJQUFJO29CQW1DZ0JNLHdCQU1HQSw4QkFBQUE7Z0JBeENyQjdJLFFBQVFDLEdBQUcsQ0FBQyxzREFBc0RzSTtnQkFDbEUsTUFBTSxFQUFFTyxlQUFlLEVBQUUsR0FBRyxNQUFNLDRKQUEyQjtnQkFDN0QsTUFBTUMsZ0JBQWdCLE1BQU1ELGdCQUFnQnhHLE9BQU9pRztnQkFDbkQsTUFBTU0sWUFBWUUsY0FBY25CLElBQUk7Z0JBRXBDNUgsUUFBUUMsR0FBRyxDQUFDLHFEQUFxRDtvQkFDL0RzSSxTQUFTTSxVQUFVRyxFQUFFO29CQUNyQjNLLFFBQVF3SyxVQUFVeEssTUFBTTtvQkFDeEI0SyxZQUFZSixVQUFVSSxVQUFVO29CQUNoQ0MsUUFBUUwsVUFBVUssTUFBTTtvQkFDeEJDLE9BQU9OLFVBQVVNLEtBQUs7Z0JBQ3hCO2dCQUVBLDZDQUE2QztnQkFDN0MsZ0ZBQWdGO2dCQUNoRixJQUFJQyxrQkFBa0I7Z0JBQ3RCLElBQUk7b0JBQ0YsSUFBSVAsVUFBVVEsS0FBSyxFQUFFO3dCQUNuQixNQUFNQyxZQUFZbEksS0FBS21JLEtBQUssQ0FBQ1YsVUFBVVEsS0FBSzt3QkFDNUNELGtCQUFrQkUsVUFBVWxILFFBQVE7b0JBQ3RDO2dCQUNGLEVBQUUsT0FBT29ILEdBQUc7b0JBQ1Z4SixRQUFRQyxHQUFHLENBQUMsK0JBQStCdUo7Z0JBQzdDO2dCQUVBLHFGQUFxRjtnQkFDckYseUVBQXlFO2dCQUN6RVoscUJBQXFCUSxtQkFBbUJoSDtnQkFFeEMsbUNBQW1DO2dCQUNuQyxNQUFNQyxZQUFZd0csVUFBVUssTUFBTSxJQUFJO2dCQUN0Q1YsMkJBQTJCLENBQUNuRyxZQUFZLEdBQUUsRUFBR29ELFFBQVE7Z0JBRXJELGlFQUFpRTtnQkFDakUsTUFBTWdFLFlBQVlaLEVBQUFBLHlCQUFBQSxVQUFVYSxXQUFXLGNBQXJCYiw2Q0FBQUEsdUJBQXVCTSxLQUFLLEtBQUk7Z0JBQ2xELE1BQU1RLHNCQUFzQnRILFlBQVlvSDtnQkFDeENoQix1QkFBdUI1RixLQUFLK0csS0FBSyxDQUFDRCxzQkFBc0IsS0FBS2xFLFFBQVE7Z0JBRXJFLGtCQUFrQjtnQkFDbEJpRCx5QkFBeUIsK0NBQStDLFdBQVc7Z0JBQ25GQyxxQkFBcUJFLEVBQUFBLDBCQUFBQSxVQUFVYSxXQUFXLGNBQXJCYiwrQ0FBQUEsK0JBQUFBLHdCQUF1QmdCLEtBQUssY0FBNUJoQixtREFBQUEsNkJBQThCaUIsWUFBWSxLQUFJdkY7Z0JBRW5FdkUsUUFBUUMsR0FBRyxDQUFDLHlEQUF5RDtvQkFDbkVtQyxVQUFVd0c7b0JBQ1ZtQixnQkFBZ0J2QjtvQkFDaEJ3QixZQUFZdkI7b0JBQ1p3QixjQUFjdkI7b0JBQ2R3QixVQUFVdkI7Z0JBQ1o7WUFFRixFQUFFLE9BQU9ySSxPQUFZO2dCQUNuQk4sUUFBUU0sS0FBSyxDQUFDLGdEQUFnREE7Z0JBQzlELE1BQU0sSUFBSXBDLFlBQVksK0JBQTZDLE9BQWRvQyxNQUFNbkMsT0FBTyxHQUFJO1lBQ3hFO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSSxDQUFDeUssb0JBQW9CO1lBQ3ZCLE1BQU0sSUFBSTFLLFlBQVkseUJBQXlCO1FBQ2pEO1FBRUEsb0NBQW9DO1FBQ3BDLE1BQU11RyxjQUFjQyxPQUFPa0U7UUFDM0IsSUFBSSxDQUFDLFFBQVFqRSxJQUFJLENBQUNGLGNBQWM7WUFDOUIsTUFBTSxJQUFJdkcsWUFBWSw2QkFBZ0QsT0FBbkIwSyxvQkFBbUIsOEJBQTRCO1FBQ3BHO1FBRUEsSUFBSSxDQUFDSCx3QkFBd0JuRyxPQUFPbUcseUJBQXlCLEdBQUc7WUFDOUQsTUFBTSxJQUFJdkssWUFBWSx1Q0FBdUM7UUFDL0Q7UUFFQSw0Q0FBNEM7UUFDNUMsTUFBTXdCLGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7UUFDOUMsTUFBTThDLG1CQUFtQixNQUFNbEIsc0JBQXNCNUI7UUFDckQsSUFBSSxDQUFDOEMsaUJBQWlCYixPQUFPLEVBQUU7WUFDN0IsTUFBTSxJQUFJekQsWUFBWSw2QkFBb0QsT0FBdkJzRSxpQkFBaUJsQyxLQUFLLEdBQUk7UUFDL0U7UUFFQSxzREFBc0Q7UUFDdEQsTUFBTW1DLGlCQUFpQixHQUFrQlosT0FBZjRDLGFBQVksS0FBa0IsT0FBZjVDLE9BQU9HLE9BQU8sRUFBQztRQUV4RCx3REFBd0Q7UUFDeEQsTUFBTVUsY0FBYzNELHdCQUF3QjRELEdBQUcsQ0FBQ0Y7UUFDaEQsTUFBTXRELE1BQU1ELEtBQUtDLEdBQUc7UUFDcEIsSUFBSXVELGVBQWUsTUFBT0EsY0FBZSxNQUFNO1lBQzdDLE1BQU0sSUFBSWhFLDBCQUEwQjtRQUN0QztRQUNBSyx3QkFBd0I2RCxHQUFHLENBQUNILGdCQUFnQnREO1FBRTVDLHlDQUF5QztRQUN6Q0g7UUFFQSwyREFBMkQ7UUFDM0QsTUFBTStELGdCQUFnQmxFLGtCQUFrQjhELEdBQUcsQ0FBQ0Y7UUFDNUMsSUFBSU0sZUFBZTtZQUNqQixJQUFJQSxjQUFjMUUsTUFBTSxLQUFLLFdBQVc7Z0JBQ3RDLE1BQU0sSUFBSTJFLE1BQU07WUFDbEI7WUFDQSxJQUFJRCxjQUFjMUUsTUFBTSxLQUFLLGVBQWUwRSxjQUFjdkUsSUFBSSxFQUFFO2dCQUM5RCxpRUFBaUU7Z0JBQ2pFLE1BQU1TLGlCQUFpQkMsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSztnQkFDN0MsSUFBSTRELGNBQWN4RCxTQUFTLEdBQUdOLGdCQUFnQjtvQkFDNUNlLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0Q4QyxjQUFjdkUsSUFBSTtvQkFDbEYsT0FBT3VFLGNBQWN2RSxJQUFJO2dCQUMzQjtZQUNGO1FBQ0Y7UUFFQSxrRUFBa0U7UUFDbEUsSUFBSTtZQUNGd0IsUUFBUUMsR0FBRyxDQUFDLG9EQUFvRHdFO1lBQ2hFLE1BQU12QixhQUFhLElBQUlwRixpREFBUyxDQUFDMkc7WUFDakMsTUFBTSxDQUFDckIsWUFBWSxHQUFHNUYsc0RBQVNBLENBQUM2RixzQkFBc0IsQ0FDcEQ7Z0JBQUNDLE1BQU1BLENBQUNDLElBQUksQ0FBQztnQkFBYUwsV0FBV00sV0FBVyxDQUFDRixNQUFNQSxFQUFFLE1BQU07YUFBRyxFQUNsRXZGLCtDQUFPQSxDQUFDMEYsU0FBUztZQUduQiwrREFBK0Q7WUFDL0QsTUFBTUMsa0JBQWtCLE1BQU0zRiwrQ0FBT0EsQ0FBQ29NLE9BQU8sQ0FBQ3JELFFBQVEsQ0FBQ3NELEtBQUssQ0FBQ2hIO1lBQzdELElBQUlNLGdCQUFnQjJHLFFBQVEsRUFBRTtnQkFDNUJySyxRQUFRQyxHQUFHLENBQUM7Z0JBRVosMERBQTBEO2dCQUMxRCxNQUFNcUssZUFBZSxJQUFJOU0sc0RBQVNBLENBQUNxRSxPQUFPRyxPQUFPO2dCQUNqRCxNQUFNNEIsYUFBYSxNQUFNbEUsV0FBV21FLHVCQUF1QixDQUFDeUcsY0FBYztvQkFBRXhHLE9BQU87Z0JBQUc7Z0JBQ3RGLEtBQUssTUFBTUMsT0FBT0gsV0FBWTtvQkFDNUIsNkRBQTZEO29CQUM3RCxNQUFNSSxnQkFBZ0I5RSxLQUFLQyxHQUFHLEtBQUssS0FBSyxLQUFLO29CQUM3QyxJQUFJNEUsSUFBSUUsU0FBUyxJQUFJRixJQUFJRSxTQUFTLEdBQUcsT0FBT0QsZUFBZTt3QkFDekQsaUNBQWlDO3dCQUNqQ25GLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCOzRCQUNwQ3BFLFFBQVE7NEJBQ1JrQixXQUFXTCxLQUFLQyxHQUFHOzRCQUNuQlgsTUFBTXVGLElBQUluRCxTQUFTO3dCQUNyQjt3QkFDQSxPQUFPbUQsSUFBSW5ELFNBQVM7b0JBQ3RCO2dCQUNGO2dCQUVBLDZEQUE2RDtnQkFDN0QsTUFBTXNELGtCQUFrQixZQUF3QixPQUFaTztnQkFDcEM1RixrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjtvQkFDcENwRSxRQUFRO29CQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztvQkFDbkJYLE1BQU0wRjtnQkFDUjtnQkFDQSxPQUFPQTtZQUNUO1FBQ0YsRUFBRSxPQUFPQyxZQUFZO1lBQ25CbkUsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q2tFO1FBQ3hELDJDQUEyQztRQUM3QztRQUVBLDhCQUE4QjtRQUM5QnRGLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztRQUNyQjtRQUVBYSxRQUFRQyxHQUFHLENBQUMsdUNBQXVDO1lBQ2pEbUMsVUFBVXFDO1lBQ1Y0RCxxQkFBcUJHO1lBQ3JCcEUsaUJBQWlCcUU7WUFDakJIO1FBQ0Y7UUFFQSxpQkFBaUI7UUFDakIsTUFBTXBGLGFBQWEsSUFBSXBGLGlEQUFTLENBQUMyRztRQUNqQyxNQUFNOEYsd0JBQXdCLElBQUl6TSxpREFBUyxDQUFDMEs7UUFDNUMsTUFBTXpELG9CQUFvQixJQUFJakgsaURBQVMsQ0FBQzJLO1FBRXhDLHFCQUFxQjtRQUNyQixNQUFNNkIsZUFBZSxJQUFJOU0sc0RBQVNBLENBQUM4SztRQUNuQyxNQUFNdEQsMEJBQTBCLElBQUl4SCxzREFBU0EsQ0FBQ2tMO1FBQzlDLE1BQU16RCxzQkFBc0IsSUFBSXpILHNEQUFTQSxDQUFDbUw7UUFFMUMsaUJBQWlCO1FBQ2pCLE1BQU0sQ0FBQ3ZGLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO1lBQUNDLE1BQU1BLENBQUNDLElBQUksQ0FBQztZQUFhTCxXQUFXTSxXQUFXLENBQUNGLE1BQU1BLEVBQUUsTUFBTTtTQUFHLEVBQ2xFdkYsK0NBQU9BLENBQUMwRixTQUFTO1FBR25CLGtDQUFrQztRQUNsQyxNQUFNK0cscUJBQXFCL00sZ0ZBQTZCQSxDQUN0RHdILHFCQUNBcUY7UUFHRiwyRUFBMkU7UUFDM0UsTUFBTSxDQUFDRyxhQUFhLEdBQUdqTixzREFBU0EsQ0FBQzZGLHNCQUFzQixDQUNyRDtZQUFDRCxZQUFZc0gsUUFBUTtZQUFJL00sK0RBQWdCQSxDQUFDK00sUUFBUTtZQUFJekYsb0JBQW9CeUYsUUFBUTtTQUFHLEVBQ3JGaE4sMEVBQTJCQTtRQUc3Qix5REFBeUQ7UUFDekQsTUFBTWlOLG9CQUFvQixNQUFNakwsV0FBV2lFLGNBQWMsQ0FBQzZHO1FBQzFELElBQUlsRixrQkFBa0IsRUFBRTtRQUV4QixJQUFJLENBQUNxRixtQkFBbUI7WUFDdEIzSyxRQUFRQyxHQUFHLENBQUMsaUNBQWlDdUssbUJBQW1CL0UsUUFBUTtZQUV4RSw4Q0FBOEM7WUFDOUMsTUFBTUMsY0FBYzlILDBGQUF1Q0EsQ0FDekQwTSxjQUNBRSxvQkFDQUYsY0FDQXJGLG9CQUFvQixPQUFPOztZQUc3QkssZ0JBQWdCSyxJQUFJLENBQUNEO1FBQ3ZCO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU1jLEtBQUssTUFBTXpJLCtDQUFPQSxDQUFDMEksT0FBTyxDQUM3Qm1FLFlBQVksQ0FBQzFILFlBQVlxSCx1QkFBdUJ4RixtQkFDaEQ0QixRQUFRLENBQUM7WUFDUkMsUUFBUTBEO1lBQ1JoRyxtQkFBbUJVO1lBQ25CVCxlQUFlVTtZQUNmZ0MsY0FBY3RKLCtEQUFnQkE7UUFDaEMsR0FDQzJILGVBQWUsQ0FBQ0EsaUJBQ2hCNkIsV0FBVztRQUVkLHVEQUF1RDtRQUN2RCxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsb0JBQW9CLEVBQUUsR0FBRyxNQUFNM0gsV0FBVzRILGtCQUFrQixDQUFDO1FBQ2hGZCxHQUFHZSxlQUFlLEdBQUdIO1FBQ3JCWixHQUFHZ0IsUUFBUSxHQUFHOEM7UUFFZCxrRUFBa0U7UUFDbEUsTUFBTTdDLGtCQUFrQixJQUFJM0osMEVBQWtDLENBQUM7WUFDN0Q2SixNQUFNLEVBQUU7WUFDUmxFLFdBQVcsSUFBSWpHLHNEQUFTQSxDQUFDO1lBQ3pCb0ssTUFBTXRFLE1BQU1BLENBQUNDLElBQUksQ0FBQyxpQkFBZ0NyRSxPQUFmdUYsYUFBWSxLQUFjLE9BQVh2RixLQUFLQyxHQUFHLEtBQU07UUFDbEU7UUFDQXFILEdBQUdxQixHQUFHLENBQUNKO1FBRVB6SCxRQUFRQyxHQUFHLENBQUMsa0NBQWtDO1lBQzVDbUMsVUFBVXFDO1lBQ1YyQyxXQUFXQSxVQUFVVSxLQUFLLENBQUMsR0FBRyxLQUFLO1lBQ25DVDtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVcsTUFBTWxHLE9BQU9FLGVBQWUsQ0FBQ3lFO1FBRTlDeEcsUUFBUUMsR0FBRyxDQUFDO1FBRVosd0NBQXdDO1FBQ3hDLE1BQU16QixPQUFPLE1BQU1pQix5QkFBeUJDLFlBQVlxSSxVQUFVLEdBQUc7UUFFckUvSCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCekI7UUFFbkMsdUNBQXVDO1FBQ3ZDLE1BQU13SixlQUFlLE1BQU10SSxXQUFXdUksa0JBQWtCLENBQUM7WUFDdkRySCxXQUFXcEM7WUFDWDRJLFdBQVdaLEdBQUdlLGVBQWU7WUFDN0JGLHNCQUFzQkE7UUFDeEIsR0FBRztRQUVILElBQUlXLGFBQWFoSCxLQUFLLENBQUNFLEdBQUcsRUFBRTtZQUMxQixNQUFNLElBQUk4QixNQUFNLHVCQUE4QyxPQUF2QmdGLGFBQWFoSCxLQUFLLENBQUNFLEdBQUc7UUFDL0Q7UUFFQWxCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEN6QjtRQUV0RCxnQ0FBZ0M7UUFDaENLLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNuQlg7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNLElBQUlnQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBRWpELE9BQU9qQztJQUVULEVBQUUsT0FBTzhCLE9BQVk7WUFJZEEsZ0JBbUJEQSxpQkF3REFBLGlCQUlBQSxpQkFBZ0RBLGlCQUloREEsaUJBQWtEQSxpQkFJbERBLGlCQUE0Q0EsaUJBSTVDQSxpQkFBNENBLGtCQUk1Q0E7UUFsR0pOLFFBQVFNLEtBQUssQ0FBQyxxQ0FBcUNBO1FBRW5ELCtEQUErRDtRQUMvRCxJQUFJLEdBQUNBLGlCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMscUNBQUFBLGVBQWVDLFFBQVEsQ0FBQyxpREFBZ0Q7WUFDM0UsTUFBTWtDLGlCQUFpQixHQUFlWixPQUFaTyxVQUFTLEtBQWtCLE9BQWZQLE9BQU9HLE9BQU8sRUFBQztZQUNyRG5ELGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO2dCQUNwQ3BFLFFBQVE7Z0JBQ1JrQixXQUFXTCxLQUFLQyxHQUFHO1lBQ3JCO1FBQ0Y7UUFFQSxrREFBa0Q7UUFDbEQsSUFBSW1CLE1BQU00SCxJQUFJLEVBQUU7WUFDZGxJLFFBQVFNLEtBQUssQ0FBQyxxQkFBcUJBLE1BQU00SCxJQUFJO1FBQy9DO1FBRUEsOENBQThDO1FBQzlDLElBQUk1SCxpQkFBaUJwQyxhQUFhO1lBQ2hDLE1BQU1vQztRQUNSO1FBRUEsMkRBQTJEO1FBQzNELEtBQUlBLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsZ0RBQWdEO1lBQzFFLE1BQU1rQyxpQkFBaUIsR0FBZVosT0FBWk8sVUFBUyxLQUFrQixPQUFmUCxPQUFPRyxPQUFPLEVBQUM7WUFFckRoQyxRQUFRQyxHQUFHLENBQUM7WUFFWiwyRkFBMkY7WUFDM0YsSUFBSTtnQkFDRixNQUFNaUQsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ3NFO2dCQUNqQyxNQUFNLENBQUNnQixZQUFZLEdBQUc1RixzREFBU0EsQ0FBQzZGLHNCQUFzQixDQUNwRDtvQkFBQ0MsTUFBTUEsQ0FBQ0MsSUFBSSxDQUFDO29CQUFhTCxXQUFXTSxXQUFXLENBQUNGLE1BQU1BLEVBQUUsTUFBTTtpQkFBRyxFQUNsRXZGLCtDQUFPQSxDQUFDMEYsU0FBUztnQkFHbkIsTUFBTUMsa0JBQWtCLE1BQU0zRiwrQ0FBT0EsQ0FBQ29NLE9BQU8sQ0FBQ3JELFFBQVEsQ0FBQ3NELEtBQUssQ0FBQ2hIO2dCQUM3RCxJQUFJTSxnQkFBZ0IyRyxRQUFRLEVBQUU7b0JBQzVCckssUUFBUUMsR0FBRyxDQUFDO29CQUVaLDBEQUEwRDtvQkFDMUQsTUFBTXFLLGVBQWUsSUFBSTlNLHNEQUFTQSxDQUFDcUUsT0FBT0csT0FBTztvQkFDakQsTUFBTXRDLGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7b0JBQzlDLE1BQU1rRSxhQUFhLE1BQU1sRSxXQUFXbUUsdUJBQXVCLENBQUN5RyxjQUFjO3dCQUFFeEcsT0FBTztvQkFBRztvQkFFdEYsSUFBSXFFLFlBQVk7b0JBQ2hCLEtBQUssTUFBTXBFLE9BQU9ILFdBQVk7d0JBQzVCLDREQUE0RDt3QkFDNUQsTUFBTTNFLGlCQUFpQkMsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSzt3QkFDN0MsSUFBSTRFLElBQUlFLFNBQVMsSUFBSUYsSUFBSUUsU0FBUyxHQUFHLE9BQU9oRixnQkFBZ0I7NEJBQzFEa0osWUFBWXBFLElBQUluRCxTQUFTOzRCQUN6Qjt3QkFDRjtvQkFDRjtvQkFFQSxJQUFJLENBQUN1SCxXQUFXO3dCQUNkQSxZQUFZLHFCQUFpQ2pKLE9BQVprRCxVQUFTLEtBQWMsT0FBWGxELEtBQUtDLEdBQUc7b0JBQ3ZEO29CQUVBLGlDQUFpQztvQkFDakNOLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO3dCQUNwQ3BFLFFBQVE7d0JBQ1JrQixXQUFXTCxLQUFLQyxHQUFHO3dCQUNuQlgsTUFBTTJKO29CQUNSO29CQUVBbkksUUFBUUMsR0FBRyxDQUFDLGtEQUFrRGtJO29CQUM5RCxPQUFPQTtnQkFDVCxPQUFPO29CQUNMbkksUUFBUUMsR0FBRyxDQUFDO2dCQUNkO1lBQ0YsRUFBRSxPQUFPa0UsWUFBWTtnQkFDbkJuRSxRQUFRQyxHQUFHLENBQUMsbUNBQW1Da0U7WUFDakQ7WUFFQSxNQUFNLElBQUl6RiwwQkFBMEI7UUFDdEM7UUFFQSwyQ0FBMkM7UUFDM0MsS0FBSTRCLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsMEJBQTBCO1lBQ3BELE1BQU0sSUFBSXJDLFlBQVksOEVBQThFO1FBQ3RHO1FBRUEsSUFBSW9DLEVBQUFBLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsMkJBQXdCRCxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLHdCQUF1QjtZQUNqRyxNQUFNLElBQUk5Qix1QkFBdUI7UUFDbkM7UUFFQSxJQUFJNkIsRUFBQUEsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyw2QkFBMEJELGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMseUJBQXdCO1lBQ3BHLE1BQU0sSUFBSWhDLGlCQUFpQjtRQUM3QjtRQUVBLElBQUkrQixFQUFBQSxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLHVCQUFvQkQsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyxxQkFBb0JELE1BQU1sQyxJQUFJLEtBQUssTUFBTTtZQUMvRyxNQUFNLElBQUlGLFlBQVksb0NBQW9DO1FBQzVEO1FBRUEsSUFBSW9DLEVBQUFBLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsdUJBQW9CRCxtQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHVDQUFBQSxpQkFBZUMsUUFBUSxDQUFDLHFCQUFvQjtZQUMxRixNQUFNLElBQUlyQyxZQUFZLG1GQUFtRjtRQUMzRztRQUVBLEtBQUlvQyxtQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHVDQUFBQSxpQkFBZUMsUUFBUSxDQUFDLHNCQUFzQjtZQUNoRCxNQUFNLElBQUloQyxpQkFBaUI7UUFDN0I7UUFFQSxvQkFBb0I7UUFDcEIsSUFBSStCLE1BQU1sQyxJQUFJLElBQUksT0FBT2tDLE1BQU1sQyxJQUFJLEtBQUssVUFBVTtZQUNoRCxJQUFJa0MsTUFBTWxDLElBQUksS0FBSyxDQUFDLE9BQU87Z0JBQ3pCLE1BQU0sSUFBSUYsWUFBWSx5Q0FBeUM7WUFDakU7WUFDQSxJQUFJb0MsTUFBTWxDLElBQUksS0FBSyxDQUFDLE9BQU87Z0JBQ3pCLE1BQU0sSUFBSU0sMEJBQTBCO1lBQ3RDO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSTRCLE1BQU1oQyxJQUFJLEtBQUssMEJBQTBCZ0MsTUFBTWhDLElBQUksS0FBSyxvQkFBb0I7WUFDOUUsTUFBTSxJQUFJSyxnQkFBZ0Isa0NBQWdELE9BQWQyQixNQUFNbkMsT0FBTyxHQUFJbUM7UUFDL0U7UUFFQSw4QkFBOEI7UUFDOUIsTUFBTSxJQUFJcEMsWUFBWW9DLE1BQU1uQyxPQUFPLElBQUkscUVBQXFFO0lBQzlHO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWUwTSw0QkFDcEJ6SSxRQUFnQixFQUNoQmtDLGlCQUF5QixFQUN6QkMsYUFBcUIsRUFDckIrRCxZQUFvQixFQUNwQjlELFdBQW1CLEVBQ25CM0MsTUFBVyxFQUNYMEcsT0FBeUI7SUFFekIsSUFBSTtRQUNGLGtCQUFrQjtRQUNsQixJQUFJLENBQUMxRyxVQUFVLE9BQU9BLE9BQU9FLGVBQWUsS0FBSyxjQUFjLENBQUNGLE9BQU9HLE9BQU8sRUFBRTtZQUM5RSxNQUFNLElBQUlnQixNQUFNO1FBQ2xCO1FBRUEsd0RBQXdEO1FBQ3hELElBQUk2RixZQUFZO1FBQ2hCLElBQUlOLFNBQVM7WUFDWCxJQUFJO2dCQUNGdkksUUFBUUMsR0FBRyxDQUFDLG9EQUFvRHNJO2dCQUNoRSxNQUFNLEVBQUVPLGVBQWUsRUFBRSxHQUFHLE1BQU0sNEpBQTJCO2dCQUM3RCxNQUFNQyxnQkFBZ0IsTUFBTUQsZ0JBQWdCeEcsT0FBT2lHO2dCQUNuRE0sWUFBWUUsY0FBY25CLElBQUk7Z0JBQzlCNUgsUUFBUUMsR0FBRyxDQUFDLG1EQUFtRDtvQkFDN0RzSSxTQUFTTSxVQUFVRyxFQUFFO29CQUNyQjNLLFFBQVF3SyxVQUFVeEssTUFBTTtvQkFDeEIrRCxVQUFVeUcsVUFBVXpHLFFBQVE7b0JBQzVCMEksUUFBUWpDLFVBQVVpQyxNQUFNO29CQUN4QjNCLE9BQU9OLFVBQVVNLEtBQUs7Z0JBQ3hCO2dCQUVBLDZEQUE2RDtnQkFDN0QsSUFBSU4sVUFBVXpHLFFBQVEsSUFBSXlHLFVBQVV6RyxRQUFRLEtBQUtBLFVBQVU7b0JBQ3pEcEMsUUFBUStLLElBQUksQ0FBQyw0Q0FBNEM7d0JBQ3ZEQyxVQUFVNUk7d0JBQ1Y2SSxXQUFXcEMsVUFBVXpHLFFBQVE7b0JBQy9CO2dCQUNGO2dCQUVBLGdEQUFnRDtnQkFDaEQsSUFBSXlHLFVBQVV4SyxNQUFNLEtBQUssWUFBWTtvQkFDbkMsTUFBTSxJQUFJMkUsTUFBTSxvREFBcUUsT0FBakI2RixVQUFVeEssTUFBTTtnQkFDdEY7Z0JBRUEsd0RBQXdEO2dCQUN4RCxJQUFJd0ssVUFBVXFDLEVBQUUsSUFBSXJDLFVBQVVxQyxFQUFFLEtBQUs1QyxjQUFjO29CQUNqRHRJLFFBQVErSyxJQUFJLENBQUMsMkNBQTJDO3dCQUN0REMsVUFBVTFDO3dCQUNWMkMsV0FBV3BDLFVBQVVxQyxFQUFFO29CQUN6QjtnQkFDRjtnQkFFQSx1REFBdUQ7Z0JBQ3ZELElBQUlyQyxVQUFVdEYsSUFBSSxJQUFJc0YsVUFBVXRGLElBQUksS0FBS2lCLGFBQWE7b0JBQ3BEeEUsUUFBUStLLElBQUksQ0FBQywwQ0FBMEM7d0JBQ3JEQyxVQUFVeEc7d0JBQ1Z5RyxXQUFXcEMsVUFBVXRGLElBQUk7b0JBQzNCO2dCQUNGO1lBRUYsRUFBRSxPQUFPakQsT0FBWTtnQkFDbkJOLFFBQVFNLEtBQUssQ0FBQyw4Q0FBOENBO2dCQUM1RCxNQUFNLElBQUkwQyxNQUFNLCtCQUE2QyxPQUFkMUMsTUFBTW5DLE9BQU87WUFDOUQ7UUFDRjtRQUVBLHNEQUFzRDtRQUN0RCxNQUFNc0UsaUJBQWlCLEdBQWVaLE9BQVpPLFVBQVMsS0FBa0IsT0FBZlAsT0FBT0csT0FBTyxFQUFDO1FBRXJELHdEQUF3RDtRQUN4RCxNQUFNVSxjQUFjM0Qsd0JBQXdCNEQsR0FBRyxDQUFDRjtRQUNoRCxNQUFNdEQsTUFBTUQsS0FBS0MsR0FBRztRQUNwQixJQUFJdUQsZUFBZSxNQUFPQSxjQUFlLE1BQU07WUFDN0MsTUFBTSxJQUFJTSxNQUFNO1FBQ2xCO1FBQ0FqRSx3QkFBd0I2RCxHQUFHLENBQUNILGdCQUFnQnREO1FBRTVDLHlDQUF5QztRQUN6QyxJQUFJMEQsS0FBS0MsTUFBTSxLQUFLLEtBQUs7WUFDdkIsTUFBTTdELGlCQUFpQkMsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSztZQUM3QyxLQUFLLE1BQU0sQ0FBQ0MsS0FBS0MsTUFBTSxJQUFJUixrQkFBa0JTLE9BQU8sR0FBSTtnQkFDdEQsSUFBSUQsTUFBTUUsU0FBUyxHQUFHTixnQkFBZ0I7b0JBQ3BDSixrQkFBa0JXLE1BQU0sQ0FBQ0o7Z0JBQzNCO1lBQ0Y7WUFDQSw2QkFBNkI7WUFDN0IsS0FBSyxNQUFNLENBQUNBLEtBQUtHLFVBQVUsSUFBSVIsd0JBQXdCTyxPQUFPLEdBQUk7Z0JBQ2hFLElBQUlDLFlBQVlOLGdCQUFnQjtvQkFDOUJGLHdCQUF3QlMsTUFBTSxDQUFDSjtnQkFDakM7WUFDRjtRQUNGO1FBRUEsMkRBQTJEO1FBQzNELE1BQU0yRCxnQkFBZ0JsRSxrQkFBa0I4RCxHQUFHLENBQUNGO1FBQzVDLElBQUlNLGVBQWU7WUFDakIsSUFBSUEsY0FBYzFFLE1BQU0sS0FBSyxXQUFXO2dCQUN0QyxNQUFNLElBQUkyRSxNQUFNO1lBQ2xCO1lBQ0EsSUFBSUQsY0FBYzFFLE1BQU0sS0FBSyxlQUFlMEUsY0FBY3ZFLElBQUksRUFBRTtnQkFDOUQsaUVBQWlFO2dCQUNqRSxNQUFNUyxpQkFBaUJDLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUs7Z0JBQzdDLElBQUk0RCxjQUFjeEQsU0FBUyxHQUFHTixnQkFBZ0I7b0JBQzVDZSxRQUFRQyxHQUFHLENBQUMsOENBQThDOEMsY0FBY3ZFLElBQUk7b0JBQzVFLE9BQU91RSxjQUFjdkUsSUFBSTtnQkFDM0I7WUFDRjtRQUNGO1FBRUEsa0VBQWtFO1FBQ2xFLElBQUk7WUFDRndCLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0RtQztZQUNoRSxNQUFNMUMsYUFBYTNCLCtDQUFPQSxDQUFDd0UsUUFBUSxDQUFDN0MsVUFBVTtZQUM5QyxNQUFNd0QsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ3NFO1lBQ2pDLE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO2dCQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7Z0JBQWFMLFdBQVdNLFdBQVcsQ0FBQ0YsTUFBTUEsRUFBRSxNQUFNO2FBQUcsRUFDbEV2RiwrQ0FBT0EsQ0FBQzBGLFNBQVM7WUFHbkIsZ0ZBQWdGO1lBQ2hGLE1BQU1DLGtCQUFrQixNQUFNaEUsV0FBV2lFLGNBQWMsQ0FBQ1A7WUFDeEQsSUFBSSxDQUFDTSxpQkFBaUI7Z0JBQ3BCMUQsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLGtFQUFrRTtnQkFDbEUsTUFBTXFLLGVBQWUsSUFBSTlNLHNEQUFTQSxDQUFDcUUsT0FBT0csT0FBTztnQkFDakQsTUFBTTRCLGFBQWEsTUFBTWxFLFdBQVdtRSx1QkFBdUIsQ0FBQ3lHLGNBQWM7b0JBQUV4RyxPQUFPO2dCQUFHO2dCQUN0RixLQUFLLE1BQU1DLE9BQU9ILFdBQVk7b0JBQzVCLDZEQUE2RDtvQkFDN0QsTUFBTUksZ0JBQWdCOUUsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSztvQkFDN0MsSUFBSTRFLElBQUlFLFNBQVMsSUFBSUYsSUFBSUUsU0FBUyxHQUFHLE9BQU9ELGVBQWU7d0JBQ3pELGlDQUFpQzt3QkFDakNuRixrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjs0QkFDcENwRSxRQUFROzRCQUNSa0IsV0FBV0wsS0FBS0MsR0FBRzs0QkFDbkJYLE1BQU11RixJQUFJbkQsU0FBUzt3QkFDckI7d0JBQ0EsT0FBT21ELElBQUluRCxTQUFTO29CQUN0QjtnQkFDRjtnQkFFQSw2REFBNkQ7Z0JBQzdELE1BQU1zRCxrQkFBa0IsWUFBcUIsT0FBVDlCO2dCQUNwQ3ZELGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO29CQUNwQ3BFLFFBQVE7b0JBQ1JrQixXQUFXTCxLQUFLQyxHQUFHO29CQUNuQlgsTUFBTTBGO2dCQUNSO2dCQUNBLE9BQU9BO1lBQ1Q7UUFDRixFQUFFLE9BQU9DLFlBQVk7WUFDbkJuRSxRQUFRQyxHQUFHLENBQUMsbUNBQW1Da0U7UUFDL0MsMkNBQTJDO1FBQzdDO1FBRUEsOEJBQThCO1FBQzlCdEYsa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7WUFDcENwRSxRQUFRO1lBQ1JrQixXQUFXTCxLQUFLQyxHQUFHO1FBQ3JCO1FBRUFhLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUM7WUFDL0NtQztZQUNBa0c7WUFDQTlEO1lBQ0FxRSxXQUFXQSxZQUFZO2dCQUNyQkcsSUFBSUgsVUFBVUcsRUFBRTtnQkFDaEIzSyxRQUFRd0ssVUFBVXhLLE1BQU07Z0JBQ3hCeU0sUUFBUWpDLFVBQVVpQyxNQUFNO2dCQUN4QjNCLE9BQU9OLFVBQVVNLEtBQUs7Z0JBQ3RCRixZQUFZSixVQUFVSSxVQUFVO1lBQ2xDLElBQUk7UUFDTjtRQUVBLGlCQUFpQjtRQUNqQixNQUFNL0YsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ3NFO1FBQ2pDLE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO1lBQUNDLE1BQU1BLENBQUNDLElBQUksQ0FBQztZQUFhTCxXQUFXTSxXQUFXLENBQUNGLE1BQU1BLEVBQUUsTUFBTTtTQUFHLEVBQ2xFdkYsK0NBQU9BLENBQUMwRixTQUFTO1FBR25CLE1BQU0sQ0FBQ1ksU0FBUyxHQUFHN0csc0RBQVNBLENBQUM2RixzQkFBc0IsQ0FDakQ7WUFBQ0QsWUFBWXNILFFBQVE7WUFBSS9NLCtEQUFnQkEsQ0FBQytNLFFBQVE7WUFBSSxJQUFJbE4sc0RBQVNBLENBQUM4RyxtQkFBbUJvRyxRQUFRO1NBQUcsRUFDbEdoTiwwRUFBMkJBO1FBRzdCLHFCQUFxQjtRQUNyQixNQUFNNE0sZUFBZSxJQUFJOU0sc0RBQVNBLENBQUM4SztRQUNuQyxNQUFNckYsY0FBYyxJQUFJekYsc0RBQVNBLENBQUNnSDtRQUNsQyxNQUFNUSwwQkFBMEIsSUFBSXhILHNEQUFTQSxDQUFDOEc7UUFDOUMsTUFBTVcsc0JBQXNCLElBQUl6SCxzREFBU0EsQ0FBQytHO1FBRTFDLHFCQUFxQjtRQUNyQixNQUFNNEcseUJBQXlCMU4sZ0ZBQTZCQSxDQUFDdUgseUJBQXlCc0Y7UUFDdEYsTUFBTWMsb0JBQW9CM04sZ0ZBQTZCQSxDQUFDd0gscUJBQXFCaEM7UUFFN0UsdUVBQXVFO1FBQ3ZFLE1BQU0sQ0FBQ3dILGFBQWEsR0FBR2pOLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3JEO1lBQUNELFlBQVlzSCxRQUFRO1lBQUkvTSwrREFBZ0JBLENBQUMrTSxRQUFRO1lBQUl6RixvQkFBb0J5RixRQUFRO1NBQUcsRUFDckZoTiwwRUFBMkJBO1FBRzdCc0MsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QjtZQUNuQ3FJO1lBQ0E5RDtZQUNBNEcsbUJBQW1CQSxrQkFBa0IzRixRQUFRO1lBQzdDMEYsd0JBQXdCQSx1QkFBdUIxRixRQUFRO1lBQ3ZEZ0YsY0FBY0EsYUFBYWhGLFFBQVE7WUFDbkM0RixhQUFhO2dCQUNYQyxxQkFBcUI7Z0JBQ3JCQyxpQkFBaUIsUUFBYyxrQkFBa0I7WUFDbkQ7UUFDRjtRQUVBLHlCQUF5QjtRQUN6QixNQUFNL0UsS0FBSyxNQUFNekksK0NBQU9BLENBQUMwSSxPQUFPLENBQzdCK0UsVUFBVSxDQUFDdEksWUFDWHlELFFBQVEsQ0FBQztZQUNSOEUsT0FBT3hJO1lBQ1B5SSxRQUFRcEI7WUFDUmhHLG1CQUFtQlU7WUFDbkJULGVBQWVVO1lBQ2Y0QixrQkFBa0JzRTtZQUNsQlEseUJBQXlCUDtZQUN6QnRFLFVBQVUxRDtZQUNWMkQsT0FBTzFDO1lBQ1B1SCxXQUFXbkI7WUFDWHpELHdCQUF3QnRKLDBFQUEyQkE7WUFDbkR1SixjQUFjdEosK0RBQWdCQTtZQUM5QnVKLGVBQWVwSixpRUFBeUIsQ0FBQzJGLFNBQVM7UUFDcEQsR0FDQzBELFdBQVc7UUFFZCx1REFBdUQ7UUFDdkQsTUFBTXpILGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7UUFDOUMsTUFBTSxFQUFFMEgsU0FBUyxFQUFFQyxvQkFBb0IsRUFBRSxHQUFHLE1BQU0zSCxXQUFXNEgsa0JBQWtCLENBQUM7UUFDaEZkLEdBQUdlLGVBQWUsR0FBR0g7UUFDckJaLEdBQUdnQixRQUFRLEdBQUc4QztRQUVkLGtFQUFrRTtRQUNsRSxNQUFNN0Msa0JBQWtCLElBQUkzSiwwRUFBa0MsQ0FBQztZQUM3RDZKLE1BQU0sRUFBRTtZQUNSbEUsV0FBVyxJQUFJakcsc0RBQVNBLENBQUM7WUFDekJvSyxNQUFNdEUsTUFBTUEsQ0FBQ0MsSUFBSSxDQUFDLGVBQTJCckUsT0FBWmtELFVBQVMsS0FBYyxPQUFYbEQsS0FBS0MsR0FBRyxLQUFNO1FBQzdEO1FBQ0FxSCxHQUFHcUIsR0FBRyxDQUFDSjtRQUVQekgsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQztZQUMxQ21DO1lBQ0FnRixXQUFXQSxVQUFVVSxLQUFLLENBQUMsR0FBRyxLQUFLO1lBQ25DVDtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVcsTUFBTWxHLE9BQU9FLGVBQWUsQ0FBQ3lFO1FBRTlDeEcsUUFBUUMsR0FBRyxDQUFDO1FBRVosd0NBQXdDO1FBQ3hDLE1BQU16QixPQUFPLE1BQU1pQix5QkFBeUJDLFlBQVlxSSxVQUFVLEdBQUc7UUFFckUvSCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCekI7UUFFbkMsdUNBQXVDO1FBQ3ZDLE1BQU13SixlQUFlLE1BQU10SSxXQUFXdUksa0JBQWtCLENBQUM7WUFDdkRySCxXQUFXcEM7WUFDWDRJLFdBQVdaLEdBQUdlLGVBQWU7WUFDN0JGLHNCQUFzQkE7UUFDeEIsR0FBRztRQUVILElBQUlXLGFBQWFoSCxLQUFLLENBQUNFLEdBQUcsRUFBRTtZQUMxQixNQUFNLElBQUk4QixNQUFNLHVCQUE4QyxPQUF2QmdGLGFBQWFoSCxLQUFLLENBQUNFLEdBQUc7UUFDL0Q7UUFFQWxCLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0N6QjtRQUVwRCxnQ0FBZ0M7UUFDaENLLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNuQlg7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNLElBQUlnQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBRWpELE9BQU9qQztJQUVULEVBQUUsT0FBTzhCLE9BQVk7WUFPZEEsZ0JBYURBLGlCQXFEQUEsaUJBSUFBLGlCQUtBQSxpQkFBa0RBO1FBakZ0RE4sUUFBUU0sS0FBSyxDQUFDLG1DQUFtQ0E7UUFFakQsTUFBTW1DLGlCQUFpQixHQUFlWixPQUFaTyxVQUFTLEtBQWtCLE9BQWZQLE9BQU9HLE9BQU8sRUFBQztRQUVyRCwrREFBK0Q7UUFDL0QsdUNBQXVDO1FBQ3ZDLElBQUksR0FBQzFCLGlCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMscUNBQUFBLGVBQWVDLFFBQVEsQ0FBQyxpREFBZ0Q7WUFDM0UxQixrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjtnQkFDcENwRSxRQUFRO2dCQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNyQjtRQUNGO1FBRUEsa0RBQWtEO1FBQ2xELElBQUltQixNQUFNNEgsSUFBSSxFQUFFO1lBQ2RsSSxRQUFRTSxLQUFLLENBQUMscUJBQXFCQSxNQUFNNEgsSUFBSTtRQUMvQztRQUVBLDhDQUE4QztRQUM5QyxLQUFJNUgsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyxnREFBZ0Q7WUFDMUVQLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHFGQUFxRjtZQUNyRixJQUFJO2dCQUNGLE1BQU1QLGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7Z0JBQzlDLE1BQU13RCxhQUFhLElBQUlwRixpREFBUyxDQUFDc0U7Z0JBQ2pDLE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO29CQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7b0JBQWFMLFdBQVdNLFdBQVcsQ0FBQ0YsTUFBTUEsRUFBRSxNQUFNO2lCQUFHLEVBQ2xFdkYsK0NBQU9BLENBQUMwRixTQUFTO2dCQUduQixNQUFNQyxrQkFBa0IsTUFBTWhFLFdBQVdpRSxjQUFjLENBQUNQO2dCQUN4RCxJQUFJLENBQUNNLGlCQUFpQjtvQkFDcEIxRCxRQUFRQyxHQUFHLENBQUM7b0JBRVosMERBQTBEO29CQUMxRCxNQUFNcUssZUFBZSxJQUFJOU0sc0RBQVNBLENBQUNxRSxPQUFPRyxPQUFPO29CQUNqRCxNQUFNNEIsYUFBYSxNQUFNbEUsV0FBV21FLHVCQUF1QixDQUFDeUcsY0FBYzt3QkFBRXhHLE9BQU87b0JBQUc7b0JBRXRGLElBQUlxRSxZQUFZO29CQUNoQixLQUFLLE1BQU1wRSxPQUFPSCxXQUFZO3dCQUM1Qiw0REFBNEQ7d0JBQzVELE1BQU0zRSxpQkFBaUJDLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUs7d0JBQzdDLElBQUk0RSxJQUFJRSxTQUFTLElBQUlGLElBQUlFLFNBQVMsR0FBRyxPQUFPaEYsZ0JBQWdCOzRCQUMxRGtKLFlBQVlwRSxJQUFJbkQsU0FBUzs0QkFDekI7d0JBQ0Y7b0JBQ0Y7b0JBRUEsSUFBSSxDQUFDdUgsV0FBVzt3QkFDZEEsWUFBWSxzQkFBa0NqSixPQUFaa0QsVUFBUyxLQUFjLE9BQVhsRCxLQUFLQyxHQUFHO29CQUN4RDtvQkFFQSxpQ0FBaUM7b0JBQ2pDTixrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjt3QkFDcENwRSxRQUFRO3dCQUNSa0IsV0FBV0wsS0FBS0MsR0FBRzt3QkFDbkJYLE1BQU0ySjtvQkFDUjtvQkFFQW5JLFFBQVFDLEdBQUcsQ0FBQyxtREFBbURrSTtvQkFDL0QsT0FBT0E7Z0JBQ1QsT0FBTztvQkFDTG5JLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLEVBQUUsT0FBT2tFLFlBQVk7Z0JBQ25CbkUsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2tFO1lBQ2xEO1lBRUEsTUFBTSxJQUFJbkIsTUFBTTtRQUNsQjtRQUVBLEtBQUkxQyxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLDBCQUEwQjtZQUNwRCxNQUFNLElBQUl5QyxNQUFNO1FBQ2xCO1FBRUEsS0FBSTFDLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsc0JBQXNCO1lBQ2hELE1BQU0sSUFBSXlDLE1BQU07UUFDbEI7UUFFQSxpREFBaUQ7UUFDakQsSUFBSTFDLEVBQUFBLGtCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMsc0NBQUFBLGdCQUFlQyxRQUFRLENBQUMsNkJBQTBCRCxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLHlCQUF3QjtZQUNwRyxNQUFNLElBQUl5QyxNQUFNO1FBQ2xCO1FBRUEsTUFBTTFDO0lBQ1I7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZXVMLDhCQUNwQnpKLFFBQWdCLEVBQ2hCa0MsaUJBQXlCLEVBQ3pCRSxXQUFtQixFQUNuQjNDLE1BQVc7SUFFWCxJQUFJO1FBQ0Ysa0RBQWtEO1FBQ2xELElBQUksQ0FBQ0EsVUFBVSxPQUFPQSxPQUFPRSxlQUFlLEtBQUssY0FBYyxDQUFDRixPQUFPRyxPQUFPLEVBQUU7WUFDOUUsTUFBTSxJQUFJZ0IsTUFBTTtRQUNsQjtRQUVBLHNEQUFzRDtRQUN0RCxNQUFNUCxpQkFBaUIsR0FBZVosT0FBWk8sVUFBUyxLQUFrQixPQUFmUCxPQUFPRyxPQUFPLEVBQUM7UUFFckQsd0RBQXdEO1FBQ3hELE1BQU1VLGNBQWMzRCx3QkFBd0I0RCxHQUFHLENBQUNGO1FBQ2hELE1BQU10RCxNQUFNRCxLQUFLQyxHQUFHO1FBQ3BCLElBQUl1RCxlQUFlLE1BQU9BLGNBQWUsTUFBTTtZQUM3QyxNQUFNLElBQUlNLE1BQU07UUFDbEI7UUFDQWpFLHdCQUF3QjZELEdBQUcsQ0FBQ0gsZ0JBQWdCdEQ7UUFFNUMsMkRBQTJEO1FBQzNELE1BQU00RCxnQkFBZ0JsRSxrQkFBa0I4RCxHQUFHLENBQUNGO1FBQzVDLElBQUlNLGVBQWU7WUFDakIsSUFBSUEsY0FBYzFFLE1BQU0sS0FBSyxXQUFXO2dCQUN0QyxNQUFNLElBQUkyRSxNQUFNO1lBQ2xCO1lBQ0EsSUFBSUQsY0FBYzFFLE1BQU0sS0FBSyxlQUFlMEUsY0FBY3ZFLElBQUksRUFBRTtnQkFDOUQsaUVBQWlFO2dCQUNqRSxNQUFNUyxpQkFBaUJDLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUs7Z0JBQzdDLElBQUk0RCxjQUFjeEQsU0FBUyxHQUFHTixnQkFBZ0I7b0JBQzVDZSxRQUFRQyxHQUFHLENBQUMsNkNBQTZDOEMsY0FBY3ZFLElBQUk7b0JBQzNFLE9BQU91RSxjQUFjdkUsSUFBSTtnQkFDM0I7WUFDRjtRQUNGO1FBRUEsa0VBQWtFO1FBQ2xFLElBQUk7WUFDRndCLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0RtQztZQUNoRSxNQUFNMUMsYUFBYTNCLCtDQUFPQSxDQUFDd0UsUUFBUSxDQUFDN0MsVUFBVTtZQUM5QyxNQUFNd0QsYUFBYSxJQUFJcEYsaURBQVMsQ0FBQ3NFO1lBQ2pDLE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO2dCQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7Z0JBQWFMLFdBQVdNLFdBQVcsQ0FBQ0YsTUFBTUEsRUFBRSxNQUFNO2FBQUcsRUFDbEV2RiwrQ0FBT0EsQ0FBQzBGLFNBQVM7WUFHbkIsZ0ZBQWdGO1lBQ2hGLE1BQU1DLGtCQUFrQixNQUFNaEUsV0FBV2lFLGNBQWMsQ0FBQ1A7WUFDeEQsSUFBSSxDQUFDTSxpQkFBaUI7Z0JBQ3BCMUQsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLGlFQUFpRTtnQkFDakUsTUFBTWdELGNBQWMsSUFBSXpGLHNEQUFTQSxDQUFDcUUsT0FBT0csT0FBTztnQkFDaEQsTUFBTTRCLGFBQWEsTUFBTWxFLFdBQVdtRSx1QkFBdUIsQ0FBQ1osYUFBYTtvQkFBRWEsT0FBTztnQkFBRztnQkFDckYsS0FBSyxNQUFNQyxPQUFPSCxXQUFZO29CQUM1Qiw2REFBNkQ7b0JBQzdELE1BQU1JLGdCQUFnQjlFLEtBQUtDLEdBQUcsS0FBSyxLQUFLLEtBQUs7b0JBQzdDLElBQUk0RSxJQUFJRSxTQUFTLElBQUlGLElBQUlFLFNBQVMsR0FBRyxPQUFPRCxlQUFlO3dCQUN6RCxpQ0FBaUM7d0JBQ2pDbkYsa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7NEJBQ3BDcEUsUUFBUTs0QkFDUmtCLFdBQVdMLEtBQUtDLEdBQUc7NEJBQ25CWCxNQUFNdUYsSUFBSW5ELFNBQVM7d0JBQ3JCO3dCQUNBLE9BQU9tRCxJQUFJbkQsU0FBUztvQkFDdEI7Z0JBQ0Y7Z0JBRUEsNkRBQTZEO2dCQUM3RCxNQUFNc0Qsa0JBQWtCLFlBQXFCLE9BQVQ5QjtnQkFDcEN2RCxrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjtvQkFDcENwRSxRQUFRO29CQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztvQkFDbkJYLE1BQU0wRjtnQkFDUjtnQkFDQSxPQUFPQTtZQUNUO1FBQ0YsRUFBRSxPQUFPQyxZQUFZO1lBQ25CbkUsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ2tFO1FBQzlDLDJDQUEyQztRQUM3QztRQUVBLDhCQUE4QjtRQUM5QnRGLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztRQUNyQjtRQUVBYSxRQUFRQyxHQUFHLENBQUMsdUNBQXVDO1lBQ2pEbUM7WUFDQW9DO1FBQ0Y7UUFFQSxpQkFBaUI7UUFDakIsTUFBTXRCLGFBQWEsSUFBSXBGLGlEQUFTLENBQUNzRTtRQUNqQyxNQUFNLENBQUNnQixZQUFZLEdBQUc1RixzREFBU0EsQ0FBQzZGLHNCQUFzQixDQUNwRDtZQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7WUFBYUwsV0FBV00sV0FBVyxDQUFDRixNQUFNQSxFQUFFLE1BQU07U0FBRyxFQUNsRXZGLCtDQUFPQSxDQUFDMEYsU0FBUztRQUduQixNQUFNLENBQUNZLFNBQVMsR0FBRzdHLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ2pEO1lBQUNELFlBQVlzSCxRQUFRO1lBQUkvTSwrREFBZ0JBLENBQUMrTSxRQUFRO1lBQUksSUFBSWxOLHNEQUFTQSxDQUFDOEcsbUJBQW1Cb0csUUFBUTtTQUFHLEVBQ2xHaE4sMEVBQTJCQTtRQUc3QixxQkFBcUI7UUFDckIsTUFBTXVGLGNBQWMsSUFBSXpGLHNEQUFTQSxDQUFDZ0g7UUFDbEMsTUFBTVEsMEJBQTBCLElBQUl4SCxzREFBU0EsQ0FBQzhHO1FBRTlDLDRCQUE0QjtRQUM1QixNQUFNYyxvQkFBb0IzSCxnRkFBNkJBLENBQUN1SCx5QkFBeUIvQjtRQUVqRix5QkFBeUI7UUFDekIsTUFBTXVELEtBQUssTUFBTXpJLCtDQUFPQSxDQUFDMEksT0FBTyxDQUM3QnFGLFlBQVksQ0FBQzVJLFlBQ2J5RCxRQUFRLENBQUM7WUFDUjhFLE9BQU94STtZQUNQcUIsbUJBQW1CVTtZQUNuQitHLGVBQWUzRztZQUNmMEIsVUFBVTFEO1lBQ1YyRCxPQUFPMUM7WUFDUDRDLGNBQWN0SiwrREFBZ0JBO1lBQzlCdUosZUFBZXBKLGlFQUF5QixDQUFDMkYsU0FBUztRQUNwRCxHQUNDMEQsV0FBVztRQUVkLHVEQUF1RDtRQUN2RCxNQUFNekgsYUFBYTNCLCtDQUFPQSxDQUFDd0UsUUFBUSxDQUFDN0MsVUFBVTtRQUM5QyxNQUFNLEVBQUUwSCxTQUFTLEVBQUVDLG9CQUFvQixFQUFFLEdBQUcsTUFBTTNILFdBQVc0SCxrQkFBa0IsQ0FBQztRQUNoRmQsR0FBR2UsZUFBZSxHQUFHSDtRQUNyQlosR0FBR2dCLFFBQVEsR0FBR3ZFO1FBRWQsa0VBQWtFO1FBQ2xFLE1BQU13RSxrQkFBa0IsSUFBSTNKLDBFQUFrQyxDQUFDO1lBQzdENkosTUFBTSxFQUFFO1lBQ1JsRSxXQUFXLElBQUlqRyxzREFBU0EsQ0FBQztZQUN6Qm9LLE1BQU10RSxNQUFNQSxDQUFDQyxJQUFJLENBQUMsaUJBQTZCckUsT0FBWmtELFVBQVMsS0FBYyxPQUFYbEQsS0FBS0MsR0FBRyxLQUFNO1FBQy9EO1FBQ0FxSCxHQUFHcUIsR0FBRyxDQUFDSjtRQUVQekgsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQztZQUM1Q21DO1lBQ0FnRixXQUFXQSxVQUFVVSxLQUFLLENBQUMsR0FBRyxLQUFLO1lBQ25DVDtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVcsTUFBTWxHLE9BQU9FLGVBQWUsQ0FBQ3lFO1FBRTlDeEcsUUFBUUMsR0FBRyxDQUFDO1FBRVosd0NBQXdDO1FBQ3hDLE1BQU16QixPQUFPLE1BQU1pQix5QkFBeUJDLFlBQVlxSSxVQUFVLEdBQUc7UUFFckUvSCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCekI7UUFFbkMsdUNBQXVDO1FBQ3ZDLE1BQU13SixlQUFlLE1BQU10SSxXQUFXdUksa0JBQWtCLENBQUM7WUFDdkRySCxXQUFXcEM7WUFDWDRJLFdBQVdaLEdBQUdlLGVBQWU7WUFDN0JGLHNCQUFzQkE7UUFDeEIsR0FBRztRQUVILElBQUlXLGFBQWFoSCxLQUFLLENBQUNFLEdBQUcsRUFBRTtZQUMxQixNQUFNLElBQUk4QixNQUFNLHVCQUE4QyxPQUF2QmdGLGFBQWFoSCxLQUFLLENBQUNFLEdBQUc7UUFDL0Q7UUFFQWxCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEN6QjtRQUV0RCxnQ0FBZ0M7UUFDaENLLGtCQUFrQitELEdBQUcsQ0FBQ0gsZ0JBQWdCO1lBQ3BDcEUsUUFBUTtZQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNuQlg7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNLElBQUlnQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBRWpELE9BQU9qQztJQUVULEVBQUUsT0FBTzhCLE9BQVk7WUFPZEEsZ0JBYURBLGlCQXFEQUEsaUJBSUFBLGlCQUtBQSxpQkFBa0RBO1FBakZ0RE4sUUFBUU0sS0FBSyxDQUFDLHFDQUFxQ0E7UUFFbkQsTUFBTW1DLGlCQUFpQixHQUFlWixPQUFaTyxVQUFTLEtBQWtCLE9BQWZQLE9BQU9HLE9BQU8sRUFBQztRQUVyRCwrREFBK0Q7UUFDL0QsdUNBQXVDO1FBQ3ZDLElBQUksR0FBQzFCLGlCQUFBQSxNQUFNbkMsT0FBTyxjQUFibUMscUNBQUFBLGVBQWVDLFFBQVEsQ0FBQyxpREFBZ0Q7WUFDM0UxQixrQkFBa0IrRCxHQUFHLENBQUNILGdCQUFnQjtnQkFDcENwRSxRQUFRO2dCQUNSa0IsV0FBV0wsS0FBS0MsR0FBRztZQUNyQjtRQUNGO1FBRUEsa0RBQWtEO1FBQ2xELElBQUltQixNQUFNNEgsSUFBSSxFQUFFO1lBQ2RsSSxRQUFRTSxLQUFLLENBQUMscUJBQXFCQSxNQUFNNEgsSUFBSTtRQUMvQztRQUVBLDhDQUE4QztRQUM5QyxLQUFJNUgsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyxnREFBZ0Q7WUFDMUVQLFFBQVFDLEdBQUcsQ0FBQztZQUVaLG9GQUFvRjtZQUNwRixJQUFJO2dCQUNGLE1BQU1QLGFBQWEzQiwrQ0FBT0EsQ0FBQ3dFLFFBQVEsQ0FBQzdDLFVBQVU7Z0JBQzlDLE1BQU13RCxhQUFhLElBQUlwRixpREFBUyxDQUFDc0U7Z0JBQ2pDLE1BQU0sQ0FBQ2dCLFlBQVksR0FBRzVGLHNEQUFTQSxDQUFDNkYsc0JBQXNCLENBQ3BEO29CQUFDQyxNQUFNQSxDQUFDQyxJQUFJLENBQUM7b0JBQWFMLFdBQVdNLFdBQVcsQ0FBQ0YsTUFBTUEsRUFBRSxNQUFNO2lCQUFHLEVBQ2xFdkYsK0NBQU9BLENBQUMwRixTQUFTO2dCQUduQixNQUFNQyxrQkFBa0IsTUFBTWhFLFdBQVdpRSxjQUFjLENBQUNQO2dCQUN4RCxJQUFJLENBQUNNLGlCQUFpQjtvQkFDcEIxRCxRQUFRQyxHQUFHLENBQUM7b0JBRVosMERBQTBEO29CQUMxRCxNQUFNZ0QsY0FBYyxJQUFJekYsc0RBQVNBLENBQUNxRSxPQUFPRyxPQUFPO29CQUNoRCxNQUFNNEIsYUFBYSxNQUFNbEUsV0FBV21FLHVCQUF1QixDQUFDWixhQUFhO3dCQUFFYSxPQUFPO29CQUFHO29CQUVyRixJQUFJcUUsWUFBWTtvQkFDaEIsS0FBSyxNQUFNcEUsT0FBT0gsV0FBWTt3QkFDNUIsNERBQTREO3dCQUM1RCxNQUFNM0UsaUJBQWlCQyxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLO3dCQUM3QyxJQUFJNEUsSUFBSUUsU0FBUyxJQUFJRixJQUFJRSxTQUFTLEdBQUcsT0FBT2hGLGdCQUFnQjs0QkFDMURrSixZQUFZcEUsSUFBSW5ELFNBQVM7NEJBQ3pCO3dCQUNGO29CQUNGO29CQUVBLElBQUksQ0FBQ3VILFdBQVc7d0JBQ2RBLFlBQVkscUJBQWlDakosT0FBWmtELFVBQVMsS0FBYyxPQUFYbEQsS0FBS0MsR0FBRztvQkFDdkQ7b0JBRUEsaUNBQWlDO29CQUNqQ04sa0JBQWtCK0QsR0FBRyxDQUFDSCxnQkFBZ0I7d0JBQ3BDcEUsUUFBUTt3QkFDUmtCLFdBQVdMLEtBQUtDLEdBQUc7d0JBQ25CWCxNQUFNMko7b0JBQ1I7b0JBRUFuSSxRQUFRQyxHQUFHLENBQUMsa0RBQWtEa0k7b0JBQzlELE9BQU9BO2dCQUNULE9BQU87b0JBQ0xuSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixFQUFFLE9BQU9rRSxZQUFZO2dCQUNuQm5FLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNrRTtZQUNqRDtZQUVBLE1BQU0sSUFBSW5CLE1BQU07UUFDbEI7UUFFQSxLQUFJMUMsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQywwQkFBMEI7WUFDcEQsTUFBTSxJQUFJeUMsTUFBTTtRQUNsQjtRQUVBLEtBQUkxQyxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLHNCQUFzQjtZQUNoRCxNQUFNLElBQUl5QyxNQUFNO1FBQ2xCO1FBRUEsaURBQWlEO1FBQ2pELElBQUkxQyxFQUFBQSxrQkFBQUEsTUFBTW5DLE9BQU8sY0FBYm1DLHNDQUFBQSxnQkFBZUMsUUFBUSxDQUFDLDZCQUEwQkQsa0JBQUFBLE1BQU1uQyxPQUFPLGNBQWJtQyxzQ0FBQUEsZ0JBQWVDLFFBQVEsQ0FBQyx5QkFBd0I7WUFDcEcsTUFBTSxJQUFJeUMsTUFBTTtRQUNsQjtRQUVBLE1BQU0xQztJQUNSO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFlMEwsc0JBQ3BCekQsT0FBZSxFQUNmMEQsTUFBYyxFQUNkQyxhQUFpQztJQUVqQyxJQUFJO1FBQ0ZsTSxRQUFRQyxHQUFHLENBQUMsOEJBQThCO1lBQUVzSTtZQUFTMEQ7WUFBUUM7UUFBYztRQUUzRSxNQUFNQyxXQUFXLE1BQU1uTyxnRUFBZUEsQ0FBQztZQUNyQ3VLO1lBQ0EwRDtZQUNBQztRQUNGO1FBRUEsSUFBSUMsU0FBUzlOLE1BQU0sS0FBSyxLQUFLO1lBQzNCMkIsUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ2tNLFNBQVN2RSxJQUFJO1lBQzVELE9BQU91RSxTQUFTdkUsSUFBSTtRQUN0QixPQUFPO1lBQ0wsTUFBTSxJQUFJNUUsTUFBTW1KLFNBQVNoTyxPQUFPLElBQUk7UUFDdEM7SUFFRixFQUFFLE9BQU9tQyxPQUFPO1FBQ2ROLFFBQVFNLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLFNBQVM4TCxtQkFDZEMsV0FBbUIsRUFDbkJDLGNBQXNCO1FBQ3RCQyxzQkFBQUEsaUVBQThCLEdBQzlCQztJQUVBLHVDQUF1QztJQUN2QyxJQUFJSCxnQkFBZ0IsWUFBWTtRQUM5QixPQUFPO1lBQ0xJLFlBQVk7WUFDWlIsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEMsSUFBSU8saUJBQWlCQSxrQkFBa0IsUUFBUTtRQUM3QyxPQUFPO1lBQ0xDLFlBQVk7WUFDWlIsUUFBUTtRQUNWO0lBQ0Y7SUFFQSx1Q0FBdUM7SUFDdkMsTUFBTVMsY0FBYyxJQUFJeE4sS0FBS29OO0lBQzdCLE1BQU1LLGVBQWUsSUFBSXpOLEtBQUt3TjtJQUM5QkMsYUFBYUMsT0FBTyxDQUFDRCxhQUFhRSxPQUFPLEtBQUtOO0lBRTlDLElBQUksSUFBSXJOLFNBQVN5TixjQUFjO1FBQzdCLE9BQU87WUFDTEYsWUFBWTtZQUNaUixRQUFRO1FBQ1Y7SUFDRjtJQUVBLE9BQU87UUFBRVEsWUFBWTtJQUFLO0FBQzVCO0FBRUE7O0NBRUMsR0FDTSxTQUFTSyxxQkFBcUJOLGFBQXFCO0lBQ3hELE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87Z0JBQ0xPLE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7WUFDWDtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMRixPQUFPO2dCQUNQQyxPQUFPO2dCQUNQQyxTQUFTO1lBQ1g7UUFDRixLQUFLO1lBQ0gsT0FBTztnQkFDTEYsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztZQUNYO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQ0xGLE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7WUFDWDtRQUNGO1lBQ0UsT0FBTztnQkFDTEYsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztZQUNYO0lBQ0o7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXHNyY1xcdXRpbHNcXGVzY3Jvdy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQdWJsaWNLZXksIFRyYW5zYWN0aW9uIH0gZnJvbSAnQHNvbGFuYS93ZWIzLmpzJztcbmltcG9ydCB7IGdldEFzc29jaWF0ZWRUb2tlbkFkZHJlc3NTeW5jLCBBU1NPQ0lBVEVEX1RPS0VOX1BST0dSQU1fSUQsIFRPS0VOX1BST0dSQU1fSUQsIGNyZWF0ZUFzc29jaWF0ZWRUb2tlbkFjY291bnRJbnN0cnVjdGlvbiwgY3JlYXRlU3luY05hdGl2ZUluc3RydWN0aW9uIH0gZnJvbSAnQHNvbGFuYS9zcGwtdG9rZW4nO1xuaW1wb3J0ICogYXMgYW5jaG9yIGZyb20gJ0Bjb3JhbC14eXovYW5jaG9yJztcbmltcG9ydCB7IHByb2dyYW0gfSBmcm9tICcuL3NvbC9zZXR1cCc7XG5pbXBvcnQgeyBpbml0aWF0ZURpc3B1dGUgfSBmcm9tICdAL2F4aW9zL3JlcXVlc3RzJztcbmltcG9ydCB7IEFwcEVycm9yIH0gZnJvbSAnLi9lcnJvckhhbmRsaW5nJztcblxuLy8gRW5oYW5jZWQgZXJyb3IgdHlwZXMgZm9yIGVzY3JvdyBvcGVyYXRpb25zXG5leHBvcnQgY2xhc3MgRXNjcm93RXJyb3IgZXh0ZW5kcyBBcHBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2U6IHN0cmluZywgY29kZTogc3RyaW5nID0gJ0VTQ1JPV19FUlJPUicsIHN0YXR1czogbnVtYmVyID0gNDAwKSB7XG4gICAgc3VwZXIobWVzc2FnZSwgY29kZSwgc3RhdHVzKTtcbiAgICB0aGlzLm5hbWUgPSAnRXNjcm93RXJyb3InO1xuICB9XG59XG5cbmV4cG9ydCBjbGFzcyBUcmFuc2FjdGlvbkVycm9yIGV4dGVuZHMgRXNjcm93RXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlOiBzdHJpbmcsIHB1YmxpYyB0eElkPzogc3RyaW5nKSB7XG4gICAgc3VwZXIobWVzc2FnZSwgJ1RSQU5TQUNUSU9OX0VSUk9SJywgNDAwKTtcbiAgICB0aGlzLm5hbWUgPSAnVHJhbnNhY3Rpb25FcnJvcic7XG4gIH1cbn1cblxuZXhwb3J0IGNsYXNzIEluc3VmZmljaWVudEZ1bmRzRXJyb3IgZXh0ZW5kcyBFc2Nyb3dFcnJvciB7XG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2U6IHN0cmluZyA9ICdJbnN1ZmZpY2llbnQgZnVuZHMgdG8gY29tcGxldGUgdGhlIHRyYW5zYWN0aW9uJykge1xuICAgIHN1cGVyKG1lc3NhZ2UsICdJTlNVRkZJQ0lFTlRfRlVORFMnLCA0MDApO1xuICAgIHRoaXMubmFtZSA9ICdJbnN1ZmZpY2llbnRGdW5kc0Vycm9yJztcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgRHVwbGljYXRlVHJhbnNhY3Rpb25FcnJvciBleHRlbmRzIEVzY3Jvd0Vycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZTogc3RyaW5nID0gJ1RyYW5zYWN0aW9uIGFscmVhZHkgaW4gcHJvZ3Jlc3Mgb3IgY29tcGxldGVkJykge1xuICAgIHN1cGVyKG1lc3NhZ2UsICdEVVBMSUNBVEVfVFJBTlNBQ1RJT04nLCA0MDkpO1xuICAgIHRoaXMubmFtZSA9ICdEdXBsaWNhdGVUcmFuc2FjdGlvbkVycm9yJztcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgQmxvY2tjaGFpbkVycm9yIGV4dGVuZHMgRXNjcm93RXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlOiBzdHJpbmcsIHB1YmxpYyBvcmlnaW5hbEVycm9yPzogYW55KSB7XG4gICAgc3VwZXIobWVzc2FnZSwgJ0JMT0NLQ0hBSU5fRVJST1InLCA1MDApO1xuICAgIHRoaXMubmFtZSA9ICdCbG9ja2NoYWluRXJyb3InO1xuICB9XG59XG5cbi8vIE9wZXJhdGlvbiBzdGF0dXMgdHlwZXNcbmV4cG9ydCB0eXBlIEVzY3Jvd09wZXJhdGlvblR5cGUgPSAnYnV5JyB8ICdzZWxsJyB8ICdyZWZ1bmQnIHwgJ2Rpc3B1dGUnIHwgJ2FjY2VwdCc7XG5leHBvcnQgdHlwZSBFc2Nyb3dPcGVyYXRpb25TdGF0dXMgPSAnaWRsZScgfCAncGVuZGluZycgfCAnc3VjY2VzcycgfCAnZXJyb3InO1xuXG5leHBvcnQgaW50ZXJmYWNlIEVzY3Jvd09wZXJhdGlvblN0YXRlIHtcbiAgc3RhdHVzOiBFc2Nyb3dPcGVyYXRpb25TdGF0dXM7XG4gIGVycm9yOiBFcnJvciB8IG51bGw7XG4gIHR4SWQ6IHN0cmluZyB8IG51bGw7XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEVzY3Jvd09wZXJhdGlvbnNTdGF0ZSB7XG4gIGJ1eTogRXNjcm93T3BlcmF0aW9uU3RhdGU7XG4gIHNlbGw6IEVzY3Jvd09wZXJhdGlvblN0YXRlO1xuICByZWZ1bmQ6IEVzY3Jvd09wZXJhdGlvblN0YXRlO1xuICBkaXNwdXRlOiBFc2Nyb3dPcGVyYXRpb25TdGF0ZTtcbiAgYWNjZXB0OiBFc2Nyb3dPcGVyYXRpb25TdGF0ZTtcbn1cblxuLy8gVHJhbnNhY3Rpb24gc3RhdGUgbWFuYWdlbWVudCB0byBwcmV2ZW50IGR1cGxpY2F0ZXNcbmNvbnN0IHRyYW5zYWN0aW9uU3RhdGVzID0gbmV3IE1hcDxzdHJpbmcsIHtcbiAgc3RhdHVzOiAncGVuZGluZycgfCAnY29tcGxldGVkJyB8ICdmYWlsZWQnO1xuICB0aW1lc3RhbXA6IG51bWJlcjtcbiAgdHhJZD86IHN0cmluZztcbn0+KCk7XG5cbi8vIExhc3QgdHJhbnNhY3Rpb24gYXR0ZW1wdCB0aW1lc3RhbXBzIHRvIHByZXZlbnQgcmFwaWQgZHVwbGljYXRlc1xuY29uc3QgbGFzdFRyYW5zYWN0aW9uQXR0ZW1wdHMgPSBuZXcgTWFwPHN0cmluZywgbnVtYmVyPigpO1xuXG4vLyBDbGVhbiB1cCBvbGQgdHJhbnNhY3Rpb24gc3RhdGVzIChvbGRlciB0aGFuIDUgbWludXRlcylcbmNvbnN0IGNsZWFudXBPbGRUcmFuc2FjdGlvbnMgPSAoKSA9PiB7XG4gIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gIGZvciAoY29uc3QgW2tleSwgc3RhdGVdIG9mIHRyYW5zYWN0aW9uU3RhdGVzLmVudHJpZXMoKSkge1xuICAgIGlmIChzdGF0ZS50aW1lc3RhbXAgPCBmaXZlTWludXRlc0Fnbykge1xuICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuZGVsZXRlKGtleSk7XG4gICAgfVxuICB9XG59O1xuXG4vLyBVdGlsaXR5IGZ1bmN0aW9uIHRvIHNlbmQgdHJhbnNhY3Rpb24gd2l0aCByZXRyeSBsb2dpY1xuY29uc3Qgc2VuZFRyYW5zYWN0aW9uV2l0aFJldHJ5ID0gYXN5bmMgKFxuICBjb25uZWN0aW9uOiBhbnksXG4gIHNpZ25lZFRyYW5zYWN0aW9uOiBhbnksXG4gIG1heFJldHJpZXM6IG51bWJlciA9IDMsXG4gIHJldHJ5RGVsYXk6IG51bWJlciA9IDEwMDBcbik6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gIGxldCBsYXN0RXJyb3I6IGFueTtcblxuICBmb3IgKGxldCBhdHRlbXB0ID0gMTsgYXR0ZW1wdCA8PSBtYXhSZXRyaWVzOyBhdHRlbXB0KyspIHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYPCflIQgVHJhbnNhY3Rpb24gYXR0ZW1wdCAke2F0dGVtcHR9LyR7bWF4UmV0cmllc31gKTtcblxuICAgICAgY29uc3QgdHhJZCA9IGF3YWl0IGNvbm5lY3Rpb24uc2VuZFJhd1RyYW5zYWN0aW9uKHNpZ25lZFRyYW5zYWN0aW9uLnNlcmlhbGl6ZSgpLCB7XG4gICAgICAgIHNraXBQcmVmbGlnaHQ6IGZhbHNlLFxuICAgICAgICBwcmVmbGlnaHRDb21taXRtZW50OiAnY29uZmlybWVkJyxcbiAgICAgICAgbWF4UmV0cmllczogMCAvLyBQcmV2ZW50IGF1dG9tYXRpYyByZXRyaWVzIHRoYXQgY291bGQgY2F1c2UgZHVwbGljYXRlc1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDinIUgVHJhbnNhY3Rpb24gc2VudCBzdWNjZXNzZnVsbHkgb24gYXR0ZW1wdCAke2F0dGVtcHR9OmAsIHR4SWQpO1xuICAgICAgcmV0dXJuIHR4SWQ7XG5cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBsYXN0RXJyb3IgPSBlcnJvcjtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCBUcmFuc2FjdGlvbiBhdHRlbXB0ICR7YXR0ZW1wdH0gZmFpbGVkOmAsIGVycm9yLm1lc3NhZ2UpO1xuXG4gICAgICAvLyBEb24ndCByZXRyeSBmb3IgY2VydGFpbiBlcnJvcnMgLSB0aHJvdyBpbW1lZGlhdGVseVxuICAgICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykgfHxcbiAgICAgICAgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1RyYW5zYWN0aW9uIGFscmVhZHkgcHJvY2Vzc2VkJykgfHxcbiAgICAgICAgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2FscmVhZHkgYmVlbiBwcm9jZXNzZWQnKSB8fFxuICAgICAgICBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnVXNlciByZWplY3RlZCcpIHx8XG4gICAgICAgIGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCd1c2VyIHJlamVjdGVkJykgfHxcbiAgICAgICAgZXJyb3IuY29kZSA9PT0gNDAwMSB8fFxuICAgICAgICBlcnJvci5jb2RlID09PSAtMzIwMDMpIHsgLy8gUlBDIGVycm9yIGZvciBhbHJlYWR5IHByb2Nlc3NlZFxuICAgICAgICBjb25zb2xlLmxvZyhg8J+aqyBOb3QgcmV0cnlpbmcgLSBlcnJvciB0eXBlIHNob3VsZCBub3QgYmUgcmV0cmllZGApO1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgLy8gRG9uJ3QgcmV0cnkgb24gbGFzdCBhdHRlbXB0XG4gICAgICBpZiAoYXR0ZW1wdCA+PSBtYXhSZXRyaWVzKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5qrIE1heCByZXRyaWVzIHJlYWNoZWQsIHRocm93aW5nIGVycm9yYCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuXG4gICAgICAvLyBXYWl0IGJlZm9yZSByZXRyeWluZ1xuICAgICAgY29uc29sZS5sb2coYOKPsyBXYWl0aW5nICR7cmV0cnlEZWxheX1tcyBiZWZvcmUgcmV0cnkuLi5gKTtcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCByZXRyeURlbGF5KSk7XG4gICAgICByZXRyeURlbGF5ICo9IDEuNTsgLy8gRXhwb25lbnRpYWwgYmFja29mZlxuICAgIH1cbiAgfVxuXG4gIHRocm93IGxhc3RFcnJvcjtcbn07XG5cbi8vIEVuaGFuY2VkIHRyYW5zYWN0aW9uIHN0YXR1cyBjaGVja2luZyB3aXRoIGRldGFpbGVkIGVycm9yIGluZm9ybWF0aW9uXG5jb25zdCBjaGVja1RyYW5zYWN0aW9uU3VjY2VzcyA9IGFzeW5jIChcbiAgY29ubmVjdGlvbjogYW55LFxuICBzaWduYXR1cmU6IHN0cmluZyxcbiAgbWF4QXR0ZW1wdHM6IG51bWJlciA9IDUsXG4gIHJldHJ5RGVsYXk6IG51bWJlciA9IDIwMDBcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZzsgY29uZmlybWF0aW9uU3RhdHVzPzogc3RyaW5nIH0+ID0+IHtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBtYXhBdHRlbXB0czsgaSsrKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN0YXR1cyA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0U2lnbmF0dXJlU3RhdHVzKHNpZ25hdHVyZSk7XG5cbiAgICAgIGlmIChzdGF0dXM/LnZhbHVlKSB7XG4gICAgICAgIGNvbnN0IGNvbmZpcm1hdGlvblN0YXR1cyA9IHN0YXR1cy52YWx1ZS5jb25maXJtYXRpb25TdGF0dXM7XG5cbiAgICAgICAgaWYgKGNvbmZpcm1hdGlvblN0YXR1cyA9PT0gJ2NvbmZpcm1lZCcgfHwgY29uZmlybWF0aW9uU3RhdHVzID09PSAnZmluYWxpemVkJykge1xuICAgICAgICAgIGlmIChzdGF0dXMudmFsdWUuZXJyKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6IGBUcmFuc2FjdGlvbiBmYWlsZWQ6ICR7SlNPTi5zdHJpbmdpZnkoc3RhdHVzLnZhbHVlLmVycil9YCxcbiAgICAgICAgICAgICAgY29uZmlybWF0aW9uU3RhdHVzXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBjb25maXJtYXRpb25TdGF0dXMgfTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFRyYW5zYWN0aW9uIGlzIHN0aWxsIHByb2Nlc3NpbmdcbiAgICAgICAgaWYgKGkgPCBtYXhBdHRlbXB0cyAtIDEpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgVHJhbnNhY3Rpb24gJHtzaWduYXR1cmV9IHN0aWxsIHByb2Nlc3NpbmcsIGF0dGVtcHQgJHtpICsgMX0vJHttYXhBdHRlbXB0c31gKTtcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgcmV0cnlEZWxheSkpO1xuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIE5vIHN0YXR1cyBhdmFpbGFibGUgeWV0XG4gICAgICBpZiAoaSA8IG1heEF0dGVtcHRzIC0gMSkge1xuICAgICAgICBjb25zb2xlLmxvZyhgTm8gc3RhdHVzIGF2YWlsYWJsZSBmb3IgdHJhbnNhY3Rpb24gJHtzaWduYXR1cmV9LCBhdHRlbXB0ICR7aSArIDF9LyR7bWF4QXR0ZW1wdHN9YCk7XG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCByZXRyeURlbGF5KSk7XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coYEF0dGVtcHQgJHtpICsgMX0gdG8gY2hlY2sgdHJhbnNhY3Rpb24gc3RhdHVzIGZhaWxlZDpgLCBlcnJvcik7XG4gICAgICBpZiAoaSA9PT0gbWF4QXR0ZW1wdHMgLSAxKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYEZhaWxlZCB0byBjaGVjayB0cmFuc2FjdGlvbiBzdGF0dXM6ICR7ZXJyb3J9YCB9O1xuICAgICAgfVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIHJldHJ5RGVsYXkpKTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdUcmFuc2FjdGlvbiBzdGF0dXMgY2hlY2sgdGltZWQgb3V0JyB9O1xufTtcblxuLy8gRW5oYW5jZWQgY29ubmVjdGlvbiBoZWFsdGggY2hlY2tcbmNvbnN0IGNoZWNrQ29ubmVjdGlvbkhlYWx0aCA9IGFzeW5jIChjb25uZWN0aW9uOiBhbnkpOiBQcm9taXNlPHsgaGVhbHRoeTogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgY29uc3Qgc2xvdCA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0U2xvdCgpO1xuICAgIGNvbnN0IHJlc3BvbnNlVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG5cbiAgICBpZiAocmVzcG9uc2VUaW1lID4gMTAwMDApIHsgLy8gMTAgc2Vjb25kc1xuICAgICAgcmV0dXJuIHsgaGVhbHRoeTogZmFsc2UsIGVycm9yOiAnQ29ubmVjdGlvbiByZXNwb25zZSB0aW1lIHRvbyBzbG93JyB9O1xuICAgIH1cblxuICAgIGlmICh0eXBlb2Ygc2xvdCAhPT0gJ251bWJlcicgfHwgc2xvdCA8PSAwKSB7XG4gICAgICByZXR1cm4geyBoZWFsdGh5OiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIHNsb3QgcmVzcG9uc2UgZnJvbSBjb25uZWN0aW9uJyB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IGhlYWx0aHk6IHRydWUgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4geyBoZWFsdGh5OiBmYWxzZSwgZXJyb3I6IGBDb25uZWN0aW9uIGhlYWx0aCBjaGVjayBmYWlsZWQ6ICR7ZXJyb3J9YCB9O1xuICB9XG59O1xuXG4vLyBFbmhhbmNlZCB3YWxsZXQgdmFsaWRhdGlvblxuY29uc3QgdmFsaWRhdGVXYWxsZXRDb25uZWN0aW9uID0gKHdhbGxldDogYW55KTogeyB2YWxpZDogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfSA9PiB7XG4gIGlmICghd2FsbGV0KSB7XG4gICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ1dhbGxldCBub3QgY29ubmVjdGVkLiBQbGVhc2UgY29ubmVjdCB5b3VyIHdhbGxldCBhbmQgdHJ5IGFnYWluLicgfTtcbiAgfVxuXG4gIGlmICh0eXBlb2Ygd2FsbGV0LnNpZ25UcmFuc2FjdGlvbiAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6ICdXYWxsZXQgZG9lcyBub3Qgc3VwcG9ydCB0cmFuc2FjdGlvbiBzaWduaW5nLiBQbGVhc2UgdXNlIGEgY29tcGF0aWJsZSB3YWxsZXQuJyB9O1xuICB9XG5cbiAgaWYgKCF3YWxsZXQuYWRkcmVzcykge1xuICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6ICdXYWxsZXQgYWRkcmVzcyBub3QgYXZhaWxhYmxlLiBQbGVhc2UgcmVjb25uZWN0IHlvdXIgd2FsbGV0LicgfTtcbiAgfVxuXG4gIC8vIENoZWNrIGlmIHdhbGxldCBhZGRyZXNzIGlzIHZhbGlkIFNvbGFuYSBhZGRyZXNzXG4gIHRyeSB7XG4gICAgbmV3IFB1YmxpY0tleSh3YWxsZXQuYWRkcmVzcyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ0ludmFsaWQgd2FsbGV0IGFkZHJlc3MgZm9ybWF0LicgfTtcbiAgfVxuXG4gIHJldHVybiB7IHZhbGlkOiB0cnVlIH07XG59O1xuXG5leHBvcnQgaW50ZXJmYWNlIEVzY3Jvd1R4RGF0YSB7XG4gIGVzY3Jvd0lkOiBzdHJpbmc7XG4gIHNvbEFtb3VudDogc3RyaW5nO1xuICBwZXJrVG9rZW5BbW91bnQ6IHN0cmluZztcbiAgcHVyY2hhc2VQZGE6IHN0cmluZztcbiAgdmF1bHRQZGE6IHN0cmluZztcbiAgcHVyY2hhc2VUb2tlbk1pbnQ6IHN0cmluZztcbiAgcGVya1Rva2VuTWludDogc3RyaW5nO1xuICBidXllcldhbGxldDogc3RyaW5nO1xuICBzZWxsZXJXYWxsZXQ6IHN0cmluZztcbn1cblxuLyoqXG4gKiBDcmVhdGUgYW5kIHNpZ24gZXNjcm93IGJ1eSB0cmFuc2FjdGlvblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlRXNjcm93QnV5VHJhbnNhY3Rpb24oXG4gIGVzY3Jvd1R4RGF0YTogRXNjcm93VHhEYXRhLFxuICB3YWxsZXQ6IGFueVxuKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgdHJ5IHtcbiAgICAvLyBFbmhhbmNlZCB3YWxsZXQgdmFsaWRhdGlvblxuICAgIGNvbnN0IHdhbGxldFZhbGlkYXRpb24gPSB2YWxpZGF0ZVdhbGxldENvbm5lY3Rpb24od2FsbGV0KTtcbiAgICBpZiAoIXdhbGxldFZhbGlkYXRpb24udmFsaWQpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcih3YWxsZXRWYWxpZGF0aW9uLmVycm9yISwgXCJXQUxMRVRfRVJST1JcIik7XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZXNjcm93IHRyYW5zYWN0aW9uIGRhdGFcbiAgICBpZiAoIWVzY3Jvd1R4RGF0YSkge1xuICAgICAgdGhyb3cgbmV3IEVzY3Jvd0Vycm9yKFwiRXNjcm93IHRyYW5zYWN0aW9uIGRhdGEgaXMgcmVxdWlyZWRcIiwgXCJJTlZBTElEX0RBVEFcIik7XG4gICAgfVxuXG4gICAgaWYgKCFlc2Nyb3dUeERhdGEuZXNjcm93SWQpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcihcIkVzY3JvdyBJRCBpcyByZXF1aXJlZFwiLCBcIklOVkFMSURfREFUQVwiKTtcbiAgICB9XG5cbiAgICBpZiAoIWVzY3Jvd1R4RGF0YS5zb2xBbW91bnQgfHwgTnVtYmVyKGVzY3Jvd1R4RGF0YS5zb2xBbW91bnQpIDw9IDApIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcihcIlZhbGlkIFNPTCBhbW91bnQgaXMgcmVxdWlyZWRcIiwgXCJJTlZBTElEX0RBVEFcIik7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgY29ubmVjdGlvbiBoZWFsdGggYmVmb3JlIHByb2NlZWRpbmdcbiAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgIGNvbnN0IGNvbm5lY3Rpb25IZWFsdGggPSBhd2FpdCBjaGVja0Nvbm5lY3Rpb25IZWFsdGgoY29ubmVjdGlvbik7XG4gICAgaWYgKCFjb25uZWN0aW9uSGVhbHRoLmhlYWx0aHkpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcihgTmV0d29yayBjb25uZWN0aW9uIGlzc3VlOiAke2Nvbm5lY3Rpb25IZWFsdGguZXJyb3J9YCwgXCJORVRXT1JLX0VSUk9SXCIpO1xuICAgIH1cblxuICAgIC8vIENyZWF0ZSB1bmlxdWUgdHJhbnNhY3Rpb24ga2V5IHRvIHByZXZlbnQgZHVwbGljYXRlc1xuICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93VHhEYXRhLmVzY3Jvd0lkfS0ke3dhbGxldC5hZGRyZXNzfS1idXlgO1xuXG4gICAgLy8gQ2hlY2sgZm9yIHJhcGlkIGR1cGxpY2F0ZSBhdHRlbXB0cyAod2l0aGluIDMgc2Vjb25kcylcbiAgICBjb25zdCBsYXN0QXR0ZW1wdCA9IGxhc3RUcmFuc2FjdGlvbkF0dGVtcHRzLmdldCh0cmFuc2FjdGlvbktleSk7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICBpZiAobGFzdEF0dGVtcHQgJiYgKG5vdyAtIGxhc3RBdHRlbXB0KSA8IDMwMDApIHtcbiAgICAgIHRocm93IG5ldyBEdXBsaWNhdGVUcmFuc2FjdGlvbkVycm9yKFwiUGxlYXNlIHdhaXQgYSBtb21lbnQgYmVmb3JlIHRyeWluZyBhZ2Fpbi4gVHJhbnNhY3Rpb24gYXR0ZW1wdCB0b28gc29vbi5cIik7XG4gICAgfVxuICAgIGxhc3RUcmFuc2FjdGlvbkF0dGVtcHRzLnNldCh0cmFuc2FjdGlvbktleSwgbm93KTtcblxuICAgIC8vIENsZWFuIHVwIG9sZCB0cmFuc2FjdGlvbnMgcGVyaW9kaWNhbGx5XG4gICAgaWYgKE1hdGgucmFuZG9tKCkgPCAwLjEpIHsgLy8gMTAlIGNoYW5jZSB0byBjbGVhbnVwIG9uIGVhY2ggY2FsbFxuICAgICAgY29uc3QgZml2ZU1pbnV0ZXNBZ28gPSBEYXRlLm5vdygpIC0gNSAqIDYwICogMTAwMDtcbiAgICAgIGZvciAoY29uc3QgW2tleSwgc3RhdGVdIG9mIHRyYW5zYWN0aW9uU3RhdGVzLmVudHJpZXMoKSkge1xuICAgICAgICBpZiAoc3RhdGUudGltZXN0YW1wIDwgZml2ZU1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICB0cmFuc2FjdGlvblN0YXRlcy5kZWxldGUoa2V5KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLy8gQWxzbyBjbGVhbnVwIGxhc3QgYXR0ZW1wdHNcbiAgICAgIGZvciAoY29uc3QgW2tleSwgdGltZXN0YW1wXSBvZiBsYXN0VHJhbnNhY3Rpb25BdHRlbXB0cy5lbnRyaWVzKCkpIHtcbiAgICAgICAgaWYgKHRpbWVzdGFtcCA8IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgbGFzdFRyYW5zYWN0aW9uQXR0ZW1wdHMuZGVsZXRlKGtleSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB0cmFuc2FjdGlvbiBpcyBhbHJlYWR5IGluIHByb2dyZXNzIG9yIGNvbXBsZXRlZFxuICAgIGNvbnN0IGV4aXN0aW5nU3RhdGUgPSB0cmFuc2FjdGlvblN0YXRlcy5nZXQodHJhbnNhY3Rpb25LZXkpO1xuICAgIGlmIChleGlzdGluZ1N0YXRlKSB7XG4gICAgICBpZiAoZXhpc3RpbmdTdGF0ZS5zdGF0dXMgPT09ICdwZW5kaW5nJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUcmFuc2FjdGlvbiBhbHJlYWR5IGluIHByb2dyZXNzLiBQbGVhc2Ugd2FpdCBmb3IgdGhlIGN1cnJlbnQgdHJhbnNhY3Rpb24gdG8gY29tcGxldGUuXCIpO1xuICAgICAgfVxuICAgICAgaWYgKGV4aXN0aW5nU3RhdGUuc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiBleGlzdGluZ1N0YXRlLnR4SWQpIHtcbiAgICAgICAgLy8gSWYgY29tcGxldGVkIHJlY2VudGx5ICh3aXRoaW4gNSBtaW51dGVzKSwgcmV0dXJuIGV4aXN0aW5nIHR4SWRcbiAgICAgICAgY29uc3QgZml2ZU1pbnV0ZXNBZ28gPSBEYXRlLm5vdygpIC0gNSAqIDYwICogMTAwMDtcbiAgICAgICAgaWYgKGV4aXN0aW5nU3RhdGUudGltZXN0YW1wID4gZml2ZU1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlJldHVybmluZyBleGlzdGluZyB0cmFuc2FjdGlvbiBJRDpcIiwgZXhpc3RpbmdTdGF0ZS50eElkKTtcbiAgICAgICAgICByZXR1cm4gZXhpc3RpbmdTdGF0ZS50eElkO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhlcmUncyBhbHJlYWR5IGEgc3VjY2Vzc2Z1bCB0cmFuc2FjdGlvbiBmb3IgdGhpcyBlc2Nyb3cgb24gdGhlIGJsb2NrY2hhaW5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gQ2hlY2tpbmcgZm9yIGV4aXN0aW5nIHRyYW5zYWN0aW9ucyBmb3IgZXNjcm93OicsIGVzY3Jvd1R4RGF0YS5lc2Nyb3dJZCk7XG4gICAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgICAgY29uc3QgYnV5ZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KHdhbGxldC5hZGRyZXNzKTtcblxuICAgICAgLy8gQ2FsY3VsYXRlIHRoZSBwdXJjaGFzZSBQREEgdG8gY2hlY2sgaWYgaXQgYWxyZWFkeSBleGlzdHNcbiAgICAgIGNvbnN0IGVzY3Jvd0lkQk4gPSBuZXcgYW5jaG9yLkJOKGVzY3Jvd1R4RGF0YS5lc2Nyb3dJZCk7XG4gICAgICBjb25zdCBbcHVyY2hhc2VQZGFdID0gUHVibGljS2V5LmZpbmRQcm9ncmFtQWRkcmVzc1N5bmMoXG4gICAgICAgIFtCdWZmZXIuZnJvbShcInB1cmNoYXNlXCIpLCBlc2Nyb3dJZEJOLnRvQXJyYXlMaWtlKEJ1ZmZlciwgXCJsZVwiLCA4KV0sXG4gICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICApO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgcHVyY2hhc2UgYWNjb3VudCBhbHJlYWR5IGV4aXN0c1xuICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhwdXJjaGFzZVBkYSk7XG4gICAgICBpZiAocHVyY2hhc2VBY2NvdW50KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgUHVyY2hhc2UgYWNjb3VudCBhbHJlYWR5IGV4aXN0cywgZXNjcm93IHRyYW5zYWN0aW9uIHdhcyBzdWNjZXNzZnVsJyk7XG5cbiAgICAgICAgLy8gVHJ5IHRvIGZpbmQgdGhlIHRyYW5zYWN0aW9uIHNpZ25hdHVyZSBpbiByZWNlbnQgaGlzdG9yeVxuICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gYXdhaXQgY29ubmVjdGlvbi5nZXRTaWduYXR1cmVzRm9yQWRkcmVzcyhidXllclB1YmtleSwgeyBsaW1pdDogMjAgfSk7XG4gICAgICAgIGZvciAoY29uc3Qgc2lnIG9mIHNpZ25hdHVyZXMpIHtcbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHNpZ25hdHVyZSBpcyByZWNlbnQgKHdpdGhpbiBsYXN0IDEwIG1pbnV0ZXMpXG4gICAgICAgICAgY29uc3QgdGVuTWludXRlc0FnbyA9IERhdGUubm93KCkgLSAxMCAqIDYwICogMTAwMDtcbiAgICAgICAgICBpZiAoc2lnLmJsb2NrVGltZSAmJiBzaWcuYmxvY2tUaW1lICogMTAwMCA+IHRlbk1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICAgIC8vIE1hcmsgYXMgY29tcGxldGVkIGluIG91ciBzdGF0ZVxuICAgICAgICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgdHhJZDogc2lnLnNpZ25hdHVyZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gc2lnLnNpZ25hdHVyZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBJZiB3ZSBjYW4ndCBmaW5kIHRoZSBleGFjdCBzaWduYXR1cmUsIGNyZWF0ZSBhIHBsYWNlaG9sZGVyXG4gICAgICAgIGNvbnN0IHBsYWNlaG9sZGVyVHhJZCA9IGBleGlzdGluZy0ke2VzY3Jvd1R4RGF0YS5lc2Nyb3dJZH1gO1xuICAgICAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICB0eElkOiBwbGFjZWhvbGRlclR4SWRcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBwbGFjZWhvbGRlclR4SWQ7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoY2hlY2tFcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCBjaGVjayBmb3IgZXhpc3RpbmcgcHVyY2hhc2UgYWNjb3VudDonLCBjaGVja0Vycm9yKTtcbiAgICAgIC8vIENvbnRpbnVlIHdpdGggbm9ybWFsIGZsb3cgaWYgY2hlY2sgZmFpbHNcbiAgICB9XG5cbiAgICAvLyBNYXJrIHRyYW5zYWN0aW9uIGFzIHBlbmRpbmdcbiAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSk7XG5cbiAgICBjb25zdCB7XG4gICAgICBlc2Nyb3dJZCxcbiAgICAgIHNvbEFtb3VudCxcbiAgICAgIHBlcmtUb2tlbkFtb3VudCxcbiAgICAgIHB1cmNoYXNlUGRhLFxuICAgICAgdmF1bHRQZGEsXG4gICAgICBwdXJjaGFzZVRva2VuTWludCxcbiAgICAgIHBlcmtUb2tlbk1pbnQsXG4gICAgICBidXllcldhbGxldFxuICAgIH0gPSBlc2Nyb3dUeERhdGE7XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgZXNjcm93IGJ1eSB0cmFuc2FjdGlvbjonLCBlc2Nyb3dUeERhdGEpO1xuXG4gICAgLy8gVmFsaWRhdGUgdGhhdCBlc2Nyb3dJZCBpcyBudW1lcmljXG4gICAgY29uc3QgZXNjcm93SWRTdHIgPSBTdHJpbmcoZXNjcm93SWQpO1xuICAgIGlmICghL15cXGQrJC8udGVzdChlc2Nyb3dJZFN0cikpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcihgSW52YWxpZCBlc2Nyb3cgSUQgZm9ybWF0OiAke2VzY3Jvd0lkfS4gRXhwZWN0ZWQgbnVtZXJpYyB2YWx1ZS5gLCBcIklOVkFMSURfREFUQVwiKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBidXllcidzIG5hdGl2ZSBTT0wgYmFsYW5jZSBmaXJzdFxuICAgIGNvbnN0IGJ1eWVyQmFsYW5jZSA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0QmFsYW5jZShuZXcgUHVibGljS2V5KGJ1eWVyV2FsbGV0KSk7XG4gICAgY29uc29sZS5sb2coYEJ1eWVyIG5hdGl2ZSBTT0wgYmFsYW5jZTogJHtidXllckJhbGFuY2UgLyAxXzAwMF8wMDBfMDAwfSBTT0wgKCR7YnV5ZXJCYWxhbmNlfSBsYW1wb3J0cylgKTtcbiAgICBjb25zb2xlLmxvZyhgUmVxdWlyZWQgYW1vdW50OiAke051bWJlcihzb2xBbW91bnQpIC8gMV8wMDBfMDAwXzAwMH0gU09MICgke3NvbEFtb3VudH0gbGFtcG9ydHMpYCk7XG5cbiAgICAvLyBDb252ZXJ0IHZhbHVlc1xuICAgIGNvbnN0IGVzY3Jvd0lkQk4gPSBuZXcgYW5jaG9yLkJOKGVzY3Jvd0lkU3RyKTtcbiAgICBjb25zdCBzb2xBbW91bnRCTiA9IG5ldyBhbmNob3IuQk4oc29sQW1vdW50KTtcbiAgICBjb25zdCBwZXJrVG9rZW5BbW91bnRCTiA9IG5ldyBhbmNob3IuQk4ocGVya1Rva2VuQW1vdW50KTtcblxuICAgIC8vIENyZWF0ZSBwdWJsaWMga2V5c1xuICAgIGNvbnN0IGJ1eWVyUHVia2V5ID0gbmV3IFB1YmxpY0tleShidXllcldhbGxldCk7XG4gICAgY29uc3QgcHVyY2hhc2VUb2tlbk1pbnRQdWJrZXkgPSBuZXcgUHVibGljS2V5KHB1cmNoYXNlVG9rZW5NaW50KTtcbiAgICBjb25zdCBwZXJrVG9rZW5NaW50UHVia2V5ID0gbmV3IFB1YmxpY0tleShwZXJrVG9rZW5NaW50KTtcbiAgICBjb25zdCBwdXJjaGFzZVBkYVB1YmtleSA9IG5ldyBQdWJsaWNLZXkocHVyY2hhc2VQZGEpO1xuICAgIGNvbnN0IHZhdWx0UGRhUHVia2V5ID0gbmV3IFB1YmxpY0tleSh2YXVsdFBkYSk7XG5cbiAgICAvLyBHZXQgYnV5ZXIncyB0b2tlbiBhY2NvdW50XG4gICAgY29uc3QgYnV5ZXJUb2tlbkFjY291bnQgPSBnZXRBc3NvY2lhdGVkVG9rZW5BZGRyZXNzU3luYyhcbiAgICAgIHB1cmNoYXNlVG9rZW5NaW50UHVia2V5LFxuICAgICAgYnV5ZXJQdWJrZXlcbiAgICApO1xuXG4gICAgLy8gQ2hlY2sgaWYgYnV5ZXIncyBBVEEgZXhpc3RzIGFuZCBjcmVhdGUgaWYgbmVlZGVkXG4gICAgY29uc3QgYnV5ZXJBdGFJbmZvID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhidXllclRva2VuQWNjb3VudCk7XG5cbiAgICBsZXQgcHJlSW5zdHJ1Y3Rpb25zID0gW107XG5cbiAgICAvLyBDaGVjayBpZiB0aGlzIGlzIHdyYXBwZWQgU09MXG4gICAgY29uc3QgaXNXcmFwcGVkU09MID0gcHVyY2hhc2VUb2tlbk1pbnRQdWJrZXkuZXF1YWxzKG5ldyBQdWJsaWNLZXkoJ1NvMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTInKSk7XG5cbiAgICBpZiAoIWJ1eWVyQXRhSW5mbykge1xuICAgICAgY29uc29sZS5sb2coJ0NyZWF0aW5nIEFUQSBmb3IgYnV5ZXI6JywgYnV5ZXJUb2tlbkFjY291bnQudG9TdHJpbmcoKSk7XG5cbiAgICAgIC8vIENyZWF0ZSBhc3NvY2lhdGVkIHRva2VuIGFjY291bnQgaW5zdHJ1Y3Rpb25cbiAgICAgIGNvbnN0IGNyZWF0ZUF0YUl4ID0gY3JlYXRlQXNzb2NpYXRlZFRva2VuQWNjb3VudEluc3RydWN0aW9uKFxuICAgICAgICBidXllclB1YmtleSwgLy8gcGF5ZXJcbiAgICAgICAgYnV5ZXJUb2tlbkFjY291bnQsIC8vIGF0YVxuICAgICAgICBidXllclB1YmtleSwgLy8gb3duZXJcbiAgICAgICAgcHVyY2hhc2VUb2tlbk1pbnRQdWJrZXkgLy8gbWludFxuICAgICAgKTtcblxuICAgICAgcHJlSW5zdHJ1Y3Rpb25zLnB1c2goY3JlYXRlQXRhSXgpO1xuICAgIH1cblxuICAgIC8vIElmIGl0J3Mgd3JhcHBlZCBTT0wsIHdlIG5lZWQgdG8gZW5zdXJlIHRoZSBBVEEgaGFzIGVub3VnaCB3cmFwcGVkIFNPTFxuICAgIGlmIChpc1dyYXBwZWRTT0wpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdIYW5kbGluZyB3cmFwcGVkIFNPTCB0cmFuc2FjdGlvbicpO1xuXG4gICAgICAvLyBGb3Igd3JhcHBlZCBTT0wsIHdlIGFsd2F5cyBuZWVkIHRvIGZ1bmQgdGhlIGFjY291bnQgd2l0aCB0aGUgZXhhY3QgYW1vdW50XG4gICAgICAvLyBQbHVzIHNvbWUgZXh0cmEgZm9yIHJlbnQgYW5kIGZlZXNcbiAgICAgIGNvbnN0IHJlcXVpcmVkQW1vdW50ID0gTnVtYmVyKHNvbEFtb3VudCk7XG4gICAgICBjb25zdCByZW50RXhlbXB0aW9uID0gYXdhaXQgY29ubmVjdGlvbi5nZXRNaW5pbXVtQmFsYW5jZUZvclJlbnRFeGVtcHRpb24oMTY1KTsgLy8gVG9rZW4gYWNjb3VudCBzaXplXG4gICAgICBjb25zdCB0b3RhbEFtb3VudE5lZWRlZCA9IHJlcXVpcmVkQW1vdW50ICsgcmVudEV4ZW1wdGlvbjtcblxuICAgICAgY29uc29sZS5sb2coYFJlcXVpcmVkIHdyYXBwZWQgU09MOiAke3JlcXVpcmVkQW1vdW50fSBsYW1wb3J0c2ApO1xuICAgICAgY29uc29sZS5sb2coYFJlbnQgZXhlbXB0aW9uOiAke3JlbnRFeGVtcHRpb259IGxhbXBvcnRzYCk7XG4gICAgICBjb25zb2xlLmxvZyhgVG90YWwgYW1vdW50IG5lZWRlZDogJHt0b3RhbEFtb3VudE5lZWRlZH0gbGFtcG9ydHNgKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgd2UgaGF2ZSBlbm91Z2ggbmF0aXZlIFNPTFxuICAgICAgaWYgKGJ1eWVyQmFsYW5jZSA8IHRvdGFsQW1vdW50TmVlZGVkICsgNTAwMCkgeyAvLyBFeHRyYSA1MDAwIGxhbXBvcnRzIGZvciB0cmFuc2FjdGlvbiBmZWVzXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSW5zdWZmaWNpZW50IFNPTCBiYWxhbmNlLiBSZXF1aXJlZDogJHsodG90YWxBbW91bnROZWVkZWQgKyA1MDAwKSAvIDFfMDAwXzAwMF8wMDB9IFNPTCwgQXZhaWxhYmxlOiAke2J1eWVyQmFsYW5jZSAvIDFfMDAwXzAwMF8wMDB9IFNPTGApO1xuICAgICAgfVxuXG4gICAgICAvLyBUcmFuc2ZlciBuYXRpdmUgU09MIHRvIHRoZSB3cmFwcGVkIFNPTCBhY2NvdW50IHRvIGZ1bmQgaXRcbiAgICAgIGNvbnN0IHRyYW5zZmVySXggPSBhbmNob3Iud2ViMy5TeXN0ZW1Qcm9ncmFtLnRyYW5zZmVyKHtcbiAgICAgICAgZnJvbVB1YmtleTogYnV5ZXJQdWJrZXksXG4gICAgICAgIHRvUHVia2V5OiBidXllclRva2VuQWNjb3VudCxcbiAgICAgICAgbGFtcG9ydHM6IHRvdGFsQW1vdW50TmVlZGVkLFxuICAgICAgfSk7XG5cbiAgICAgIHByZUluc3RydWN0aW9ucy5wdXNoKHRyYW5zZmVySXgpO1xuXG4gICAgICAvLyBTeW5jIG5hdGl2ZSBpbnN0cnVjdGlvbiB0byBjb252ZXJ0IHRoZSB0cmFuc2ZlcnJlZCBTT0wgdG8gd3JhcHBlZCBTT0xcbiAgICAgIGNvbnN0IHN5bmNJeCA9IGNyZWF0ZVN5bmNOYXRpdmVJbnN0cnVjdGlvbihidXllclRva2VuQWNjb3VudCk7XG4gICAgICBwcmVJbnN0cnVjdGlvbnMucHVzaChzeW5jSXgpO1xuXG4gICAgICBjb25zb2xlLmxvZygnQWRkZWQgaW5zdHJ1Y3Rpb25zIHRvIHdyYXAgU09MJyk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSB0cmFuc2FjdGlvblxuICAgIGNvbnN0IHR4ID0gYXdhaXQgcHJvZ3JhbS5tZXRob2RzXG4gICAgICAuZXNjcm93QnV5KGVzY3Jvd0lkQk4sIHNvbEFtb3VudEJOLCBwZXJrVG9rZW5BbW91bnRCTilcbiAgICAgIC5hY2NvdW50cyh7XG4gICAgICAgIHNpZ25lcjogYnV5ZXJQdWJrZXksXG4gICAgICAgIHB1cmNoYXNlVG9rZW5NaW50OiBwdXJjaGFzZVRva2VuTWludFB1YmtleSxcbiAgICAgICAgcGVya1Rva2VuTWludDogcGVya1Rva2VuTWludFB1YmtleSxcbiAgICAgICAgcHVyY2hhc2VUb2tlbkF0YTogYnV5ZXJUb2tlbkFjY291bnQsXG4gICAgICAgIHB1cmNoYXNlOiBwdXJjaGFzZVBkYVB1YmtleSxcbiAgICAgICAgdmF1bHQ6IHZhdWx0UGRhUHVia2V5LFxuICAgICAgICBhc3NvY2lhdGVkVG9rZW5Qcm9ncmFtOiBBU1NPQ0lBVEVEX1RPS0VOX1BST0dSQU1fSUQsXG4gICAgICAgIHRva2VuUHJvZ3JhbTogVE9LRU5fUFJPR1JBTV9JRCxcbiAgICAgICAgc3lzdGVtUHJvZ3JhbTogYW5jaG9yLndlYjMuU3lzdGVtUHJvZ3JhbS5wcm9ncmFtSWQsXG4gICAgICB9KVxuICAgICAgLnByZUluc3RydWN0aW9ucyhwcmVJbnN0cnVjdGlvbnMpXG4gICAgICAudHJhbnNhY3Rpb24oKTtcblxuICAgIC8vIEdldCBmcmVzaCBibG9ja2hhc2ggdG8gZW5zdXJlIHRyYW5zYWN0aW9uIHVuaXF1ZW5lc3NcbiAgICBjb25zdCB7IGJsb2NraGFzaCwgbGFzdFZhbGlkQmxvY2tIZWlnaHQgfSA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0TGF0ZXN0QmxvY2toYXNoKCdmaW5hbGl6ZWQnKTtcbiAgICB0eC5yZWNlbnRCbG9ja2hhc2ggPSBibG9ja2hhc2g7XG4gICAgdHguZmVlUGF5ZXIgPSBidXllclB1YmtleTtcblxuICAgIC8vIEFkZCBhIHVuaXF1ZSBtZW1vIGluc3RydWN0aW9uIHRvIHByZXZlbnQgZHVwbGljYXRlIHRyYW5zYWN0aW9uc1xuICAgIGNvbnN0IG1lbW9JbnN0cnVjdGlvbiA9IG5ldyBhbmNob3Iud2ViMy5UcmFuc2FjdGlvbkluc3RydWN0aW9uKHtcbiAgICAgIGtleXM6IFtdLFxuICAgICAgcHJvZ3JhbUlkOiBuZXcgUHVibGljS2V5KFwiTWVtb1NxNGdxQUJBWEtiOTZxbkg4VHlzTmNXeE15V0NxWGdETEdtZmNIclwiKSxcbiAgICAgIGRhdGE6IEJ1ZmZlci5mcm9tKGBlc2Nyb3ctYnV5LSR7ZXNjcm93VHhEYXRhLmVzY3Jvd0lkfS0ke0RhdGUubm93KCl9YCwgJ3V0Zi04JylcbiAgICB9KTtcbiAgICB0eC5hZGQobWVtb0luc3RydWN0aW9uKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFRyYW5zYWN0aW9uIGRldGFpbHM6Jywge1xuICAgICAgZXNjcm93SWQ6IGVzY3Jvd1R4RGF0YS5lc2Nyb3dJZCxcbiAgICAgIGJsb2NraGFzaDogYmxvY2toYXNoLnNsaWNlKDAsIDgpICsgJy4uLicsXG4gICAgICBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0pO1xuXG4gICAgLy8gU2lnbiB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCBzaWduZWRUeCA9IGF3YWl0IHdhbGxldC5zaWduVHJhbnNhY3Rpb24odHgpO1xuXG4gICAgY29uc29sZS5sb2coJ/CflI0gU2VuZGluZyB0cmFuc2FjdGlvbi4uLicpO1xuXG4gICAgLy8gU2VuZCB0aGUgdHJhbnNhY3Rpb24gd2l0aCByZXRyeSBsb2dpY1xuICAgIGNvbnN0IHR4SWQgPSBhd2FpdCBzZW5kVHJhbnNhY3Rpb25XaXRoUmV0cnkoY29ubmVjdGlvbiwgc2lnbmVkVHgsIDIsIDE1MDApO1xuXG4gICAgY29uc29sZS5sb2coJ+KchSBUcmFuc2FjdGlvbiBzZW50OicsIHR4SWQpO1xuXG4gICAgLy8gQ29uZmlybSB0aGUgdHJhbnNhY3Rpb24gd2l0aCB0aW1lb3V0XG4gICAgY29uc3QgY29uZmlybWF0aW9uID0gYXdhaXQgY29ubmVjdGlvbi5jb25maXJtVHJhbnNhY3Rpb24oe1xuICAgICAgc2lnbmF0dXJlOiB0eElkLFxuICAgICAgYmxvY2toYXNoOiB0eC5yZWNlbnRCbG9ja2hhc2ghLFxuICAgICAgbGFzdFZhbGlkQmxvY2tIZWlnaHQ6IGxhc3RWYWxpZEJsb2NrSGVpZ2h0XG4gICAgfSwgJ2NvbmZpcm1lZCcpO1xuXG4gICAgaWYgKGNvbmZpcm1hdGlvbi52YWx1ZS5lcnIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVHJhbnNhY3Rpb24gZmFpbGVkOiAke2NvbmZpcm1hdGlvbi52YWx1ZS5lcnJ9YCk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ+KchSBFc2Nyb3cgYnV5IHRyYW5zYWN0aW9uIGNvbmZpcm1lZDonLCB0eElkKTtcblxuICAgIC8vIE1hcmsgdHJhbnNhY3Rpb24gYXMgY29tcGxldGVkXG4gICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgdHhJZFxuICAgIH0pO1xuXG4gICAgLy8gU21hbGwgZGVsYXkgdG8gZW5zdXJlIHRyYW5zYWN0aW9uIGlzIGZ1bGx5IHByb2Nlc3NlZFxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG5cbiAgICByZXR1cm4gdHhJZDtcblxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignRXNjcm93IGJ1eSB0cmFuc2FjdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuXG4gICAgLy8gT25seSBtYXJrIGFzIGZhaWxlZCBpZiBpdCdzIG5vdCBhbiBcImFscmVhZHkgcHJvY2Vzc2VkXCIgZXJyb3JcbiAgICAvLyAod2hpY2ggbWlnaHQgYWN0dWFsbHkgYmUgc3VjY2Vzc2Z1bClcbiAgICBpZiAoIWVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykpIHtcbiAgICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93VHhEYXRhLmVzY3Jvd0lkfS0ke3dhbGxldC5hZGRyZXNzfS1idXlgO1xuICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgIHN0YXR1czogJ2ZhaWxlZCcsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gRW5oYW5jZWQgZXJyb3IgaGFuZGxpbmcgZm9yIFNvbGFuYSB0cmFuc2FjdGlvbnNcbiAgICBpZiAoZXJyb3IubG9ncykge1xuICAgICAgY29uc29sZS5lcnJvcignVHJhbnNhY3Rpb24gbG9nczonLCBlcnJvci5sb2dzKTtcbiAgICB9XG5cbiAgICAvLyBJZiBpdCdzIGFscmVhZHkgYW4gRXNjcm93RXJyb3IsIHJlLXRocm93IGl0XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXNjcm93RXJyb3IpIHtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSBzcGVjaWZpYyBlcnJvciB0eXBlcyB3aXRoIGVuaGFuY2VkIGVycm9yIG1lc3NhZ2VzXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykpIHtcbiAgICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93VHhEYXRhLmVzY3Jvd0lkfS0ke3dhbGxldC5hZGRyZXNzfS1idXlgO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBUcmFuc2FjdGlvbiBhbHJlYWR5IHByb2Nlc3NlZCBlcnJvciAtIGNoZWNraW5nIGlmIGVzY3JvdyB3YXMgYWN0dWFsbHkgY3JlYXRlZC4uLicpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgcHVyY2hhc2UgYWNjb3VudCB3YXMgYWN0dWFsbHkgY3JlYXRlZCAobWVhbmluZyB0cmFuc2FjdGlvbiB3YXMgc3VjY2Vzc2Z1bClcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBwcm9ncmFtLnByb3ZpZGVyLmNvbm5lY3Rpb247XG4gICAgICAgIGNvbnN0IGVzY3Jvd0lkQk4gPSBuZXcgYW5jaG9yLkJOKGVzY3Jvd1R4RGF0YS5lc2Nyb3dJZCk7XG4gICAgICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhwdXJjaGFzZVBkYSk7XG4gICAgICAgIGlmIChwdXJjaGFzZUFjY291bnQpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFB1cmNoYXNlIGFjY291bnQgZXhpc3RzIC0gdHJhbnNhY3Rpb24gd2FzIGFjdHVhbGx5IHN1Y2Nlc3NmdWwhJyk7XG5cbiAgICAgICAgICAvLyBUcnkgdG8gZmluZCB0aGUgdHJhbnNhY3Rpb24gc2lnbmF0dXJlIGluIHJlY2VudCBoaXN0b3J5XG4gICAgICAgICAgY29uc3QgYnV5ZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KHdhbGxldC5hZGRyZXNzKTtcbiAgICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gYXdhaXQgY29ubmVjdGlvbi5nZXRTaWduYXR1cmVzRm9yQWRkcmVzcyhidXllclB1YmtleSwgeyBsaW1pdDogMTAgfSk7XG5cbiAgICAgICAgICBsZXQgZm91bmRUeElkID0gbnVsbDtcbiAgICAgICAgICBmb3IgKGNvbnN0IHNpZyBvZiBzaWduYXR1cmVzKSB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHNpZ25hdHVyZSBpcyByZWNlbnQgKHdpdGhpbiBsYXN0IDUgbWludXRlcylcbiAgICAgICAgICAgIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gICAgICAgICAgICBpZiAoc2lnLmJsb2NrVGltZSAmJiBzaWcuYmxvY2tUaW1lICogMTAwMCA+IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgICAgIGZvdW5kVHhJZCA9IHNpZy5zaWduYXR1cmU7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghZm91bmRUeElkKSB7XG4gICAgICAgICAgICBmb3VuZFR4SWQgPSBgc3VjY2Vzc2Z1bC0ke2VzY3Jvd1R4RGF0YS5lc2Nyb3dJZH0tJHtEYXRlLm5vdygpfWA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gTWFyayBhcyBjb21wbGV0ZWQgaW4gb3VyIHN0YXRlXG4gICAgICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgdHhJZDogZm91bmRUeElkXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+OiSBSZXR1cm5pbmcgc3VjY2Vzc2Z1bCB0cmFuc2FjdGlvbiBJRDonLCBmb3VuZFR4SWQpO1xuICAgICAgICAgIHJldHVybiBmb3VuZFR4SWQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KdjCBQdXJjaGFzZSBhY2NvdW50IGRvZXMgbm90IGV4aXN0IC0gdHJhbnNhY3Rpb24gYWN0dWFsbHkgZmFpbGVkJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGNoZWNrRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCB2ZXJpZnkgcHVyY2hhc2UgYWNjb3VudDonLCBjaGVja0Vycm9yKTtcbiAgICAgIH1cblxuICAgICAgdGhyb3cgbmV3IER1cGxpY2F0ZVRyYW5zYWN0aW9uRXJyb3IoJ1RyYW5zYWN0aW9uIGFscmVhZHkgcHJvY2Vzc2VkLiBQbGVhc2UgcmVmcmVzaCB0aGUgcGFnZSBhbmQgdHJ5IGFnYWluLicpO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSBzcGVjaWZpYyBTb2xhbmEvYmxvY2tjaGFpbiBlcnJvcnNcbiAgICBpZiAoZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ0FjY291bnROb3RJbml0aWFsaXplZCcpKSB7XG4gICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoJ1Rva2VuIGFjY291bnQgbm90IGluaXRpYWxpemVkLiBQbGVhc2UgZW5zdXJlIHlvdSBoYXZlIHRoZSByZXF1aXJlZCB0b2tlbnMuJywgJ0FDQ09VTlRfTk9UX0lOSVRJQUxJWkVEJyk7XG4gICAgfVxuXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdJbnN1ZmZpY2llbnRGdW5kcycpIHx8IGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdpbnN1ZmZpY2llbnQgZnVuZHMnKSkge1xuICAgICAgdGhyb3cgbmV3IEluc3VmZmljaWVudEZ1bmRzRXJyb3IoJ0luc3VmZmljaWVudCBmdW5kcyB0byBjb21wbGV0ZSB0aGUgdHJhbnNhY3Rpb24uJyk7XG4gICAgfVxuXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdibG9ja2hhc2ggbm90IGZvdW5kJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1RyYW5zYWN0aW9uIGV4cGlyZWQnKSkge1xuICAgICAgdGhyb3cgbmV3IFRyYW5zYWN0aW9uRXJyb3IoJ1RyYW5zYWN0aW9uIGV4cGlyZWQuIFBsZWFzZSB0cnkgYWdhaW4gd2l0aCBhIGZyZXNoIHRyYW5zYWN0aW9uLicpO1xuICAgIH1cblxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnVXNlciByZWplY3RlZCcpIHx8IGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCd1c2VyIHJlamVjdGVkJykgfHwgZXJyb3IuY29kZSA9PT0gNDAwMSkge1xuICAgICAgdGhyb3cgbmV3IEVzY3Jvd0Vycm9yKCdUcmFuc2FjdGlvbiB3YXMgcmVqZWN0ZWQgYnkgdXNlcicsICdVU0VSX1JFSkVDVEVEJyk7XG4gICAgfVxuXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdOZXR3b3JrIEVycm9yJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ0ZhaWxlZCB0byBmZXRjaCcpKSB7XG4gICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoJ05ldHdvcmsgY29ubmVjdGlvbiBmYWlsZWQuIFBsZWFzZSBjaGVjayB5b3VyIGludGVybmV0IGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2Fpbi4nLCAnTkVUV09SS19FUlJPUicpO1xuICAgIH1cblxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnU2ltdWxhdGlvbiBmYWlsZWQnKSkge1xuICAgICAgdGhyb3cgbmV3IFRyYW5zYWN0aW9uRXJyb3IoJ1RyYW5zYWN0aW9uIHNpbXVsYXRpb24gZmFpbGVkLiBUaGlzIG1heSBiZSBkdWUgdG8gaW5zdWZmaWNpZW50IGZ1bmRzIG9yIGludmFsaWQgdHJhbnNhY3Rpb24gcGFyYW1ldGVycy4nKTtcbiAgICB9XG5cbiAgICAvLyBIYW5kbGUgUlBDIGVycm9yc1xuICAgIGlmIChlcnJvci5jb2RlICYmIHR5cGVvZiBlcnJvci5jb2RlID09PSAnbnVtYmVyJykge1xuICAgICAgaWYgKGVycm9yLmNvZGUgPT09IC0zMjAwMikge1xuICAgICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoJ1JQQyByZXF1ZXN0IGZhaWxlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nLCAnUlBDX0VSUk9SJyk7XG4gICAgICB9XG4gICAgICBpZiAoZXJyb3IuY29kZSA9PT0gLTMyMDAzKSB7XG4gICAgICAgIHRocm93IG5ldyBEdXBsaWNhdGVUcmFuc2FjdGlvbkVycm9yKCdUcmFuc2FjdGlvbiBhbHJlYWR5IHByb2Nlc3NlZC4nKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBHZW5lcmljIGJsb2NrY2hhaW4gZXJyb3JcbiAgICBpZiAoZXJyb3IubmFtZSA9PT0gJ1NlbmRUcmFuc2FjdGlvbkVycm9yJyB8fCBlcnJvci5uYW1lID09PSAnVHJhbnNhY3Rpb25FcnJvcicpIHtcbiAgICAgIHRocm93IG5ldyBCbG9ja2NoYWluRXJyb3IoYEJsb2NrY2hhaW4gdHJhbnNhY3Rpb24gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCwgZXJyb3IpO1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIGZvciB1bmtub3duIGVycm9yc1xuICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcihlcnJvci5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkIGR1cmluZyB0aGUgZXNjcm93IHRyYW5zYWN0aW9uJywgJ1VOS05PV05fRVJST1InKTtcbiAgfVxufVxuXG4vKipcbiAqIENyZWF0ZSBhbmQgc2lnbiBlc2Nyb3cgYWNjZXB0IHRyYW5zYWN0aW9uIChmb3Igc2VsbGVycylcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUVzY3Jvd0FjY2VwdFRyYW5zYWN0aW9uKFxuICBlc2Nyb3dJZDogc3RyaW5nLFxuICBwdXJjaGFzZVRva2VuQW1vdW50OiBzdHJpbmcsXG4gIHBlcmtUb2tlbkFtb3VudDogc3RyaW5nLFxuICBwdXJjaGFzZVRva2VuTWludDogc3RyaW5nLFxuICBwZXJrVG9rZW5NaW50OiBzdHJpbmcsXG4gIHNlbGxlcldhbGxldDogc3RyaW5nLFxuICB3YWxsZXQ6IGFueSxcbiAgdHJhZGVJZD86IHN0cmluZyB8IG51bWJlclxuKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgdHJ5IHtcbiAgICAvLyBFbmhhbmNlZCB3YWxsZXQgdmFsaWRhdGlvblxuICAgIGNvbnN0IHdhbGxldFZhbGlkYXRpb24gPSB2YWxpZGF0ZVdhbGxldENvbm5lY3Rpb24od2FsbGV0KTtcbiAgICBpZiAoIXdhbGxldFZhbGlkYXRpb24udmFsaWQpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcih3YWxsZXRWYWxpZGF0aW9uLmVycm9yISwgXCJXQUxMRVRfRVJST1JcIik7XG4gICAgfVxuXG4gICAgLy8gSWYgdHJhZGVJZCBpcyBwcm92aWRlZCwgZmV0Y2ggdHJhZGUgZGV0YWlscyBhbmQgY2FsY3VsYXRlIGFtb3VudHNcbiAgICBsZXQgY2FsY3VsYXRlZFB1cmNoYXNlQW1vdW50ID0gcHVyY2hhc2VUb2tlbkFtb3VudDtcbiAgICBsZXQgY2FsY3VsYXRlZFBlcmtBbW91bnQgPSBwZXJrVG9rZW5BbW91bnQ7XG4gICAgbGV0IGNhbGN1bGF0ZWRQdXJjaGFzZU1pbnQgPSBwdXJjaGFzZVRva2VuTWludDtcbiAgICBsZXQgY2FsY3VsYXRlZFBlcmtNaW50ID0gcGVya1Rva2VuTWludDtcbiAgICBsZXQgY2FsY3VsYXRlZEVzY3Jvd0lkID0gZXNjcm93SWQ7XG5cbiAgICBpZiAodHJhZGVJZCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0VzY3Jvd0FjY2VwdF0gRmV0Y2hpbmcgdHJhZGUgZGF0YSBmb3IgdHJhZGVJZDonLCB0cmFkZUlkKTtcbiAgICAgICAgY29uc3QgeyBnZXRUcmFkZURldGFpbHMgfSA9IGF3YWl0IGltcG9ydCgnLi4vYXhpb3MvcmVxdWVzdHMnKTtcbiAgICAgICAgY29uc3QgdHJhZGVSZXNwb25zZSA9IGF3YWl0IGdldFRyYWRlRGV0YWlscyhOdW1iZXIodHJhZGVJZCkpO1xuICAgICAgICBjb25zdCB0cmFkZURhdGEgPSB0cmFkZVJlc3BvbnNlLmRhdGE7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBbRXNjcm93QWNjZXB0XSBUcmFkZSBkYXRhIGZldGNoZWQgc3VjY2Vzc2Z1bGx5OicsIHtcbiAgICAgICAgICB0cmFkZUlkOiB0cmFkZURhdGEuaWQsXG4gICAgICAgICAgc3RhdHVzOiB0cmFkZURhdGEuc3RhdHVzLFxuICAgICAgICAgIGVzY3Jvd1R4SWQ6IHRyYWRlRGF0YS5lc2Nyb3dUeElkLFxuICAgICAgICAgIGFtb3VudDogdHJhZGVEYXRhLmFtb3VudCxcbiAgICAgICAgICBwcmljZTogdHJhZGVEYXRhLnByaWNlXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFVzZSB0cmFkZSBkYXRhIHRvIGdldCB0aGUgbnVtZXJpYyBlc2Nyb3dJZFxuICAgICAgICAvLyBGaXJzdCB0cnkgdG8gZ2V0IGl0IGZyb20gdGhlIGV4dHJhIGZpZWxkLCB0aGVuIGZhbGwgYmFjayB0byBvcmlnaW5hbCBlc2Nyb3dJZFxuICAgICAgICBsZXQgbnVtZXJpY0VzY3Jvd0lkID0gbnVsbDtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBpZiAodHJhZGVEYXRhLmV4dHJhKSB7XG4gICAgICAgICAgICBjb25zdCBleHRyYURhdGEgPSBKU09OLnBhcnNlKHRyYWRlRGF0YS5leHRyYSk7XG4gICAgICAgICAgICBudW1lcmljRXNjcm93SWQgPSBleHRyYURhdGEuZXNjcm93SWQ7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCBwYXJzZSBleHRyYSBkYXRhOicsIGUpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gT25seSB1c2UgbnVtZXJpY0VzY3Jvd0lkIGlmIGl0J3MgYWN0dWFsbHkgbnVtZXJpYywgb3RoZXJ3aXNlIHVzZSBvcmlnaW5hbCBlc2Nyb3dJZFxuICAgICAgICAvLyBOZXZlciB1c2UgZXNjcm93VHhJZCBhcyBpdCdzIGEgdHJhbnNhY3Rpb24gc2lnbmF0dXJlLCBub3QgYSBudW1lcmljIElEXG4gICAgICAgIGNhbGN1bGF0ZWRFc2Nyb3dJZCA9IG51bWVyaWNFc2Nyb3dJZCB8fCBlc2Nyb3dJZDtcblxuICAgICAgICAvLyBDYWxjdWxhdGUgU09MIGFtb3VudCBpbiBsYW1wb3J0c1xuICAgICAgICBjb25zdCBzb2xBbW91bnQgPSB0cmFkZURhdGEuYW1vdW50IHx8IDA7XG4gICAgICAgIGNhbGN1bGF0ZWRQdXJjaGFzZUFtb3VudCA9IChzb2xBbW91bnQgKiAxZTkpLnRvU3RyaW5nKCk7XG5cbiAgICAgICAgLy8gQ2FsY3VsYXRlIHBlcmsgdG9rZW4gYW1vdW50IGJhc2VkIG9uIFNPTCBhbW91bnQgYW5kIHBlcmsgcHJpY2VcbiAgICAgICAgY29uc3QgcGVya1ByaWNlID0gdHJhZGVEYXRhLnBlcmtEZXRhaWxzPy5wcmljZSB8fCAwLjAwMjtcbiAgICAgICAgY29uc3QgcGVya1Rva2Vuc1RvUmVjZWl2ZSA9IHNvbEFtb3VudCAvIHBlcmtQcmljZTtcbiAgICAgICAgY2FsY3VsYXRlZFBlcmtBbW91bnQgPSBNYXRoLmZsb29yKHBlcmtUb2tlbnNUb1JlY2VpdmUgKiAxZTYpLnRvU3RyaW5nKCk7XG5cbiAgICAgICAgLy8gU2V0IHRva2VuIG1pbnRzXG4gICAgICAgIGNhbGN1bGF0ZWRQdXJjaGFzZU1pbnQgPSAnU28xMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMic7IC8vIFNPTCBtaW50XG4gICAgICAgIGNhbGN1bGF0ZWRQZXJrTWludCA9IHRyYWRlRGF0YS5wZXJrRGV0YWlscz8udG9rZW4/LnRva2VuQWRkcmVzcyB8fCBwZXJrVG9rZW5NaW50O1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtFc2Nyb3dBY2NlcHRdIENhbGN1bGF0ZWQgYW1vdW50cyBmcm9tIHRyYWRlIGRhdGE6Jywge1xuICAgICAgICAgIGVzY3Jvd0lkOiBjYWxjdWxhdGVkRXNjcm93SWQsXG4gICAgICAgICAgcHVyY2hhc2VBbW91bnQ6IGNhbGN1bGF0ZWRQdXJjaGFzZUFtb3VudCxcbiAgICAgICAgICBwZXJrQW1vdW50OiBjYWxjdWxhdGVkUGVya0Ftb3VudCxcbiAgICAgICAgICBwdXJjaGFzZU1pbnQ6IGNhbGN1bGF0ZWRQdXJjaGFzZU1pbnQsXG4gICAgICAgICAgcGVya01pbnQ6IGNhbGN1bGF0ZWRQZXJrTWludFxuICAgICAgICB9KTtcblxuICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW0VzY3Jvd0FjY2VwdF0gRmFpbGVkIHRvIGZldGNoIHRyYWRlIGRhdGE6JywgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoYEZhaWxlZCB0byBmZXRjaCB0cmFkZSBkYXRhOiAke2Vycm9yLm1lc3NhZ2V9YCwgXCJUUkFERV9EQVRBX0VSUk9SXCIpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGVzY3JvdyBhY2NlcHQgZGF0YVxuICAgIGlmICghY2FsY3VsYXRlZEVzY3Jvd0lkKSB7XG4gICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoXCJFc2Nyb3cgSUQgaXMgcmVxdWlyZWRcIiwgXCJJTlZBTElEX0RBVEFcIik7XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgdGhhdCBlc2Nyb3dJZCBpcyBudW1lcmljXG4gICAgY29uc3QgZXNjcm93SWRTdHIgPSBTdHJpbmcoY2FsY3VsYXRlZEVzY3Jvd0lkKTtcbiAgICBpZiAoIS9eXFxkKyQvLnRlc3QoZXNjcm93SWRTdHIpKSB7XG4gICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoYEludmFsaWQgZXNjcm93IElEIGZvcm1hdDogJHtjYWxjdWxhdGVkRXNjcm93SWR9LiBFeHBlY3RlZCBudW1lcmljIHZhbHVlLmAsIFwiSU5WQUxJRF9EQVRBXCIpO1xuICAgIH1cblxuICAgIGlmICghY2FsY3VsYXRlZFBlcmtBbW91bnQgfHwgTnVtYmVyKGNhbGN1bGF0ZWRQZXJrQW1vdW50KSA8PSAwKSB7XG4gICAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoXCJWYWxpZCBwZXJrIHRva2VuIGFtb3VudCBpcyByZXF1aXJlZFwiLCBcIklOVkFMSURfREFUQVwiKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBjb25uZWN0aW9uIGhlYWx0aCBiZWZvcmUgcHJvY2VlZGluZ1xuICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBwcm9ncmFtLnByb3ZpZGVyLmNvbm5lY3Rpb247XG4gICAgY29uc3QgY29ubmVjdGlvbkhlYWx0aCA9IGF3YWl0IGNoZWNrQ29ubmVjdGlvbkhlYWx0aChjb25uZWN0aW9uKTtcbiAgICBpZiAoIWNvbm5lY3Rpb25IZWFsdGguaGVhbHRoeSkge1xuICAgICAgdGhyb3cgbmV3IEVzY3Jvd0Vycm9yKGBOZXR3b3JrIGNvbm5lY3Rpb24gaXNzdWU6ICR7Y29ubmVjdGlvbkhlYWx0aC5lcnJvcn1gLCBcIk5FVFdPUktfRVJST1JcIik7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHVuaXF1ZSB0cmFuc2FjdGlvbiBrZXkgdG8gcHJldmVudCBkdXBsaWNhdGVzXG4gICAgY29uc3QgdHJhbnNhY3Rpb25LZXkgPSBgJHtlc2Nyb3dJZFN0cn0tJHt3YWxsZXQuYWRkcmVzc30tYWNjZXB0YDtcblxuICAgIC8vIENoZWNrIGZvciByYXBpZCBkdXBsaWNhdGUgYXR0ZW1wdHMgKHdpdGhpbiAzIHNlY29uZHMpXG4gICAgY29uc3QgbGFzdEF0dGVtcHQgPSBsYXN0VHJhbnNhY3Rpb25BdHRlbXB0cy5nZXQodHJhbnNhY3Rpb25LZXkpO1xuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgaWYgKGxhc3RBdHRlbXB0ICYmIChub3cgLSBsYXN0QXR0ZW1wdCkgPCAzMDAwKSB7XG4gICAgICB0aHJvdyBuZXcgRHVwbGljYXRlVHJhbnNhY3Rpb25FcnJvcihcIlBsZWFzZSB3YWl0IGEgbW9tZW50IGJlZm9yZSB0cnlpbmcgYWdhaW4uIFRyYW5zYWN0aW9uIGF0dGVtcHQgdG9vIHNvb24uXCIpO1xuICAgIH1cbiAgICBsYXN0VHJhbnNhY3Rpb25BdHRlbXB0cy5zZXQodHJhbnNhY3Rpb25LZXksIG5vdyk7XG5cbiAgICAvLyBDbGVhbiB1cCBvbGQgdHJhbnNhY3Rpb25zIHBlcmlvZGljYWxseVxuICAgIGNsZWFudXBPbGRUcmFuc2FjdGlvbnMoKTtcblxuICAgIC8vIENoZWNrIGlmIHRyYW5zYWN0aW9uIGlzIGFscmVhZHkgaW4gcHJvZ3Jlc3Mgb3IgY29tcGxldGVkXG4gICAgY29uc3QgZXhpc3RpbmdTdGF0ZSA9IHRyYW5zYWN0aW9uU3RhdGVzLmdldCh0cmFuc2FjdGlvbktleSk7XG4gICAgaWYgKGV4aXN0aW5nU3RhdGUpIHtcbiAgICAgIGlmIChleGlzdGluZ1N0YXRlLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkVzY3JvdyBhY2NlcHQgdHJhbnNhY3Rpb24gYWxyZWFkeSBpbiBwcm9ncmVzcy4gUGxlYXNlIHdhaXQgZm9yIHRoZSBjdXJyZW50IHRyYW5zYWN0aW9uIHRvIGNvbXBsZXRlLlwiKTtcbiAgICAgIH1cbiAgICAgIGlmIChleGlzdGluZ1N0YXRlLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgZXhpc3RpbmdTdGF0ZS50eElkKSB7XG4gICAgICAgIC8vIElmIGNvbXBsZXRlZCByZWNlbnRseSAod2l0aGluIDUgbWludXRlcyksIHJldHVybiBleGlzdGluZyB0eElkXG4gICAgICAgIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gICAgICAgIGlmIChleGlzdGluZ1N0YXRlLnRpbWVzdGFtcCA+IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJSZXR1cm5pbmcgZXhpc3RpbmcgZXNjcm93IGFjY2VwdCB0cmFuc2FjdGlvbiBJRDpcIiwgZXhpc3RpbmdTdGF0ZS50eElkKTtcbiAgICAgICAgICByZXR1cm4gZXhpc3RpbmdTdGF0ZS50eElkO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGVzY3JvdyBoYXMgYWxyZWFkeSBiZWVuIGFjY2VwdGVkIG9uIHRoZSBibG9ja2NoYWluXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIENoZWNraW5nIGlmIGVzY3JvdyBoYXMgYWxyZWFkeSBiZWVuIGFjY2VwdGVkOicsIGVzY3Jvd0lkU3RyKTtcbiAgICAgIGNvbnN0IGVzY3Jvd0lkQk4gPSBuZXcgYW5jaG9yLkJOKGVzY3Jvd0lkU3RyKTtcbiAgICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgICAgW0J1ZmZlci5mcm9tKFwicHVyY2hhc2VcIiksIGVzY3Jvd0lkQk4udG9BcnJheUxpa2UoQnVmZmVyLCBcImxlXCIsIDgpXSxcbiAgICAgICAgcHJvZ3JhbS5wcm9ncmFtSWRcbiAgICAgICk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoZSBwdXJjaGFzZSBhY2NvdW50IGV4aXN0cyBhbmQgaXMgYWxyZWFkeSBhY2NlcHRlZFxuICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgcHJvZ3JhbS5hY2NvdW50LnB1cmNoYXNlLmZldGNoKHB1cmNoYXNlUGRhKTtcbiAgICAgIGlmIChwdXJjaGFzZUFjY291bnQuYWNjZXB0ZWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBFc2Nyb3cgYWxyZWFkeSBhY2NlcHRlZCcpO1xuXG4gICAgICAgIC8vIFRyeSB0byBmaW5kIHRoZSB0cmFuc2FjdGlvbiBzaWduYXR1cmUgaW4gcmVjZW50IGhpc3RvcnlcbiAgICAgICAgY29uc3Qgc2VsbGVyUHVia2V5ID0gbmV3IFB1YmxpY0tleSh3YWxsZXQuYWRkcmVzcyk7XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZXMgPSBhd2FpdCBjb25uZWN0aW9uLmdldFNpZ25hdHVyZXNGb3JBZGRyZXNzKHNlbGxlclB1YmtleSwgeyBsaW1pdDogMjAgfSk7XG4gICAgICAgIGZvciAoY29uc3Qgc2lnIG9mIHNpZ25hdHVyZXMpIHtcbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHNpZ25hdHVyZSBpcyByZWNlbnQgKHdpdGhpbiBsYXN0IDEwIG1pbnV0ZXMpXG4gICAgICAgICAgY29uc3QgdGVuTWludXRlc0FnbyA9IERhdGUubm93KCkgLSAxMCAqIDYwICogMTAwMDtcbiAgICAgICAgICBpZiAoc2lnLmJsb2NrVGltZSAmJiBzaWcuYmxvY2tUaW1lICogMTAwMCA+IHRlbk1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICAgIC8vIE1hcmsgYXMgY29tcGxldGVkIGluIG91ciBzdGF0ZVxuICAgICAgICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgdHhJZDogc2lnLnNpZ25hdHVyZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gc2lnLnNpZ25hdHVyZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBJZiB3ZSBjYW4ndCBmaW5kIHRoZSBleGFjdCBzaWduYXR1cmUsIGNyZWF0ZSBhIHBsYWNlaG9sZGVyXG4gICAgICAgIGNvbnN0IHBsYWNlaG9sZGVyVHhJZCA9IGBhY2NlcHRlZC0ke2VzY3Jvd0lkU3RyfWA7XG4gICAgICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgIHR4SWQ6IHBsYWNlaG9sZGVyVHhJZFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHBsYWNlaG9sZGVyVHhJZDtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChjaGVja0Vycm9yKSB7XG4gICAgICBjb25zb2xlLmxvZygnQ291bGQgbm90IGNoZWNrIGZvciBleGlzdGluZyBhY2NlcHRhbmNlOicsIGNoZWNrRXJyb3IpO1xuICAgICAgLy8gQ29udGludWUgd2l0aCBub3JtYWwgZmxvdyBpZiBjaGVjayBmYWlsc1xuICAgIH1cblxuICAgIC8vIE1hcmsgdHJhbnNhY3Rpb24gYXMgcGVuZGluZ1xuICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCdDcmVhdGluZyBlc2Nyb3cgYWNjZXB0IHRyYW5zYWN0aW9uOicsIHtcbiAgICAgIGVzY3Jvd0lkOiBlc2Nyb3dJZFN0cixcbiAgICAgIHB1cmNoYXNlVG9rZW5BbW91bnQ6IGNhbGN1bGF0ZWRQdXJjaGFzZUFtb3VudCxcbiAgICAgIHBlcmtUb2tlbkFtb3VudDogY2FsY3VsYXRlZFBlcmtBbW91bnQsXG4gICAgICBzZWxsZXJXYWxsZXRcbiAgICB9KTtcblxuICAgIC8vIENvbnZlcnQgdmFsdWVzXG4gICAgY29uc3QgZXNjcm93SWRCTiA9IG5ldyBhbmNob3IuQk4oZXNjcm93SWRTdHIpO1xuICAgIGNvbnN0IHB1cmNoYXNlVG9rZW5BbW91bnRCTiA9IG5ldyBhbmNob3IuQk4oY2FsY3VsYXRlZFB1cmNoYXNlQW1vdW50KTtcbiAgICBjb25zdCBwZXJrVG9rZW5BbW91bnRCTiA9IG5ldyBhbmNob3IuQk4oY2FsY3VsYXRlZFBlcmtBbW91bnQpO1xuXG4gICAgLy8gQ3JlYXRlIHB1YmxpYyBrZXlzXG4gICAgY29uc3Qgc2VsbGVyUHVia2V5ID0gbmV3IFB1YmxpY0tleShzZWxsZXJXYWxsZXQpO1xuICAgIGNvbnN0IHB1cmNoYXNlVG9rZW5NaW50UHVia2V5ID0gbmV3IFB1YmxpY0tleShjYWxjdWxhdGVkUHVyY2hhc2VNaW50KTtcbiAgICBjb25zdCBwZXJrVG9rZW5NaW50UHVia2V5ID0gbmV3IFB1YmxpY0tleShjYWxjdWxhdGVkUGVya01pbnQpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIFBEQXNcbiAgICBjb25zdCBbcHVyY2hhc2VQZGFdID0gUHVibGljS2V5LmZpbmRQcm9ncmFtQWRkcmVzc1N5bmMoXG4gICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgcHJvZ3JhbS5wcm9ncmFtSWRcbiAgICApO1xuXG4gICAgLy8gR2V0IHNlbGxlcidzIHBlcmsgdG9rZW4gYWNjb3VudFxuICAgIGNvbnN0IHNlbGxlclBlcmtUb2tlbkF0YSA9IGdldEFzc29jaWF0ZWRUb2tlbkFkZHJlc3NTeW5jKFxuICAgICAgcGVya1Rva2VuTWludFB1YmtleSxcbiAgICAgIHNlbGxlclB1YmtleVxuICAgICk7XG5cbiAgICAvLyBDYWxjdWxhdGUgdGhlIHBlcmsgdmF1bHQgUERBICh3aGVyZSBzZWxsZXIncyBwZXJrIHRva2VucyB3aWxsIGJlIHN0b3JlZClcbiAgICBjb25zdCBbcGVya1ZhdWx0UGRhXSA9IFB1YmxpY0tleS5maW5kUHJvZ3JhbUFkZHJlc3NTeW5jKFxuICAgICAgW3B1cmNoYXNlUGRhLnRvQnVmZmVyKCksIFRPS0VOX1BST0dSQU1fSUQudG9CdWZmZXIoKSwgcGVya1Rva2VuTWludFB1YmtleS50b0J1ZmZlcigpXSxcbiAgICAgIEFTU09DSUFURURfVE9LRU5fUFJPR1JBTV9JRFxuICAgICk7XG5cbiAgICAvLyBDaGVjayBpZiBzZWxsZXIncyBwZXJrIEFUQSBleGlzdHMgYW5kIGNyZWF0ZSBpZiBuZWVkZWRcbiAgICBjb25zdCBzZWxsZXJQZXJrQXRhSW5mbyA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0QWNjb3VudEluZm8oc2VsbGVyUGVya1Rva2VuQXRhKTtcbiAgICBsZXQgcHJlSW5zdHJ1Y3Rpb25zID0gW107XG5cbiAgICBpZiAoIXNlbGxlclBlcmtBdGFJbmZvKSB7XG4gICAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgcGVyayBBVEEgZm9yIHNlbGxlcjonLCBzZWxsZXJQZXJrVG9rZW5BdGEudG9TdHJpbmcoKSk7XG5cbiAgICAgIC8vIENyZWF0ZSBhc3NvY2lhdGVkIHRva2VuIGFjY291bnQgaW5zdHJ1Y3Rpb25cbiAgICAgIGNvbnN0IGNyZWF0ZUF0YUl4ID0gY3JlYXRlQXNzb2NpYXRlZFRva2VuQWNjb3VudEluc3RydWN0aW9uKFxuICAgICAgICBzZWxsZXJQdWJrZXksIC8vIHBheWVyXG4gICAgICAgIHNlbGxlclBlcmtUb2tlbkF0YSwgLy8gYXRhXG4gICAgICAgIHNlbGxlclB1YmtleSwgLy8gb3duZXJcbiAgICAgICAgcGVya1Rva2VuTWludFB1YmtleSAvLyBtaW50XG4gICAgICApO1xuXG4gICAgICBwcmVJbnN0cnVjdGlvbnMucHVzaChjcmVhdGVBdGFJeCk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSB0cmFuc2FjdGlvblxuICAgIGNvbnN0IHR4ID0gYXdhaXQgcHJvZ3JhbS5tZXRob2RzXG4gICAgICAuZXNjcm93QWNjZXB0KGVzY3Jvd0lkQk4sIHB1cmNoYXNlVG9rZW5BbW91bnRCTiwgcGVya1Rva2VuQW1vdW50Qk4pXG4gICAgICAuYWNjb3VudHMoe1xuICAgICAgICBzaWduZXI6IHNlbGxlclB1YmtleSxcbiAgICAgICAgcHVyY2hhc2VUb2tlbk1pbnQ6IHB1cmNoYXNlVG9rZW5NaW50UHVia2V5LFxuICAgICAgICBwZXJrVG9rZW5NaW50OiBwZXJrVG9rZW5NaW50UHVia2V5LFxuICAgICAgICB0b2tlblByb2dyYW06IFRPS0VOX1BST0dSQU1fSUQsXG4gICAgICB9KVxuICAgICAgLnByZUluc3RydWN0aW9ucyhwcmVJbnN0cnVjdGlvbnMpXG4gICAgICAudHJhbnNhY3Rpb24oKTtcblxuICAgIC8vIEdldCBmcmVzaCBibG9ja2hhc2ggdG8gZW5zdXJlIHRyYW5zYWN0aW9uIHVuaXF1ZW5lc3NcbiAgICBjb25zdCB7IGJsb2NraGFzaCwgbGFzdFZhbGlkQmxvY2tIZWlnaHQgfSA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0TGF0ZXN0QmxvY2toYXNoKCdmaW5hbGl6ZWQnKTtcbiAgICB0eC5yZWNlbnRCbG9ja2hhc2ggPSBibG9ja2hhc2g7XG4gICAgdHguZmVlUGF5ZXIgPSBzZWxsZXJQdWJrZXk7XG5cbiAgICAvLyBBZGQgYSB1bmlxdWUgbWVtbyBpbnN0cnVjdGlvbiB0byBwcmV2ZW50IGR1cGxpY2F0ZSB0cmFuc2FjdGlvbnNcbiAgICBjb25zdCBtZW1vSW5zdHJ1Y3Rpb24gPSBuZXcgYW5jaG9yLndlYjMuVHJhbnNhY3Rpb25JbnN0cnVjdGlvbih7XG4gICAgICBrZXlzOiBbXSxcbiAgICAgIHByb2dyYW1JZDogbmV3IFB1YmxpY0tleShcIk1lbW9TcTRncUFCQVhLYjk2cW5IOFR5c05jV3hNeVdDcVhnRExHbWZjSHJcIiksXG4gICAgICBkYXRhOiBCdWZmZXIuZnJvbShgZXNjcm93LWFjY2VwdC0ke2VzY3Jvd0lkU3RyfS0ke0RhdGUubm93KCl9YCwgJ3V0Zi04JylcbiAgICB9KTtcbiAgICB0eC5hZGQobWVtb0luc3RydWN0aW9uKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIEFjY2VwdCB0cmFuc2FjdGlvbiBkZXRhaWxzOicsIHtcbiAgICAgIGVzY3Jvd0lkOiBlc2Nyb3dJZFN0cixcbiAgICAgIGJsb2NraGFzaDogYmxvY2toYXNoLnNsaWNlKDAsIDgpICsgJy4uLicsXG4gICAgICBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0pO1xuXG4gICAgLy8gU2lnbiB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCBzaWduZWRUeCA9IGF3YWl0IHdhbGxldC5zaWduVHJhbnNhY3Rpb24odHgpO1xuXG4gICAgY29uc29sZS5sb2coJ/CflI0gU2VuZGluZyBhY2NlcHQgdHJhbnNhY3Rpb24uLi4nKTtcblxuICAgIC8vIFNlbmQgdGhlIHRyYW5zYWN0aW9uIHdpdGggcmV0cnkgbG9naWNcbiAgICBjb25zdCB0eElkID0gYXdhaXQgc2VuZFRyYW5zYWN0aW9uV2l0aFJldHJ5KGNvbm5lY3Rpb24sIHNpZ25lZFR4LCAyLCAxNTAwKTtcblxuICAgIGNvbnNvbGUubG9nKCfinIUgVHJhbnNhY3Rpb24gc2VudDonLCB0eElkKTtcblxuICAgIC8vIENvbmZpcm0gdGhlIHRyYW5zYWN0aW9uIHdpdGggdGltZW91dFxuICAgIGNvbnN0IGNvbmZpcm1hdGlvbiA9IGF3YWl0IGNvbm5lY3Rpb24uY29uZmlybVRyYW5zYWN0aW9uKHtcbiAgICAgIHNpZ25hdHVyZTogdHhJZCxcbiAgICAgIGJsb2NraGFzaDogdHgucmVjZW50QmxvY2toYXNoISxcbiAgICAgIGxhc3RWYWxpZEJsb2NrSGVpZ2h0OiBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0sICdjb25maXJtZWQnKTtcblxuICAgIGlmIChjb25maXJtYXRpb24udmFsdWUuZXJyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRyYW5zYWN0aW9uIGZhaWxlZDogJHtjb25maXJtYXRpb24udmFsdWUuZXJyfWApO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfinIUgRXNjcm93IGFjY2VwdCB0cmFuc2FjdGlvbiBjb25maXJtZWQ6JywgdHhJZCk7XG5cbiAgICAvLyBNYXJrIHRyYW5zYWN0aW9uIGFzIGNvbXBsZXRlZFxuICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIHR4SWRcbiAgICB9KTtcblxuICAgIC8vIFNtYWxsIGRlbGF5IHRvIGVuc3VyZSB0cmFuc2FjdGlvbiBpcyBmdWxseSBwcm9jZXNzZWRcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuXG4gICAgcmV0dXJuIHR4SWQ7XG5cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0VzY3JvdyBhY2NlcHQgdHJhbnNhY3Rpb24gZmFpbGVkOicsIGVycm9yKTtcblxuICAgIC8vIE9ubHkgbWFyayBhcyBmYWlsZWQgaWYgaXQncyBub3QgYW4gXCJhbHJlYWR5IHByb2Nlc3NlZFwiIGVycm9yXG4gICAgaWYgKCFlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnVGhpcyB0cmFuc2FjdGlvbiBoYXMgYWxyZWFkeSBiZWVuIHByb2Nlc3NlZCcpKSB7XG4gICAgICBjb25zdCB0cmFuc2FjdGlvbktleSA9IGAke2VzY3Jvd0lkfS0ke3dhbGxldC5hZGRyZXNzfS1hY2NlcHRgO1xuICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgIHN0YXR1czogJ2ZhaWxlZCcsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gRW5oYW5jZWQgZXJyb3IgaGFuZGxpbmcgZm9yIFNvbGFuYSB0cmFuc2FjdGlvbnNcbiAgICBpZiAoZXJyb3IubG9ncykge1xuICAgICAgY29uc29sZS5lcnJvcignVHJhbnNhY3Rpb24gbG9nczonLCBlcnJvci5sb2dzKTtcbiAgICB9XG5cbiAgICAvLyBJZiBpdCdzIGFscmVhZHkgYW4gRXNjcm93RXJyb3IsIHJlLXRocm93IGl0XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXNjcm93RXJyb3IpIHtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSBzcGVjaWZpYyBlcnJvciB0eXBlcyB3aXRoIGVuaGFuY2VkIGVycm9yIG1lc3NhZ2VzXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykpIHtcbiAgICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93SWR9LSR7d2FsbGV0LmFkZHJlc3N9LWFjY2VwdGA7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFRyYW5zYWN0aW9uIGFscmVhZHkgcHJvY2Vzc2VkIGVycm9yIC0gY2hlY2tpbmcgaWYgZXNjcm93IHdhcyBhY3R1YWxseSBhY2NlcHRlZC4uLicpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgcHVyY2hhc2UgYWNjb3VudCB3YXMgYWN0dWFsbHkgYWNjZXB0ZWQgKG1lYW5pbmcgdHJhbnNhY3Rpb24gd2FzIHN1Y2Nlc3NmdWwpXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBlc2Nyb3dJZEJOID0gbmV3IGFuY2hvci5CTihlc2Nyb3dJZCk7XG4gICAgICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgcHJvZ3JhbS5hY2NvdW50LnB1cmNoYXNlLmZldGNoKHB1cmNoYXNlUGRhKTtcbiAgICAgICAgaWYgKHB1cmNoYXNlQWNjb3VudC5hY2NlcHRlZCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgUHVyY2hhc2UgYWNjb3VudCBpcyBhY2NlcHRlZCAtIHRyYW5zYWN0aW9uIHdhcyBhY3R1YWxseSBzdWNjZXNzZnVsIScpO1xuXG4gICAgICAgICAgLy8gVHJ5IHRvIGZpbmQgdGhlIHRyYW5zYWN0aW9uIHNpZ25hdHVyZSBpbiByZWNlbnQgaGlzdG9yeVxuICAgICAgICAgIGNvbnN0IHNlbGxlclB1YmtleSA9IG5ldyBQdWJsaWNLZXkod2FsbGV0LmFkZHJlc3MpO1xuICAgICAgICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBwcm9ncmFtLnByb3ZpZGVyLmNvbm5lY3Rpb247XG4gICAgICAgICAgY29uc3Qgc2lnbmF0dXJlcyA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0U2lnbmF0dXJlc0ZvckFkZHJlc3Moc2VsbGVyUHVia2V5LCB7IGxpbWl0OiAxMCB9KTtcblxuICAgICAgICAgIGxldCBmb3VuZFR4SWQgPSBudWxsO1xuICAgICAgICAgIGZvciAoY29uc3Qgc2lnIG9mIHNpZ25hdHVyZXMpIHtcbiAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgc2lnbmF0dXJlIGlzIHJlY2VudCAod2l0aGluIGxhc3QgNSBtaW51dGVzKVxuICAgICAgICAgICAgY29uc3QgZml2ZU1pbnV0ZXNBZ28gPSBEYXRlLm5vdygpIC0gNSAqIDYwICogMTAwMDtcbiAgICAgICAgICAgIGlmIChzaWcuYmxvY2tUaW1lICYmIHNpZy5ibG9ja1RpbWUgKiAxMDAwID4gZml2ZU1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICAgICAgZm91bmRUeElkID0gc2lnLnNpZ25hdHVyZTtcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKCFmb3VuZFR4SWQpIHtcbiAgICAgICAgICAgIGZvdW5kVHhJZCA9IGBzdWNjZXNzZnVsLWFjY2VwdC0ke2VzY3Jvd0lkfS0ke0RhdGUubm93KCl9YDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBNYXJrIGFzIGNvbXBsZXRlZCBpbiBvdXIgc3RhdGVcbiAgICAgICAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICB0eElkOiBmb3VuZFR4SWRcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn46JIFJldHVybmluZyBzdWNjZXNzZnVsIGFjY2VwdCB0cmFuc2FjdGlvbiBJRDonLCBmb3VuZFR4SWQpO1xuICAgICAgICAgIHJldHVybiBmb3VuZFR4SWQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KdjCBQdXJjaGFzZSBhY2NvdW50IGlzIG5vdCBhY2NlcHRlZCAtIHRyYW5zYWN0aW9uIGFjdHVhbGx5IGZhaWxlZCcpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChjaGVja0Vycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdDb3VsZCBub3QgdmVyaWZ5IGFjY2VwdCBzdGF0dXM6JywgY2hlY2tFcnJvcik7XG4gICAgICB9XG5cbiAgICAgIHRocm93IG5ldyBEdXBsaWNhdGVUcmFuc2FjdGlvbkVycm9yKCdBY2NlcHQgdHJhbnNhY3Rpb24gYWxyZWFkeSBwcm9jZXNzZWQuIFBsZWFzZSByZWZyZXNoIHRoZSBwYWdlIGFuZCB0cnkgYWdhaW4uJyk7XG4gICAgfVxuXG4gICAgLy8gSGFuZGxlIHNwZWNpZmljIFNvbGFuYS9ibG9ja2NoYWluIGVycm9yc1xuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnQWNjb3VudE5vdEluaXRpYWxpemVkJykpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcignVG9rZW4gYWNjb3VudCBub3QgaW5pdGlhbGl6ZWQuIFBsZWFzZSBlbnN1cmUgeW91IGhhdmUgdGhlIHJlcXVpcmVkIHRva2Vucy4nLCAnQUNDT1VOVF9OT1RfSU5JVElBTElaRUQnKTtcbiAgICB9XG5cbiAgICBpZiAoZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ0luc3VmZmljaWVudEZ1bmRzJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2luc3VmZmljaWVudCBmdW5kcycpKSB7XG4gICAgICB0aHJvdyBuZXcgSW5zdWZmaWNpZW50RnVuZHNFcnJvcignSW5zdWZmaWNpZW50IHBlcmsgdG9rZW5zIHRvIGFjY2VwdCB0aGUgZXNjcm93IHRyYW5zYWN0aW9uLicpO1xuICAgIH1cblxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnYmxvY2toYXNoIG5vdCBmb3VuZCcpIHx8IGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUcmFuc2FjdGlvbiBleHBpcmVkJykpIHtcbiAgICAgIHRocm93IG5ldyBUcmFuc2FjdGlvbkVycm9yKCdUcmFuc2FjdGlvbiBleHBpcmVkLiBQbGVhc2UgdHJ5IGFnYWluIHdpdGggYSBmcmVzaCB0cmFuc2FjdGlvbi4nKTtcbiAgICB9XG5cbiAgICBpZiAoZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1VzZXIgcmVqZWN0ZWQnKSB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygndXNlciByZWplY3RlZCcpIHx8IGVycm9yLmNvZGUgPT09IDQwMDEpIHtcbiAgICAgIHRocm93IG5ldyBFc2Nyb3dFcnJvcignVHJhbnNhY3Rpb24gd2FzIHJlamVjdGVkIGJ5IHVzZXInLCAnVVNFUl9SRUpFQ1RFRCcpO1xuICAgIH1cblxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnTmV0d29yayBFcnJvcicpIHx8IGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdGYWlsZWQgdG8gZmV0Y2gnKSkge1xuICAgICAgdGhyb3cgbmV3IEVzY3Jvd0Vycm9yKCdOZXR3b3JrIGNvbm5lY3Rpb24gZmFpbGVkLiBQbGVhc2UgY2hlY2sgeW91ciBpbnRlcm5ldCBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uJywgJ05FVFdPUktfRVJST1InKTtcbiAgICB9XG5cbiAgICBpZiAoZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1NpbXVsYXRpb24gZmFpbGVkJykpIHtcbiAgICAgIHRocm93IG5ldyBUcmFuc2FjdGlvbkVycm9yKCdUcmFuc2FjdGlvbiBzaW11bGF0aW9uIGZhaWxlZC4gVGhpcyBtYXkgYmUgZHVlIHRvIGluc3VmZmljaWVudCBwZXJrIHRva2VucyBvciBpbnZhbGlkIHRyYW5zYWN0aW9uIHBhcmFtZXRlcnMuJyk7XG4gICAgfVxuXG4gICAgLy8gSGFuZGxlIFJQQyBlcnJvcnNcbiAgICBpZiAoZXJyb3IuY29kZSAmJiB0eXBlb2YgZXJyb3IuY29kZSA9PT0gJ251bWJlcicpIHtcbiAgICAgIGlmIChlcnJvci5jb2RlID09PSAtMzIwMDIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVzY3Jvd0Vycm9yKCdSUEMgcmVxdWVzdCBmYWlsZWQuIFBsZWFzZSB0cnkgYWdhaW4uJywgJ1JQQ19FUlJPUicpO1xuICAgICAgfVxuICAgICAgaWYgKGVycm9yLmNvZGUgPT09IC0zMjAwMykge1xuICAgICAgICB0aHJvdyBuZXcgRHVwbGljYXRlVHJhbnNhY3Rpb25FcnJvcignVHJhbnNhY3Rpb24gYWxyZWFkeSBwcm9jZXNzZWQuJyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gR2VuZXJpYyBibG9ja2NoYWluIGVycm9yXG4gICAgaWYgKGVycm9yLm5hbWUgPT09ICdTZW5kVHJhbnNhY3Rpb25FcnJvcicgfHwgZXJyb3IubmFtZSA9PT0gJ1RyYW5zYWN0aW9uRXJyb3InKSB7XG4gICAgICB0aHJvdyBuZXcgQmxvY2tjaGFpbkVycm9yKGBCbG9ja2NoYWluIHRyYW5zYWN0aW9uIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWAsIGVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayBmb3IgdW5rbm93biBlcnJvcnNcbiAgICB0aHJvdyBuZXcgRXNjcm93RXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCBkdXJpbmcgdGhlIGVzY3JvdyBhY2NlcHQgdHJhbnNhY3Rpb24nLCAnVU5LTk9XTl9FUlJPUicpO1xuICB9XG59XG5cbi8qKlxuICogQ3JlYXRlIGFuZCBzaWduIGVzY3JvdyBzZWxsIHRyYW5zYWN0aW9uIChmb3Igc2VsbGVycylcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUVzY3Jvd1NlbGxUcmFuc2FjdGlvbihcbiAgZXNjcm93SWQ6IHN0cmluZyxcbiAgcHVyY2hhc2VUb2tlbk1pbnQ6IHN0cmluZyxcbiAgcGVya1Rva2VuTWludDogc3RyaW5nLFxuICBzZWxsZXJXYWxsZXQ6IHN0cmluZyxcbiAgYnV5ZXJXYWxsZXQ6IHN0cmluZyxcbiAgd2FsbGV0OiBhbnksXG4gIHRyYWRlSWQ/OiBzdHJpbmcgfCBudW1iZXJcbik6IFByb21pc2U8c3RyaW5nPiB7XG4gIHRyeSB7XG4gICAgLy8gVmFsaWRhdGUgd2FsbGV0XG4gICAgaWYgKCF3YWxsZXQgfHwgdHlwZW9mIHdhbGxldC5zaWduVHJhbnNhY3Rpb24gIT09IFwiZnVuY3Rpb25cIiB8fCAhd2FsbGV0LmFkZHJlc3MpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIldhbGxldCBub3QgY29ubmVjdGVkIG9yIHNpZ25UcmFuc2FjdGlvbiBtaXNzaW5nXCIpO1xuICAgIH1cblxuICAgIC8vIFN0ZXAgMTogRmV0Y2ggdHJhZGUgZGF0YSBmaXJzdCBpZiB0cmFkZUlkIGlzIHByb3ZpZGVkXG4gICAgbGV0IHRyYWRlRGF0YSA9IG51bGw7XG4gICAgaWYgKHRyYWRlSWQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtFc2Nyb3dTZWxsXSBGZXRjaGluZyB0cmFkZSBkYXRhIGZvciB0cmFkZUlkOicsIHRyYWRlSWQpO1xuICAgICAgICBjb25zdCB7IGdldFRyYWRlRGV0YWlscyB9ID0gYXdhaXQgaW1wb3J0KCcuLi9heGlvcy9yZXF1ZXN0cycpO1xuICAgICAgICBjb25zdCB0cmFkZVJlc3BvbnNlID0gYXdhaXQgZ2V0VHJhZGVEZXRhaWxzKE51bWJlcih0cmFkZUlkKSk7XG4gICAgICAgIHRyYWRlRGF0YSA9IHRyYWRlUmVzcG9uc2UuZGF0YTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBbRXNjcm93U2VsbF0gVHJhZGUgZGF0YSBmZXRjaGVkIHN1Y2Nlc3NmdWxseTonLCB7XG4gICAgICAgICAgdHJhZGVJZDogdHJhZGVEYXRhLmlkLFxuICAgICAgICAgIHN0YXR1czogdHJhZGVEYXRhLnN0YXR1cyxcbiAgICAgICAgICBlc2Nyb3dJZDogdHJhZGVEYXRhLmVzY3Jvd0lkLFxuICAgICAgICAgIHBlcmtJZDogdHJhZGVEYXRhLnBlcmtJZCxcbiAgICAgICAgICBwcmljZTogdHJhZGVEYXRhLnByaWNlXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFZhbGlkYXRlIHRoYXQgdGhlIHRyYWRlIGRhdGEgbWF0Y2hlcyB0aGUgZXNjcm93IHBhcmFtZXRlcnNcbiAgICAgICAgaWYgKHRyYWRlRGF0YS5lc2Nyb3dJZCAmJiB0cmFkZURhdGEuZXNjcm93SWQgIT09IGVzY3Jvd0lkKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gW0VzY3Jvd1NlbGxdIFRyYWRlIGVzY3Jvd0lkIG1pc21hdGNoOicsIHtcbiAgICAgICAgICAgIHByb3ZpZGVkOiBlc2Nyb3dJZCxcbiAgICAgICAgICAgIGZyb21UcmFkZTogdHJhZGVEYXRhLmVzY3Jvd0lkXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBFbnN1cmUgdHJhZGUgaXMgaW4gY29ycmVjdCBzdGF0dXMgZm9yIHJlbGVhc2VcbiAgICAgICAgaWYgKHRyYWRlRGF0YS5zdGF0dXMgIT09ICdlc2Nyb3dlZCcpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRyYWRlIGlzIG5vdCBpbiBlc2Nyb3dlZCBzdGF0dXMuIEN1cnJlbnQgc3RhdHVzOiAke3RyYWRlRGF0YS5zdGF0dXN9YCk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGRpdGlvbmFsIHZhbGlkYXRpb246IGNoZWNrIGlmIHNlbGxlciB3YWxsZXQgbWF0Y2hlc1xuICAgICAgICBpZiAodHJhZGVEYXRhLnRvICYmIHRyYWRlRGF0YS50byAhPT0gc2VsbGVyV2FsbGV0KSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gW0VzY3Jvd1NlbGxdIFNlbGxlciB3YWxsZXQgbWlzbWF0Y2g6Jywge1xuICAgICAgICAgICAgcHJvdmlkZWQ6IHNlbGxlcldhbGxldCxcbiAgICAgICAgICAgIGZyb21UcmFkZTogdHJhZGVEYXRhLnRvXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGRpdGlvbmFsIHZhbGlkYXRpb246IGNoZWNrIGlmIGJ1eWVyIHdhbGxldCBtYXRjaGVzXG4gICAgICAgIGlmICh0cmFkZURhdGEuZnJvbSAmJiB0cmFkZURhdGEuZnJvbSAhPT0gYnV5ZXJXYWxsZXQpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBbRXNjcm93U2VsbF0gQnV5ZXIgd2FsbGV0IG1pc21hdGNoOicsIHtcbiAgICAgICAgICAgIHByb3ZpZGVkOiBidXllcldhbGxldCxcbiAgICAgICAgICAgIGZyb21UcmFkZTogdHJhZGVEYXRhLmZyb21cbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbRXNjcm93U2VsbF0gRmFpbGVkIHRvIGZldGNoIHRyYWRlIGRhdGE6JywgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCB0cmFkZSBkYXRhOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHVuaXF1ZSB0cmFuc2FjdGlvbiBrZXkgdG8gcHJldmVudCBkdXBsaWNhdGVzXG4gICAgY29uc3QgdHJhbnNhY3Rpb25LZXkgPSBgJHtlc2Nyb3dJZH0tJHt3YWxsZXQuYWRkcmVzc30tc2VsbGA7XG5cbiAgICAvLyBDaGVjayBmb3IgcmFwaWQgZHVwbGljYXRlIGF0dGVtcHRzICh3aXRoaW4gMyBzZWNvbmRzKVxuICAgIGNvbnN0IGxhc3RBdHRlbXB0ID0gbGFzdFRyYW5zYWN0aW9uQXR0ZW1wdHMuZ2V0KHRyYW5zYWN0aW9uS2V5KTtcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgIGlmIChsYXN0QXR0ZW1wdCAmJiAobm93IC0gbGFzdEF0dGVtcHQpIDwgMzAwMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUGxlYXNlIHdhaXQgYSBtb21lbnQgYmVmb3JlIHRyeWluZyB0byByZWxlYXNlIGFnYWluLiBUcmFuc2FjdGlvbiBhdHRlbXB0IHRvbyBzb29uLlwiKTtcbiAgICB9XG4gICAgbGFzdFRyYW5zYWN0aW9uQXR0ZW1wdHMuc2V0KHRyYW5zYWN0aW9uS2V5LCBub3cpO1xuXG4gICAgLy8gQ2xlYW4gdXAgb2xkIHRyYW5zYWN0aW9ucyBwZXJpb2RpY2FsbHlcbiAgICBpZiAoTWF0aC5yYW5kb20oKSA8IDAuMSkgeyAvLyAxMCUgY2hhbmNlIHRvIGNsZWFudXAgb24gZWFjaCBjYWxsXG4gICAgICBjb25zdCBmaXZlTWludXRlc0FnbyA9IERhdGUubm93KCkgLSA1ICogNjAgKiAxMDAwO1xuICAgICAgZm9yIChjb25zdCBba2V5LCBzdGF0ZV0gb2YgdHJhbnNhY3Rpb25TdGF0ZXMuZW50cmllcygpKSB7XG4gICAgICAgIGlmIChzdGF0ZS50aW1lc3RhbXAgPCBmaXZlTWludXRlc0Fnbykge1xuICAgICAgICAgIHRyYW5zYWN0aW9uU3RhdGVzLmRlbGV0ZShrZXkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAvLyBBbHNvIGNsZWFudXAgbGFzdCBhdHRlbXB0c1xuICAgICAgZm9yIChjb25zdCBba2V5LCB0aW1lc3RhbXBdIG9mIGxhc3RUcmFuc2FjdGlvbkF0dGVtcHRzLmVudHJpZXMoKSkge1xuICAgICAgICBpZiAodGltZXN0YW1wIDwgZml2ZU1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICBsYXN0VHJhbnNhY3Rpb25BdHRlbXB0cy5kZWxldGUoa2V5KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRyYW5zYWN0aW9uIGlzIGFscmVhZHkgaW4gcHJvZ3Jlc3Mgb3IgY29tcGxldGVkXG4gICAgY29uc3QgZXhpc3RpbmdTdGF0ZSA9IHRyYW5zYWN0aW9uU3RhdGVzLmdldCh0cmFuc2FjdGlvbktleSk7XG4gICAgaWYgKGV4aXN0aW5nU3RhdGUpIHtcbiAgICAgIGlmIChleGlzdGluZ1N0YXRlLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlbGVhc2UgdHJhbnNhY3Rpb24gYWxyZWFkeSBpbiBwcm9ncmVzcy4gUGxlYXNlIHdhaXQgZm9yIHRoZSBjdXJyZW50IHRyYW5zYWN0aW9uIHRvIGNvbXBsZXRlLlwiKTtcbiAgICAgIH1cbiAgICAgIGlmIChleGlzdGluZ1N0YXRlLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgZXhpc3RpbmdTdGF0ZS50eElkKSB7XG4gICAgICAgIC8vIElmIGNvbXBsZXRlZCByZWNlbnRseSAod2l0aGluIDUgbWludXRlcyksIHJldHVybiBleGlzdGluZyB0eElkXG4gICAgICAgIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gICAgICAgIGlmIChleGlzdGluZ1N0YXRlLnRpbWVzdGFtcCA+IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJSZXR1cm5pbmcgZXhpc3RpbmcgcmVsZWFzZSB0cmFuc2FjdGlvbiBJRDpcIiwgZXhpc3RpbmdTdGF0ZS50eElkKTtcbiAgICAgICAgICByZXR1cm4gZXhpc3RpbmdTdGF0ZS50eElkO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGVzY3JvdyBoYXMgYWxyZWFkeSBiZWVuIHJlbGVhc2VkIG9uIHRoZSBibG9ja2NoYWluXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIENoZWNraW5nIGlmIGVzY3JvdyBoYXMgYWxyZWFkeSBiZWVuIHJlbGVhc2VkOicsIGVzY3Jvd0lkKTtcbiAgICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBwcm9ncmFtLnByb3ZpZGVyLmNvbm5lY3Rpb247XG4gICAgICBjb25zdCBlc2Nyb3dJZEJOID0gbmV3IGFuY2hvci5CTihlc2Nyb3dJZCk7XG4gICAgICBjb25zdCBbcHVyY2hhc2VQZGFdID0gUHVibGljS2V5LmZpbmRQcm9ncmFtQWRkcmVzc1N5bmMoXG4gICAgICAgIFtCdWZmZXIuZnJvbShcInB1cmNoYXNlXCIpLCBlc2Nyb3dJZEJOLnRvQXJyYXlMaWtlKEJ1ZmZlciwgXCJsZVwiLCA4KV0sXG4gICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICApO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgcHVyY2hhc2UgYWNjb3VudCBzdGlsbCBleGlzdHMgKGlmIHJlbGVhc2VkLCBpdCBzaG91bGQgYmUgY2xvc2VkKVxuICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhwdXJjaGFzZVBkYSk7XG4gICAgICBpZiAoIXB1cmNoYXNlQWNjb3VudCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFB1cmNoYXNlIGFjY291bnQgbm8gbG9uZ2VyIGV4aXN0cywgZXNjcm93IHdhcyBhbHJlYWR5IHJlbGVhc2VkJyk7XG5cbiAgICAgICAgLy8gVHJ5IHRvIGZpbmQgdGhlIHJlbGVhc2UgdHJhbnNhY3Rpb24gc2lnbmF0dXJlIGluIHJlY2VudCBoaXN0b3J5XG4gICAgICAgIGNvbnN0IHNlbGxlclB1YmtleSA9IG5ldyBQdWJsaWNLZXkod2FsbGV0LmFkZHJlc3MpO1xuICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gYXdhaXQgY29ubmVjdGlvbi5nZXRTaWduYXR1cmVzRm9yQWRkcmVzcyhzZWxsZXJQdWJrZXksIHsgbGltaXQ6IDIwIH0pO1xuICAgICAgICBmb3IgKGNvbnN0IHNpZyBvZiBzaWduYXR1cmVzKSB7XG4gICAgICAgICAgLy8gQ2hlY2sgaWYgdGhpcyBzaWduYXR1cmUgaXMgcmVjZW50ICh3aXRoaW4gbGFzdCAxMCBtaW51dGVzKVxuICAgICAgICAgIGNvbnN0IHRlbk1pbnV0ZXNBZ28gPSBEYXRlLm5vdygpIC0gMTAgKiA2MCAqIDEwMDA7XG4gICAgICAgICAgaWYgKHNpZy5ibG9ja1RpbWUgJiYgc2lnLmJsb2NrVGltZSAqIDEwMDAgPiB0ZW5NaW51dGVzQWdvKSB7XG4gICAgICAgICAgICAvLyBNYXJrIGFzIGNvbXBsZXRlZCBpbiBvdXIgc3RhdGVcbiAgICAgICAgICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgIHR4SWQ6IHNpZy5zaWduYXR1cmVcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHNpZy5zaWduYXR1cmU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gSWYgd2UgY2FuJ3QgZmluZCB0aGUgZXhhY3Qgc2lnbmF0dXJlLCBjcmVhdGUgYSBwbGFjZWhvbGRlclxuICAgICAgICBjb25zdCBwbGFjZWhvbGRlclR4SWQgPSBgcmVsZWFzZWQtJHtlc2Nyb3dJZH1gO1xuICAgICAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICB0eElkOiBwbGFjZWhvbGRlclR4SWRcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBwbGFjZWhvbGRlclR4SWQ7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoY2hlY2tFcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCBjaGVjayByZWxlYXNlIHN0YXR1czonLCBjaGVja0Vycm9yKTtcbiAgICAgIC8vIENvbnRpbnVlIHdpdGggbm9ybWFsIGZsb3cgaWYgY2hlY2sgZmFpbHNcbiAgICB9XG5cbiAgICAvLyBNYXJrIHRyYW5zYWN0aW9uIGFzIHBlbmRpbmdcbiAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgZXNjcm93IHNlbGwgdHJhbnNhY3Rpb246Jywge1xuICAgICAgZXNjcm93SWQsXG4gICAgICBzZWxsZXJXYWxsZXQsXG4gICAgICBidXllcldhbGxldCxcbiAgICAgIHRyYWRlRGF0YTogdHJhZGVEYXRhID8ge1xuICAgICAgICBpZDogdHJhZGVEYXRhLmlkLFxuICAgICAgICBzdGF0dXM6IHRyYWRlRGF0YS5zdGF0dXMsXG4gICAgICAgIHBlcmtJZDogdHJhZGVEYXRhLnBlcmtJZCxcbiAgICAgICAgcHJpY2U6IHRyYWRlRGF0YS5wcmljZSxcbiAgICAgICAgZXNjcm93VHhJZDogdHJhZGVEYXRhLmVzY3Jvd1R4SWRcbiAgICAgIH0gOiAnTm8gdHJhZGUgZGF0YSBwcm92aWRlZCdcbiAgICB9KTtcblxuICAgIC8vIENhbGN1bGF0ZSBQREFzXG4gICAgY29uc3QgZXNjcm93SWRCTiA9IG5ldyBhbmNob3IuQk4oZXNjcm93SWQpO1xuICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgIFtCdWZmZXIuZnJvbShcInB1cmNoYXNlXCIpLCBlc2Nyb3dJZEJOLnRvQXJyYXlMaWtlKEJ1ZmZlciwgXCJsZVwiLCA4KV0sXG4gICAgICBwcm9ncmFtLnByb2dyYW1JZFxuICAgICk7XG5cbiAgICBjb25zdCBbdmF1bHRQZGFdID0gUHVibGljS2V5LmZpbmRQcm9ncmFtQWRkcmVzc1N5bmMoXG4gICAgICBbcHVyY2hhc2VQZGEudG9CdWZmZXIoKSwgVE9LRU5fUFJPR1JBTV9JRC50b0J1ZmZlcigpLCBuZXcgUHVibGljS2V5KHB1cmNoYXNlVG9rZW5NaW50KS50b0J1ZmZlcigpXSxcbiAgICAgIEFTU09DSUFURURfVE9LRU5fUFJPR1JBTV9JRFxuICAgICk7XG5cbiAgICAvLyBDcmVhdGUgcHVibGljIGtleXNcbiAgICBjb25zdCBzZWxsZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KHNlbGxlcldhbGxldCk7XG4gICAgY29uc3QgYnV5ZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KGJ1eWVyV2FsbGV0KTtcbiAgICBjb25zdCBwdXJjaGFzZVRva2VuTWludFB1YmtleSA9IG5ldyBQdWJsaWNLZXkocHVyY2hhc2VUb2tlbk1pbnQpO1xuICAgIGNvbnN0IHBlcmtUb2tlbk1pbnRQdWJrZXkgPSBuZXcgUHVibGljS2V5KHBlcmtUb2tlbk1pbnQpO1xuXG4gICAgLy8gR2V0IHRva2VuIGFjY291bnRzXG4gICAgY29uc3Qgc2VsbGVyUHVyY2hhc2VUb2tlbkF0YSA9IGdldEFzc29jaWF0ZWRUb2tlbkFkZHJlc3NTeW5jKHB1cmNoYXNlVG9rZW5NaW50UHVia2V5LCBzZWxsZXJQdWJrZXkpO1xuICAgIGNvbnN0IGJ1eWVyUGVya1Rva2VuQXRhID0gZ2V0QXNzb2NpYXRlZFRva2VuQWRkcmVzc1N5bmMocGVya1Rva2VuTWludFB1YmtleSwgYnV5ZXJQdWJrZXkpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIHRoZSBwZXJrIHZhdWx0IFBEQSAod2hlcmUgc2VsbGVyJ3MgcGVyayB0b2tlbnMgYXJlIHN0b3JlZClcbiAgICBjb25zdCBbcGVya1ZhdWx0UGRhXSA9IFB1YmxpY0tleS5maW5kUHJvZ3JhbUFkZHJlc3NTeW5jKFxuICAgICAgW3B1cmNoYXNlUGRhLnRvQnVmZmVyKCksIFRPS0VOX1BST0dSQU1fSUQudG9CdWZmZXIoKSwgcGVya1Rva2VuTWludFB1YmtleS50b0J1ZmZlcigpXSxcbiAgICAgIEFTU09DSUFURURfVE9LRU5fUFJPR1JBTV9JRFxuICAgICk7XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBBY2NvdW50IGRlYnVnZ2luZzonLCB7XG4gICAgICBzZWxsZXJXYWxsZXQsXG4gICAgICBidXllcldhbGxldCxcbiAgICAgIGJ1eWVyUGVya1Rva2VuQXRhOiBidXllclBlcmtUb2tlbkF0YS50b1N0cmluZygpLFxuICAgICAgc2VsbGVyUHVyY2hhc2VUb2tlbkF0YTogc2VsbGVyUHVyY2hhc2VUb2tlbkF0YS50b1N0cmluZygpLFxuICAgICAgcGVya1ZhdWx0UGRhOiBwZXJrVmF1bHRQZGEudG9TdHJpbmcoKSxcbiAgICAgIHdob093bnNXaGF0OiB7XG4gICAgICAgIHB1cmNoYXNlVG9rZW5Hb2VzVG86ICdzZWxsZXInLCAvLyBTaG91bGQgYmUgc2VsbGVyXG4gICAgICAgIHBlcmtUb2tlbkdvZXNUbzogJ2J1eWVyJyAgICAgICAvLyBTaG91bGQgYmUgYnV5ZXJcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIENyZWF0ZSB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCB0eCA9IGF3YWl0IHByb2dyYW0ubWV0aG9kc1xuICAgICAgLmVzY3Jvd1NlbGwoZXNjcm93SWRCTilcbiAgICAgIC5hY2NvdW50cyh7XG4gICAgICAgIGJ1eWVyOiBidXllclB1YmtleSxcbiAgICAgICAgc2VsbGVyOiBzZWxsZXJQdWJrZXksXG4gICAgICAgIHB1cmNoYXNlVG9rZW5NaW50OiBwdXJjaGFzZVRva2VuTWludFB1YmtleSxcbiAgICAgICAgcGVya1Rva2VuTWludDogcGVya1Rva2VuTWludFB1YmtleSxcbiAgICAgICAgcHVyY2hhc2VUb2tlbkF0YTogc2VsbGVyUHVyY2hhc2VUb2tlbkF0YSxcbiAgICAgICAgcGVya1Rva2VuRGVzdGluYXRpb25BdGE6IGJ1eWVyUGVya1Rva2VuQXRhLFxuICAgICAgICBwdXJjaGFzZTogcHVyY2hhc2VQZGEsXG4gICAgICAgIHZhdWx0OiB2YXVsdFBkYSxcbiAgICAgICAgcGVya1ZhdWx0OiBwZXJrVmF1bHRQZGEsXG4gICAgICAgIGFzc29jaWF0ZWRUb2tlblByb2dyYW06IEFTU09DSUFURURfVE9LRU5fUFJPR1JBTV9JRCxcbiAgICAgICAgdG9rZW5Qcm9ncmFtOiBUT0tFTl9QUk9HUkFNX0lELFxuICAgICAgICBzeXN0ZW1Qcm9ncmFtOiBhbmNob3Iud2ViMy5TeXN0ZW1Qcm9ncmFtLnByb2dyYW1JZCxcbiAgICAgIH0pXG4gICAgICAudHJhbnNhY3Rpb24oKTtcblxuICAgIC8vIEdldCBmcmVzaCBibG9ja2hhc2ggdG8gZW5zdXJlIHRyYW5zYWN0aW9uIHVuaXF1ZW5lc3NcbiAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgIGNvbnN0IHsgYmxvY2toYXNoLCBsYXN0VmFsaWRCbG9ja0hlaWdodCB9ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRMYXRlc3RCbG9ja2hhc2goJ2ZpbmFsaXplZCcpO1xuICAgIHR4LnJlY2VudEJsb2NraGFzaCA9IGJsb2NraGFzaDtcbiAgICB0eC5mZWVQYXllciA9IHNlbGxlclB1YmtleTtcblxuICAgIC8vIEFkZCBhIHVuaXF1ZSBtZW1vIGluc3RydWN0aW9uIHRvIHByZXZlbnQgZHVwbGljYXRlIHRyYW5zYWN0aW9uc1xuICAgIGNvbnN0IG1lbW9JbnN0cnVjdGlvbiA9IG5ldyBhbmNob3Iud2ViMy5UcmFuc2FjdGlvbkluc3RydWN0aW9uKHtcbiAgICAgIGtleXM6IFtdLFxuICAgICAgcHJvZ3JhbUlkOiBuZXcgUHVibGljS2V5KFwiTWVtb1NxNGdxQUJBWEtiOTZxbkg4VHlzTmNXeE15V0NxWGdETEdtZmNIclwiKSxcbiAgICAgIGRhdGE6IEJ1ZmZlci5mcm9tKGBlc2Nyb3ctc2VsbC0ke2VzY3Jvd0lkfS0ke0RhdGUubm93KCl9YCwgJ3V0Zi04JylcbiAgICB9KTtcbiAgICB0eC5hZGQobWVtb0luc3RydWN0aW9uKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFNlbGwgdHJhbnNhY3Rpb24gZGV0YWlsczonLCB7XG4gICAgICBlc2Nyb3dJZCxcbiAgICAgIGJsb2NraGFzaDogYmxvY2toYXNoLnNsaWNlKDAsIDgpICsgJy4uLicsXG4gICAgICBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0pO1xuXG4gICAgLy8gU2lnbiB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCBzaWduZWRUeCA9IGF3YWl0IHdhbGxldC5zaWduVHJhbnNhY3Rpb24odHgpO1xuXG4gICAgY29uc29sZS5sb2coJ/CflI0gU2VuZGluZyBzZWxsIHRyYW5zYWN0aW9uLi4uJyk7XG5cbiAgICAvLyBTZW5kIHRoZSB0cmFuc2FjdGlvbiB3aXRoIHJldHJ5IGxvZ2ljXG4gICAgY29uc3QgdHhJZCA9IGF3YWl0IHNlbmRUcmFuc2FjdGlvbldpdGhSZXRyeShjb25uZWN0aW9uLCBzaWduZWRUeCwgMiwgMTUwMCk7XG5cbiAgICBjb25zb2xlLmxvZygn4pyFIFRyYW5zYWN0aW9uIHNlbnQ6JywgdHhJZCk7XG5cbiAgICAvLyBDb25maXJtIHRoZSB0cmFuc2FjdGlvbiB3aXRoIHRpbWVvdXRcbiAgICBjb25zdCBjb25maXJtYXRpb24gPSBhd2FpdCBjb25uZWN0aW9uLmNvbmZpcm1UcmFuc2FjdGlvbih7XG4gICAgICBzaWduYXR1cmU6IHR4SWQsXG4gICAgICBibG9ja2hhc2g6IHR4LnJlY2VudEJsb2NraGFzaCEsXG4gICAgICBsYXN0VmFsaWRCbG9ja0hlaWdodDogbGFzdFZhbGlkQmxvY2tIZWlnaHRcbiAgICB9LCAnY29uZmlybWVkJyk7XG5cbiAgICBpZiAoY29uZmlybWF0aW9uLnZhbHVlLmVycikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUcmFuc2FjdGlvbiBmYWlsZWQ6ICR7Y29uZmlybWF0aW9uLnZhbHVlLmVycn1gKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn4pyFIEVzY3JvdyBzZWxsIHRyYW5zYWN0aW9uIGNvbmZpcm1lZDonLCB0eElkKTtcblxuICAgIC8vIE1hcmsgdHJhbnNhY3Rpb24gYXMgY29tcGxldGVkXG4gICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgdHhJZFxuICAgIH0pO1xuXG4gICAgLy8gU21hbGwgZGVsYXkgdG8gZW5zdXJlIHRyYW5zYWN0aW9uIGlzIGZ1bGx5IHByb2Nlc3NlZFxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG5cbiAgICByZXR1cm4gdHhJZDtcblxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignRXNjcm93IHNlbGwgdHJhbnNhY3Rpb24gZmFpbGVkOicsIGVycm9yKTtcblxuICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93SWR9LSR7d2FsbGV0LmFkZHJlc3N9LXNlbGxgO1xuXG4gICAgLy8gT25seSBtYXJrIGFzIGZhaWxlZCBpZiBpdCdzIG5vdCBhbiBcImFscmVhZHkgcHJvY2Vzc2VkXCIgZXJyb3JcbiAgICAvLyAod2hpY2ggbWlnaHQgYWN0dWFsbHkgYmUgc3VjY2Vzc2Z1bClcbiAgICBpZiAoIWVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykpIHtcbiAgICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgICBzdGF0dXM6ICdmYWlsZWQnLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEVuaGFuY2VkIGVycm9yIGhhbmRsaW5nIGZvciBTb2xhbmEgdHJhbnNhY3Rpb25zXG4gICAgaWYgKGVycm9yLmxvZ3MpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RyYW5zYWN0aW9uIGxvZ3M6JywgZXJyb3IubG9ncyk7XG4gICAgfVxuXG4gICAgLy8gSGFuZGxlIHNwZWNpZmljIGR1cGxpY2F0ZSB0cmFuc2FjdGlvbiBlcnJvclxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnVGhpcyB0cmFuc2FjdGlvbiBoYXMgYWxyZWFkeSBiZWVuIHByb2Nlc3NlZCcpKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBSZWxlYXNlIHRyYW5zYWN0aW9uIGFscmVhZHkgcHJvY2Vzc2VkIGVycm9yIC0gY2hlY2tpbmcgaWYgZXNjcm93IHdhcyBhY3R1YWxseSByZWxlYXNlZC4uLicpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgcHVyY2hhc2UgYWNjb3VudCB3YXMgYWN0dWFsbHkgY2xvc2VkIChtZWFuaW5nIHJlbGVhc2Ugd2FzIHN1Y2Nlc3NmdWwpXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgICAgICBjb25zdCBlc2Nyb3dJZEJOID0gbmV3IGFuY2hvci5CTihlc2Nyb3dJZCk7XG4gICAgICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhwdXJjaGFzZVBkYSk7XG4gICAgICAgIGlmICghcHVyY2hhc2VBY2NvdW50KSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBQdXJjaGFzZSBhY2NvdW50IG5vIGxvbmdlciBleGlzdHMgLSByZWxlYXNlIHdhcyBhY3R1YWxseSBzdWNjZXNzZnVsIScpO1xuXG4gICAgICAgICAgLy8gVHJ5IHRvIGZpbmQgdGhlIHRyYW5zYWN0aW9uIHNpZ25hdHVyZSBpbiByZWNlbnQgaGlzdG9yeVxuICAgICAgICAgIGNvbnN0IHNlbGxlclB1YmtleSA9IG5ldyBQdWJsaWNLZXkod2FsbGV0LmFkZHJlc3MpO1xuICAgICAgICAgIGNvbnN0IHNpZ25hdHVyZXMgPSBhd2FpdCBjb25uZWN0aW9uLmdldFNpZ25hdHVyZXNGb3JBZGRyZXNzKHNlbGxlclB1YmtleSwgeyBsaW1pdDogMTAgfSk7XG5cbiAgICAgICAgICBsZXQgZm91bmRUeElkID0gbnVsbDtcbiAgICAgICAgICBmb3IgKGNvbnN0IHNpZyBvZiBzaWduYXR1cmVzKSB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHNpZ25hdHVyZSBpcyByZWNlbnQgKHdpdGhpbiBsYXN0IDUgbWludXRlcylcbiAgICAgICAgICAgIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gICAgICAgICAgICBpZiAoc2lnLmJsb2NrVGltZSAmJiBzaWcuYmxvY2tUaW1lICogMTAwMCA+IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgICAgIGZvdW5kVHhJZCA9IHNpZy5zaWduYXR1cmU7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghZm91bmRUeElkKSB7XG4gICAgICAgICAgICBmb3VuZFR4SWQgPSBgc3VjY2Vzc2Z1bC1yZWxlYXNlLSR7ZXNjcm93SWR9LSR7RGF0ZS5ub3coKX1gO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIE1hcmsgYXMgY29tcGxldGVkIGluIG91ciBzdGF0ZVxuICAgICAgICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIHR4SWQ6IGZvdW5kVHhJZFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CfjokgUmV0dXJuaW5nIHN1Y2Nlc3NmdWwgcmVsZWFzZSB0cmFuc2FjdGlvbiBJRDonLCBmb3VuZFR4SWQpO1xuICAgICAgICAgIHJldHVybiBmb3VuZFR4SWQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KdjCBQdXJjaGFzZSBhY2NvdW50IHN0aWxsIGV4aXN0cyAtIHJlbGVhc2UgYWN0dWFsbHkgZmFpbGVkJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGNoZWNrRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCB2ZXJpZnkgcmVsZWFzZSBzdGF0dXM6JywgY2hlY2tFcnJvcik7XG4gICAgICB9XG5cbiAgICAgIHRocm93IG5ldyBFcnJvcignUmVsZWFzZSB0cmFuc2FjdGlvbiBhbHJlYWR5IHByb2Nlc3NlZC4gUGxlYXNlIHJlZnJlc2ggdGhlIHBhZ2UgYW5kIHRyeSBhZ2Fpbi4nKTtcbiAgICB9XG5cbiAgICBpZiAoZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ0FjY291bnROb3RJbml0aWFsaXplZCcpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Rva2VuIGFjY291bnQgbm90IGluaXRpYWxpemVkLiBQbGVhc2UgZW5zdXJlIHlvdSBoYXZlIHRoZSByZXF1aXJlZCB0b2tlbnMuJyk7XG4gICAgfVxuXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdJbnN1ZmZpY2llbnRGdW5kcycpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0luc3VmZmljaWVudCBmdW5kcyB0byBjb21wbGV0ZSB0aGUgcmVsZWFzZSB0cmFuc2FjdGlvbi4nKTtcbiAgICB9XG5cbiAgICAvLyBIYW5kbGUgdHJhbnNhY3Rpb24gdGltZW91dCBvciBibG9ja2hhc2ggZXhwaXJ5XG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdibG9ja2hhc2ggbm90IGZvdW5kJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1RyYW5zYWN0aW9uIGV4cGlyZWQnKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZWxlYXNlIHRyYW5zYWN0aW9uIGV4cGlyZWQuIFBsZWFzZSB0cnkgYWdhaW4gd2l0aCBhIGZyZXNoIHRyYW5zYWN0aW9uLicpO1xuICAgIH1cblxuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogQ3JlYXRlIGFuZCBzaWduIGVzY3JvdyByZWZ1bmQgdHJhbnNhY3Rpb24gKGZvciBidXllcnMpXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVFc2Nyb3dSZWZ1bmRUcmFuc2FjdGlvbihcbiAgZXNjcm93SWQ6IHN0cmluZyxcbiAgcHVyY2hhc2VUb2tlbk1pbnQ6IHN0cmluZyxcbiAgYnV5ZXJXYWxsZXQ6IHN0cmluZyxcbiAgd2FsbGV0OiBhbnlcbik6IFByb21pc2U8c3RyaW5nPiB7XG4gIHRyeSB7XG4gICAgLy8gVmFsaWRhdGUgd2FsbGV0IC0gc2FtZSBwYXR0ZXJuIGFzIGluIGhlbHBlcnMudHNcbiAgICBpZiAoIXdhbGxldCB8fCB0eXBlb2Ygd2FsbGV0LnNpZ25UcmFuc2FjdGlvbiAhPT0gXCJmdW5jdGlvblwiIHx8ICF3YWxsZXQuYWRkcmVzcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiV2FsbGV0IG5vdCBjb25uZWN0ZWQgb3Igc2lnblRyYW5zYWN0aW9uIG1pc3NpbmdcIik7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHVuaXF1ZSB0cmFuc2FjdGlvbiBrZXkgdG8gcHJldmVudCBkdXBsaWNhdGVzXG4gICAgY29uc3QgdHJhbnNhY3Rpb25LZXkgPSBgJHtlc2Nyb3dJZH0tJHt3YWxsZXQuYWRkcmVzc30tcmVmdW5kYDtcblxuICAgIC8vIENoZWNrIGZvciByYXBpZCBkdXBsaWNhdGUgYXR0ZW1wdHMgKHdpdGhpbiAzIHNlY29uZHMpXG4gICAgY29uc3QgbGFzdEF0dGVtcHQgPSBsYXN0VHJhbnNhY3Rpb25BdHRlbXB0cy5nZXQodHJhbnNhY3Rpb25LZXkpO1xuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgaWYgKGxhc3RBdHRlbXB0ICYmIChub3cgLSBsYXN0QXR0ZW1wdCkgPCAzMDAwKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJQbGVhc2Ugd2FpdCBhIG1vbWVudCBiZWZvcmUgdHJ5aW5nIHRvIHJlZnVuZCBhZ2Fpbi4gVHJhbnNhY3Rpb24gYXR0ZW1wdCB0b28gc29vbi5cIik7XG4gICAgfVxuICAgIGxhc3RUcmFuc2FjdGlvbkF0dGVtcHRzLnNldCh0cmFuc2FjdGlvbktleSwgbm93KTtcblxuICAgIC8vIENoZWNrIGlmIHRyYW5zYWN0aW9uIGlzIGFscmVhZHkgaW4gcHJvZ3Jlc3Mgb3IgY29tcGxldGVkXG4gICAgY29uc3QgZXhpc3RpbmdTdGF0ZSA9IHRyYW5zYWN0aW9uU3RhdGVzLmdldCh0cmFuc2FjdGlvbktleSk7XG4gICAgaWYgKGV4aXN0aW5nU3RhdGUpIHtcbiAgICAgIGlmIChleGlzdGluZ1N0YXRlLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlZnVuZCB0cmFuc2FjdGlvbiBhbHJlYWR5IGluIHByb2dyZXNzLiBQbGVhc2Ugd2FpdCBmb3IgdGhlIGN1cnJlbnQgdHJhbnNhY3Rpb24gdG8gY29tcGxldGUuXCIpO1xuICAgICAgfVxuICAgICAgaWYgKGV4aXN0aW5nU3RhdGUuc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiBleGlzdGluZ1N0YXRlLnR4SWQpIHtcbiAgICAgICAgLy8gSWYgY29tcGxldGVkIHJlY2VudGx5ICh3aXRoaW4gNSBtaW51dGVzKSwgcmV0dXJuIGV4aXN0aW5nIHR4SWRcbiAgICAgICAgY29uc3QgZml2ZU1pbnV0ZXNBZ28gPSBEYXRlLm5vdygpIC0gNSAqIDYwICogMTAwMDtcbiAgICAgICAgaWYgKGV4aXN0aW5nU3RhdGUudGltZXN0YW1wID4gZml2ZU1pbnV0ZXNBZ28pIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlJldHVybmluZyBleGlzdGluZyByZWZ1bmQgdHJhbnNhY3Rpb24gSUQ6XCIsIGV4aXN0aW5nU3RhdGUudHhJZCk7XG4gICAgICAgICAgcmV0dXJuIGV4aXN0aW5nU3RhdGUudHhJZDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRoZSBlc2Nyb3cgaGFzIGFscmVhZHkgYmVlbiByZWZ1bmRlZCBvbiB0aGUgYmxvY2tjaGFpblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBDaGVja2luZyBpZiBlc2Nyb3cgaGFzIGFscmVhZHkgYmVlbiByZWZ1bmRlZDonLCBlc2Nyb3dJZCk7XG4gICAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgICAgY29uc3QgZXNjcm93SWRCTiA9IG5ldyBhbmNob3IuQk4oZXNjcm93SWQpO1xuICAgICAgY29uc3QgW3B1cmNoYXNlUGRhXSA9IFB1YmxpY0tleS5maW5kUHJvZ3JhbUFkZHJlc3NTeW5jKFxuICAgICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgICBwcm9ncmFtLnByb2dyYW1JZFxuICAgICAgKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgdGhlIHB1cmNoYXNlIGFjY291bnQgc3RpbGwgZXhpc3RzIChpZiByZWZ1bmRlZCwgaXQgc2hvdWxkIGJlIGNsb3NlZClcbiAgICAgIGNvbnN0IHB1cmNoYXNlQWNjb3VudCA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0QWNjb3VudEluZm8ocHVyY2hhc2VQZGEpO1xuICAgICAgaWYgKCFwdXJjaGFzZUFjY291bnQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBQdXJjaGFzZSBhY2NvdW50IG5vIGxvbmdlciBleGlzdHMsIGVzY3JvdyB3YXMgYWxyZWFkeSByZWZ1bmRlZCcpO1xuXG4gICAgICAgIC8vIFRyeSB0byBmaW5kIHRoZSByZWZ1bmQgdHJhbnNhY3Rpb24gc2lnbmF0dXJlIGluIHJlY2VudCBoaXN0b3J5XG4gICAgICAgIGNvbnN0IGJ1eWVyUHVia2V5ID0gbmV3IFB1YmxpY0tleSh3YWxsZXQuYWRkcmVzcyk7XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZXMgPSBhd2FpdCBjb25uZWN0aW9uLmdldFNpZ25hdHVyZXNGb3JBZGRyZXNzKGJ1eWVyUHVia2V5LCB7IGxpbWl0OiAyMCB9KTtcbiAgICAgICAgZm9yIChjb25zdCBzaWcgb2Ygc2lnbmF0dXJlcykge1xuICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgc2lnbmF0dXJlIGlzIHJlY2VudCAod2l0aGluIGxhc3QgMTAgbWludXRlcylcbiAgICAgICAgICBjb25zdCB0ZW5NaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDEwICogNjAgKiAxMDAwO1xuICAgICAgICAgIGlmIChzaWcuYmxvY2tUaW1lICYmIHNpZy5ibG9ja1RpbWUgKiAxMDAwID4gdGVuTWludXRlc0Fnbykge1xuICAgICAgICAgICAgLy8gTWFyayBhcyBjb21wbGV0ZWQgaW4gb3VyIHN0YXRlXG4gICAgICAgICAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICB0eElkOiBzaWcuc2lnbmF0dXJlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBzaWcuc2lnbmF0dXJlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIElmIHdlIGNhbid0IGZpbmQgdGhlIGV4YWN0IHNpZ25hdHVyZSwgY3JlYXRlIGEgcGxhY2Vob2xkZXJcbiAgICAgICAgY29uc3QgcGxhY2Vob2xkZXJUeElkID0gYHJlZnVuZGVkLSR7ZXNjcm93SWR9YDtcbiAgICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgdHhJZDogcGxhY2Vob2xkZXJUeElkXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gcGxhY2Vob2xkZXJUeElkO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGNoZWNrRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdDb3VsZCBub3QgY2hlY2sgcmVmdW5kIHN0YXR1czonLCBjaGVja0Vycm9yKTtcbiAgICAgIC8vIENvbnRpbnVlIHdpdGggbm9ybWFsIGZsb3cgaWYgY2hlY2sgZmFpbHNcbiAgICB9XG5cbiAgICAvLyBNYXJrIHRyYW5zYWN0aW9uIGFzIHBlbmRpbmdcbiAgICB0cmFuc2FjdGlvblN0YXRlcy5zZXQodHJhbnNhY3Rpb25LZXksIHtcbiAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgZXNjcm93IHJlZnVuZCB0cmFuc2FjdGlvbjonLCB7XG4gICAgICBlc2Nyb3dJZCxcbiAgICAgIGJ1eWVyV2FsbGV0XG4gICAgfSk7XG5cbiAgICAvLyBDYWxjdWxhdGUgUERBc1xuICAgIGNvbnN0IGVzY3Jvd0lkQk4gPSBuZXcgYW5jaG9yLkJOKGVzY3Jvd0lkKTtcbiAgICBjb25zdCBbcHVyY2hhc2VQZGFdID0gUHVibGljS2V5LmZpbmRQcm9ncmFtQWRkcmVzc1N5bmMoXG4gICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgcHJvZ3JhbS5wcm9ncmFtSWRcbiAgICApO1xuXG4gICAgY29uc3QgW3ZhdWx0UGRhXSA9IFB1YmxpY0tleS5maW5kUHJvZ3JhbUFkZHJlc3NTeW5jKFxuICAgICAgW3B1cmNoYXNlUGRhLnRvQnVmZmVyKCksIFRPS0VOX1BST0dSQU1fSUQudG9CdWZmZXIoKSwgbmV3IFB1YmxpY0tleShwdXJjaGFzZVRva2VuTWludCkudG9CdWZmZXIoKV0sXG4gICAgICBBU1NPQ0lBVEVEX1RPS0VOX1BST0dSQU1fSURcbiAgICApO1xuXG4gICAgLy8gQ3JlYXRlIHB1YmxpYyBrZXlzXG4gICAgY29uc3QgYnV5ZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KGJ1eWVyV2FsbGV0KTtcbiAgICBjb25zdCBwdXJjaGFzZVRva2VuTWludFB1YmtleSA9IG5ldyBQdWJsaWNLZXkocHVyY2hhc2VUb2tlbk1pbnQpO1xuXG4gICAgLy8gR2V0IGJ1eWVyJ3MgdG9rZW4gYWNjb3VudFxuICAgIGNvbnN0IGJ1eWVyVG9rZW5BY2NvdW50ID0gZ2V0QXNzb2NpYXRlZFRva2VuQWRkcmVzc1N5bmMocHVyY2hhc2VUb2tlbk1pbnRQdWJrZXksIGJ1eWVyUHVia2V5KTtcblxuICAgIC8vIENyZWF0ZSB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCB0eCA9IGF3YWl0IHByb2dyYW0ubWV0aG9kc1xuICAgICAgLmVzY3Jvd1JlZnVuZChlc2Nyb3dJZEJOKVxuICAgICAgLmFjY291bnRzKHtcbiAgICAgICAgYnV5ZXI6IGJ1eWVyUHVia2V5LFxuICAgICAgICBwdXJjaGFzZVRva2VuTWludDogcHVyY2hhc2VUb2tlbk1pbnRQdWJrZXksXG4gICAgICAgIGJ1eWVyVG9rZW5BdGE6IGJ1eWVyVG9rZW5BY2NvdW50LFxuICAgICAgICBwdXJjaGFzZTogcHVyY2hhc2VQZGEsXG4gICAgICAgIHZhdWx0OiB2YXVsdFBkYSxcbiAgICAgICAgdG9rZW5Qcm9ncmFtOiBUT0tFTl9QUk9HUkFNX0lELFxuICAgICAgICBzeXN0ZW1Qcm9ncmFtOiBhbmNob3Iud2ViMy5TeXN0ZW1Qcm9ncmFtLnByb2dyYW1JZCxcbiAgICAgIH0pXG4gICAgICAudHJhbnNhY3Rpb24oKTtcblxuICAgIC8vIEdldCBmcmVzaCBibG9ja2hhc2ggdG8gZW5zdXJlIHRyYW5zYWN0aW9uIHVuaXF1ZW5lc3NcbiAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgIGNvbnN0IHsgYmxvY2toYXNoLCBsYXN0VmFsaWRCbG9ja0hlaWdodCB9ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRMYXRlc3RCbG9ja2hhc2goJ2ZpbmFsaXplZCcpO1xuICAgIHR4LnJlY2VudEJsb2NraGFzaCA9IGJsb2NraGFzaDtcbiAgICB0eC5mZWVQYXllciA9IGJ1eWVyUHVia2V5O1xuXG4gICAgLy8gQWRkIGEgdW5pcXVlIG1lbW8gaW5zdHJ1Y3Rpb24gdG8gcHJldmVudCBkdXBsaWNhdGUgdHJhbnNhY3Rpb25zXG4gICAgY29uc3QgbWVtb0luc3RydWN0aW9uID0gbmV3IGFuY2hvci53ZWIzLlRyYW5zYWN0aW9uSW5zdHJ1Y3Rpb24oe1xuICAgICAga2V5czogW10sXG4gICAgICBwcm9ncmFtSWQ6IG5ldyBQdWJsaWNLZXkoXCJNZW1vU3E0Z3FBQkFYS2I5NnFuSDhUeXNOY1d4TXlXQ3FYZ0RMR21mY0hyXCIpLFxuICAgICAgZGF0YTogQnVmZmVyLmZyb20oYGVzY3Jvdy1yZWZ1bmQtJHtlc2Nyb3dJZH0tJHtEYXRlLm5vdygpfWAsICd1dGYtOCcpXG4gICAgfSk7XG4gICAgdHguYWRkKG1lbW9JbnN0cnVjdGlvbik7XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBSZWZ1bmQgdHJhbnNhY3Rpb24gZGV0YWlsczonLCB7XG4gICAgICBlc2Nyb3dJZCxcbiAgICAgIGJsb2NraGFzaDogYmxvY2toYXNoLnNsaWNlKDAsIDgpICsgJy4uLicsXG4gICAgICBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0pO1xuXG4gICAgLy8gU2lnbiB0aGUgdHJhbnNhY3Rpb25cbiAgICBjb25zdCBzaWduZWRUeCA9IGF3YWl0IHdhbGxldC5zaWduVHJhbnNhY3Rpb24odHgpO1xuXG4gICAgY29uc29sZS5sb2coJ/CflI0gU2VuZGluZyByZWZ1bmQgdHJhbnNhY3Rpb24uLi4nKTtcblxuICAgIC8vIFNlbmQgdGhlIHRyYW5zYWN0aW9uIHdpdGggcmV0cnkgbG9naWNcbiAgICBjb25zdCB0eElkID0gYXdhaXQgc2VuZFRyYW5zYWN0aW9uV2l0aFJldHJ5KGNvbm5lY3Rpb24sIHNpZ25lZFR4LCAyLCAxNTAwKTtcblxuICAgIGNvbnNvbGUubG9nKCfinIUgVHJhbnNhY3Rpb24gc2VudDonLCB0eElkKTtcblxuICAgIC8vIENvbmZpcm0gdGhlIHRyYW5zYWN0aW9uIHdpdGggdGltZW91dFxuICAgIGNvbnN0IGNvbmZpcm1hdGlvbiA9IGF3YWl0IGNvbm5lY3Rpb24uY29uZmlybVRyYW5zYWN0aW9uKHtcbiAgICAgIHNpZ25hdHVyZTogdHhJZCxcbiAgICAgIGJsb2NraGFzaDogdHgucmVjZW50QmxvY2toYXNoISxcbiAgICAgIGxhc3RWYWxpZEJsb2NrSGVpZ2h0OiBsYXN0VmFsaWRCbG9ja0hlaWdodFxuICAgIH0sICdjb25maXJtZWQnKTtcblxuICAgIGlmIChjb25maXJtYXRpb24udmFsdWUuZXJyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRyYW5zYWN0aW9uIGZhaWxlZDogJHtjb25maXJtYXRpb24udmFsdWUuZXJyfWApO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfinIUgRXNjcm93IHJlZnVuZCB0cmFuc2FjdGlvbiBjb25maXJtZWQ6JywgdHhJZCk7XG5cbiAgICAvLyBNYXJrIHRyYW5zYWN0aW9uIGFzIGNvbXBsZXRlZFxuICAgIHRyYW5zYWN0aW9uU3RhdGVzLnNldCh0cmFuc2FjdGlvbktleSwge1xuICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIHR4SWRcbiAgICB9KTtcblxuICAgIC8vIFNtYWxsIGRlbGF5IHRvIGVuc3VyZSB0cmFuc2FjdGlvbiBpcyBmdWxseSBwcm9jZXNzZWRcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuXG4gICAgcmV0dXJuIHR4SWQ7XG5cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0VzY3JvdyByZWZ1bmQgdHJhbnNhY3Rpb24gZmFpbGVkOicsIGVycm9yKTtcblxuICAgIGNvbnN0IHRyYW5zYWN0aW9uS2V5ID0gYCR7ZXNjcm93SWR9LSR7d2FsbGV0LmFkZHJlc3N9LXJlZnVuZGA7XG5cbiAgICAvLyBPbmx5IG1hcmsgYXMgZmFpbGVkIGlmIGl0J3Mgbm90IGFuIFwiYWxyZWFkeSBwcm9jZXNzZWRcIiBlcnJvclxuICAgIC8vICh3aGljaCBtaWdodCBhY3R1YWxseSBiZSBzdWNjZXNzZnVsKVxuICAgIGlmICghZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1RoaXMgdHJhbnNhY3Rpb24gaGFzIGFscmVhZHkgYmVlbiBwcm9jZXNzZWQnKSkge1xuICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgIHN0YXR1czogJ2ZhaWxlZCcsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gRW5oYW5jZWQgZXJyb3IgaGFuZGxpbmcgZm9yIFNvbGFuYSB0cmFuc2FjdGlvbnNcbiAgICBpZiAoZXJyb3IubG9ncykge1xuICAgICAgY29uc29sZS5lcnJvcignVHJhbnNhY3Rpb24gbG9nczonLCBlcnJvci5sb2dzKTtcbiAgICB9XG5cbiAgICAvLyBIYW5kbGUgc3BlY2lmaWMgZHVwbGljYXRlIHRyYW5zYWN0aW9uIGVycm9yXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdUaGlzIHRyYW5zYWN0aW9uIGhhcyBhbHJlYWR5IGJlZW4gcHJvY2Vzc2VkJykpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFJlZnVuZCB0cmFuc2FjdGlvbiBhbHJlYWR5IHByb2Nlc3NlZCBlcnJvciAtIGNoZWNraW5nIGlmIGVzY3JvdyB3YXMgYWN0dWFsbHkgcmVmdW5kZWQuLi4nKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgdGhlIHB1cmNoYXNlIGFjY291bnQgd2FzIGFjdHVhbGx5IGNsb3NlZCAobWVhbmluZyByZWZ1bmQgd2FzIHN1Y2Nlc3NmdWwpXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjb25uZWN0aW9uID0gcHJvZ3JhbS5wcm92aWRlci5jb25uZWN0aW9uO1xuICAgICAgICBjb25zdCBlc2Nyb3dJZEJOID0gbmV3IGFuY2hvci5CTihlc2Nyb3dJZCk7XG4gICAgICAgIGNvbnN0IFtwdXJjaGFzZVBkYV0gPSBQdWJsaWNLZXkuZmluZFByb2dyYW1BZGRyZXNzU3luYyhcbiAgICAgICAgICBbQnVmZmVyLmZyb20oXCJwdXJjaGFzZVwiKSwgZXNjcm93SWRCTi50b0FycmF5TGlrZShCdWZmZXIsIFwibGVcIiwgOCldLFxuICAgICAgICAgIHByb2dyYW0ucHJvZ3JhbUlkXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3QgcHVyY2hhc2VBY2NvdW50ID0gYXdhaXQgY29ubmVjdGlvbi5nZXRBY2NvdW50SW5mbyhwdXJjaGFzZVBkYSk7XG4gICAgICAgIGlmICghcHVyY2hhc2VBY2NvdW50KSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBQdXJjaGFzZSBhY2NvdW50IG5vIGxvbmdlciBleGlzdHMgLSByZWZ1bmQgd2FzIGFjdHVhbGx5IHN1Y2Nlc3NmdWwhJyk7XG5cbiAgICAgICAgICAvLyBUcnkgdG8gZmluZCB0aGUgdHJhbnNhY3Rpb24gc2lnbmF0dXJlIGluIHJlY2VudCBoaXN0b3J5XG4gICAgICAgICAgY29uc3QgYnV5ZXJQdWJrZXkgPSBuZXcgUHVibGljS2V5KHdhbGxldC5hZGRyZXNzKTtcbiAgICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gYXdhaXQgY29ubmVjdGlvbi5nZXRTaWduYXR1cmVzRm9yQWRkcmVzcyhidXllclB1YmtleSwgeyBsaW1pdDogMTAgfSk7XG5cbiAgICAgICAgICBsZXQgZm91bmRUeElkID0gbnVsbDtcbiAgICAgICAgICBmb3IgKGNvbnN0IHNpZyBvZiBzaWduYXR1cmVzKSB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHNpZ25hdHVyZSBpcyByZWNlbnQgKHdpdGhpbiBsYXN0IDUgbWludXRlcylcbiAgICAgICAgICAgIGNvbnN0IGZpdmVNaW51dGVzQWdvID0gRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDA7XG4gICAgICAgICAgICBpZiAoc2lnLmJsb2NrVGltZSAmJiBzaWcuYmxvY2tUaW1lICogMTAwMCA+IGZpdmVNaW51dGVzQWdvKSB7XG4gICAgICAgICAgICAgIGZvdW5kVHhJZCA9IHNpZy5zaWduYXR1cmU7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghZm91bmRUeElkKSB7XG4gICAgICAgICAgICBmb3VuZFR4SWQgPSBgc3VjY2Vzc2Z1bC1yZWZ1bmQtJHtlc2Nyb3dJZH0tJHtEYXRlLm5vdygpfWA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gTWFyayBhcyBjb21wbGV0ZWQgaW4gb3VyIHN0YXRlXG4gICAgICAgICAgdHJhbnNhY3Rpb25TdGF0ZXMuc2V0KHRyYW5zYWN0aW9uS2V5LCB7XG4gICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgdHhJZDogZm91bmRUeElkXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+OiSBSZXR1cm5pbmcgc3VjY2Vzc2Z1bCByZWZ1bmQgdHJhbnNhY3Rpb24gSUQ6JywgZm91bmRUeElkKTtcbiAgICAgICAgICByZXR1cm4gZm91bmRUeElkO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinYwgUHVyY2hhc2UgYWNjb3VudCBzdGlsbCBleGlzdHMgLSByZWZ1bmQgYWN0dWFsbHkgZmFpbGVkJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGNoZWNrRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCB2ZXJpZnkgcmVmdW5kIHN0YXR1czonLCBjaGVja0Vycm9yKTtcbiAgICAgIH1cblxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZWZ1bmQgdHJhbnNhY3Rpb24gYWxyZWFkeSBwcm9jZXNzZWQuIFBsZWFzZSByZWZyZXNoIHRoZSBwYWdlIGFuZCB0cnkgYWdhaW4uJyk7XG4gICAgfVxuXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdBY2NvdW50Tm90SW5pdGlhbGl6ZWQnKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUb2tlbiBhY2NvdW50IG5vdCBpbml0aWFsaXplZC4gUGxlYXNlIGVuc3VyZSB5b3UgaGF2ZSB0aGUgcmVxdWlyZWQgdG9rZW5zLicpO1xuICAgIH1cblxuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnSW5zdWZmaWNpZW50RnVuZHMnKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnN1ZmZpY2llbnQgZnVuZHMgdG8gY29tcGxldGUgdGhlIHJlZnVuZCB0cmFuc2FjdGlvbi4nKTtcbiAgICB9XG5cbiAgICAvLyBIYW5kbGUgdHJhbnNhY3Rpb24gdGltZW91dCBvciBibG9ja2hhc2ggZXhwaXJ5XG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdibG9ja2hhc2ggbm90IGZvdW5kJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ1RyYW5zYWN0aW9uIGV4cGlyZWQnKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZWZ1bmQgdHJhbnNhY3Rpb24gZXhwaXJlZC4gUGxlYXNlIHRyeSBhZ2FpbiB3aXRoIGEgZnJlc2ggdHJhbnNhY3Rpb24uJyk7XG4gICAgfVxuXG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBJbml0aWF0ZSBhIGRpc3B1dGUgZm9yIGFuIGVzY3JvdyB0cmFuc2FjdGlvblxuICogQ2FuIGJlIGNhbGxlZCBieSBidXllciBvciBzZWxsZXIgd2hlbiB0aGVyZSdzIGFuIGlzc3VlIHdpdGggdGhlIHRyYWRlXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBpbml0aWF0ZUVzY3Jvd0Rpc3B1dGUoXG4gIHRyYWRlSWQ6IG51bWJlcixcbiAgcmVhc29uOiBzdHJpbmcsXG4gIGluaXRpYXRvclJvbGU6ICdidXllcicgfCAnc2VsbGVyJ1xuKTogUHJvbWlzZTx7IGRpc3B1dGVJZDogbnVtYmVyOyBzdGF0dXM6IHN0cmluZyB9PiB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ0luaXRpYXRpbmcgZXNjcm93IGRpc3B1dGU6JywgeyB0cmFkZUlkLCByZWFzb24sIGluaXRpYXRvclJvbGUgfSk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGluaXRpYXRlRGlzcHV0ZSh7XG4gICAgICB0cmFkZUlkLFxuICAgICAgcmVhc29uLFxuICAgICAgaW5pdGlhdG9yUm9sZVxuICAgIH0pO1xuXG4gICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gMjAxKSB7XG4gICAgICBjb25zb2xlLmxvZygnRGlzcHV0ZSBpbml0aWF0ZWQgc3VjY2Vzc2Z1bGx5OicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gaW5pdGlhdGUgZGlzcHV0ZScpO1xuICAgIH1cblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Rpc3B1dGUgaW5pdGlhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogQ2hlY2sgaWYgYSB0cmFkZSBpcyBlbGlnaWJsZSBmb3IgZGlzcHV0ZVxuICogRGlzcHV0ZXMgY2FuIG9ubHkgYmUgaW5pdGlhdGVkIGZvciBlc2Nyb3dlZCB0cmFkZXMgd2l0aGluIHRoZSBkZWFkbGluZVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2FuSW5pdGlhdGVEaXNwdXRlKFxuICB0cmFkZVN0YXR1czogc3RyaW5nLFxuICB0cmFkZUNyZWF0ZWRBdDogc3RyaW5nLFxuICBkaXNwdXRlRGVhZGxpbmVEYXlzOiBudW1iZXIgPSAyLFxuICBkaXNwdXRlU3RhdHVzPzogc3RyaW5nXG4pOiB7IGNhbkRpc3B1dGU6IGJvb2xlYW47IHJlYXNvbj86IHN0cmluZyB9IHtcbiAgLy8gQ2hlY2sgaWYgdHJhZGUgaXMgaW4gZXNjcm93ZWQgc3RhdHVzXG4gIGlmICh0cmFkZVN0YXR1cyAhPT0gJ2VzY3Jvd2VkJykge1xuICAgIHJldHVybiB7XG4gICAgICBjYW5EaXNwdXRlOiBmYWxzZSxcbiAgICAgIHJlYXNvbjogJ0Rpc3B1dGUgY2FuIG9ubHkgYmUgaW5pdGlhdGVkIGZvciBlc2Nyb3dlZCB0cmFkZXMnXG4gICAgfTtcbiAgfVxuXG4gIC8vIENoZWNrIGlmIGRpc3B1dGUgYWxyZWFkeSBleGlzdHNcbiAgaWYgKGRpc3B1dGVTdGF0dXMgJiYgZGlzcHV0ZVN0YXR1cyAhPT0gJ25vbmUnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGNhbkRpc3B1dGU6IGZhbHNlLFxuICAgICAgcmVhc29uOiAnQSBkaXNwdXRlIGFscmVhZHkgZXhpc3RzIGZvciB0aGlzIHRyYWRlJ1xuICAgIH07XG4gIH1cblxuICAvLyBDaGVjayBpZiBkaXNwdXRlIGRlYWRsaW5lIGhhcyBwYXNzZWRcbiAgY29uc3QgY3JlYXRlZERhdGUgPSBuZXcgRGF0ZSh0cmFkZUNyZWF0ZWRBdCk7XG4gIGNvbnN0IGRlYWRsaW5lRGF0ZSA9IG5ldyBEYXRlKGNyZWF0ZWREYXRlKTtcbiAgZGVhZGxpbmVEYXRlLnNldERhdGUoZGVhZGxpbmVEYXRlLmdldERhdGUoKSArIGRpc3B1dGVEZWFkbGluZURheXMpO1xuXG4gIGlmIChuZXcgRGF0ZSgpID4gZGVhZGxpbmVEYXRlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGNhbkRpc3B1dGU6IGZhbHNlLFxuICAgICAgcmVhc29uOiAnRGlzcHV0ZSBkZWFkbGluZSBoYXMgcGFzc2VkJ1xuICAgIH07XG4gIH1cblxuICByZXR1cm4geyBjYW5EaXNwdXRlOiB0cnVlIH07XG59XG5cbi8qKlxuICogR2V0IGRpc3B1dGUgc3RhdHVzIGluZm9ybWF0aW9uIGZvciBkaXNwbGF5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXREaXNwdXRlU3RhdHVzSW5mbyhkaXNwdXRlU3RhdHVzOiBzdHJpbmcpIHtcbiAgc3dpdGNoIChkaXNwdXRlU3RhdHVzKSB7XG4gICAgY2FzZSAnbm9uZSc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBsYWJlbDogJ05vIERpc3B1dGUnLFxuICAgICAgICBjb2xvcjogJ3RleHQtZ3JheS02MDAnLFxuICAgICAgICBiZ0NvbG9yOiAnYmctZ3JheS0xMDAnXG4gICAgICB9O1xuICAgIGNhc2UgJ29wZW4nOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbGFiZWw6ICdEaXNwdXRlIE9wZW4nLFxuICAgICAgICBjb2xvcjogJ3RleHQteWVsbG93LTgwMCcsXG4gICAgICAgIGJnQ29sb3I6ICdiZy15ZWxsb3ctMTAwJ1xuICAgICAgfTtcbiAgICBjYXNlICdhc3NpZ25lZCc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBsYWJlbDogJ1VuZGVyIFJldmlldycsXG4gICAgICAgIGNvbG9yOiAndGV4dC1ibHVlLTgwMCcsXG4gICAgICAgIGJnQ29sb3I6ICdiZy1ibHVlLTEwMCdcbiAgICAgIH07XG4gICAgY2FzZSAncmVzb2x2ZWQnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbGFiZWw6ICdEaXNwdXRlIFJlc29sdmVkJyxcbiAgICAgICAgY29sb3I6ICd0ZXh0LWdyZWVuLTgwMCcsXG4gICAgICAgIGJnQ29sb3I6ICdiZy1ncmVlbi0xMDAnXG4gICAgICB9O1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBsYWJlbDogJ1Vua25vd24gU3RhdHVzJyxcbiAgICAgICAgY29sb3I6ICd0ZXh0LWdyYXktNjAwJyxcbiAgICAgICAgYmdDb2xvcjogJ2JnLWdyYXktMTAwJ1xuICAgICAgfTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlB1YmxpY0tleSIsImdldEFzc29jaWF0ZWRUb2tlbkFkZHJlc3NTeW5jIiwiQVNTT0NJQVRFRF9UT0tFTl9QUk9HUkFNX0lEIiwiVE9LRU5fUFJPR1JBTV9JRCIsImNyZWF0ZUFzc29jaWF0ZWRUb2tlbkFjY291bnRJbnN0cnVjdGlvbiIsImNyZWF0ZVN5bmNOYXRpdmVJbnN0cnVjdGlvbiIsImFuY2hvciIsInByb2dyYW0iLCJpbml0aWF0ZURpc3B1dGUiLCJBcHBFcnJvciIsIkVzY3Jvd0Vycm9yIiwibWVzc2FnZSIsImNvZGUiLCJzdGF0dXMiLCJuYW1lIiwiVHJhbnNhY3Rpb25FcnJvciIsInR4SWQiLCJJbnN1ZmZpY2llbnRGdW5kc0Vycm9yIiwiRHVwbGljYXRlVHJhbnNhY3Rpb25FcnJvciIsIkJsb2NrY2hhaW5FcnJvciIsIm9yaWdpbmFsRXJyb3IiLCJ0cmFuc2FjdGlvblN0YXRlcyIsIk1hcCIsImxhc3RUcmFuc2FjdGlvbkF0dGVtcHRzIiwiY2xlYW51cE9sZFRyYW5zYWN0aW9ucyIsImZpdmVNaW51dGVzQWdvIiwiRGF0ZSIsIm5vdyIsImtleSIsInN0YXRlIiwiZW50cmllcyIsInRpbWVzdGFtcCIsImRlbGV0ZSIsInNlbmRUcmFuc2FjdGlvbldpdGhSZXRyeSIsImNvbm5lY3Rpb24iLCJzaWduZWRUcmFuc2FjdGlvbiIsIm1heFJldHJpZXMiLCJyZXRyeURlbGF5IiwibGFzdEVycm9yIiwiYXR0ZW1wdCIsImNvbnNvbGUiLCJsb2ciLCJzZW5kUmF3VHJhbnNhY3Rpb24iLCJzZXJpYWxpemUiLCJza2lwUHJlZmxpZ2h0IiwicHJlZmxpZ2h0Q29tbWl0bWVudCIsImVycm9yIiwiaW5jbHVkZXMiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJjaGVja1RyYW5zYWN0aW9uU3VjY2VzcyIsInNpZ25hdHVyZSIsIm1heEF0dGVtcHRzIiwiaSIsImdldFNpZ25hdHVyZVN0YXR1cyIsInZhbHVlIiwiY29uZmlybWF0aW9uU3RhdHVzIiwiZXJyIiwic3VjY2VzcyIsIkpTT04iLCJzdHJpbmdpZnkiLCJjaGVja0Nvbm5lY3Rpb25IZWFsdGgiLCJzdGFydFRpbWUiLCJzbG90IiwiZ2V0U2xvdCIsInJlc3BvbnNlVGltZSIsImhlYWx0aHkiLCJ2YWxpZGF0ZVdhbGxldENvbm5lY3Rpb24iLCJ3YWxsZXQiLCJ2YWxpZCIsInNpZ25UcmFuc2FjdGlvbiIsImFkZHJlc3MiLCJjcmVhdGVFc2Nyb3dCdXlUcmFuc2FjdGlvbiIsImVzY3Jvd1R4RGF0YSIsIndhbGxldFZhbGlkYXRpb24iLCJlc2Nyb3dJZCIsInNvbEFtb3VudCIsIk51bWJlciIsInByb3ZpZGVyIiwiY29ubmVjdGlvbkhlYWx0aCIsInRyYW5zYWN0aW9uS2V5IiwibGFzdEF0dGVtcHQiLCJnZXQiLCJzZXQiLCJNYXRoIiwicmFuZG9tIiwiZXhpc3RpbmdTdGF0ZSIsIkVycm9yIiwiYnV5ZXJQdWJrZXkiLCJlc2Nyb3dJZEJOIiwiQk4iLCJwdXJjaGFzZVBkYSIsImZpbmRQcm9ncmFtQWRkcmVzc1N5bmMiLCJCdWZmZXIiLCJmcm9tIiwidG9BcnJheUxpa2UiLCJwcm9ncmFtSWQiLCJwdXJjaGFzZUFjY291bnQiLCJnZXRBY2NvdW50SW5mbyIsInNpZ25hdHVyZXMiLCJnZXRTaWduYXR1cmVzRm9yQWRkcmVzcyIsImxpbWl0Iiwic2lnIiwidGVuTWludXRlc0FnbyIsImJsb2NrVGltZSIsInBsYWNlaG9sZGVyVHhJZCIsImNoZWNrRXJyb3IiLCJwZXJrVG9rZW5BbW91bnQiLCJ2YXVsdFBkYSIsInB1cmNoYXNlVG9rZW5NaW50IiwicGVya1Rva2VuTWludCIsImJ1eWVyV2FsbGV0IiwiZXNjcm93SWRTdHIiLCJTdHJpbmciLCJ0ZXN0IiwiYnV5ZXJCYWxhbmNlIiwiZ2V0QmFsYW5jZSIsInNvbEFtb3VudEJOIiwicGVya1Rva2VuQW1vdW50Qk4iLCJwdXJjaGFzZVRva2VuTWludFB1YmtleSIsInBlcmtUb2tlbk1pbnRQdWJrZXkiLCJwdXJjaGFzZVBkYVB1YmtleSIsInZhdWx0UGRhUHVia2V5IiwiYnV5ZXJUb2tlbkFjY291bnQiLCJidXllckF0YUluZm8iLCJwcmVJbnN0cnVjdGlvbnMiLCJpc1dyYXBwZWRTT0wiLCJlcXVhbHMiLCJ0b1N0cmluZyIsImNyZWF0ZUF0YUl4IiwicHVzaCIsInJlcXVpcmVkQW1vdW50IiwicmVudEV4ZW1wdGlvbiIsImdldE1pbmltdW1CYWxhbmNlRm9yUmVudEV4ZW1wdGlvbiIsInRvdGFsQW1vdW50TmVlZGVkIiwidHJhbnNmZXJJeCIsIndlYjMiLCJTeXN0ZW1Qcm9ncmFtIiwidHJhbnNmZXIiLCJmcm9tUHVia2V5IiwidG9QdWJrZXkiLCJsYW1wb3J0cyIsInN5bmNJeCIsInR4IiwibWV0aG9kcyIsImVzY3Jvd0J1eSIsImFjY291bnRzIiwic2lnbmVyIiwicHVyY2hhc2VUb2tlbkF0YSIsInB1cmNoYXNlIiwidmF1bHQiLCJhc3NvY2lhdGVkVG9rZW5Qcm9ncmFtIiwidG9rZW5Qcm9ncmFtIiwic3lzdGVtUHJvZ3JhbSIsInRyYW5zYWN0aW9uIiwiYmxvY2toYXNoIiwibGFzdFZhbGlkQmxvY2tIZWlnaHQiLCJnZXRMYXRlc3RCbG9ja2hhc2giLCJyZWNlbnRCbG9ja2hhc2giLCJmZWVQYXllciIsIm1lbW9JbnN0cnVjdGlvbiIsIlRyYW5zYWN0aW9uSW5zdHJ1Y3Rpb24iLCJrZXlzIiwiZGF0YSIsImFkZCIsInNsaWNlIiwic2lnbmVkVHgiLCJjb25maXJtYXRpb24iLCJjb25maXJtVHJhbnNhY3Rpb24iLCJsb2dzIiwiZm91bmRUeElkIiwiY3JlYXRlRXNjcm93QWNjZXB0VHJhbnNhY3Rpb24iLCJwdXJjaGFzZVRva2VuQW1vdW50Iiwic2VsbGVyV2FsbGV0IiwidHJhZGVJZCIsImNhbGN1bGF0ZWRQdXJjaGFzZUFtb3VudCIsImNhbGN1bGF0ZWRQZXJrQW1vdW50IiwiY2FsY3VsYXRlZFB1cmNoYXNlTWludCIsImNhbGN1bGF0ZWRQZXJrTWludCIsImNhbGN1bGF0ZWRFc2Nyb3dJZCIsInRyYWRlRGF0YSIsImdldFRyYWRlRGV0YWlscyIsInRyYWRlUmVzcG9uc2UiLCJpZCIsImVzY3Jvd1R4SWQiLCJhbW91bnQiLCJwcmljZSIsIm51bWVyaWNFc2Nyb3dJZCIsImV4dHJhIiwiZXh0cmFEYXRhIiwicGFyc2UiLCJlIiwicGVya1ByaWNlIiwicGVya0RldGFpbHMiLCJwZXJrVG9rZW5zVG9SZWNlaXZlIiwiZmxvb3IiLCJ0b2tlbiIsInRva2VuQWRkcmVzcyIsInB1cmNoYXNlQW1vdW50IiwicGVya0Ftb3VudCIsInB1cmNoYXNlTWludCIsInBlcmtNaW50IiwiYWNjb3VudCIsImZldGNoIiwiYWNjZXB0ZWQiLCJzZWxsZXJQdWJrZXkiLCJwdXJjaGFzZVRva2VuQW1vdW50Qk4iLCJzZWxsZXJQZXJrVG9rZW5BdGEiLCJwZXJrVmF1bHRQZGEiLCJ0b0J1ZmZlciIsInNlbGxlclBlcmtBdGFJbmZvIiwiZXNjcm93QWNjZXB0IiwiY3JlYXRlRXNjcm93U2VsbFRyYW5zYWN0aW9uIiwicGVya0lkIiwid2FybiIsInByb3ZpZGVkIiwiZnJvbVRyYWRlIiwidG8iLCJzZWxsZXJQdXJjaGFzZVRva2VuQXRhIiwiYnV5ZXJQZXJrVG9rZW5BdGEiLCJ3aG9Pd25zV2hhdCIsInB1cmNoYXNlVG9rZW5Hb2VzVG8iLCJwZXJrVG9rZW5Hb2VzVG8iLCJlc2Nyb3dTZWxsIiwiYnV5ZXIiLCJzZWxsZXIiLCJwZXJrVG9rZW5EZXN0aW5hdGlvbkF0YSIsInBlcmtWYXVsdCIsImNyZWF0ZUVzY3Jvd1JlZnVuZFRyYW5zYWN0aW9uIiwiZXNjcm93UmVmdW5kIiwiYnV5ZXJUb2tlbkF0YSIsImluaXRpYXRlRXNjcm93RGlzcHV0ZSIsInJlYXNvbiIsImluaXRpYXRvclJvbGUiLCJyZXNwb25zZSIsImNhbkluaXRpYXRlRGlzcHV0ZSIsInRyYWRlU3RhdHVzIiwidHJhZGVDcmVhdGVkQXQiLCJkaXNwdXRlRGVhZGxpbmVEYXlzIiwiZGlzcHV0ZVN0YXR1cyIsImNhbkRpc3B1dGUiLCJjcmVhdGVkRGF0ZSIsImRlYWRsaW5lRGF0ZSIsInNldERhdGUiLCJnZXREYXRlIiwiZ2V0RGlzcHV0ZVN0YXR1c0luZm8iLCJsYWJlbCIsImNvbG9yIiwiYmdDb2xvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});