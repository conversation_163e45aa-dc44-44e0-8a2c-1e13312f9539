import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useTranslation } from '../../../hooks/useTranslation';


import { useAppContext } from '@/contexts/AppContext';

const TwoFactorSetup = dynamic(() => import('../two-factor-setup').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const SecuritySection = () => {
    const { t } = useTranslation();

  const { state } = useAppContext();
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  
  //console.log(state);
  // Check if 2FA is already enabled or in setup process
  useEffect(() => {
    if (state.userBo) {
      // console.log('SecuritySection - 2FA Status:', {
      //   twoFactorStatus: state.userBo.twoFactorStatus,
      //   twoFactorData: state.userBo.twoFactorData
      // });
      
      // If 2FA is enabled or in the process of being set up, show the component
      if (state.userBo.twoFactorStatus || 
          (state.userBo.twoFactorData && state.userBo.twoFactorData.verified === false)) {
        //console.log('SecuritySection - Showing TwoFactorSetup component');
        setShowTwoFactorSetup(true);
      } else {
        //console.log('SecuritySection - Not showing TwoFactorSetup component');
      }
    }
  }, [state.userBo]);

  return (
    <div className='flex flex-col gap-4'>
      <h2 className="font-['IBM_Plex_Sans'] font-semibold text-[48px] leading-[48px] md:leading-[36px] text-[#17181A] mb-8">{t('securitySection.title')}</h2>

      <div className='flex flex-col gap-5'>
        <h3 className="font-['IBM_Plex_Sans'] font-semibold text-[32px] leading-[36px] md:leading-[27px] text-[#000000] mb-12">{t('securitySection.twoFactor')}</h3>

        {!showTwoFactorSetup ? (
          <div className="bg-white rounded mb-4">
            <div className="flex justify-between items-center lg:flex-row flex-col gap-4">
              <div>
                <h4 className="font-['IBM_Plex_Sans'] font-semibold text-[30px] leading-[30px] text-[#000000] mb-2">Google Authenticator</h4>
                <p className="font-['IBM_Plex_Sans'] font-semibold text-[24px] leading-[30px] text-[#000000]">Random time-bound passcode generated by the app.</p>
              </div>
              <button 
                type='button' 
                className="mt-auto bg-black text-white px-24 py-5 rounded-[10px] font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-white"
                onClick={() => setShowTwoFactorSetup(true)}
              >
                              {t('securitySection.activate')}

              </button>
            </div>
          </div>
        ) : (
          <TwoFactorSetup />
        )}
      </div>
    </div>
  );
};

export default SecuritySection; 