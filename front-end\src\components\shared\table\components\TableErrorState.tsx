import { motion } from 'framer-motion';
import React from 'react';

interface TableErrorStateProps {
  error: string;
  className?: string;
  variant?: 'default' | 'minimal' | 'bordered';
}

const TableErrorState: React.FC<TableErrorStateProps> = ({ 
  error, 
  className = '',
  variant = 'default'
}) => {
  const variantStyles = {
    default: 'bg-white border border-gray-200 shadow-sm',
    minimal: 'bg-transparent',
    bordered: 'bg-white border-2 border-gray-300 shadow-md',
  };

  return (
    <motion.div
      className={`w-full p-8 rounded-xl ${variantStyles[variant]} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="text-center">
        <div className="text-red-500 text-lg font-medium mb-2">Error</div>
        <div className="text-gray-600">{error}</div>
      </div>
    </motion.div>
  );
};

export { TableErrorState }; 