

import * as anchor from "@coral-xyz/anchor";
import { IdlAccounts, Program } from "@coral-xyz/anchor";
import { IDL, Funhi } from "./idl";
import { clusterApiUrl, Connection, Keypair, PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
const config = require("../../config/security");

const decoded = bs58.decode(config.ADMIN_PRIVATE_KEY);
const keypair = Keypair.fromSecretKey(decoded);
const wallet = new anchor.Wallet(keypair);

export const connection = new Connection(
  config.NEXT_PUBLIC_RPC || clusterApiUrl("devnet"),
  "confirmed",
);

const provider = new anchor.AnchorProvider(connection, wallet, {
  preflightCommitment: "confirmed",
});

anchor.setProvider(provider);

// Initialize the program with the modified IDL
export const program: Program<Funhi> = new Program(
  IDL as any,
  provider
);

// Debug output
console.log("Program initialized with ID:", program.programId.toString());

// Account types
export type Global = IdlAccounts<Funhi>["global"];
export type BondingCurve = IdlAccounts<Funhi>["bondingCurve"];
export type CreatorVault = IdlAccounts<Funhi>["creatorVault"];
export type LastWithdraw = IdlAccounts<Funhi>["lastWithdraw"];
