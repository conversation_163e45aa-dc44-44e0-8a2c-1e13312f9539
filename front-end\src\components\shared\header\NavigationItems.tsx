import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';

import { ROUTES } from '@/constants';
import { useTranslation } from '@/hooks/useTranslation';

interface NavigationItemsProps {
  isActiveRoute: (route: string) => boolean;
  handleNavItemClick: (item: any) => void;
  hideCreate: boolean;
  handleCreateTokenClick: () => void;
  handleCreatePerkClick: () => void;
}

const NavigationItems: React.FC<NavigationItemsProps> = ({
  isActiveRoute,
  handleNavItemClick,
  hideCreate,
  handleCreateTokenClick,
  handleCreatePerkClick,
}) => {
  const { t, isReady, i18n, currentLanguage } = useTranslation();

 

  const navigationItems = [
    {
      label: t('navigation.dashboard'),
      route: ROUTES.DASHBOARD,
      icon: "/icons/dashboard.svg",
      activeIcon: "/icons/dashboard.svg",
      requiresAuth: true,
    },
    {
      label: t('navigation.perks'),
      route: ROUTES.PERKS,
      icon: "/icons/perks.svg",
      activeIcon: "/icons/perks.svg",
      requiresAuth: false,
    },
    {
      label: t('navigation.coins'),
      route: ROUTES.HOME,
      icon: "/icons/coin.svg",
      activeIcon: "/icons/coin.svg",
      requiresAuth: false,
    },
  ];

  return (
    !isReady ? null : (
      <motion.div
        className="hidden lg:flex items-center gap-2 xl:gap-3 2xl:gap-4"
        variants={{
          hidden: { opacity: 0, x: 20 },
          visible: {
            opacity: 1,
            x: 0,
            transition: { staggerChildren: 0.1, delayChildren: 0.2 },
          },
        }}
        initial="hidden"
        animate="visible"
      >
        {navigationItems.map((item) => (
          <motion.div
            key={item.label}
            variants={{
              hidden: { opacity: 0, x: 20 },
              visible: { opacity: 1, x: 0 },
            }}
            className={`group h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center gap-2 rounded-full text-sm lg:text-base transition-all duration-300 text-white cursor-pointer ${
              isActiveRoute(item.route)
                ? "bg-black/20 scale-105"
                : "hover:bg-black/10"
            }`}
            onClick={() => handleNavItemClick(item)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Image
              src={isActiveRoute(item.route) ? item.activeIcon : item.icon}
              alt={item.label}
              width={20}
              height={20}
              className="invert group-hover:brightness-0 group-hover:invert filter transition-filter duration-300 ease-in-out h-auto"
            />
            <span className="hidden xl:inline">{item.label}</span>
          </motion.div>
        ))}

        {!hideCreate && (
          <>
            <motion.div
              variants={{
                hidden: { opacity: 0, x: 20 },
                visible: { opacity: 1, x: 0 },
              }}
              className="bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30"
              onClick={handleCreateTokenClick}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="hidden lg:inline">{t('coins.createCoin')}</span>
              <span className="lg:hidden">{t('coins.createCoin')}</span>
            </motion.div>
            <motion.div
              variants={{
                hidden: { opacity: 0, x: 20 },
                visible: { opacity: 1, x: 0 },
              }}
              className="bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30 ml-1"
              onClick={handleCreatePerkClick}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="hidden lg:inline">{t('perks.createPerk')}</span>
              <span className="lg:hidden">{t('perks.perk')}</span>
            </motion.div>
          </>
        )}
      </motion.div>
    )
  );
};

export default NavigationItems; 