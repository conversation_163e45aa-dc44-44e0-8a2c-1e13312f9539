import { toast } from "react-hot-toast";
import { isProduction } from "../config/environment";

// Base error class for application errors
export class AppError extends Error {
  public code: string;
  public status: number;
  public timestamp: Date;
  public retryable: boolean;
  public userFriendly: boolean;

  constructor(
    message: string,
    code: string,
    status: number = 500,
    retryable: boolean = true,
    userFriendly: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.status = status;
    this.timestamp = new Date();
    this.retryable = retryable;
    this.userFriendly = userFriendly;
  }

  toJSON() {
    return {
      message: this.message,
      code: this.code,
      statusCode: this.status,
      timestamp: this.timestamp,
      retryable: this.retryable,
      userFriendly: this.userFriendly,
    };
  }
}

// Validation-related errors
export class ValidationError extends AppError {
  public field?: string;

  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400, false, true);
    this.name = 'ValidationError';
    this.field = field;
  }
}

// Authentication-related errors
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTH_ERROR', 401, false, true);
    this.name = 'AuthenticationError';
  }
}

// Network-related errors
export class NetworkError extends AppError {
  constructor(message: string = 'Network error occurred') {
    super(message, 'NETWORK_ERROR', 0, true, true);
    this.name = 'NetworkError';
  }
}

// Wallet-related errors
export class WalletError extends AppError {
  constructor(message: string = 'Wallet operation failed') {
    super(message, 'WALLET_ERROR', 400, true, true);
    this.name = 'WalletError';
  }
}

// Form-related errors
export class FormError extends AppError {
  public field?: string;

  constructor(message: string, field?: string) {
    super(message, 'FORM_ERROR', 400, false, true);
    this.name = 'FormError';
    this.field = field;
  }
}

// API-related errors
export class APIError extends AppError {
  constructor(message: string, status: number = 500) {
    super(message, 'API_ERROR', status, true, true);
    this.name = 'APIError';
  }
}

// Standardized Toast Message Constants
export const TOAST_MESSAGES = {
  // Authentication & Login
  AUTH: {
    LOGIN_SUCCESS: 'Successfully logged in! Welcome back.',
    LOGIN_FAILED: 'Login failed. Please check your credentials and try again.',
    LOGIN_REQUIRED: 'Please log in to access this feature.',
    LOGOUT_SUCCESS: 'Successfully logged out. See you soon!',
    SESSION_EXPIRED: 'Your session has expired. Please log in again to continue.',
    TWO_FACTOR_SUCCESS: 'Two-factor authentication successful!',
    TWO_FACTOR_INVALID: 'Invalid verification code. Please check and try again.',
    TWO_FACTOR_REQUIRED: 'Please enter your 6-digit verification code.',
  },

  // Wallet Operations
  WALLET: {
    CONNECT_REQUIRED: 'Please connect your Solana wallet to continue.',
    CONNECTION_FAILED: 'Failed to connect wallet. Please try again or check your wallet extension.',
    DISCONNECTED: 'Wallet disconnected. Please reconnect to continue transactions.',
    TRANSACTION_FAILED: 'Transaction failed. Please check your wallet balance and try again.',
    INSUFFICIENT_FUNDS: 'Insufficient funds in your wallet. Please add funds and try again.',
    WRONG_NETWORK: 'Please switch to the correct network in your wallet.',
  },

  // Form Operations
  FORM: {
    VALIDATION_FAILED: 'Please check your input and fix any errors before submitting.',
    REQUIRED_FIELDS: 'Please fill in all required fields.',
    INVALID_EMAIL: 'Please enter a valid email address.',
    INVALID_URL: 'Please enter a valid URL (e.g., https://example.com).',
    INVALID_PRICE: 'Please enter a valid price (numbers only).',
    INVALID_AMOUNT: 'Please enter a valid amount.',
    IMAGE_UPLOAD_FAILED: 'Failed to upload image. Please try again with a different file.',
    FILE_SIZE_TOO_LARGE: 'File size is too large. Please choose a smaller file (max 5MB).',
    INVALID_FILE_TYPE: 'Invalid file type. Please upload a valid image file.',
  },

  // Token Operations
  TOKEN: {
    CREATE_SUCCESS: 'Token created successfully! Your token is now live.',
    CREATE_FAILED: 'Failed to create token. Please check your input and try again.',
    FETCH_FAILED: 'Failed to load token data. Please refresh the page and try again.',
    UPDATE_SUCCESS: 'Token updated successfully!',
    UPDATE_FAILED: 'Failed to update token. Please try again.',
    PURCHASE_SUCCESS: 'Token purchased successfully! Check your wallet for confirmation.',
    PURCHASE_FAILED: 'Failed to purchase token. Please check your wallet balance and try again.',
  },

  // Perk Operations
  PERK: {
    CREATE_SUCCESS: 'Perk created successfully! Your perk is now available in the shop.',
    CREATE_FAILED: 'Failed to create perk. Please check your input and try again.',
    PURCHASE_SUCCESS: 'Perk purchased successfully! Check your email for details.',
    PURCHASE_FAILED: 'Failed to purchase perk. Please check your payment method and try again.',
    FETCH_FAILED: 'Failed to load perks. Please refresh the page and try again.',
    OUT_OF_STOCK: 'This perk is currently out of stock. Please try again later.',
  },

  // Profile Operations
  PROFILE: {
    UPDATE_SUCCESS: 'Profile updated successfully!',
    UPDATE_FAILED: 'Failed to update profile. Please check your input and try again.',
    FETCH_FAILED: 'Failed to load profile data. Please refresh the page and try again.',
    PASSWORD_CHANGE_SUCCESS: 'Password changed successfully!',
    PASSWORD_CHANGE_FAILED: 'Failed to change password. Please check your current password and try again.',
    PASSWORD_MISMATCH: 'New passwords do not match. Please enter matching passwords.',
    WEAK_PASSWORD: 'Password is too weak. Please use at least 8 characters with letters and numbers.',
  },

  // Comment Operations
  COMMENT: {
    POST_SUCCESS: 'Comment posted successfully!',
    POST_FAILED: 'Failed to post comment. Please try again.',
    FETCH_FAILED: 'Failed to load comments. Please refresh the page and try again.',
    DELETE_SUCCESS: 'Comment deleted successfully!',
    DELETE_FAILED: 'Failed to delete comment. Please try again.',
    EDIT_SUCCESS: 'Comment updated successfully!',
    EDIT_FAILED: 'Failed to update comment. Please try again.',
  },

  // Airdrop Operations
  AIRDROP: {
    CLAIM_SUCCESS: 'Airdrop claimed successfully! Tokens have been added to your wallet.',
    CLAIM_FAILED: 'Failed to claim airdrop. Please check your wallet connection and try again.',
    ALREADY_CLAIMED: 'This airdrop has already been claimed.',
    EXPIRED: 'This airdrop has expired and can no longer be claimed.',
    CREATE_SUCCESS: 'Airdrop created successfully!',
    CREATE_FAILED: 'Failed to create airdrop. Please check your token balance and try again.',
  },

  // Network & API
  NETWORK: {
    CONNECTION_FAILED: 'Connection failed. Please check your internet connection and try again.',
    TIMEOUT: 'Request timed out. Please try again.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    RATE_LIMITED: 'Too many requests. Please wait a moment and try again.',
    MAINTENANCE: 'Service is temporarily unavailable for maintenance. Please try again later.',
  },

  // Data Operations
  DATA: {
    FETCH_FAILED: 'Failed to load data. Please refresh the page and try again.',
    SAVE_SUCCESS: 'Data saved successfully!',
    SAVE_FAILED: 'Failed to save data. Please try again.',
    DELETE_SUCCESS: 'Data deleted successfully!',
    DELETE_FAILED: 'Failed to delete data. Please try again.',
    SYNC_FAILED: 'Failed to sync data. Please check your connection and try again.',
  },

  // General
  GENERAL: {
    UNEXPECTED_ERROR: 'An unexpected error occurred. Please try again or contact support if the issue persists.',
    FEATURE_UNAVAILABLE: 'This feature is currently unavailable. Please try again later.',
    PERMISSION_DENIED: 'You do not have permission to perform this action.',
    INVALID_INPUT: 'Invalid input provided. Please check your data and try again.',
    PROCESSING: 'Processing your request... Please wait.',
    SUCCESS: 'Operation completed successfully!',
  }
};

// Enhanced user-friendly error messages mapping
const USER_FRIENDLY_MESSAGES: Record<string, string> = {
  'NETWORK_ERROR': TOAST_MESSAGES.NETWORK.CONNECTION_FAILED,
  'AUTH_ERROR': TOAST_MESSAGES.AUTH.LOGIN_REQUIRED,
  'WALLET_ERROR': TOAST_MESSAGES.WALLET.CONNECTION_FAILED,
  'TRANSACTION_ERROR': TOAST_MESSAGES.WALLET.TRANSACTION_FAILED,
  'VALIDATION_ERROR': TOAST_MESSAGES.FORM.VALIDATION_FAILED,
  'FORM_ERROR': TOAST_MESSAGES.FORM.VALIDATION_FAILED,
  'API_ERROR': TOAST_MESSAGES.NETWORK.SERVER_ERROR,
  'RATE_LIMIT_ERROR': TOAST_MESSAGES.NETWORK.RATE_LIMITED,
  'UNKNOWN_ERROR': TOAST_MESSAGES.GENERAL.UNEXPECTED_ERROR,
};

// Error handler utility
const handleError = (error: unknown): AppError => {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Error) {
    return new AppError(error.message, 'UNKNOWN_ERROR');
  }

  return new AppError('An unexpected error occurred', 'UNKNOWN_ERROR');
};

// Enhanced error logger utility
const logError = (error: unknown, context?: string): string => {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    error: typeof error === 'string' ? error : error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : error,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    url: typeof window !== 'undefined' ? window.location.href : 'server',
  };

  // Show user-friendly toast
  const appError = handleError(error);
  const userMessage = typeof error === 'string' ? error : appError?.userFriendly
    ? USER_FRIENDLY_MESSAGES[appError?.code] || appError?.message
    : USER_FRIENDLY_MESSAGES['UNKNOWN_ERROR'];


  // In production, send to error tracking service
  if (isProduction()) {
    // Example: sendToErrorTrackingService(errorInfo);
    console.log('Production error logging:', errorInfo);
  }
  return userMessage;
};

// Enhanced toast system with better error handling
export const showErrorToast = (error: unknown, context?: string) => {
  const userMessage = logError(error, context);
  toast.error(userMessage, {
    duration: 5000,
    position: 'top-right',
  });
};

export const showSuccessToast = (message: string) => {
  toast.success(message, {
    duration: 3000,
    position: 'top-right',
  });
};

export const showWarningToast = (message: string) => {
  toast(message, {
    duration: 4000,
    position: 'top-right',
    style: {
      background: '#FEF3C7',
      color: '#92400E',
      border: '1px solid #F59E0B',
    },
  });
};

export const showInfoToast = (message: string) => {
  toast(message, {
    duration: 3000,
    position: 'top-right',
    style: {
      background: '#DBEAFE',
      color: '#1E40AF',
      border: '1px solid #3B82F6',
    },
  });
};

// Utility function to create specific error types
export const createAuthError = (message?: string) =>
  new AuthenticationError(message || TOAST_MESSAGES.AUTH.LOGIN_REQUIRED);

export const createWalletError = (message?: string) =>
  new WalletError(message || TOAST_MESSAGES.WALLET.CONNECTION_FAILED);

export const createFormError = (message?: string, field?: string) =>
  new FormError(message || TOAST_MESSAGES.FORM.VALIDATION_FAILED, field);

export const createAPIError = (message?: string, status?: number) =>
  new APIError(message || TOAST_MESSAGES.NETWORK.SERVER_ERROR, status);

export const createNetworkError = (message?: string) =>
  new NetworkError(message || TOAST_MESSAGES.NETWORK.CONNECTION_FAILED);

// Helper function to handle API responses with better error messaging
export const handleAPIResponse = (response: any, successMessage?: string) => {
  if (response.status >= 200 && response.status < 300) {
    if (successMessage) {
      showSuccessToast(successMessage);
    }
    return response;
  } else {
    const errorMessage = response.data?.message || response.message || TOAST_MESSAGES.NETWORK.SERVER_ERROR;
    throw createAPIError(errorMessage, response.status);
  }
};

// Helper function to handle form validation errors
export const handleFormValidation = (errors: Record<string, string>) => {
  const errorEntries = Object.entries(errors).filter(([_, value]) => value);

  if (errorEntries.length > 0) {
    const firstError = errorEntries[0];
    throw createFormError(firstError[1], firstError[0]);
  }
};
