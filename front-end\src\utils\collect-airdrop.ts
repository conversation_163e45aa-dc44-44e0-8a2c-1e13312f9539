import { claimAirDrop } from "@/axios/requests";

export interface AirdropData {
  isValid: boolean;
  tokenAmount: number;
  message?: string;
}

/**
 * Dummy function to validate airdrop code
 * This should be replaced with actual backend API call when ready
 */
export const validateAirdropCode = async (
  code: string
): Promise<AirdropData> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Dummy validation logic - replace with actual API call
  // For now, codes that are 8+ characters and alphanumeric are considered valid
  const isValidFormat = /^[a-zA-Z0-9]{8,}$/.test(code);

  // Simulate some codes being already claimed or invalid
  const invalidCodes = ['invalid1', 'claimed1', 'expired1'];
  const isBlacklisted = invalidCodes.includes(code.toLowerCase());

  if (!isValidFormat || isBlacklisted) {
    return {
      isValid: false,
      tokenAmount: 0,
      message: 'Invalid or expired airdrop code',
    };
  }

  // Generate random token amount between 1000-10000 for demo
  const tokenAmount = Math.floor(Math.random() * 9000) + 1000;

  return {
    isValid: true,
    tokenAmount,
    message: 'Valid airdrop code',
  };
};

/**
 * Extract airdrop code from URL hash
 */
export const extractAirdropCode = (hash: string): string | null => {
  if (!hash || hash.length <= 1) return null;

  // Remove the # symbol and get the code
  const code = hash.substring(1);

  // Basic validation - should be alphanumeric and at least 6 characters
  if (code.length >= 6 && /^[a-zA-Z0-9]+$/.test(code)) {
    return code;
  }

  return null;
};

/**
 * Claim airdrop tokens
 * This should be replaced with actual backend API call when ready
 */
export const claimAirdropTokens = async (
  code: string,
  walletAddress: string,
): Promise<{ success: boolean; txHash?: string; error?: string }> => {

  try {
    const storedUserBo = localStorage.getItem('userBo');

    if (storedUserBo && storedUserBo !== 'undefined') {

      // Safely parse the userBo string into an object of type UserBo
      const parsedUserBo = JSON.parse(storedUserBo);
      const dataIs = {
        code: code,
        wallet: walletAddress,
        userId: parsedUserBo.id,
      };
      const data = await claimAirDrop(dataIs);
      return {
        success: true,
        txHash: data.signature,
      };
    } else {
      return {
        success: false,
        txHash: '',
      };

    }
  } catch (error) {
    return {
      success: false,
      error: 'Failed to claim airdrop tokens',
    };
  }
};
