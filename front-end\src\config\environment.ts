import { Cluster } from "@solana/web3.js";

/**
 * Environment Configuration
 * 
 * This file centralizes all environment variables used throughout the application.
 * It provides type safety, default values, and validation for environment variables.
 */

// =============================================================================
// Solana Network Configuration
// =============================================================================

export const SOLANA_CONFIG = {
  // Primary RPC endpoint
  RPC_URL: process.env.NEXT_PUBLIC_RPC || 'https://api.devnet.solana.com',
  
  // Connection URL (used as fallback)
  CONNECTION_URL: process.env.NEXT_PUBLIC_CONNECTION_URL || 'https://api.devnet.solana.com',
  
  // Network cluster (devnet, testnet, mainnet-beta)
  CLUSTER: process.env.NEXT_PUBLIC_SOLANA_CLUSTER || 'devnet',
  
  // Raydium SDK cluster configuration
  RAYDIUM_CLUSTER: (process.env.NEXT_PUBLIC_RAYDIUM_CLUSTER || 'devnet') as Cluster,
} as const;

// =============================================================================
// Backend API Configuration
// =============================================================================

export const API_CONFIG = {
  // Backend API base URL
  BASE_URL: process.env.NEXT_PUBLIC_BACKEND_URL,
  
  // API request timeout in milliseconds
  TIMEOUT: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
  
  // Retry configuration
  MAX_RETRIES: parseInt(process.env.NEXT_PUBLIC_API_MAX_RETRIES || '3'),
  RETRY_DELAY: parseInt(process.env.NEXT_PUBLIC_API_RETRY_DELAY || '1000'),
} as const;

// =============================================================================
// Authentication & Wallet Configuration
// =============================================================================

export const AUTH_CONFIG = {
  // Privy App ID for wallet authentication
  PRIVY_APP_ID: process.env.NEXT_PUBLIC_PRIVY_APP_ID,
  
  // Token refresh interval in minutes
  TOKEN_REFRESH_INTERVAL: parseInt(process.env.NEXT_PUBLIC_TOKEN_REFRESH_INTERVAL || '15'),
} as const;

// =============================================================================
// File Upload Configuration
// =============================================================================

export const UPLOAD_CONFIG = {
  // Pinata IPFS API credentials
  PINATA_API_KEY: process.env.NEXT_PUBLIC_PINATA_API_KEY,
  PINATA_API_SECRET: process.env.NEXT_PUBLIC_PINATA_API_SECRET,
  
  // Maximum file size in bytes (default: 10MB)
  MAX_FILE_SIZE: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),
  
  // Allowed file types
  ALLOWED_FILE_TYPES: process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  
  // IPFS gateway URL
  IPFS_GATEWAY: process.env.NEXT_PUBLIC_IPFS_GATEWAY || 'https://gateway.pinata.cloud/ipfs/',
} as const;

// =============================================================================
// Application Settings
// =============================================================================

export const APP_CONFIG = {
  // Maximum coins allowed per user
  MAX_COINS_PER_USER: parseInt(process.env.NEXT_PUBLIC_COIN_ALLOWED_PER_USER || '1'),
  
  // Conversion rate for perks
  CONVERSION_RATE: parseFloat(process.env.NEXT_PUBLIC_CONVERSION_RATE || '155'),
  
  // Environment mode (true for production/live)
  IS_LIVE: process.env.NEXT_PUBLIC_ISLIVE === 'true',
  
  // Debug mode
  DEBUG: process.env.NEXT_PUBLIC_DEBUG === 'true',
  
  // Application name
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'FunHi',
  
  // Application version
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  
  // Support email
  SUPPORT_EMAIL: process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>',
} as const;

// =============================================================================
// Wallet Addresses
// =============================================================================

export const WALLET_CONFIG = {
  // Burn address for token burning operations
  BURN_ADDRESS: process.env.NEXT_PUBLIC_BURN_ADDRESS || '',
  
  // Airdrop wallet address
  AIRDROP_WALLET: process.env.NEXT_PUBLIC_AIRDROP_WALLET || '',
} as const;

// =============================================================================
// Feature Flags
// =============================================================================

export const FEATURE_FLAGS = {
  // Enable real-time notifications
  ENABLE_NOTIFICATIONS: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS !== 'false',
  
  // Enable auto-refresh functionality
  ENABLE_AUTO_REFRESH: process.env.NEXT_PUBLIC_ENABLE_AUTO_REFRESH !== 'false',
} as const;

// =============================================================================
// Performance & Caching Configuration
// =============================================================================

export const PERFORMANCE_CONFIG = {
  // Auto-refresh interval in seconds
  AUTO_REFRESH_INTERVAL: parseInt(process.env.NEXT_PUBLIC_AUTO_REFRESH_INTERVAL || '30'),
} as const;

/**
 * Checks if the application is running in production mode
 * @returns True if in production mode
 */
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};
