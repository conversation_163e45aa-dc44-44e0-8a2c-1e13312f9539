'use client';

import { useEffect } from 'react';
import i18n from '@/lib/i18n';

const I18nInitializer: React.FC = () => {
  useEffect(() => {
    // Ensure i18n is initialized
    if (!i18n.isInitialized) {
      i18n.init();
    }
    
    // Set the initial language
    const currentLang = localStorage.getItem('i18nextLng') || 'en';
    i18n.changeLanguage(currentLang);
    
    // Set document attributes
    if (typeof document !== 'undefined') {
      document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = currentLang;
    }
    
    console.log('i18n initialized with language:', currentLang);
  }, []);

  return null;
};

export default I18nInitializer; 