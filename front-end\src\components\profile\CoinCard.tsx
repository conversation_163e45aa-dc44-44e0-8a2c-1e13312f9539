import React from "react";
import TradeChart from "@/components/shared/chart";
import { Coin } from "./types";
import { useTranslation } from '@/hooks/useTranslation';

interface CoinCardProps {
  coin: Coin;
}

const CoinCard: React.FC<CoinCardProps> = ({ coin }) => {
  const { t } = useTranslation();
  return (
    <div className="w-full  mx-auto " aria-label={`${t('coinCard.coinCard')}: ${coin.name ?? t('coinCard.coin')}`}
      tabIndex={0}
    >
      <div className="border-0 rounded-2xl overflow-hidden shadow-lg bg-[#FF6600]">
        <div className="sm:p-4 p-2 ">
          {/* Header Section */}
          <div className="flex items-center gap-3 mb-4 ">
            {/* Coin Icon or Image */}
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center overflow-hidden">
              {coin.image ? (
                <img
                  src={coin.image}
                  alt={coin.name}
                  className="w-10 h-10 object-cover"
                  aria-label={`${t('coinCard.coinImage')}: ${coin.name}`}
                />
              ) : (
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{coin.ticker?.slice(0,2)?.toUpperCase() ?? "?"}</span>
                </div>
              )}
            </div>
            {/* Coin Info */}
            <div>
              <h2 className="text-white text-base font-bold">
                {coin.name ?? t('coinCard.coin')}
              </h2>
              <div className="text-white/90 text-xs space-y-0.5">
                <div>{t('coinCard.symbol')}: {coin.ticker ?? '-'}</div>
                <div>{t('coinCard.marketCap')}: ${coin.marketCap ?? '-'}</div>
                <div>{t('coinCard.price')}: ${coin.price ?? '-'}</div>
                <div>{t('coinCard.holders')}: -</div>
              </div>
            </div>
          </div>
          {/* Price Chart Section */}
          <div className="bg-white rounded-xl p-2">
            <h3 className="text-gray-800 font-semibold mb-3">{t('coinCard.priceChart')}</h3>
            <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 overflow-hidden h-[280px]">
              <div className="w-full h-full">
                <TradeChart />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoinCard; 