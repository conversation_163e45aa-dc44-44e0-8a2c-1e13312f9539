"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_detail_index_tsx";
exports.ids = ["_ssr_src_components_shared_detail_index_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/detail/index.tsx":
/*!************************************************!*\
  !*** ./src/components/shared/detail/index.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Detail = ({ productName = \"Water bottle\", category = \"Perks\", imageUrl, backgroundColor = \"#E5E5E5\" })=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4 text-sm font-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/perks-shop\",\n                        className: \"text-gray-600 hover:text-gray-900 rounded-full  px-5 py-2 text-[16px] border font-['Inter']\",\n                        children: category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 \",\n                        children: \"/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: `/perks-shop/${productName.toLowerCase().replace(/\\s+/g, '-')}`,\n                        className: \"text-gray-600 hover:text-gray-900 rounded-full  px-5 py-2 text-[16px] border font-['Inter']\",\n                        children: productName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: \"/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-gray-100 px-5 py-2 rounded-full text-gray-900 font-[600] text-[16px] leading-[120%] tracking-[-0.01em] border border-[#1111111A] font-['Inter']\",\n                        children: t('detail.breadcrumb')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] rounded-lg overflow-hidden\",\n                style: {\n                    backgroundColor\n                },\n                children: imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: imageUrl,\n                    alt: productName,\n                    width: 975,\n                    height: 600,\n                    className: \"w-full h-full object-contain py-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center text-gray-400\",\n                    children: t('detail.productImage')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\detail\\\\index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Detail);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/detail/index.tsx\n");

/***/ })

};
;