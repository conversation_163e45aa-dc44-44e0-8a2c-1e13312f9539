"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_twitch_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   twitchSvg: () => (/* binding */ twitchSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst twitchSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#5A3E85\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M18.22 25.7 20 23.91h3.34l2.1-2.1v-6.68H15.4v8.78h2.82v1.77Zm3.87-8.16h1.25v3.66H22.1v-3.66Zm-3.34 0H20v3.66h-1.25v-3.66ZM20 7.9a12 12 0 1 0 0 24 12 12 0 0 0 0-24Zm6.69 14.56-3.66 3.66h-2.72l-1.77 1.78h-1.88V26.1H13.3v-9.82l.94-2.4H26.7v8.56Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=twitch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3R3aXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxAcmVvd25cXGFwcGtpdC11aVxcZGlzdFxcZXNtXFxzcmNcXGFzc2V0c1xcc3ZnXFx0d2l0Y2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCB0d2l0Y2hTdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDQwIDQwXCI+XG4gIDxnIGNsaXAtcGF0aD1cInVybCgjYSlcIj5cbiAgICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2IpXCI+XG4gICAgICA8Y2lyY2xlIGN4PVwiMjBcIiBjeT1cIjE5Ljg5XCIgcj1cIjIwXCIgZmlsbD1cIiM1QTNFODVcIiAvPlxuICAgICAgPGcgY2xpcC1wYXRoPVwidXJsKCNjKVwiPlxuICAgICAgICA8cGF0aFxuICAgICAgICAgIGZpbGw9XCIjZmZmXCJcbiAgICAgICAgICBkPVwiTTE4LjIyIDI1LjcgMjAgMjMuOTFoMy4zNGwyLjEtMi4xdi02LjY4SDE1LjR2OC43OGgyLjgydjEuNzdabTMuODctOC4xNmgxLjI1djMuNjZIMjIuMXYtMy42NlptLTMuMzQgMEgyMHYzLjY2aC0xLjI1di0zLjY2Wk0yMCA3LjlhMTIgMTIgMCAxIDAgMCAyNCAxMiAxMiAwIDAgMCAwLTI0Wm02LjY5IDE0LjU2LTMuNjYgMy42NmgtMi43MmwtMS43NyAxLjc4aC0xLjg4VjI2LjFIMTMuM3YtOS44MmwuOTQtMi40SDI2Ljd2OC41NlpcIlxuICAgICAgICAvPlxuICAgICAgPC9nPlxuICAgIDwvZz5cbiAgPC9nPlxuICA8ZGVmcz5cbiAgICA8Y2xpcFBhdGggaWQ9XCJhXCI+PHJlY3Qgd2lkdGg9XCI0MFwiIGhlaWdodD1cIjQwXCIgZmlsbD1cIiNmZmZcIiByeD1cIjIwXCIgLz48L2NsaXBQYXRoPlxuICAgIDxjbGlwUGF0aCBpZD1cImJcIj48cGF0aCBmaWxsPVwiI2ZmZlwiIGQ9XCJNMCAwaDQwdjQwSDB6XCIgLz48L2NsaXBQYXRoPlxuICAgIDxjbGlwUGF0aCBpZD1cImNcIj48cGF0aCBmaWxsPVwiI2ZmZlwiIGQ9XCJNOCA3Ljg5aDI0djI0SDh6XCIgLz48L2NsaXBQYXRoPlxuICA8L2RlZnM+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHdpdGNoLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js\n"));

/***/ })

}]);