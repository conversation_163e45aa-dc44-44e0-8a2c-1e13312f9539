"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_auth_create-coin-form_FormFields_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLGlCQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBLENBQUU7WUFBQSxFQUFHLGVBQWdCO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhN0Usa0JBQWMsa0VBQWlCLGlCQUFnQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/create-coin-form/FormFields.tsx":
/*!*************************************************************!*\
  !*** ./src/components/auth/create-coin-form/FormFields.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,DollarSign!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,DollarSign!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(app-pages-browser)/./src/components/auth/create-coin-form/constants.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst FormFields = (param)=>{\n    let { formData, errors, isSubmitting, showOptions, onToggleOptions, onChange, onBlur } = param;\n    _s();\n    const inputClass = 'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4 focus-within:outline-[#F58A38] transition-all duration-200';\n    const inputErrorClass = 'self-stretch h-[52px] py-0 rounded outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-start items-center gap-3 w-full mx-auto pl-4 pr-4';\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex flex-col justify-start items-start gap-5 w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.name ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    placeholder: t('createCoinForm.name'),\n                    value: formData.name,\n                    onChange: onChange,\n                    onBlur: onBlur,\n                    className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                    required: true,\n                    disabled: isSubmitting\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.ticker ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        name: \"ticker\",\n                        placeholder: t('createCoinForm.ticker'),\n                        value: formData.ticker,\n                        onChange: onChange,\n                        onBlur: onBlur,\n                        className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                        required: true,\n                        disabled: isSubmitting\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 20,\n                        className: \"text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            errors.ticker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.ticker\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: errors.category ? inputErrorClass : inputClass,\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        name: \"category\",\n                        value: formData.category,\n                        onChange: onChange,\n                        onBlur: onBlur,\n                        className: \"flex-1 bg-transparent outline-none w-full text-gray-700\",\n                        required: true,\n                        disabled: isSubmitting,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                className: \"text-gray-400\",\n                                children: t('createCoinForm.selectCategory')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            _constants__WEBPACK_IMPORTED_MODULE_2__.FORM_CATEGORIES.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: category,\n                                    className: \"text-gray-700\",\n                                    children: category\n                                }, category, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 20,\n                        className: \"text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"text-red-500 text-sm w-full\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: t('createCoinForm.pleaseSelectCategory')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"self-stretch h-36 py-0 pl-4 pr-4 relative rounded outline outline-1 outline-offset-[-1px] outline-gray-300 inline-flex justify-start items-start gap-3 w-full mx-auto focus-within:outline-[#F58A38] transition-all duration-200\",\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    name: \"description\",\n                    placeholder: t('createCoinForm.description'),\n                    value: formData.description,\n                    onChange: onChange,\n                    onBlur: onBlur,\n                    className: \"flex-1 bg-transparent outline-none w-full pt-4 resize-none text-gray-700 placeholder-gray-400\",\n                    required: true,\n                    disabled: isSubmitting\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"w-full mx-auto flex items-center\",\n                variants: _constants__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                    type: \"button\",\n                    onClick: onToggleOptions,\n                    className: \"justify-start text-black text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed focus:outline-none flex items-center gap-2 hover:text-[#F58A38] transition-colors\",\n                    whileHover: {\n                        scale: 1.01\n                    },\n                    disabled: isSubmitting,\n                    children: [\n                        showOptions ? t('createCoinForm.hideMoreOptions') : t('createCoinForm.showMoreOptions'),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                rotate: showOptions ? 180 : 0\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            showOptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"w-full space-y-5\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.2\n                },\n                children: [\n                    {\n                        name: 'telegram',\n                        placeholder: t('createCoinForm.telegram'),\n                        error: errors.telegram\n                    },\n                    {\n                        name: 'website',\n                        placeholder: t('createCoinForm.website'),\n                        error: errors.website\n                    },\n                    {\n                        name: 'twitter',\n                        placeholder: t('createCoinForm.twitter'),\n                        error: errors.twitter\n                    }\n                ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: field.error ? inputErrorClass : inputClass,\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.15\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: field.name,\n                                    placeholder: field.placeholder,\n                                    value: formData[field.name],\n                                    onChange: onChange,\n                                    onBlur: onBlur,\n                                    className: \"flex-1 bg-transparent outline-none w-full text-gray-700 placeholder-gray-400\",\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined),\n                            field.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"text-red-500 text-sm w-full mt-1\",\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                transition: {\n                                    duration: 0.15\n                                },\n                                children: field.error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, field.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, undefined),\n            errors.api && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                className: \"text-red-500 font-semibold text-base w-full mx-auto\",\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: 'auto'\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                transition: {\n                    duration: 0.15\n                },\n                children: errors.api\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\create-coin-form\\\\FormFields.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FormFields, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = FormFields;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormFields);\nvar _c;\n$RefreshReg$(_c, \"FormFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvY3JlYXRlLWNvaW4tZm9ybS9Gb3JtRmllbGRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDYTtBQUNnQjtBQUNGO0FBQ0w7QUFDUTtBQXVDeEQsTUFBTU8sYUFBd0M7UUFBQyxFQUM3Q0MsUUFBUSxFQUNSQyxNQUFNLEVBQ05DLFlBQVksRUFDWkMsV0FBVyxFQUNYQyxlQUFlLEVBQ2ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNQOztJQUNDLE1BQU1DLGFBQ0o7SUFDRixNQUFNQyxrQkFDSjtJQUNGLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdYLHFFQUFjQTtJQUU1QixxQkFDRSw4REFBQ1k7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNsQiwrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVdWLE9BQU9XLElBQUksR0FBR0osa0JBQWtCRDtnQkFDM0NNLFVBQVVoQix5REFBaUJBOzBCQUUzQiw0RUFBQ2lCO29CQUNDQyxNQUFLO29CQUNMSCxNQUFLO29CQUNMSSxhQUFhUCxFQUFFO29CQUNmUSxPQUFPakIsU0FBU1ksSUFBSTtvQkFDcEJQLFVBQVVBO29CQUNWQyxRQUFRQTtvQkFDUkssV0FBVTtvQkFDVk8sUUFBUTtvQkFDUkMsVUFBVWpCOzs7Ozs7Ozs7OztZQUdiRCxPQUFPVyxJQUFJLGtCQUNWLDhEQUFDbkIsK0VBQU1BLENBQUNpQixHQUFHO2dCQUNUQyxXQUFVO2dCQUNWUyxTQUFTO29CQUFFQyxTQUFTO29CQUFHQyxRQUFRO2dCQUFFO2dCQUNqQ0MsU0FBUztvQkFBRUYsU0FBUztvQkFBR0MsUUFBUTtnQkFBTztnQkFDdENFLE1BQU07b0JBQUVILFNBQVM7b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQzlCRyxZQUFZO29CQUFFQyxVQUFVO2dCQUFLOzBCQUU1QnpCLE9BQU9XLElBQUk7Ozs7OzswQkFLaEIsOERBQUNuQiwrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVdWLE9BQU8wQixNQUFNLEdBQUduQixrQkFBa0JEO2dCQUM3Q00sVUFBVWhCLHlEQUFpQkE7O2tDQUUzQiw4REFBQ2lCO3dCQUNDQyxNQUFLO3dCQUNMSCxNQUFLO3dCQUNMSSxhQUFhUCxFQUFFO3dCQUNmUSxPQUFPakIsU0FBUzJCLE1BQU07d0JBQ3RCdEIsVUFBVUE7d0JBQ1ZDLFFBQVFBO3dCQUNSSyxXQUFVO3dCQUNWTyxRQUFRO3dCQUNSQyxVQUFVakI7Ozs7OztrQ0FFWiw4REFBQ1Asa0dBQVVBO3dCQUFDaUMsTUFBTTt3QkFBSWpCLFdBQVU7Ozs7Ozs7Ozs7OztZQUVqQ1YsT0FBTzBCLE1BQU0sa0JBQ1osOERBQUNsQywrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVU7Z0JBQ1ZTLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQ2pDQyxTQUFTO29CQUFFRixTQUFTO29CQUFHQyxRQUFRO2dCQUFPO2dCQUN0Q0UsTUFBTTtvQkFBRUgsU0FBUztvQkFBR0MsUUFBUTtnQkFBRTtnQkFDOUJHLFlBQVk7b0JBQUVDLFVBQVU7Z0JBQUs7MEJBRTVCekIsT0FBTzBCLE1BQU07Ozs7OzswQkFLbEIsOERBQUNsQywrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVdWLE9BQU80QixRQUFRLEdBQUdyQixrQkFBa0JEO2dCQUMvQ00sVUFBVWhCLHlEQUFpQkE7O2tDQUUzQiw4REFBQ2lDO3dCQUNDbEIsTUFBSzt3QkFDTEssT0FBT2pCLFNBQVM2QixRQUFRO3dCQUN4QnhCLFVBQVVBO3dCQUNWQyxRQUFRQTt3QkFDUkssV0FBVTt3QkFDVk8sUUFBUTt3QkFDUkMsVUFBVWpCOzswQ0FFViw4REFBQzZCO2dDQUFPZCxPQUFNO2dDQUFHRSxRQUFRO2dDQUFDUixXQUFVOzBDQUNqQ0YsRUFBRTs7Ozs7OzRCQUVKYix1REFBZUEsQ0FBQ29DLEdBQUcsQ0FBQyxDQUFDSCx5QkFDcEIsOERBQUNFO29DQUFzQmQsT0FBT1k7b0NBQVVsQixXQUFVOzhDQUMvQ2tCO21DQURVQTs7Ozs7Ozs7Ozs7a0NBS2pCLDhEQUFDbkMsa0dBQVdBO3dCQUFDa0MsTUFBTTt3QkFBSWpCLFdBQVU7Ozs7Ozs7Ozs7OztZQUVsQ1YsT0FBTzRCLFFBQVEsa0JBQ2QsOERBQUNwQywrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVU7Z0JBQ1ZTLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQ2pDQyxTQUFTO29CQUFFRixTQUFTO29CQUFHQyxRQUFRO2dCQUFPO2dCQUN0Q0UsTUFBTTtvQkFBRUgsU0FBUztvQkFBR0MsUUFBUTtnQkFBRTtnQkFDOUJHLFlBQVk7b0JBQUVDLFVBQVU7Z0JBQUs7MEJBRTVCakIsRUFBRTs7Ozs7OzBCQUtQLDhEQUFDaEIsK0VBQU1BLENBQUNpQixHQUFHO2dCQUNUQyxXQUFVO2dCQUNWRSxVQUFVaEIseURBQWlCQTswQkFFM0IsNEVBQUNvQztvQkFDQ3JCLE1BQUs7b0JBQ0xJLGFBQWFQLEVBQUU7b0JBQ2ZRLE9BQU9qQixTQUFTa0MsV0FBVztvQkFDM0I3QixVQUFVQTtvQkFDVkMsUUFBUUE7b0JBQ1JLLFdBQVU7b0JBQ1ZPLFFBQVE7b0JBQ1JDLFVBQVVqQjs7Ozs7Ozs7Ozs7MEJBS2QsOERBQUNULCtFQUFNQSxDQUFDaUIsR0FBRztnQkFDVEMsV0FBVTtnQkFDVkUsVUFBVWhCLHlEQUFpQkE7MEJBRTNCLDRFQUFDSiwrRUFBTUEsQ0FBQzBDLE1BQU07b0JBQ1pwQixNQUFLO29CQUNMcUIsU0FBU2hDO29CQUNUTyxXQUFVO29CQUNWMEIsWUFBWTt3QkFBRUMsT0FBTztvQkFBSztvQkFDMUJuQixVQUFVakI7O3dCQUVUQyxjQUFjTSxFQUFFLG9DQUFvQ0EsRUFBRTtzQ0FDdkQsOERBQUNoQiwrRUFBTUEsQ0FBQ2lCLEdBQUc7NEJBQ1RhLFNBQVM7Z0NBQUVnQixRQUFRcEMsY0FBYyxNQUFNOzRCQUFFOzRCQUN6Q3NCLFlBQVk7Z0NBQUVDLFVBQVU7NEJBQUk7c0NBRTVCLDRFQUFDaEMsa0dBQVdBO2dDQUFDa0MsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU14QnpCLDZCQUNDLDhEQUFDViwrRUFBTUEsQ0FBQ2lCLEdBQUc7Z0JBQ1RDLFdBQVU7Z0JBQ1ZTLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQ2pDQyxTQUFTO29CQUFFRixTQUFTO29CQUFHQyxRQUFRO2dCQUFPO2dCQUN0Q0UsTUFBTTtvQkFBRUgsU0FBUztvQkFBR0MsUUFBUTtnQkFBRTtnQkFDOUJHLFlBQVk7b0JBQUVDLFVBQVU7Z0JBQUk7MEJBRTNCO29CQUNDO3dCQUNFZCxNQUFNO3dCQUNOSSxhQUFhUCxFQUFFO3dCQUNmK0IsT0FBT3ZDLE9BQU93QyxRQUFRO29CQUN4QjtvQkFDQTt3QkFDRTdCLE1BQU07d0JBQ05JLGFBQWFQLEVBQUU7d0JBQ2YrQixPQUFPdkMsT0FBT3lDLE9BQU87b0JBQ3ZCO29CQUNBO3dCQUNFOUIsTUFBTTt3QkFDTkksYUFBYVAsRUFBRTt3QkFDZitCLE9BQU92QyxPQUFPMEMsT0FBTztvQkFDdkI7aUJBQ0QsQ0FBQ1gsR0FBRyxDQUFDLENBQUNZLHNCQUNMLDhEQUFDbEM7OzBDQUNDLDhEQUFDakIsK0VBQU1BLENBQUNpQixHQUFHO2dDQUNUQyxXQUFXaUMsTUFBTUosS0FBSyxHQUFHaEMsa0JBQWtCRDtnQ0FDM0NhLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUd3QixHQUFHO2dDQUFHO2dDQUM3QnRCLFNBQVM7b0NBQUVGLFNBQVM7b0NBQUd3QixHQUFHO2dDQUFFO2dDQUM1QnBCLFlBQVk7b0NBQUVDLFVBQVU7Z0NBQUs7MENBRTdCLDRFQUFDWjtvQ0FDQ0MsTUFBSztvQ0FDTEgsTUFBTWdDLE1BQU1oQyxJQUFJO29DQUNoQkksYUFBYTRCLE1BQU01QixXQUFXO29DQUM5QkMsT0FDRWpCLFFBQVEsQ0FBQzRDLE1BQU1oQyxJQUFJLENBQTBCO29DQUUvQ1AsVUFBVUE7b0NBQ1ZDLFFBQVFBO29DQUNSSyxXQUFVO29DQUNWUSxVQUFVakI7Ozs7Ozs7Ozs7OzRCQUdiMEMsTUFBTUosS0FBSyxrQkFDViw4REFBQy9DLCtFQUFNQSxDQUFDaUIsR0FBRztnQ0FDVEMsV0FBVTtnQ0FDVlMsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsUUFBUTtnQ0FBRTtnQ0FDakNDLFNBQVM7b0NBQUVGLFNBQVM7b0NBQUdDLFFBQVE7Z0NBQU87Z0NBQ3RDRSxNQUFNO29DQUFFSCxTQUFTO29DQUFHQyxRQUFRO2dDQUFFO2dDQUM5QkcsWUFBWTtvQ0FBRUMsVUFBVTtnQ0FBSzswQ0FFNUJrQixNQUFNSixLQUFLOzs7Ozs7O3VCQTVCUkksTUFBTWhDLElBQUk7Ozs7Ozs7Ozs7WUFxQ3pCWCxPQUFPNkMsR0FBRyxrQkFDVCw4REFBQ3JELCtFQUFNQSxDQUFDc0QsQ0FBQztnQkFDUHBDLFdBQVU7Z0JBQ1ZTLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQ2pDQyxTQUFTO29CQUFFRixTQUFTO29CQUFHQyxRQUFRO2dCQUFPO2dCQUN0Q0UsTUFBTTtvQkFBRUgsU0FBUztvQkFBR0MsUUFBUTtnQkFBRTtnQkFDOUJHLFlBQVk7b0JBQUVDLFVBQVU7Z0JBQUs7MEJBRTVCekIsT0FBTzZDLEdBQUc7Ozs7Ozs7Ozs7OztBQUtyQjtHQXBPTS9DOztRQWFVRCxpRUFBY0E7OztLQWJ4QkM7QUFzT04saUVBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxzcmNcXGNvbXBvbmVudHNcXGF1dGhcXGNyZWF0ZS1jb2luLWZvcm1cXEZvcm1GaWVsZHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xyXG5pbXBvcnQgeyBDaGV2cm9uRG93biwgRG9sbGFyU2lnbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IEZPUk1fQ0FURUdPUklFUyB9IGZyb20gJy4uLy4uLy4uL2NvbnN0YW50cyc7XHJcbmltcG9ydCB7IGZvcm1GaWVsZFZhcmlhbnRzIH0gZnJvbSAnLi9jb25zdGFudHMnO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlVHJhbnNsYXRpb24nO1xyXG5cclxuaW50ZXJmYWNlIEZvcm1GaWVsZHNQcm9wcyB7XHJcbiAgZm9ybURhdGE6IHtcclxuICAgIG5hbWU6IHN0cmluZztcclxuICAgIHRpY2tlcjogc3RyaW5nO1xyXG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgIHBpY3R1cmU6IEZpbGUgfCBudWxsO1xyXG4gICAgdGVsZWdyYW06IHN0cmluZztcclxuICAgIHdlYnNpdGU6IHN0cmluZztcclxuICAgIHR3aXR0ZXI6IHN0cmluZztcclxuICAgIGNhdGVnb3J5OiBzdHJpbmc7XHJcbiAgfTtcclxuICBlcnJvcnM6IHtcclxuICAgIG5hbWU6IHN0cmluZztcclxuICAgIHRpY2tlcjogc3RyaW5nO1xyXG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgIHBpY3R1cmU6IHN0cmluZztcclxuICAgIHRlbGVncmFtOiBzdHJpbmc7XHJcbiAgICB3ZWJzaXRlOiBzdHJpbmc7XHJcbiAgICB0d2l0dGVyOiBzdHJpbmc7XHJcbiAgICBjYXRlZ29yeTogc3RyaW5nO1xyXG4gICAgYXBpOiBzdHJpbmc7XHJcbiAgfTtcclxuICBpc1N1Ym1pdHRpbmc6IGJvb2xlYW47XHJcbiAgc2hvd09wdGlvbnM6IGJvb2xlYW47XHJcbiAgb25Ub2dnbGVPcHRpb25zOiAoKSA9PiB2b2lkO1xyXG4gIG9uQ2hhbmdlOiAoXHJcbiAgICBlOiBSZWFjdC5DaGFuZ2VFdmVudDxcclxuICAgICAgSFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudFxyXG4gICAgPlxyXG4gICkgPT4gdm9pZDtcclxuICBvbkJsdXI6IChcclxuICAgIGU6IFJlYWN0LkZvY3VzRXZlbnQ8XHJcbiAgICAgIEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MVGV4dEFyZWFFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnRcclxuICAgID5cclxuICApID0+IHZvaWQ7XHJcbn1cclxuXHJcbmNvbnN0IEZvcm1GaWVsZHM6IFJlYWN0LkZDPEZvcm1GaWVsZHNQcm9wcz4gPSAoe1xyXG4gIGZvcm1EYXRhLFxyXG4gIGVycm9ycyxcclxuICBpc1N1Ym1pdHRpbmcsXHJcbiAgc2hvd09wdGlvbnMsXHJcbiAgb25Ub2dnbGVPcHRpb25zLFxyXG4gIG9uQ2hhbmdlLFxyXG4gIG9uQmx1cixcclxufSkgPT4ge1xyXG4gIGNvbnN0IGlucHV0Q2xhc3MgPVxyXG4gICAgJ3NlbGYtc3RyZXRjaCBoLVs1MnB4XSBweS0wIHJvdW5kZWQgb3V0bGluZSBvdXRsaW5lLTEgb3V0bGluZS1vZmZzZXQtWy0xcHhdIG91dGxpbmUtZ3JheS0zMDAgaW5saW5lLWZsZXgganVzdGlmeS1zdGFydCBpdGVtcy1jZW50ZXIgZ2FwLTMgdy1mdWxsIG14LWF1dG8gcGwtNCBwci00IGZvY3VzLXdpdGhpbjpvdXRsaW5lLVsjRjU4QTM4XSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAnO1xyXG4gIGNvbnN0IGlucHV0RXJyb3JDbGFzcyA9XHJcbiAgICAnc2VsZi1zdHJldGNoIGgtWzUycHhdIHB5LTAgcm91bmRlZCBvdXRsaW5lIG91dGxpbmUtMSBvdXRsaW5lLW9mZnNldC1bLTFweF0gb3V0bGluZS1yZWQtNTAwIGlubGluZS1mbGV4IGp1c3RpZnktc3RhcnQgaXRlbXMtY2VudGVyIGdhcC0zIHctZnVsbCBteC1hdXRvIHBsLTQgcHItNCc7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBmbGV4LWNvbCBqdXN0aWZ5LXN0YXJ0IGl0ZW1zLXN0YXJ0IGdhcC01IHctZnVsbFwiPlxyXG4gICAgICB7LyogTmFtZSBGaWVsZCAqL31cclxuICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5uYW1lID8gaW5wdXRFcnJvckNsYXNzIDogaW5wdXRDbGFzc31cclxuICAgICAgICB2YXJpYW50cz17Zm9ybUZpZWxkVmFyaWFudHN9XHJcbiAgICAgID5cclxuICAgICAgICA8aW5wdXRcclxuICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgIG5hbWU9XCJuYW1lXCJcclxuICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdjcmVhdGVDb2luRm9ybS5uYW1lJyl9XHJcbiAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cclxuICAgICAgICAgIG9uQ2hhbmdlPXtvbkNoYW5nZX1cclxuICAgICAgICAgIG9uQmx1cj17b25CbHVyfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXRyYW5zcGFyZW50IG91dGxpbmUtbm9uZSB3LWZ1bGwgdGV4dC1ncmF5LTcwMCBwbGFjZWhvbGRlci1ncmF5LTQwMFwiXHJcbiAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cclxuICAgICAgICAvPlxyXG4gICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgIHtlcnJvcnMubmFtZSAmJiAoXHJcbiAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIHctZnVsbFwiXHJcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxyXG4gICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cclxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMTUgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7ZXJyb3JzLm5hbWV9XHJcbiAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIFRpY2tlciBGaWVsZCAqL31cclxuICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy50aWNrZXIgPyBpbnB1dEVycm9yQ2xhc3MgOiBpbnB1dENsYXNzfVxyXG4gICAgICAgIHZhcmlhbnRzPXtmb3JtRmllbGRWYXJpYW50c31cclxuICAgICAgPlxyXG4gICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgbmFtZT1cInRpY2tlclwiXHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj17dCgnY3JlYXRlQ29pbkZvcm0udGlja2VyJyl9XHJcbiAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGlja2VyfVxyXG4gICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfVxyXG4gICAgICAgICAgb25CbHVyPXtvbkJsdXJ9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctdHJhbnNwYXJlbnQgb3V0bGluZS1ub25lIHctZnVsbCB0ZXh0LWdyYXktNzAwIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcclxuICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPERvbGxhclNpZ24gc2l6ZT17MjB9IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIiAvPlxyXG4gICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgIHtlcnJvcnMudGlja2VyICYmIChcclxuICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQtc20gdy1mdWxsXCJcclxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XHJcbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIGhlaWdodDogJ2F1dG8nIH19XHJcbiAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxyXG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4xNSB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtlcnJvcnMudGlja2VyfVxyXG4gICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBDYXRlZ29yeSBGaWVsZCAqL31cclxuICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5jYXRlZ29yeSA/IGlucHV0RXJyb3JDbGFzcyA6IGlucHV0Q2xhc3N9XHJcbiAgICAgICAgdmFyaWFudHM9e2Zvcm1GaWVsZFZhcmlhbnRzfVxyXG4gICAgICA+XHJcbiAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgbmFtZT1cImNhdGVnb3J5XCJcclxuICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jYXRlZ29yeX1cclxuICAgICAgICAgIG9uQ2hhbmdlPXtvbkNoYW5nZX1cclxuICAgICAgICAgIG9uQmx1cj17b25CbHVyfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXRyYW5zcGFyZW50IG91dGxpbmUtbm9uZSB3LWZ1bGwgdGV4dC1ncmF5LTcwMFwiXHJcbiAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCIgZGlzYWJsZWQgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICB7dCgnY3JlYXRlQ29pbkZvcm0uc2VsZWN0Q2F0ZWdvcnknKX1cclxuICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAge0ZPUk1fQ0FURUdPUklFUy5tYXAoKGNhdGVnb3J5KSA9PiAoXHJcbiAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjYXRlZ29yeX0gdmFsdWU9e2NhdGVnb3J5fSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgICAge2NhdGVnb3J5fVxyXG4gICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgIDxDaGV2cm9uRG93biBzaXplPXsyMH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiIC8+XHJcbiAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAge2Vycm9ycy5jYXRlZ29yeSAmJiAoXHJcbiAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIHctZnVsbFwiXHJcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxyXG4gICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cclxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMTUgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7dCgnY3JlYXRlQ29pbkZvcm0ucGxlYXNlU2VsZWN0Q2F0ZWdvcnknKX1cclxuICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogRGVzY3JpcHRpb24gRmllbGQgKi99XHJcbiAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPVwic2VsZi1zdHJldGNoIGgtMzYgcHktMCBwbC00IHByLTQgcmVsYXRpdmUgcm91bmRlZCBvdXRsaW5lIG91dGxpbmUtMSBvdXRsaW5lLW9mZnNldC1bLTFweF0gb3V0bGluZS1ncmF5LTMwMCBpbmxpbmUtZmxleCBqdXN0aWZ5LXN0YXJ0IGl0ZW1zLXN0YXJ0IGdhcC0zIHctZnVsbCBteC1hdXRvIGZvY3VzLXdpdGhpbjpvdXRsaW5lLVsjRjU4QTM4XSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgIHZhcmlhbnRzPXtmb3JtRmllbGRWYXJpYW50c31cclxuICAgICAgPlxyXG4gICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdjcmVhdGVDb2luRm9ybS5kZXNjcmlwdGlvbicpfVxyXG4gICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfVxyXG4gICAgICAgICAgb25CbHVyPXtvbkJsdXJ9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctdHJhbnNwYXJlbnQgb3V0bGluZS1ub25lIHctZnVsbCBwdC00IHJlc2l6ZS1ub25lIHRleHQtZ3JheS03MDAgcGxhY2Vob2xkZXItZ3JheS00MDBcIlxyXG4gICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9tb3Rpb24uZGl2PlxyXG5cclxuICAgICAgey8qIE1vcmUgT3B0aW9ucyBUb2dnbGUgKi99XHJcbiAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIG14LWF1dG8gZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgIHZhcmlhbnRzPXtmb3JtRmllbGRWYXJpYW50c31cclxuICAgICAgPlxyXG4gICAgICAgIDxtb3Rpb24uYnV0dG9uXHJcbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlT3B0aW9uc31cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImp1c3RpZnktc3RhcnQgdGV4dC1ibGFjayB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgZm9udC1bJ0lCTV9QbGV4X1NhbnMnXSBsZWFkaW5nLXJlbGF4ZWQgZm9jdXM6b3V0bGluZS1ub25lIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtWyNGNThBMzhdIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDEgfX1cclxuICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAge3Nob3dPcHRpb25zID8gdCgnY3JlYXRlQ29pbkZvcm0uaGlkZU1vcmVPcHRpb25zJykgOiB0KCdjcmVhdGVDb2luRm9ybS5zaG93TW9yZU9wdGlvbnMnKX1cclxuICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgIGFuaW1hdGU9e3sgcm90YXRlOiBzaG93T3B0aW9ucyA/IDE4MCA6IDAgfX1cclxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4yIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxDaGV2cm9uRG93biBzaXplPXsyMH0gLz5cclxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICA8L21vdGlvbi5idXR0b24+XHJcbiAgICAgIDwvbW90aW9uLmRpdj5cclxuXHJcbiAgICAgIHsvKiBPcHRpb25hbCBGaWVsZHMgKi99XHJcbiAgICAgIHtzaG93T3B0aW9ucyAmJiAoXHJcbiAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBzcGFjZS15LTVcIlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cclxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgaGVpZ2h0OiAnYXV0bycgfX1cclxuICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7W1xyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgbmFtZTogJ3RlbGVncmFtJyxcclxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogdCgnY3JlYXRlQ29pbkZvcm0udGVsZWdyYW0nKSxcclxuICAgICAgICAgICAgICBlcnJvcjogZXJyb3JzLnRlbGVncmFtLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgbmFtZTogJ3dlYnNpdGUnLFxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiB0KCdjcmVhdGVDb2luRm9ybS53ZWJzaXRlJyksXHJcbiAgICAgICAgICAgICAgZXJyb3I6IGVycm9ycy53ZWJzaXRlLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgbmFtZTogJ3R3aXR0ZXInLFxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiB0KCdjcmVhdGVDb2luRm9ybS50d2l0dGVyJyksXHJcbiAgICAgICAgICAgICAgZXJyb3I6IGVycm9ycy50d2l0dGVyLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgXS5tYXAoKGZpZWxkKSA9PiAoXHJcbiAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZC5uYW1lfT5cclxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtmaWVsZC5lcnJvciA/IGlucHV0RXJyb3JDbGFzcyA6IGlucHV0Q2xhc3N9XHJcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDEwIH19XHJcbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMTUgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICBuYW1lPXtmaWVsZC5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17ZmllbGQucGxhY2Vob2xkZXJ9XHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YVtmaWVsZC5uYW1lIGFzIGtleW9mIHR5cGVvZiBmb3JtRGF0YV0gYXMgc3RyaW5nXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICBvbkJsdXI9e29uQmx1cn1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXRyYW5zcGFyZW50IG91dGxpbmUtbm9uZSB3LWZ1bGwgdGV4dC1ncmF5LTcwMCBwbGFjZWhvbGRlci1ncmF5LTQwMFwiXHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgICB7ZmllbGQuZXJyb3IgJiYgKFxyXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQtc20gdy1mdWxsIG10LTFcIlxyXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxyXG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIGhlaWdodDogJ2F1dG8nIH19XHJcbiAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMTUgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2ZpZWxkLmVycm9yfVxyXG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIEFQSSBFcnJvciAqL31cclxuICAgICAge2Vycm9ycy5hcGkgJiYgKFxyXG4gICAgICAgIDxtb3Rpb24ucFxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIGZvbnQtc2VtaWJvbGQgdGV4dC1iYXNlIHctZnVsbCBteC1hdXRvXCJcclxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XHJcbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIGhlaWdodDogJ2F1dG8nIH19XHJcbiAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxyXG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4xNSB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtlcnJvcnMuYXBpfVxyXG4gICAgICAgIDwvbW90aW9uLnA+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgRm9ybUZpZWxkcztcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibW90aW9uIiwiQ2hldnJvbkRvd24iLCJEb2xsYXJTaWduIiwiRk9STV9DQVRFR09SSUVTIiwiZm9ybUZpZWxkVmFyaWFudHMiLCJ1c2VUcmFuc2xhdGlvbiIsIkZvcm1GaWVsZHMiLCJmb3JtRGF0YSIsImVycm9ycyIsImlzU3VibWl0dGluZyIsInNob3dPcHRpb25zIiwib25Ub2dnbGVPcHRpb25zIiwib25DaGFuZ2UiLCJvbkJsdXIiLCJpbnB1dENsYXNzIiwiaW5wdXRFcnJvckNsYXNzIiwidCIsImRpdiIsImNsYXNzTmFtZSIsIm5hbWUiLCJ2YXJpYW50cyIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJyZXF1aXJlZCIsImRpc2FibGVkIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJoZWlnaHQiLCJhbmltYXRlIiwiZXhpdCIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInRpY2tlciIsInNpemUiLCJjYXRlZ29yeSIsInNlbGVjdCIsIm9wdGlvbiIsIm1hcCIsInRleHRhcmVhIiwiZGVzY3JpcHRpb24iLCJidXR0b24iLCJvbkNsaWNrIiwid2hpbGVIb3ZlciIsInNjYWxlIiwicm90YXRlIiwiZXJyb3IiLCJ0ZWxlZ3JhbSIsIndlYnNpdGUiLCJ0d2l0dGVyIiwiZmllbGQiLCJ5IiwiYXBpIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/create-coin-form/FormFields.tsx\n"));

/***/ })

}]);