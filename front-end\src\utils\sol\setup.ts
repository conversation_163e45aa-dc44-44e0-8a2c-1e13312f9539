import { Program } from "@coral-xyz/anchor";
import { Connection } from "@solana/web3.js";

import { SOLANA_CONFIG } from "@/config/environment";
import { IDL, Funhi } from "./idl";

const connection = new Connection(
  SOLANA_CONFIG.RPC_URL,
  "confirmed",
);

// Initialize the program interface with the IDL, program ID, and connection.
// This setup allows us to interact with the on-chain program using the defined interface.
export const program: Program<Funhi> = new Program(IDL as unknown as <PERSON><PERSON>, {
  connection,
});

