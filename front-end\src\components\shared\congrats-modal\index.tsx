import Image from "next/image";
import React, { useEffect, useRef } from "react";
import { useGlobalModal } from "@/contexts/GlobalModalContext";
import { useTranslation } from '@/hooks/useTranslation';


interface CongratsModalProps {
  onClose: () => void;
  onClaim: () => void;
  tokensUnlocked: number;
  tokensRemaining: number;
  justCongrate: boolean;
  message: string;
}

const CongratsModal: React.FC<CongratsModalProps> = ({
  onClose,
  onClaim,
  tokensUnlocked,
  tokensRemaining,
  justCongrate,
  message
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  const { isModalOpen } = useGlobalModal();
  const isOpen = isModalOpen("congrats");
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      ref={modalRef}
      className="bg-white rounded-[24px] md:rounded-[48px] shadow-xl py-8 px-4 md:px-13 w-[563px] text-center relative m-4"
    >
      <button
        className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none"
        onClick={() => onClose()}
      >
        <Image
          src={"/icons/close.svg"}
          alt="Menu"
          width={24}
          height={24}
          style={{ filter: 'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)' }}
        />
      </button>
      <h2 className="text-3xl	 font-semibold m-2">{t('congrats.title')}</h2>

      {justCongrate && (
        <p className="text-lg text-gray-600 mb-9">
          {message.toLocaleString()}
        </p>

      )}

      {/* Show unlocked tokens only if justCongrate is false */}
      {!justCongrate && (
        <>
          <p className="text-lg text-gray-600 mb-9">
            {t('congrats.unlockedTokens', { count: tokensUnlocked.toLocaleString() })}
          </p>
          <button
            onClick={onClaim}
            className="text-lg bg-black text-white font-medium w-full py-3 hover:bg-gray-800 transition"
          >
             {t('congrats.claimNow')}
          </button>

          <p className="text-base text-gray-500 mt-4">
          { t('congrats.tokensRemaining')}
            <span className="font-medium">
              {tokensRemaining.toLocaleString()}
            </span>
          </p>

          <div className="mt-4 mb-2 flex justify-between items-center">
            <div className="w-14 h-14 bg-stone-300 rounded-full relative">
              <Image
                src="/images/placeholder.png"
                alt=""
                fill
                className="rounded-full object-cover"
              />
            </div>
            <div className="flex-1 flex justify-center">
              <Image
                src="/icons/success.svg"
                width={30}
                height={28}
                alt="success"
              />
            </div>
          </div>


        </>
      )}






    </div>
  );
};

export default CongratsModal;
