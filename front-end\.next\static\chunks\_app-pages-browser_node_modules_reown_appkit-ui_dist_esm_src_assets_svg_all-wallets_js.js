"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allWalletsSvg: () => (/* binding */ allWalletsSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst allWalletsSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 24 24\">\n  <path\n    style=\"fill: var(--wui-color-accent-100);\"\n    d=\"M10.2 6.6a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM21 6.6a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM10.2 17.4a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM21 17.4a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0Z\"\n  />\n</svg>`;\n//# sourceMappingURL=all-wallets.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2FsbC13YWxsZXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLHNCQUFzQix3Q0FBRztBQUNoQztBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcQHJlb3duXFxhcHBraXQtdWlcXGRpc3RcXGVzbVxcc3JjXFxhc3NldHNcXHN2Z1xcYWxsLXdhbGxldHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBhbGxXYWxsZXRzU3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICA8cGF0aFxuICAgIHN0eWxlPVwiZmlsbDogdmFyKC0td3VpLWNvbG9yLWFjY2VudC0xMDApO1wiXG4gICAgZD1cIk0xMC4yIDYuNmEzLjYgMy42IDAgMSAxLTcuMiAwIDMuNiAzLjYgMCAwIDEgNy4yIDBaTTIxIDYuNmEzLjYgMy42IDAgMSAxLTcuMiAwIDMuNiAzLjYgMCAwIDEgNy4yIDBaTTEwLjIgMTcuNGEzLjYgMy42IDAgMSAxLTcuMiAwIDMuNiAzLjYgMCAwIDEgNy4yIDBaTTIxIDE3LjRhMy42IDMuNiAwIDEgMS03LjIgMCAzLjYgMy42IDAgMCAxIDcuMiAwWlwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWxsLXdhbGxldHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js\n"));

/***/ })

}]);