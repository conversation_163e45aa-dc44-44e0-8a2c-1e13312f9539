import { ErrorCategory } from '@/components/ui/ErrorModals';
import { EscrowOperationType } from './escrow';

// Enhanced Error Information
export interface DetectedError {
  category: ErrorCategory;
  message: string;
  userFriendlyMessage: string;
  details?: string;
  errorCode?: string;
  canRetry: boolean;
  severity: 'low' | 'medium' | 'high';
  additionalInfo?: {
    currentBalance?: number;
    requiredAmount?: number;
    walletAddress?: string;
    transactionId?: string;
    networkLatency?: number;
    suggestedAction?: string;
  };
}

// Error Pattern Matching
const errorPatterns = {
  // Insufficient Funds Patterns
  INSUFFICIENT_FUNDS: [
    /insufficient funds/i,
    /not enough balance/i,
    /balance too low/i,
    /insufficient lamports/i,
    /account does not have enough sol/i,
    /0x1/i // Solana insufficient funds error code
  ],
  
  // Wallet Connection Patterns
  WALLET_CONNECTION: [
    /wallet not connected/i,
    /wallet.*disconnected/i,
    /no wallet/i,
    /wallet.*not.*found/i,
    /wallet.*unavailable/i,
    /signTransaction.*not.*function/i,
    /wallet.*locked/i
  ],
  
  // Network Error Patterns
  NETWORK_ERROR: [
    /network.*error/i,
    /connection.*failed/i,
    /failed to fetch/i,
    /timeout/i,
    /rpc.*error/i,
    /502 bad gateway/i,
    /503 service unavailable/i,
    /504 gateway timeout/i,
    /network request failed/i,
    /connection.*refused/i
  ],
  
  // Transaction Timeout Patterns
  TRANSACTION_TIMEOUT: [
    /transaction.*timeout/i,
    /transaction.*expired/i,
    /blockhash.*not.*found/i,
    /transaction.*too.*old/i,
    /signature.*not.*found/i,
    /transaction.*not.*confirmed/i
  ],
  
  // Smart Contract Error Patterns
  SMART_CONTRACT_ERROR: [
    /program.*error/i,
    /instruction.*error/i,
    /account.*not.*initialized/i,
    /invalid.*program.*id/i,
    /custom.*program.*error/i,
    /anchor.*error/i,
    /0x[0-9a-f]+/i // Hex error codes
  ],
  
  // User Rejected Patterns
  USER_REJECTED: [
    /user.*rejected/i,
    /user.*denied/i,
    /user.*cancelled/i,
    /transaction.*rejected/i,
    /user.*declined/i,
    /cancelled.*by.*user/i
  ],
  
  // Duplicate Transaction Patterns
  DUPLICATE_TRANSACTION: [
    /already.*processed/i,
    /duplicate.*transaction/i,
    /transaction.*already.*exists/i,
    /already.*in.*progress/i,
    /operation.*pending/i
  ],
  
  // Validation Error Patterns
  VALIDATION_ERROR: [
    /validation.*failed/i,
    /invalid.*input/i,
    /missing.*required/i,
    /invalid.*format/i,
    /invalid.*address/i,
    /invalid.*amount/i
  ]
};

// Operation-specific error messages
const operationMessages = {
  buy: {
    INSUFFICIENT_FUNDS: 'You don\'t have enough SOL to complete this purchase',
    WALLET_CONNECTION: 'Please connect your wallet to make a purchase',
    NETWORK_ERROR: 'Unable to process your purchase due to network issues',
    TRANSACTION_TIMEOUT: 'Your purchase transaction is taking longer than expected',
    SMART_CONTRACT_ERROR: 'There was an issue with the escrow smart contract',
    USER_REJECTED: 'Purchase was cancelled',
    DUPLICATE_TRANSACTION: 'A purchase is already in progress for this item',
    VALIDATION_ERROR: 'Please check your purchase details and try again'
  },
  sell: {
    INSUFFICIENT_FUNDS: 'You don\'t have the required tokens to release the payment',
    WALLET_CONNECTION: 'Please connect your wallet to release the escrow funds',
    NETWORK_ERROR: 'Unable to release funds due to network issues',
    TRANSACTION_TIMEOUT: 'The fund release is taking longer than expected',
    SMART_CONTRACT_ERROR: 'There was an issue accessing the escrow account',
    USER_REJECTED: 'Fund release was cancelled',
    DUPLICATE_TRANSACTION: 'A fund release is already in progress',
    VALIDATION_ERROR: 'Please verify the escrow details and try again'
  },
  refund: {
    INSUFFICIENT_FUNDS: 'Insufficient funds available for refund',
    WALLET_CONNECTION: 'Please connect your wallet to process the refund',
    NETWORK_ERROR: 'Unable to process refund due to network issues',
    TRANSACTION_TIMEOUT: 'The refund is taking longer than expected',
    SMART_CONTRACT_ERROR: 'There was an issue accessing the escrow account',
    USER_REJECTED: 'Refund was cancelled',
    DUPLICATE_TRANSACTION: 'A refund is already in progress',
    VALIDATION_ERROR: 'Please verify the refund details and try again'
  },
  dispute: {
    INSUFFICIENT_FUNDS: 'You need a small amount of SOL to submit a dispute',
    WALLET_CONNECTION: 'Please connect your wallet to submit a dispute',
    NETWORK_ERROR: 'Unable to submit dispute due to network issues',
    TRANSACTION_TIMEOUT: 'Dispute submission is taking longer than expected',
    SMART_CONTRACT_ERROR: 'There was an issue recording the dispute',
    USER_REJECTED: 'Dispute submission was cancelled',
    DUPLICATE_TRANSACTION: 'A dispute has already been submitted for this transaction',
    VALIDATION_ERROR: 'Please check your dispute details and try again'
  }
};

// Suggested actions for each error category
const suggestedActions = {
  INSUFFICIENT_FUNDS: 'Add more SOL to your wallet and try again',
  WALLET_CONNECTION: 'Reconnect your wallet and ensure it\'s unlocked',
  NETWORK_ERROR: 'Check your internet connection and try again',
  TRANSACTION_TIMEOUT: 'Wait a moment and check if the transaction completed, then retry if needed',
  SMART_CONTRACT_ERROR: 'Contact support if this issue persists',
  USER_REJECTED: 'Approve the transaction in your wallet to continue',
  DUPLICATE_TRANSACTION: 'Wait for the current operation to complete before trying again',
  VALIDATION_ERROR: 'Review your input and correct any errors'
};

// Detect error category from error message
export function detectErrorCategory(error: Error | string): ErrorCategory {
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  for (const [category, patterns] of Object.entries(errorPatterns)) {
    for (const pattern of patterns) {
      if (pattern.test(errorMessage)) {
        return category as ErrorCategory;
      }
    }
  }
  
  return 'UNKNOWN_ERROR';
}

// Extract additional information from error
function extractAdditionalInfo(error: Error | string, category: ErrorCategory): any {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const additionalInfo: any = {};
  
  // Extract balance information for insufficient funds errors
  if (category === 'INSUFFICIENT_FUNDS') {
    const balanceMatch = errorMessage.match(/(\d+\.?\d*)\s*SOL/gi);
    if (balanceMatch && balanceMatch.length >= 2) {
      additionalInfo.currentBalance = parseFloat(balanceMatch[0].replace(/[^\d.]/g, ''));
      additionalInfo.requiredAmount = parseFloat(balanceMatch[1].replace(/[^\d.]/g, ''));
    }
  }
  
  // Extract transaction ID if available
  const txIdMatch = errorMessage.match(/([1-9A-HJ-NP-Za-km-z]{32,44})/);
  if (txIdMatch) {
    additionalInfo.transactionId = txIdMatch[1];
  }
  
  // Extract wallet address if available
  const walletMatch = errorMessage.match(/([1-9A-HJ-NP-Za-km-z]{32,44})/);
  if (walletMatch && !additionalInfo.transactionId) {
    additionalInfo.walletAddress = walletMatch[1];
  }
  
  // Extract error codes
  const errorCodeMatch = errorMessage.match(/0x[0-9a-fA-F]+|error code:?\s*(\d+)/i);
  if (errorCodeMatch) {
    additionalInfo.errorCode = errorCodeMatch[0];
  }
  
  return additionalInfo;
}

// Get retry capability for error category
function getRetryCapability(category: ErrorCategory): boolean {
  const retryableErrors: ErrorCategory[] = [
    'NETWORK_ERROR',
    'TRANSACTION_TIMEOUT',
    'INSUFFICIENT_FUNDS',
    'WALLET_CONNECTION'
  ];
  
  return retryableErrors.includes(category);
}

// Get error severity
function getErrorSeverity(category: ErrorCategory): 'low' | 'medium' | 'high' {
  const severityMap: Record<ErrorCategory, 'low' | 'medium' | 'high'> = {
    INSUFFICIENT_FUNDS: 'high',
    WALLET_CONNECTION: 'medium',
    NETWORK_ERROR: 'medium',
    TRANSACTION_TIMEOUT: 'medium',
    SMART_CONTRACT_ERROR: 'high',
    USER_REJECTED: 'low',
    DUPLICATE_TRANSACTION: 'medium',
    VALIDATION_ERROR: 'medium',
    UNKNOWN_ERROR: 'medium'
  };
  
  return severityMap[category];
}

// Main error detection and enhancement function
export function detectAndEnhanceError(
  error: Error | string,
  operation: EscrowOperationType,
  context?: any
): DetectedError {
  const category = detectErrorCategory(error);
  const errorMessage = typeof error === 'string' ? error : error.message;
  const additionalInfo = extractAdditionalInfo(error, category);
  
  // Add context information if available
  if (context) {
    if (context.walletAddress) additionalInfo.walletAddress = context.walletAddress;
    if (context.balance !== undefined) additionalInfo.currentBalance = context.balance;
    if (context.requiredAmount !== undefined) additionalInfo.requiredAmount = context.requiredAmount;
    if (context.networkLatency !== undefined) additionalInfo.networkLatency = context.networkLatency;
  }
  
  // Get operation-specific message
  const userFriendlyMessage = operationMessages[operation][category] || 
    `An error occurred during the ${operation} operation`;
  
  // Add suggested action
  additionalInfo.suggestedAction = suggestedActions[category];
  
  return {
    category,
    message: errorMessage,
    userFriendlyMessage,
    details: typeof error === 'object' && error.stack ? error.stack : undefined,
    errorCode: additionalInfo.errorCode,
    canRetry: getRetryCapability(category),
    severity: getErrorSeverity(category),
    additionalInfo
  };
}

// Helper function to create user-friendly error messages
export function createUserFriendlyErrorMessage(
  category: ErrorCategory,
  operation: EscrowOperationType,
  additionalContext?: string
): string {
  const baseMessage = operationMessages[operation][category] || 
    `An error occurred during the ${operation} operation`;
  
  if (additionalContext) {
    return `${baseMessage}. ${additionalContext}`;
  }
  
  return baseMessage;
}

// Helper function to determine if an error should show technical details
export function shouldShowTechnicalDetails(category: ErrorCategory): boolean {
  const technicalCategories: ErrorCategory[] = [
    'SMART_CONTRACT_ERROR',
    'NETWORK_ERROR',
    'UNKNOWN_ERROR'
  ];
  
  return technicalCategories.includes(category);
}
