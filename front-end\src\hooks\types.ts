import { TokenOption } from "@/components/shared/exchange-form/types";

export interface UseExchangeFormProps {
    fromOptions: TokenOption[];
    toOptions: TokenOption[];
  }
  
  export interface UseExchangeFormReturn {
    // State
    fromToken: TokenOption | null;
    toToken: TokenOption | null;
    fromAmount: string;
    toAmount: string;
    isLoading: boolean;
    isRefreshing: boolean;
    userSOL: number;
  
    // Handlers
    setFromToken: (token: TokenOption) => void;
    setToToken: (token: TokenOption) => void;
    handleFromAmountChange: (value: string) => Promise<void>;
    handleToAmountChange: (value: string) => Promise<void>;
    handleSwapTokens: () => void;
    handleBuyTokens: () => Promise<void>;
    refreshTokenBalances: () => Promise<void>;
  }