'use client';

import { motion } from 'framer-motion';
import { Info, HelpCircle, Lightbulb } from 'lucide-react';
import React from 'react';

import { fadeInUp } from '@/lib/animations';

interface TextBoxProps {
  title?: string;
  description?: string;
  type?: 'info' | 'help' | 'tip';
  className?: string;
  icon?: React.ReactNode;
}

const TextBox: React.FC<TextBoxProps> = ({
  title = 'Information',
  description = 'This is some helpful information.',
  type = 'info',
  className = '',
  icon,
}) => {
  const getIcon = () => {
    if (icon) return icon;

    const iconProps = { size: 24, className: 'text-[#FF6600]' };

    switch (type) {
      case 'help':
        return <HelpCircle {...iconProps} />;
      case 'tip':
        return <Lightbulb {...iconProps} />;
      case 'info':
      default:
        return <Info {...iconProps} />;
    }
  };

  const getBorderColor = () => {
    switch (type) {
      case 'help':
        return 'border-blue-200';
      case 'tip':
        return 'border-yellow-200';
      case 'info':
      default:
        return 'border-orange-200';
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'help':
        return 'bg-blue-50';
      case 'tip':
        return 'bg-yellow-50';
      case 'info':
      default:
        return 'bg-orange-50';
    }
  };

  return (
    <motion.div
      className={`max-w-[459px] m-auto lg:m-0 ${className}`}
      {...fadeInUp}
    >
      <motion.div
        className={`w-full rounded-2xl border-2 ${getBorderColor()} ${getBackgroundColor()} p-6 shadow-sm`}
        whileHover={{ scale: 1.01, y: -2 }}
        transition={{ duration: 0.2 }}
      >
        <motion.div
          className="flex items-start space-x-4"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <motion.div
            className="flex-shrink-0 mt-1"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          >
            {getIcon()}
          </motion.div>

          <div className="flex-1">
            <motion.h3
              className="text-xl font-semibold text-gray-900 mb-3 font-['IBM_Plex_Sans']"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {title}
            </motion.h3>

            <motion.p
              className="text-gray-700 leading-relaxed font-['IBM_Plex_Sans']"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              {description}
            </motion.p>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default TextBox;
