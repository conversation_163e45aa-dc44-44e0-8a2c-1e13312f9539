"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_header_ProfileDropdown_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/log-out.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LogOut)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\",\n            key: \"1uf3rs\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 17 21 12 16 7\",\n            key: \"1gabdz\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"21\",\n            x2: \"9\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1uyos4\"\n        }\n    ]\n];\nconst LogOut = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"log-out\", __iconNode);\n //# sourceMappingURL=log-out.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/settings.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Settings)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Settings = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"settings\", __iconNode);\n //# sourceMappingURL=settings.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\",\n            key: \"975kel\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"17ys0d\"\n        }\n    ]\n];\nconst User = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user\", __iconNode);\n //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wallet.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1\",\n            key: \"18etb6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4\",\n            key: \"xoc0q4\"\n        }\n    ]\n];\nconst Wallet = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wallet\", __iconNode);\n //# sourceMappingURL=wallet.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shared/header/ProfileDropdown.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shared/header/ProfileDropdown.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,LogOut,Settings,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ProfileDropdown = (param)=>{\n    let { dropdownRef, dropdownOpen, toggleDropdown, authenticated, login, logout, handleNavigation, userBalance, walletAddress, isAuthenticating = false } = param;\n    _s();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    // Helper function to truncate wallet address\n    const truncateAddress = (address)=>{\n        if (!address) return '';\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    // Copy wallet address to clipboard\n    const copyAddress = async ()=>{\n        if (walletAddress) {\n            try {\n                await navigator.clipboard.writeText(walletAddress);\n            // You can add a toast notification here\n            } catch (err) {\n                console.error('Failed to copy address:', err);\n            }\n        }\n    };\n    // If not authenticated, show connect wallet button instead\n    if (!authenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n            onClick: isAuthenticating ? undefined : login,\n            disabled: isAuthenticating,\n            className: \"hidden lg:flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white cursor-pointer font-medium hover:from-black hover:to-gray-800 transition-all duration-200 rounded-full shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed\",\n            whileHover: isAuthenticating ? {} : {\n                scale: 1.02\n            },\n            whileTap: isAuthenticating ? {} : {\n                scale: 0.98\n            },\n            transition: {\n                type: 'spring',\n                stiffness: 400,\n                damping: 17\n            },\n            children: isAuthenticating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Connecting...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden lg:block relative\",\n        ref: dropdownRef,\n        onClick: toggleDropdown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 cursor-pointer bg-gradient-to-r from-[#F8F9FA] to-[#F5F5F5] h-[40px] lg:h-[56px] pr-4 lg:pr-6 pl-2 rounded-full transition-all duration-300 hover:from-gray-100 hover:to-gray-200 shadow-sm hover:shadow-md border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                        className: \"flex items-center justify-center relative\",\n                        onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.SETTINGS),\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        transition: {\n                            type: 'spring',\n                            stiffness: 400,\n                            damping: 17\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-[#E0E0E0] to-[#C4C4C4] w-[36px] h-[36px] lg:w-[44px] lg:h-[44px] rounded-full flex items-center justify-center overflow-hidden ring-2 ring-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: \"/icons/owl.png\",\n                                alt: t('header.profile'),\n                                width: 44,\n                                height: 44,\n                                className: \"w-full h-full object-cover h-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col justify-center min-w-0 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-sm lg:text-base text-gray-800 truncate\",\n                                children: userBalance\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined),\n                            walletAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 font-mono truncate\",\n                                children: truncateAddress(walletAddress)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"cursor-pointer transition-all duration-300 p-1 rounded-full hover:bg-white/50 \".concat(dropdownOpen ? 'rotate-180 bg-white/50' : ''),\n                        whileHover: {\n                            scale: 1.1\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        transition: {\n                            type: 'spring',\n                            stiffness: 400,\n                            damping: 17\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: \"/icons/dropdown.svg\",\n                            alt: t('header.dropdown'),\n                            width: 16,\n                            height: 16,\n                            className: \"lg:w-5 lg:h-5 opacity-70 h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: dropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"absolute top-full mt-2 w-full bg-white rounded-xl shadow-xl z-50 border border-gray-100 overflow-hidden backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    transition: {\n                        type: 'spring',\n                        stiffness: 300,\n                        damping: 25,\n                        duration: 0.2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: [\n                            walletAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 bg-gray-50 border-b border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16,\n                                                        className: \"text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono text-sm text-gray-700\",\n                                                        children: truncateAddress(walletAddress)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                onClick: copyAddress,\n                                                className: \"p-1 hover:bg-gray-200 rounded transition-colors\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: t('balance.availableBalance', {\n                                            amount: parseFloat(userBalance).toLocaleString()\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors\",\n                                onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.PROFILE),\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 18,\n                                        className: \"text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('navigation.profile')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 text-gray-700 transition-colors\",\n                                onClick: ()=>handleNavigation(_constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.SETTINGS),\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 18,\n                                        className: \"text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('navigation.settings')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"px-4 py-3 hover:bg-red-50 cursor-pointer flex items-center gap-3 text-red-600 transition-colors border-t border-gray-100\",\n                                onClick: logout,\n                                whileHover: {\n                                    scale: 1.02,\n                                    x: 4\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                transition: {\n                                    type: 'spring',\n                                    stiffness: 400,\n                                    damping: 17\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_LogOut_Settings_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: t('dashboard.disconnect')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\ProfileDropdown.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfileDropdown, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = ProfileDropdown;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfileDropdown);\nvar _c;\n$RefreshReg$(_c, \"ProfileDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/header/ProfileDropdown.tsx\n"));

/***/ })

}]);