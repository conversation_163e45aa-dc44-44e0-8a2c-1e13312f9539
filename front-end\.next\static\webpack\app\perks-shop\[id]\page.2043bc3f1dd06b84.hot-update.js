"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/shared/chat/ChatModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useChatSocket */ \"(app-pages-browser)/./src/hooks/useChatSocket.ts\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_escrow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// \"use client\";\n// import React, { useState, useEffect, useCallback, useRef } from \"react\";\n// import { ArrowLeft, MoreVertical, CheckCheck, Send, X } from \"lucide-react\";\n// import { usePrivy } from \"@privy-io/react-auth\";\n// import { useChatSocket } from \"@/hooks/useChatSocket\";\n// import axios from \"axios\";\n// import dayjs from 'dayjs';\n// import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';\n// import { useTranslation } from '@/hooks/useTranslation';\n// interface ChatModalProps {\n//   chatRoomId: string;\n//   buyerId: string | number;\n//   sellerId: string | number;\n//   onClose: () => void;\n// }\n// const API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\n// const ChatModal: React.FC<ChatModalProps> = ({ chatRoomId, buyerId, sellerId, onClose }) => {\n//   const { t, isRTL } = useTranslation();\n//   // All hooks at the top\n//   const { user } = usePrivy();\n//   const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();\n//   const { setOpenChatTradeId } = useAppContext();\n//   const [messages, setMessages] = useState<any[]>([]);\n//   const [input, setInput] = useState(\"\");\n//   const messagesEndRef = useRef<HTMLDivElement>(null);\n//   const [currentStatus, setCurrentStatus] = useState<string | null>(null);\n//   const [releaseDeadline, setReleaseDeadline] = useState<Date | null>(null);\n//   const [timeLeft, setTimeLeft] = useState<string | null>(null);\n//   const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);\n//   const notificationAudioRef = useRef<HTMLAudioElement | null>(null);\n//   // Get userBo.id from localStorage for consistent sender check\n//   const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//   const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//   const myUserId = userBo?.id;\n//   // Fetch receiver info (email) when receiverId changes\n//   useEffect(() => {\n//     async function fetchReceiver() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/users/${sellerId}`);\n//         // Try to extract email from possible locations\n//         let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);\n//         let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);\n//         let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);\n//         setReceiverInfo({ name, email, wallet });\n//       } catch (err) {\n//         setReceiverInfo(null);\n//       }\n//     }\n//     if (sellerId) fetchReceiver();\n//   }, [sellerId]);\n//   // Fetch message history on mount or when chatRoomId changes\n//   useEffect(() => {\n//     async function fetchMessages() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);\n//         // Replace messages state with fetched history (no merge)\n//         setMessages((res.data.data || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()));\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchMessages();\n//   }, [chatRoomId]);\n//   // Fetch release deadline on mount or when tradeId changes\n//   useEffect(() => {\n//     async function fetchDeadline() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/${chatRoomId}/release-deadline`);\n//         const deadline = new Date(res.data.releaseDeadline);\n//         setReleaseDeadline(deadline);\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchDeadline();\n//   }, [chatRoomId]);\n//   // Timer countdown (only updates local state)\n//   useEffect(() => {\n//     if (!releaseDeadline) return;\n//     const interval = setInterval(() => {\n//       const now = new Date();\n//       const diff = releaseDeadline.getTime() - now.getTime();\n//       if (diff <= 0) {\n//         setTimeLeft(t('chat.autoReleaseInProgress'));\n//         clearInterval(interval);\n//       } else {\n//         // Format as translation string\n//         const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n//         const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);\n//         const minutes = Math.floor((diff / (1000 * 60)) % 60);\n//         const seconds = Math.floor((diff / 1000) % 60);\n//         setTimeLeft(t('chat.timeFormat', { days, hours, minutes, seconds }));\n//       }\n//     }, 1000);\n//     return () => clearInterval(interval);\n//   }, [releaseDeadline, t]);\n//   // Handle incoming real-time messages (deduplicate by id, tempId, and content)\n//   const handleMessage = useCallback((msg: any) => {\n//     setMessages(prev => {\n//       // If message already exists (by id, tempId, or identical content+createdAt+senderId), skip\n//       if (prev.some(m =>\n//         (m.id && msg.id && m.id === msg.id) ||\n//         (m.tempId && msg.tempId && m.tempId === msg.tempId) ||\n//         (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)\n//       )) {\n//         return prev;\n//       }\n//       // Play notification sound if the current user is the receiver\n//       const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//       const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//       if (userBo && msg.receiverId === userBo.id && notificationAudioRef.current) {\n//         notificationAudioRef.current.currentTime = 0;\n//         notificationAudioRef.current.play();\n//       }\n//       return [...prev, msg].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n//     });\n//   }, []);\n//   const handleTradeStatus = useCallback((data: { status: string }) => setCurrentStatus(data.status), []);\n//   // Setup socket\n//   const { sendMessage, release, report, authenticated, joinedRoom, tradeStatus } = useChatSocket({\n//     chatRoomId, // Pass chatRoomId to useChatSocket\n//     userId: user?.id || user?.wallet?.address || \"unknown\",\n//     wallet: user?.wallet?.address || \"\",\n//     onMessage: handleMessage,\n//     onTradeStatus: handleTradeStatus,\n//   });\n//   // Scroll to bottom on new message\n//   useEffect(() => {\n//     messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n//   }, [messages]);\n//   // Send message handler (optimistic update)\n//   const handleSend = (e: React.FormEvent) => {\n//     e.preventDefault();\n//     if (!input.trim()) return;\n//     const userBoStr = localStorage.getItem(\"userBo\");\n//     const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//     if (!userBo?.id) {\n//       alert(t('chat.userIdNotFound'));\n//       return;\n//     }\n//     sendMessage({\n//       chatRoomId,\n//       senderId: userBo.id,\n//       receiverId: userBo.id === buyerId ? sellerId : buyerId,\n//       message: input,\n//     });\n//     setInput(\"\");\n//   };\n//   // Helper: format time\n//   const formatTime = (dateStr: string) => {\n//     const date = new Date(dateStr);\n//     return date.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\n//   };\n//   // Helper: format date for separator\n//   const formatDateSeparator = (date: Date) => {\n//     const today = dayjs().startOf('day');\n//     const msgDay = dayjs(date).startOf('day');\n//     if (msgDay.isSame(today)) return t('chat.today');\n//     if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n//     return msgDay.format('D MMM YYYY');\n//   };\n//   // Helper to get initials from email or name\n//   const getInitials = (email?: string, name?: string) => {\n//     if (name && name.trim()) return name.slice(0, 2).toUpperCase();\n//     if (email && email.trim()) return email.slice(0, 2).toUpperCase();\n//     return \"?\";\n//   };\n//   // Helper to format wallet address (first 4 + last 4 characters)\n//   const formatWalletAddress = (wallet?: string) => {\n//     if (!wallet || wallet.length < 8) return wallet;\n//     return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;\n//   };\n//   // Helper to get display name with priority: name > email > formatted wallet\n//   const getDisplayName = () => {\n//     if (receiverInfo?.name && receiverInfo.name.trim()) {\n//       return receiverInfo.name;\n//     }\n//     if (receiverInfo?.email && receiverInfo.email.trim()) {\n//       return receiverInfo.email;\n//     }\n//     if (receiverInfo?.wallet && receiverInfo.wallet.trim()) {\n//       return formatWalletAddress(receiverInfo.wallet);\n//     }\n//     return t('chat.user');\n//   };\n//   // Show status in the UI and disable buttons if released or reported\n//   const isActionDisabled = currentStatus === 'released' || currentStatus === 'reported';\n//   // useEffect to set openChatTradeId and clear unread chat messages for this chatRoomId\n//   useEffect(() => {\n//     setOpenChatTradeId(chatRoomId);\n//     removeUnreadChatMessagesForTrade(chatRoomId);\n//     return () => {\n//       setOpenChatTradeId(null);\n//     };\n//   }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade]);\n//   // Only after all hooks, do conditional returns\n//   if (!user?.wallet?.address) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectWalletToChat')}</p>\n//           {/* You can trigger Privy login here if needed */}\n//         </div>\n//       </div>\n//     );\n//   }\n//   if (!authenticated || !joinedRoom) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectingToChat')}</p>\n//         </div>\n//       </div>\n//     );\n//   }\n//   return (\n//     <div\n//       className={`fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300 ${isRTL ? 'rtl' : 'ltr'}`}\n//     >\n//       {/* Notification sound */}\n//       <audio ref={notificationAudioRef} src=\"/sounds/notification.mp3\" preload=\"auto\" />\n//         {/* Close button */}\n//         <button\n//           className=\"absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1\"\n//           onClick={onClose}\n//         >\n//           <X className=\"w-5 h-5 text-gray-700\" />\n//         </button>\n//         {/* Header */}\n//         {/* <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button\n//             className={isRTL ? 'ml-3' : 'mr-3'}\n//             onClick={onClose}\n//             style={isRTL ? { marginLeft: '12px', marginRight: 0 } : { marginRight: '12px', marginLeft: 0 }}\n//           >\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button> */}\n//           <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button className={isRTL ? 'ml-3' : 'mr-3'} onClick={onClose}>\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button>\n//           {/* <div\n//             className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}\n//             style={isRTL ? { direction: 'rtl', textAlign: 'right', alignItems: 'flex-end' } : { direction: 'ltr', textAlign: 'left', alignItems: 'flex-start' }}\n//           >\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div> */}\n//           <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`} style={isRTL ? { direction: 'rtl' } : {}}>\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//         {/* Messages Container */}\n//         <div className=\"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto\" style={{ maxHeight: 400 }}>\n//           {(() => {\n//             let lastDate: string | null = null;\n//             return messages.map((msg, idx) => {\n//               const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n//               const showDate = msgDate && msgDate !== lastDate;\n//               lastDate = msgDate;\n//               return (\n//                 <React.Fragment key={msg.id ? `id-${msg.id}` : msg.tempId ? `temp-${msg.tempId}` : `fallback-${msg.senderId}-${msg.createdAt}-${idx}` }>\n//                   {showDate && (\n//                     <div className=\"flex justify-center my-2\">\n//                       <span className=\"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\">{msgDate}</span>\n//                     </div>\n//                   )}\n//                   <div className={`flex flex-col ${msg.senderId === myUserId ? \"items-end\" : \"items-start\"}`}>\n//                     <div\n//                       className={\n//                         msg.senderId === myUserId\n//                           ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\"\n//                           : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\"\n//                       }\n//                     >\n//                       <p className=\"text-sm leading-relaxed whitespace-pre-line\">{msg.message || msg.text}</p>\n//                     </div>\n//                     <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"}`}>\n//                       <span className=\"text-xs text-gray-400\">\n//                         {msg.createdAt ? formatTime(msg.createdAt) : msg.time || \"\"}\n//                       </span>\n//                       {msg.senderId === myUserId && <CheckCheck className=\"w-4 h-4 text-green-500\" />}\n//                     </div>\n//                   </div>\n//                 </React.Fragment>\n//               );\n//             });\n//           })()}\n//           <div ref={messagesEndRef} />\n//         </div>\n//         {/* Bottom Section */}\n//         {/* Auto-release Info */}\n//         <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n//           <p className=\"text-xs text-gray-500 text-center leading-relaxed\">\n//             {timeLeft ? (\n//               <>{t('chat.autoReleaseIn', { time: timeLeft })}</>\n//             ) : (\n//               <>{t('chat.loadingAutoRelease')}</>\n//             )}<br />\n//             {t('chat.reportTrade')}\n//           </p>\n//         </div>\n//         {/* Action Buttons */}\n//         <div className=\"px-4 py-3 space-y-2\">\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={release}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.iGotTheItem')}\n//           </button>\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={report}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.reportTrade')}\n//           </button>\n//           {currentStatus && (\n//             <div className=\"text-center text-xs text-gray-500 mt-2\">\n//               {t('chat.tradeStatus')} <span className=\"font-semibold\">{currentStatus}</span>\n//             </div>\n//           )}\n//         </div>\n//         {/* Message Input */}\n//         <form className=\"px-4 py-3 bg-orange-50\" onSubmit={handleSend}>\n//           <div className=\"flex items-center gap-2\">\n//             <input\n//               type=\"text\"\n//               placeholder={t('chat.typeMessage')}\n//               className=\"flex-1 bg-orange-100 rounded-full px-4 py-2 text-sm outline-none border border-transparent focus:border-orange-300 placeholder:text-gray-500\"\n//               value={input}\n//               onChange={e => setInput(e.target.value)}\n//               disabled={!authenticated || !joinedRoom}\n//             />\n//             <button type=\"submit\" className=\"bg-orange-500 p-2 rounded-full hover:bg-orange-600 transition-colors\" disabled={!authenticated || !joinedRoom}>\n//               <Send className=\"w-5 h-5 text-white\" />\n//             </button>\n//           </div>\n//         </form>\n//     </div>\n//   );\n// };\n// export default ChatModal; \n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\nconst ChatModal = (param)=>{\n    let { chatRoomId, buyerId, sellerId, onClose, onRelease, onRefund, onReport, onAccept, onInitiateDispute, activeTrade } = param;\n    var _user_wallet, _user_wallet1, _receiverInfo_name, _receiverInfo_email;\n    _s();\n    const { t, isRTL } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    // Debug props received\n    console.log('🎯 [ChatModal] Props received:', {\n        chatRoomId,\n        buyerId,\n        sellerId,\n        hasOnRelease: !!onRelease,\n        hasOnAccept: !!onAccept,\n        hasOnReport: !!onReport,\n        hasActiveTrade: !!activeTrade,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId,\n            from: activeTrade.from,\n            to: activeTrade.to\n        } : null\n    });\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H)();\n    const { solanaWallet, isConnected, getWalletAddress } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { removeUnreadChatMessagesForTrade } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages)();\n    const { setOpenChatTradeId } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [receiverInfo, setReceiverInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const notificationAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentTradeStatus, setCurrentTradeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status) || null);\n    const [isOperationInProgress, setIsOperationInProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Request notification permission when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            if ('Notification' in window && Notification.permission === 'default') {\n                Notification.requestPermission().then({\n                    \"ChatModal.useEffect\": (permission)=>{\n                        console.log('🔔 [ChatModal] Notification permission:', permission);\n                    }\n                }[\"ChatModal.useEffect\"]);\n            }\n        }\n    }[\"ChatModal.useEffect\"], []);\n    // Get user ID\n    const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n    const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n    // Determine user role - ensure type consistency\n    const buyerIdNum = typeof buyerId === 'string' ? parseInt(buyerId) : buyerId;\n    const sellerIdNum = typeof sellerId === 'string' ? parseInt(sellerId) : sellerId;\n    const myUserIdNum = typeof myUserId === 'string' ? parseInt(myUserId) : myUserId;\n    const isBuyer = myUserIdNum === buyerIdNum;\n    const isSeller = myUserIdNum === sellerIdNum;\n    // Use current trade status (which can be updated via Socket.IO) or fallback to activeTrade status\n    const tradeStatus = currentTradeStatus || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status);\n    const isEscrowed = tradeStatus === 'escrowed';\n    const isPendingAcceptance = tradeStatus === 'pending_acceptance';\n    const isEscrowReleased = tradeStatus === 'completed' || tradeStatus === 'released';\n    const isUserAuthorized = user && (isBuyer || isSeller);\n    // Trade action buttons should only show when escrow is active (not released)\n    const shouldShowTradeActions = (isEscrowed || isPendingAcceptance) && !isEscrowReleased;\n    // Seller can accept when escrow is pending acceptance\n    const canSellerAccept = isPendingAcceptance && isSeller && onAccept;\n    // Buyer can only release after seller has accepted\n    const canBuyerRelease = isEscrowed && !isPendingAcceptance && isBuyer;\n    // Debug logging\n    console.log('🔍 [ChatModal] User role debugging:', {\n        myUserId,\n        myUserIdNum,\n        buyerId,\n        buyerIdNum,\n        sellerId,\n        sellerIdNum,\n        isBuyer,\n        isSeller,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId\n        } : null,\n        tradeStatus,\n        currentTradeStatus,\n        isEscrowed,\n        isPendingAcceptance,\n        isEscrowReleased,\n        shouldShowTradeActions,\n        canSellerAccept,\n        canBuyerRelease,\n        isUserAuthorized,\n        onAcceptExists: !!onAccept,\n        onReleaseExists: !!onRelease,\n        onReportExists: !!onReport,\n        hasActiveTrade: !!activeTrade,\n        activeTradeStatus: activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status\n    });\n    // Chat should be enabled if user is authorized (buyer or seller), regardless of escrow status\n    const canChat = isUserAuthorized && isConnected && solanaWallet;\n    // Enhanced dispute button logic\n    const canShowDisputeButton = (userRole)=>{\n        if (!activeTrade || !onInitiateDispute) return false;\n        // Use the enhanced canInitiateDispute function with dispute status\n        const disputeCheck = (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.canInitiateDispute)(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus);\n        // Debug logging for dispute button visibility\n        console.log(\"\\uD83D\\uDD0D [ChatModal] Dispute button check for \".concat(userRole, \":\"), {\n            tradeStatus: activeTrade.status,\n            disputeStatus: activeTrade.disputeStatus,\n            canDispute: disputeCheck.canDispute,\n            reason: disputeCheck.reason,\n            createdAt: activeTrade.createdAt\n        });\n        return disputeCheck.canDispute;\n    };\n    // Fetch receiver info\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchReceiver() {\n                try {\n                    const targetId = isBuyer ? sellerId : buyerId;\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/users/\").concat(targetId));\n                    let email = res.data.email || res.data.user && res.data.user.email || res.data.data && res.data.data.email;\n                    let name = res.data.name || res.data.user && res.data.user.name || res.data.data && res.data.data.name;\n                    let wallet = res.data.wallet || res.data.user && res.data.user.wallet || res.data.data && res.data.data.wallet;\n                    setReceiverInfo({\n                        name,\n                        email,\n                        wallet\n                    });\n                } catch (err) {\n                    setReceiverInfo(null);\n                }\n            }\n            fetchReceiver();\n        }\n    }[\"ChatModal.useEffect\"], [\n        buyerId,\n        sellerId,\n        isBuyer\n    ]);\n    // Fetch messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchMessages() {\n                try {\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/messages/chat-room/\").concat(chatRoomId));\n                    setMessages((res.data.data || []).sort({\n                        \"ChatModal.useEffect.fetchMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useEffect.fetchMessages\"]));\n                } catch (err) {\n                    console.error(\"Error fetching messages:\", err);\n                }\n            }\n            fetchMessages();\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId\n    ]);\n    // Handle incoming messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleMessage]\": (msg)=>{\n            console.log('📨 [ChatModal] Received message:', msg, 'myUserId:', myUserId);\n            setMessages({\n                \"ChatModal.useCallback[handleMessage]\": (prev)=>{\n                    // Check for duplicates\n                    const isDuplicate = prev.some({\n                        \"ChatModal.useCallback[handleMessage].isDuplicate\": (m)=>m.id && msg.id && m.id === msg.id || m.tempId && msg.tempId && m.tempId === msg.tempId || m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId\n                    }[\"ChatModal.useCallback[handleMessage].isDuplicate\"]);\n                    if (isDuplicate) {\n                        console.log('🔄 [ChatModal] Duplicate message, skipping');\n                        return prev;\n                    }\n                    // Play notification sound for received messages (not sent by me)\n                    if (msg.senderId !== myUserId && notificationAudioRef.current) {\n                        console.log('🔊 [ChatModal] Playing notification sound');\n                        try {\n                            notificationAudioRef.current.currentTime = 0;\n                            notificationAudioRef.current.play().catch({\n                                \"ChatModal.useCallback[handleMessage]\": (e)=>{\n                                    console.log('🔇 [ChatModal] Could not play notification sound (user interaction required):', e);\n                                    // Fallback: show browser notification if audio fails\n                                    if ('Notification' in window && Notification.permission === 'granted') {\n                                        new Notification('New Message', {\n                                            body: msg.message,\n                                            icon: '/images/funhi-logo.png'\n                                        });\n                                    }\n                                }\n                            }[\"ChatModal.useCallback[handleMessage]\"]);\n                        } catch (error) {\n                            console.log('🔇 [ChatModal] Audio play error:', error);\n                        }\n                    }\n                    const newMessages = [\n                        ...prev,\n                        msg\n                    ].sort({\n                        \"ChatModal.useCallback[handleMessage].newMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useCallback[handleMessage].newMessages\"]);\n                    console.log('✅ [ChatModal] Added new message, total messages:', newMessages.length);\n                    return newMessages;\n                }\n            }[\"ChatModal.useCallback[handleMessage]\"]);\n        }\n    }[\"ChatModal.useCallback[handleMessage]\"], [\n        myUserId\n    ]);\n    // Handle trade status updates\n    const handleTradeStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleTradeStatusUpdate]\": (data)=>{\n            console.log(\"[ChatModal] Trade status update received:\", data);\n            if (data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId)) {\n                setCurrentTradeStatus(data.status);\n                console.log(\"[ChatModal] Updated trade status to: \".concat(data.status));\n            }\n        }\n    }[\"ChatModal.useCallback[handleTradeStatusUpdate]\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId\n    ]);\n    // Get the correct wallet address\n    const walletAddress = getWalletAddress();\n    console.log('🔍 [ChatModal] Wallet info:', {\n        privyWallet: user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : _user_wallet.address,\n        useWalletAddress: walletAddress,\n        isConnected,\n        chatRoomId\n    });\n    // Setup socket\n    const { sendMessage, authenticated, joinedRoom } = (0,_hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket)({\n        chatRoomId,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || myUserId || \"unknown\",\n        wallet: walletAddress || \"\",\n        onMessage: handleMessage,\n        onTradeStatus: handleTradeStatusUpdate\n    });\n    // Scroll to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }[\"ChatModal.useEffect\"], [\n        messages\n    ]);\n    // Set chat as open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            setOpenChatTradeId(chatRoomId);\n            removeUnreadChatMessagesForTrade(chatRoomId);\n            return ({\n                \"ChatModal.useEffect\": ()=>setOpenChatTradeId(null)\n            })[\"ChatModal.useEffect\"];\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId,\n        setOpenChatTradeId,\n        removeUnreadChatMessagesForTrade\n    ]);\n    // Fetch latest trade status when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            const fetchLatestTradeStatus = {\n                \"ChatModal.useEffect.fetchLatestTradeStatus\": async ()=>{\n                    const tradeId = (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId);\n                    if (tradeId) {\n                        try {\n                            console.log('🔄 [ChatModal] Fetching latest trade status for tradeId:', tradeId);\n                            const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                            const tradeResponse = await getTradeDetails(Number(tradeId));\n                            if (tradeResponse.status === 200 && tradeResponse.data.status !== currentTradeStatus) {\n                                console.log(\"\\uD83D\\uDD04 [ChatModal] Trade status updated: \".concat(currentTradeStatus, \" → \").concat(tradeResponse.data.status));\n                                setCurrentTradeStatus(tradeResponse.data.status);\n                            }\n                        } catch (error) {\n                            console.error('❌ [ChatModal] Failed to fetch latest trade status:', error);\n                        }\n                    } else {\n                        console.log('⚠️ [ChatModal] No trade ID available for status fetch');\n                    }\n                }\n            }[\"ChatModal.useEffect.fetchLatestTradeStatus\"];\n            fetchLatestTradeStatus();\n        }\n    }[\"ChatModal.useEffect\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId\n    ]); // Run when either ID changes\n    // Send message\n    const handleSend = (e)=>{\n        e.preventDefault();\n        if (!input.trim() || !canChat) return;\n        const receiverId = isBuyer ? sellerId : buyerId;\n        console.log('🔍 [ChatModal] Sending message:', {\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim(),\n            canChat,\n            isEscrowed\n        });\n        sendMessage({\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim()\n        });\n        setInput(\"\");\n    };\n    // Enhanced action handlers that update status immediately and send system messages\n    const handleReleaseWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleReleaseWithStatusUpdate]\": async ()=>{\n            if (!onRelease) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow release...');\n                // Call the original release function\n                await onRelease();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('released');\n                console.log('✅ [ChatModal] Escrow released, status updated to released');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"\\uD83C\\uDF89 Escrow has been released! Trade completed successfully.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Release failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleReleaseWithStatusUpdate]\"], [\n        onRelease,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    const handleAcceptWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleAcceptWithStatusUpdate]\": async ()=>{\n            if (!onAccept) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow accept...');\n                // Call the original accept function\n                await onAccept();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('escrowed');\n                console.log('✅ [ChatModal] Escrow accepted, status updated to escrowed');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"✅ Escrow accepted! Buyer can now release funds when ready.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Accept failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleAcceptWithStatusUpdate]\"], [\n        onAccept,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    // Format time\n    const formatTime = (dateStr)=>{\n        const date = new Date(dateStr);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Format date separator\n    const formatDateSeparator = (date)=>{\n        const today = dayjs__WEBPACK_IMPORTED_MODULE_4___default()().startOf('day');\n        const msgDay = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).startOf('day');\n        if (msgDay.isSame(today)) return t('chat.today');\n        if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n        return msgDay.format('D MMM YYYY');\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.name) return receiverInfo.name;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) return receiverInfo.email;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) {\n            const wallet = receiverInfo.wallet;\n            return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n        }\n        return t('chat.user');\n    };\n    // Wallet formatting\n    const formatWalletAddress = (wallet)=>{\n        if (!wallet || wallet.length < 8) return wallet;\n        return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n    };\n    if (!(user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : _user_wallet1.address)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-700 text-center\",\n                        children: t('chat.connectWalletToChat')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 853,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 851,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 850,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!authenticated || !joinedRoom) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-gray-700 text-center\",\n                    children: t('chat.connectingToChat')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 868,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 867,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 866,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: notificationAudioRef,\n                src: \"/sounds/notification.mp3\",\n                preload: \"auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 876,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-2 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 884,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 880,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 ml-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_name = receiverInfo.name) === null || _receiverInfo_name === void 0 ? void 0 : _receiverInfo_name.slice(0, 2).toUpperCase()) || (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_email = receiverInfo.email) === null || _receiverInfo_email === void 0 ? void 0 : _receiverInfo_email.slice(0, 2).toUpperCase()) || \"?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 888,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute bottom-0 right-0 w-3 h-3 bg-emerald-500 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 887,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900 text-sm\",\n                                        children: getDisplayName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: receiverInfo.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 font-mono\",\n                                        children: formatWalletAddress(receiverInfo.wallet)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 886,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 879,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b \".concat(isEscrowReleased ? 'bg-green-50 border-green-100' : isEscrowed ? 'bg-orange-50 border-orange-100' : isPendingAcceptance ? 'bg-blue-50 border-blue-100' : 'bg-amber-50 border-amber-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isEscrowReleased ? 'bg-green-600' : isEscrowed ? 'bg-[#FF6600]' : isPendingAcceptance ? 'bg-blue-500' : 'bg-amber-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-slate-700\",\n                                    children: isEscrowReleased ? 'Trade Completed' : isEscrowed ? 'Escrow Active' : isPendingAcceptance ? 'Awaiting Seller Acceptance' : 'Pre-Purchase Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500\",\n                            children: isEscrowReleased ? 'Funds released successfully' : isEscrowed ? 'Funds secured on-chain' : isPendingAcceptance ? 'Seller needs to accept escrow' : 'Discussing before purchase'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 921,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 912,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto max-h-[400px]\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-orange-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: isEscrowed ? 'Escrow Chat Started' : 'Start the Conversation'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 max-w-xs\",\n                                children: isEscrowed ? 'Your funds are secured. Chat with the other party about the trade details.' : 'Discuss the details before making a purchase. Ask questions and clarify expectations.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 968,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 11\n                    }, undefined) : messages.map((msg, idx)=>{\n                        const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n                        const showDate = idx === 0 || msgDate !== formatDateSeparator(new Date(messages[idx - 1].createdAt));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\",\n                                        children: msgDate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col \".concat(msg.senderId === myUserId ? \"items-end\" : \"items-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: msg.senderId === myUserId ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\" : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed whitespace-pre-line\",\n                                                children: msg.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 995,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 mt-1 \".concat(msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: msg.createdAt ? formatTime(msg.createdAt) : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                msg.senderId === myUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, msg.id || \"msg-\".concat(idx), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 981,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1007,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 957,\n                columnNumber: 7\n            }, undefined),\n            activeTrade && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Trade Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium capitalize \".concat((currentTradeStatus || activeTrade.status) === 'escrowed' ? 'bg-blue-100 text-blue-800' : (currentTradeStatus || activeTrade.status) === 'completed' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'released' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'pending_acceptance' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'),\n                                        children: currentTradeStatus || activeTrade.status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTrade.disputeStatus && activeTrade.disputeStatus !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Dispute:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).bgColor, \" \").concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).color),\n                                        children: (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1014,\n                        columnNumber: 11\n                    }, undefined),\n                    shouldShowTradeActions && isConnected && solanaWallet && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 p-4 bg-slate-50 rounded-lg\",\n                        children: [\n                            isBuyer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canBuyerRelease && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleReleaseWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Releasing...' : t('chat.releaseFunds')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-700 font-medium\",\n                                                        children: \"Waiting for seller to accept escrow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 text-center mt-1\",\n                                                children: \"You'll be able to release funds once the seller accepts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('buyer') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('buyer'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            isSeller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canSellerAccept && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleAcceptWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Accepting...' : 'Accept Escrow'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isEscrowed && !isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-700 font-medium\",\n                                                        children: \"Escrow accepted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 text-center mt-1\",\n                                                children: \"Waiting for buyer to release funds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1142,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1137,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('seller'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    !canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-800 text-center\",\n                                            children: (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.disputeStatus) && activeTrade.disputeStatus !== 'none' ? \"Dispute status: \".concat(activeTrade.disputeStatus) : 'Dispute option available within 2 days of escrow creation'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1043,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowReleased && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"Trade Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-700\",\n                                            children: \"Escrow has been released. This trade is now complete.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1176,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && isUserAuthorized && (!isConnected || !solanaWallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Wallet Connection Required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700\",\n                                            children: \"Please connect your Solana wallet to perform trade actions like releasing funds or initiating disputes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1196,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && (!user || !isBuyer && !isSeller) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-red-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1211,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Access Restricted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: \"Only the buyer and seller can perform trade actions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1213,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1209,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1208,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'open' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-800\",\n                            children: \"A dispute has been initiated. A moderator will review this case shortly.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1225,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'resolved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-800\",\n                            children: \"The dispute for this trade has been resolved by a moderator.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1233,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1012,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"px-4 py-3 bg-slate-50 border-t border-slate-200\",\n                onSubmit: handleSend,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: canChat ? t('chat.typeMessage') : 'Connect wallet to chat...',\n                                className: \"flex-1 bg-white rounded-lg px-4 py-3 text-sm outline-none border border-slate-300 focus:border-[#FF6600] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 placeholder:text-slate-500 transition-all duration-200\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                disabled: !canChat\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"p-3 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm \".concat(canChat && input.trim() ? 'bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2' : 'bg-slate-300 cursor-not-allowed opacity-50'),\n                                disabled: !input.trim() || !canChat,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1253,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 9\n                    }, undefined),\n                    !canChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-gray-600 text-center\",\n                        children: !isUserAuthorized ? 'Only buyer and seller can chat' : !isConnected ? 'Please connect your wallet to chat' : 'Wallet connection required'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1266,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1243,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n        lineNumber: 875,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModal, \"DNr/tjJYbHPK+3Qa5/mVdoIMeas=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket\n    ];\n});\n_c = ChatModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatModal);\nvar _c;\n$RefreshReg$(_c, \"ChatModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\n"));

/***/ })

});