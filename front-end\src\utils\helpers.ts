import * as anchor from "@coral-xyz/anchor";
import { BN } from "@coral-xyz/anchor";
import { CurveCalculator, Raydium } from "@raydium-io/raydium-sdk-v2";
import { getMint, createAssociatedTokenAccountInstruction, createTransferInstruction, getAccount, getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from "@solana/spl-token";
import {
  Connection,
  PublicKey,
  clusterApiUrl,
  TransactionInstruction,
  LAMPORTS_PER_SOL,
  Transaction,
} from "@solana/web3.js";
import axios from "axios";

import { global } from "./sol/pdas";
import { getBondingCurvePda } from "./sol/pdas";
import { program } from "./sol/setup";


import { getUserLogs } from "@/axios/requests";
import { useWallet } from "@/hooks/useWallet";
import { SOLANA_CONFIG, UPLOAD_CONFIG } from "@/config/environment";
import { Cluster } from "@solana/web3.js";

type MessageType = 'buy' | 'sell' | 'airdrop' | 'burn' | 'general';

type Message = {
  id: string;
  text: string;
  type: MessageType;
  icon: string;
  timestamp: Date;
  chatRoomId?: string; // Added for chat notifications
  receiverId?: number; // Added for chat notifications
  tradeId?: number; // Added for trade notifications
};

const typeMap: Record<string, MessageType> = {
  buy: 'buy',
  sell: 'sell',
  AirDrop: 'airdrop',
  Burn: 'burn',
  chat_message: 'general', // Map chat messages to general type
};

const typeIcons: Record<MessageType, string> = {
  buy: '💰',
  sell: '📈',
  airdrop: '🎁',
  burn: '🔥',
  general: '📝',
};


// Helper: validate if string is valid Solana address
const isValidSolAddress = (addr: string) => {
  try {
    new PublicKey(addr);
    return true;
  } catch {
    return false;
  }
};


// Check valid number
const isPositiveNumber = (value: any): boolean =>
  typeof value === "number" && value > 0 && Number.isFinite(value);

// Helper: create validated connection or throw
const getConnection = (): Connection => {
  if (!SOLANA_CONFIG.CONNECTION_URL) {
    throw new Error("Solana connection URL missing");
  }
  return new Connection(SOLANA_CONFIG.CONNECTION_URL);
};

// Validate positive number (amounts)
const validatePositiveNumber = (value: number, name: string) => {
  if (typeof value !== "number" || isNaN(value) || value <= 0) {
    throw new Error(`${name} must be a positive number`);
  }
};

export async function fetchFormattedUserLogs(userId: string, type: string): Promise<Message[]> {
  try {
    const response = await getUserLogs({ userId, type });

    // If we have data, format and return it
    if (response.data && response.data.length > 0) {
      return response.data.map((log: any): Message => {
        const logType = typeMap[log.type] || 'general';
        
        // Special handling for chat messages
        if (log.type === 'chat_message') {
          return {
            id: String(log.id),
            text: `New message: ${log.message || log.messageDetail || 'No message content'}`,
            type: 'general',
            icon: '💬',
            timestamp: new Date(log.createdAt),
            chatRoomId: log.chatRoomId,
            receiverId: log.userId,
            tradeId: log.tradeId,
          };
        }
        
        return {
          id: String(log.id),
          text: log.message || log.messageDetail || 'No message provided',
          type: logType,
          icon: typeIcons[logType],
          timestamp: new Date(log.createdAt),
          chatRoomId: log.chatRoomId,
          receiverId: log.userId,
          tradeId: log.tradeId,
        };
      });
    } else {
      // If no data returned, return empty array (will be handled by the component)
      console.log('No logs returned from API');
      return [];
    }
    return response.data.map((log: any): Message => {
      const logType = typeMap[log.type] || 'general';
      return {
        id: String(log.id),
        text: type === "0" ? log.message : log.messageDetail,
        type: logType,
        icon: typeIcons[logType],
        timestamp: new Date(log.createdAt),
      };
    });
  } catch (err) {
    console.error('Failed to fetch and format logs:', err);
    return [];
  }
}

export const uploadToPinata = async (file: File): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const metadata = JSON.stringify({ name: file.name });
    formData.append("pinataMetadata", metadata);

    const options = JSON.stringify({ cidVersion: 1 });
    formData.append("pinataOptions", options);
    const res = await axios.post(
      "https://api.pinata.cloud/pinning/pinFileToIPFS",
      formData,
      {
        maxBodyLength: Infinity,
        headers: {
          "Content-Type": "multipart/form-data",
          pinata_api_key: UPLOAD_CONFIG.PINATA_API_KEY,
          pinata_secret_api_key: UPLOAD_CONFIG.PINATA_API_SECRET,
        },
      }
    );

    const ipfsHash = res.data.IpfsHash;
    return `${UPLOAD_CONFIG.IPFS_GATEWAY}${ipfsHash}`;
  } catch (error) {
    console.error("Pinata upload failed:", error);
    throw new Error("Failed to upload file to Pinata.");
  }
};


interface TokenOption {
  id: number;
  symbol?: string;
  name?: string;
  icon?: string;
  tokenAddress?: string;
  balance: number;
}

/**
 * Simulates how many tokens you'd receive for a given SOL input with slippage tolerance.
 *
 * @param {PublicKey} mint - Token mint address.
 * @param {number} solInputLamports - Amount of SOL (in lamports).
 * @param {number} slippageTolerance - Decimal (e.g. 0.005 for 0.5%)
 * @returns {Promise<{ estimatedOutput: number, minOutput: number }>}
 */
export const getTokenOutputForSolInput = async (
  mintAddress: string,
  solInput: number,
  slippageTolerance: number = 0.005
): Promise<{ estimatedOutput: number; minOutput: number }> => {
  // Input validation
  if (!mintAddress || !isValidSolAddress(mintAddress)) throw new Error("Invalid or missing mint address.");
  if (!isPositiveNumber(solInput)) throw new Error("SOL input must be a positive number.");
  if (slippageTolerance < 0 || slippageTolerance >= 1) throw new Error("Invalid slippage tolerance.");

  const mint = new PublicKey(mintAddress);
  const solInputLamports = Math.round(solInput * LAMPORTS_PER_SOL);

  const bondingCurvePda = getBondingCurvePda(mint);
  
  // Check if bonding curve account exists
  try {
    const bondingCurve = await program.account.bondingCurve.fetch(bondingCurvePda);
    
    // Rest of existing logic...
    const x = bondingCurve.virtualTokenReserves.toNumber();
    const y = bondingCurve.virtualSolReserves.toNumber();
    let dy = solInputLamports;

    if (dy <= 0 || x <= 0 || y <= 0) throw new Error("Invalid input or zero reserves.");

    const k = x * y;

    const initialTokenReserves = 1_073_000_191 * 1e6;
    let fee;

    // Fee logic
    if (x === initialTokenReserves) {
      fee = 0.02 * LAMPORTS_PER_SOL;
      dy -= fee;
    } else {
      fee = 0.01 * dy;
      dy -= fee;
    }

    if (dy <= 0) throw new Error("Fee exceeds available input.");

    const newY = y + dy;
    if (newY === 0) throw new Error("Division by zero detected in bonding curve.");

    const newX = Math.floor(k / newY);
    let estimatedOutput = x - newX;
    const minOutput = Math.floor(estimatedOutput * (1 - slippageTolerance));

    estimatedOutput = estimatedOutput / 1e6;

    return { estimatedOutput, minOutput };
  } catch (error) {
    // Handle case where bonding curve doesn't exist (e.g., perk tokens)
    if (error.message?.includes("Account does not exist")) {
      console.warn(`No bonding curve found for token ${mintAddress}. This may be a perk token.`);
      return {
        estimatedOutput: 0,
        minOutput: 0
      };
    }
    throw error;
  }
};

export const getSolOutputForTokenInput = async (
  mintAddress: string,
  tokenInput: number,
  slippageTolerance = 0.005
) => {
  if (!mintAddress || !isValidSolAddress(mintAddress)) throw new Error("Invalid or missing mint address.");
  if (!isPositiveNumber(tokenInput)) throw new Error("Token input must be a positive number.");
  if (slippageTolerance < 0 || slippageTolerance >= 1) throw new Error("Invalid slippage tolerance.");

  const mint = new PublicKey(mintAddress);
  const tokenOutput = Math.round(tokenInput * 1e6); // Apply decimals

  const bondingCurvePda = getBondingCurvePda(mint);
  const bondingCurve = await program.account.bondingCurve.fetch(bondingCurvePda);

  const x = bondingCurve.virtualTokenReserves.toNumber();
  const y = bondingCurve.virtualSolReserves.toNumber();

  if (tokenOutput <= 0 || x <= 0 || y <= 0 || tokenOutput >= x)
    throw new Error("Invalid token input or insufficient reserves.");

  const k = x * y;
  const newX = x - tokenOutput;

  if (newX === 0) throw new Error("Division by zero detected in bonding curve.");
  const newY = Math.floor(k / newX);

  const dy = newY - y;
  if (dy <= 0) throw new Error("Calculated SOL is non-positive.");

  const initialTokenReserves = 1_073_000_191 * 1e6;
  let feeLamports;

  if (x === initialTokenReserves) {
    feeLamports = 0.02 * LAMPORTS_PER_SOL;
  } else {
    feeLamports = 0.01 * dy;
  }

  const totalInputLamports = Math.ceil(dy + feeLamports);
  const maxInputLamports = Math.ceil(totalInputLamports * (1 + slippageTolerance));
  const solAmount = maxInputLamports / LAMPORTS_PER_SOL;

  return {
    estimatedOutput: solAmount,
    minOutput: maxInputLamports,
    rawSolInputWithoutFee: dy,
    fee: feeLamports,
  };
};

export const getSolOutputForTokenInputFromPool = async (
  poolIdDB: string,
  tokenInput: number
) => {
  if (!poolIdDB || tokenInput <= 0 || isNaN(tokenInput)) {
    throw new Error("Invalid pool ID or token input amount");
  }

  let connection: Connection;
  try {
    connection = new Connection(SOLANA_CONFIG.CONNECTION_URL);
    const version = await connection.getVersion(); // ping
    if (!version) throw new Error("Connection failed");
  } catch (err) {
    throw new Error("Invalid Solana connection");
  }

  const raydium = await Raydium.load({
    connection,
    cluster: SOLANA_CONFIG.RAYDIUM_CLUSTER as "mainnet" | "devnet",
  });

  const poolId = new PublicKey(poolIdDB);
  const { poolInfo, rpcData } = await raydium.cpmm.getPoolInfoFromRpc(poolId.toBase58());

  const tokenDecimals = 6;
  const solDecimals = 9;

  const inputMint = poolInfo.mintB.address;
  const inputAmount = new BN(Math.floor(tokenInput * 10 ** tokenDecimals));

  if (inputAmount.lte(new BN(0))) {
    throw new Error("Input amount too small");
  }

  const baseIn = inputMint === poolInfo.mintA.address;
  const reserveIn = baseIn ? rpcData.baseReserve : rpcData.quoteReserve;
  const reserveOut = baseIn ? rpcData.quoteReserve : rpcData.baseReserve;

  if (reserveIn.lte(new BN(0)) || reserveOut.lte(new BN(0))) {
    throw new Error("Insufficient liquidity in pool");
  }

  const swapResult = CurveCalculator.swap(
    inputAmount,
    reserveIn,
    reserveOut,
    rpcData.configInfo!.tradeFeeRate
  );

  if (swapResult.destinationAmountSwapped.lte(new BN(0))) {
    throw new Error("Output too small");
  }

  const destinationReceived = swapResult.destinationAmountSwapped.toNumber() / 10 ** solDecimals;

  return {
    estimatedOutput: destinationReceived,
  };
};

export const getTokenOutputForSolInputFromPool = async (
  poolIdDB: string,
  solInput: number
) => {
  if (!poolIdDB || solInput <= 0 || isNaN(solInput)) {
    throw new Error("Invalid pool ID or SOL input amount");
  }

  let connection: Connection;
  try {
    connection = new Connection(SOLANA_CONFIG.CONNECTION_URL);
    const version = await connection.getVersion(); // ping
    if (!version) throw new Error("Connection failed");
  } catch (err) {
    throw new Error("Invalid Solana connection");
  }

  const raydium = await Raydium.load({
    connection,
    cluster: SOLANA_CONFIG.RAYDIUM_CLUSTER as "mainnet" | "devnet",
  });

  const poolId = new PublicKey(poolIdDB);
  const { poolInfo, rpcData } = await raydium.cpmm.getPoolInfoFromRpc(poolId.toBase58());

  const tokenDecimals = 6;
  const solDecimals = 9;

  const inputMint = poolInfo.mintA.address;
  const inputAmount = new BN(Math.floor(solInput * 10 ** solDecimals));

  if (inputAmount.lte(new BN(0))) {
    throw new Error("Input amount too small");
  }

  const baseIn = inputMint === poolInfo.mintA.address;
  const reserveIn = baseIn ? rpcData.baseReserve : rpcData.quoteReserve;
  const reserveOut = baseIn ? rpcData.quoteReserve : rpcData.baseReserve;

  if (reserveIn.lte(new BN(0)) || reserveOut.lte(new BN(0))) {
    throw new Error("Insufficient liquidity in pool");
  }

  const swapResult = CurveCalculator.swap(
    inputAmount,
    reserveIn,
    reserveOut,
    rpcData.configInfo!.tradeFeeRate
  );

  if (swapResult.destinationAmountSwapped.lte(new BN(0))) {
    throw new Error("Output too small");
  }

  const destinationReceived = swapResult.destinationAmountSwapped.toNumber() / 10 ** tokenDecimals;

  return {
    estimatedOutput: destinationReceived,
  };
};


export const executeWithdrawUnlocked = async (
  privyWallet: { address: string; signTransaction: (tx: Transaction) => Promise<Transaction> },
  mintAddress: string,
  creatorAddress: string
) => {
  try {


    const connection = new Connection(
      SOLANA_CONFIG.CONNECTION_URL || clusterApiUrl(SOLANA_CONFIG.CLUSTER as Cluster)
    );


    const mint = new PublicKey(mintAddress);
    const creator = new PublicKey(creatorAddress);

    // Get the instruction
    const withdrawIx: TransactionInstruction = await withdrawUnlocked(
      mintAddress,
      creatorAddress
    );

    // Build the transaction
    const transaction = new Transaction().add(withdrawIx);
    transaction.feePayer = creator; // This should be the user's public key
    transaction.recentBlockhash = (
      await connection.getLatestBlockhash()
    ).blockhash;

    (transaction as Transaction).feePayer = new PublicKey(creatorAddress);
    (transaction as Transaction).recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

    const signedTx = await privyWallet.signTransaction(transaction);
    const txId = await connection.sendRawTransaction(signedTx.serialize());
    await connection.confirmTransaction(txId, "confirmed");


    console.log("Transaction successful:", txId);
    return txId;

  } catch (error) {
    console.error("Withdraw failed:", error);
    throw error;
  }
};

const withdrawUnlocked = async (
  mintIs: string,
  creatorIs: string,
): Promise<TransactionInstruction> => {

  const mint = new PublicKey(mintIs)
  const creator = new PublicKey(creatorIs)
  const withdrawUnlockedIx = await program.methods
    .withdrawUnlocked()
    .accounts({
      creator,
      mint,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .instruction();
  return withdrawUnlockedIx;
};

export const tokenBuyFromPool = async (
  poolIdDB: string,
  buyToken: string,
  privyWallet: any,
  slippage: number,
  isBuying: boolean
) => {
  if (
    !privyWallet ||
    typeof privyWallet.signTransaction !== "function" ||
    !privyWallet.address
  ) {
    throw new Error("Wallet not connected or signTransaction missing");
  }

  if (!poolIdDB || !buyToken || isNaN(Number(buyToken)) || Number(buyToken) <= 0) {
    throw new Error("Invalid input: Pool ID or amount");
  }

  const connection = new Connection(SOLANA_CONFIG.CONNECTION_URL);

  const poolId = new PublicKey(poolIdDB);
  const ownerKey = new PublicKey(privyWallet.address);
  let txId = "";

  try {
    const raydium = await Raydium.load({
      connection,
      cluster: SOLANA_CONFIG.RAYDIUM_CLUSTER as "mainnet" | "devnet",
      owner: ownerKey,
    });

    const { poolInfo, poolKeys, rpcData } = await raydium.cpmm.getPoolInfoFromRpc(
      poolId.toBase58()
    );

    if (!poolInfo || !poolKeys || !rpcData) {
      throw new Error("Failed to fetch pool data");
    }

    const tokenDecimals = 6;
    const solDecimals = 9;
    const inputMint = isBuying ? poolInfo.mintA.address : poolInfo.mintB.address;
    const decimals = isBuying ? solDecimals : tokenDecimals;

    const inputAmount = new BN(Math.floor(Number(buyToken) * 10 ** decimals));
    if (inputAmount.lte(new BN(0))) {
      throw new Error("Input amount must be greater than 0");
    }

    // Check if pool reserves are non-zero
    if (
      rpcData.baseReserve.lte(new BN(0)) ||
      rpcData.quoteReserve.lte(new BN(0))
    ) {
      throw new Error("Insufficient liquidity in pool");
    }

    const baseIn = inputMint === poolInfo.mintA.address;

    const swapResult = CurveCalculator.swap(
      inputAmount,
      baseIn ? rpcData.baseReserve : rpcData.quoteReserve,
      baseIn ? rpcData.quoteReserve : rpcData.baseReserve,
      rpcData.configInfo!.tradeFeeRate
    );

   

    const { transaction } = await raydium.cpmm.swap({
      poolInfo,
      poolKeys,
      inputAmount,
      swapResult,
      slippage,
      baseIn,
    });

    if (!transaction) throw new Error("Transaction generation failed");

    (transaction as Transaction).feePayer = ownerKey;
    (transaction as Transaction).recentBlockhash = (
      await connection.getLatestBlockhash()
    ).blockhash;

    const signedTx = await privyWallet.signTransaction(transaction);
    txId = await connection.sendRawTransaction(signedTx.serialize());
    await connection.confirmTransaction(txId, "confirmed");

  } catch (err: any) {
    if (err.message?.includes("User rejected") || err.code === 4001) {
      console.warn("User rejected the transaction");
      return "0";
    } else {
      console.error("Transaction failed:", err.message || err);
      return "0";
    }
  }

  return txId;
};


export const tokenBuyFromContract = async (
  tokenMint: string,
  creatorWallet: string,
  privyWallet: any,
  slippage: number,
  amount: number
) => {
  // Input validation
 
  if (!tokenMint || !creatorWallet) throw new Error("tokenMint and creatorWallet are required");
  if (!isValidSolAddress(tokenMint)) throw new Error("Invalid tokenMint address");
  if (!isValidSolAddress(creatorWallet)) throw new Error("Invalid creatorWallet address");
  if (typeof amount !== "number" || amount <= 0) throw new Error("Amount must be a positive number");
  if (typeof slippage !== "number" || slippage < 0 || slippage > 1) throw new Error("Slippage must be between 0 and 1");
  if (!privyWallet || !privyWallet.signTransaction || !privyWallet.address) {
    throw new Error("Wallet not connected or signTransaction missing");
  }

  console.log(slippage);
  console.log(typeof amount !== "number");
  console.log(amount <= 0);
  const connection = new Connection(SOLANA_CONFIG.CONNECTION_URL);

  // Connection validation
  try {
    await connection.getVersion();
  } catch (e) {
    throw new Error("Failed to connect to Solana network");
  }

  // Check if amount is safe integer to avoid overflow issues when converting decimals
  if (!Number.isSafeInteger(amount * 1e6)) {
    throw new Error("Amount is too large or has precision issues");
  }

  const tokenMint_key = new PublicKey(tokenMint);
  const creatorWallet_key = new PublicKey(creatorWallet);
  const buyer_key = new PublicKey(privyWallet.address);

  // --- Balance verification (example placeholder) ---
  // const buyerBalance = await connection.getBalance(buyer_key);
  // if (buyerBalance < requiredLamports) throw new Error("Insufficient balance");

  // --- Withdraw limit checks (implement your logic as needed) ---
  // if (amount > MAX_BUY_LIMIT) throw new Error("Amount exceeds maximum buy limit");

  let txId = "";

  try {
    // Here minTokenOutputUi is set to 0, can adjust to slippage calculations if needed
    const instruction = await buyTokenInstruction(
      amount,
      0,
      tokenMint_key,
      creatorWallet_key,
      buyer_key
    );

    const transaction = new Transaction().add(instruction);
    transaction.feePayer = buyer_key;

    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

    const signedTx = await privyWallet.signTransaction(transaction);
    txId = await connection.sendRawTransaction(signedTx.serialize());
    await connection.confirmTransaction(txId, "confirmed");
  } catch (err: any) {
    if (err.message?.includes("User rejected") || err.code === 4001) {
      console.log("User rejected the transaction");
      return "0";
    }
    console.error("Transaction failed:", err);
    return "0";
  }

  return txId;
};
///////////////////////////////////////////////////

export const tokenSellFromContract = async (
  tokenMint: string,
  creatorWallet: string,
  privyWallet: any,
  slippage: number,
  amount: number
) => {
  if (!privyWallet || !privyWallet.signTransaction || !privyWallet.address) {
    throw new Error("Wallet not connected or signTransaction missing");
  }

  if (!tokenMint || !isValidSolAddress(tokenMint)) {
    throw new Error("Invalid or missing tokenMint address");
  }
  if (!creatorWallet || !isValidSolAddress(creatorWallet)) {
    throw new Error("Invalid or missing creatorWallet address");
  }
  validatePositiveNumber(amount, "Amount");
  if (typeof slippage !== "number" || slippage < 0 || slippage > 1) {
    throw new Error("Slippage must be between 0 and 1");
  }

  const connection = getConnection();

  const tokenMint_key = new PublicKey(tokenMint);
  const creatorWallet_key = new PublicKey(creatorWallet);
  const buyer_key = new PublicKey(privyWallet.address);

  // Optional: Check withdraw limit here (implement your logic)
  // e.g. const withdrawLimit = await getWithdrawLimit(creatorWallet_key);
  // if(amount > withdrawLimit) throw new Error("Amount exceeds withdraw limit");

  // Balance check for seller's token account
  const sellerTokenAccounts = await connection.getParsedTokenAccountsByOwner(buyer_key, {
    programId: TOKEN_PROGRAM_ID,
  });
  let tokenBalance = 0;
  for (const acc of sellerTokenAccounts.value) {
    const info = acc.account.data.parsed.info;
    if (info.mint === tokenMint_key.toBase58()) {
      tokenBalance = info.tokenAmount.uiAmount || 0;
      break;
    }
  }
  if (tokenBalance < amount) {
    throw new Error(`Insufficient token balance. You have ${tokenBalance}, but tried to sell ${amount}`);
  }

  let txId = "";

  try {
    const instruction = await sellTokenInstruction(
      amount,
      0, // minSolOutput placeholder, can be calculated with slippage tolerance if needed
      tokenMint_key,
      creatorWallet_key,
      buyer_key
    );

    const transaction = new Transaction().add(instruction);
    transaction.feePayer = buyer_key;

    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

    const signedTx = await privyWallet.signTransaction(transaction);

    txId = await connection.sendRawTransaction(signedTx.serialize());
    await connection.confirmTransaction(txId, "confirmed");
  } catch (err: any) {
    if (err.message?.includes("User rejected") || err.code === 4001) {
      console.log("User rejected the transaction");
      return "0";
    } else {
      console.error("Transaction failed:", err);
      return "0";
    }
  }

  return txId;
};

export const getTokenDetails = async (
  mintAddress: PublicKey
): Promise<{ tokenPrice: number; tokenMarketcap: number }> => {
  const connection = getConnection();

  if (!mintAddress) {
    throw new Error("mintAddress is required");
  }

  const bondingCurvePda = getBondingCurvePda(mintAddress);
  const bondingCurve = await program.account.bondingCurve.fetch(bondingCurvePda);

  // Defensive division check
  if (bondingCurve.virtualTokenReserves.isZero()) {
    throw new Error("Virtual token reserves is zero - division by zero avoided");
  }

  const tokenPrice =
    bondingCurve.virtualSolReserves.toNumber() / bondingCurve.virtualTokenReserves.toNumber();

  const mintInfo = await getMint(connection, mintAddress);
  const decimals = mintInfo.decimals;

  const circulatingSupply = bondingCurve.virtualTokenReserves.toNumber() / Math.pow(10, decimals);
  const tokenMarketcap = tokenPrice * circulatingSupply;

  return { tokenPrice, tokenMarketcap };
};

export const buyTokenInstruction = async (
  solAmountInSol: number,
  minTokenOutputUi: number,
  mint: PublicKey,
  creator: PublicKey,
  buyer: PublicKey,
  tokenDecimals: number = 6
): Promise<TransactionInstruction> => {
  const globalData = await program.account.global.fetch(global);

  const solAmount = new BN(solAmountInSol * 1_000_000_000);
  const minTokenOutput = new BN(minTokenOutputUi * 10 ** tokenDecimals);

  const buyIx = await program.methods
    .buy(solAmount, minTokenOutput)
    .accounts({
      signer: buyer,
      mint,
      creator,
      feeRecipient: globalData.feeRecipient,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .instruction();

  return buyIx;
};

const sellTokenInstruction = async (
  tokenAmountUi: number,
  minSolOutputSol: number,
  mint: PublicKey,
  creator: PublicKey,
  seller: PublicKey,
  tokenDecimals: number = 6
): Promise<TransactionInstruction> => {
  validatePositiveNumber(tokenAmountUi, "Token amount");
  if (minSolOutputSol < 0) {
    throw new Error("Minimum SOL output cannot be negative");
  }

  const globalData = await program.account.global.fetch(global);

  const tokenAmount = new BN(tokenAmountUi * 10 ** tokenDecimals);
  const minSolOutput = new BN(minSolOutputSol * 1_000_000_000);

  const sellIx = await program.methods
    .sell(tokenAmount, minSolOutput)
    .accounts({
      signer: seller,
      mint,
      creator,
      feeRecipient: globalData.feeRecipient,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .instruction();

  return sellIx;
};

export const formatSecondsToHMS = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600)
    .toString()
    .padStart(2, "0");
  const minutes = Math.floor((totalSeconds % 3600) / 60)
    .toString()
    .padStart(2, "0");
  const seconds = (totalSeconds % 60).toString().padStart(2, "0");
  return `${hours}h : ${minutes}m : ${seconds}s`;
};

export async function fetchTokenOptions(address: string): Promise<TokenOption[]> {
  if (!address) return [];

  if (!isValidSolAddress(address)) {
    console.warn("Invalid wallet address for fetching token options");
    return [];
  }

  try {
    const connection = getConnection();
    const publicKey = new PublicKey(address);

    const accounts = await connection.getParsedTokenAccountsByOwner(publicKey, {
      programId: TOKEN_PROGRAM_ID,
    });

    return accounts.value
      .map((acc, idx) => {
        const info = acc.account.data.parsed.info;
        const tokenAmount = info.tokenAmount.uiAmount;

        return tokenAmount > 0
          ? {
              id: idx,
              tokenAddress: info.mint,
              balance: tokenAmount,
              symbol: info?.mint?.slice(0, 6), // Placeholder
              name: `Token ${idx + 1}`, // Placeholder
            }
          : null;
      })
      .filter(Boolean) as TokenOption[];
  } catch (err) {
    console.error("Failed to fetch token options:", err);
    return [];
  }
}

export const transferSPLTokenWithSigner = async (
  privyWallet: { address: string; signTransaction: (tx: Transaction) => Promise<Transaction> },
  recipientAddress: string | PublicKey,
  mintAddress: string | PublicKey,
  amount: number,
  decimals: number
): Promise<{ success: boolean; signature?: string; error?: any }> => {
  if (!privyWallet || !privyWallet.signTransaction || !privyWallet.address) {
    return { success: false, error: "Wallet not connected or signTransaction missing" };
  }

  if (!recipientAddress) {
    return { success: false, error: "Recipient address missing" };
  }

  if (!mintAddress) {
    return { success: false, error: "Mint address missing" };
  }

  const connection = getConnection();

  try {
    const sender = new PublicKey(privyWallet.address);
    const recipient = new PublicKey(recipientAddress);
    const mint = new PublicKey(mintAddress);

    const senderATA = await getAssociatedTokenAddress(mint, sender);
    const recipientATA = await getAssociatedTokenAddress(mint, recipient);

    const amountInSmallestUnit = BigInt(Math.floor(amount * 10 ** decimals));

    const tx = new Transaction();

    // Create recipient ATA if it does not exist
    try {
      await getAccount(connection, recipientATA);
    } catch {
      tx.add(createAssociatedTokenAccountInstruction(sender, recipientATA, recipient, mint));
    }

    tx.add(createTransferInstruction(senderATA, recipientATA, sender, amountInSmallestUnit));

    tx.feePayer = sender;
    tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

    const signedTx = await privyWallet.signTransaction(tx);
    const signature = await connection.sendRawTransaction(signedTx.serialize());
    await connection.confirmTransaction(signature, "confirmed");

    return { success: true, signature };
  } catch (error) {
    console.error("Transfer failed:", error);
    return { success: false, error };
  }
};
