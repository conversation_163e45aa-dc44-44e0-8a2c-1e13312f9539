'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from '@/hooks/useTranslation';

import {
  ChevronLeft,
  ChevronRight,
  Filter,
  ExternalLink,
  Calendar,
  DollarSign,
  ShoppingCart,
  Coins,
  ChevronDown,
  SortAsc,
  SortDesc,
} from 'lucide-react';
import { APP_CONFIG } from '@/config/environment';

// Enhanced interfaces
interface Transaction {
  purchasedType: string;
  hasH: any;
  dollorPrice: any;
  buyorsell: any;
  id: number;
  name: string;
  date: string;
  amount: string;
  type: 'buy' | 'sell';
  icon?: string;
}

interface TransactionHistoryProps {
  transactions?: Transaction[];
  defaultTransactions?: Transaction[];
  itemsPerPage?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  onSort?: () => void;
  onShowDetails?: (transaction: Transaction) => void;
  onTransactionClick?: (transaction: Transaction) => void;
  title?: string;
  sortButtonText?: string;
  showPagination?: boolean;
  className?: string;
  loading?: boolean;
}

type FilterType = 'all' | 'perk' | 'token' | 'coin';
type SortField = 'date' | 'amount' | 'name';
type SortDirection = 'asc' | 'desc';

const defaultTransactionsData: Transaction[] = [
  {
    id: 1,
    name: 'Token buy',
    date: 'Apr 18, 2024 | 10:58PM',
    amount: '-$75.50',
    dollorPrice: 75.5,
    hasH: 'sample_hash_1',
    type: 'buy',
    icon: '/icons/shopping-cart.svg',
    buyorsell: false,
    purchasedType: 'Token',
  },
  {
    id: 2,
    name: 'Perk Purchase',
    date: 'Apr 17, 2024 | 08:30AM',
    amount: '-$125.00',
    dollorPrice: 125.0,
    hasH: 'sample_hash_2',
    type: 'buy',
    icon: '/icons/shopping-cart.svg',
    buyorsell: false,
    purchasedType: 'Perk',
  },
  {
    id: 3,
    name: 'Perk sell',
    date: 'Apr 16, 2024 | 02:15PM',
    amount: '+$75.50',
    dollorPrice: 75.5,
    hasH: 'sample_hash_3',
    type: 'sell',
    icon: '/icons/money-recive.svg',
    buyorsell: true,
    purchasedType: 'Perk',
  },
  {
    id: 4,
    name: 'Coin Purchase',
    date: 'Apr 15, 2024 | 11:45AM',
    amount: '-$200.00',
    dollorPrice: 200.0,
    hasH: 'sample_hash_4',
    type: 'buy',
    icon: '/icons/shopping-cart.svg',
    buyorsell: false,
    purchasedType: 'Coin',
  },
];

const TransactionHistory: React.FC<TransactionHistoryProps> = ({
  transactions,
  defaultTransactions = defaultTransactionsData,
  itemsPerPage = 8,
  currentPage: controlledCurrentPage,
  onPageChange,
  onSort,
  onShowDetails,
  onTransactionClick,
  title = 'Transaction History',
  showPagination = true,
  className = '',
  loading = false,
}) => {
  const { t } = useTranslation();
  // State management
  const [internalCurrentPage, setInternalCurrentPage] = useState(1);
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  const currentPage =
    controlledCurrentPage !== undefined
      ? controlledCurrentPage
      : internalCurrentPage;
  const displayTransactions = transactions || defaultTransactions;

  // Filter options
  const filterOptions = [
    {
      value: 'all' as FilterType,
      label: 'All Transactions',
      icon: <Filter className="w-4 h-4" />,
    },
    {
      value: 'perk' as FilterType,
      label: 'Perk Purchases',
      icon: <ShoppingCart className="w-4 h-4" />,
    },
    {
      value: 'token' as FilterType,
      label: 'Token Purchases',
      icon: <Coins className="w-4 h-4" />,
    },
    {
      value: 'coin' as FilterType,
      label: 'Coin Purchases',
      icon: <DollarSign className="w-4 h-4" />,
    },
  ];

  // Memoized filtered and sorted data
  const processedTransactions = useMemo(() => {
    let filtered = displayTransactions;

    // Apply filters
    if (filterType !== 'all') {
      filtered = displayTransactions.filter((transaction) => {
        const type = transaction.purchasedType.toLowerCase();
        return type.includes(filterType.toLowerCase());
      });
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case 'date':
          aValue = new Date(a.date.replace(' | ', ' ')).getTime();
          bValue = new Date(b.date.replace(' | ', ' ')).getTime();
          break;
        case 'amount':
          aValue = Math.abs(a.dollorPrice || 0);
          bValue = Math.abs(b.dollorPrice || 0);
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        default:
          return 0;
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return sorted;
  }, [displayTransactions, filterType, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(processedTransactions.length / itemsPerPage);
  const paginatedTransactions = processedTransactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Event handlers
  const handlePageChange = useCallback(
    (page: number) => {
      if (onPageChange) {
        onPageChange(page);
      } else {
        setInternalCurrentPage(page);
      }
    },
    [onPageChange]
  );

  const handleSort = useCallback(
    (field: SortField) => {
      if (field === sortField) {
        setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
      } else {
        setSortField(field);
        setSortDirection('desc');
      }

      if (onSort) {
        onSort();
      }
    },
    [sortField, onSort]
  );

  const handleShowDetails = useCallback(
    (transaction: Transaction) => {
      if (transaction?.hasH) {
        const isLive = APP_CONFIG.IS_LIVE;
        const clusterParam = isLive ? '' : '?cluster=devnet';
        window.open(
          `https://solscan.io/tx/${transaction.hasH}${clusterParam}`,
          '_blank'
        );
      }

      if (onShowDetails) {
        onShowDetails(transaction);
      }
    },
    [onShowDetails]
  );

  const handleTransactionClick = useCallback(
    (transaction: Transaction) => {
      if (onTransactionClick) {
        onTransactionClick(transaction);
      }
    },
    [onTransactionClick]
  );

  const getTransactionIcon = (transaction: Transaction) => {
    if (transaction.icon) {
      return (
        <Image
          src={transaction.icon}
          alt={transaction.name}
          width={20}
          height={20}
          className="text-white"
        />
      );
    }

    // Fallback icons based on type
    switch (transaction.purchasedType.toLowerCase()) {
      case 'perk':
        return <ShoppingCart className="w-5 h-5 text-white" />;
      case 'token':
        return <Coins className="w-5 h-5 text-white" />;
      case 'coin':
        return <DollarSign className="w-5 h-5 text-white" />;
      default:
        return <ShoppingCart className="w-5 h-5 text-white" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'perk':
        return 'border border-orange-300 text-white';
      case 'token':
        return 'border border-orange-400 text-orange-800';
      case 'coin':
        return 'border border-orange-200 text-orange-700';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeBgColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'perk':
        return '#FF6600';
      case 'token':
        return '#FFE5CC';
      case 'coin':
        return '#FFF2E5';
      default:
        return '';
    }
  };

  // Reset to first page when filter changes
  useEffect(() => {
    setInternalCurrentPage(1);
  }, [filterType]);

  if (loading) {
    return (
      <div className="w-full p-8 bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={`w-full bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-2xl font-semibold text-gray-900">{title}</h2>

          <div className="flex items-center gap-3">
            {/* Filter Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                {filterOptions.find((opt) => opt.value === filterType)?.icon}
                <span className="font-medium text-gray-700">
                  {filterOptions.find((opt) => opt.value === filterType)?.label}
                </span>
                <ChevronDown
                  className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                    showFilterDropdown ? 'rotate-180' : ''
                  }`}
                />
              </button>

              <AnimatePresence>
                {showFilterDropdown && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    transition={{ duration: 0.15 }}
                    className="absolute top-full mt-2 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[200px]"
                  >
                    {filterOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => {
                          setFilterType(option.value);
                          setShowFilterDropdown(false);
                        }}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 ${
                          filterType === option.value
                            ? 'text-white'
                            : 'text-gray-700'
                        } ${
                          option.value === filterOptions[0].value
                            ? 'rounded-t-lg'
                            : ''
                        } ${
                          option.value ===
                          filterOptions[filterOptions.length - 1].value
                            ? 'rounded-b-lg'
                            : ''
                        }`}
                        style={
                          filterType === option.value
                            ? { backgroundColor: '#FF6600' }
                            : {}
                        }
                      >
                        {option.icon}
                        <span className="font-medium">{option.label}</span>
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filter Summary */}
        {filterType !== 'all' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 text-sm text-gray-600"
          >
            Showing {processedTransactions.length} {filterType} transaction
            {processedTransactions.length !== 1 ? 's' : ''}
          </motion.div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        {processedTransactions.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center py-16 px-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <div className="text-gray-400 mb-4">
              <Filter className="w-16 h-16" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('table.noTransactionsFound')}
            </h3>
            <p className="text-gray-600 text-center max-w-md">
              {filterType !== 'all'
                ? t('table.noFilteredTransactions', { filterType })
                : t('table.noTransactionsAvailable')}
            </p>
          </motion.div>
        ) : (
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 font-semibold text-gray-900 transition-colors hover:text-[#FF6600]"
                  >
                    Transaction
                    {sortField === 'name' &&
                      (sortDirection === 'asc' ? (
                        <SortAsc className="w-4 h-4" />
                      ) : (
                        <SortDesc className="w-4 h-4" />
                      ))}
                  </button>
                </th>
                <th className="px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort('date')}
                    className="flex items-center gap-2 font-semibold text-gray-900 transition-colors hover:text-[#FF6600]"
                  >
                    Date & Time
                    {sortField === 'date' &&
                      (sortDirection === 'asc' ? (
                        <SortAsc className="w-4 h-4" />
                      ) : (
                        <SortDesc className="w-4 h-4" />
                      ))}
                  </button>
                </th>
                <th className="px-6 py-4 text-center font-semibold text-gray-900">
                  Type
                </th>
                <th className="px-6 py-4 text-right">
                  <button
                    onClick={() => handleSort('amount')}
                    className="flex items-center gap-2 font-semibold text-gray-900 transition-colors hover:text-[#FF6600] ml-auto"
                  >
                    Amount
                    {sortField === 'amount' &&
                      (sortDirection === 'asc' ? (
                        <SortAsc className="w-4 h-4" />
                      ) : (
                        <SortDesc className="w-4 h-4" />
                      ))}
                  </button>
                </th>
                <th className="px-6 py-4 text-center font-semibold text-gray-900">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <AnimatePresence mode="wait">
                {paginatedTransactions.map((transaction, index) => (
                  <motion.tr
                    key={transaction.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2, delay: index * 0.02 }}
                    className="hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                    onClick={() => handleTransactionClick(transaction)}
                  >
                    {/* Transaction Name & Icon */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gray-900 rounded-full flex items-center justify-center">
                          {getTransactionIcon(transaction)}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {transaction.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {transaction.id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Date */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2 text-gray-900">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="font-medium">{transaction.date}</span>
                      </div>
                    </td>

                    {/* Type Badge */}
                    <td className="px-6 py-4 text-center">
                      <span
                        className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getTypeColor(
                          transaction.purchasedType
                        )}`}
                        style={{
                          backgroundColor: getTypeBgColor(
                            transaction.purchasedType
                          ),
                        }}
                      >
                        {transaction.purchasedType}
                      </span>
                    </td>

                    {/* Amount */}
                    <td className="px-6 py-4 text-right">
                      <div
                        className={`font-semibold ${
                          transaction.buyorsell
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {transaction.buyorsell ? '+' : '-'}$
                        {transaction.dollorPrice}
                      </div>
                    </td>

                    {/* Action Button */}
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShowDetails(transaction);
                        }}
                        className="inline-flex items-center gap-2 px-3 py-1.5 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                        style={{
                          backgroundColor: '#FF6600',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#E55A00';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#FF6600';
                        }}
                      >
                        <ExternalLink className="w-4 h-4" />
                        Details
                      </button>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        )}
      </div>

      {/* Pagination */}
      {showPagination && totalPages > 1 && processedTransactions.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
              {Math.min(
                currentPage * itemsPerPage,
                processedTransactions.length
              )}{' '}
              of {processedTransactions.length} transactions
            </div>

            <div className="flex items-center gap-2">
              <motion.button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronLeft className="w-4 h-4" />
              </motion.button>

              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <motion.button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      pageNum === currentPage
                        ? 'text-white'
                        : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                    style={
                      pageNum === currentPage
                        ? { backgroundColor: '#FF6600' }
                        : {}
                    }
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {pageNum}
                  </motion.button>
                );
              })}

              <motion.button
                onClick={() =>
                  handlePageChange(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronRight className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default TransactionHistory;
