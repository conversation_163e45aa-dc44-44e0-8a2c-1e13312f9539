"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/borsh";
exports.ids = ["vendor-chunks/borsh"];
exports.modules = {

/***/ "(ssr)/./node_modules/borsh/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/borsh/lib/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deserializeUnchecked = exports.deserialize = exports.serialize = exports.BinaryReader = exports.BinaryWriter = exports.BorshError = exports.baseDecode = exports.baseEncode = void 0;\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\"));\nconst bs58_1 = __importDefault(__webpack_require__(/*! bs58 */ \"(ssr)/./node_modules/bs58/index.js\"));\n// TODO: Make sure this polyfill not included when not required\nconst encoding = __importStar(__webpack_require__(/*! text-encoding-utf-8 */ \"(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\"));\nconst ResolvedTextDecoder = typeof TextDecoder !== \"function\" ? encoding.TextDecoder : TextDecoder;\nconst textDecoder = new ResolvedTextDecoder(\"utf-8\", { fatal: true });\nfunction baseEncode(value) {\n    if (typeof value === \"string\") {\n        value = Buffer.from(value, \"utf8\");\n    }\n    return bs58_1.default.encode(Buffer.from(value));\n}\nexports.baseEncode = baseEncode;\nfunction baseDecode(value) {\n    return Buffer.from(bs58_1.default.decode(value));\n}\nexports.baseDecode = baseDecode;\nconst INITIAL_LENGTH = 1024;\nclass BorshError extends Error {\n    constructor(message) {\n        super(message);\n        this.fieldPath = [];\n        this.originalMessage = message;\n    }\n    addToFieldPath(fieldName) {\n        this.fieldPath.splice(0, 0, fieldName);\n        // NOTE: Modifying message directly as jest doesn't use .toString()\n        this.message = this.originalMessage + \": \" + this.fieldPath.join(\".\");\n    }\n}\nexports.BorshError = BorshError;\n/// Binary encoder.\nclass BinaryWriter {\n    constructor() {\n        this.buf = Buffer.alloc(INITIAL_LENGTH);\n        this.length = 0;\n    }\n    maybeResize() {\n        if (this.buf.length < 16 + this.length) {\n            this.buf = Buffer.concat([this.buf, Buffer.alloc(INITIAL_LENGTH)]);\n        }\n    }\n    writeU8(value) {\n        this.maybeResize();\n        this.buf.writeUInt8(value, this.length);\n        this.length += 1;\n    }\n    writeU16(value) {\n        this.maybeResize();\n        this.buf.writeUInt16LE(value, this.length);\n        this.length += 2;\n    }\n    writeU32(value) {\n        this.maybeResize();\n        this.buf.writeUInt32LE(value, this.length);\n        this.length += 4;\n    }\n    writeU64(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 8)));\n    }\n    writeU128(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 16)));\n    }\n    writeU256(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 32)));\n    }\n    writeU512(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 64)));\n    }\n    writeBuffer(buffer) {\n        // Buffer.from is needed as this.buf.subarray can return plain Uint8Array in browser\n        this.buf = Buffer.concat([\n            Buffer.from(this.buf.subarray(0, this.length)),\n            buffer,\n            Buffer.alloc(INITIAL_LENGTH),\n        ]);\n        this.length += buffer.length;\n    }\n    writeString(str) {\n        this.maybeResize();\n        const b = Buffer.from(str, \"utf8\");\n        this.writeU32(b.length);\n        this.writeBuffer(b);\n    }\n    writeFixedArray(array) {\n        this.writeBuffer(Buffer.from(array));\n    }\n    writeArray(array, fn) {\n        this.maybeResize();\n        this.writeU32(array.length);\n        for (const elem of array) {\n            this.maybeResize();\n            fn(elem);\n        }\n    }\n    toArray() {\n        return this.buf.subarray(0, this.length);\n    }\n}\nexports.BinaryWriter = BinaryWriter;\nfunction handlingRangeError(target, propertyKey, propertyDescriptor) {\n    const originalMethod = propertyDescriptor.value;\n    propertyDescriptor.value = function (...args) {\n        try {\n            return originalMethod.apply(this, args);\n        }\n        catch (e) {\n            if (e instanceof RangeError) {\n                const code = e.code;\n                if ([\"ERR_BUFFER_OUT_OF_BOUNDS\", \"ERR_OUT_OF_RANGE\"].indexOf(code) >= 0) {\n                    throw new BorshError(\"Reached the end of buffer when deserializing\");\n                }\n            }\n            throw e;\n        }\n    };\n}\nclass BinaryReader {\n    constructor(buf) {\n        this.buf = buf;\n        this.offset = 0;\n    }\n    readU8() {\n        const value = this.buf.readUInt8(this.offset);\n        this.offset += 1;\n        return value;\n    }\n    readU16() {\n        const value = this.buf.readUInt16LE(this.offset);\n        this.offset += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.buf.readUInt32LE(this.offset);\n        this.offset += 4;\n        return value;\n    }\n    readU64() {\n        const buf = this.readBuffer(8);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU128() {\n        const buf = this.readBuffer(16);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU256() {\n        const buf = this.readBuffer(32);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU512() {\n        const buf = this.readBuffer(64);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readBuffer(len) {\n        if (this.offset + len > this.buf.length) {\n            throw new BorshError(`Expected buffer length ${len} isn't within bounds`);\n        }\n        const result = this.buf.slice(this.offset, this.offset + len);\n        this.offset += len;\n        return result;\n    }\n    readString() {\n        const len = this.readU32();\n        const buf = this.readBuffer(len);\n        try {\n            // NOTE: Using TextDecoder to fail on invalid UTF-8\n            return textDecoder.decode(buf);\n        }\n        catch (e) {\n            throw new BorshError(`Error decoding UTF-8 string: ${e}`);\n        }\n    }\n    readFixedArray(len) {\n        return new Uint8Array(this.readBuffer(len));\n    }\n    readArray(fn) {\n        const len = this.readU32();\n        const result = Array();\n        for (let i = 0; i < len; ++i) {\n            result.push(fn());\n        }\n        return result;\n    }\n}\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU8\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU16\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU32\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU64\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU128\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU256\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU512\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readString\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readFixedArray\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readArray\", null);\nexports.BinaryReader = BinaryReader;\nfunction capitalizeFirstLetter(string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}\nfunction serializeField(schema, fieldName, value, fieldType, writer) {\n    try {\n        // TODO: Handle missing values properly (make sure they never result in just skipped write)\n        if (typeof fieldType === \"string\") {\n            writer[`write${capitalizeFirstLetter(fieldType)}`](value);\n        }\n        else if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                if (value.length !== fieldType[0]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[0]}, but got ${value.length} bytes`);\n                }\n                writer.writeFixedArray(value);\n            }\n            else if (fieldType.length === 2 && typeof fieldType[1] === \"number\") {\n                if (value.length !== fieldType[1]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[1]}, but got ${value.length} bytes`);\n                }\n                for (let i = 0; i < fieldType[1]; i++) {\n                    serializeField(schema, null, value[i], fieldType[0], writer);\n                }\n            }\n            else {\n                writer.writeArray(value, (item) => {\n                    serializeField(schema, fieldName, item, fieldType[0], writer);\n                });\n            }\n        }\n        else if (fieldType.kind !== undefined) {\n            switch (fieldType.kind) {\n                case \"option\": {\n                    if (value === null || value === undefined) {\n                        writer.writeU8(0);\n                    }\n                    else {\n                        writer.writeU8(1);\n                        serializeField(schema, fieldName, value, fieldType.type, writer);\n                    }\n                    break;\n                }\n                case \"map\": {\n                    writer.writeU32(value.size);\n                    value.forEach((val, key) => {\n                        serializeField(schema, fieldName, key, fieldType.key, writer);\n                        serializeField(schema, fieldName, val, fieldType.value, writer);\n                    });\n                    break;\n                }\n                default:\n                    throw new BorshError(`FieldType ${fieldType} unrecognized`);\n            }\n        }\n        else {\n            serializeStruct(schema, value, writer);\n        }\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction serializeStruct(schema, obj, writer) {\n    if (typeof obj.borshSerialize === \"function\") {\n        obj.borshSerialize(writer);\n        return;\n    }\n    const structSchema = schema.get(obj.constructor);\n    if (!structSchema) {\n        throw new BorshError(`Class ${obj.constructor.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        structSchema.fields.map(([fieldName, fieldType]) => {\n            serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n        });\n    }\n    else if (structSchema.kind === \"enum\") {\n        const name = obj[structSchema.field];\n        for (let idx = 0; idx < structSchema.values.length; ++idx) {\n            const [fieldName, fieldType] = structSchema.values[idx];\n            if (fieldName === name) {\n                writer.writeU8(idx);\n                serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n                break;\n            }\n        }\n    }\n    else {\n        throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${obj.constructor.name}`);\n    }\n}\n/// Serialize given object using schema of the form:\n/// { class_name -> [ [field_name, field_type], .. ], .. }\nfunction serialize(schema, obj, Writer = BinaryWriter) {\n    const writer = new Writer();\n    serializeStruct(schema, obj, writer);\n    return writer.toArray();\n}\nexports.serialize = serialize;\nfunction deserializeField(schema, fieldName, fieldType, reader) {\n    try {\n        if (typeof fieldType === \"string\") {\n            return reader[`read${capitalizeFirstLetter(fieldType)}`]();\n        }\n        if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                return reader.readFixedArray(fieldType[0]);\n            }\n            else if (typeof fieldType[1] === \"number\") {\n                const arr = [];\n                for (let i = 0; i < fieldType[1]; i++) {\n                    arr.push(deserializeField(schema, null, fieldType[0], reader));\n                }\n                return arr;\n            }\n            else {\n                return reader.readArray(() => deserializeField(schema, fieldName, fieldType[0], reader));\n            }\n        }\n        if (fieldType.kind === \"option\") {\n            const option = reader.readU8();\n            if (option) {\n                return deserializeField(schema, fieldName, fieldType.type, reader);\n            }\n            return undefined;\n        }\n        if (fieldType.kind === \"map\") {\n            let map = new Map();\n            const length = reader.readU32();\n            for (let i = 0; i < length; i++) {\n                const key = deserializeField(schema, fieldName, fieldType.key, reader);\n                const val = deserializeField(schema, fieldName, fieldType.value, reader);\n                map.set(key, val);\n            }\n            return map;\n        }\n        return deserializeStruct(schema, fieldType, reader);\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction deserializeStruct(schema, classType, reader) {\n    if (typeof classType.borshDeserialize === \"function\") {\n        return classType.borshDeserialize(reader);\n    }\n    const structSchema = schema.get(classType);\n    if (!structSchema) {\n        throw new BorshError(`Class ${classType.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        const result = {};\n        for (const [fieldName, fieldType] of schema.get(classType).fields) {\n            result[fieldName] = deserializeField(schema, fieldName, fieldType, reader);\n        }\n        return new classType(result);\n    }\n    if (structSchema.kind === \"enum\") {\n        const idx = reader.readU8();\n        if (idx >= structSchema.values.length) {\n            throw new BorshError(`Enum index: ${idx} is out of range`);\n        }\n        const [fieldName, fieldType] = structSchema.values[idx];\n        const fieldValue = deserializeField(schema, fieldName, fieldType, reader);\n        return new classType({ [fieldName]: fieldValue });\n    }\n    throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${classType.constructor.name}`);\n}\n/// Deserializes object from bytes using schema.\nfunction deserialize(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    const result = deserializeStruct(schema, classType, reader);\n    if (reader.offset < buffer.length) {\n        throw new BorshError(`Unexpected ${buffer.length - reader.offset} bytes after deserialized data`);\n    }\n    return result;\n}\nexports.deserialize = deserialize;\n/// Deserializes object from bytes using schema, without checking the length read\nfunction deserializeUnchecked(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    return deserializeStruct(schema, classType, reader);\n}\nexports.deserializeUnchecked = deserializeUnchecked;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/lib/index.js\n");

/***/ })

};
;