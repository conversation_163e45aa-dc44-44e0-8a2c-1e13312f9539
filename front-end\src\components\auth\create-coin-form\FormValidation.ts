import {
  validateNameWithMinLength,
  validateTicker,
  validateUrl,
  validateSocialHandle,
  validateImage,
  validateUserSession,
} from '../../../utils/formValidation';

export interface FormData {
  name: string;
  ticker: string;
  description: string;
  picture: File | null;
  telegram: string;
  website: string;
  twitter: string;
  category: string;
}

export interface FormErrors {
  name: string;
  ticker: string;
  description: string;
  picture: string;
  telegram: string;
  website: string;
  twitter: string;
  category: string;
  api: string;
}

export const validateName = (name: string): string => {
  return validateNameWithMinLength(name, 3);
};

export const validateWebsite = (website: string): string => {
  return validateUrl(website, 'website URL');
};

export const validateSocial = (handle: string, platform: string): string => {
  return validateSocialHandle(handle, platform);
};

const validateCategory = (category: string): string => {
  if (!category || category.trim() === '') {
    return 'Please select a category';
  }
  return '';
};

export const validateForm = (
  formData: FormData,
  userId: number
): FormErrors => {
  return {
    name: validateName(formData.name),
    ticker: validateTicker(formData.ticker),
    description: '',
    picture: validateImage(formData.picture, 5),
    telegram: validateSocial(formData.telegram, 'telegram'),
    website: validateWebsite(formData.website),
    twitter: validateSocial(formData.twitter, 'twitter'),
    category: validateCategory(formData.category),
    api: validateUserSession(),
  };
};

// Re-export the shared functions for backward compatibility
export { validateTicker, validateImage };
