"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_AnimatedWrapper_tsx"],{

/***/ "(app-pages-browser)/./src/components/ui/AnimatedWrapper.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/AnimatedWrapper.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedWrapper: () => (/* binding */ AnimatedWrapper),\n/* harmony export */   PageTransition: () => (/* binding */ PageTransition),\n/* harmony export */   StaggeredGrid: () => (/* binding */ StaggeredGrid),\n/* harmony export */   StaggeredItem: () => (/* binding */ StaggeredItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* __next_internal_client_entry_do_not_use__ AnimatedWrapper,StaggeredGrid,StaggeredItem,PageTransition auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n// Custom hook for intersection observer\nconst useIntersectionObserver = function() {\n    let threshold = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0.1, triggerOnce = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasTriggered, setHasTriggered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleIntersection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useIntersectionObserver.useCallback[handleIntersection]\": (entries)=>{\n            const [entry] = entries;\n            if (entry.isIntersecting) {\n                setIsVisible(true);\n                if (triggerOnce) {\n                    setHasTriggered(true);\n                }\n            } else if (!triggerOnce) {\n                setIsVisible(false);\n            }\n        }\n    }[\"useIntersectionObserver.useCallback[handleIntersection]\"], [\n        triggerOnce\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useIntersectionObserver.useEffect\": ()=>{\n            const observer = new IntersectionObserver(handleIntersection, {\n                threshold,\n                rootMargin: '50px'\n            });\n            if (ref.current) {\n                observer.observe(ref.current);\n            }\n            return ({\n                \"useIntersectionObserver.useEffect\": ()=>observer.disconnect()\n            })[\"useIntersectionObserver.useEffect\"];\n        }\n    }[\"useIntersectionObserver.useEffect\"], [\n        handleIntersection,\n        threshold\n    ]);\n    return {\n        ref,\n        isVisible: isVisible || hasTriggered\n    };\n};\n_s(useIntersectionObserver, \"BploaYliHgxe/J9fzrf41X6b3F4=\");\n// Animation variants\nconst getAnimationVariants = function(animation) {\n    let delay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    const baseTransition = {\n        delay,\n        duration: 0.6,\n        ease: 'easeOut'\n    };\n    switch(animation){\n        case 'fadeInUp':\n            return {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                        delay,\n                        duration: 0.6,\n                        ease: 'easeOut'\n                    }\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                }\n            };\n        case 'fadeIn':\n            return {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1,\n                    transition: baseTransition\n                }\n            };\n        case 'slideIn':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: -20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        case 'scaleIn':\n            return {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1,\n                    transition: baseTransition\n                }\n            };\n        case 'slideInFromLeft':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: -50\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        case 'slideInFromRight':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: 50\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        default:\n            return {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                        delay,\n                        duration: 0.6,\n                        ease: 'easeOut'\n                    }\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                }\n            };\n    }\n};\nconst AnimatedWrapper = (param)=>{\n    let { children, className = '', delay = 0, animation = 'fadeInUp', threshold = 0.1, triggerOnce = true, disabled = false, 'data-testid': dataTestId, 'aria-label': ariaLabel } = param;\n    _s1();\n    const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);\n    if (disabled) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            \"data-testid\": dataTestId,\n            \"aria-label\": ariaLabel,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        ...getAnimationVariants(animation, delay),\n        initial: \"initial\",\n        animate: isVisible ? 'animate' : 'initial',\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AnimatedWrapper, \"kQ6BV+Y12SWf1xtQm7QC+AwtlpY=\", false, function() {\n    return [\n        useIntersectionObserver\n    ];\n});\n_c = AnimatedWrapper;\nconst StaggeredGrid = (param)=>{\n    let { children, className = '', staggerDelay = 0.1, threshold = 0.1, triggerOnce = true, 'data-testid': dataTestId, 'aria-label': ariaLabel } = param;\n    _s2();\n    const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        variants: {\n            ..._lib_animations__WEBPACK_IMPORTED_MODULE_2__.staggerContainer,\n            animate: {\n                transition: {\n                    staggerChildren: staggerDelay,\n                    delayChildren: 0.1\n                }\n            }\n        },\n        initial: \"initial\",\n        animate: isVisible ? 'animate' : 'initial',\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(StaggeredGrid, \"kQ6BV+Y12SWf1xtQm7QC+AwtlpY=\", false, function() {\n    return [\n        useIntersectionObserver\n    ];\n});\n_c1 = StaggeredGrid;\nconst StaggeredItem = (param)=>{\n    let { children, className = '', index = 0, 'data-testid': dataTestId, 'aria-label': ariaLabel } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                delay: index * 0.1,\n                duration: 0.4,\n                ease: [\n                    0.4,\n                    0.0,\n                    0.2,\n                    1\n                ]\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: -20,\n            transition: {\n                duration: 0.2,\n                ease: [\n                    0.4,\n                    0.0,\n                    1,\n                    1\n                ]\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = StaggeredItem;\nconst PageTransition = (param)=>{\n    let { children, className = '', duration = 0.4, 'data-testid': dataTestId, 'aria-label': ariaLabel } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: className,\n            \"data-testid\": dataTestId,\n            \"aria-label\": ariaLabel,\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -10\n            },\n            transition: {\n                duration,\n                ease: 'easeOut'\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n_c3 = PageTransition;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AnimatedWrapper\");\n$RefreshReg$(_c1, \"StaggeredGrid\");\n$RefreshReg$(_c2, \"StaggeredItem\");\n$RefreshReg$(_c3, \"PageTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/AnimatedWrapper.tsx\n"));

/***/ })

}]);