###############################################################################
#                             .env.example FILE                              #
#        Configuration Template for Solana.js-based Application              #
#             Copy this file to `.env.local` and fill in values              #
###############################################################################

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION
# -----------------------------------------------------------------------------

# Database host for the application (usually "localhost" for local dev)
# Example: localhost, 127.0.0.1, or an IP address.
DATABASE_HOST=localhost

# Database dialect (MySQL or other supported DBMS)
# Example: mysql, postgres, sqlite, etc.
DATABASE_DIALECT=mysql

# The name of your database to be used by the application
# Example: funhi
DATABASE_NAME=funhi

# The username for connecting to the database
# Example: root (or another DB user)
DATABASE_USERNAME=root

# The password for connecting to the database (leave empty if no password)
# Example: your-db-password-here
DATABASE_PASSWORD=

# The port on which the database service is running
# Example: 3306 for MySQL, 5432 for PostgreSQL, etc.
DATABASE_PORT=3306

# Maximum number of connections allowed for the database
# Example: 10 (adjust as necessary based on usage)
MYSQL_MAX_CONNECTION=10

# -----------------------------------------------------------------------------
# SERVER CONFIGURATION
# -----------------------------------------------------------------------------

# Port where your app will run locally
# Example: 8081
PORT=8081

# Maximum number of items per page (for pagination)
# Example: 15
MAX_PAGE=15

# JWT Secret Key for authenticating users
# Example: random string used as a secret
JWT_SECRET=

# -----------------------------------------------------------------------------
# HELIUS API CONFIGURATION
# -----------------------------------------------------------------------------

# Helius API Key for interacting with Solana blockchain
# Example: your-helius-api-key-here
HELIUS_API_KEY=

# -----------------------------------------------------------------------------
# SOLANA NETWORK CONFIGURATION
# -----------------------------------------------------------------------------

# The URL of the Solana network you're connecting to (Devnet, Mainnet, etc.)
# Example: https://api.devnet.solana.com (for development)
#        - https://api.mainnet-beta.solana.com (for production)
CONNECTIONURL=

# The Solana RPC URL for client-side interaction (could be the same as the network URL)
# Example: https://api.devnet.solana.com (for development)
#        - https://api.mainnet-beta.solana.com (for production)
NEXT_PUBLIC_RPC=

# -----------------------------------------------------------------------------
# WALLET CONFIGURATION
# -----------------------------------------------------------------------------

# Admin's private key (must be kept secure, avoid committing this in production)
# Example: Base58 encoded private key string for the admin wallet
ADMIN_PRIVATE_KEY=

# Wallet address used for airdrops to users (Base58 encoded private key)
# Example: Base58 encoded private key for the airdrop wallet
AIRDROP_WALLET_PRIVATE_KEY=

# Flag to indicate whether the app is in production or development mode
# Acceptable values: "true" or "false"
# Set "true" when deploying to production and "false" when in development.
ISLIVE="false"

###############################################################################
#                            SETUP INSTRUCTIONS                               #
###############################################################################

# 1. Copy this file to `.env.local` in the root of your project:
#      cp .env.example .env.local

# 2. Replace all placeholder values (e.g., 'your-...') with real ones.

# -----------------------------------------------------------------------------
# DATABASE SETUP:
# -----------------------------------------------------------------------------
# - Make sure your MySQL database is running and accessible from your application.
# - Replace the database credentials (username, password, database name, etc.) 
#   with the actual values used for your environment.

# -----------------------------------------------------------------------------
# JWT_SECRET SETUP:
# -----------------------------------------------------------------------------
# - The JWT_SECRET is used to sign and verify tokens. You should replace this with
#   a strong secret key for your production environment.

# -----------------------------------------------------------------------------
# Helius API KEY SETUP:
# -----------------------------------------------------------------------------
# - Get your Helius API key from the Helius dashboard and add it to the `HELIUS_API_KEY`.

# -----------------------------------------------------------------------------
# SOLANA CONFIGURATION:
# -----------------------------------------------------------------------------
# - The `CONNECTIONURL` is the RPC endpoint you’ll use to connect to the Solana network. 
#   If you’re working with Devnet for testing, use the Devnet URL. 
#   For production, switch to Mainnet.
# - The `NEXT_PUBLIC_RPC` is used on the client side, and it should match the `CONNECTIONURL` 
#   for the network you’re using.

# -----------------------------------------------------------------------------
# WALLET CONFIGURATION:
# -----------------------------------------------------------------------------
# - Make sure your wallet private keys (e.g., `ADMIN_PRIVATE_KEY` and `AIRDROP_WALLET_PRIVATE_KEY`)
#   are securely stored and are not exposed in production. Never commit these keys to version control.
# - Use the Admin's private key for signing sensitive operations and the Airdrop wallet for token distributions.

# -----------------------------------------------------------------------------
# 3. Never commit `.env.local` to version control! Add it to your `.gitignore`.

# 4. In case of production deployment, ensure you replace all development credentials with live ones.
#    For example, use the production MySQL database credentials and the production Solana RPC URL.

# End of .env.example
