"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_header_Notifications_index_tsx"],{

/***/ "(app-pages-browser)/./src/components/shared/header/Notifications/index.tsx":
/*!**************************************************************!*\
  !*** ./src/components/shared/header/Notifications/index.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst NotificationModal = (param)=>{\n    let { onClose, onOpenChat } = param;\n    _s();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_5__.H)();\n    // Helper function to determine if a notification is trade-related (same as NotificationBell)\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { setUnreadSystemNotificationsCount } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationModal.useEffect\": ()=>{\n            const fetchNotifications = {\n                \"NotificationModal.useEffect.fetchNotifications\": async ()=>{\n                    if (!(user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"No user ID available\");\n                        return;\n                    }\n                    try {\n                        console.log(\"Fetching notifications for user:\", user.id);\n                        // Fetch all notifications using the same API as NotificationBell\n                        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                            page: 1,\n                            pageSize: 50\n                        });\n                        if (response.status === 200) {\n                            const allNotifications = response.data.notifications;\n                            console.log(\"All notifications fetched:\", allNotifications);\n                            // Separate system and trade notifications using the same logic as NotificationBell\n                            const systemNots = [];\n                            const tradeNots = [];\n                            allNotifications.forEach({\n                                \"NotificationModal.useEffect.fetchNotifications\": (notification)=>{\n                                    if (isTradeNotification(notification)) {\n                                        tradeNots.push(notification);\n                                    } else {\n                                        systemNots.push(notification);\n                                    }\n                                }\n                            }[\"NotificationModal.useEffect.fetchNotifications\"]);\n                            console.log(\"Categorized notifications:\", {\n                                system: systemNots.length,\n                                trade: tradeNots.length\n                            });\n                            setSystemNotifications(systemNots);\n                            setTradeNotifications(tradeNots);\n                            setUnreadSystemNotificationsCount(systemNots.filter({\n                                \"NotificationModal.useEffect.fetchNotifications\": (n)=>!n.isRead\n                            }[\"NotificationModal.useEffect.fetchNotifications\"]).length);\n                        }\n                    } catch (e) {\n                        console.error(\"Error fetching notifications:\", e);\n                    }\n                }\n            }[\"NotificationModal.useEffect.fetchNotifications\"];\n            fetchNotifications();\n        }\n    }[\"NotificationModal.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationModal.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationModal.useEffect.handleClickOutside\": (event)=>{\n                    if (modalRef.current && !modalRef.current.contains(event.target)) {\n                        onClose();\n                    }\n                }\n            }[\"NotificationModal.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationModal.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"NotificationModal.useEffect\"];\n        }\n    }[\"NotificationModal.useEffect\"], [\n        onClose\n    ]);\n    const getTypeStyles = (type)=>{\n        switch(type){\n            case 'buy':\n                return {\n                    bg: 'bg-green-50 hover:bg-green-100',\n                    border: 'border-l-4 border-l-green-500',\n                    icon: 'text-green-600',\n                    typeText: 'text-green-700 bg-green-100'\n                };\n            case 'sell':\n                return {\n                    bg: 'bg-red-50 hover:bg-red-100',\n                    border: 'border-l-4 border-l-red-500',\n                    icon: 'text-red-600',\n                    typeText: 'text-red-700 bg-red-100'\n                };\n            case 'airdrop':\n                return {\n                    bg: 'bg-blue-50 hover:bg-blue-100',\n                    border: 'border-l-4 border-l-blue-500',\n                    icon: 'text-blue-600',\n                    typeText: 'text-blue-700 bg-blue-100'\n                };\n            case 'burn':\n                return {\n                    bg: 'bg-orange-50 hover:bg-orange-100',\n                    border: 'border-l-4 border-l-orange-500',\n                    icon: 'text-orange-600',\n                    typeText: 'text-orange-700 bg-orange-100'\n                };\n            case 'general':\n                return {\n                    bg: 'bg-gray-50 hover:bg-gray-100',\n                    border: 'border-l-4 border-l-gray-500',\n                    icon: 'text-gray-600',\n                    typeText: 'text-gray-700 bg-gray-100'\n                };\n            default:\n                return {\n                    bg: 'bg-gray-50 hover:bg-gray-100',\n                    border: 'border-l-4 border-l-gray-500',\n                    icon: 'text-gray-600',\n                    typeText: 'text-gray-700 bg-gray-100'\n                };\n        }\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Helper to get notifications for the current tab\n    let notifications = selectedTab === 'trade' ? tradeNotifications : systemNotifications;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-start justify-end bg-black/20 backdrop-blur-sm z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col bg-white rounded-xl shadow-2xl w-[420px] max-h-[600px] mt-20 mr-6 border border-gray-200 overflow-hidden\",\n            ref: modalRef,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-[#FF6600] to-[#FF8533]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-white font-semibold text-lg\",\n                                    children: t('navigation.notifications')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(selectedTab === 'trade' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'),\n                                            onClick: ()=>setSelectedTab('trade'),\n                                            children: t('notifications.trade')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(selectedTab === 'system' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'),\n                                            onClick: ()=>setSelectedTab('system'),\n                                            children: t('notifications.system')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white group-hover:scale-110 transition-transform duration-200\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto max-h-[500px]\",\n                    children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center p-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-gray-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-900 mb-2\",\n                                children: t('notifications.noNotifications')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 text-center\",\n                                children: t('notifications.allCaughtUp')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-3\",\n                        children: notifications.map((notification, index)=>{\n                            // Map notification types to Message types for styling\n                            const messageType = notification.type === 'chat_message' ? 'general' : [\n                                'perk_purchased',\n                                'perk_sold'\n                            ].includes(notification.type) ? 'buy' : 'general';\n                            const styles = getTypeStyles(messageType);\n                            // Get icon based on notification type\n                            const getNotificationIcon = (type)=>{\n                                switch(type){\n                                    case 'chat_message':\n                                        return '💬';\n                                    case 'perk_purchased':\n                                        return '🛒';\n                                    case 'perk_sold':\n                                        return '💰';\n                                    case 'escrow_created':\n                                        return '🔒';\n                                    case 'escrow_released':\n                                        return '✅';\n                                    case 'system_announcement':\n                                        return '📢';\n                                    case 'perk_created':\n                                        return '🆕';\n                                    case 'token_created':\n                                        return '🪙';\n                                    case 'moderator_added':\n                                        return '👮';\n                                    default:\n                                        return '📋';\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(styles.bg, \" \").concat(styles.border, \" p-4 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-[1.02] cursor-pointer group border border-gray-100\"),\n                                style: {\n                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                },\n                                onClick: ()=>{\n                                    var _notification_data;\n                                    console.log('Notification clicked:', notification);\n                                    // Handle chat notifications using the proper data structure\n                                    if (notification.type === 'chat_message' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId)) {\n                                        console.log('Opening chat with chatRoomId:', notification.data.chatRoomId);\n                                        const receiverId = notification.data.senderId; // The sender becomes the receiver for the chat\n                                        onOpenChat(notification.data.chatRoomId, receiverId);\n                                        onClose();\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-full flex items-center justify-center text-xl \".concat(styles.icon, \" bg-white/60 group-hover:bg-white/80 transition-all duration-200\"),\n                                            children: getNotificationIcon(notification.type)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900 font-medium text-sm leading-relaxed group-hover:text-gray-800 transition-colors duration-200\",\n                                                    children: notification.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-xs mt-1\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(styles.typeText, \" capitalize\"),\n                                                            children: notification.type.replace('_', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                            children: formatTimeAgo(new Date(notification.createdAt))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#FF6600] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 41\n                                }, undefined)\n                            }, notification.id || \"notif-fallback-\".concat(index), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 37\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-gray-100 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full py-2 px-4 bg-[#FF6600] hover:bg-[#E55A00] text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]\",\n                        children: t('notifications.markAllAsRead')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n            lineNumber: 199,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\Notifications\\\\index.tsx\",\n        lineNumber: 198,\n        columnNumber: 9\n    }, undefined);\n};\n_s(NotificationModal, \"8wK9gt3fEk2ZmziMOAo1rbgyEBA=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_5__.H,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext\n    ];\n});\n_c = NotificationModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationModal);\nvar _c;\n$RefreshReg$(_c, \"NotificationModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/header/Notifications/index.tsx\n"));

/***/ })

}]);