'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Volume2,
  BarChart3,
  User,
} from 'lucide-react';
import React, { useMemo, useState } from 'react';

import { StatsSkeleton } from '@/components/ui/LoadingSkeletons';
import { fadeInUp, staggerContainer } from '@/lib/animations';
import { useTranslation } from '@/hooks/useTranslation';

interface TokenStats {
  className?: string;
  symbols?: string[];
  creator?: string;
  tokenImage?: string;
  stats: {
    usd: number;
    min24h: number;
    max24h: number;
    volume24h: number;
    return24h: number;
  };
  loading?: boolean;
}

const TokenStats: React.FC<TokenStats> = ({
  className,
  symbols = ['ANAS/USDTaaa'],
  creator = 'By Anas Sharif',
  tokenImage = '/images/placeholder.png',
  stats,
  loading = false,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedSymbol, setSelectedSymbol] = useState(symbols[0]);
  const { t } = useTranslation();

  const formatPercentage = (value: number): string => {
    return value.toFixed(2) + '%';
  };

  const formatVolume: (value: number) => string = (value) => {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K';
    }
    return value.toString();
  };

  const statsDisplay = useMemo(() => [
    {
      label: t('tokenStats.marketValue'),
      value: stats.usd.toFixed(4),
      icon: DollarSign,
      color: 'text-[#FF6600]',
      bgColor: 'bg-[#FFF7F1]',
      borderColor: 'border-[#F58A38]/30',
    },
    {
      label: t('tokenStats.low24h'),
      value: stats.usd.toFixed(4),
      icon: TrendingDown,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
    },
    {
      label: t('tokenStats.high24h'),
      value: stats.usd.toFixed(4),
      icon: TrendingUp,
      color: 'text-[#F58A38]',
      bgColor: 'bg-[#FFF7F1]',
      borderColor: 'border-[#F58A38]/30',
    },
    {
      label: t('tokenStats.volume'),
      value: formatVolume(stats.volume24h),
      icon: Volume2,
      color: 'text-[#FF6600]',
      bgColor: 'bg-[#FFF7F1]',
      borderColor: 'border-[#F58A38]/30',
    },
    {
      label: t('tokenStats.return24h'),
      value: formatPercentage(stats.return24h),
      icon: stats.return24h >= 0 ? TrendingUp : TrendingDown,
      color: stats.return24h >= 0 ? 'text-[#F58A38]' : 'text-red-600',
      bgColor: stats.return24h >= 0 ? 'bg-[#FFF7F1]' : 'bg-red-50',
      borderColor:
        stats.return24h >= 0 ? 'border-[#F58A38]/30' : 'border-red-200',
    },
  ], [stats.usd, stats.volume24h, stats.return24h]);

  const statsValueMaxLength = useMemo(() => {
    return Math.max(...statsDisplay.map((stat) => stat.value.length));
  }, [statsDisplay]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleSelectSymbol = (newSymbol: string) => {
    setSelectedSymbol(newSymbol);
    setIsDropdownOpen(false);
  };

  if (loading) {
    return <StatsSkeleton />;
  }

  return (
    <motion.div
      className={`w-full bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden ${className}`}
      {...fadeInUp}
    >
      {/* Header Section */}
      <div className="bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          {/* Token Info */}
          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="relative">
              <motion.div
                className="h-12 w-12 rounded-xl bg-gradient-to-br from-[#FF6600] to-[#F58A38] flex items-center justify-center shadow-lg overflow-hidden"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              >
                <BarChart3 size={24} className="text-white" />
              </motion.div>
            </div>

            <div>
              <div className="flex items-center space-x-2">
                <motion.h3
                  className="text-xl font-bold text-gray-900"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  {selectedSymbol}
                </motion.h3>

                <div className="relative">
                  <motion.button
                    className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-lg hover:bg-gray-100"
                    onClick={toggleDropdown}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown size={18} />
                    </motion.div>
                  </motion.button>

                  <AnimatePresence>
                    {isDropdownOpen && (
                      <motion.div
                        className="absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-xl z-50 border border-gray-200 overflow-hidden"
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                      >
                        {symbols.map((symbol, index) => (
                          <motion.div
                            key={index}
                            className="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => handleSelectSymbol(symbol)}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            whileHover={{ scale: 1.02 }}
                          >
                            <div className="w-8 h-8 rounded-lg bg-gray-100 mr-3 flex items-center justify-center">
                              <BarChart3 size={16} className="text-gray-600" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {symbol}
                              </div>
                              <div className="text-sm text-gray-500">
                                {t('tokenStats.tradingPair')}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              <motion.div
                className="flex items-center space-x-2 mt-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                <User size={14} className="text-gray-400" />
                <p className="text-sm text-gray-600">{creator}</p>
              </motion.div>
            </div>
          </motion.div>

          {/* Quick Stats Badge */}
          <motion.div
            className="flex items-center space-x-2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="px-3 py-1 bg-[#FFF7F1] rounded-full">
              <span className="text-sm font-medium text-[#FF6600]">
                {t('tokenStats.activeTrading')}
              </span>
            </div>
            <div className="w-2 h-2 bg-[#F58A38] rounded-full animate-pulse"></div>
          </motion.div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="p-6">
        <motion.div
          className={`grid gap-4 ${
            statsValueMaxLength > 10
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'
              : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5'
          }`}
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {statsDisplay.map((stat, index) => {
            const IconComponent = stat.icon;

            return (
              <motion.div
                key={index}
                className={`relative group ${stat.bgColor} ${stat.borderColor} border rounded-xl p-4 transition-all duration-300 hover:shadow-md cursor-pointer`}
                variants={{
                  initial: { opacity: 0, y: 20 },
                  animate: {
                    opacity: 1,
                    y: 0,
                    transition: { delay: 0.5 + index * 0.1 },
                  },
                }}
                whileHover={{
                  scale: 1.03,
                  y: -2,
                }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div
                    className={`w-8 h-8 ${stat.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}
                  >
                    <IconComponent size={16} className={stat.color} />
                  </div>
                  <div className="w-2 h-2 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </div>

                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                    {stat.label}
                  </p>
                  <motion.p
                    className={`text-lg font-bold ${stat.color} group-hover:scale-105 transition-transform duration-200`}
                    whileHover={{ scale: 1.05 }}
                  >
                    {stat.value}
                  </motion.p>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-white/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default TokenStats;
