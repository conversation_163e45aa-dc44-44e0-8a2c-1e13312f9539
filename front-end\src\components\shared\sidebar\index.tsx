import React from 'react';
import { useTranslation } from '../../../hooks/useTranslation';

interface SettingsSidebarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
}

const SettingsSidebar: React.FC<SettingsSidebarProps> = ({ 
  activeSection, 
  setActiveSection 
}) => {
  const { t } = useTranslation();
  const navItems = [
    { id: 'profile', label: t('sidebar.profileDetails') },
    { id: 'security', label: t('sidebar.security') },
  ];

  return (
    <div className="bg-white rounded-[10px] border border-[#D9E1E7] w-full">
      <ul className="divide-y-3 divide-[#D9E1E7]">
        {navItems.map((item) => (
          <li key={item.id}>
            <button
              className={`w-full text-left py-2 px-4 rounded font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] h-[78px] flex items-center ${
                activeSection === item.id
                  ? 'text-[#17181A]'
                  : 'text-[#809FB8] hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection(item.id)}
            >
              {item.label}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SettingsSidebar; 