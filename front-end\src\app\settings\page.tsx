"use client";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { ROUTES } from "@/constants";
import { updateProfile } from "../../axios/requests";
import { useAppContext } from "../../contexts/AppContext";
import { useTranslation } from '@/hooks/useTranslation';
import { showErrorToast, showSuccessToast, TOAST_MESSAGES, createAPIError } from "@/utils/errorHandling";
import dynamic from "next/dynamic";

const PasswordSection = dynamic(
  () =>
    import("@/components/shared/password").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const ProfileDetailsSection = dynamic(
  () =>
    import("@/components/shared/profile-details").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const SecuritySection = dynamic(
  () =>
    import("@/components/shared/security").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const TokenSettingsSection = dynamic(
  () =>
    import("@/components/shared/token-settings").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const VerifyAccountSection = dynamic(
  () =>
    import("@/components/shared/verify-account").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

const SettingsSidebar = dynamic(
  () =>
    import("@/components/shared/sidebar").then((mod) => ({
      default: mod.default,
    })),
  {
    loading: () => (
      <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>
    ),
  }
);

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState("profile");
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formFail, setFormFail] = useState(false);
  const [responseData, setResponseData] = useState("");
  const [isValid, setIsValid] = useState(true);
  const router = useRouter();

  const { state, updateUserBo  } = useAppContext();
  const { t } = useTranslation();

  const [userID, setUserID] = useState(0);
  const [userBo, setUserBo] = useState<any>(null);

  const [profileData, setProfileData] = useState({
    value: {
      name: "",
      email: "",
      bio: "",
    },
    errors: {
      name: "",
      email: "",
      bio: "",
    },
  });

  const [tokenSettings, setTokenSettings] = useState({
    value: {
      website: "",
      tiktok: "",
      facebook: "",
      instagram: "",
    },
    errors: {
      website: "",
      tiktok: "",
      facebook: "",
      instagram: "",
    },
  });

  const [passwordData, setPasswordData] = useState({
    value: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    errors: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    //console.log('Settings page - AppContext state:', state);

    if (!state?.userBo) {
      console.log("AppContext state not ready yet");

      if (!localStorage.getItem("token")) {
        router.push(ROUTES.HOME);
        return;
      } else {
      }
      return;
    }
    try {
      if (state.userBo) {
        const parsed =
          typeof state.userBo === "string"
            ? JSON.parse(state.userBo)
            : state.userBo;
        setUserBo(parsed);
        setUserID(parsed.id);
        // console.log("UserID loaded:", parsed.id);
        // console.log("User 2FA status:", {
        //   twoFactorStatus: parsed.twoFactorStatus,
        //   twoFactorData: parsed.twoFactorData
        // });

        setProfileData({
          value: {
            name: parsed.name ?? "",
            email: parsed.email ?? "",
            bio: parsed.bio ?? "",
          },
          errors: profileData.errors,
        });
        setTokenSettings({
          value: {
            website: parsed.website ?? "",
            tiktok: parsed.tiktok ?? "",
            facebook: parsed.facebook ?? "",
            instagram: parsed.instagram ?? "",
          },
          errors: tokenSettings.errors,
        });
      }
    } catch (err) {
      console.error("Error parsing userBo:", err);
    }
  }, [state.userBo]); // will run when context updates

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!isValid) {
      return;
    }

    setFormSubmitting(true);

    const requestData = {
      userId: userID,
      name: profileData.value.name,
      bio: profileData.value.bio,
      website: tokenSettings.value.website,
      tiktok: tokenSettings.value.tiktok,
      facebook: tokenSettings.value.facebook,
      instagram: tokenSettings.value.instagram,
      password: passwordData.value.oldPassword || undefined,
      newPassword: passwordData.value.newPassword || undefined,
    };

    try {
      const dataIs = await updateProfile(requestData);

      if (dataIs?.status === 200) {
        setFormSuccess(true);
        setResponseData(dataIs.message || TOAST_MESSAGES.PROFILE.UPDATE_SUCCESS);
        showSuccessToast(TOAST_MESSAGES.PROFILE.UPDATE_SUCCESS);
        updateUserBo(dataIs?.data);
        setTimeout(() => setFormSuccess(false), 3000);
      } else {
        console.log(dataIs.message);
        const errorMessage = dataIs?.message || TOAST_MESSAGES.PROFILE.UPDATE_FAILED;
        showErrorToast(createAPIError(errorMessage, dataIs?.status));
        setFormFail(true);
        setResponseData(errorMessage);
        setTimeout(() => setFormFail(false), 3000);
      }
    } catch (error: any) {
      console.error("Update profile failed:", error);
      setFormFail(true);
      
      // Handle different types of errors with specific messages
      let errorMessage = TOAST_MESSAGES.PROFILE.UPDATE_FAILED;
      
      if (error?.response?.status === 401) {
        errorMessage = TOAST_MESSAGES.AUTH.LOGIN_REQUIRED;
      } else if (error?.response?.status === 400) {
        errorMessage = error?.response?.data?.message || TOAST_MESSAGES.FORM.VALIDATION_FAILED;
      } else if (error?.response?.status >= 500) {
        errorMessage = TOAST_MESSAGES.NETWORK.SERVER_ERROR;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      showErrorToast(createAPIError(errorMessage, error?.response?.status));
      setResponseData(errorMessage);
      setTimeout(() => setFormFail(false), 3000);
    } finally {
      setFormSubmitting(false);
    }
  };

  useEffect(() => {
    const { name, email, bio } = profileData.errors;
    const { website, tiktok, facebook, instagram } = tokenSettings.errors;
    const { oldPassword, newPassword, confirmPassword } = passwordData.errors;

    if (
      name ||
      email ||
      bio ||
      website ||
      tiktok ||
      facebook ||
      instagram ||
      oldPassword ||
      newPassword ||
      confirmPassword
    ) {
      setIsValid(false);
    } else {
      setIsValid(true);
    }
  }, [profileData.errors, tokenSettings.errors, passwordData.errors]);

  return (
    <div className="px-6 py-8 flex flex-col md:flex-row 2xl:gap-36 max-2xl:justify-between">
      <div className="w-full md:w-[18.66%] md:max-2xl:w-[30%] mb-12">
        <SettingsSidebar
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />
      </div>

      <div className="w-full md:w-[68%] xl:w-[55.42%]">
        <h1 className="font-['IBM_Plex_Sans'] font-semibold text-[48px] leading-[48px] md:leading-[36px] text-[#17181A] mb-8">{t('navigation.settings')}</h1>
        <h1 className="font-['IBM_Plex_Sans'] font-semibold text-[48px] leading-[48px] md:leading-[36px] text-[#17181A] mb-8">
          Profiless Settings
        </h1>

        <form onSubmit={handleSubmit}>
          <div className="space-y-8">
            <ProfileDetailsSection
              profileData={profileData}
              setProfileData={setProfileData}
            />
            <hr className="my-8 border-[#000000]" />
            <TokenSettingsSection
              tokenSettings={tokenSettings}
              setTokenSettings={setTokenSettings}
            />
            <hr className="my-8 border-[#000000]" />
            <VerifyAccountSection />
            <hr className="my-8 border-[1px] border-[#000000]" />
            <SecuritySection />
            <PasswordSection
              passwordData={passwordData}
              setPasswordData={setPasswordData}
            />
            <div className="mt-8 flex items-center flex-col md:flex-row">
              <button
                type="submit"
                disabled={formSubmitting || !isValid}
                className="bg-black text-white px-[50px] py-[12px] rounded-[10px] font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2 min-w-[200px]"
              >
                {formSubmitting ? t('ui.saving') : t('ui.saveAndContinue')}
                {formSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  "Save and continue"
                )}
              </button>

              {formSuccess && (
                <span className="ml-4 text-green-500 font-['IBM_Plex_Sans'] font-semibold flex items-center gap-2">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  {responseData}
                </span>
              )}
              {formFail && (
                <span className="ml-4 text-red-500 font-['IBM_Plex_Sans'] font-semibold flex items-center gap-2">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  {responseData}
                </span>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
