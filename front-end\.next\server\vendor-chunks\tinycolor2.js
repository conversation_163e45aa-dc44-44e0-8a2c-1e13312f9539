"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tinycolor2";
exports.ids = ["vendor-chunks/tinycolor2"];
exports.modules = {

/***/ "(ssr)/./node_modules/tinycolor2/esm/tinycolor.js":
/*!**************************************************!*\
  !*** ./node_modules/tinycolor2/esm/tinycolor.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tinycolor)\n/* harmony export */ });\n// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// Brian Grinstead, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tinycolor2/esm/tinycolor.js\n");

/***/ })

};
;