// require("ts-node/register");
// import * as anchor from "@coral-xyz/anchor";
// import { PublicKey, TransactionInstruction } from "@solana/web3.js";
// import { program } from "./setup";

// export const initialize = async (
//   initialVirtualTokenReserves: anchor.BN,
//   initialVirtualSolReserves: anchor.BN,
//   initialRealTokenReserves: anchor.BN,
//   tokenTotalSupply: anchor.BN,
//   tradingFeeBps: anchor.BN,
//   feeRecipient: PublicKey,
//   firstBuyFeeSol: anchor.BN,
//   graduationThreshold: anchor.BN
// ): Promise<TransactionInstruction> => {
//   console.log({
//     initialVirtualTokenReserves,
//     initialVirtualSolReserves,
//     initialRealTokenReserves,
//     tokenTotalSupply,
//     tradingFeeBps,
//     feeRecipient,
//     firstBuyFeeSol,
//     graduationThreshold,
//   });
//   const ix = await program.methods
//     .initialize(
//       initialVirtualTokenReserves,
//       initialVirtualSolReserves,
//       initialRealTokenReserves,
//       tokenTotalSupply,
//       tradingFeeBps,
//       feeRecipient,
//       firstBuyFeeSol,
//       graduationThreshold
//     )
//     .instruction();

//   return ix;
// };


require("ts-node/register");
import * as anchor from "@coral-xyz/anchor";
import { PublicKey, TransactionInstruction } from "@solana/web3.js";
import { program } from "./setup";

export const initialize = async (
  initialVirtualTokenReserves: anchor.BN,
  initialVirtualSolReserves: anchor.BN,
  initialRealTokenReserves: anchor.BN,
  tokenTotalSupply: anchor.BN,
  tradingFeeBps: anchor.BN,
  feeRecipient: PublicKey,
  firstBuyFeeSol: anchor.BN,
  graduationThreshold: anchor.BN
): Promise<TransactionInstruction> => {
  console.log({
    initialVirtualTokenReserves,
    initialVirtualSolReserves,
    initialRealTokenReserves,
    tokenTotalSupply,
    tradingFeeBps,
    feeRecipient,
    firstBuyFeeSol,
    graduationThreshold,
  });
  const ix = await program.methods
    .initialize(
      initialVirtualTokenReserves,
      initialVirtualSolReserves,
      initialRealTokenReserves,
      tokenTotalSupply,
      tradingFeeBps,
      feeRecipient,
      firstBuyFeeSol,
      graduationThreshold
    )
    .instruction();

  return ix;
};

