"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_auth_two-factor-auth-modal_tsx";
exports.ids = ["_ssr_src_components_auth_two-factor-auth-modal_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/auth/two-factor-auth-modal.tsx":
/*!*******************************************************!*\
  !*** ./src/components/auth/two-factor-auth-modal.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(ssr)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppContext */ \"(ssr)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @privy-io/react-auth */ \"(ssr)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/errorHandling */ \"(ssr)/./src/utils/errorHandling.ts\");\n\n\n\n\n\n\n\n\nconst TwoFactorAuthModal = ({ onClose, userId })=>{\n    const [verificationCode, setVerificationCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fieldError, setFieldError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, logout } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    const { logout: privyLogout } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_5__.H)();\n    // Validation function\n    const validateCode = (code)=>{\n        if (!code.trim()) {\n            return {\n                isValid: false,\n                error: 'Verification code is required'\n            };\n        }\n        if (code.length !== 6) {\n            return {\n                isValid: false,\n                error: 'Code must be exactly 6 digits'\n            };\n        }\n        if (!/^\\d{6}$/.test(code)) {\n            return {\n                isValid: false,\n                error: 'Code must contain only numbers'\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    // Handle input change with validation\n    const handleCodeChange = (value)=>{\n        const numericValue = value.replace(/[^0-9]/g, '');\n        setVerificationCode(numericValue);\n        // Clear error when user starts typing\n        if (fieldError) {\n            setFieldError(null);\n        }\n        // Real-time validation for 6-digit requirement\n        if (numericValue.length > 6) {\n            setFieldError('Code cannot exceed 6 digits');\n        } else if (fieldError && numericValue.length === 6) {\n            setFieldError(null);\n        }\n    };\n    const cancelSubmit = ()=>{\n        privyLogout();\n        logout();\n        onClose();\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate before submission\n        const validation = validateCode(verificationCode);\n        if (!validation.isValid) {\n            setFieldError(validation.error || 'Invalid verification code');\n            return;\n        }\n        setIsLoading(true);\n        setIsValidating(true);\n        setFieldError(null);\n        try {\n            const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.verify2FALogin)({\n                userId,\n                token: verificationCode\n            });\n            if (response.status === 200) {\n                // Complete the login process with the user data and token\n                login(response.data, response.token);\n                (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.showSuccessToast)('Two-factor authentication successful!');\n                onClose();\n            } else {\n                setFieldError(response.message || 'Invalid verification code');\n                (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.showErrorToast)(response.message || 'Invalid verification code');\n            }\n        } catch (error) {\n            let errorMessage = 'Failed to verify 2FA code';\n            // Handle specific error types\n            if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.ValidationError) {\n                errorMessage = error.message;\n                setFieldError(error.message);\n            } else if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.AuthenticationError) {\n                errorMessage = 'Authentication failed. Please try again.';\n                setFieldError('Authentication failed');\n            } else if (error instanceof _utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.NetworkError) {\n                errorMessage = 'Network error. Please check your connection and try again.';\n            } else if (error.code === '2FA_LOGIN_ERROR') {\n                errorMessage = error.message || 'Invalid verification code';\n                setFieldError('Invalid verification code');\n            } else {\n                // Generic error handling\n                errorMessage = error.message || 'Failed to verify 2FA code';\n                setFieldError('Verification failed');\n            }\n            (0,_utils_errorHandling__WEBPACK_IMPORTED_MODULE_4__.showErrorToast)(errorMessage);\n        } finally{\n            setIsLoading(false);\n            setIsValidating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        exit: {\n            opacity: 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n            initial: {\n                scale: 0.9,\n                opacity: 0\n            },\n            animate: {\n                scale: 1,\n                opacity: 1\n            },\n            exit: {\n                scale: 0.9,\n                opacity: 0\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-4 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Two-Factor Authentication\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: cancelSubmit,\n                            className: \"text-gray-500 hover:text-gray-700 focus:outline-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Please enter the 6-digit verification code from your Google Authenticator app.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"verificationCode\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Verification Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"verificationCode\",\n                                            type: \"text\",\n                                            inputMode: \"numeric\",\n                                            pattern: \"[0-9]*\",\n                                            maxLength: 6,\n                                            value: verificationCode,\n                                            onChange: (e)=>handleCodeChange(e.target.value),\n                                            placeholder: \"Enter 6-digit code\",\n                                            className: `w-full p-3 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${fieldError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} ${isValidating ? 'bg-gray-50' : ''}`,\n                                            required: true,\n                                            autoFocus: true,\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isValidating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                fieldError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"text-red-500 text-sm mt-1 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1\",\n                                            children: \"⚠\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        fieldError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined),\n                                verificationCode.length === 6 && !fieldError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"text-green-500 text-sm mt-1 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Code format is valid\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: cancelSubmit,\n                                    disabled: isLoading,\n                                    className: \"mr-2 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 focus:outline-none disabled:opacity-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || verificationCode.length !== 6 || !!fieldError,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'Verifying...' : 'Verify'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\auth\\\\two-factor-auth-modal.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwoFactorAuthModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/two-factor-auth-modal.tsx\n");

/***/ })

};
;