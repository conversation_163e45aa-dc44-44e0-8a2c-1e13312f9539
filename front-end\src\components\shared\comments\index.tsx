import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import { showErrorToast, showSuccessToast, TOAST_MESSAGES, createAPIError } from '@/utils/errorHandling';

import { postTokenComment } from '../../../axios/requests';
import { useAppContext } from '../../../contexts/AppContext';
import { useTranslation } from '../../../hooks/useTranslation';

// Dynamically import heavy components
const NoCommentsEmpty = dynamic(() => import('@/components/ui/EmptyStates').then(mod => ({ default: mod.NoCommentsEmpty })), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>,
});

const CommentsSkeleton = dynamic(() => import('@/components/ui/LoadingSkeletons').then(mod => ({ default: mod.CommentsSkeleton })), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const AboutSection = dynamic(() => import('./AboutSection').then(mod => ({ default: mod.AboutSection })), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>,
});

const CommentInput = dynamic(() => import('./CommentInput').then(mod => ({ default: mod.CommentInput })), {
  loading: () => <div className="animate-pulse h-16 bg-gray-200 rounded-lg"></div>,
});

const CommentItem = dynamic(() => import('./CommentItem').then(mod => ({ default: mod.CommentItem })), {
  loading: () => <div className="animate-pulse h-20 bg-gray-200 rounded-lg"></div>,
});

const CommentTab = dynamic(() => import('./CommentTab').then(mod => ({ default: mod.CommentTab })), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>,
});

interface Comment {
  ID: any;
  Comment: string;
  id: number;
  UserName: string;
  createdAt: string;
  comment: string;
  avatar?: string | null;
}

interface CommentsProps {
  tokenID: any;
  comments?: Comment[];
  description?: string;
  onSort?: () => void;
  onShowDetails?: (comment: Comment) => void;
  loading?: boolean;
  isLoggedIn: boolean;
  onLogin: () => void;
}

const Comments: React.FC<CommentsProps> = ({
  tokenID,
  comments = [],
  description = '',
  onSort,
  onShowDetails,
  loading = false,
  isLoggedIn,
  onLogin,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'comments' | 'about'>('comments');
  const [commentsList, setCommentsList] = useState<Comment[]>(comments);
  const { state } = useAppContext();

  useEffect(() => {
    setCommentsList(comments);
  }, [comments]);

  const handleSubmitComment = async (commentText: string) => {
    if (!state?.userBo?.id) {
      showErrorToast(TOAST_MESSAGES.AUTH.LOGIN_REQUIRED);
      return;
    }

    try {
      const response = await postTokenComment({
        userId: state.userBo.id,
        tokenId: tokenID,
        username: state.userBo.username,
        comment: commentText,
        avatar: '/images/placeholder.png',
      });
      setCommentsList(response.data);
      showSuccessToast(TOAST_MESSAGES.COMMENT.POST_SUCCESS);
      return response.data;
    } catch (error: any) {
      console.error('Post comment error:', error);
      
      // Handle different types of errors with specific messages
      let errorMessage = TOAST_MESSAGES.COMMENT.POST_FAILED;
      
      if (error?.response?.status === 401) {
        errorMessage = TOAST_MESSAGES.AUTH.LOGIN_REQUIRED;
      } else if (error?.response?.status === 400) {
        errorMessage = error?.response?.data?.message || TOAST_MESSAGES.FORM.VALIDATION_FAILED;
      } else if (error?.response?.status >= 500) {
        errorMessage = TOAST_MESSAGES.NETWORK.SERVER_ERROR;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      showErrorToast(createAPIError(errorMessage, error?.response?.status));
      throw error;
    }
  };

  const handleTabChange = (tab: 'comments' | 'about') => {
    setActiveTab(tab);
  };

  return (
    <motion.div
      className="w-full h-[500px] flex flex-col bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <CommentTab
        activeTab={activeTab}
        onTabChange={handleTabChange}
        commentCount={commentsList.length}
      />

      <AnimatePresence mode="wait">
        {activeTab === 'comments' ? (
          <motion.div
            key="comments"
            className="flex-1 bg-gray-50 p-6 overflow-y-auto"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            {loading ? (
              <CommentsSkeleton />
            ) : commentsList.length === 0 ? (
              <NoCommentsEmpty />
            ) : (
              <div className="space-y-4">
                {commentsList.map((comment) => (
                  <CommentItem
                    key={comment.ID}
                    comment={{
                      id: comment.ID ? comment.ID.toString() : 'unknown',  // Handle undefined id
                      user: {
                        name: comment.UserName ? comment.UserName: 'USER',
                        avatar: comment.avatar || '/images/placeholder.png',
                      },
                      content: comment.Comment,
                      createdAt: comment.createdAt,
                    }}
                  />
                ))}
              </div>
            )}
          </motion.div>
        ) : (
          <AboutSection description={description || t('comments.noDescription')} />
        )}
      </AnimatePresence>

      {activeTab === 'comments' && (
        <CommentInput
          isLoggedIn={isLoggedIn}
          onLogin={onLogin}
          onSubmit={handleSubmitComment}
        />
      )}
    </motion.div>
  );
};

export default Comments;
