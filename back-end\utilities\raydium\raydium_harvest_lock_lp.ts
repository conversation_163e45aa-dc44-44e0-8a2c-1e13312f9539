import {
  Connection,
  Keypair,
  <PERSON>Key,
  VersionedTransaction,
} from "@solana/web3.js";
import {
  ApiV3PoolInfoStandardItemCpmm,
  DEV_LOCK_CPMM_PROGRAM,
  DEV_LOCK_CPMM_AUTH,
  CpmmKeys,
  Raydium,
  TxVersion,
  LOCK_CPMM_PROGRAM,
  LOCK_CPMM_AUTH,
} from "@raydium-io/raydium-sdk-v2";
import { isValidCpmm } from "./utils";
import { BN } from "@coral-xyz/anchor";
import axios from "axios";
import { isValidConnection } from "../isValidConnection";

export interface NFTAsset {
  interface: string;
  id: string;
  content: {
    $schema: string;
    json_uri: string;
    files: Array<{
      uri: string;
      cdn_uri: string;
      mime: string;
    }>;
    metadata: {
      attributes: Array<{
        value: string;
        trait_type: string;
      }>;
      description: string;
      name: string;
      symbol: string;
      token_standard: string;
    };
    links: {
      external_url: string;
      image: string;
    };
  };
  authorities: Array<{
    address: string;
    scopes: string[];
  }>;
  compression: {
    eligible: boolean;
    compressed: boolean;
    data_hash: string;
    creator_hash: string;
    asset_hash: string;
    tree: string;
    seq: number;
    leaf_id: number;
  };
  grouping: Array<{
    group_key: string;
    group_value: string;
    collection_metadata: {
      name: string;
      symbol: string;
      image: string;
      description: string;
      external_url: string;
    };
  }>;
  royalty: {
    royalty_model: string;
    target: null;
    percent: number;
    basis_points: number;
    primary_sale_happened: boolean;
    locked: boolean;
  };
  creators: Array<{
    address: string;
    share: number;
    verified: boolean;
  }>;
  ownership: {
    frozen: boolean;
    delegated: boolean;
    delegate: null;
    ownership_model: string;
    owner: string;
  };
  supply: {
    print_max_supply: number;
    print_current_supply: number;
    edition_nonce: null;
  };
  mutable: boolean;
  burnt: boolean;
}

// https://www.helius.dev/docs/api-reference/das/getasset
// gets details of the nft issued by raydium when lp tokens were locked
export const getAsset = async (
  assetId: PublicKey,
  rpcEndpoint: string,
): Promise<NFTAsset> => {
  const response = await axios.post(rpcEndpoint, {
    jsonrpc: "2.0",
    id: "1",
    method: "getAsset",
    params: { id: assetId.toBase58() },
  });
  const asset = response.data.result;

  return asset;
};

export const raydiumHarvestLockLiquidity = async (
  signer: PublicKey | Keypair,
  connection: Connection,
  network: "devnet" | "mainnet",
  nftMint: PublicKey,
  poolId: PublicKey,
): Promise<VersionedTransaction> => {
  const validConnection = isValidConnection(connection);
  if (!validConnection) {
    throw new Error("Invalid connection");
  }
  if (!process.env.HELIUS_RPC_ENDPOINT) {
    throw new Error(
      "HELIUS_RPC_ENDPOINT is required to get nft data via DAS api",
    );
  }

  let lpFeeAmount = new BN(0);
  try {
    const raydiumNft = await getAsset(nftMint, process.env.HELIUS_RPC_ENDPOINT);
    if (!raydiumNft) {
      throw new Error(`Raydium nft ${nftMint.toBase58()} not found`);
    }
    const uriData = await axios.get(raydiumNft.content.json_uri);
    const decimals = uriData.data.poolInfo?.lpMint?.decimals;
    const lp = uriData.data.positionInfo?.unclaimedFee?.lp;
    lpFeeAmount =
      decimals && lp
        ? new BN(
            uriData.data.positionInfo?.unclaimedFee?.lp *
              Math.pow(10, decimals),
          )
        : new BN(0);
    if (lpFeeAmount.eq(new BN(0))) {
      throw new Error(
        `Unclaimed fee is zero for ${nftMint.toBase58()}, nothing to withdraw`,
      );
    }
  } catch (error) {
    throw new Error(error);
  }
  const raydium = await Raydium.load({
    owner: signer,
    connection,
    cluster: network,
  });

  let poolInfo: ApiV3PoolInfoStandardItemCpmm;
  let poolKeys: CpmmKeys | undefined;
  if (raydium.cluster === "mainnet") {
    // note: api doesn't support get devnet pool info, so in devnet else we go rpc method
    // if you wish to get pool info from rpc, also can modify logic to go rpc method directly
    const data = await raydium.api.fetchPoolById({ ids: poolId.toBase58() });
    poolInfo = data[0] as ApiV3PoolInfoStandardItemCpmm;
    if (!isValidCpmm(poolInfo.programId))
      throw new Error("target pool is not CPMM pool");
  } else {
    const data = await raydium.cpmm.getPoolInfoFromRpc(poolId.toBase58());
    poolInfo = data.poolInfo;
    poolKeys = data.poolKeys;
  }

  const { builder } = await raydium.cpmm.harvestLockLp({
    programId: network == "mainnet" ? LOCK_CPMM_PROGRAM : DEV_LOCK_CPMM_PROGRAM,
    authProgram: network == "mainnet" ? LOCK_CPMM_AUTH : DEV_LOCK_CPMM_AUTH,
    poolKeys,
    poolInfo,
    nftMint,
    lpFeeAmount,
    txVersion: TxVersion.V0,
  });

  const txBuild = await builder.versionBuild({ txVersion: TxVersion.V0 });
  return txBuild.transaction as VersionedTransaction;
};