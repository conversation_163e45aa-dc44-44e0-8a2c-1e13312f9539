// global.d.ts
import { Transaction } from '@solana/web3.js';

/// <reference types="jest" />

declare global {
    interface Window {
        privy?: {
          signTransaction: (tx: Transaction) => Promise<Transaction>;
          // Add other methods you use if needed
        };
      }

  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R
      toHaveClass(className: string): R
      toHaveValue(value: string | number): R
      toBeDisabled(): R
      toBeEnabled(): R
      toBeVisible(): R
      toBeEmptyDOMElement(): R
      toHaveTextContent(text: string | RegExp): R
      toHaveAttribute(attr: string, value?: string): R
      toHaveStyle(style: string | Record<string, any>): R
      toHaveFocus(): R
      toBeChecked(): R
      toBePartiallyChecked(): R
      toHaveDisplayValue(value: string | string[]): R
      toHaveFormValues(values: Record<string, any>): R
      toHaveAccessibleDescription(description?: string | RegExp): R
      toHaveAccessibleName(name?: string | RegExp): R
      toHaveErrorMessage(message?: string | RegExp): R
      toBeRequired(): R
      toBeValid(): R
      toBeInvalid(): R
    }
  }
} 