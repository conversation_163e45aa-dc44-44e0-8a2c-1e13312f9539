import { motion } from 'framer-motion';
import { FileText } from 'lucide-react';
import React from 'react';
import { useTranslation } from '../../../hooks/useTranslation';

interface AboutSectionProps {
  description: string;
}

export const AboutSection: React.FC<AboutSectionProps> = ({ description }) => {
  const { t } = useTranslation();
  return (
    <motion.div
      key="about"
      className="flex-1 bg-gray-50 p-6 overflow-y-auto"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <motion.div
          className="flex items-center space-x-3 mb-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="w-10 h-10 bg-[#FFF7F1] rounded-lg flex items-center justify-center">
            <FileText size={20} className="text-[#F58A38]" />
          </div>
          <h3 className="text-xl font-bold text-gray-900">
            {t('comments.aboutThisToken')}
          </h3>
        </motion.div>
        <motion.div
          className="prose prose-gray max-w-none"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <p className="text-gray-700 leading-relaxed whitespace-pre-line">
            {description}
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
}; 