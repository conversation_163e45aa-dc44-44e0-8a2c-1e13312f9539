{"name": "funhi-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "yarn next lint -- --fix", "knip": "knip"}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@privy-io/react-auth": "^2.13.7", "@raydium-io/raydium-sdk-v2": "^0.1.139-alpha", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@types/body-scroll-lock": "^3.1.2", "ag-charts-enterprise": "^11.2.3", "ag-charts-react": "^11.2.3", "axios": "^1.8.4", "body-scroll-lock": "^4.0.0-beta.0", "critters": "^0.0.25", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.485.0", "next": "^15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/html2canvas": "^0.5.35", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-plugin-import": "^2.32.0", "jest-environment-jsdom": "^30.0.4", "knip": "^5.60.2", "tailwindcss": "^4", "typescript": "^5"}}