// setupDatabase.js
const mysql = require("mysql2");
const config = require("./config/security");
const { initialize } = require("./utilities/funhi-program/initialize.ts");
const anchor = require("@coral-xyz/anchor");
const { LAMPORTS_PER_SOL, PublicKey } = require("@solana/web3.js");
require("ts-node/register");
const { program, connection } = require("./utilities/funhi-program/setup.ts");
const { global } = require("./utilities/funhi-program/pdas.ts");
const bs58 = require("bs58");
const {
  Transaction,
  sendAndConfirmTransaction,
  Keypair,
} = require("@solana/web3.js");
const {
  updateGlobalConfig,
} = require("./utilities/funhi-program/updateGlobalConfig.ts");

const dbConf = {
  host: config.DB.HOST,
  user: config.DB.USERNAME,
  password: config.DB.PASSWORD,
  connectionLimit: config.DB.MAX_CONNECTION,
  debug: false,
};

const pool = mysql.createPool(dbConf);

// Utility to execute queries
const executeQuery = ({ query }) =>
  new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) return reject(err);
      connection.query(query, (error, results) => {
        connection.release();
        if (error) reject(error);
        else resolve(results);
      });
    });
  });

// Initialization logic
async function createDatabase() {
  console.log("✅ Creating database if not exists...");
  await executeQuery({
    query: `CREATE DATABASE IF NOT EXISTS \`${config.DB.NAME}\`;`,
  });
}

async function initializeDatabase() {
  try {
    await createDatabase();
    console.log("✅ Database initialization complete.");
  } catch (err) {
    console.error("❌ Database setup failed:", err);
    process.exit(1);
  }
}

// async function initializeContract() {
//   try {
//     const initialVirtualTokenReserves = new anchor.BN(
//       config.INITIAL_VIRTUAL_TOKEN_RESERVES
//     );
//     const initialVirtualSolReserves = new anchor.BN(
//       config.INITIAL_VIRTUAL_SOL_RESERVES
//     );
//     const tokenTotalSupply = new anchor.BN(config.TOKEN_TOTAL_SUPPLY);
//     const creatorVaultAmount = new anchor.BN(config.CREATOR_VAULT_AMOUNT);
//     const initialRealTokenReserves = tokenTotalSupply.sub(creatorVaultAmount);
//     const graduationThreshold = new anchor.BN(config.GRADUATION_THRESHOLD);
//     const tradingFeeBps = new anchor.BN(config.TRADING_FEE_BPS);
//     const firstBuyFeeSol = new anchor.BN(config.FIRST_BUY_FEE_SOL);
//     const feeRecipient = new PublicKey(config.FEE_RECIPIENT);

//     let globalData = null;
//     try {
//       const accountInfo = await connection.getAccountInfo(global);
//       if (accountInfo !== null) {
//         console.log("Global account already exists. Skipping initialization.");
//         return;
//       }
//     } catch (e) {
//       // If fetch fails, assume account does not exist
//       globalData = null;
//     }
//     if (!globalData) {
//       // If the account does not exist, proceed with initialization
//       const ix = await initialize(
//         initialVirtualTokenReserves,
//         initialVirtualSolReserves,
//         initialRealTokenReserves,
//         tokenTotalSupply,
//         tradingFeeBps,
//         feeRecipient,
//         firstBuyFeeSol,
//         graduationThreshold
//       );
//       const adminKeypair = Keypair.fromSecretKey(
//         bs58.default.decode(config.ADMIN_PRIVATE_KEY)
//       );
//       const tx = new Transaction().add(ix);
//       tx.feePayer = adminKeypair.publicKey;
//       tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
//       tx.partialSign(adminKeypair);
//       await sendAndConfirmTransaction(connection, tx, [adminKeypair]);
//       console.log("✅ Contract initialization complete.");
//     } else {
//       console.log("Global account already exists. Skipping initialization.");
//     }
//   } catch (err) {
//     console.error("❌ Contract initialization failed:", err);
//     process.exit(1);
//   }
// }
// Add this function to handle account cleanup (devnet/testnet only)
async function cleanupExistingAccount() {
  try {
    const adminKeypair = Keypair.fromSecretKey(
      bs58.default.decode(config.ADMIN_PRIVATE_KEY)
    );
    
    console.log("Checking if global account exists...");
    const accountInfo = await connection.getAccountInfo(global);
    
    if (accountInfo !== null) {
      console.log("Global account exists. Account data length:", accountInfo.data.length);
      console.log("Account owner:", accountInfo.owner.toString());
      
      // If the account has SOL but no data, you might need to close it
      if (accountInfo.lamports > 0) {
        console.log("Account has lamports:", accountInfo.lamports);
        
        // For devnet/testnet only - you can request airdrop to admin account
        // and then close the problematic account if your program supports it
        console.log("⚠️  Manual intervention may be required to close this account");
      }
    } else {
      console.log("Global account does not exist.");
    }
  } catch (err) {
    console.error("Error checking account:", err);
  }
}

// Call this before initialization
// await cleanupExistingAccount();

async function initializeContract() {
  try {
    const initialVirtualTokenReserves = new anchor.BN(
      config.INITIAL_VIRTUAL_TOKEN_RESERVES
    );
    const initialVirtualSolReserves = new anchor.BN(
      config.INITIAL_VIRTUAL_SOL_RESERVES
    );
    const tokenTotalSupply = new anchor.BN(config.TOKEN_TOTAL_SUPPLY);
    const creatorVaultAmount = new anchor.BN(config.CREATOR_VAULT_AMOUNT);
    const initialRealTokenReserves = tokenTotalSupply.sub(creatorVaultAmount);
    const graduationThreshold = new anchor.BN(config.GRADUATION_THRESHOLD);
    const tradingFeeBps = new anchor.BN(config.TRADING_FEE_BPS);
    const firstBuyFeeSol = new anchor.BN(config.FIRST_BUY_FEE_SOL);
    const feeRecipient = new PublicKey(config.FEE_RECIPIENT);

    // More robust check for existing accounts
    try {
      const accountInfo = await connection.getAccountInfo(global);
      if (accountInfo !== null && accountInfo.data.length > 0) {
        console.log("Global account already exists and is initialized. Skipping initialization.");
        console.log("Global account address:", global.toString());
        return;
      }
    } catch (e) {
      console.log("Error checking global account:", e.message);
    }

    // Additional check: try to fetch the account data using the program
    try {
      const globalAccountData = await program.account.global.fetch(global);
      if (globalAccountData) {
        console.log("Global account data already exists. Skipping initialization.");
        return;
      }
    } catch (e) {
      // If fetching fails, the account likely doesn't exist or isn't initialized
      console.log("Global account not found or not initialized, proceeding...");
    }

    console.log("Proceeding with contract initialization...");
    console.log("Global account address that will be created:", global.toString());

    // If we reach here, proceed with initialization
    const ix = await initialize(
      initialVirtualTokenReserves,
      initialVirtualSolReserves,
      initialRealTokenReserves,
      tokenTotalSupply,
      tradingFeeBps,
      feeRecipient,
      firstBuyFeeSol,
      graduationThreshold
    );
    
    const adminKeypair = Keypair.fromSecretKey(
      bs58.default.decode(config.ADMIN_PRIVATE_KEY)
    );
    
    const tx = new Transaction().add(ix);
    tx.feePayer = adminKeypair.publicKey;
    tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    tx.partialSign(adminKeypair);
    
    console.log("Sending initialization transaction...");
    const signature = await sendAndConfirmTransaction(connection, tx, [adminKeypair]);
    console.log("✅ Contract initialization complete. Signature:", signature);
    
  } catch (err) {
    if (err.message.includes("already in use") || err.message.includes("account Address")) {
      console.log("⚠️  Account already exists. This might be expected if running setup multiple times.");
      console.log("If you need to reinitialize, you may need to:");
      console.log("1. Use a different network (devnet/testnet/mainnet)");
      console.log("2. Clear the existing account data");
      console.log("3. Use the updateGlobalConfig function instead");
      return; // Don't exit the process
    }
    
    console.error("❌ Contract initialization failed:", err);
    process.exit(1);
  }
}

async function updateGlobalConfigFlow() {
  if (!config.UPDATE_GLOBAL_CONFIG) {
    console.log(
      "UPDATE_GLOBAL_CONFIG is not set to true. Skipping global config update."
    );
    return;
  }
  try {
    const initialVirtualTokenReserves = new anchor.BN(
      config.INITIAL_VIRTUAL_TOKEN_RESERVES
    );
    const initialVirtualSolReserves = new anchor.BN(
      config.INITIAL_VIRTUAL_SOL_RESERVES
    );
    const tokenTotalSupply = new anchor.BN(config.TOKEN_TOTAL_SUPPLY);
    const creatorVaultAmount = new anchor.BN(config.CREATOR_VAULT_AMOUNT);
    const initialRealTokenReserves = tokenTotalSupply.sub(creatorVaultAmount);
    const graduationThreshold = new anchor.BN(config.GRADUATION_THRESHOLD);
    const tradingFeeBps = new anchor.BN(config.TRADING_FEE_BPS);
    const firstBuyFeeSol = new anchor.BN(config.FIRST_BUY_FEE_SOL);
    const feeRecipient = new PublicKey(config.FEE_RECIPIENT);
    const secretKeyArray = JSON.parse(
      fs.readFileSync("./admin-keypair.json", "utf8")
    );
    const adminKeypair = Keypair.fromSecretKey(Uint8Array.from(secretKeyArray));
    const newAuthority = new PublicKey(config.NEW_AUTHORITY);
    const ix = await updateGlobalConfig(
      adminKeypair.publicKey, // authority
      newAuthority, // newAuthority from env
      feeRecipient,
      tradingFeeBps,
      firstBuyFeeSol,
      initialVirtualTokenReserves,
      initialVirtualSolReserves,
      initialRealTokenReserves,
      tokenTotalSupply,
      graduationThreshold
    );
    const tx = new Transaction().add(ix);
    tx.feePayer = adminKeypair.publicKey;
    tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    tx.partialSign(adminKeypair);
    await sendAndConfirmTransaction(connection, tx, [adminKeypair]);
    console.log("✅ Global config updated.");
  } catch (err) {
    console.error("❌ Global config update failed:", err);
    process.exit(1);
  }
}

module.exports = {
  initializeDatabase,
  initializeContract,
  cleanupExistingAccount,
  updateGlobalConfig: updateGlobalConfigFlow,
};
