// src/components/shared/filters/index.tsx
"use client";
import Image from "next/image";
import React, { useState, useRef, useEffect, useCallback } from "react";

/**
 * Custom Dropdown Component
 */
interface Option {
    value: string;
    label: string;
    disabled?: boolean;
}

interface CustomDropdownProps {
    options: Option[];
    selectedValue: string;
    onChange: (value: string) => void;
    label: string;
    placeholder?: string;
    disabled?: boolean;
    error?: string;
    required?: boolean;
    className?: string;
    'aria-label'?: string;
    'aria-describedby'?: string;
    'data-testid'?: string;
}

// Custom hook for keyboard navigation
const useKeyboardNavigation = (
    options: Option[],
    isOpen: boolean,
    onSelect: (value: string) => void,
    onClose: () => void
) => {
    const [focusedIndex, setFocusedIndex] = useState(-1);

    const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
        if (!isOpen) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                return 'open';
            }
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setFocusedIndex(prev => {
                    const nextIndex = prev + 1;
                    if (nextIndex >= options.length) return 0;
                    return nextIndex;
                });
                break;
            case 'ArrowUp':
                e.preventDefault();
                setFocusedIndex(prev => {
                    const nextIndex = prev - 1;
                    if (nextIndex < 0) return options.length - 1;
                    return nextIndex;
                });
                break;
            case 'Enter':
                e.preventDefault();
                if (focusedIndex >= 0 && !options[focusedIndex].disabled) {
                    onSelect(options[focusedIndex].value);
                    onClose();
                }
                break;
            case 'Escape':
                e.preventDefault();
                onClose();
                break;
            case 'Tab':
                onClose();
                break;
        }
    }, [isOpen, focusedIndex, options]);

    const resetFocus = useCallback(() => {
        setFocusedIndex(-1);
    }, []);

    return { focusedIndex, handleKeyDown, resetFocus };
};

const CustomDropdown: React.FC<CustomDropdownProps> = ({
    options,
    selectedValue,
    onChange,
    label,
    placeholder = "Select an option",
    disabled = false,
    error,
    required = false,
    className = "",
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    'data-testid': dataTestId,
}) => {
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    const selectedOption = options.find(option => option.value === selectedValue);

    const { focusedIndex, handleKeyDown, resetFocus } = useKeyboardNavigation(
        options,
        dropdownOpen,
        onChange,
        () => setDropdownOpen(false)
    );

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
                resetFocus();
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [resetFocus]);

    // Close dropdown on escape key
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && dropdownOpen) {
                setDropdownOpen(false);
                resetFocus();
            }
        };

        document.addEventListener('keydown', handleEscape);
        return () => document.removeEventListener('keydown', handleEscape);
    }, [dropdownOpen, resetFocus]);

    const handleToggle = () => {
        if (!disabled) {
            setDropdownOpen(!dropdownOpen);
            if (!dropdownOpen) {
                resetFocus();
            }
        }
    };

    const handleOptionClick = (option: Option) => {
        if (!option.disabled) {
            onChange(option.value);
            setDropdownOpen(false);
            resetFocus();
            // Return focus to button
            buttonRef.current?.focus();
        }
    };

    const dropdownId = `dropdown-${label.toLowerCase().replace(/\s+/g, '-')}`;
    const listboxId = `${dropdownId}-listbox`;

    return (
        <div 
            className={`relative flex flex-wrap items-center gap-2 min-w-[120px] ${className}`}
            ref={dropdownRef}
            data-testid={dataTestId}
        >
            <span className="text-xs sm:text-sm font-semibold whitespace-nowrap">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}:
            </span>
            
            <div className="relative flex-grow">
                <button
                    ref={buttonRef}
                    className={`w-full pl-2 py-1 rounded-md border text-xs sm:text-sm cursor-pointer flex items-center justify-between transition-colors ${
                        disabled
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
                            : error
                            ? 'border-red-300 bg-red-50 hover:bg-red-100'
                            : 'bg-white border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                    } ${dropdownOpen ? 'border-blue-500 ring-2 ring-blue-200' : ''}`}
                    onClick={handleToggle}
                    onKeyDown={handleKeyDown}
                    aria-haspopup="listbox"
                    aria-expanded={dropdownOpen}
                    aria-labelledby={dropdownId}
                    aria-describedby={ariaDescribedBy}
                    aria-label={ariaLabel}
                    disabled={disabled}
                    role="combobox"
                    aria-activedescendant={focusedIndex >= 0 ? `${listboxId}-${focusedIndex}` : undefined}
                >
                    <span className="truncate max-w-[80px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[200px]">
                        {selectedOption?.label || placeholder}
                    </span>
                    <Image
                        src="/icons/arrow-down-grey.svg"
                        alt=""
                        width={16}
                        height={16}
                        className={`transition-transform duration-300 ${
                            dropdownOpen ? 'transform rotate-180' : ''
                        }`}
                        aria-hidden="true"
                    />
                </button>

                {error && (
                    <div className="text-red-500 text-xs mt-1" role="alert">
                        {error}
                    </div>
                )}

                {dropdownOpen && (
                    <div
                        className="absolute z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg w-full max-h-60 overflow-auto"
                        role="listbox"
                        id={listboxId}
                        aria-label={`${label} options`}
                    >
                        {options.map((option, index) => (
                            <div
                                key={option.value}
                                id={`${listboxId}-${index}`}
                                className={`px-2 py-1 cursor-pointer text-xs sm:text-sm transition-colors ${
                                    option.value === selectedValue
                                        ? 'bg-blue-100 text-blue-900'
                                        : option.disabled
                                        ? 'text-gray-400 cursor-not-allowed'
                                        : focusedIndex === index
                                        ? 'bg-gray-100'
                                        : 'hover:bg-gray-100'
                                }`}
                                onClick={() => handleOptionClick(option)}
                                role="option"
                                aria-selected={option.value === selectedValue}
                                aria-disabled={option.disabled}
                            >
                                <span className="truncate block max-w-[80px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[200px]">
                                    {option.label}
                                </span>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default CustomDropdown;