const { Model, DataTypes } = require("sequelize");

class Tokens extends Model {
    static initModel(sequelize) {
        return Tokens.init(
            {
                
                tokenId: {
                    primaryKey: true,
                    allowNull: false,
                    autoIncrement: true,
                    type: DataTypes.BIGINT,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users', // Make sure this matches your table name
                        key: 'id',      // Make sure this matches the primary key of the User model
                    },
                    onDelete: 'CASCADE', // Optional: removes tokens if the user is deleted
                },
                name: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                },
                ticker: {
                    type: DataTypes.STRING(10),
                    allowNull: false,
                },
                description: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
                image: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                tokenAddress: {
                    type: DataTypes.STRING,
                    allowNull: false,
                    unique: true,
                },
                bondingCurvePda: {
                    type: DataTypes.STRING,
                    allowNull: true,
                    unique: false,
                },
                poolAddress: {
                    type: DataTypes.STRING,
                    allowNull: false,
                    defaultValue: "",
                    unique: false,
                },
                poolsignature: {
                    type: DataTypes.STRING,
                    allowNull: false,
                    defaultValue: "",
                    unique: false,
                },
                locklpTransaction: {
                    type: DataTypes.STRING,
                    allowNull: false,
                    defaultValue: "",
                    unique: false,
                },
                nftMint: {
                    type: DataTypes.STRING,
                    allowNull: false,
                    defaultValue: "",
                    unique: false,
                },
                graduated: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                category: {
                    type: DataTypes.STRING(50),
                    allowNull: true,
                },
                isVerified: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                marketCap: {
                    type: DataTypes.FLOAT,
                    allowNull: true,
                    defaultValue: 0,
                },
                price: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                    defaultValue: 0,
                },
                time: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    defaultValue: DataTypes.NOW,
                },
                telegram: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                website: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                twitter: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                creatorWallet: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
            },
            {
                sequelize,
                tableName: 'tokens',
                timestamps: true, // Automatically handles createdAt & updatedAt
            }
        );

        
    }
}

module.exports = Tokens;
