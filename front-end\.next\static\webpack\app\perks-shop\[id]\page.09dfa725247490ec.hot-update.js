"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/shared/chat/ChatModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useChatSocket */ \"(app-pages-browser)/./src/hooks/useChatSocket.ts\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_escrow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\");\n/* harmony import */ var _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ChatModalStateContext */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// \"use client\";\n// import React, { useState, useEffect, useCallback, useRef } from \"react\";\n// import { ArrowLeft, MoreVertical, CheckCheck, Send, X } from \"lucide-react\";\n// import { usePrivy } from \"@privy-io/react-auth\";\n// import { useChatSocket } from \"@/hooks/useChatSocket\";\n// import axios from \"axios\";\n// import dayjs from 'dayjs';\n// import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';\n// import { useTranslation } from '@/hooks/useTranslation';\n// interface ChatModalProps {\n//   chatRoomId: string;\n//   buyerId: string | number;\n//   sellerId: string | number;\n//   onClose: () => void;\n// }\n// const API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\n// const ChatModal: React.FC<ChatModalProps> = ({ chatRoomId, buyerId, sellerId, onClose }) => {\n//   const { t, isRTL } = useTranslation();\n//   // All hooks at the top\n//   const { user } = usePrivy();\n//   const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();\n//   const { setOpenChatTradeId } = useAppContext();\n//   const [messages, setMessages] = useState<any[]>([]);\n//   const [input, setInput] = useState(\"\");\n//   const messagesEndRef = useRef<HTMLDivElement>(null);\n//   const [currentStatus, setCurrentStatus] = useState<string | null>(null);\n//   const [releaseDeadline, setReleaseDeadline] = useState<Date | null>(null);\n//   const [timeLeft, setTimeLeft] = useState<string | null>(null);\n//   const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);\n//   const notificationAudioRef = useRef<HTMLAudioElement | null>(null);\n//   // Get userBo.id from localStorage for consistent sender check\n//   const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//   const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//   const myUserId = userBo?.id;\n//   // Fetch receiver info (email) when receiverId changes\n//   useEffect(() => {\n//     async function fetchReceiver() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/users/${sellerId}`);\n//         // Try to extract email from possible locations\n//         let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);\n//         let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);\n//         let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);\n//         setReceiverInfo({ name, email, wallet });\n//       } catch (err) {\n//         setReceiverInfo(null);\n//       }\n//     }\n//     if (sellerId) fetchReceiver();\n//   }, [sellerId]);\n//   // Fetch message history on mount or when chatRoomId changes\n//   useEffect(() => {\n//     async function fetchMessages() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);\n//         // Replace messages state with fetched history (no merge)\n//         setMessages((res.data.data || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()));\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchMessages();\n//   }, [chatRoomId]);\n//   // Fetch release deadline on mount or when tradeId changes\n//   useEffect(() => {\n//     async function fetchDeadline() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/${chatRoomId}/release-deadline`);\n//         const deadline = new Date(res.data.releaseDeadline);\n//         setReleaseDeadline(deadline);\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchDeadline();\n//   }, [chatRoomId]);\n//   // Timer countdown (only updates local state)\n//   useEffect(() => {\n//     if (!releaseDeadline) return;\n//     const interval = setInterval(() => {\n//       const now = new Date();\n//       const diff = releaseDeadline.getTime() - now.getTime();\n//       if (diff <= 0) {\n//         setTimeLeft(t('chat.autoReleaseInProgress'));\n//         clearInterval(interval);\n//       } else {\n//         // Format as translation string\n//         const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n//         const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);\n//         const minutes = Math.floor((diff / (1000 * 60)) % 60);\n//         const seconds = Math.floor((diff / 1000) % 60);\n//         setTimeLeft(t('chat.timeFormat', { days, hours, minutes, seconds }));\n//       }\n//     }, 1000);\n//     return () => clearInterval(interval);\n//   }, [releaseDeadline, t]);\n//   // Handle incoming real-time messages (deduplicate by id, tempId, and content)\n//   const handleMessage = useCallback((msg: any) => {\n//     setMessages(prev => {\n//       // If message already exists (by id, tempId, or identical content+createdAt+senderId), skip\n//       if (prev.some(m =>\n//         (m.id && msg.id && m.id === msg.id) ||\n//         (m.tempId && msg.tempId && m.tempId === msg.tempId) ||\n//         (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)\n//       )) {\n//         return prev;\n//       }\n//       // Play notification sound if the current user is the receiver\n//       const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//       const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//       if (userBo && msg.receiverId === userBo.id && notificationAudioRef.current) {\n//         notificationAudioRef.current.currentTime = 0;\n//         notificationAudioRef.current.play();\n//       }\n//       return [...prev, msg].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n//     });\n//   }, []);\n//   const handleTradeStatus = useCallback((data: { status: string }) => setCurrentStatus(data.status), []);\n//   // Setup socket\n//   const { sendMessage, release, report, authenticated, joinedRoom, tradeStatus } = useChatSocket({\n//     chatRoomId, // Pass chatRoomId to useChatSocket\n//     userId: user?.id || user?.wallet?.address || \"unknown\",\n//     wallet: user?.wallet?.address || \"\",\n//     onMessage: handleMessage,\n//     onTradeStatus: handleTradeStatus,\n//   });\n//   // Scroll to bottom on new message\n//   useEffect(() => {\n//     messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n//   }, [messages]);\n//   // Send message handler (optimistic update)\n//   const handleSend = (e: React.FormEvent) => {\n//     e.preventDefault();\n//     if (!input.trim()) return;\n//     const userBoStr = localStorage.getItem(\"userBo\");\n//     const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//     if (!userBo?.id) {\n//       alert(t('chat.userIdNotFound'));\n//       return;\n//     }\n//     sendMessage({\n//       chatRoomId,\n//       senderId: userBo.id,\n//       receiverId: userBo.id === buyerId ? sellerId : buyerId,\n//       message: input,\n//     });\n//     setInput(\"\");\n//   };\n//   // Helper: format time\n//   const formatTime = (dateStr: string) => {\n//     const date = new Date(dateStr);\n//     return date.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\n//   };\n//   // Helper: format date for separator\n//   const formatDateSeparator = (date: Date) => {\n//     const today = dayjs().startOf('day');\n//     const msgDay = dayjs(date).startOf('day');\n//     if (msgDay.isSame(today)) return t('chat.today');\n//     if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n//     return msgDay.format('D MMM YYYY');\n//   };\n//   // Helper to get initials from email or name\n//   const getInitials = (email?: string, name?: string) => {\n//     if (name && name.trim()) return name.slice(0, 2).toUpperCase();\n//     if (email && email.trim()) return email.slice(0, 2).toUpperCase();\n//     return \"?\";\n//   };\n//   // Helper to format wallet address (first 4 + last 4 characters)\n//   const formatWalletAddress = (wallet?: string) => {\n//     if (!wallet || wallet.length < 8) return wallet;\n//     return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;\n//   };\n//   // Helper to get display name with priority: name > email > formatted wallet\n//   const getDisplayName = () => {\n//     if (receiverInfo?.name && receiverInfo.name.trim()) {\n//       return receiverInfo.name;\n//     }\n//     if (receiverInfo?.email && receiverInfo.email.trim()) {\n//       return receiverInfo.email;\n//     }\n//     if (receiverInfo?.wallet && receiverInfo.wallet.trim()) {\n//       return formatWalletAddress(receiverInfo.wallet);\n//     }\n//     return t('chat.user');\n//   };\n//   // Show status in the UI and disable buttons if released or reported\n//   const isActionDisabled = currentStatus === 'released' || currentStatus === 'reported';\n//   // useEffect to set openChatTradeId and clear unread chat messages for this chatRoomId\n//   useEffect(() => {\n//     setOpenChatTradeId(chatRoomId);\n//     removeUnreadChatMessagesForTrade(chatRoomId);\n//     return () => {\n//       setOpenChatTradeId(null);\n//     };\n//   }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade]);\n//   // Only after all hooks, do conditional returns\n//   if (!user?.wallet?.address) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectWalletToChat')}</p>\n//           {/* You can trigger Privy login here if needed */}\n//         </div>\n//       </div>\n//     );\n//   }\n//   if (!authenticated || !joinedRoom) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectingToChat')}</p>\n//         </div>\n//       </div>\n//     );\n//   }\n//   return (\n//     <div\n//       className={`fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300 ${isRTL ? 'rtl' : 'ltr'}`}\n//     >\n//       {/* Notification sound */}\n//       <audio ref={notificationAudioRef} src=\"/sounds/notification.mp3\" preload=\"auto\" />\n//         {/* Close button */}\n//         <button\n//           className=\"absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1\"\n//           onClick={onClose}\n//         >\n//           <X className=\"w-5 h-5 text-gray-700\" />\n//         </button>\n//         {/* Header */}\n//         {/* <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button\n//             className={isRTL ? 'ml-3' : 'mr-3'}\n//             onClick={onClose}\n//             style={isRTL ? { marginLeft: '12px', marginRight: 0 } : { marginRight: '12px', marginLeft: 0 }}\n//           >\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button> */}\n//           <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button className={isRTL ? 'ml-3' : 'mr-3'} onClick={onClose}>\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button>\n//           {/* <div\n//             className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}\n//             style={isRTL ? { direction: 'rtl', textAlign: 'right', alignItems: 'flex-end' } : { direction: 'ltr', textAlign: 'left', alignItems: 'flex-start' }}\n//           >\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div> */}\n//           <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`} style={isRTL ? { direction: 'rtl' } : {}}>\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//         {/* Messages Container */}\n//         <div className=\"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto\" style={{ maxHeight: 400 }}>\n//           {(() => {\n//             let lastDate: string | null = null;\n//             return messages.map((msg, idx) => {\n//               const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n//               const showDate = msgDate && msgDate !== lastDate;\n//               lastDate = msgDate;\n//               return (\n//                 <React.Fragment key={msg.id ? `id-${msg.id}` : msg.tempId ? `temp-${msg.tempId}` : `fallback-${msg.senderId}-${msg.createdAt}-${idx}` }>\n//                   {showDate && (\n//                     <div className=\"flex justify-center my-2\">\n//                       <span className=\"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\">{msgDate}</span>\n//                     </div>\n//                   )}\n//                   <div className={`flex flex-col ${msg.senderId === myUserId ? \"items-end\" : \"items-start\"}`}>\n//                     <div\n//                       className={\n//                         msg.senderId === myUserId\n//                           ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\"\n//                           : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\"\n//                       }\n//                     >\n//                       <p className=\"text-sm leading-relaxed whitespace-pre-line\">{msg.message || msg.text}</p>\n//                     </div>\n//                     <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"}`}>\n//                       <span className=\"text-xs text-gray-400\">\n//                         {msg.createdAt ? formatTime(msg.createdAt) : msg.time || \"\"}\n//                       </span>\n//                       {msg.senderId === myUserId && <CheckCheck className=\"w-4 h-4 text-green-500\" />}\n//                     </div>\n//                   </div>\n//                 </React.Fragment>\n//               );\n//             });\n//           })()}\n//           <div ref={messagesEndRef} />\n//         </div>\n//         {/* Bottom Section */}\n//         {/* Auto-release Info */}\n//         <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n//           <p className=\"text-xs text-gray-500 text-center leading-relaxed\">\n//             {timeLeft ? (\n//               <>{t('chat.autoReleaseIn', { time: timeLeft })}</>\n//             ) : (\n//               <>{t('chat.loadingAutoRelease')}</>\n//             )}<br />\n//             {t('chat.reportTrade')}\n//           </p>\n//         </div>\n//         {/* Action Buttons */}\n//         <div className=\"px-4 py-3 space-y-2\">\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={release}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.iGotTheItem')}\n//           </button>\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={report}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.reportTrade')}\n//           </button>\n//           {currentStatus && (\n//             <div className=\"text-center text-xs text-gray-500 mt-2\">\n//               {t('chat.tradeStatus')} <span className=\"font-semibold\">{currentStatus}</span>\n//             </div>\n//           )}\n//         </div>\n//         {/* Message Input */}\n//         <form className=\"px-4 py-3 bg-orange-50\" onSubmit={handleSend}>\n//           <div className=\"flex items-center gap-2\">\n//             <input\n//               type=\"text\"\n//               placeholder={t('chat.typeMessage')}\n//               className=\"flex-1 bg-orange-100 rounded-full px-4 py-2 text-sm outline-none border border-transparent focus:border-orange-300 placeholder:text-gray-500\"\n//               value={input}\n//               onChange={e => setInput(e.target.value)}\n//               disabled={!authenticated || !joinedRoom}\n//             />\n//             <button type=\"submit\" className=\"bg-orange-500 p-2 rounded-full hover:bg-orange-600 transition-colors\" disabled={!authenticated || !joinedRoom}>\n//               <Send className=\"w-5 h-5 text-white\" />\n//             </button>\n//           </div>\n//         </form>\n//     </div>\n//   );\n// };\n// export default ChatModal; \n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\nconst ChatModal = (param)=>{\n    let { chatRoomId, buyerId, sellerId, onClose, onRelease, onRefund, onReport, onAccept, onInitiateDispute, activeTrade } = param;\n    var _user_wallet, _user_wallet1, _receiverInfo_name, _receiverInfo_email;\n    _s();\n    const { t, isRTL } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    // Debug props received\n    console.log('🎯 [ChatModal] Props received:', {\n        chatRoomId,\n        buyerId,\n        sellerId,\n        hasOnRelease: !!onRelease,\n        hasOnAccept: !!onAccept,\n        hasOnReport: !!onReport,\n        hasActiveTrade: !!activeTrade,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId,\n            from: activeTrade.from,\n            to: activeTrade.to\n        } : null\n    });\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { solanaWallet, isConnected, getWalletAddress } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { removeUnreadChatMessagesForTrade } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages)();\n    const { setOpenChatTradeId } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const { registerChatModal, unregisterChatModal, updateChatModalState, getChatModalState } = (0,_contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_8__.useChatModalState)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [receiverInfo, setReceiverInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const notificationAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentTradeStatus, setCurrentTradeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status) || null);\n    const [isOperationInProgress, setIsOperationInProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Request notification permission when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            if ('Notification' in window && Notification.permission === 'default') {\n                Notification.requestPermission().then({\n                    \"ChatModal.useEffect\": (permission)=>{\n                        console.log('🔔 [ChatModal] Notification permission:', permission);\n                    }\n                }[\"ChatModal.useEffect\"]);\n            }\n        }\n    }[\"ChatModal.useEffect\"], []);\n    // Get user ID\n    const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n    const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n    // Determine user role - ensure type consistency\n    const buyerIdNum = typeof buyerId === 'string' ? parseInt(buyerId) : buyerId;\n    const sellerIdNum = typeof sellerId === 'string' ? parseInt(sellerId) : sellerId;\n    const myUserIdNum = typeof myUserId === 'string' ? parseInt(myUserId) : myUserId;\n    const isBuyer = myUserIdNum === buyerIdNum;\n    const isSeller = myUserIdNum === sellerIdNum;\n    // Use current trade status (which can be updated via Socket.IO) or fallback to activeTrade status\n    const tradeStatus = currentTradeStatus || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status);\n    const isEscrowed = tradeStatus === 'escrowed';\n    const isPendingAcceptance = tradeStatus === 'pending_acceptance';\n    const isEscrowReleased = tradeStatus === 'completed' || tradeStatus === 'released';\n    const isUserAuthorized = user && (isBuyer || isSeller);\n    // Trade action buttons should only show when escrow is active (not released)\n    const shouldShowTradeActions = (isEscrowed || isPendingAcceptance) && !isEscrowReleased;\n    // Seller can accept when escrow is pending acceptance\n    const canSellerAccept = isPendingAcceptance && isSeller && onAccept;\n    // Buyer can only release after seller has accepted\n    const canBuyerRelease = isEscrowed && !isPendingAcceptance && isBuyer;\n    // Debug logging\n    console.log('🔍 [ChatModal] User role debugging:', {\n        myUserId,\n        myUserIdNum,\n        buyerId,\n        buyerIdNum,\n        sellerId,\n        sellerIdNum,\n        isBuyer,\n        isSeller,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId\n        } : null,\n        tradeStatus,\n        currentTradeStatus,\n        isEscrowed,\n        isPendingAcceptance,\n        isEscrowReleased,\n        shouldShowTradeActions,\n        canSellerAccept,\n        canBuyerRelease,\n        isUserAuthorized,\n        onAcceptExists: !!onAccept,\n        onReleaseExists: !!onRelease,\n        onReportExists: !!onReport,\n        hasActiveTrade: !!activeTrade,\n        activeTradeStatus: activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status\n    });\n    // Chat should be enabled if user is authorized (buyer or seller), regardless of escrow status\n    const canChat = isUserAuthorized && isConnected && solanaWallet;\n    // Enhanced dispute button logic\n    const canShowDisputeButton = (userRole)=>{\n        if (!activeTrade || !onInitiateDispute) return false;\n        // Use the enhanced canInitiateDispute function with dispute status\n        const disputeCheck = (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.canInitiateDispute)(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus);\n        // Debug logging for dispute button visibility\n        console.log(\"\\uD83D\\uDD0D [ChatModal] Dispute button check for \".concat(userRole, \":\"), {\n            tradeStatus: activeTrade.status,\n            disputeStatus: activeTrade.disputeStatus,\n            canDispute: disputeCheck.canDispute,\n            reason: disputeCheck.reason,\n            createdAt: activeTrade.createdAt\n        });\n        return disputeCheck.canDispute;\n    };\n    // Fetch receiver info\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchReceiver() {\n                try {\n                    const targetId = isBuyer ? sellerId : buyerId;\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"\".concat(API_BASE, \"/users/\").concat(targetId));\n                    let email = res.data.email || res.data.user && res.data.user.email || res.data.data && res.data.data.email;\n                    let name = res.data.name || res.data.user && res.data.user.name || res.data.data && res.data.data.name;\n                    let wallet = res.data.wallet || res.data.user && res.data.user.wallet || res.data.data && res.data.data.wallet;\n                    setReceiverInfo({\n                        name,\n                        email,\n                        wallet\n                    });\n                } catch (err) {\n                    setReceiverInfo(null);\n                }\n            }\n            fetchReceiver();\n        }\n    }[\"ChatModal.useEffect\"], [\n        buyerId,\n        sellerId,\n        isBuyer\n    ]);\n    // Fetch messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchMessages() {\n                try {\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"\".concat(API_BASE, \"/messages/chat-room/\").concat(chatRoomId));\n                    setMessages((res.data.data || []).sort({\n                        \"ChatModal.useEffect.fetchMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useEffect.fetchMessages\"]));\n                } catch (err) {\n                    console.error(\"Error fetching messages:\", err);\n                }\n            }\n            fetchMessages();\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId\n    ]);\n    // Handle incoming messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleMessage]\": (msg)=>{\n            console.log('📨 [ChatModal] Received message:', msg, 'myUserId:', myUserId);\n            setMessages({\n                \"ChatModal.useCallback[handleMessage]\": (prev)=>{\n                    // Check for duplicates\n                    const isDuplicate = prev.some({\n                        \"ChatModal.useCallback[handleMessage].isDuplicate\": (m)=>m.id && msg.id && m.id === msg.id || m.tempId && msg.tempId && m.tempId === msg.tempId || m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId\n                    }[\"ChatModal.useCallback[handleMessage].isDuplicate\"]);\n                    if (isDuplicate) {\n                        console.log('🔄 [ChatModal] Duplicate message, skipping');\n                        return prev;\n                    }\n                    // Play notification sound for received messages (not sent by me)\n                    if (msg.senderId !== myUserId && notificationAudioRef.current) {\n                        console.log('🔊 [ChatModal] Playing notification sound');\n                        try {\n                            notificationAudioRef.current.currentTime = 0;\n                            notificationAudioRef.current.play().catch({\n                                \"ChatModal.useCallback[handleMessage]\": (e)=>{\n                                    console.log('🔇 [ChatModal] Could not play notification sound (user interaction required):', e);\n                                    // Fallback: show browser notification if audio fails\n                                    if ('Notification' in window && Notification.permission === 'granted') {\n                                        new Notification('New Message', {\n                                            body: msg.message,\n                                            icon: '/images/funhi-logo.png'\n                                        });\n                                    }\n                                }\n                            }[\"ChatModal.useCallback[handleMessage]\"]);\n                        } catch (error) {\n                            console.log('🔇 [ChatModal] Audio play error:', error);\n                        }\n                    }\n                    const newMessages = [\n                        ...prev,\n                        msg\n                    ].sort({\n                        \"ChatModal.useCallback[handleMessage].newMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useCallback[handleMessage].newMessages\"]);\n                    console.log('✅ [ChatModal] Added new message, total messages:', newMessages.length);\n                    return newMessages;\n                }\n            }[\"ChatModal.useCallback[handleMessage]\"]);\n        }\n    }[\"ChatModal.useCallback[handleMessage]\"], [\n        myUserId\n    ]);\n    // Handle trade status updates\n    const handleTradeStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleTradeStatusUpdate]\": (data)=>{\n            console.log(\"[ChatModal] Trade status update received:\", data);\n            if (data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId)) {\n                setCurrentTradeStatus(data.status);\n                console.log(\"[ChatModal] Updated trade status to: \".concat(data.status));\n                // Update the state context\n                updateChatModalState(chatRoomId, {\n                    currentTradeStatus: data.status,\n                    activeTrade: {\n                        ...activeTrade,\n                        status: data.status\n                    }\n                });\n            }\n        }\n    }[\"ChatModal.useCallback[handleTradeStatusUpdate]\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId,\n        activeTrade,\n        chatRoomId,\n        updateChatModalState\n    ]);\n    // Get the correct wallet address\n    const walletAddress = getWalletAddress();\n    console.log('🔍 [ChatModal] Wallet info:', {\n        privyWallet: user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : _user_wallet.address,\n        useWalletAddress: walletAddress,\n        isConnected,\n        chatRoomId\n    });\n    // Setup socket\n    const { sendMessage, authenticated, joinedRoom } = (0,_hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket)({\n        chatRoomId,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || myUserId || \"unknown\",\n        wallet: walletAddress || \"\",\n        onMessage: handleMessage,\n        onTradeStatus: handleTradeStatusUpdate\n    });\n    // Scroll to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }[\"ChatModal.useEffect\"], [\n        messages\n    ]);\n    // Set chat as open and register with state context\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            setOpenChatTradeId(chatRoomId);\n            removeUnreadChatMessagesForTrade(chatRoomId);\n            // Register this chat modal with the state context\n            registerChatModal(chatRoomId, {\n                chatRoomId,\n                activeTrade,\n                currentTradeStatus: (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status) || null,\n                isOperationInProgress: false\n            });\n            return ({\n                \"ChatModal.useEffect\": ()=>{\n                    setOpenChatTradeId(null);\n                    unregisterChatModal(chatRoomId);\n                }\n            })[\"ChatModal.useEffect\"];\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId,\n        setOpenChatTradeId,\n        removeUnreadChatMessagesForTrade,\n        registerChatModal,\n        unregisterChatModal,\n        activeTrade\n    ]);\n    // Fetch latest trade status when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            const fetchLatestTradeStatus = {\n                \"ChatModal.useEffect.fetchLatestTradeStatus\": async ()=>{\n                    const tradeId = (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId);\n                    if (tradeId) {\n                        try {\n                            console.log('🔄 [ChatModal] Fetching latest trade status for tradeId:', tradeId);\n                            const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                            const tradeResponse = await getTradeDetails(Number(tradeId));\n                            if (tradeResponse.status === 200 && tradeResponse.data.status !== currentTradeStatus) {\n                                console.log(\"\\uD83D\\uDD04 [ChatModal] Trade status updated: \".concat(currentTradeStatus, \" → \").concat(tradeResponse.data.status));\n                                setCurrentTradeStatus(tradeResponse.data.status);\n                                // Update the state context with the latest status\n                                updateChatModalState(chatRoomId, {\n                                    currentTradeStatus: tradeResponse.data.status,\n                                    activeTrade: {\n                                        ...activeTrade,\n                                        status: tradeResponse.data.status\n                                    }\n                                });\n                            }\n                        } catch (error) {\n                            console.error('❌ [ChatModal] Failed to fetch latest trade status:', error);\n                        }\n                    } else {\n                        console.log('⚠️ [ChatModal] No trade ID available for status fetch');\n                    }\n                }\n            }[\"ChatModal.useEffect.fetchLatestTradeStatus\"];\n            fetchLatestTradeStatus();\n        }\n    }[\"ChatModal.useEffect\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId,\n        chatRoomId,\n        updateChatModalState,\n        activeTrade,\n        currentTradeStatus\n    ]); // Run when either ID changes\n    // Send message\n    const handleSend = (e)=>{\n        e.preventDefault();\n        if (!input.trim() || !canChat) return;\n        const receiverId = isBuyer ? sellerId : buyerId;\n        console.log('🔍 [ChatModal] Sending message:', {\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim(),\n            canChat,\n            isEscrowed\n        });\n        sendMessage({\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim()\n        });\n        setInput(\"\");\n    };\n    // Enhanced action handlers that update status immediately and send system messages\n    const handleReleaseWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleReleaseWithStatusUpdate]\": async ()=>{\n            if (!onRelease) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow release...');\n                // Call the original release function\n                await onRelease();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('released');\n                console.log('✅ [ChatModal] Escrow released, status updated to released');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"\\uD83C\\uDF89 Escrow has been released! Trade completed successfully.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Release failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleReleaseWithStatusUpdate]\"], [\n        onRelease,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    const handleAcceptWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleAcceptWithStatusUpdate]\": async ()=>{\n            if (!onAccept) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow accept...');\n                // Call the original accept function\n                await onAccept();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('escrowed');\n                console.log('✅ [ChatModal] Escrow accepted, status updated to escrowed');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"✅ Escrow accepted! Buyer can now release funds when ready.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Accept failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleAcceptWithStatusUpdate]\"], [\n        onAccept,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    // Format time\n    const formatTime = (dateStr)=>{\n        const date = new Date(dateStr);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Format date separator\n    const formatDateSeparator = (date)=>{\n        const today = dayjs__WEBPACK_IMPORTED_MODULE_4___default()().startOf('day');\n        const msgDay = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).startOf('day');\n        if (msgDay.isSame(today)) return t('chat.today');\n        if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n        return msgDay.format('D MMM YYYY');\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.name) return receiverInfo.name;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) return receiverInfo.email;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) {\n            const wallet = receiverInfo.wallet;\n            return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n        }\n        return t('chat.user');\n    };\n    // Wallet formatting\n    const formatWalletAddress = (wallet)=>{\n        if (!wallet || wallet.length < 8) return wallet;\n        return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n    };\n    if (!(user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : _user_wallet1.address)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-700 text-center\",\n                        children: t('chat.connectWalletToChat')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 878,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 879,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 877,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 876,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!authenticated || !joinedRoom) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-gray-700 text-center\",\n                    children: t('chat.connectingToChat')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 894,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 893,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 892,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: notificationAudioRef,\n                src: \"/sounds/notification.mp3\",\n                preload: \"auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 902,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-2 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 910,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 ml-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_name = receiverInfo.name) === null || _receiverInfo_name === void 0 ? void 0 : _receiverInfo_name.slice(0, 2).toUpperCase()) || (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_email = receiverInfo.email) === null || _receiverInfo_email === void 0 ? void 0 : _receiverInfo_email.slice(0, 2).toUpperCase()) || \"?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute bottom-0 right-0 w-3 h-3 bg-emerald-500 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900 text-sm\",\n                                        children: getDisplayName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: receiverInfo.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 926,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 font-mono\",\n                                        children: formatWalletAddress(receiverInfo.wallet)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 905,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b \".concat(isEscrowReleased ? 'bg-green-50 border-green-100' : isEscrowed ? 'bg-orange-50 border-orange-100' : isPendingAcceptance ? 'bg-blue-50 border-blue-100' : 'bg-amber-50 border-amber-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isEscrowReleased ? 'bg-green-600' : isEscrowed ? 'bg-[#FF6600]' : isPendingAcceptance ? 'bg-blue-500' : 'bg-amber-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-slate-700\",\n                                    children: isEscrowReleased ? 'Trade Completed' : isEscrowed ? 'Escrow Active' : isPendingAcceptance ? 'Awaiting Seller Acceptance' : 'Pre-Purchase Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500\",\n                            children: isEscrowReleased ? 'Funds released successfully' : isEscrowed ? 'Funds secured on-chain' : isPendingAcceptance ? 'Seller needs to accept escrow' : 'Discussing before purchase'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 947,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 938,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto max-h-[400px]\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-orange-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: isEscrowed ? 'Escrow Chat Started' : 'Start the Conversation'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 max-w-xs\",\n                                children: isEscrowed ? 'Your funds are secured. Chat with the other party about the trade details.' : 'Discuss the details before making a purchase. Ask questions and clarify expectations.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 994,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 985,\n                        columnNumber: 11\n                    }, undefined) : messages.map((msg, idx)=>{\n                        const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n                        const showDate = idx === 0 || msgDate !== formatDateSeparator(new Date(messages[idx - 1].createdAt));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\",\n                                        children: msgDate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col \".concat(msg.senderId === myUserId ? \"items-end\" : \"items-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: msg.senderId === myUserId ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\" : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed whitespace-pre-line\",\n                                                children: msg.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 mt-1 \".concat(msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: msg.createdAt ? formatTime(msg.createdAt) : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                msg.senderId === myUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, msg.id || \"msg-\".concat(idx), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1033,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 983,\n                columnNumber: 7\n            }, undefined),\n            activeTrade && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Trade Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium capitalize \".concat((currentTradeStatus || activeTrade.status) === 'escrowed' ? 'bg-blue-100 text-blue-800' : (currentTradeStatus || activeTrade.status) === 'completed' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'released' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'pending_acceptance' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'),\n                                        children: currentTradeStatus || activeTrade.status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1041,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTrade.disputeStatus && activeTrade.disputeStatus !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Dispute:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).bgColor, \" \").concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).color),\n                                        children: (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1056,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1040,\n                        columnNumber: 11\n                    }, undefined),\n                    shouldShowTradeActions && isConnected && solanaWallet && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 p-4 bg-slate-50 rounded-lg\",\n                        children: [\n                            isBuyer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canBuyerRelease && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleReleaseWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Releasing...' : t('chat.releaseFunds')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-700 font-medium\",\n                                                        children: \"Waiting for seller to accept escrow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 text-center mt-1\",\n                                                children: \"You'll be able to release funds once the seller accepts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1091,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('buyer') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('buyer'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1121,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            isSeller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canSellerAccept && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleAcceptWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Accepting...' : 'Accept Escrow'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1133,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isEscrowed && !isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-700 font-medium\",\n                                                        children: \"Escrow accepted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1150,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 text-center mt-1\",\n                                                children: \"Waiting for buyer to release funds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('seller'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    !canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-800 text-center\",\n                                            children: (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.disputeStatus) && activeTrade.disputeStatus !== 'none' ? \"Dispute status: \".concat(activeTrade.disputeStatus) : 'Dispute option available within 2 days of escrow creation'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1069,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowReleased && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"Trade Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1206,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-700\",\n                                            children: \"Escrow has been released. This trade is now complete.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1205,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1202,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && isUserAuthorized && (!isConnected || !solanaWallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Wallet Connection Required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700\",\n                                            children: \"Please connect your Solana wallet to perform trade actions like releasing funds or initiating disputes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1224,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1222,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && (!user || !isBuyer && !isSeller) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-red-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Access Restricted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: \"Only the buyer and seller can perform trade actions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1241,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1235,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1234,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'open' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-800\",\n                            children: \"A dispute has been initiated. A moderator will review this case shortly.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1252,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1251,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'resolved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-800\",\n                            children: \"The dispute for this trade has been resolved by a moderator.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1260,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1259,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1038,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"px-4 py-3 bg-slate-50 border-t border-slate-200\",\n                onSubmit: handleSend,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: canChat ? t('chat.typeMessage') : 'Connect wallet to chat...',\n                                className: \"flex-1 bg-white rounded-lg px-4 py-3 text-sm outline-none border border-slate-300 focus:border-[#FF6600] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 placeholder:text-slate-500 transition-all duration-200\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                disabled: !canChat\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1271,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"p-3 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm \".concat(canChat && input.trim() ? 'bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2' : 'bg-slate-300 cursor-not-allowed opacity-50'),\n                                disabled: !input.trim() || !canChat,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1288,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1279,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1270,\n                        columnNumber: 9\n                    }, undefined),\n                    !canChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-gray-600 text-center\",\n                        children: !isUserAuthorized ? 'Only buyer and seller can chat' : !isConnected ? 'Please connect your wallet to chat' : 'Wallet connection required'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1292,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n        lineNumber: 901,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModal, \"2g78kYvhHoVUr0NyP2TaS4Jx4eA=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_8__.useChatModalState,\n        _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket\n    ];\n});\n_c = ChatModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatModal);\nvar _c;\n$RefreshReg$(_c, \"ChatModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\n"));

/***/ })

});