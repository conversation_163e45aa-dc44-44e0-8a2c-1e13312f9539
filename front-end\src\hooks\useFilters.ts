import { useState, useCallback, useMemo } from 'react';

// Type for sort options
export type SortOption = 'marketCap' | 'price' | 'newest';

// Define the interface for the filter state
export interface FilterState {
  category: string;
  search: string;
  sortBy: SortOption;
  filterVerified: boolean | null; // true = verified only, false = unverified only, null = all
}

// Define the interface for the hook return value
interface UseFiltersReturn {
  filters: FilterState;
  setCategory: (category: string) => void;
  setSearch: (search: string) => void;
  setSortBy: (sortBy: SortOption) => void;
  setFilterVerified: (filter: boolean | null) => void;
  resetFilters: () => void;
}

/**
 * Hook for managing filter state
 *
 * This hook provides:
 * - The current filter state
 * - Functions to update individual filters
 * - A function to reset all filters to their default values
 */
export function useFilters(
  initialFilters?: Partial<FilterState>
): UseFiltersReturn {
  // Default filter values - memoize to prevent recreation
  const defaultFilters: FilterState = useMemo(
    () => ({
      category: 'All',
      search: '',
      sortBy: 'newest' as SortOption,
      filterVerified: null,
    }),
    []
  );

  // Initialize state with defaults and any overrides
  const [filters, setFilters] = useState<FilterState>(() => ({
    ...defaultFilters,
    ...initialFilters,
  }));

  // Functions to update individual filters
  const setCategory = useCallback((category: string) => {
    setFilters((prev) => ({ ...prev, category }));
  }, []);

  const setSearch = useCallback((search: string) => {
    setFilters((prev) => ({ ...prev, search }));
  }, []);

  const setSortBy = useCallback((sortBy: SortOption) => {
    setFilters((prev) => ({ ...prev, sortBy }));
  }, []);

  const setFilterVerified = useCallback((filterVerified: boolean | null) => {
    setFilters((prev) => ({ ...prev, filterVerified }));
  }, []);

  // Function to reset all filters to their default values
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, [defaultFilters]);

  // Return the complete hook interface
  return useMemo(
    () => ({
      filters,
      setCategory,
      setSearch,
      setSortBy,
      setFilterVerified,
      resetFilters,
    }),
    [
      filters,
      setCategory,
      setSearch,
      setSortBy,
      setFilterVerified,
      resetFilters,
    ]
  );
}
