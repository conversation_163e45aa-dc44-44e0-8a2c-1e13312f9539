'use client';
import { useEffect, useRef } from 'react';
import { useSocket } from '@/contexts/SocketProvider';
import { useUnreadChatMessages, useAppContext } from '@/contexts/AppContext';

const ChatNotificationListener = () => {
  const socket = useSocket();
  const { addUnreadChatMessage } = useUnreadChatMessages();
  const { state } = useAppContext();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (!socket) return;
    const handleMessage = (msg: any) => {
      // Only add to unread if the message is for the current user and the chat is not open
      const userBoStr = typeof window !== 'undefined' ? localStorage.getItem('userBo') : null;
      const userBo = userBoStr ? JSON.parse(userBoStr) : null;
      if (!userBo) return;
      if (
        msg.receiverId === userBo.id &&
        String(state.openChatTradeId) !== String(msg.tradeId)
      ) {
        addUnreadChatMessage({
          id: msg.id,
          tradeId: msg.tradeId,
          senderId: msg.senderId,
          receiverId: msg.receiverId,
          message: msg.message,
          createdAt: msg.createdAt,
          chatRoomId: msg.chatRoomId, // Include chatRoomId from the socket message
        });
        // Play sound for new unread message
        if (audioRef.current) {
          audioRef.current.currentTime = 0;
          audioRef.current.play();
        }
      }
    };
    socket.on('message', handleMessage);
    return () => {
      socket.off('message', handleMessage);
    };
  }, [socket, addUnreadChatMessage, state.openChatTradeId]);

  return <audio ref={audioRef} src="/sounds/notification.mp3" preload="auto" style={{ display: 'none' }} />;
};

export default ChatNotificationListener; 