"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wallet-standard";
exports.ids = ["vendor-chunks/@wallet-standard"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wallet-standard/app/lib/esm/wallets.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wallet-standard/app/lib/esm/wallets.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEPRECATED_getWallets: () => (/* binding */ DEPRECATED_getWallets),\n/* harmony export */   getWallets: () => (/* binding */ getWallets)\n/* harmony export */ });\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _AppReadyEvent_detail;\nlet wallets = undefined;\nconst registeredWalletsSet = new Set();\nfunction addRegisteredWallet(wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.add(wallet);\n}\nfunction removeRegisteredWallet(wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.delete(wallet);\n}\nconst listeners = {};\n/**\n * Get an API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * When called for the first time --\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowAppReadyEvent} to notify each Wallet that the app is ready\n * to register it.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to listen for a notification\n * from each Wallet that the Wallet is ready to be registered by the app.\n *\n * This combination of event dispatch and listener guarantees that each Wallet will be registered synchronously as soon\n * as the app is ready whether the app loads before or after each Wallet.\n *\n * @return API for getting, listening for, and registering Wallets.\n *\n * @group App\n */\nfunction getWallets() {\n    if (wallets)\n        return wallets;\n    wallets = Object.freeze({ register, get, on });\n    if (typeof window === 'undefined')\n        return wallets;\n    const api = Object.freeze({ register });\n    try {\n        window.addEventListener('wallet-standard:register-wallet', ({ detail: callback }) => callback(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:register-wallet event listener could not be added\\n', error);\n    }\n    try {\n        window.dispatchEvent(new AppReadyEvent(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:app-ready event could not be dispatched\\n', error);\n    }\n    return wallets;\n}\nfunction register(...wallets) {\n    // Filter out wallets that have already been registered.\n    // This prevents the same wallet from being registered twice, but it also prevents wallets from being\n    // unregistered by reusing a reference to the wallet to obtain the unregister function for it.\n    wallets = wallets.filter((wallet) => !registeredWalletsSet.has(wallet));\n    // If there are no new wallets to register, just return a no-op unregister function.\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    if (!wallets.length)\n        return () => { };\n    wallets.forEach((wallet) => addRegisteredWallet(wallet));\n    listeners['register']?.forEach((listener) => guard(() => listener(...wallets)));\n    // Return a function that unregisters the registered wallets.\n    return function unregister() {\n        wallets.forEach((wallet) => removeRegisteredWallet(wallet));\n        listeners['unregister']?.forEach((listener) => guard(() => listener(...wallets)));\n    };\n}\nlet cachedWalletsArray;\nfunction get() {\n    if (!cachedWalletsArray) {\n        cachedWalletsArray = [...registeredWalletsSet];\n    }\n    return cachedWalletsArray;\n}\nfunction on(event, listener) {\n    listeners[event]?.push(listener) || (listeners[event] = [listener]);\n    // Return a function that removes the event listener.\n    return function off() {\n        listeners[event] = listeners[event]?.filter((existingListener) => listener !== existingListener);\n    };\n}\nfunction guard(callback) {\n    try {\n        callback();\n    }\n    catch (error) {\n        console.error(error);\n    }\n}\nclass AppReadyEvent extends Event {\n    get detail() {\n        return __classPrivateFieldGet(this, _AppReadyEvent_detail, \"f\");\n    }\n    get type() {\n        return 'wallet-standard:app-ready';\n    }\n    constructor(api) {\n        super('wallet-standard:app-ready', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        _AppReadyEvent_detail.set(this, void 0);\n        __classPrivateFieldSet(this, _AppReadyEvent_detail, api, \"f\");\n    }\n    /** @deprecated */\n    preventDefault() {\n        throw new Error('preventDefault cannot be called');\n    }\n    /** @deprecated */\n    stopImmediatePropagation() {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n    /** @deprecated */\n    stopPropagation() {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n_AppReadyEvent_detail = new WeakMap();\n/**\n * @deprecated Use {@link getWallets} instead.\n *\n * @group Deprecated\n */\nfunction DEPRECATED_getWallets() {\n    if (wallets)\n        return wallets;\n    wallets = getWallets();\n    if (typeof window === 'undefined')\n        return wallets;\n    const callbacks = window.navigator.wallets || [];\n    if (!Array.isArray(callbacks)) {\n        console.error('window.navigator.wallets is not an array');\n        return wallets;\n    }\n    const { register } = wallets;\n    const push = (...callbacks) => callbacks.forEach((callback) => guard(() => callback({ register })));\n    try {\n        Object.defineProperty(window.navigator, 'wallets', {\n            value: Object.freeze({ push }),\n        });\n    }\n    catch (error) {\n        console.error('window.navigator.wallets could not be set');\n        return wallets;\n    }\n    push(...callbacks);\n    return wallets;\n}\n//# sourceMappingURL=wallets.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/app/lib/esm/wallets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/connect.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/connect.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* binding */ Connect),\n/* harmony export */   StandardConnect: () => (/* binding */ StandardConnect)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardConnect = 'standard:connect';\n/**\n * @deprecated Use {@link StandardConnect} instead.\n *\n * @group Deprecated\n */\nconst Connect = StandardConnect;\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXEB3YWxsZXQtc3RhbmRhcmRcXGZlYXR1cmVzXFxsaWJcXGVzbVxcY29ubmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogTmFtZSBvZiB0aGUgZmVhdHVyZS4gKi9cbmV4cG9ydCBjb25zdCBTdGFuZGFyZENvbm5lY3QgPSAnc3RhbmRhcmQ6Y29ubmVjdCc7XG4vKipcbiAqIEBkZXByZWNhdGVkIFVzZSB7QGxpbmsgU3RhbmRhcmRDb25uZWN0fSBpbnN0ZWFkLlxuICpcbiAqIEBncm91cCBEZXByZWNhdGVkXG4gKi9cbmV4cG9ydCBjb25zdCBDb25uZWN0ID0gU3RhbmRhcmRDb25uZWN0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/disconnect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/disconnect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Disconnect: () => (/* binding */ Disconnect),\n/* harmony export */   StandardDisconnect: () => (/* binding */ StandardDisconnect)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardDisconnect = 'standard:disconnect';\n/**\n * @deprecated Use {@link StandardDisconnect} instead.\n *\n * @group Deprecated\n */\nconst Disconnect = StandardDisconnect;\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2Rpc2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQSxvQkFBb0IsMEJBQTBCO0FBQzlDO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxub2RlX21vZHVsZXNcXEB3YWxsZXQtc3RhbmRhcmRcXGZlYXR1cmVzXFxsaWJcXGVzbVxcZGlzY29ubmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogTmFtZSBvZiB0aGUgZmVhdHVyZS4gKi9cbmV4cG9ydCBjb25zdCBTdGFuZGFyZERpc2Nvbm5lY3QgPSAnc3RhbmRhcmQ6ZGlzY29ubmVjdCc7XG4vKipcbiAqIEBkZXByZWNhdGVkIFVzZSB7QGxpbmsgU3RhbmRhcmREaXNjb25uZWN0fSBpbnN0ZWFkLlxuICpcbiAqIEBncm91cCBEZXByZWNhdGVkXG4gKi9cbmV4cG9ydCBjb25zdCBEaXNjb25uZWN0ID0gU3RhbmRhcmREaXNjb25uZWN0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGlzY29ubmVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/events.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/events.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Events: () => (/* binding */ Events),\n/* harmony export */   StandardEvents: () => (/* binding */ StandardEvents)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardEvents = 'standard:events';\n/**\n * @deprecated Use {@link StandardEvents} instead.\n *\n * @group Deprecated\n */\nconst Events = StandardEvents;\n//# sourceMappingURL=events.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2V2ZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcQHdhbGxldC1zdGFuZGFyZFxcZmVhdHVyZXNcXGxpYlxcZXNtXFxldmVudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIE5hbWUgb2YgdGhlIGZlYXR1cmUuICovXG5leHBvcnQgY29uc3QgU3RhbmRhcmRFdmVudHMgPSAnc3RhbmRhcmQ6ZXZlbnRzJztcbi8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIHtAbGluayBTdGFuZGFyZEV2ZW50c30gaW5zdGVhZC5cbiAqXG4gKiBAZ3JvdXAgRGVwcmVjYXRlZFxuICovXG5leHBvcnQgY29uc3QgRXZlbnRzID0gU3RhbmRhcmRFdmVudHM7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ldmVudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/events.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/wallet/lib/esm/util.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wallet-standard/wallet/lib/esm/util.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReadonlyWalletAccount: () => (/* binding */ ReadonlyWalletAccount),\n/* harmony export */   arraysEqual: () => (/* binding */ arraysEqual),\n/* harmony export */   bytesEqual: () => (/* binding */ bytesEqual),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   guard: () => (/* binding */ guard),\n/* harmony export */   pick: () => (/* binding */ pick)\n/* harmony export */ });\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _ReadonlyWalletAccount_address, _ReadonlyWalletAccount_publicKey, _ReadonlyWalletAccount_chains, _ReadonlyWalletAccount_features, _ReadonlyWalletAccount_label, _ReadonlyWalletAccount_icon;\n/**\n * Base implementation of a {@link \"@wallet-standard/base\".WalletAccount} to be used or extended by a\n * {@link \"@wallet-standard/base\".Wallet}.\n *\n * `WalletAccount` properties must be read-only. This class enforces this by making all properties\n * [truly private](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes/Private_class_fields) and\n * read-only, using getters for access, returning copies instead of references, and calling\n * [Object.freeze](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze)\n * on the instance.\n *\n * @group Account\n */\nclass ReadonlyWalletAccount {\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.address | WalletAccount::address} */\n    get address() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_address, \"f\");\n    }\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.publicKey | WalletAccount::publicKey} */\n    get publicKey() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_publicKey, \"f\").slice();\n    }\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.chains | WalletAccount::chains} */\n    get chains() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_chains, \"f\").slice();\n    }\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.features | WalletAccount::features} */\n    get features() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_features, \"f\").slice();\n    }\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.label | WalletAccount::label} */\n    get label() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_label, \"f\");\n    }\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.icon | WalletAccount::icon} */\n    get icon() {\n        return __classPrivateFieldGet(this, _ReadonlyWalletAccount_icon, \"f\");\n    }\n    /**\n     * Create and freeze a read-only account.\n     *\n     * @param account Account to copy properties from.\n     */\n    constructor(account) {\n        _ReadonlyWalletAccount_address.set(this, void 0);\n        _ReadonlyWalletAccount_publicKey.set(this, void 0);\n        _ReadonlyWalletAccount_chains.set(this, void 0);\n        _ReadonlyWalletAccount_features.set(this, void 0);\n        _ReadonlyWalletAccount_label.set(this, void 0);\n        _ReadonlyWalletAccount_icon.set(this, void 0);\n        if (new.target === ReadonlyWalletAccount) {\n            Object.freeze(this);\n        }\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_address, account.address, \"f\");\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_publicKey, account.publicKey.slice(), \"f\");\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_chains, account.chains.slice(), \"f\");\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_features, account.features.slice(), \"f\");\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_label, account.label, \"f\");\n        __classPrivateFieldSet(this, _ReadonlyWalletAccount_icon, account.icon, \"f\");\n    }\n}\n_ReadonlyWalletAccount_address = new WeakMap(), _ReadonlyWalletAccount_publicKey = new WeakMap(), _ReadonlyWalletAccount_chains = new WeakMap(), _ReadonlyWalletAccount_features = new WeakMap(), _ReadonlyWalletAccount_label = new WeakMap(), _ReadonlyWalletAccount_icon = new WeakMap();\n/**\n * Efficiently compare {@link Indexed} arrays (e.g. `Array` and `Uint8Array`).\n *\n * @param a An array.\n * @param b Another array.\n *\n * @return `true` if the arrays have the same length and elements, `false` otherwise.\n *\n * @group Util\n */\nfunction arraysEqual(a, b) {\n    if (a === b)\n        return true;\n    const length = a.length;\n    if (length !== b.length)\n        return false;\n    for (let i = 0; i < length; i++) {\n        if (a[i] !== b[i])\n            return false;\n    }\n    return true;\n}\n/**\n * Efficiently compare byte arrays, using {@link arraysEqual}.\n *\n * @param a A byte array.\n * @param b Another byte array.\n *\n * @return `true` if the byte arrays have the same length and bytes, `false` otherwise.\n *\n * @group Util\n */\nfunction bytesEqual(a, b) {\n    return arraysEqual(a, b);\n}\n/**\n * Efficiently concatenate byte arrays without modifying them.\n *\n * @param first  A byte array.\n * @param others Additional byte arrays.\n *\n * @return New byte array containing the concatenation of all the byte arrays.\n *\n * @group Util\n */\nfunction concatBytes(first, ...others) {\n    const length = others.reduce((length, bytes) => length + bytes.length, first.length);\n    const bytes = new Uint8Array(length);\n    bytes.set(first, 0);\n    for (const other of others) {\n        bytes.set(other, bytes.length);\n    }\n    return bytes;\n}\n/**\n * Create a new object with a subset of fields from a source object.\n *\n * @param source Object to pick fields from.\n * @param keys   Names of fields to pick.\n *\n * @return New object with only the picked fields.\n *\n * @group Util\n */\nfunction pick(source, ...keys) {\n    const picked = {};\n    for (const key of keys) {\n        picked[key] = source[key];\n    }\n    return picked;\n}\n/**\n * Call a callback function, catch an error if it throws, and log the error without rethrowing.\n *\n * @param callback Function to call.\n *\n * @group Util\n */\nfunction guard(callback) {\n    try {\n        callback();\n    }\n    catch (error) {\n        console.error(error);\n    }\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/wallet/lib/esm/util.js\n");

/***/ })

};
;