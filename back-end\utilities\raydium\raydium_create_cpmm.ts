import { BN } from "@coral-xyz/anchor";
import {
  CREATE_CPMM_POOL_FEE_ACC,
  CREATE_CPMM_POOL_PROGRAM,
  Raydium,
  TxVersion,
  DEVNET_PROGRAM_ID,
  getCpmmPdaAmmConfigId,
  getCpmmPdaPoolId,
} from "@raydium-io/raydium-sdk-v2";
import { MintLayout } from "@solana/spl-token";
import {
  Connection,
  Keypair,
  PublicKey,
  TransactionInstruction,
} from "@solana/web3.js";
import { isValidConnection } from "../isValidConnection";

export async function raydiumCreateCpmm(
  mintA: PublicKey,
  mintB: PublicKey,
  mintAAmount: BN,
  mintBAmount: BN,
  startTime: BN,
  owner: PublicKey | Keypair,
  connection: Connection,
  network: "devnet" | "mainnet",
): Promise<{ instructions: TransactionInstruction[]; poolAddress: PublicKey }> {
  const validConnection = isValidConnection(connection);
  if (!validConnection) {
    throw new Error("Invalid connection");
  }
  const raydium = await Raydium.load({
    owner,
    connection,
    cluster: network,
  });

  const mintAInfo = await raydium.token.getTokenInfo(mintA.toBase58());
  if (!mintAInfo) {
    throw new Error("MintA token info not found");
  }
  const mintBInfo = await raydium.token.getTokenInfo(mintB.toBase58());
  if (!mintBInfo) {
    throw new Error("MintB token info not found");
  }

  const feeConfigs = await raydium.api.getCpmmConfigs();
  if (!feeConfigs) {
    throw new Error("No fee configs found");
  }

  if (raydium.cluster === "devnet") {
    feeConfigs.forEach((config) => {
      config.id = getCpmmPdaAmmConfigId(
        DEVNET_PROGRAM_ID.CREATE_CPMM_POOL_PROGRAM,
        config.index,
      ).publicKey.toBase58();
    });
  }

  const { builder, extInfo } = await raydium.cpmm.createPool({
    programId:
      network == "mainnet"
        ? CREATE_CPMM_POOL_PROGRAM
        : DEVNET_PROGRAM_ID.CREATE_CPMM_POOL_PROGRAM,
    poolFeeAccount:
      network == "mainnet"
        ? CREATE_CPMM_POOL_FEE_ACC
        : DEVNET_PROGRAM_ID.CREATE_CPMM_POOL_FEE_ACC,
    mintA: mintAInfo,
    mintB: mintBInfo,
    mintAAmount,
    mintBAmount,
    startTime,
    feeConfig: feeConfigs[0],
    associatedOnly: false,
    ownerInfo: {
      useSOLBalance: true,
    },
    txVersion: TxVersion.V0,
    // computeBudgetConfig: {
    //   units: 600000,
    //   microLamports: ********,
    // },
  });

  const { transaction } = builder.build();

  return {
    instructions: transaction.instructions,
    poolAddress: extInfo.address.poolId,
  };
}