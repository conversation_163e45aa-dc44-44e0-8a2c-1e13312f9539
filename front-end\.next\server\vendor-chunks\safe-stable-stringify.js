"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/safe-stable-stringify";
exports.ids = ["vendor-chunks/safe-stable-stringify"];
exports.modules = {

/***/ "(ssr)/./node_modules/safe-stable-stringify/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/safe-stable-stringify/index.js ***!
  \*****************************************************/
/***/ ((module, exports) => {

eval("\n\nconst { hasOwnProperty } = Object.prototype\n\nconst stringify = configure()\n\n// @ts-expect-error\nstringify.configure = configure\n// @ts-expect-error\nstringify.stringify = stringify\n\n// @ts-expect-error\nstringify.default = stringify\n\n// @ts-expect-error used for named export\nexports.stringify = stringify\n// @ts-expect-error used for named export\nexports.configure = configure\n\nmodule.exports = stringify\n\n// eslint-disable-next-line no-control-regex\nconst strEscapeSequencesRegExp = /[\\u0000-\\u001f\\u0022\\u005c\\ud800-\\udfff]/\n\n// Escape C0 control characters, double quotes, the backslash and every code\n// unit with a numeric value in the inclusive range 0xD800 to 0xDFFF.\nfunction strEscape (str) {\n  // Some magic numbers that worked out fine while benchmarking with v8 8.0\n  if (str.length < 5000 && !strEscapeSequencesRegExp.test(str)) {\n    return `\"${str}\"`\n  }\n  return JSON.stringify(str)\n}\n\nfunction sort (array, comparator) {\n  // Insertion sort is very efficient for small input sizes, but it has a bad\n  // worst case complexity. Thus, use native array sort for bigger values.\n  if (array.length > 2e2 || comparator) {\n    return array.sort(comparator)\n  }\n  for (let i = 1; i < array.length; i++) {\n    const currentValue = array[i]\n    let position = i\n    while (position !== 0 && array[position - 1] > currentValue) {\n      array[position] = array[position - 1]\n      position--\n    }\n    array[position] = currentValue\n  }\n  return array\n}\n\nconst typedArrayPrototypeGetSymbolToStringTag =\n  Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(\n      Object.getPrototypeOf(\n        new Int8Array()\n      )\n    ),\n    Symbol.toStringTag\n  ).get\n\nfunction isTypedArrayWithEntries (value) {\n  return typedArrayPrototypeGetSymbolToStringTag.call(value) !== undefined && value.length !== 0\n}\n\nfunction stringifyTypedArray (array, separator, maximumBreadth) {\n  if (array.length < maximumBreadth) {\n    maximumBreadth = array.length\n  }\n  const whitespace = separator === ',' ? '' : ' '\n  let res = `\"0\":${whitespace}${array[0]}`\n  for (let i = 1; i < maximumBreadth; i++) {\n    res += `${separator}\"${i}\":${whitespace}${array[i]}`\n  }\n  return res\n}\n\nfunction getCircularValueOption (options) {\n  if (hasOwnProperty.call(options, 'circularValue')) {\n    const circularValue = options.circularValue\n    if (typeof circularValue === 'string') {\n      return `\"${circularValue}\"`\n    }\n    if (circularValue == null) {\n      return circularValue\n    }\n    if (circularValue === Error || circularValue === TypeError) {\n      return {\n        toString () {\n          throw new TypeError('Converting circular structure to JSON')\n        }\n      }\n    }\n    throw new TypeError('The \"circularValue\" argument must be of type string or the value null or undefined')\n  }\n  return '\"[Circular]\"'\n}\n\nfunction getDeterministicOption (options) {\n  let value\n  if (hasOwnProperty.call(options, 'deterministic')) {\n    value = options.deterministic\n    if (typeof value !== 'boolean' && typeof value !== 'function') {\n      throw new TypeError('The \"deterministic\" argument must be of type boolean or comparator function')\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getBooleanOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'boolean') {\n      throw new TypeError(`The \"${key}\" argument must be of type boolean`)\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getPositiveIntegerOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'number') {\n      throw new TypeError(`The \"${key}\" argument must be of type number`)\n    }\n    if (!Number.isInteger(value)) {\n      throw new TypeError(`The \"${key}\" argument must be an integer`)\n    }\n    if (value < 1) {\n      throw new RangeError(`The \"${key}\" argument must be >= 1`)\n    }\n  }\n  return value === undefined ? Infinity : value\n}\n\nfunction getItemCount (number) {\n  if (number === 1) {\n    return '1 item'\n  }\n  return `${number} items`\n}\n\nfunction getUniqueReplacerSet (replacerArray) {\n  const replacerSet = new Set()\n  for (const value of replacerArray) {\n    if (typeof value === 'string' || typeof value === 'number') {\n      replacerSet.add(String(value))\n    }\n  }\n  return replacerSet\n}\n\nfunction getStrictOption (options) {\n  if (hasOwnProperty.call(options, 'strict')) {\n    const value = options.strict\n    if (typeof value !== 'boolean') {\n      throw new TypeError('The \"strict\" argument must be of type boolean')\n    }\n    if (value) {\n      return (value) => {\n        let message = `Object can not safely be stringified. Received type ${typeof value}`\n        if (typeof value !== 'function') message += ` (${value.toString()})`\n        throw new Error(message)\n      }\n    }\n  }\n}\n\nfunction configure (options) {\n  options = { ...options }\n  const fail = getStrictOption(options)\n  if (fail) {\n    if (options.bigint === undefined) {\n      options.bigint = false\n    }\n    if (!('circularValue' in options)) {\n      options.circularValue = Error\n    }\n  }\n  const circularValue = getCircularValueOption(options)\n  const bigint = getBooleanOption(options, 'bigint')\n  const deterministic = getDeterministicOption(options)\n  const comparator = typeof deterministic === 'function' ? deterministic : undefined\n  const maximumDepth = getPositiveIntegerOption(options, 'maximumDepth')\n  const maximumBreadth = getPositiveIntegerOption(options, 'maximumBreadth')\n\n  function stringifyFnReplacer (key, parent, stack, replacer, spacer, indentation) {\n    let value = parent[key]\n\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n    value = replacer.call(parent, key, value)\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n        let join = ','\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let whitespace = ''\n        let separator = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (deterministic && !isTypedArrayWithEntries(value)) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyFnReplacer(key, value, stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":${whitespace}\"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyArrayReplacer (key, value, stack, replacer, spacer, indentation) {\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        const originalIndentation = indentation\n        let res = ''\n        let join = ','\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n        stack.push(value)\n        let whitespace = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        let separator = ''\n        for (const key of replacer) {\n          const tmp = stringifyArrayReplacer(key, value[key], stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyIndent (key, value, stack, spacer, indentation) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again.\n          if (typeof value !== 'object') {\n            return stringifyIndent(key, value, stack, spacer, indentation)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          indentation += spacer\n          let res = `\\n${indentation}`\n          const join = `,\\n${indentation}`\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          res += `\\n${originalIndentation}`\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        indentation += spacer\n        const join = `,\\n${indentation}`\n        let res = ''\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, join, maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = join\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyIndent(key, value[key], stack, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}: ${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\": \"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (separator !== '') {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifySimple (key, value, stack) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again\n          if (typeof value !== 'object') {\n            return stringifySimple(key, value, stack)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n\n        const hasLength = value.length !== undefined\n        if (hasLength && Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifySimple(String(i), value[i], stack)\n            res += tmp !== undefined ? tmp : 'null'\n            res += ','\n          }\n          const tmp = stringifySimple(String(i), value[i], stack)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `,\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (hasLength && isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, ',', maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = ','\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifySimple(key, value[key], stack)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${tmp}`\n            separator = ','\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":\"${getItemCount(removedKeys)} not stringified\"`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringify (value, replacer, space) {\n    if (arguments.length > 1) {\n      let spacer = ''\n      if (typeof space === 'number') {\n        spacer = ' '.repeat(Math.min(space, 10))\n      } else if (typeof space === 'string') {\n        spacer = space.slice(0, 10)\n      }\n      if (replacer != null) {\n        if (typeof replacer === 'function') {\n          return stringifyFnReplacer('', { '': value }, [], replacer, spacer, '')\n        }\n        if (Array.isArray(replacer)) {\n          return stringifyArrayReplacer('', value, [], getUniqueReplacerSet(replacer), spacer, '')\n        }\n      }\n      if (spacer.length !== 0) {\n        return stringifyIndent('', value, [], spacer, '')\n      }\n    }\n    return stringifySimple('', value, [])\n  }\n\n  return stringify\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/safe-stable-stringify/index.js\n");

/***/ })

};
;