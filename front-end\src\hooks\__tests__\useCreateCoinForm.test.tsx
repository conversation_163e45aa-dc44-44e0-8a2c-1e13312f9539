// Mock error handling before imports
jest.mock("../../utils/errorHandling", () => ({
  ...jest.requireActual("../../utils/errorHandling"),
  showErrorToast: jest.fn(),
  showSuccessToast: jest.fn(),
}));

import { renderHook, act } from "@testing-library/react";

import { useCreateCoinForm } from "../useCreateCoinForm";
import * as errorHandling from "../../utils/errorHandling";

// Mock dependencies
jest.mock("../../contexts/AppContext", () => ({
  useAppContext: () => ({
    state: {
      userBo: { id: 123 },
    },
  }),
}));

jest.mock("../useWallet", () => ({
  useWallet: () => ({
    solanaWallet: { address: "mock-wallet-address" },
    isConnected: true,
    getWalletAddress: () => "mock-wallet-address",
  }),
}));

jest.mock("../../components/auth/create-coin-form/FormValidation", () => ({
  validateForm: jest.fn(() => ({})),
  validateName: jest.fn(() => ""),
  validateTicker: jest.fn(() => ""),
  validateWebsite: jest.fn(() => ""),
  validateSocial: jest.fn(() => ""),
  validateImage: jest.fn(() => ""),
}));

jest.mock("../../components/auth/create-coin-form/FormSubmission", () => ({
  submitForm: jest.fn(),
}));

jest.mock("../../utils/helpers", () => ({
  uploadToPinata: jest.fn(),
}));

jest.mock("../../utils/formValidation", () => ({
  isFormValid: jest.fn(() => true),
}));

// Error handling is already mocked above

describe("useCreateCoinForm", () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => "mock-url");
    global.URL.revokeObjectURL = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Form State Management", () => {
    it("should initialize with default form state", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      expect(result.current.formData).toEqual({
        name: "",
        ticker: "",
        description: "",
        picture: null,
        telegram: "",
        website: "",
        twitter: "",
        category: "",
      });

      expect(result.current.errors).toEqual({
        name: "",
        ticker: "",
        description: "",
        picture: "",
        telegram: "",
        website: "",
        twitter: "",
        category: "",
        api: "",
      });

      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.submitted).toBe(false);
      expect(result.current.previewUrl).toBeNull();
      expect(result.current.showOptions).toBe(false);
    });

    it("should update form data on change", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleChange({
          target: { name: "name", value: "Test Token" },
        } as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formData.name).toBe("Test Token");
    });

    it("should clear errors when user starts typing", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      // Set initial error state
      act(() => {
        result.current.errors.name = "Name is required";
      });

      act(() => {
        result.current.handleChange({
          target: { name: "name", value: "Test Token" },
        } as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.errors.name).toBe("");
    });

    it("should handle special characters in form fields", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      const specialChars = "Test 中文 🚀 @#$%^&*()";

      act(() => {
        result.current.handleChange({
          target: { name: "name", value: specialChars },
        } as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formData.name).toBe(specialChars);
    });

    it("should handle very long input values", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      const longValue = "a".repeat(10000);

      act(() => {
        result.current.handleChange({
          target: { name: "description", value: longValue },
        } as React.ChangeEvent<HTMLTextAreaElement>);
      });

      expect(result.current.formData.description).toBe(longValue);
    });
  });

  describe("Form Validation", () => {
    it("should trigger validation on blur", () => {
      const {
        validateName,
      } = require("../../components/auth/create-coin-form/FormValidation");
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleBlur({
          target: { name: "name", value: "Test" },
        } as React.FocusEvent<HTMLInputElement>);
      });

      expect(validateName).toHaveBeenCalledWith("Test");
    });

    it("should validate ticker format", () => {
      const {
        validateTicker,
      } = require("../../components/auth/create-coin-form/FormValidation");
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleBlur({
          target: { name: "ticker", value: "BTC" },
        } as React.FocusEvent<HTMLInputElement>);
      });

      expect(validateTicker).toHaveBeenCalledWith("BTC");
    });

    it("should validate website URL", () => {
      const {
        validateWebsite,
      } = require("../../components/auth/create-coin-form/FormValidation");
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleBlur({
          target: { name: "website", value: "https://example.com" },
        } as React.FocusEvent<HTMLInputElement>);
      });

      expect(validateWebsite).toHaveBeenCalledWith("https://example.com");
    });

    it("should validate social media handles", () => {
      const {
        validateSocial,
      } = require("../../components/auth/create-coin-form/FormValidation");
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleBlur({
          target: { name: "telegram", value: "@testuser" },
        } as React.FocusEvent<HTMLInputElement>);
      });

      expect(validateSocial).toHaveBeenCalledWith("@testuser", "telegram");
    });
  });

  describe("File Upload Handling", () => {
    const createMockFile = (
      size: number,
      type: string,
      name: string = "test.jpg"
    ): File => {
      const file = new File([""], name, { type });
      Object.defineProperty(file, "size", { value: size });
      return file;
    };

    it("should handle valid image file upload", () => {
      const {
        validateImage,
      } = require("../../components/auth/create-coin-form/FormValidation");
      validateImage.mockReturnValue("");

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );
      const mockFile = createMockFile(1024 * 1024, "image/jpeg");

      act(() => {
        result.current.handleFileChange({
          target: { files: [mockFile] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formData.picture).toBe(mockFile);
      expect(result.current.previewUrl).toBe("mock-url");
      expect(result.current.errors.picture).toBe("");
    });

    it("should handle invalid image file", () => {
      const {
        validateImage,
      } = require("../../components/auth/create-coin-form/FormValidation");
      validateImage.mockReturnValue("Invalid file type");

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );
      const mockFile = createMockFile(1024, "application/pdf");

      act(() => {
        result.current.handleFileChange({
          target: { files: [mockFile] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formData.picture).toBe(mockFile);
      expect(result.current.previewUrl).toBeNull();
      expect(result.current.errors.picture).toBe("Invalid file type");
    });

    it("should handle file size exceeding limit", () => {
      const {
        validateImage,
      } = require("../../components/auth/create-coin-form/FormValidation");
      validateImage.mockReturnValue("File too large");

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );
      const mockFile = createMockFile(10 * 1024 * 1024, "image/jpeg"); // 10MB

      act(() => {
        result.current.handleFileChange({
          target: { files: [mockFile] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.errors.picture).toBe("File too large");
    });

    it("should remove image and preview", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );
      const mockFile = createMockFile(1024, "image/jpeg");

      // First add an image
      act(() => {
        result.current.handleFileChange({
          target: { files: [mockFile] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      // Then remove it
      act(() => {
        result.current.removeImage();
      });

      expect(result.current.formData.picture).toBeNull();
      expect(result.current.previewUrl).toBeNull();
      expect(result.current.errors.picture).toBe("");
    });

    it("should handle no file selected", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleFileChange({
          target: { files: [] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formData.picture).toBeNull();
      expect(result.current.previewUrl).toBeNull();
    });
  });

  describe("Form Submission", () => {
    it("should handle successful form submission", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockResolvedValue({
        success: true,
        message: "Token created successfully",
      });
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(result.current.submitted).toBe(true);
      expect(errorHandling.showSuccessToast).toHaveBeenCalledWith(
        "Token created successfully"
      );
    });

    it("should handle form validation failure", async () => {
      const { isFormValid } = require("../../utils/formValidation");
      isFormValid.mockReturnValue(false);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.submitted).toBe(false);
    });

    it("should handle wallet not connected", async () => {
      // Mock wallet as disconnected
      jest.doMock("../useWallet", () => ({
        useWallet: () => ({
          solanaWallet: null,
          isConnected: false,
          getWalletAddress: () => null,
        }),
      }));

      const { isFormValid } = require("../../utils/formValidation");
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(errorHandling.showErrorToast).toHaveBeenCalledWith(
        errorHandling.TOAST_MESSAGES.WALLET.CONNECT_REQUIRED
      );
    });

    it("should handle image upload during submission", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");
      const { uploadToPinata } = require("../../utils/helpers");

      uploadToPinata.mockResolvedValue("https://pinata.com/image-url");
      submitForm.mockResolvedValue({ success: true, message: "Success" });
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );
      const mockFile = new File([""], "test.jpg", { type: "image/jpeg" });

      // Add image to form
      act(() => {
        result.current.handleFileChange({
          target: { files: [mockFile] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      });

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(uploadToPinata).toHaveBeenCalledWith(mockFile);
    });

    it("should handle submission error", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockRejectedValue(new Error("Network error"));
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(errorHandling.showErrorToast).toHaveBeenCalled();
      expect(result.current.isSubmitting).toBe(false);
    });

    it("should handle API error responses", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      const apiError = {
        response: {
          status: 400,
          data: { message: "Token name already exists" },
        },
      };

      submitForm.mockRejectedValue(apiError);
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(errorHandling.showErrorToast).toHaveBeenCalled();
    });

    it("should handle failed form submission result", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockResolvedValue({
        success: false,
        message: "Insufficient balance",
      });
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      expect(errorHandling.showErrorToast).toHaveBeenCalledWith(
        "Insufficient balance"
      );
      expect(result.current.submitted).toBe(false);
    });
  });

  describe("UI State Management", () => {
    it("should toggle options visibility", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      expect(result.current.showOptions).toBe(false);

      act(() => {
        result.current.toggleOptions();
      });

      expect(result.current.showOptions).toBe(true);

      act(() => {
        result.current.toggleOptions();
      });

      expect(result.current.showOptions).toBe(false);
    });

    it("should reset form after successful submission", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockResolvedValue({ success: true, message: "Success" });
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      // Fill form with data
      act(() => {
        result.current.handleChange({
          target: { name: "name", value: "Test Token" },
        } as React.ChangeEvent<HTMLInputElement>);
      });

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as any);
      });

      // Wait for timeout in submission handler
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 2100));
      });

      expect(result.current.formData.name).toBe("");
      expect(result.current.submitted).toBe(false);
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe("Edge Cases", () => {
    it("should handle null file input", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleFileChange({
          target: { files: null },
        } as any);
      });

      expect(result.current.formData.picture).toBeNull();
    });

    it("should handle undefined values in form changes", () => {
      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleChange({
          target: { name: "name", value: undefined },
        } as any);
      });

      expect(result.current.formData.name).toBeUndefined();
    });

    it("should handle rapid successive form submissions", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ success: true, message: "Success" }),
              1000
            )
          )
      );
      isFormValid.mockReturnValue(true);

      const { result } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      // Submit form twice rapidly
      act(() => {
        result.current.handleSubmit({ preventDefault: jest.fn() } as any);
        result.current.handleSubmit({ preventDefault: jest.fn() } as any);
      });

      expect(result.current.isSubmitting).toBe(true);
    });

    it("should handle component unmount during submission", async () => {
      const {
        submitForm,
      } = require("../../components/auth/create-coin-form/FormSubmission");
      const { isFormValid } = require("../../utils/formValidation");

      submitForm.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ success: true, message: "Success" }),
              1000
            )
          )
      );
      isFormValid.mockReturnValue(true);

      const { result, unmount } = renderHook(() =>
        useCreateCoinForm({ onClose: mockOnClose })
      );

      act(() => {
        result.current.handleSubmit({ preventDefault: jest.fn() } as any);
      });

      // Unmount component while submission is in progress
      unmount();

      // Should not throw error
      expect(() => unmount()).not.toThrow();
    });
  });
});
