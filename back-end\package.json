{"name": "FUNHI", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "concurrently \"npm run start:server\" ", "deploy": "pm2 start npm --name 'GrowGAMI API' -- start", "start:server": "node server.js", "dev:server": "nodemon server.js", "db:migrate": "node ./db/migrate.js", "test": "jest"}, "jest": {"testEnvironment": "node"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@raydium-io/raydium-sdk-v2": "^0.1.139-alpha", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.95.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "bs58": "^6.0.0", "child_process": "^1.0.2", "cluster": "^0.7.7", "colors": "^1.4.0", "cors": "^2.8.5", "date-and-time": "^2.4.3", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "i18next": "^25.3.2", "i18next-fs-backend": "^2.6.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mysql": "^2.18.1", "mysql2": "^3.14.2", "node-cron": "^3.0.2", "node-fetch": "^3.3.2", "nodemon": "^3.0.1", "path": "^0.12.7", "qrcode": "^1.5.4", "sequelize": "^6.37.3", "sequelize-auto-migrations": "^1.0.3", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "web3": "4.0.1-alpha.5", "yarn": "^1.22.19"}, "devDependencies": {"@types/node": "^24.0.1", "concurrently": "^9.0.1", "jest": "^30.0.4", "morgan": "^1.10.0", "socket.io-client": "^4.8.1", "supertest": "^7.1.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}