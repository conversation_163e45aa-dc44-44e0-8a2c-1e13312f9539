import { motion } from 'framer-motion';
import React, { useMemo } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

interface TablePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems?: number;
  itemsPerPage?: number;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
  onPageSizeChange?: (pageSize: number) => void;
  loading?: boolean;
}

const TablePagination: React.FC<TablePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalItems,
  itemsPerPage = 10,
  showPageSize = false,
  pageSizeOptions = [10, 25, 50, 100],
  onPageSizeChange,
  loading = false,
}) => {
  // Generate page numbers to display
  const getVisiblePages = () => {
    const delta = 2; // Number of pages to show on each side of current page
    const pages: (number | string)[] = [];

    // Always show first page
    if (totalPages > 1) {
      pages.push(1);
    }

    // Add ellipsis if there's a gap
    if (currentPage - delta > 2) {
      pages.push('...');
    }

    // Add pages around current page
    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      pages.push(i);
    }

    // Add ellipsis if there's a gap
    if (currentPage + delta < totalPages - 1) {
      pages.push('...');
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  // Memoize visible pages calculation
  const visiblePages = useMemo(() => getVisiblePages(), [currentPage, totalPages]);

  // Memoize pagination stats calculation
  const paginationStats = useMemo(() => {
    const startItem = totalItems ? (currentPage - 1) * itemsPerPage + 1 : 0;
    const endItem = totalItems
      ? Math.min(currentPage * itemsPerPage, totalItems)
      : 0;
    
    return { startItem, endItem };
  }, [currentPage, totalItems, itemsPerPage]);

  const { startItem, endItem } = paginationStats;

  // Memoize button classes to avoid recalculation on every render
  const buttonClass = useMemo(() => `
    flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-500 
    bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700
    disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500
    transition-colors duration-200
  `, []);

  const getPageButtonClass = useMemo(() => (isActive: boolean) => `
    flex items-center justify-center w-10 h-10 text-sm font-medium
    ${
      isActive
        ? 'text-blue-600 bg-blue-50 border border-blue-300 z-10'
        : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700'
    }
    transition-colors duration-200
  `, []);

  if (totalPages <= 1) {
    return null;
  }

  return (
    <motion.div
      className="flex items-center justify-between px-6 py-4 bg-white border-t border-gray-200"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Results summary */}
      <div className="flex items-center text-sm text-gray-700">
        {totalItems && (
          <span>
            Showing <span className="font-medium">{startItem}</span> to{' '}
            <span className="font-medium">{endItem}</span> of{' '}
            <span className="font-medium">{totalItems}</span> results
          </span>
        )}

        {/* Page size selector */}
        {showPageSize && onPageSizeChange && (
          <div className="ml-6 flex items-center gap-2">
            <label htmlFor="pageSize" className="text-sm text-gray-700">
              Show:
            </label>
            <select
              id="pageSize"
              value={itemsPerPage}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        {/* First page */}
        <motion.button
          className={`${buttonClass} rounded-l-md`}
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1 || loading}
          aria-label="First page"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ChevronsLeft className="w-4 h-4" />
        </motion.button>

        {/* Previous page */}
        <motion.button
          className={buttonClass}
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1 || loading}
          aria-label="Previous page"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ChevronLeft className="w-4 h-4" />
        </motion.button>

        {/* Page numbers */}
        {visiblePages.map((page, index) => {
          if (page === '...') {
            return (
              <span
                key={`ellipsis-${index}`}
                className="flex items-center justify-center w-10 h-10 text-sm text-gray-500"
              >
                ...
              </span>
            );
          }

          const pageNum = page as number;
          const isActive = pageNum === currentPage;

          return (
            <motion.button
              key={pageNum}
              className={getPageButtonClass(isActive)}
              onClick={() => onPageChange(pageNum)}
              disabled={loading}
              aria-label={`Page ${pageNum}`}
              aria-current={isActive ? 'page' : undefined}
              whileHover={{ scale: isActive ? 1 : 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.05 }}
            >
              {pageNum}
            </motion.button>
          );
        })}

        {/* Next page */}
        <motion.button
          className={buttonClass}
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || loading}
          aria-label="Next page"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ChevronRight className="w-4 h-4" />
        </motion.button>

        {/* Last page */}
        <motion.button
          className={`${buttonClass} rounded-r-md`}
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages || loading}
          aria-label="Last page"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ChevronsRight className="w-4 h-4" />
        </motion.button>
      </div>
    </motion.div>
  );
};

export { TablePagination };
