"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scure";
exports.ids = ["vendor-chunks/@scure"];
exports.modules = {

/***/ "(ssr)/./node_modules/@scure/base/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@scure/base/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base16: () => (/* binding */ base16),\n/* harmony export */   base32: () => (/* binding */ base32),\n/* harmony export */   base32crockford: () => (/* binding */ base32crockford),\n/* harmony export */   base32hex: () => (/* binding */ base32hex),\n/* harmony export */   base32hexnopad: () => (/* binding */ base32hexnopad),\n/* harmony export */   base32nopad: () => (/* binding */ base32nopad),\n/* harmony export */   base58: () => (/* binding */ base58),\n/* harmony export */   base58check: () => (/* binding */ base58check),\n/* harmony export */   base58flickr: () => (/* binding */ base58flickr),\n/* harmony export */   base58xmr: () => (/* binding */ base58xmr),\n/* harmony export */   base58xrp: () => (/* binding */ base58xrp),\n/* harmony export */   base64: () => (/* binding */ base64),\n/* harmony export */   base64nopad: () => (/* binding */ base64nopad),\n/* harmony export */   base64url: () => (/* binding */ base64url),\n/* harmony export */   base64urlnopad: () => (/* binding */ base64urlnopad),\n/* harmony export */   bech32: () => (/* binding */ bech32),\n/* harmony export */   bech32m: () => (/* binding */ bech32m),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   bytesToString: () => (/* binding */ bytesToString),\n/* harmony export */   createBase58check: () => (/* binding */ createBase58check),\n/* harmony export */   hex: () => (/* binding */ hex),\n/* harmony export */   str: () => (/* binding */ str),\n/* harmony export */   stringToBytes: () => (/* binding */ stringToBytes),\n/* harmony export */   utf8: () => (/* binding */ utf8),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\nfunction isArrayOf(isString, arr) {\n    if (!Array.isArray(arr))\n        return false;\n    if (arr.length === 0)\n        return true;\n    if (isString) {\n        return arr.every((item) => typeof item === 'string');\n    }\n    else {\n        return arr.every((item) => Number.isSafeInteger(item));\n    }\n}\n// no abytes: seems to have 10% slowdown. Why?!\nfunction afn(input) {\n    if (typeof input !== 'function')\n        throw new Error('function expected');\n    return true;\n}\nfunction astr(label, input) {\n    if (typeof input !== 'string')\n        throw new Error(`${label}: string expected`);\n    return true;\n}\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`invalid integer: ${n}`);\n}\nfunction aArr(input) {\n    if (!Array.isArray(input))\n        throw new Error('array expected');\n}\nfunction astrArr(label, input) {\n    if (!isArrayOf(true, input))\n        throw new Error(`${label}: array of strings expected`);\n}\nfunction anumArr(label, input) {\n    if (!isArrayOf(false, input))\n        throw new Error(`${label}: array of numbers expected`);\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    const id = (a) => a;\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = args.map((x) => x.encode).reduceRight(wrap, id);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.map((x) => x.decode).reduce(wrap, id);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back.\n * Could also be array of strings.\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(letters) {\n    // mapping 1 to \"b\"\n    const lettersA = typeof letters === 'string' ? letters.split('') : letters;\n    const len = lettersA.length;\n    astrArr('alphabet', lettersA);\n    // mapping \"b\" to 1\n    const indexes = new Map(lettersA.map((l, i) => [l, i]));\n    return {\n        encode: (digits) => {\n            aArr(digits);\n            return digits.map((i) => {\n                if (!Number.isSafeInteger(i) || i < 0 || i >= len)\n                    throw new Error(`alphabet.encode: digit index outside alphabet \"${i}\". Allowed: ${letters}`);\n                return lettersA[i];\n            });\n        },\n        decode: (input) => {\n            aArr(input);\n            return input.map((letter) => {\n                astr('alphabet.decode', letter);\n                const i = indexes.get(letter);\n                if (i === undefined)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${letters}`);\n                return i;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    astr('join', separator);\n    return {\n        encode: (from) => {\n            astrArr('join.decode', from);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            astr('join.decode', to);\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    anumber(bits);\n    astr('padding', chr);\n    return {\n        encode(data) {\n            astrArr('padding.encode', data);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            astrArr('padding.decode', input);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('padding: invalid, string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                const last = end - 1;\n                const byte = last * bits;\n                if (byte % 8 === 0)\n                    throw new Error('padding: invalid, string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n    afn(fn);\n    return { encode: (from) => from, decode: (to) => fn(to) };\n}\n/**\n * Slow: O(n^2) time complexity\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: invalid from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: invalid to=${to}, base cannot be less than 2`);\n    aArr(data);\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data, (d) => {\n        anumber(d);\n        if (d < 0 || d >= from)\n            throw new Error(`invalid integer: ${d}`);\n        return d;\n    });\n    const dlen = digits.length;\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < dlen; i++) {\n            const digit = digits[i];\n            const fromCarry = from * carry;\n            const digitBase = fromCarry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                fromCarry / from !== carry ||\n                digitBase - digit !== fromCarry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            const div = digitBase / to;\n            carry = digitBase % to;\n            const rounded = Math.floor(div);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = (a, b) => (b === 0 ? a : gcd(b, a % b));\nconst radix2carry = /* @__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\nconst powers = /* @__PURE__ */ (() => {\n    let res = [];\n    for (let i = 0; i < 40; i++)\n        res.push(2 ** i);\n    return res;\n})();\n/**\n * Implemented with numbers, because BigInt is 5x slower\n */\nfunction convertRadix2(data, from, to, padding) {\n    aArr(data);\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const max = powers[from];\n    const mask = powers[to] - 1;\n    const res = [];\n    for (const n of data) {\n        anumber(n);\n        if (n >= max)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        const pow = powers[pos];\n        if (pow === undefined)\n            throw new Error('invalid carry');\n        carry &= pow - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry > 0)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    anumber(num);\n    const _256 = 2 ** 8;\n    return {\n        encode: (bytes) => {\n            if (!isBytes(bytes))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), _256, num);\n        },\n        decode: (digits) => {\n            anumArr('radix.decode', digits);\n            return Uint8Array.from(convertRadix(digits, num, _256));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    anumber(bits);\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!isBytes(bytes))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            anumArr('radix2.decode', digits);\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\nfunction unsafeWrapper(fn) {\n    afn(fn);\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\nfunction checksum(len, fn) {\n    anumber(len);\n    afn(fn);\n    return {\n        encode(data) {\n            if (!isBytes(data))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const sum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(sum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!isBytes(data))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const oldChecksum = data.slice(-len);\n            const newChecksum = fn(payload).slice(0, len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\n// prettier-ignore\nconst utils = {\n    alphabet, chain, checksum, convertRadix, convertRadix2, radix, radix2, join, padding,\n};\n// RFC 4648 aka RFC 3548\n// ---------------------\n/**\n * base16 encoding from RFC 4648.\n * @example\n * ```js\n * base16.encode(Uint8Array.from([0x12, 0xab]));\n * // => '12AB'\n * ```\n */\nconst base16 = chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\n/**\n * base32 encoding from RFC 4648. Has padding.\n * Use `base32nopad` for unpadded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ===='\n * base32.decode('CKVQ====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32 = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding.\n * Use `base32` for padded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ'\n * base32nopad.decode('CKVQ');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32nopad = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), join(''));\n/**\n * base32 encoding from RFC 4648. Padded. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hexnopad` for unpadded version.\n * @example\n * ```js\n * base32hex.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG===='\n * base32hex.decode('2ALG====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32hex = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hex` for padded version.\n * @example\n * ```js\n * base32hexnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG'\n * base32hexnopad.decode('2ALG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32hexnopad = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), join(''));\n/**\n * base32 encoding from RFC 4648. Doug Crockford's version.\n * https://www.crockford.com/base32.html\n * @example\n * ```js\n * base32crockford.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ANG'\n * base32crockford.decode('2ANG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32crockford = chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize((s) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\n// Built-in base64 conversion https://caniuse.com/mdn-javascript_builtins_uint8array_frombase64\n// TODO: temporarily set to false, trying to understand bugs\n// prettier-ignore\nconst hasBase64Builtin = /* @__PURE__ */ (() => typeof Uint8Array.from([]).toBase64 === 'function' &&\n    typeof Uint8Array.fromBase64 === 'function')();\n/**\n * base64 from RFC 4648. Padded.\n * Use `base64nopad` for unpadded version.\n * Also check out `base64url`, `base64urlnopad`.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nconst base64 = hasBase64Builtin ? {\n    encode(b) { abytes(b); return b.toBase64(); },\n    decode(s) {\n        astr('base64', s);\n        return Uint8Array.fromBase64(s, { lastChunkHandling: 'strict' });\n    },\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\n/**\n * base64 from RFC 4648. No padding.\n * Use `base64` for padded version.\n * @example\n * ```js\n * base64nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64nopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base64nopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. Padded.\n * Use `base64urlnopad` for unpadded version.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64url.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64url.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nconst base64url = hasBase64Builtin ? {\n    encode(b) { abytes(b); return b.toBase64({ alphabet: 'base64url' }); },\n    decode(s) { astr('base64', s); return Uint8Array.fromBase64(s, { alphabet: 'base64url' }); },\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. No padding.\n * Use `base64url` for padded version.\n * @example\n * ```js\n * base64urlnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64urlnopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base64urlnopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = /* @__NO_SIDE_EFFECTS__ */ (abc) => chain(radix(58), alphabet(abc), join(''));\n/**\n * base58: base64 without ambigous characters +, /, 0, O, I, l.\n * Quadratic (O(n^2)) - so, can't be used on large inputs.\n * @example\n * ```js\n * base58.decode('01abcdef');\n * // => '3UhJW'\n * ```\n */\nconst base58 = genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\n/**\n * base58: flickr version. Check out `base58`.\n */\nconst base58flickr = genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\n/**\n * base58: XRP version. Check out `base58`.\n */\nconst base58xrp = genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n/**\n * base58: XMR version. Check out `base58`.\n * Done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n * Block encoding significantly reduces quadratic complexity of base58.\n */\nconst base58xmr = {\n    encode(data) {\n        let res = '';\n        for (let i = 0; i < data.length; i += 8) {\n            const block = data.subarray(i, i + 8);\n            res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n        }\n        return res;\n    },\n    decode(str) {\n        let res = [];\n        for (let i = 0; i < str.length; i += 11) {\n            const slice = str.slice(i, i + 11);\n            const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n            const block = base58.decode(slice);\n            for (let j = 0; j < block.length - blockLen; j++) {\n                if (block[j] !== 0)\n                    throw new Error('base58xmr: wrong padding');\n            }\n            res = res.concat(Array.from(block.slice(block.length - blockLen)));\n        }\n        return Uint8Array.from(res);\n    },\n};\n/**\n * Method, which creates base58check encoder.\n * Requires function, calculating sha256.\n */\nconst createBase58check = (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\n/**\n * Use `createBase58check` instead.\n * @deprecated\n */\nconst base58check = createBase58check;\nconst BECH_ALPHABET = chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % powers[30]], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        astr('bech32.encode prefix', prefix);\n        if (isBytes(words))\n            words = Array.from(words);\n        anumArr('bech32.encode', words);\n        const plen = prefix.length;\n        if (plen === 0)\n            throw new TypeError(`Invalid prefix length ${plen}`);\n        const actualLength = plen + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        astr('bech32.decode input', str);\n        const slen = str.length;\n        if (slen < 8 || (limit !== false && slen > limit))\n            throw new TypeError(`invalid string length: ${slen} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        const sepIndex = lowered.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = lowered.slice(0, sepIndex);\n        const data = lowered.slice(sepIndex + 1);\n        if (data.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(data).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!data.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    function encodeFromBytes(prefix, bytes) {\n        return encode(prefix, toWords(bytes));\n    }\n    return {\n        encode,\n        decode,\n        encodeFromBytes,\n        decodeToBytes,\n        decodeUnsafe,\n        fromWords,\n        fromWordsUnsafe,\n        toWords,\n    };\n}\n/**\n * bech32 from BIP 173. Operates on words.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nconst bech32 = genBech32('bech32');\n/**\n * bech32m from BIP 350. Operates on words.\n * It was to mitigate `bech32` weaknesses.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nconst bech32m = genBech32('bech32m');\n/**\n * UTF-8-to-byte decoder. Uses built-in TextDecoder / TextEncoder.\n * @example\n * ```js\n * const b = utf8.decode(\"hey\"); // => new Uint8Array([ 104, 101, 121 ])\n * const str = utf8.encode(b); // \"hey\"\n * ```\n */\nconst utf8 = {\n    encode: (data) => new TextDecoder().decode(data),\n    decode: (str) => new TextEncoder().encode(str),\n};\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\n// prettier-ignore\nconst hasHexBuiltin = /* @__PURE__ */ (() => typeof Uint8Array.from([]).toHex === 'function' &&\n    typeof Uint8Array.fromHex === 'function')();\n// prettier-ignore\nconst hexBuiltin = {\n    encode(data) { abytes(data); return data.toHex(); },\n    decode(s) { astr('hex', s); return Uint8Array.fromHex(s); },\n};\n/**\n * hex string decoder. Uses built-in function, when available.\n * @example\n * ```js\n * const b = hex.decode(\"0102ff\"); // => new Uint8Array([ 1, 2, 255 ])\n * const str = hex.encode(b); // \"0102ff\"\n * ```\n */\nconst hex = hasHexBuiltin\n    ? hexBuiltin\n    : chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize((s) => {\n        if (typeof s !== 'string' || s.length % 2 !== 0)\n            throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n        return s.toLowerCase();\n    }));\n// prettier-ignore\nconst CODERS = {\n    utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n/** @deprecated */\nconst bytesToString = (type, bytes) => {\n    if (typeof type !== 'string' || !CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (!isBytes(bytes))\n        throw new TypeError('bytesToString() expects Uint8Array');\n    return CODERS[type].encode(bytes);\n};\n/** @deprecated */\nconst str = bytesToString; // as in python, but for bytes only\n/** @deprecated */\nconst stringToBytes = (type, str) => {\n    if (!CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (typeof str !== 'string')\n        throw new TypeError('stringToBytes() expects string');\n    return CODERS[type].decode(str);\n};\n/** @deprecated */\nconst bytes = stringToBytes;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@scure/base/lib/esm/index.js\n");

/***/ })

};
;