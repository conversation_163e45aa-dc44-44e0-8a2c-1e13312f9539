"use client";

import { motion } from "framer-motion";
import { ArrowUpDown } from "lucide-react";
import React from "react";

import type { SwapButtonProps } from "../types";

const SwapButton: React.FC<SwapButtonProps> = ({ onClick, disabled }) => {
  return (
    <motion.div
      className="h-[16px] relative flex items-center justify-center"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: 0.3 }}
    >
      <motion.div
        className="absolute w-[62px] h-[63px] flex items-center justify-center cursor-pointer transition-transform"
        onClick={onClick}
        whileHover={{ scale: 1.1, rotate: 180 }}
        whileTap={{ scale: 0.9 }}
      >
        <div className="w-12 h-12 bg-[#F58A38] rounded-full flex items-center justify-center shadow-lg">
          <ArrowUpDown size={24} className="text-white" />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SwapButton; 