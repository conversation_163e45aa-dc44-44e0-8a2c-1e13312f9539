"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

import { loginUser } from "../../axios/requests";
import { useAppContext } from "../../contexts/AppContext";
import { useTranslation } from '@/hooks/useTranslation';
import { useGlobalModal } from "@/contexts/GlobalModalContext";

interface SigninModalProps {
  onClose: () => void;
  onSuccessfulLogin?: () => void;
}

const SigninModal: React.FC<SigninModalProps> = ({
  onClose,
  onSuccessfulLogin,
}) => {
  const { login } = useAppContext();
  const { t } = useTranslation();

  const { isModalOpen } = useGlobalModal();
  const isOpen = isModalOpen("signin");
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({
    password: "",
    success: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear errors when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const validatePassword = (password: string) => {
    // Password must be at least 8 characters, include uppercase, lowercase, number, and special character
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // if (name === "password" && value ) {
    //   setErrors((prev) => ({
    //     ...prev,
    //     password:
    //       "Password11 must be at least 8 characters and include uppercase, lowercase, number, and special character",
    //   }));
    // }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate password
    if (formData.password == "") {
      setErrors((prev) => ({
        ...prev,
        password: "Password is required.",
      }));
      return;
    }

    try {
      // Call login API
      const res = await loginUser({
        email: formData.username,
        password: formData.password,
      });
      if (res.status != 200) {
        setErrors((prev: any) => ({
          ...prev,
          password: res.message,
        }));
        return;
      } else {
        // Save userBo globally
        login(res.data,res.token);
        setErrors((prev) => ({
          ...prev,
          success: res.message,
          password: "",
        }));
        setTimeout(() => {
          onClose();
          if (onSuccessfulLogin) onSuccessfulLogin();
        }, 2000);
        return;
      }
    } catch (error: any) {
      console.error("Login failed:", error);
      setErrors((prev: any) => ({
        ...prev,
        apiError:
          error.response?.data?.message || "Login failed. Please try again.",
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-0 modal-backdrop"
      style={{ backgroundColor: "#000000b0" }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className="bg-white rounded-[24px] md:rounded-[48px] w-full max-w-[705px] p-4 md:p-12 relative flex flex-col m-4 max-h-full overflow-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none"
          onClick={() => onClose()}
        >
          <Image 
            src={"/icons/close.svg"} 
            alt="Menu" 
            width={24} 
            height={24}
            style={{ filter: 'invert(40%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%)' }}
          />
        </button>
        <div className="text-center mb-12 max-w-[459px] m-auto">
          <div className="w-full text-center justify-start text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mx-auto mb-3">
            {t('authModal.signinTitle')}
          </div>
          <div className="self-stretch text-center justify-start text-gray-500 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
            {t('authModal.signinSubtitle')}
          </div>
        </div>

        <form
          onSubmit={handleSubmit}
          className="flex-1 flex flex-col max-w-[459px] m-auto w-full"
        >
          <div className="mb-5">
            <div className="flex items-center border border-[#D0D5DD] rounded-lg h-[52px] w-full mx-auto px-4">
              <span className="text-[#98A2B3] mr-3">
                <Image
                  src="/icons/username.svg"
                  alt="Usename"
                  width={24}
                  height={24}
                />
              </span>
              <input
                type="text"
                name="username"
                placeholder={t('authModal.email')}
                value={formData.username}
                onChange={handleChange}
                className="flex-1 outline-none text-gray-700 h-full text-[18px]"
                required
              />
            </div>
          </div>

          {/* Password Field */}
          <div className="mb-4">
            <div className="flex items-center border border-[#D0D5DD] rounded-lg h-[52px] w-full mx-auto px-4">
              <span className="text-[#98A2B3] mr-3">
                <Image
                  src="/icons/password.svg"
                  alt="Password"
                  width={24}
                  height={24}
                />
              </span>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                placeholder={t('authModal.password')}
                className="flex-1 outline-none text-gray-700 h-full text-[18px]"
                value={formData.password}
                onChange={handleChange}
                onBlur={handleBlur}
                required
              />
              <button
                type="button"
                onClick={handleTogglePassword}
                className="ml-2 text-[#98A2B3] focus:outline-none"
              >
                {showPassword ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm mt-1 w-full mx-auto">
                {t('authModal.passwordRequired')}
              </p>
            )}
          </div>

          {errors.success && (
            <p className="text-green-500 font-semibold text-base mt-1 w-full mx-auto">
              {t('authModal.success')}
            </p>
          )}

          {/* Sign Up Button */}
          <button
            type="submit"
            className="w-full h-12 px-5 py-2 bg-[#FF6600] rounded-sm mx-auto inline-flex justify-center items-center gap-2.5 mb-7"
            onClick={handleSubmit}
          >
            <div className="text-center justify-start text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
              {t('authModal.login')}
            </div>
          </button>

          {/* Or sign up with */}
          <div className="text-center mb-16">
            <div className="self-stretch text-center justify-start text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal">
              {t('authModal.orLoginWith')}
            </div>
          </div>

          {/* Social Login Options */}
          <div className="w-full md:max-w-96 flex flex-col md:flex-row justify-start items-start gap-5 mx-auto mb-4">
            <div className="w-full md:w-44 px-6 py-3 bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300 flex justify-center items-center gap-2">
              <svg viewBox="0 0 48 48" width="24" height="24" className="mr-2">
                <path
                  fill="#FFC107"
                  d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                ></path>
                <path
                  fill="#FF3D00"
                  d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
                ></path>
                <path
                  fill="#4CAF50"
                  d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
                ></path>
                <path
                  fill="#1976D2"
                  d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
                ></path>
              </svg>
              <div className="text-center justify-start text-gray-900 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
                {t('authModal.google')}
              </div>
            </div>
            <div className="w-full md:w-44 px-6 py-3 bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300 flex justify-center items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 48 48"
                width="24"
                height="24"
                className="mr-2"
              >
                <path
                  fill="#039be5"
                  d="M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z"
                ></path>
                <path
                  fill="#fff"
                  d="M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z"
                ></path>
              </svg>
              <div className="text-center justify-start text-gray-900 text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
                {t('authModal.facebook')}
              </div>
            </div>
          </div>

          {/* Already have an account */}
          <div className="text-center">
            <div className="w-72 text-center justify-start mx-auto">
              <span className="text-gray-500 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal">
                {t('authModal.dontHaveAccount')} 
              </span>
              <Link
                href="/auth/signup"
                className="text-neutral-800 text-base font-semibold font-['IBM_Plex_Sans'] leading-normal hover:underline"
              >
                {t('authModal.signupLink')}
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SigninModal;