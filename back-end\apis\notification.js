const { Router } = require("express");
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createAnnouncement,
  createSystemNotification
} = require("../controller/notification");
const { body, param, query } = require('express-validator');
const { validate } = require("../middleware/validator");
const { authenticate } = require("../middleware/authenticate");
const { requireAdmin } = require("../middleware/roleAuth");

const router = Router();

// Get notifications for authenticated user
router.get(
  "/",
  authenticate,
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("pageSize")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Page size must be between 1 and 100"),
    query("unreadOnly")
      .optional()
      .isBoolean()
      .withMessage("unreadOnly must be a boolean"),
  ],
  validate,
  getNotifications
);

// Get unread notification count
router.get(
  "/unread-count",
  authenticate,
  getUnreadCount
);

// Mark notification as read
router.patch(
  "/:notificationId/read",
  authenticate,
  [
    param("notificationId")
      .isInt({ min: 1 })
      .withMessage("Notification ID must be a positive integer"),
  ],
  validate,
  markAsRead
);

// Mark all notifications as read
router.patch(
  "/mark-all-read",
  authenticate,
  markAllAsRead
);

// Delete notification
router.delete(
  "/:notificationId",
  authenticate,
  [
    param("notificationId")
      .isInt({ min: 1 })
      .withMessage("Notification ID must be a positive integer"),
  ],
  validate,
  deleteNotification
);

// Create system announcement (admin only)
router.post(
  "/announcement",
  authenticate,
  requireAdmin,
  [
    body("title")
      .isLength({ min: 1, max: 255 })
      .withMessage("Title must be between 1 and 255 characters"),
    body("message")
      .isLength({ min: 1, max: 1000 })
      .withMessage("Message must be between 1 and 1000 characters"),
    body("priority")
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage("Priority must be low, medium, high, or urgent"),
    body("targetRole")
      .optional()
      .isIn(['all', 'moderators'])
      .withMessage("Target role must be all or moderators"),
    body("expiresAt")
      .optional()
      .isISO8601()
      .withMessage("Expires at must be a valid date"),
  ],
  validate,
  createAnnouncement
);

// Create system-wide notification (admin only)
router.post(
  "/system",
  authenticate,
  requireAdmin,
  [
    body("type")
      .isIn(['system_announcement', 'perk_created', 'token_created', 'moderator_added'])
      .withMessage("Type must be a valid system notification type"),
    body("title")
      .isLength({ min: 1, max: 255 })
      .withMessage("Title must be between 1 and 255 characters"),
    body("message")
      .isLength({ min: 1, max: 1000 })
      .withMessage("Message must be between 1 and 1000 characters"),
    body("priority")
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage("Priority must be low, medium, high, or urgent"),
    body("actionUrl")
      .optional()
      .isURL()
      .withMessage("Action URL must be a valid URL"),
    body("data")
      .optional()
      .isObject()
      .withMessage("Data must be a valid object"),
  ],
  validate,
  createSystemNotification
);

// Test endpoint to verify real-time system notifications
router.post(
  "/test-system",
  authenticate,
  async (req, res) => {
    try {
      console.log('🧪 [Test] Creating test system notification for user:', req.user.id);

      const notificationCount = await dataContext.Notification.createSystemNotificationForAllUsers(
        'system_announcement',
        'Test System Notification',
        'This is a test system notification to verify real-time broadcasting works correctly.',
        { test: true },
        'medium',
        null
      );

      res.status(201).json({
        status: 201,
        message: 'Test system notification sent successfully',
        data: {
          recipientCount: notificationCount
        }
      });
    } catch (error) {
      console.error('Test system notification error:', error);
      res.status(500).json({ status: 500, message: 'Internal server error' });
    }
  }
);



module.exports = router;
