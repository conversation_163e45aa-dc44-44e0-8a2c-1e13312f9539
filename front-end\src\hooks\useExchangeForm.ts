import { useState, useEffect, useCallback } from "react";
import { Cluster, clusterApiUrl, Connection, PublicKey } from "@solana/web3.js";

import { useAppContext } from "../contexts/AppContext";
import { useWallet } from "./useWallet";
import { buyToken } from "../axios/requests";
import {
  getTokenOutputForSolInput,
  getSolOutputForTokenInput,
  getSolOutputForTokenInputFromPool,
  getTokenOutputForSolInputFromPool,
  fetchTokenOptions,
  tokenBuyFromPool,
  tokenBuyFromContract,
  tokenSellFromContract,
} from "../utils/helpers";
import type { TokenOption } from "@/components/shared/exchange-form/types";
import { UseExchangeFormProps, UseExchangeFormReturn } from "./types";
import { SOLANA_CONFIG } from "@/config/environment";
import { showErrorToast, showSuccessToast } from "@/utils/errorHandling";

export const useExchangeForm = ({
  fromOptions,
  toOptions,
}: UseExchangeFormProps): UseExchangeFormReturn => {
  const { solanaWallet, isConnected, getWalletAddress } = useWallet();
  const { state } = useAppContext();

  const [fromToken, setFromToken] = useState<TokenOption | null>(
    fromOptions[0] || null
  );
  const [toToken, setToToken] = useState<TokenOption | null>(
    toOptions[0] || null
  );
  const [fromAmount, setFromAmount] = useState("0.00");
  const [toAmount, setToAmount] = useState("0.00");
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [userSOL, setUserSOL] = useState(0);

  useEffect(() => {
    if (isConnected) {
      refreshTokenBalances();
    }
  }, [isConnected]);

  const getTokenAndSOlPriceLogic = useCallback(
    async (value: string, typeIs: string) => {
      let isgraduated;
      let poolAddress: string | undefined = "";
      let mintAddress;

      if (toToken?.tokenAddress == "") {
        isgraduated = fromToken?.tokenDetails?.graduated;
        poolAddress = fromToken?.tokenDetails?.poolAddress;
        mintAddress = fromToken?.tokenAddress;
      } else {
        isgraduated = toToken?.tokenDetails?.graduated;
        poolAddress = toToken?.tokenDetails?.poolAddress;
        mintAddress = toToken?.tokenAddress;
      }

      if (!mintAddress) {
        console.error("Invalid mint address");
        return;
      }

      const tokenInput = parseFloat(value);

      // checking isgraduated true or false, if true get price from pool else contract
      if (isgraduated) {
        console.log("yes " + isgraduated);

        if (!poolAddress) {
          console.error("Invalid pool address");
          return;
        }

        if (typeIs == "to") {
          console.log("yes 3");
          const price = await getSolOutputForTokenInputFromPool(
            poolAddress,
            tokenInput
          );
          return price.estimatedOutput.toFixed(6);
        } else {
          console.log("yes 4");
          const price = await getTokenOutputForSolInputFromPool(
            poolAddress,
            tokenInput
          );
          return price.estimatedOutput.toFixed(6);
        }
      } else {
        console.log("no " + isgraduated);
        if (typeIs == "to") {
          console.log("1");
          const price = await getSolOutputForTokenInput(
            mintAddress,
            tokenInput,
            0.05
          );
          return price.estimatedOutput.toFixed(6);
        } else {
          console.log("2");
          setFromAmount(value);
          const price = await getTokenOutputForSolInput(
            mintAddress,
            tokenInput,
            0.05
          );
          return price.estimatedOutput.toFixed(2);
        }
      }
    },
    [fromToken, toToken]
  );

  const handleFromAmountChange = useCallback(
    async (value: string) => {
      setFromAmount(value);
      let typeIs = "from";
      //this is because on swap function remain same.
      if (toToken?.tokenAddress == "") {
        typeIs = "to";
      }
      const valueIs = await getTokenAndSOlPriceLogic(value, typeIs);
      setToAmount(valueIs ? valueIs : "0.00");
    },
    [getTokenAndSOlPriceLogic, toToken]
  );

  const handleToAmountChange = useCallback(
    async (value: string) => {
      setToAmount(value);
      let typeIs = "from";
      //this is because on swap function remain same
      if (toToken?.tokenAddress != "") {
        typeIs = "to";
      }
      const valueIs = await getTokenAndSOlPriceLogic(value, typeIs);
      setFromAmount(valueIs ? valueIs : "0.00");
    },
    [getTokenAndSOlPriceLogic, toToken]
  );

  const handleSwapTokens = useCallback(() => {
    if (!fromToken || !toToken || isLoading) return;

    const tempToken = fromToken;
    setFromToken(toToken);
    setToToken(tempToken);

    const tempAmount = fromAmount;
    setFromAmount(toAmount);
    setToAmount(tempAmount);
  }, [fromToken, toToken, fromAmount, toAmount, isLoading]);

  const refreshTokenBalances = useCallback(async () => {
    if (!isConnected) return;

    const hasSession = localStorage.getItem("userSession");
    if (!hasSession) {
      console.log("not login ");
      return;
    }

    setIsRefreshing(true);

    try {
      const walletAddress = getWalletAddress();
      if (!walletAddress) return;

      const connection = new Connection(
        SOLANA_CONFIG.CONNECTION_URL || clusterApiUrl(SOLANA_CONFIG.CLUSTER as Cluster),
        "confirmed"
      );
      const updatedTokens = await fetchTokenOptions(walletAddress);

      const getUpdatedBalance = async (token: TokenOption) => {
        if (token.symbol === "SOL") {
          try {
            const lamports = await connection.getBalance(
              new PublicKey(walletAddress)
            );
            setUserSOL(+(lamports / 1e9).toFixed(4));
            return +(lamports / 1e9).toFixed(4);
          } catch (error) {
            console.error("Error fetching SOL balance:", error);
            return +token.balance.toFixed(4);
          }
        } else {
          const matched = updatedTokens.find(
            (u) => u.tokenAddress === token.tokenAddress
          );
          return matched
            ? +matched.balance.toFixed(4)
            : +token.balance.toFixed(4);
        }
      };

      const updatedTo = await Promise.all(
        toOptions.map(async (opt) => ({
          ...opt,
          balance: await getUpdatedBalance(opt),
        }))
      );

      const updatedFrom = await Promise.all(
        fromOptions.map(async (opt) => ({
          ...opt,
          balance: await getUpdatedBalance(opt),
        }))
      );

      setToToken((prev) => {
        if (!prev) return null;
        return (
          updatedTo.find((t) => t.tokenAddress === prev.tokenAddress) || prev
        );
      });

      setFromToken((prev) => {
        if (!prev) return null;
        return (
          updatedFrom.find((t) => t.tokenAddress === prev.tokenAddress) || prev
        );
      });
    } catch (error) {
      console.error("Error refreshing balances:", error);
      showErrorToast("Failed to refresh token balances");
    } finally {
      setIsRefreshing(false);
    }
  }, [isConnected, toOptions, fromOptions]);

  const handleBuyTokens = useCallback(async () => {
    if (isLoading) return;

    const userBo =
      typeof state.userBo === "string"
        ? JSON.parse(state.userBo)
        : state.userBo;
    const userId = userBo?.id;
    if (!userId) {
      showErrorToast("Please log in before purchasing tokens.");
      return;
    }

    const privyWallet = solanaWallet;

    if (!privyWallet || !privyWallet.signTransaction) {
      showErrorToast("Solana wallet not connected or the wrong wallet is being used. Please log out and log in again.");
      throw new Error(
        "Privy Solana wallet not connected or signTransaction missing"
      );
    }

    if (!toToken || parseFloat(toAmount) === 0) {
      showErrorToast("Please Enter valid amount you want to buy.");
      return;
    }

    console.log("User have SOl :" + userSOL);
    if (userSOL == 0) {
      showErrorToast(
        "Your wallet has insufficient SOL balance. Please deposit SOL to proceed."
      );
      return;
    }

    setIsLoading(true);

    const slippage = 0.005;
    let isgraduated;
    let buyTokenAmount;
    let poolAddress: string | undefined = "";
    let tokenMint: string | undefined;
    let creatorWallet: string | undefined;
    let tokenId;
    let isBuying = true;
    let txId = "";
    let tokenName;

    if (toToken?.tokenAddress == "") {
      isgraduated = fromToken?.tokenDetails?.graduated;
      poolAddress = fromToken?.tokenDetails?.poolAddress;
      tokenMint = fromToken?.tokenAddress;
      creatorWallet = fromToken?.creatorWallet;
      tokenId = fromToken?.id;
      tokenName = fromToken?.name;
    } else {
      isgraduated = toToken?.tokenDetails?.graduated;
      poolAddress = toToken?.tokenDetails?.poolAddress;
      tokenMint = toToken.tokenAddress;
      creatorWallet = toToken?.creatorWallet;
      tokenId = toToken.id;
      tokenName = toToken?.name;
    }

    //check if moved to raydium pool
    if (isgraduated) {
      console.log("in isgraduated");

      if (!poolAddress) {
        console.error("Invalid pool address");
        return;
      }

      if (toToken?.tokenAddress !== "") {
        buyTokenAmount = fromAmount;
        isBuying = true;
      } else {
        buyTokenAmount = fromAmount;
        isBuying = false;
      }

      txId = await tokenBuyFromPool(
        poolAddress,
        buyTokenAmount,
        privyWallet,
        slippage,
        isBuying
      );
      console.log(txId);
      if (txId == "0") {
        setIsLoading(false);
        return;
      }
    } else {
      console.log("out isgraduated");
      if (!tokenMint || !creatorWallet)
        throw new Error("tokenMint is undefined");

      if (toToken.tokenAddress != "") {
        console.log("buy");
        isBuying = true;
        txId = await tokenBuyFromContract(
          tokenMint,
          creatorWallet,
          privyWallet,
          slippage,
          parseFloat(fromAmount)
        );
        console.log(txId);
      } else {
        console.log("sel");
        console.log(fromAmount);
        isBuying = false;
        txId = await tokenSellFromContract(
          tokenMint,
          creatorWallet,
          privyWallet,
          slippage,
          parseFloat(fromAmount)
        );
        console.log(txId);
      }
      if (txId == "0") {
        setIsLoading(false);
        return;
      }
    }

    try {
      //saving in DB
      let buyOrSellAmount = 0;
      let dollorPrice = 0.0;
      let isBuy = 1;
      if (isBuying == true) {
        // mean User is buying
        buyOrSellAmount = parseFloat(toAmount);
        dollorPrice = parseFloat(toAmount) * 0.0000045515; // coinPrice
        isBuy = 1;
      } else {
        buyOrSellAmount = parseFloat(fromAmount);
        dollorPrice = parseFloat(fromAmount) * 0.0000045515; // coinPrice
        isBuy = 0;
      }
    

      const data = {
        userId: userId,
        tokenId: tokenId,
        amount: buyOrSellAmount,
        price: 0.0000045515, // coinPrice
        dollorPrice: dollorPrice,
        hasH: txId,
        isBuy: isBuy,
        status: "Token",
        tokenName: tokenName,
      };

      const response = await buyToken(data);

      if (response.status === 200) {
        showSuccessToast(response?.message || "Token purchased successfully");
        setIsLoading(false);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await refreshTokenBalances();

        // Reset form
        setFromAmount("0.00");
        setToAmount("0.00");
      } else {
        showErrorToast(response.data?.message || "Something went wrong");
      }
    } catch (error) {
      console.error("Error buying tokens:", error);
      console.error(error);
      showErrorToast("Transaction failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [
    isLoading,
    state.userBo,
    solanaWallet,
    toToken,
    toAmount,
    userSOL,
    fromToken,
    fromAmount,
    refreshTokenBalances,
  ]);

  return {
    // State
    fromToken,
    toToken,
    fromAmount,
    toAmount,
    isLoading,
    isRefreshing,
    userSOL,

    // Handlers
    setFromToken,
    setToToken,
    handleFromAmountChange,
    handleToAmountChange,
    handleSwapTokens,
    handleBuyTokens,
    refreshTokenBalances,
  };
};
