"use client";

import React, { useState } from 'react';
import { useEscrowOperations } from '@/hooks/useEscrowOperations';
import { EscrowLoadingButton, EscrowErrorDisplay } from '@/components/ui/EscrowComponents';

interface DisputeModalProps {
  isOpen: boolean;
  onClose: () => void;
  tradeId: number;
  initiatorRole: 'buyer' | 'seller';
  onDisputeInitiated: (disputeData: { disputeId: number; status: string }) => void;
}

const DISPUTE_REASONS = [
  'Product not as described',
  'Product not received',
  'Product damaged/defective',
  'Seller not responding',
  'Buyer not releasing payment',
  'Delivery issues',
  'Quality issues',
  'Other'
];

export default function DisputeModal({
  isOpen,
  onClose,
  tradeId,
  initiatorRole,
  onDisputeInitiated
}: DisputeModalProps) {
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [additionalDetails, setAdditionalDetails] = useState('');

  // Use escrow operations hook for dispute handling
  const escrowOperations = useEscrowOperations({
    onOperationSuccess: (operation, result) => {
      if (operation === 'dispute') {
        onDisputeInitiated(result);
        onClose();
        // Reset form
        setSelectedReason('');
        setCustomReason('');
        setAdditionalDetails('');
      }
    },
    showToasts: true
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedReason) {
      setError('Please select a dispute reason');
      return;
    }

    const reason = selectedReason === 'Other' ? customReason : selectedReason;
    const fullReason = additionalDetails 
      ? `${reason}. Additional details: ${additionalDetails}`
      : reason;

    if (!reason.trim()) {
      setError('Please provide a dispute reason');
      return;
    }

    try {
      await escrowOperations.executeDispute(tradeId, fullReason, initiatorRole);
    } catch (error: any) {
      console.error('Failed to initiate dispute:', error);
      // Error handling is managed by the escrow operations hook
    }
  };

  const handleClose = () => {
    if (escrowOperations.getOperationStatus('dispute') !== 'pending') {
      setSelectedReason('');
      setCustomReason('');
      setAdditionalDetails('');
      escrowOperations.clearOperation('dispute');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">Initiate Dispute</h2>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Important</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Initiating a dispute will involve a moderator to review the case. 
                Please provide accurate information as this will be used for resolution.
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dispute Reason *
            </label>
            <div className="space-y-2">
              {DISPUTE_REASONS.map((reason) => (
                <label key={reason} className="flex items-center">
                  <input
                    type="radio"
                    name="disputeReason"
                    value={reason}
                    checked={selectedReason === reason}
                    onChange={(e) => setSelectedReason(e.target.value)}
                    className="mr-2"
                    disabled={loading}
                  />
                  <span className="text-sm text-gray-700">{reason}</span>
                </label>
              ))}
            </div>
          </div>

          {selectedReason === 'Other' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Reason *
              </label>
              <input
                type="text"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Please specify the reason..."
                disabled={escrowOperations.getOperationStatus('dispute') === 'pending'}
                required
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Details (Optional)
            </label>
            <textarea
              value={additionalDetails}
              onChange={(e) => setAdditionalDetails(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows={4}
              placeholder="Provide any additional information that might help resolve this dispute..."
              disabled={escrowOperations.getOperationStatus('dispute') === 'pending'}
            />
          </div>



          {/* Error Display */}
          {escrowOperations.getOperationError('dispute') && (
            <EscrowErrorDisplay
              error={escrowOperations.getOperationError('dispute')!}
              operation="dispute"
              onRetry={() => escrowOperations.retryOperation('dispute')}
              onDismiss={() => escrowOperations.clearOperation('dispute')}
              className="mb-4"
            />
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={escrowOperations.getOperationStatus('dispute') === 'pending'}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>

            <EscrowLoadingButton
              onClick={handleSubmit}
              disabled={!selectedReason}
              loading={escrowOperations.getOperationStatus('dispute') === 'pending'}
              loadingText="Initiating..."
              variant="danger"
              className="flex-1"
            >
              Initiate Dispute
            </EscrowLoadingButton>
          </div>
        </form>

        <div className="mt-4 p-3 bg-gray-50 rounded-md">
          <p className="text-xs text-gray-600">
            <strong>Note:</strong> Disputes must be initiated within 2 days of the original purchase.
            Once a dispute is initiated, the funds will remain locked until
            a moderator reviews the case and makes a decision. This process typically takes 1-3 business days.
          </p>
        </div>
      </div>
    </div>
  );
}
