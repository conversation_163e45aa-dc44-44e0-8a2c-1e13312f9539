'use client';

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { useGlobalModal } from './GlobalModalContext';
import { useSocket } from './SocketProvider';

interface ChatModalState {
  chatRoomId: string;
  activeTrade?: any;
  currentTradeStatus?: string;
  isOperationInProgress?: boolean;
  lastUpdated?: number;
}

interface ChatModalStateContextType {
  getChatModalState: (chatRoomId: string) => ChatModalState | undefined;
  updateChatModalState: (chatRoomId: string, updates: Partial<ChatModalState>) => void;
  registerChatModal: (chatRoomId: string, initialState: ChatModalState) => void;
  unregisterChatModal: (chatRoomId: string) => void;
  updateModalProps: (chatRoomId: string, newProps: any) => boolean;
  syncTradeStatusUpdate: (tradeId: string | number, status: string) => void;
}

const ChatModalStateContext = createContext<ChatModalStateContextType | null>(null);

export const ChatModalStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [chatModalStates, setChatModalStates] = useState<Map<string, ChatModalState>>(new Map());
  const { updateModal, getModalById } = useGlobalModal();
  const socket = useSocket();
  const stateRef = useRef<Map<string, ChatModalState>>(new Map());

  // Keep ref in sync with state
  React.useEffect(() => {
    stateRef.current = chatModalStates;
  }, [chatModalStates]);

  const getChatModalState = useCallback((chatRoomId: string): ChatModalState | undefined => {
    return stateRef.current.get(chatRoomId);
  }, []);

  const updateChatModalState = useCallback((chatRoomId: string, updates: Partial<ChatModalState>) => {
    console.log(`🔄 [ChatModalStateContext] Updating state for chatRoom ${chatRoomId}:`, updates);
    
    setChatModalStates(prev => {
      const newMap = new Map(prev);
      const currentState = newMap.get(chatRoomId);
      
      if (currentState) {
        const updatedState = {
          ...currentState,
          ...updates,
          lastUpdated: Date.now()
        };
        newMap.set(chatRoomId, updatedState);
        console.log(`✅ [ChatModalStateContext] Updated state for ${chatRoomId}:`, updatedState);
      } else {
        console.log(`⚠️ [ChatModalStateContext] No state found for chatRoom ${chatRoomId}`);
      }
      
      return newMap;
    });
  }, []);

  const registerChatModal = useCallback((chatRoomId: string, initialState: ChatModalState) => {
    console.log(`📝 [ChatModalStateContext] Registering chatRoom ${chatRoomId}:`, initialState);
    setChatModalStates(prev => {
      const newMap = new Map(prev);
      newMap.set(chatRoomId, { ...initialState, lastUpdated: Date.now() });
      return newMap;
    });
  }, []);

  const unregisterChatModal = useCallback((chatRoomId: string) => {
    console.log(`🗑️ [ChatModalStateContext] Unregistering chatRoom ${chatRoomId}`);
    setChatModalStates(prev => {
      const newMap = new Map(prev);
      newMap.delete(chatRoomId);
      return newMap;
    });
  }, []);

  const updateModalProps = useCallback((chatRoomId: string, newProps: any): boolean => {
    const modalId = `chat-modal-${chatRoomId}`;
    const existingModal = getModalById(modalId);
    
    if (!existingModal) {
      console.log(`⚠️ [ChatModalStateContext] No modal found with id ${modalId}`);
      return false;
    }

    console.log(`🔄 [ChatModalStateContext] Updating modal props for ${modalId}:`, newProps);
    
    // Update the modal component with new props
    const updatedComponent = React.cloneElement(
      existingModal.component as React.ReactElement,
      {
        ...((existingModal.component as React.ReactElement).props || {}),
        ...newProps,
        key: `${modalId}-${Date.now()}` // Force re-render with new props
      }
    );

    return updateModal(modalId, {
      component: updatedComponent,
      updateProps: newProps
    });
  }, [getModalById, updateModal]);

  const syncTradeStatusUpdate = useCallback((tradeId: string | number, status: string) => {
    console.log(`🔄 [ChatModalStateContext] Syncing trade status update: ${tradeId} -> ${status}`);

    // Find all chat modals that have this trade and update them
    stateRef.current.forEach((state, chatRoomId) => {
      if (state.activeTrade &&
          (state.activeTrade.id === tradeId || state.activeTrade.tradeId === tradeId)) {
        console.log(`📡 [ChatModalStateContext] Updating chatRoom ${chatRoomId} with new status: ${status}`);

        // Update the state
        updateChatModalState(chatRoomId, {
          currentTradeStatus: status,
          activeTrade: { ...state.activeTrade, status }
        });

        // Update the modal props to trigger re-render
        const modalId = `chat-modal-${chatRoomId}`;
        const existingModal = getModalById(modalId);
        if (existingModal) {
          const updatedProps = {
            activeTrade: { ...state.activeTrade, status },
            currentTradeStatus: status
          };
          updateModalProps(chatRoomId, updatedProps);
        }
      }
    });
  }, [updateChatModalState, getModalById, updateModalProps]);

  // Listen for trade status updates from socket
  useEffect(() => {
    if (!socket) return;

    const handleTradeStatusUpdate = (data: { tradeId: string | number; status: string }) => {
      console.log('🔔 [ChatModalStateContext] Received trade status update from socket:', data);
      syncTradeStatusUpdate(data.tradeId, data.status);
    };

    const handleEscrowAccepted = (data: { tradeId: string | number }) => {
      console.log('🔔 [ChatModalStateContext] Received escrow accepted from socket:', data);
      syncTradeStatusUpdate(data.tradeId, 'escrowed');
    };

    const handlePerkReleased = (data: { tradeId: string | number }) => {
      console.log('🔔 [ChatModalStateContext] Received perk released from socket:', data);
      syncTradeStatusUpdate(data.tradeId, 'completed');
    };

    const handleRefundProcessed = (data: { tradeId: string | number }) => {
      console.log('🔔 [ChatModalStateContext] Received refund processed from socket:', data);
      syncTradeStatusUpdate(data.tradeId, 'refunded');
    };

    socket.on('tradeStatus', handleTradeStatusUpdate);
    socket.on('escrowAccepted', handleEscrowAccepted);
    socket.on('perkReleased', handlePerkReleased);
    socket.on('refundProcessed', handleRefundProcessed);

    return () => {
      socket.off('tradeStatus', handleTradeStatusUpdate);
      socket.off('escrowAccepted', handleEscrowAccepted);
      socket.off('perkReleased', handlePerkReleased);
      socket.off('refundProcessed', handleRefundProcessed);
    };
  }, [socket, syncTradeStatusUpdate]);

  const value: ChatModalStateContextType = {
    getChatModalState,
    updateChatModalState,
    registerChatModal,
    unregisterChatModal,
    updateModalProps,
    syncTradeStatusUpdate
  };

  return (
    <ChatModalStateContext.Provider value={value}>
      {children}
    </ChatModalStateContext.Provider>
  );
};

export const useChatModalState = () => {
  const context = useContext(ChatModalStateContext);
  if (!context) {
    throw new Error('useChatModalState must be used within a ChatModalStateProvider');
  }
  return context;
};
