"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_modal_dist_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/modal-core/dist/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@walletconnect/modal-core/dist/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigCtrl: () => (/* binding */ ConfigCtrl),\n/* harmony export */   CoreUtil: () => (/* binding */ CoreUtil),\n/* harmony export */   EventsCtrl: () => (/* binding */ EventsCtrl),\n/* harmony export */   ExplorerCtrl: () => (/* binding */ ExplorerCtrl),\n/* harmony export */   ModalCtrl: () => (/* binding */ ModalCtrl),\n/* harmony export */   OptionsCtrl: () => (/* binding */ OptionsCtrl),\n/* harmony export */   RouterCtrl: () => (/* binding */ RouterCtrl),\n/* harmony export */   ThemeCtrl: () => (/* binding */ ThemeCtrl),\n/* harmony export */   ToastCtrl: () => (/* binding */ ToastCtrl)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! valtio/vanilla */ \"(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/valtio/esm/vanilla.mjs\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\nconst state$7 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  history: [\"ConnectWallet\"],\n  view: \"ConnectWallet\",\n  data: void 0\n});\nconst RouterCtrl = {\n  state: state$7,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$7, () => callback(state$7));\n  },\n  push(view, data) {\n    if (view !== state$7.view) {\n      state$7.view = view;\n      if (data) {\n        state$7.data = data;\n      }\n      state$7.history.push(view);\n    }\n  },\n  reset(view) {\n    state$7.view = view;\n    state$7.history = [view];\n  },\n  replace(view) {\n    if (state$7.history.length > 1) {\n      state$7.history[state$7.history.length - 1] = view;\n      state$7.view = view;\n    }\n  },\n  goBack() {\n    if (state$7.history.length > 1) {\n      state$7.history.pop();\n      const [last] = state$7.history.slice(-1);\n      state$7.view = last;\n    }\n  },\n  setData(data) {\n    state$7.data = data;\n  }\n};\n\nconst CoreUtil = {\n  WALLETCONNECT_DEEPLINK_CHOICE: \"WALLETCONNECT_DEEPLINK_CHOICE\",\n  WCM_VERSION: \"WCM_VERSION\",\n  RECOMMENDED_WALLET_AMOUNT: 9,\n  isMobile() {\n    if (typeof window !== \"undefined\") {\n      return Boolean(\n        window.matchMedia(\"(pointer:coarse)\").matches || /Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini/u.test(navigator.userAgent)\n      );\n    }\n    return false;\n  },\n  isAndroid() {\n    return CoreUtil.isMobile() && navigator.userAgent.toLowerCase().includes(\"android\");\n  },\n  isIos() {\n    const ua = navigator.userAgent.toLowerCase();\n    return CoreUtil.isMobile() && (ua.includes(\"iphone\") || ua.includes(\"ipad\"));\n  },\n  isHttpUrl(url) {\n    return url.startsWith(\"http://\") || url.startsWith(\"https://\");\n  },\n  isArray(data) {\n    return Array.isArray(data) && data.length > 0;\n  },\n  isTelegram() {\n    return typeof window !== \"undefined\" && // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (Boolean(window.TelegramWebviewProxy) || // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    Boolean(window.Telegram) || // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    Boolean(window.TelegramWebviewProxyProto));\n  },\n  formatNativeUrl(appUrl, wcUri, name) {\n    if (CoreUtil.isHttpUrl(appUrl)) {\n      return this.formatUniversalUrl(appUrl, wcUri, name);\n    }\n    let safeAppUrl = appUrl;\n    if (!safeAppUrl.includes(\"://\")) {\n      safeAppUrl = appUrl.replaceAll(\"/\", \"\").replaceAll(\":\", \"\");\n      safeAppUrl = `${safeAppUrl}://`;\n    }\n    if (!safeAppUrl.endsWith(\"/\")) {\n      safeAppUrl = `${safeAppUrl}/`;\n    }\n    this.setWalletConnectDeepLink(safeAppUrl, name);\n    const encodedWcUrl = encodeURIComponent(wcUri);\n    return `${safeAppUrl}wc?uri=${encodedWcUrl}`;\n  },\n  formatUniversalUrl(appUrl, wcUri, name) {\n    if (!CoreUtil.isHttpUrl(appUrl)) {\n      return this.formatNativeUrl(appUrl, wcUri, name);\n    }\n    let safeAppUrl = appUrl;\n    if (safeAppUrl.startsWith(\"https://t.me\")) {\n      const formattedUri = Buffer.from(wcUri).toString(\"base64\").replace(/[=]/g, \"\");\n      if (safeAppUrl.endsWith(\"/\")) {\n        safeAppUrl = safeAppUrl.slice(0, -1);\n      }\n      this.setWalletConnectDeepLink(safeAppUrl, name);\n      const url = new URL(safeAppUrl);\n      url.searchParams.set(\"startapp\", formattedUri);\n      const link = url.toString();\n      return link;\n    }\n    if (!safeAppUrl.endsWith(\"/\")) {\n      safeAppUrl = `${safeAppUrl}/`;\n    }\n    this.setWalletConnectDeepLink(safeAppUrl, name);\n    const encodedWcUrl = encodeURIComponent(wcUri);\n    return `${safeAppUrl}wc?uri=${encodedWcUrl}`;\n  },\n  async wait(miliseconds) {\n    return new Promise((resolve) => {\n      setTimeout(resolve, miliseconds);\n    });\n  },\n  openHref(href, target) {\n    const adjustedTarget = this.isTelegram() ? \"_blank\" : target;\n    window.open(href, adjustedTarget, \"noreferrer noopener\");\n  },\n  setWalletConnectDeepLink(href, name) {\n    try {\n      localStorage.setItem(CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE, JSON.stringify({ href, name }));\n    } catch (e) {\n      console.info(\"Unable to set WalletConnect deep link\");\n    }\n  },\n  setWalletConnectAndroidDeepLink(wcUri) {\n    try {\n      const [href] = wcUri.split(\"?\");\n      localStorage.setItem(\n        CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE,\n        JSON.stringify({ href, name: \"Android\" })\n      );\n    } catch (e) {\n      console.info(\"Unable to set WalletConnect android deep link\");\n    }\n  },\n  removeWalletConnectDeepLink() {\n    try {\n      localStorage.removeItem(CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE);\n    } catch (e) {\n      console.info(\"Unable to remove WalletConnect deep link\");\n    }\n  },\n  setModalVersionInStorage() {\n    try {\n      if (typeof localStorage !== \"undefined\") {\n        localStorage.setItem(CoreUtil.WCM_VERSION, \"2.7.0\");\n      }\n    } catch (e) {\n      console.info(\"Unable to set Web3Modal version in storage\");\n    }\n  },\n  getWalletRouterData() {\n    var _a;\n    const routerData = (_a = RouterCtrl.state.data) == null ? void 0 : _a.Wallet;\n    if (!routerData) {\n      throw new Error('Missing \"Wallet\" view data');\n    }\n    return routerData;\n  }\n};\n\nconst isEnabled = typeof location !== \"undefined\" && (location.hostname.includes(\"localhost\") || location.protocol.includes(\"https\"));\nconst state$6 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  enabled: isEnabled,\n  userSessionId: \"\",\n  events: [],\n  connectedWalletId: void 0\n});\nconst EventsCtrl = {\n  state: state$6,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$6.events, () => callback((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.snapshot)(state$6.events[state$6.events.length - 1])));\n  },\n  initialize() {\n    if (state$6.enabled && typeof (crypto == null ? void 0 : crypto.randomUUID) !== \"undefined\") {\n      state$6.userSessionId = crypto.randomUUID();\n    }\n  },\n  setConnectedWalletId(connectedWalletId) {\n    state$6.connectedWalletId = connectedWalletId;\n  },\n  click(data) {\n    if (state$6.enabled) {\n      const event = {\n        type: \"CLICK\",\n        name: data.name,\n        userSessionId: state$6.userSessionId,\n        timestamp: Date.now(),\n        data\n      };\n      state$6.events.push(event);\n    }\n  },\n  track(data) {\n    if (state$6.enabled) {\n      const event = {\n        type: \"TRACK\",\n        name: data.name,\n        userSessionId: state$6.userSessionId,\n        timestamp: Date.now(),\n        data\n      };\n      state$6.events.push(event);\n    }\n  },\n  view(data) {\n    if (state$6.enabled) {\n      const event = {\n        type: \"VIEW\",\n        name: data.name,\n        userSessionId: state$6.userSessionId,\n        timestamp: Date.now(),\n        data\n      };\n      state$6.events.push(event);\n    }\n  }\n};\n\nconst state$5 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  chains: void 0,\n  walletConnectUri: void 0,\n  isAuth: false,\n  isCustomDesktop: false,\n  isCustomMobile: false,\n  isDataLoaded: false,\n  isUiLoaded: false\n});\nconst OptionsCtrl = {\n  state: state$5,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$5, () => callback(state$5));\n  },\n  setChains(chains) {\n    state$5.chains = chains;\n  },\n  setWalletConnectUri(walletConnectUri) {\n    state$5.walletConnectUri = walletConnectUri;\n  },\n  setIsCustomDesktop(isCustomDesktop) {\n    state$5.isCustomDesktop = isCustomDesktop;\n  },\n  setIsCustomMobile(isCustomMobile) {\n    state$5.isCustomMobile = isCustomMobile;\n  },\n  setIsDataLoaded(isDataLoaded) {\n    state$5.isDataLoaded = isDataLoaded;\n  },\n  setIsUiLoaded(isUiLoaded) {\n    state$5.isUiLoaded = isUiLoaded;\n  },\n  setIsAuth(isAuth) {\n    state$5.isAuth = isAuth;\n  }\n};\n\nconst state$4 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  projectId: \"\",\n  mobileWallets: void 0,\n  desktopWallets: void 0,\n  walletImages: void 0,\n  chains: void 0,\n  enableAuthMode: false,\n  enableExplorer: true,\n  explorerExcludedWalletIds: void 0,\n  explorerRecommendedWalletIds: void 0,\n  termsOfServiceUrl: void 0,\n  privacyPolicyUrl: void 0\n});\nconst ConfigCtrl = {\n  state: state$4,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$4, () => callback(state$4));\n  },\n  setConfig(config) {\n    var _a, _b;\n    EventsCtrl.initialize();\n    OptionsCtrl.setChains(config.chains);\n    OptionsCtrl.setIsAuth(Boolean(config.enableAuthMode));\n    OptionsCtrl.setIsCustomMobile(Boolean((_a = config.mobileWallets) == null ? void 0 : _a.length));\n    OptionsCtrl.setIsCustomDesktop(Boolean((_b = config.desktopWallets) == null ? void 0 : _b.length));\n    CoreUtil.setModalVersionInStorage();\n    Object.assign(state$4, config);\n  }\n};\n\nvar __defProp$2 = Object.defineProperty;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nconst W3M_API = \"https://explorer-api.walletconnect.com\";\nconst SDK_TYPE = \"wcm\";\nconst SDK_VERSION = `js-${\"2.7.0\"}`;\nasync function fetchListings(endpoint, params) {\n  const allParams = __spreadValues$2({ sdkType: SDK_TYPE, sdkVersion: SDK_VERSION }, params);\n  const url = new URL(endpoint, W3M_API);\n  url.searchParams.append(\"projectId\", ConfigCtrl.state.projectId);\n  Object.entries(allParams).forEach(([key, value]) => {\n    if (value) {\n      url.searchParams.append(key, String(value));\n    }\n  });\n  const request = await fetch(url);\n  return request.json();\n}\nconst ExplorerUtil = {\n  async getDesktopListings(params) {\n    return fetchListings(\"/w3m/v1/getDesktopListings\", params);\n  },\n  async getMobileListings(params) {\n    return fetchListings(\"/w3m/v1/getMobileListings\", params);\n  },\n  async getInjectedListings(params) {\n    return fetchListings(\"/w3m/v1/getInjectedListings\", params);\n  },\n  async getAllListings(params) {\n    return fetchListings(\"/w3m/v1/getAllListings\", params);\n  },\n  getWalletImageUrl(imageId) {\n    return `${W3M_API}/w3m/v1/getWalletImage/${imageId}?projectId=${ConfigCtrl.state.projectId}&sdkType=${SDK_TYPE}&sdkVersion=${SDK_VERSION}`;\n  },\n  getAssetImageUrl(imageId) {\n    return `${W3M_API}/w3m/v1/getAssetImage/${imageId}?projectId=${ConfigCtrl.state.projectId}&sdkType=${SDK_TYPE}&sdkVersion=${SDK_VERSION}`;\n  }\n};\n\nvar __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nconst isMobile = CoreUtil.isMobile();\nconst state$3 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  wallets: { listings: [], total: 0, page: 1 },\n  search: { listings: [], total: 0, page: 1 },\n  recomendedWallets: []\n});\nconst ExplorerCtrl = {\n  state: state$3,\n  async getRecomendedWallets() {\n    const { explorerRecommendedWalletIds, explorerExcludedWalletIds } = ConfigCtrl.state;\n    if (explorerRecommendedWalletIds === \"NONE\" || explorerExcludedWalletIds === \"ALL\" && !explorerRecommendedWalletIds) {\n      return state$3.recomendedWallets;\n    }\n    if (CoreUtil.isArray(explorerRecommendedWalletIds)) {\n      const recommendedIds = explorerRecommendedWalletIds.join(\",\");\n      const params = { recommendedIds };\n      const { listings } = await ExplorerUtil.getAllListings(params);\n      const listingsArr = Object.values(listings);\n      listingsArr.sort((a, b) => {\n        const aIndex = explorerRecommendedWalletIds.indexOf(a.id);\n        const bIndex = explorerRecommendedWalletIds.indexOf(b.id);\n        return aIndex - bIndex;\n      });\n      state$3.recomendedWallets = listingsArr;\n    } else {\n      const { chains, isAuth } = OptionsCtrl.state;\n      const chainsFilter = chains == null ? void 0 : chains.join(\",\");\n      const isExcluded = CoreUtil.isArray(explorerExcludedWalletIds);\n      const params = {\n        page: 1,\n        sdks: isAuth ? \"auth_v1\" : void 0,\n        entries: CoreUtil.RECOMMENDED_WALLET_AMOUNT,\n        chains: chainsFilter,\n        version: 2,\n        excludedIds: isExcluded ? explorerExcludedWalletIds.join(\",\") : void 0\n      };\n      const { listings } = isMobile ? await ExplorerUtil.getMobileListings(params) : await ExplorerUtil.getDesktopListings(params);\n      state$3.recomendedWallets = Object.values(listings);\n    }\n    return state$3.recomendedWallets;\n  },\n  async getWallets(params) {\n    const extendedParams = __spreadValues$1({}, params);\n    const { explorerRecommendedWalletIds, explorerExcludedWalletIds } = ConfigCtrl.state;\n    const { recomendedWallets } = state$3;\n    if (explorerExcludedWalletIds === \"ALL\") {\n      return state$3.wallets;\n    }\n    if (recomendedWallets.length) {\n      extendedParams.excludedIds = recomendedWallets.map((wallet) => wallet.id).join(\",\");\n    } else if (CoreUtil.isArray(explorerRecommendedWalletIds)) {\n      extendedParams.excludedIds = explorerRecommendedWalletIds.join(\",\");\n    }\n    if (CoreUtil.isArray(explorerExcludedWalletIds)) {\n      extendedParams.excludedIds = [extendedParams.excludedIds, explorerExcludedWalletIds].filter(Boolean).join(\",\");\n    }\n    if (OptionsCtrl.state.isAuth) {\n      extendedParams.sdks = \"auth_v1\";\n    }\n    const { page, search } = params;\n    const { listings: listingsObj, total } = isMobile ? await ExplorerUtil.getMobileListings(extendedParams) : await ExplorerUtil.getDesktopListings(extendedParams);\n    const listings = Object.values(listingsObj);\n    const type = search ? \"search\" : \"wallets\";\n    state$3[type] = {\n      listings: [...state$3[type].listings, ...listings],\n      total,\n      page: page != null ? page : 1\n    };\n    return { listings, total };\n  },\n  getWalletImageUrl(imageId) {\n    return ExplorerUtil.getWalletImageUrl(imageId);\n  },\n  getAssetImageUrl(imageId) {\n    return ExplorerUtil.getAssetImageUrl(imageId);\n  },\n  resetSearch() {\n    state$3.search = { listings: [], total: 0, page: 1 };\n  }\n};\n\nconst state$2 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  open: false\n});\nconst ModalCtrl = {\n  state: state$2,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$2, () => callback(state$2));\n  },\n  async open(options) {\n    return new Promise((resolve) => {\n      const { isUiLoaded, isDataLoaded } = OptionsCtrl.state;\n      CoreUtil.removeWalletConnectDeepLink();\n      OptionsCtrl.setWalletConnectUri(options == null ? void 0 : options.uri);\n      OptionsCtrl.setChains(options == null ? void 0 : options.chains);\n      RouterCtrl.reset(\"ConnectWallet\");\n      if (isUiLoaded && isDataLoaded) {\n        state$2.open = true;\n        resolve();\n      } else {\n        const interval = setInterval(() => {\n          const opts = OptionsCtrl.state;\n          if (opts.isUiLoaded && opts.isDataLoaded) {\n            clearInterval(interval);\n            state$2.open = true;\n            resolve();\n          }\n        }, 200);\n      }\n    });\n  },\n  close() {\n    state$2.open = false;\n  }\n};\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nfunction isDarkMode() {\n  return typeof matchMedia !== \"undefined\" && matchMedia(\"(prefers-color-scheme: dark)\").matches;\n}\nconst state$1 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  themeMode: isDarkMode() ? \"dark\" : \"light\"\n});\nconst ThemeCtrl = {\n  state: state$1,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state$1, () => callback(state$1));\n  },\n  setThemeConfig(theme) {\n    const { themeMode, themeVariables } = theme;\n    if (themeMode) {\n      state$1.themeMode = themeMode;\n    }\n    if (themeVariables) {\n      state$1.themeVariables = __spreadValues({}, themeVariables);\n    }\n  }\n};\n\nconst state = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({\n  open: false,\n  message: \"\",\n  variant: \"success\"\n});\nconst ToastCtrl = {\n  state,\n  subscribe(callback) {\n    return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(state, () => callback(state));\n  },\n  openToast(message, variant) {\n    state.open = true;\n    state.message = message;\n    state.variant = variant;\n  },\n  closeToast() {\n    state.open = false;\n  }\n};\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/modal-core/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/proxy-compare/dist/index.modern.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/modal-core/node_modules/proxy-compare/dist/index.modern.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   affectedToPathList: () => (/* binding */ w),\n/* harmony export */   createProxy: () => (/* binding */ a),\n/* harmony export */   getUntracked: () => (/* binding */ y),\n/* harmony export */   isChanged: () => (/* binding */ p),\n/* harmony export */   markToTrack: () => (/* binding */ h),\n/* harmony export */   replaceNewProxy: () => (/* binding */ O),\n/* harmony export */   trackMemo: () => (/* binding */ g)\n/* harmony export */ });\nconst e=Symbol(),t=Symbol(),r=\"a\",n=\"w\";let o=(e,t)=>new Proxy(e,t);const s=Object.getPrototypeOf,c=new WeakMap,l=e=>e&&(c.has(e)?c.get(e):s(e)===Object.prototype||s(e)===Array.prototype),f=e=>\"object\"==typeof e&&null!==e,i=e=>{if(Array.isArray(e))return Array.from(e);const t=Object.getOwnPropertyDescriptors(e);return Object.values(t).forEach(e=>{e.configurable=!0}),Object.create(s(e),t)},u=e=>e[t]||e,a=(s,c,f,p)=>{if(!l(s))return s;let g=p&&p.get(s);if(!g){const e=u(s);g=(e=>Object.values(Object.getOwnPropertyDescriptors(e)).some(e=>!e.configurable&&!e.writable))(e)?[e,i(e)]:[e],null==p||p.set(s,g)}const[y,h]=g;let w=f&&f.get(y);return w&&w[1].f===!!h||(w=((o,s)=>{const c={f:s};let l=!1;const f=(e,t)=>{if(!l){let s=c[r].get(o);if(s||(s={},c[r].set(o,s)),e===n)s[n]=!0;else{let r=s[e];r||(r=new Set,s[e]=r),r.add(t)}}},i={get:(e,n)=>n===t?o:(f(\"k\",n),a(Reflect.get(e,n),c[r],c.c,c.t)),has:(t,n)=>n===e?(l=!0,c[r].delete(o),!0):(f(\"h\",n),Reflect.has(t,n)),getOwnPropertyDescriptor:(e,t)=>(f(\"o\",t),Reflect.getOwnPropertyDescriptor(e,t)),ownKeys:e=>(f(n),Reflect.ownKeys(e))};return s&&(i.set=i.deleteProperty=()=>!1),[i,c]})(y,!!h),w[1].p=o(h||y,w[0]),f&&f.set(y,w)),w[1][r]=c,w[1].c=f,w[1].t=p,w[1].p},p=(e,t,r,o)=>{if(Object.is(e,t))return!1;if(!f(e)||!f(t))return!0;const s=r.get(u(e));if(!s)return!0;if(o){const r=o.get(e);if(r&&r.n===t)return r.g;o.set(e,{n:t,g:!1})}let c=null;try{for(const r of s.h||[])if(c=Reflect.has(e,r)!==Reflect.has(t,r),c)return c;if(!0===s[n]){if(c=((e,t)=>{const r=Reflect.ownKeys(e),n=Reflect.ownKeys(t);return r.length!==n.length||r.some((e,t)=>e!==n[t])})(e,t),c)return c}else for(const r of s.o||[])if(c=!!Reflect.getOwnPropertyDescriptor(e,r)!=!!Reflect.getOwnPropertyDescriptor(t,r),c)return c;for(const n of s.k||[])if(c=p(e[n],t[n],r,o),c)return c;return null===c&&(c=!0),c}finally{o&&o.set(e,{n:t,g:c})}},g=t=>!!l(t)&&e in t,y=e=>l(e)&&e[t]||null,h=(e,t=!0)=>{c.set(e,t)},w=(e,t,r)=>{const o=[],s=new WeakSet,c=(e,l)=>{if(s.has(e))return;f(e)&&s.add(e);const i=f(e)&&t.get(u(e));if(i){var a,p;if(null==(a=i.h)||a.forEach(e=>{const t=`:has(${String(e)})`;o.push(l?[...l,t]:[t])}),!0===i[n]){const e=\":ownKeys\";o.push(l?[...l,e]:[e])}else{var g;null==(g=i.o)||g.forEach(e=>{const t=`:hasOwn(${String(e)})`;o.push(l?[...l,t]:[t])})}null==(p=i.k)||p.forEach(t=>{r&&!(\"value\"in(Object.getOwnPropertyDescriptor(e,t)||{}))||c(e[t],l?[...l,t]:[t])})}else l&&o.push(l)};return c(e),o},O=e=>{o=e};\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/proxy-compare/dist/index.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/valtio/esm/vanilla.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/@walletconnect/modal-core/node_modules/valtio/esm/vanilla.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   proxy: () => (/* binding */ proxy),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   snapshot: () => (/* binding */ snapshot),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unstable_buildProxyFunction: () => (/* binding */ unstable_buildProxyFunction)\n/* harmony export */ });\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! proxy-compare */ \"(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/proxy-compare/dist/index.modern.js\");\n\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const desc = {\n      value,\n      enumerable: true,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if (( false ? 0 : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if (( false ? 0 : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.getUntracked)(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/modal-core/node_modules/valtio/esm/vanilla.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/modal/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@walletconnect/modal/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnectModal: () => (/* binding */ WalletConnectModal)\n/* harmony export */ });\n/* harmony import */ var _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @walletconnect/modal-core */ \"(app-pages-browser)/./node_modules/@walletconnect/modal-core/dist/index.js\");\n\n\nclass WalletConnectModal {\n  constructor(config) {\n    this.openModal = _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalCtrl.open;\n    this.closeModal = _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalCtrl.close;\n    this.subscribeModal = _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ModalCtrl.subscribe;\n    this.setTheme = _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ThemeCtrl.setThemeConfig;\n    _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ThemeCtrl.setThemeConfig(config);\n    _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.ConfigCtrl.setConfig(config);\n    this.initUi();\n  }\n  async initUi() {\n    if (typeof window !== \"undefined\") {\n      await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_walletconnect_modal-ui_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/modal-ui */ \"(app-pages-browser)/./node_modules/@walletconnect/modal-ui/dist/index.js\"));\n      const modal = document.createElement(\"wcm-modal\");\n      document.body.insertAdjacentElement(\"beforeend\", modal);\n      _walletconnect_modal_core__WEBPACK_IMPORTED_MODULE_0__.OptionsCtrl.setIsUiLoaded(true);\n    }\n  }\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9tb2RhbC9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBGOztBQUUxRjtBQUNBO0FBQ0EscUJBQXFCLGdFQUFTO0FBQzlCLHNCQUFzQixnRUFBUztBQUMvQiwwQkFBMEIsZ0VBQVM7QUFDbkMsb0JBQW9CLGdFQUFTO0FBQzdCLElBQUksZ0VBQVM7QUFDYixJQUFJLGlFQUFVO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDRRQUFpQztBQUM3QztBQUNBO0FBQ0EsTUFBTSxrRUFBVztBQUNqQjtBQUNBO0FBQ0E7O0FBRThCO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxAd2FsbGV0Y29ubmVjdFxcbW9kYWxcXGRpc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vZGFsQ3RybCwgVGhlbWVDdHJsLCBDb25maWdDdHJsLCBPcHRpb25zQ3RybCB9IGZyb20gJ0B3YWxsZXRjb25uZWN0L21vZGFsLWNvcmUnO1xuXG5jbGFzcyBXYWxsZXRDb25uZWN0TW9kYWwge1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICB0aGlzLm9wZW5Nb2RhbCA9IE1vZGFsQ3RybC5vcGVuO1xuICAgIHRoaXMuY2xvc2VNb2RhbCA9IE1vZGFsQ3RybC5jbG9zZTtcbiAgICB0aGlzLnN1YnNjcmliZU1vZGFsID0gTW9kYWxDdHJsLnN1YnNjcmliZTtcbiAgICB0aGlzLnNldFRoZW1lID0gVGhlbWVDdHJsLnNldFRoZW1lQ29uZmlnO1xuICAgIFRoZW1lQ3RybC5zZXRUaGVtZUNvbmZpZyhjb25maWcpO1xuICAgIENvbmZpZ0N0cmwuc2V0Q29uZmlnKGNvbmZpZyk7XG4gICAgdGhpcy5pbml0VWkoKTtcbiAgfVxuICBhc3luYyBpbml0VWkoKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgIGF3YWl0IGltcG9ydCgnQHdhbGxldGNvbm5lY3QvbW9kYWwtdWknKTtcbiAgICAgIGNvbnN0IG1vZGFsID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcIndjbS1tb2RhbFwiKTtcbiAgICAgIGRvY3VtZW50LmJvZHkuaW5zZXJ0QWRqYWNlbnRFbGVtZW50KFwiYmVmb3JlZW5kXCIsIG1vZGFsKTtcbiAgICAgIE9wdGlvbnNDdHJsLnNldElzVWlMb2FkZWQodHJ1ZSk7XG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCB7IFdhbGxldENvbm5lY3RNb2RhbCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/modal/dist/index.js\n"));

/***/ })

}]);