globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/shared/chat/ChatNotificationListener.tsx":{"*":{"id":"(ssr)/./src/components/shared/chat/ChatNotificationListener.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/footer/index.tsx":{"*":{"id":"(ssr)/./src/components/shared/footer/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/header/index.tsx":{"*":{"id":"(ssr)/./src/components/shared/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/I18nInitializer.tsx":{"*":{"id":"(ssr)/./src/components/shared/I18nInitializer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/runningLine/index.tsx":{"*":{"id":"(ssr)/./src/components/ui/runningLine/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AppContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AppContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx":{"*":{"id":"(ssr)/./src/contexts/GlobalModalContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PrivyContext.tsx":{"*":{"id":"(ssr)/./src/contexts/PrivyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SocketProvider.tsx":{"*":{"id":"(ssr)/./src/contexts/SocketProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/perks-shop/page.tsx":{"*":{"id":"(ssr)/./src/app/perks-shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/perks-shop/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/perks-shop/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\constants\\\\layout.ts\",\"import\":\"IBM_Plex_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-sans\",\"display\":\"swap\",\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"IbmFont\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\constants\\\\layout.ts\",\"import\":\"IBM_Plex_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-sans\",\"display\":\"swap\",\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"IbmFont\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\components\\shared\\chat\\ChatNotificationListener.tsx":{"id":"(app-pages-browser)/./src/components/shared/chat/ChatNotificationListener.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\components\\shared\\footer\\index.tsx":{"id":"(app-pages-browser)/./src/components/shared/footer/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\components\\shared\\header\\index.tsx":{"id":"(app-pages-browser)/./src/components/shared/header/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\components\\shared\\I18nInitializer.tsx":{"id":"(app-pages-browser)/./src/components/shared/I18nInitializer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\components\\ui\\runningLine\\index.tsx":{"id":"(app-pages-browser)/./src/components/ui/runningLine/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\contexts\\AppContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AppContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\contexts\\GlobalModalContext.tsx":{"id":"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\contexts\\PrivyContext.tsx":{"id":"(app-pages-browser)/./src/contexts/PrivyContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\contexts\\SocketProvider.tsx":{"id":"(app-pages-browser)/./src/contexts/SocketProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\styles\\globals.css":{"id":"(app-pages-browser)/./src/styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\app\\perks-shop\\page.tsx":{"id":"(app-pages-browser)/./src/app/perks-shop/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\app\\perks-shop\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/perks-shop/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Ahmed Barakat\\funHi-project\\funHi-project\\front-end\\src\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/components/shared/chat/ChatNotificationListener.tsx":{"*":{"id":"(rsc)/./src/components/shared/chat/ChatNotificationListener.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/footer/index.tsx":{"*":{"id":"(rsc)/./src/components/shared/footer/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/header/index.tsx":{"*":{"id":"(rsc)/./src/components/shared/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/I18nInitializer.tsx":{"*":{"id":"(rsc)/./src/components/shared/I18nInitializer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/runningLine/index.tsx":{"*":{"id":"(rsc)/./src/components/ui/runningLine/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AppContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AppContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx":{"*":{"id":"(rsc)/./src/contexts/GlobalModalContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PrivyContext.tsx":{"*":{"id":"(rsc)/./src/contexts/PrivyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SocketProvider.tsx":{"*":{"id":"(rsc)/./src/contexts/SocketProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/globals.css":{"*":{"id":"(rsc)/./src/styles/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/perks-shop/page.tsx":{"*":{"id":"(rsc)/./src/app/perks-shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/perks-shop/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/perks-shop/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}