import React, { useEffect, useState } from "react";
import axios from "axios";
import ChatModal from "../chat/ChatModal";
import { useTranslation } from '@/hooks/useTranslation';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8081";

const TradeList: React.FC<{ userId: string }> = ({ userId }) => {
  const [trades, setTrades] = useState<any[]>([]);
  const [openChatTradeId, setOpenChatTradeId] = useState<string | null>(null);
  const [chatReceiverId, setChatReceiverId] = useState<string | null>(null);
  const { t } = useTranslation();

  useEffect(() => {
    async function fetchTrades() {
      try {
        const res = await axios.get(`${API_BASE}/users/${userId}/trades`);
        setTrades(res.data.data || []);
      } catch (err) {
        setTrades([]);
      }
    }
    if (userId) fetchTrades();
  }, [userId]);

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl p-6">
      <h2 className="text-xl font-bold mb-4">{t('tradeList.yourTrades')}</h2>
      {trades.length === 0 ? (
        <div className="text-gray-500">{t('tradeList.noTradesFound')}</div>
      ) : (
        <ul className="divide-y divide-gray-200">
          {trades.map((trade) => (
            <li key={trade.id} className="py-4 flex items-center justify-between">
              <div>
                <div className="font-semibold">{t('tradeList.perk')}: {trade.perkId}</div>
                <div className="text-sm text-gray-500">{t('tradeList.status')}: {trade.status}</div>
              </div>
              <button
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600"
                onClick={() => {
                  setOpenChatTradeId(trade.id.toString());
                  setChatReceiverId(trade.userId === userId ? trade.to?.toString() : trade.userId?.toString());
                }}
              >
                {t('tradeList.chat')}
              </button>
            </li>
          ))}
        </ul>
      )}
      {openChatTradeId && chatReceiverId && (
        <ChatModal
          chatRoomId={openChatTradeId}
          buyerId={trades.find(t => t.id.toString() === openChatTradeId)?.buyerId}
          sellerId={trades.find(t => t.id.toString() === openChatTradeId)?.sellerId}
          onClose={() => setOpenChatTradeId(null)}
        />
      )}
    </div>
  );
};

export default TradeList; 