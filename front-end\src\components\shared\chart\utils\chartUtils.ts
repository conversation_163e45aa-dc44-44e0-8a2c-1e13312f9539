import { AgChartOptions } from 'ag-charts-enterprise';

export interface ChartData {
  time: number;
  open: number;
  close: number;
  low: number;
  high: number;
  volume: number;
}

class ChartError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ChartError';
  }
}

export const generateMockCandles = (
  count = 50,
  dateNow = 0,
  startPrice = 0.0033,
  intervalInMs = 60000
): ChartData[] => {
  if (count <= 0) {
    throw new ChartError('Count must be greater than 0');
  }

  if (startPrice <= 0) {
    throw new ChartError('Start price must be greater than 0');
  }

  if (intervalInMs <= 0) {
    throw new ChartError('Interval must be greater than 0');
  }

  try {
    const candles: ChartData[] = [];
    const timestampStart = dateNow - count * intervalInMs;
    let lastClose = startPrice;

    for (let i = 0; i < count; i++) {
      const time = timestampStart + i * intervalInMs;
      const open = lastClose;
      const high = open + Math.random() * 0.00005;
      const low = open - Math.random() * 0.00005;
      const close = low + Math.random() * (high - low);

      candles.push({
        time,
        open: +open.toFixed(6),
        high: +high.toFixed(6),
        low: +low.toFixed(6),
        close: +close.toFixed(6),
        volume: Math.floor(Math.random() * 100),
      });

      lastClose = close;
    }

    return candles;
  } catch (error) {
    throw new ChartError(`Failed to generate mock candles: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getChartOptions = (data: ChartData[], windowWidth: number): AgChartOptions => {
  if (!Array.isArray(data)) {
    throw new ChartError('Data must be an array');
  }

  if (data.length === 0) {
    throw new ChartError('Data array cannot be empty');
  }

  if (windowWidth <= 0) {
    throw new ChartError('Window width must be greater than 0');
  }

  try {
    return {
      width: windowWidth,
      height: windowWidth < 600 ? windowWidth : windowWidth / 1.5,
      data: data,
      theme: 'ag-default-dark',
      background: {
        fill: '#ffffff',
      },
      padding: {
        bottom: 50,
      },
      zoom: {
        enabled: true,
      },
      annotations: {
        toolbar: {
          padding: 0,
          buttons: [
            { value: 'line-menu', icon: 'line-style-solid' },
            { value: 'fibonacci-menu', icon: 'fibonacci-retracement-drawing' },
            { value: 'text-menu', icon: 'text-annotation' },
            { value: 'shape-menu', icon: 'trend-line-drawing' },
            { value: 'measurer-menu', icon: 'measurer-drawing' },
            { value: 'clear', icon: 'delete' }
          ],
        },
      },
      series: [
        {
          type: 'candlestick' as const,
          xKey: 'time',
          openKey: 'open',
          highKey: 'high',
          lowKey: 'low',
          closeKey: 'close',
          tooltip: {
            renderer: (params: { datum: ChartData }) => {
              try {
                const { datum } = params;
                return `
                  <div style="padding: 4px;">
                    <b>${new Date(datum.time).toLocaleString()}</b><br/>
                    Open: ${datum.open}<br/>
                    High: ${datum.high}<br/>
                    Low: ${datum.low}<br/>
                    Close: ${datum.close}<br/>
                  </div>
                `;
              } catch (error) {
                console.error('Tooltip render error:', error);
                return '<div style="padding: 4px;">Error loading tooltip data</div>';
              }
            },
          },
          item: {
            up: {
              fill: '#3BB266',
              stroke: '#3BB266',
              wick: {
                strokeWidth: 2,
              },
            },
            down: {
              fill: '#E10000',
              stroke: '#E10000',
              wick: {
                strokeWidth: 2,
              },
            },
          },
        },
      ],
      axes: [
        {
          type: 'ordinal-time',
          position: 'bottom',
          crosshair: {
            enabled: true,
          },
          label: {
            autoRotate: true,
            autoRotateAngle: 90,
            rotation: windowWidth > 576 ? 0 : 90,
            format: '%H:%M',
            color: '#111112',
          },
          tick: {
            enabled: true,
          },
        },
        {
          type: 'number',
          position: 'right',
          thickness: windowWidth < 600 ? 40 : 50,
          label: {
            color: '#111112',
          },
        },
      ],
    };
  } catch (error) {
    throw new ChartError(`Failed to generate chart options: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}; 