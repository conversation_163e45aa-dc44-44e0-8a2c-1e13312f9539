import { Keypair, PublicKey, SystemProgram, TransactionInstruction, SYSVAR_RENT_PUBKEY } from "@solana/web3.js";
import { program } from "./setup";
import { global, getBondingCurvePda, getCreatorVault } from "./pdas";
import { ASSOCIATED_TOKEN_PROGRAM_ID, TOKEN_PROGRAM_ID, getAssociatedTokenAddress } from "@solana/spl-token";

const TOKEN_METADATA_PROGRAM_ID = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");

export const create = async (
  token_name: string,
  token_symbol: string,
  token_uri: string,
  payer: Keypair,
  creator: PublicKey,
  mint: PublicKey,
): Promise<TransactionInstruction> => {
  const globalData = await program.account.global.fetch(global);
  if (!globalData) {
    throw new Error("Global account not found");
  }

  const bondingCurve = getBondingCurvePda(mint);
  const bondingCurveAta = await getAssociatedTokenAddress(mint, bondingCurve, true);

  const [metadataAccount] = PublicKey.findProgramAddressSync(
    [
      Buffer.from("metadata"),
      TOKEN_METADATA_PROGRAM_ID.toBuffer(),
      mint.toBuffer(),
    ],
    TOKEN_METADATA_PROGRAM_ID,
  );

  const creatorVault = getCreatorVault(mint, creator);
  const creatorVaultAta = await getAssociatedTokenAddress(mint, creatorVault, true);


  const createTx = await program.methods
    .create(token_name, token_symbol, token_uri)
    .accountsStrict({
      signer: payer.publicKey,
      creator: creator,
      feeRecipient: globalData.feeRecipient,
      mint: mint,
      global: global,
      bondingCurve: bondingCurve,
      bondingCurveAta: bondingCurveAta,
      metadataAccount: metadataAccount,
      creatorVault: creatorVault,
      creatorVaultAta: creatorVaultAta,
      tokenMetadataProgram: TOKEN_METADATA_PROGRAM_ID,
      associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
      tokenProgram: TOKEN_PROGRAM_ID,
      systemProgram: SystemProgram.programId,
      rent: SYSVAR_RENT_PUBKEY,
    })
    .instruction();
  return createTx;
};
