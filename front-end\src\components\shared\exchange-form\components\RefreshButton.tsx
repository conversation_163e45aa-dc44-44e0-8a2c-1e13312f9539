"use client";

import { motion } from "framer-motion";
import { RefreshCw } from "lucide-react";
import React from "react";

import type { RefreshButtonProps } from "../types";
import { useTranslation } from '../../../../hooks/useTranslation';

const RefreshButton: React.FC<RefreshButtonProps> = ({ 
  onClick, 
  isRefreshing, 
  isLoading 
}) => {
  const { t } = useTranslation();
  return (
    <motion.button
      className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
      onClick={onClick}
      disabled={isRefreshing || isLoading}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <motion.div
        animate={isRefreshing ? { rotate: 360 } : {}}
        transition={
          isRefreshing
            ? { duration: 1, repeat: Infinity, ease: "linear" }
            : {}
        }
      >
        <RefreshCw size={16} />
      </motion.div>
      <span className="text-sm">{t('exchangeForm.refreshBalances')}</span>
    </motion.button>
  );
};

export default RefreshButton; 