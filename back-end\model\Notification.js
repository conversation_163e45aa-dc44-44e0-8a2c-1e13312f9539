const { Model, DataTypes, Op } = require("sequelize");

class Notification extends Model {
    static initModel(sequelize) {
        return Notification.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                type: {
                    type: DataTypes.ENUM(
                        // System notifications (non-trade)
                        'system_announcement',
                        'moderator_added',
                        'perk_created',
                        'token_created',
                        // Trade and escrow notifications
                        'perk_purchased',
                        'perk_sold',
                        'escrow_created',
                        'escrow_pending_acceptance',
                        'escrow_accepted',
                        'escrow_released',
                        'escrow_release_reminder',
                        'trade_completed',
                        'trade_refunded',
                        'trade_reported',
                        'trade_disputed',
                        // Dispute notifications
                        'dispute_created',
                        'dispute_assigned',
                        'dispute_resolved',
                        // Chat notifications
                        'chat_message'
                    ),
                    allowNull: false,
                },
                title: {
                    type: DataTypes.STRING(255),
                    allowNull: false,
                },
                message: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                data: {
                    type: DataTypes.JSON,
                    allowNull: true,
                    comment: 'Additional data related to the notification (e.g., dispute ID, trade ID)',
                },
                isRead: {
                    type: DataTypes.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                priority: {
                    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
                    allowNull: false,
                    defaultValue: 'medium',
                },
                expiresAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    comment: 'When the notification expires (optional)',
                },
                actionUrl: {
                    type: DataTypes.STRING(500),
                    allowNull: true,
                    comment: 'URL to navigate to when notification is clicked',
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'notifications',
                timestamps: true,
                indexes: [
                    {
                        fields: ['userId'],
                    },
                    {
                        fields: ['type'],
                    },
                    {
                        fields: ['isRead'],
                    },
                    {
                        fields: ['priority'],
                    },
                    {
                        fields: ['createdAt'],
                    },
                ],
            }
        );
    }

    static associate(models) {
        // Notification belongs to a user
        Notification.belongsTo(models.User, { 
            foreignKey: 'userId', 
            as: 'user' 
        });
    }

    // Instance method to mark as read
    async markAsRead() {
        this.isRead = true;
        await this.save();
    }

    // Static method to create dispute notification for all active moderators
    static async createDisputeNotification(disputeId, tradeId, initiatorRole, reason) {
        try {
            const { Moderator } = require('./index');
            
            // Get all active moderators
            const activeModerators = await Moderator.findAll({
                where: { isActive: true },
                include: [{ model: this.sequelize.models.User, as: 'user' }]
            });

            const notifications = activeModerators.map(moderator => ({
                userId: moderator.userId,
                type: 'dispute_created',
                title: 'New Dispute Created',
                message: `A new dispute has been initiated by the ${initiatorRole} for trade #${tradeId}. Reason: ${reason.substring(0, 100)}${reason.length > 100 ? '...' : ''}`,
                data: {
                    disputeId,
                    tradeId,
                    initiatorRole,
                    reason
                },
                priority: 'high',
                actionUrl: `/moderator/dashboard?dispute=${disputeId}`
            }));

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating dispute notifications:', error);
            return 0;
        }
    }

    // Static method to create assignment notification
    static async createAssignmentNotification(disputeId, moderatorId, tradeId) {
        try {
            await this.create({
                userId: moderatorId,
                type: 'dispute_assigned',
                title: 'Dispute Assigned to You',
                message: `You have been assigned to resolve dispute for trade #${tradeId}. Please review the case details.`,
                data: {
                    disputeId,
                    tradeId
                },
                priority: 'high',
                actionUrl: `/moderator/dashboard?dispute=${disputeId}`
            });
        } catch (error) {
            console.error('Error creating assignment notification:', error);
        }
    }

    // Static method to create resolution notification
    static async createResolutionNotification(disputeId, buyerId, sellerId, resolution, tradeId, perkId, chatRoomId) {
        try {
            // Remove route dependency - notifications will open chat modals globally
            const notifications = [
                {
                    userId: buyerId,
                    type: 'dispute_resolved',
                    title: 'Dispute Resolved',
                    message: `Your dispute for trade #${tradeId} has been resolved. Resolution: ${resolution.replace('resolved_', '').replace('_', ' ')}. Click to view chat.`,
                    data: {
                        disputeId,
                        tradeId,
                        perkId,
                        resolution,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: null // Remove route dependency
                },
                {
                    userId: sellerId,
                    type: 'dispute_resolved',
                    title: 'Dispute Resolved',
                    message: `The dispute for trade #${tradeId} has been resolved. Resolution: ${resolution.replace('resolved_', '').replace('_', ' ')}. Click to view chat.`,
                    data: {
                        disputeId,
                        tradeId,
                        perkId,
                        resolution,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: null // Remove route dependency
                }
            ];

            await this.bulkCreate(notifications);
        } catch (error) {
            console.error('Error creating resolution notifications:', error);
        }
    }

    // Static method to get unread count for a user
    static async getUnreadCount(userId) {
        try {
            return await this.count({
                where: {
                    userId,
                    isRead: false,
                    expiresAt: {
                        [Op.or]: [
                            null,
                            { [Op.gt]: new Date() }
                        ]
                    }
                }
            });
        } catch (error) {
            console.error('Error getting unread count:', error);
            return 0;
        }
    }

    // Static method to create system-wide notifications for all users
    static async createSystemNotificationForAllUsers(type, title, message, data = {}, priority = 'medium', actionUrl = null) {
        try {
            // Get all active users using sequelize instance to avoid circular dependency
            console.log('🔍 [Notification] Fetching all users for system notification...');
            const allUsers = await this.sequelize.models.User.findAll({
                attributes: ['id'],
                where: {
                    // Add any conditions for active users if needed
                },
                limit: 5000 // Reasonable limit for system notifications
            });

            console.log(`📊 [Notification] Found ${allUsers.length} users for system notification`);

            if (allUsers.length === 0) {
                console.log('⚠️ [Notification] No users found for system notification');
                return 0;
            }



            // Create notifications for all users
            const notifications = allUsers.map(user => ({
                userId: user.id,
                type,
                title,
                message,
                data,
                priority,
                actionUrl
            }));

            console.log(`📝 [Notification] Attempting to create ${notifications.length} notifications of type: ${type}`);

            await this.bulkCreate(notifications);
            console.log(`✅ [Notification] Successfully created ${type} notifications for ${notifications.length} users`);

            // Broadcast system notification via Socket.IO for real-time updates
            if (global.broadcastSystemNotification) {
                console.log(`📡 [Notification] Broadcasting ${type} notification via Socket.IO`);
                global.broadcastSystemNotification({
                    type,
                    title,
                    message,
                    data,
                    priority,
                    actionUrl,
                    timestamp: new Date().toISOString()
                });
            } else {
                console.log('⚠️ [Notification] Socket.IO broadcast function not available');
            }

            return notifications.length;
        } catch (error) {
            console.error('❌ [Notification] Error creating system notification for all users:', error);
            console.error('❌ [Notification] Error details:', {
                message: error.message,
                name: error.name,
                sql: error.sql || 'No SQL',
                original: error.original?.message || 'No original error'
            });

            // Check if it's an enum constraint error
            if (error.message && error.message.includes('invalid input value for enum')) {
                console.error('🚨 [Notification] DATABASE ENUM ERROR: The notification type is not in the database enum. Database migration needed!');
            }

            return 0;
        }
    }

    // Static method to create escrow acceptance notification for seller
    static async createEscrowAcceptanceNotification(tradeId, sellerId, buyerUsername, perkTitle, chatRoomId) {
        try {
            await this.create({
                userId: sellerId,
                type: 'escrow_pending_acceptance',
                title: 'Escrow Awaiting Your Acceptance',
                message: `${buyerUsername || 'A buyer'} has created an escrow for "${perkTitle}". Please accept the escrow to proceed with the transaction.`,
                data: {
                    tradeId,
                    perkTitle,
                    buyerUsername,
                    chatRoomId
                },
                priority: 'high',
                actionUrl: `/perks-shop/trade/${tradeId}`
            });
            console.log(`✅ [Notification] Created escrow acceptance notification for seller ${sellerId}`);
        } catch (error) {
            console.error('Error creating escrow acceptance notification:', error);
        }
    }

    // Static method to create escrow accepted notification for buyer
    static async createEscrowAcceptedNotification(tradeId, buyerId, sellerUsername, perkTitle, chatRoomId) {
        try {
            await this.create({
                userId: buyerId,
                type: 'escrow_accepted',
                title: 'Escrow Accepted!',
                message: `${sellerUsername || 'The seller'} has accepted your escrow for "${perkTitle}". You can now release the funds when ready.`,
                data: {
                    tradeId,
                    perkTitle,
                    sellerUsername,
                    chatRoomId
                },
                priority: 'medium',
                actionUrl: `/perks-shop/trade/${tradeId}`
            });
            console.log(`✅ [Notification] Created escrow accepted notification for buyer ${buyerId}`);
        } catch (error) {
            console.error('Error creating escrow accepted notification:', error);
        }
    }

    // Static method to create perk creation notification (system notification for all users)
    static async createPerkCreationNotification(creatorId, perkName, perkId, creatorUsername) {
        try {
            return await this.createSystemNotificationForAllUsers(
                'perk_created',
                'New Perk Available!',
                `${creatorUsername || 'A user'} has created a new perk "${perkName}" in the marketplace. Check it out!`,
                {
                    perkId,
                    perkName,
                    creatorId,
                    creatorUsername
                },
                'medium',
                `/perks-shop/${perkId}`
            );
        } catch (error) {
            console.error('Error creating perk creation notification:', error);
            return 0;
        }
    }

    // Static method to create token creation notification (system notification for all users)
    static async createTokenCreationNotification(creatorId, tokenName, tokenId, tokenAddress, creatorUsername) {
        try {
            return await this.createSystemNotificationForAllUsers(
                'token_created',
                'New Token Available!',
                `${creatorUsername || 'A user'} has created a new token "${tokenName}" on the Solana blockchain. Explore the new token!`,
                {
                    tokenId,
                    tokenName,
                    tokenAddress,
                    creatorId,
                    creatorUsername
                },
                'medium',
                `/tokens/${tokenAddress}`
            );
        } catch (error) {
            console.error('Error creating token creation notification:', error);
            return 0;
        }
    }

    // Static method to create perk purchase notifications
    static async createPerkPurchaseNotifications(buyerId, sellerId, perkName, price, tradeId, chatRoomId, perkId) {
        try {
            // Remove specific route dependency - notifications will open chat modals globally
            const notifications = [
                {
                    userId: sellerId,
                    type: 'perk_sold',
                    title: 'New Perk Purchase!',
                    message: `Your perk "${perkName}" has been purchased for ${price} SOL. Click to open chat with buyer.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        price,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: null // Remove route dependency
                },
                {
                    userId: buyerId,
                    type: 'perk_purchased',
                    title: 'Perk Purchase Confirmed',
                    message: `You have successfully purchased "${perkName}" for ${price} SOL. Funds are now in escrow. Click to chat with seller.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        price,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: null // Remove route dependency
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating perk purchase notifications:', error);
            return 0;
        }
    }

    // Static method to create seller release reminder notification
    static async createSellerReleaseReminderNotification(sellerId, buyerId, perkName, tradeId, chatRoomId, perkId) {
        try {
            await this.create({
                userId: sellerId,
                type: 'escrow_release_reminder',
                title: 'Ready to Release Payment?',
                message: `The buyer has purchased your perk "${perkName}". After providing your service, click here to release the payment and complete the transaction.`,
                data: {
                    tradeId,
                    perkId,
                    perkName,
                    buyerId,
                    sellerId,
                    chatRoomId
                },
                priority: 'high',
                actionUrl: null // Remove route dependency - will open chat modal globally
            });
        } catch (error) {
            console.error('Error creating seller release reminder notification:', error);
        }
    }

    // Static method to create escrow release notifications
    static async createEscrowReleaseNotifications(buyerId, sellerId, perkName, tradeId, perkId, chatRoomId) {
        try {
            // Remove route dependency - notifications will open chat modals globally
            const notifications = [
                {
                    userId: sellerId,
                    type: 'escrow_released',
                    title: 'Payment Released!',
                    message: `Funds for "${perkName}" have been released to you. Trade completed successfully! Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: null // Remove route dependency
                },
                {
                    userId: buyerId,
                    type: 'trade_completed',
                    title: 'Trade Completed!',
                    message: `Your purchase of "${perkName}" has been completed. Funds have been released to the seller. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: null // Remove route dependency
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating escrow release notifications:', error);
            return 0;
        }
    }

    // Static method to create trade refund notifications
    static async createTradeRefundNotifications(buyerId, sellerId, perkName, tradeId, perkId, chatRoomId) {
        try {
            // Remove route dependency - notifications will open chat modals globally
            const notifications = [
                {
                    userId: buyerId,
                    type: 'trade_refunded',
                    title: 'Trade Refunded',
                    message: `Your purchase of "${perkName}" has been refunded. Funds have been returned to your wallet. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: null // Remove route dependency
                },
                {
                    userId: sellerId,
                    type: 'trade_refunded',
                    title: 'Trade Refunded',
                    message: `The purchase of your perk "${perkName}" has been refunded to the buyer. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: null // Remove route dependency
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating trade refund notifications:', error);
            return 0;
        }
    }

    // Static method to get notifications for a user
    static async getUserNotifications(userId, options = {}) {
        try {
            const { page = 1, pageSize = 20, unreadOnly = false } = options;
            const offset = (page - 1) * pageSize;

            const whereConditions = {
                userId,
                expiresAt: {
                    [Op.or]: [
                        null,
                        { [Op.gt]: new Date() }
                    ]
                }
            };

            if (unreadOnly) {
                whereConditions.isRead = false;
            }

            return await this.findAndCountAll({
                where: whereConditions,
                order: [['createdAt', 'DESC']],
                limit: parseInt(pageSize),
                offset: offset
            });
        } catch (error) {
            console.error('Error getting user notifications:', error);
            return { rows: [], count: 0 };
        }
    }
}

module.exports = Notification;
