"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_simplewebauthn_browser_dist_bundle_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@simplewebauthn/browser/dist/bundle/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/dist/bundle/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebAuthnAbortService: () => (/* binding */ WebAuthnAbortService),\n/* harmony export */   WebAuthnError: () => (/* binding */ WebAuthnError),\n/* harmony export */   base64URLStringToBuffer: () => (/* binding */ base64URLStringToBuffer),\n/* harmony export */   browserSupportsWebAuthn: () => (/* binding */ browserSupportsWebAuthn),\n/* harmony export */   browserSupportsWebAuthnAutofill: () => (/* binding */ browserSupportsWebAuthnAutofill),\n/* harmony export */   bufferToBase64URLString: () => (/* binding */ bufferToBase64URLString),\n/* harmony export */   platformAuthenticatorIsAvailable: () => (/* binding */ platformAuthenticatorIsAvailable),\n/* harmony export */   startAuthentication: () => (/* binding */ startAuthentication),\n/* harmony export */   startRegistration: () => (/* binding */ startRegistration)\n/* harmony export */ });\n/* [@simplewebauthn/browser@9.0.1] */\nfunction utf8StringToBuffer(value) {\n    return new TextEncoder().encode(value);\n}\n\nfunction bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n\nfunction base64URLStringToBuffer(base64URLString) {\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    const binary = atob(padded);\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n\nfunction browserSupportsWebAuthn() {\n    return (window?.PublicKeyCredential !== undefined &&\n        typeof window.PublicKeyCredential === 'function');\n}\n\nfunction toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: base64URLStringToBuffer(id),\n        transports: descriptor.transports,\n    };\n}\n\nfunction isValidDomain(hostname) {\n    return (hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n\nclass WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        super(message, { cause });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n\nfunction identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            return new WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            return new WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        return new WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            return new WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        return new WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            return new WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nclass BaseWebAuthnAbortService {\n    createNewAbortSignal() {\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\nconst WebAuthnAbortService = new BaseWebAuthnAbortService();\n\nconst attachments = ['cross-platform', 'platform'];\nfunction toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n\nasync function startRegistration(creationOptionsJSON) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    const publicKey = {\n        ...creationOptionsJSON,\n        challenge: base64URLStringToBuffer(creationOptionsJSON.challenge),\n        user: {\n            ...creationOptionsJSON.user,\n            id: utf8StringToBuffer(creationOptionsJSON.user.id),\n        },\n        excludeCredentials: creationOptionsJSON.excludeCredentials?.map(toPublicKeyCredentialDescriptor),\n    };\n    const options = { publicKey };\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(options));\n    }\n    catch (err) {\n        throw identifyRegistrationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = bufferToBase64URLString(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = bufferToBase64URLString(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            attestationObject: bufferToBase64URLString(response.attestationObject),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n\nfunction bufferToUTF8String(value) {\n    return new TextDecoder('utf-8').decode(value);\n}\n\nfunction browserSupportsWebAuthnAutofill() {\n    const globalPublicKeyCredential = window\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential.isConditionalMediationAvailable === undefined) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return globalPublicKeyCredential.isConditionalMediationAvailable();\n}\n\nfunction identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nasync function startAuthentication(requestOptionsJSON, useBrowserAutofill = false) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    let allowCredentials;\n    if (requestOptionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = requestOptionsJSON.allowCredentials?.map(toPublicKeyCredentialDescriptor);\n    }\n    const publicKey = {\n        ...requestOptionsJSON,\n        challenge: base64URLStringToBuffer(requestOptionsJSON.challenge),\n        allowCredentials,\n    };\n    const options = {};\n    if (useBrowserAutofill) {\n        if (!(await browserSupportsWebAuthnAutofill())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        const eligibleInputs = document.querySelectorAll('input[autocomplete$=\\'webauthn\\']');\n        if (eligibleInputs.length < 1) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        options.mediation = 'conditional';\n        publicKey.allowCredentials = [];\n    }\n    options.publicKey = publicKey;\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(options));\n    }\n    catch (err) {\n        throw identifyAuthenticationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = bufferToUTF8String(response.userHandle);\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            authenticatorData: bufferToBase64URLString(response.authenticatorData),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            signature: bufferToBase64URLString(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n\nfunction platformAuthenticatorIsAvailable() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@simplewebauthn/browser/dist/bundle/index.js\n"));

/***/ })

}]);