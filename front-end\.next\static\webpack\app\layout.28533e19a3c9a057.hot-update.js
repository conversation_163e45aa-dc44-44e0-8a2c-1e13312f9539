"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/shared/chat/ChatModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useChatSocket */ \"(app-pages-browser)/./src/hooks/useChatSocket.ts\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_escrow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// \"use client\";\n// import React, { useState, useEffect, useCallback, useRef } from \"react\";\n// import { ArrowLeft, MoreVertical, CheckCheck, Send, X } from \"lucide-react\";\n// import { usePrivy } from \"@privy-io/react-auth\";\n// import { useChatSocket } from \"@/hooks/useChatSocket\";\n// import axios from \"axios\";\n// import dayjs from 'dayjs';\n// import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';\n// import { useTranslation } from '@/hooks/useTranslation';\n// interface ChatModalProps {\n//   chatRoomId: string;\n//   buyerId: string | number;\n//   sellerId: string | number;\n//   onClose: () => void;\n// }\n// const API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\n// const ChatModal: React.FC<ChatModalProps> = ({ chatRoomId, buyerId, sellerId, onClose }) => {\n//   const { t, isRTL } = useTranslation();\n//   // All hooks at the top\n//   const { user } = usePrivy();\n//   const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();\n//   const { setOpenChatTradeId } = useAppContext();\n//   const [messages, setMessages] = useState<any[]>([]);\n//   const [input, setInput] = useState(\"\");\n//   const messagesEndRef = useRef<HTMLDivElement>(null);\n//   const [currentStatus, setCurrentStatus] = useState<string | null>(null);\n//   const [releaseDeadline, setReleaseDeadline] = useState<Date | null>(null);\n//   const [timeLeft, setTimeLeft] = useState<string | null>(null);\n//   const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);\n//   const notificationAudioRef = useRef<HTMLAudioElement | null>(null);\n//   // Get userBo.id from localStorage for consistent sender check\n//   const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//   const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//   const myUserId = userBo?.id;\n//   // Fetch receiver info (email) when receiverId changes\n//   useEffect(() => {\n//     async function fetchReceiver() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/users/${sellerId}`);\n//         // Try to extract email from possible locations\n//         let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);\n//         let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);\n//         let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);\n//         setReceiverInfo({ name, email, wallet });\n//       } catch (err) {\n//         setReceiverInfo(null);\n//       }\n//     }\n//     if (sellerId) fetchReceiver();\n//   }, [sellerId]);\n//   // Fetch message history on mount or when chatRoomId changes\n//   useEffect(() => {\n//     async function fetchMessages() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);\n//         // Replace messages state with fetched history (no merge)\n//         setMessages((res.data.data || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()));\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchMessages();\n//   }, [chatRoomId]);\n//   // Fetch release deadline on mount or when tradeId changes\n//   useEffect(() => {\n//     async function fetchDeadline() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/${chatRoomId}/release-deadline`);\n//         const deadline = new Date(res.data.releaseDeadline);\n//         setReleaseDeadline(deadline);\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchDeadline();\n//   }, [chatRoomId]);\n//   // Timer countdown (only updates local state)\n//   useEffect(() => {\n//     if (!releaseDeadline) return;\n//     const interval = setInterval(() => {\n//       const now = new Date();\n//       const diff = releaseDeadline.getTime() - now.getTime();\n//       if (diff <= 0) {\n//         setTimeLeft(t('chat.autoReleaseInProgress'));\n//         clearInterval(interval);\n//       } else {\n//         // Format as translation string\n//         const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n//         const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);\n//         const minutes = Math.floor((diff / (1000 * 60)) % 60);\n//         const seconds = Math.floor((diff / 1000) % 60);\n//         setTimeLeft(t('chat.timeFormat', { days, hours, minutes, seconds }));\n//       }\n//     }, 1000);\n//     return () => clearInterval(interval);\n//   }, [releaseDeadline, t]);\n//   // Handle incoming real-time messages (deduplicate by id, tempId, and content)\n//   const handleMessage = useCallback((msg: any) => {\n//     setMessages(prev => {\n//       // If message already exists (by id, tempId, or identical content+createdAt+senderId), skip\n//       if (prev.some(m =>\n//         (m.id && msg.id && m.id === msg.id) ||\n//         (m.tempId && msg.tempId && m.tempId === msg.tempId) ||\n//         (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)\n//       )) {\n//         return prev;\n//       }\n//       // Play notification sound if the current user is the receiver\n//       const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//       const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//       if (userBo && msg.receiverId === userBo.id && notificationAudioRef.current) {\n//         notificationAudioRef.current.currentTime = 0;\n//         notificationAudioRef.current.play();\n//       }\n//       return [...prev, msg].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n//     });\n//   }, []);\n//   const handleTradeStatus = useCallback((data: { status: string }) => setCurrentStatus(data.status), []);\n//   // Setup socket\n//   const { sendMessage, release, report, authenticated, joinedRoom, tradeStatus } = useChatSocket({\n//     chatRoomId, // Pass chatRoomId to useChatSocket\n//     userId: user?.id || user?.wallet?.address || \"unknown\",\n//     wallet: user?.wallet?.address || \"\",\n//     onMessage: handleMessage,\n//     onTradeStatus: handleTradeStatus,\n//   });\n//   // Scroll to bottom on new message\n//   useEffect(() => {\n//     messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n//   }, [messages]);\n//   // Send message handler (optimistic update)\n//   const handleSend = (e: React.FormEvent) => {\n//     e.preventDefault();\n//     if (!input.trim()) return;\n//     const userBoStr = localStorage.getItem(\"userBo\");\n//     const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//     if (!userBo?.id) {\n//       alert(t('chat.userIdNotFound'));\n//       return;\n//     }\n//     sendMessage({\n//       chatRoomId,\n//       senderId: userBo.id,\n//       receiverId: userBo.id === buyerId ? sellerId : buyerId,\n//       message: input,\n//     });\n//     setInput(\"\");\n//   };\n//   // Helper: format time\n//   const formatTime = (dateStr: string) => {\n//     const date = new Date(dateStr);\n//     return date.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\n//   };\n//   // Helper: format date for separator\n//   const formatDateSeparator = (date: Date) => {\n//     const today = dayjs().startOf('day');\n//     const msgDay = dayjs(date).startOf('day');\n//     if (msgDay.isSame(today)) return t('chat.today');\n//     if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n//     return msgDay.format('D MMM YYYY');\n//   };\n//   // Helper to get initials from email or name\n//   const getInitials = (email?: string, name?: string) => {\n//     if (name && name.trim()) return name.slice(0, 2).toUpperCase();\n//     if (email && email.trim()) return email.slice(0, 2).toUpperCase();\n//     return \"?\";\n//   };\n//   // Helper to format wallet address (first 4 + last 4 characters)\n//   const formatWalletAddress = (wallet?: string) => {\n//     if (!wallet || wallet.length < 8) return wallet;\n//     return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;\n//   };\n//   // Helper to get display name with priority: name > email > formatted wallet\n//   const getDisplayName = () => {\n//     if (receiverInfo?.name && receiverInfo.name.trim()) {\n//       return receiverInfo.name;\n//     }\n//     if (receiverInfo?.email && receiverInfo.email.trim()) {\n//       return receiverInfo.email;\n//     }\n//     if (receiverInfo?.wallet && receiverInfo.wallet.trim()) {\n//       return formatWalletAddress(receiverInfo.wallet);\n//     }\n//     return t('chat.user');\n//   };\n//   // Show status in the UI and disable buttons if released or reported\n//   const isActionDisabled = currentStatus === 'released' || currentStatus === 'reported';\n//   // useEffect to set openChatTradeId and clear unread chat messages for this chatRoomId\n//   useEffect(() => {\n//     setOpenChatTradeId(chatRoomId);\n//     removeUnreadChatMessagesForTrade(chatRoomId);\n//     return () => {\n//       setOpenChatTradeId(null);\n//     };\n//   }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade]);\n//   // Only after all hooks, do conditional returns\n//   if (!user?.wallet?.address) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectWalletToChat')}</p>\n//           {/* You can trigger Privy login here if needed */}\n//         </div>\n//       </div>\n//     );\n//   }\n//   if (!authenticated || !joinedRoom) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectingToChat')}</p>\n//         </div>\n//       </div>\n//     );\n//   }\n//   return (\n//     <div\n//       className={`fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300 ${isRTL ? 'rtl' : 'ltr'}`}\n//     >\n//       {/* Notification sound */}\n//       <audio ref={notificationAudioRef} src=\"/sounds/notification.mp3\" preload=\"auto\" />\n//         {/* Close button */}\n//         <button\n//           className=\"absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1\"\n//           onClick={onClose}\n//         >\n//           <X className=\"w-5 h-5 text-gray-700\" />\n//         </button>\n//         {/* Header */}\n//         {/* <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button\n//             className={isRTL ? 'ml-3' : 'mr-3'}\n//             onClick={onClose}\n//             style={isRTL ? { marginLeft: '12px', marginRight: 0 } : { marginRight: '12px', marginLeft: 0 }}\n//           >\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button> */}\n//           <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button className={isRTL ? 'ml-3' : 'mr-3'} onClick={onClose}>\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button>\n//           {/* <div\n//             className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}\n//             style={isRTL ? { direction: 'rtl', textAlign: 'right', alignItems: 'flex-end' } : { direction: 'ltr', textAlign: 'left', alignItems: 'flex-start' }}\n//           >\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div> */}\n//           <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`} style={isRTL ? { direction: 'rtl' } : {}}>\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//         {/* Messages Container */}\n//         <div className=\"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto\" style={{ maxHeight: 400 }}>\n//           {(() => {\n//             let lastDate: string | null = null;\n//             return messages.map((msg, idx) => {\n//               const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n//               const showDate = msgDate && msgDate !== lastDate;\n//               lastDate = msgDate;\n//               return (\n//                 <React.Fragment key={msg.id ? `id-${msg.id}` : msg.tempId ? `temp-${msg.tempId}` : `fallback-${msg.senderId}-${msg.createdAt}-${idx}` }>\n//                   {showDate && (\n//                     <div className=\"flex justify-center my-2\">\n//                       <span className=\"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\">{msgDate}</span>\n//                     </div>\n//                   )}\n//                   <div className={`flex flex-col ${msg.senderId === myUserId ? \"items-end\" : \"items-start\"}`}>\n//                     <div\n//                       className={\n//                         msg.senderId === myUserId\n//                           ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\"\n//                           : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\"\n//                       }\n//                     >\n//                       <p className=\"text-sm leading-relaxed whitespace-pre-line\">{msg.message || msg.text}</p>\n//                     </div>\n//                     <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"}`}>\n//                       <span className=\"text-xs text-gray-400\">\n//                         {msg.createdAt ? formatTime(msg.createdAt) : msg.time || \"\"}\n//                       </span>\n//                       {msg.senderId === myUserId && <CheckCheck className=\"w-4 h-4 text-green-500\" />}\n//                     </div>\n//                   </div>\n//                 </React.Fragment>\n//               );\n//             });\n//           })()}\n//           <div ref={messagesEndRef} />\n//         </div>\n//         {/* Bottom Section */}\n//         {/* Auto-release Info */}\n//         <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n//           <p className=\"text-xs text-gray-500 text-center leading-relaxed\">\n//             {timeLeft ? (\n//               <>{t('chat.autoReleaseIn', { time: timeLeft })}</>\n//             ) : (\n//               <>{t('chat.loadingAutoRelease')}</>\n//             )}<br />\n//             {t('chat.reportTrade')}\n//           </p>\n//         </div>\n//         {/* Action Buttons */}\n//         <div className=\"px-4 py-3 space-y-2\">\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={release}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.iGotTheItem')}\n//           </button>\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={report}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.reportTrade')}\n//           </button>\n//           {currentStatus && (\n//             <div className=\"text-center text-xs text-gray-500 mt-2\">\n//               {t('chat.tradeStatus')} <span className=\"font-semibold\">{currentStatus}</span>\n//             </div>\n//           )}\n//         </div>\n//         {/* Message Input */}\n//         <form className=\"px-4 py-3 bg-orange-50\" onSubmit={handleSend}>\n//           <div className=\"flex items-center gap-2\">\n//             <input\n//               type=\"text\"\n//               placeholder={t('chat.typeMessage')}\n//               className=\"flex-1 bg-orange-100 rounded-full px-4 py-2 text-sm outline-none border border-transparent focus:border-orange-300 placeholder:text-gray-500\"\n//               value={input}\n//               onChange={e => setInput(e.target.value)}\n//               disabled={!authenticated || !joinedRoom}\n//             />\n//             <button type=\"submit\" className=\"bg-orange-500 p-2 rounded-full hover:bg-orange-600 transition-colors\" disabled={!authenticated || !joinedRoom}>\n//               <Send className=\"w-5 h-5 text-white\" />\n//             </button>\n//           </div>\n//         </form>\n//     </div>\n//   );\n// };\n// export default ChatModal; \n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\nconst ChatModal = (param)=>{\n    let { chatRoomId, buyerId, sellerId, onClose, onRelease, onRefund, onReport, onAccept, onInitiateDispute, activeTrade } = param;\n    var _user_wallet, _user_wallet1, _receiverInfo_name, _receiverInfo_email;\n    _s();\n    const { t, isRTL } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H)();\n    const { solanaWallet, isConnected, getWalletAddress } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { removeUnreadChatMessagesForTrade } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages)();\n    const { setOpenChatTradeId } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [receiverInfo, setReceiverInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const notificationAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentTradeStatus, setCurrentTradeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status) || null);\n    const [isOperationInProgress, setIsOperationInProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Request notification permission when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            if ('Notification' in window && Notification.permission === 'default') {\n                Notification.requestPermission().then({\n                    \"ChatModal.useEffect\": (permission)=>{\n                        console.log('🔔 [ChatModal] Notification permission:', permission);\n                    }\n                }[\"ChatModal.useEffect\"]);\n            }\n        }\n    }[\"ChatModal.useEffect\"], []);\n    // Get user ID\n    const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n    const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n    // Determine user role - ensure type consistency\n    const buyerIdNum = typeof buyerId === 'string' ? parseInt(buyerId) : buyerId;\n    const sellerIdNum = typeof sellerId === 'string' ? parseInt(sellerId) : sellerId;\n    const myUserIdNum = typeof myUserId === 'string' ? parseInt(myUserId) : myUserId;\n    const isBuyer = myUserIdNum === buyerIdNum;\n    const isSeller = myUserIdNum === sellerIdNum;\n    // Use current trade status (which can be updated via Socket.IO) or fallback to activeTrade status\n    const tradeStatus = currentTradeStatus || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status);\n    const isEscrowed = tradeStatus === 'escrowed';\n    const isPendingAcceptance = tradeStatus === 'pending_acceptance';\n    const isEscrowReleased = tradeStatus === 'completed' || tradeStatus === 'released';\n    const isUserAuthorized = user && (isBuyer || isSeller);\n    // Trade action buttons should only show when escrow is active (not released)\n    const shouldShowTradeActions = (isEscrowed || isPendingAcceptance) && !isEscrowReleased;\n    // Seller can accept when escrow is pending acceptance\n    const canSellerAccept = isPendingAcceptance && isSeller && onAccept;\n    // Buyer can only release after seller has accepted\n    const canBuyerRelease = isEscrowed && !isPendingAcceptance && isBuyer;\n    // Debug logging\n    console.log('🔍 [ChatModal] User role debugging:', {\n        myUserId,\n        myUserIdNum,\n        buyerId,\n        buyerIdNum,\n        sellerId,\n        sellerIdNum,\n        isBuyer,\n        isSeller,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId\n        } : null,\n        tradeStatus,\n        isEscrowed,\n        isPendingAcceptance,\n        isEscrowReleased,\n        shouldShowTradeActions,\n        canSellerAccept,\n        canBuyerRelease,\n        isUserAuthorized\n    });\n    // Chat should be enabled if user is authorized (buyer or seller), regardless of escrow status\n    const canChat = isUserAuthorized && isConnected && solanaWallet;\n    // Enhanced dispute button logic\n    const canShowDisputeButton = (userRole)=>{\n        if (!activeTrade || !onInitiateDispute) return false;\n        // Use the enhanced canInitiateDispute function with dispute status\n        const disputeCheck = (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.canInitiateDispute)(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus);\n        // Debug logging for dispute button visibility\n        console.log(\"\\uD83D\\uDD0D [ChatModal] Dispute button check for \".concat(userRole, \":\"), {\n            tradeStatus: activeTrade.status,\n            disputeStatus: activeTrade.disputeStatus,\n            canDispute: disputeCheck.canDispute,\n            reason: disputeCheck.reason,\n            createdAt: activeTrade.createdAt\n        });\n        return disputeCheck.canDispute;\n    };\n    // Fetch receiver info\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchReceiver() {\n                try {\n                    const targetId = isBuyer ? sellerId : buyerId;\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/users/\").concat(targetId));\n                    let email = res.data.email || res.data.user && res.data.user.email || res.data.data && res.data.data.email;\n                    let name = res.data.name || res.data.user && res.data.user.name || res.data.data && res.data.data.name;\n                    let wallet = res.data.wallet || res.data.user && res.data.user.wallet || res.data.data && res.data.data.wallet;\n                    setReceiverInfo({\n                        name,\n                        email,\n                        wallet\n                    });\n                } catch (err) {\n                    setReceiverInfo(null);\n                }\n            }\n            fetchReceiver();\n        }\n    }[\"ChatModal.useEffect\"], [\n        buyerId,\n        sellerId,\n        isBuyer\n    ]);\n    // Fetch messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchMessages() {\n                try {\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/messages/chat-room/\").concat(chatRoomId));\n                    setMessages((res.data.data || []).sort({\n                        \"ChatModal.useEffect.fetchMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useEffect.fetchMessages\"]));\n                } catch (err) {\n                    console.error(\"Error fetching messages:\", err);\n                }\n            }\n            fetchMessages();\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId\n    ]);\n    // Handle incoming messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleMessage]\": (msg)=>{\n            console.log('📨 [ChatModal] Received message:', msg, 'myUserId:', myUserId);\n            setMessages({\n                \"ChatModal.useCallback[handleMessage]\": (prev)=>{\n                    // Check for duplicates\n                    const isDuplicate = prev.some({\n                        \"ChatModal.useCallback[handleMessage].isDuplicate\": (m)=>m.id && msg.id && m.id === msg.id || m.tempId && msg.tempId && m.tempId === msg.tempId || m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId\n                    }[\"ChatModal.useCallback[handleMessage].isDuplicate\"]);\n                    if (isDuplicate) {\n                        console.log('🔄 [ChatModal] Duplicate message, skipping');\n                        return prev;\n                    }\n                    // Play notification sound for received messages (not sent by me)\n                    if (msg.senderId !== myUserId && notificationAudioRef.current) {\n                        console.log('🔊 [ChatModal] Playing notification sound');\n                        try {\n                            notificationAudioRef.current.currentTime = 0;\n                            notificationAudioRef.current.play().catch({\n                                \"ChatModal.useCallback[handleMessage]\": (e)=>{\n                                    console.log('🔇 [ChatModal] Could not play notification sound (user interaction required):', e);\n                                    // Fallback: show browser notification if audio fails\n                                    if ('Notification' in window && Notification.permission === 'granted') {\n                                        new Notification('New Message', {\n                                            body: msg.message,\n                                            icon: '/images/funhi-logo.png'\n                                        });\n                                    }\n                                }\n                            }[\"ChatModal.useCallback[handleMessage]\"]);\n                        } catch (error) {\n                            console.log('🔇 [ChatModal] Audio play error:', error);\n                        }\n                    }\n                    const newMessages = [\n                        ...prev,\n                        msg\n                    ].sort({\n                        \"ChatModal.useCallback[handleMessage].newMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useCallback[handleMessage].newMessages\"]);\n                    console.log('✅ [ChatModal] Added new message, total messages:', newMessages.length);\n                    return newMessages;\n                }\n            }[\"ChatModal.useCallback[handleMessage]\"]);\n        }\n    }[\"ChatModal.useCallback[handleMessage]\"], [\n        myUserId\n    ]);\n    // Handle trade status updates\n    const handleTradeStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleTradeStatusUpdate]\": (data)=>{\n            console.log(\"[ChatModal] Trade status update received:\", data);\n            if (data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId)) {\n                setCurrentTradeStatus(data.status);\n                console.log(\"[ChatModal] Updated trade status to: \".concat(data.status));\n            }\n        }\n    }[\"ChatModal.useCallback[handleTradeStatusUpdate]\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId\n    ]);\n    // Get the correct wallet address\n    const walletAddress = getWalletAddress();\n    console.log('🔍 [ChatModal] Wallet info:', {\n        privyWallet: user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : _user_wallet.address,\n        useWalletAddress: walletAddress,\n        isConnected,\n        chatRoomId\n    });\n    // Setup socket\n    const { sendMessage, authenticated, joinedRoom } = (0,_hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket)({\n        chatRoomId,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || myUserId || \"unknown\",\n        wallet: walletAddress || \"\",\n        onMessage: handleMessage,\n        onTradeStatus: handleTradeStatusUpdate\n    });\n    // Scroll to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }[\"ChatModal.useEffect\"], [\n        messages\n    ]);\n    // Set chat as open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            setOpenChatTradeId(chatRoomId);\n            removeUnreadChatMessagesForTrade(chatRoomId);\n            return ({\n                \"ChatModal.useEffect\": ()=>setOpenChatTradeId(null)\n            })[\"ChatModal.useEffect\"];\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId,\n        setOpenChatTradeId,\n        removeUnreadChatMessagesForTrade\n    ]);\n    // Fetch latest trade status when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            const fetchLatestTradeStatus = {\n                \"ChatModal.useEffect.fetchLatestTradeStatus\": async ()=>{\n                    if (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) {\n                        try {\n                            console.log('🔄 [ChatModal] Fetching latest trade status for tradeId:', activeTrade.id);\n                            const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                            const tradeResponse = await getTradeDetails(Number(activeTrade.id));\n                            if (tradeResponse.status === 200 && tradeResponse.data.status !== currentTradeStatus) {\n                                console.log(\"\\uD83D\\uDD04 [ChatModal] Trade status updated: \".concat(currentTradeStatus, \" → \").concat(tradeResponse.data.status));\n                                setCurrentTradeStatus(tradeResponse.data.status);\n                            }\n                        } catch (error) {\n                            console.error('❌ [ChatModal] Failed to fetch latest trade status:', error);\n                        }\n                    }\n                }\n            }[\"ChatModal.useEffect.fetchLatestTradeStatus\"];\n            fetchLatestTradeStatus();\n        }\n    }[\"ChatModal.useEffect\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id\n    ]); // Only run when trade ID changes\n    // Send message\n    const handleSend = (e)=>{\n        e.preventDefault();\n        if (!input.trim() || !canChat) return;\n        const receiverId = isBuyer ? sellerId : buyerId;\n        console.log('🔍 [ChatModal] Sending message:', {\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim(),\n            canChat,\n            isEscrowed\n        });\n        sendMessage({\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim()\n        });\n        setInput(\"\");\n    };\n    // Enhanced action handlers that update status immediately and send system messages\n    const handleReleaseWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleReleaseWithStatusUpdate]\": async ()=>{\n            if (!onRelease) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow release...');\n                // Call the original release function\n                await onRelease();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('released');\n                console.log('✅ [ChatModal] Escrow released, status updated to released');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"\\uD83C\\uDF89 Escrow has been released! Trade completed successfully.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Release failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleReleaseWithStatusUpdate]\"], [\n        onRelease,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    const handleAcceptWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleAcceptWithStatusUpdate]\": async ()=>{\n            if (!onAccept) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow accept...');\n                // Call the original accept function\n                await onAccept();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('escrowed');\n                console.log('✅ [ChatModal] Escrow accepted, status updated to escrowed');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"✅ Escrow accepted! Buyer can now release funds when ready.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Accept failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleAcceptWithStatusUpdate]\"], [\n        onAccept,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    // Format time\n    const formatTime = (dateStr)=>{\n        const date = new Date(dateStr);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Format date separator\n    const formatDateSeparator = (date)=>{\n        const today = dayjs__WEBPACK_IMPORTED_MODULE_4___default()().startOf('day');\n        const msgDay = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).startOf('day');\n        if (msgDay.isSame(today)) return t('chat.today');\n        if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n        return msgDay.format('D MMM YYYY');\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.name) return receiverInfo.name;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) return receiverInfo.email;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) {\n            const wallet = receiverInfo.wallet;\n            return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n        }\n        return t('chat.user');\n    };\n    // Wallet formatting\n    const formatWalletAddress = (wallet)=>{\n        if (!wallet || wallet.length < 8) return wallet;\n        return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n    };\n    if (!(user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : _user_wallet1.address)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-700 text-center\",\n                        children: t('chat.connectWalletToChat')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 824,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!authenticated || !joinedRoom) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-gray-700 text-center\",\n                    children: t('chat.connectingToChat')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 842,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 841,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 840,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: notificationAudioRef,\n                src: \"/sounds/notification.mp3\",\n                preload: \"auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 850,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-2 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 ml-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_name = receiverInfo.name) === null || _receiverInfo_name === void 0 ? void 0 : _receiverInfo_name.slice(0, 2).toUpperCase()) || (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_email = receiverInfo.email) === null || _receiverInfo_email === void 0 ? void 0 : _receiverInfo_email.slice(0, 2).toUpperCase()) || \"?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute bottom-0 right-0 w-3 h-3 bg-emerald-500 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900 text-sm\",\n                                        children: getDisplayName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: receiverInfo.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 font-mono\",\n                                        children: formatWalletAddress(receiverInfo.wallet)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 860,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 853,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b \".concat(isEscrowReleased ? 'bg-green-50 border-green-100' : isEscrowed ? 'bg-orange-50 border-orange-100' : isPendingAcceptance ? 'bg-blue-50 border-blue-100' : 'bg-amber-50 border-amber-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isEscrowReleased ? 'bg-green-600' : isEscrowed ? 'bg-[#FF6600]' : isPendingAcceptance ? 'bg-blue-500' : 'bg-amber-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-slate-700\",\n                                    children: isEscrowReleased ? 'Trade Completed' : isEscrowed ? 'Escrow Active' : isPendingAcceptance ? 'Awaiting Seller Acceptance' : 'Pre-Purchase Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 896,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500\",\n                            children: isEscrowReleased ? 'Funds released successfully' : isEscrowed ? 'Funds secured on-chain' : isPendingAcceptance ? 'Seller needs to accept escrow' : 'Discussing before purchase'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 895,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 886,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto max-h-[400px]\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-orange-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: isEscrowed ? 'Escrow Chat Started' : 'Start the Conversation'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 max-w-xs\",\n                                children: isEscrowed ? 'Your funds are secured. Chat with the other party about the trade details.' : 'Discuss the details before making a purchase. Ask questions and clarify expectations.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 942,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 933,\n                        columnNumber: 11\n                    }, undefined) : messages.map((msg, idx)=>{\n                        const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n                        const showDate = idx === 0 || msgDate !== formatDateSeparator(new Date(messages[idx - 1].createdAt));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\",\n                                        children: msgDate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col \".concat(msg.senderId === myUserId ? \"items-end\" : \"items-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: msg.senderId === myUserId ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\" : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed whitespace-pre-line\",\n                                                children: msg.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 mt-1 \".concat(msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: msg.createdAt ? formatTime(msg.createdAt) : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                msg.senderId === myUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, msg.id || \"msg-\".concat(idx), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 931,\n                columnNumber: 7\n            }, undefined),\n            activeTrade && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Trade Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium capitalize \".concat((currentTradeStatus || activeTrade.status) === 'escrowed' ? 'bg-blue-100 text-blue-800' : (currentTradeStatus || activeTrade.status) === 'completed' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'released' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'pending_acceptance' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'),\n                                        children: currentTradeStatus || activeTrade.status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 989,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTrade.disputeStatus && activeTrade.disputeStatus !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Dispute:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1005,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).bgColor, \" \").concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).color),\n                                        children: (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 11\n                    }, undefined),\n                    shouldShowTradeActions && isConnected && solanaWallet && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 p-4 bg-slate-50 rounded-lg\",\n                        children: [\n                            isBuyer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canBuyerRelease && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onRelease,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            t('chat.releaseFunds')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-700 font-medium\",\n                                                        children: \"Waiting for seller to accept escrow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 text-center mt-1\",\n                                                children: \"You'll be able to release funds once the seller accepts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('buyer') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('buyer'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            isSeller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canSellerAccept && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onAccept,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Accept Escrow\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isEscrowed && !isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-700 font-medium\",\n                                                        children: \"Escrow accepted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 text-center mt-1\",\n                                                children: \"Waiting for buyer to release funds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1103,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('seller'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    !canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-800 text-center\",\n                                            children: (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.disputeStatus) && activeTrade.disputeStatus !== 'none' ? \"Dispute status: \".concat(activeTrade.disputeStatus) : 'Dispute option available within 2 days of escrow creation'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowReleased && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"Trade Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1146,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-700\",\n                                            children: \"Escrow has been released. This trade is now complete.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1145,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1142,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && isUserAuthorized && (!isConnected || !solanaWallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Wallet Connection Required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1163,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700\",\n                                            children: \"Please connect your Solana wallet to perform trade actions like releasing funds or initiating disputes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1157,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && (!user || !isBuyer && !isSeller) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-red-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1176,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Access Restricted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: \"Only the buyer and seller can perform trade actions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1175,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1174,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'open' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-800\",\n                            children: \"A dispute has been initiated. A moderator will review this case shortly.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'resolved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-800\",\n                            children: \"The dispute for this trade has been resolved by a moderator.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1200,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1199,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 986,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"px-4 py-3 bg-slate-50 border-t border-slate-200\",\n                onSubmit: handleSend,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: canChat ? t('chat.typeMessage') : 'Connect wallet to chat...',\n                                className: \"flex-1 bg-white rounded-lg px-4 py-3 text-sm outline-none border border-slate-300 focus:border-[#FF6600] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 placeholder:text-slate-500 transition-all duration-200\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                disabled: !canChat\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1211,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"p-3 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm \".concat(canChat && input.trim() ? 'bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2' : 'bg-slate-300 cursor-not-allowed opacity-50'),\n                                disabled: !input.trim() || !canChat,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1210,\n                        columnNumber: 9\n                    }, undefined),\n                    !canChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-gray-600 text-center\",\n                        children: !isUserAuthorized ? 'Only buyer and seller can chat' : !isConnected ? 'Please connect your wallet to chat' : 'Wallet connection required'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1232,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1209,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n        lineNumber: 849,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModal, \"DNr/tjJYbHPK+3Qa5/mVdoIMeas=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket\n    ];\n});\n_c = ChatModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatModal);\nvar _c;\n$RefreshReg$(_c, \"ChatModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"791df3365aec\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzkxZGYzMzY1YWVjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});