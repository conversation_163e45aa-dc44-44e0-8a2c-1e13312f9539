import React from 'react';

const TokenHeaderSkeleton = () => (
  <div className="w-full bg-gradient-to-r from-gray-50 to-gray-100 p-8 rounded-2xl animate-pulse mb-8">
    <div className="flex flex-col space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start space-y-4 lg:space-y-0">
        <div className="space-y-4">
          <div className="h-12 bg-gray-200 rounded-lg w-80"></div>
          <div className="flex space-x-4">
            <div className="h-8 bg-gray-200 rounded w-32"></div>
            <div className="h-8 bg-gray-200 rounded w-28"></div>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
          <div className="space-y-2">
            <div className="h-5 bg-gray-200 rounded w-24"></div>
            <div className="h-4 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-6 bg-gray-200 rounded"></div>
        ))}
      </div>
    </div>
  </div>
);

const TokenStatsSkeleton = () => (
  <div className="w-full bg-white border border-gray-200 rounded-2xl p-6 animate-pulse mb-8">
    <div className="flex items-center space-x-4 mb-6">
      <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
      <div className="space-y-2">
        <div className="h-5 bg-gray-200 rounded w-32"></div>
        <div className="h-4 bg-gray-200 rounded w-24"></div>
      </div>
    </div>
    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="space-y-2">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-5 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  </div>
);

const ExchangeFormSkeleton = () => (
  <div className="space-y-4 animate-pulse">
    <div className="h-40 bg-gray-200 rounded-2xl"></div>
    <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto"></div>
    <div className="h-40 bg-gray-200 rounded-2xl"></div>
    <div className="h-12 bg-gray-200 rounded-lg"></div>
  </div>
);

export const LoadingSkeleton = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <div className="w-32 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>

      <TokenHeaderSkeleton />

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-12">
        <div className="xl:col-span-2 space-y-8">
          <TokenStatsSkeleton />
          <div className="h-96 bg-gray-200 rounded-2xl animate-pulse"></div>
        </div>

        <div className="xl:col-span-1">
          <div className="sticky top-8">
            <ExchangeFormSkeleton />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="h-96 bg-gray-200 rounded-2xl animate-pulse"></div>
        <div className="h-96 bg-gray-200 rounded-2xl animate-pulse"></div>
      </div>
    </div>
  </div>
); 