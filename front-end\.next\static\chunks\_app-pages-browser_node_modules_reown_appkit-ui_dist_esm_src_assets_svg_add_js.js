"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_add_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addSvg: () => (/* binding */ addSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst addSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"14\"\n  height=\"14\"\n  viewBox=\"0 0 14 14\"\n  fill=\"none\"\n  xmlns=\"http://www.w3.org/2000/svg\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M7.0023 0.875C7.48571 0.875 7.8776 1.26675 7.8776 1.75V6.125H12.2541C12.7375 6.125 13.1294 6.51675 13.1294 7C13.1294 7.48325 12.7375 7.875 12.2541 7.875H7.8776V12.25C7.8776 12.7332 7.48571 13.125 7.0023 13.125C6.51889 13.125 6.12701 12.7332 6.12701 12.25V7.875H1.75054C1.26713 7.875 0.875244 7.48325 0.875244 7C0.875244 6.51675 1.26713 6.125 1.75054 6.125H6.12701V1.75C6.12701 1.26675 6.51889 0.875 7.0023 0.875Z\"\n    fill=\"#667dff\"\n  /></svg\n>`;\n//# sourceMappingURL=add.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2FkZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixlQUFlLHdDQUFHO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXG5vZGVfbW9kdWxlc1xcQHJlb3duXFxhcHBraXQtdWlcXGRpc3RcXGVzbVxcc3JjXFxhc3NldHNcXHN2Z1xcYWRkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgYWRkU3ZnID0gc3ZnIGA8c3ZnXG4gIHdpZHRoPVwiMTRcIlxuICBoZWlnaHQ9XCIxNFwiXG4gIHZpZXdCb3g9XCIwIDAgMTQgMTRcIlxuICBmaWxsPVwibm9uZVwiXG4gIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuPlxuICA8cGF0aFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgIGZpbGwtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGNsaXAtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGQ9XCJNNy4wMDIzIDAuODc1QzcuNDg1NzEgMC44NzUgNy44Nzc2IDEuMjY2NzUgNy44Nzc2IDEuNzVWNi4xMjVIMTIuMjU0MUMxMi43Mzc1IDYuMTI1IDEzLjEyOTQgNi41MTY3NSAxMy4xMjk0IDdDMTMuMTI5NCA3LjQ4MzI1IDEyLjczNzUgNy44NzUgMTIuMjU0MSA3Ljg3NUg3Ljg3NzZWMTIuMjVDNy44Nzc2IDEyLjczMzIgNy40ODU3MSAxMy4xMjUgNy4wMDIzIDEzLjEyNUM2LjUxODg5IDEzLjEyNSA2LjEyNzAxIDEyLjczMzIgNi4xMjcwMSAxMi4yNVY3Ljg3NUgxLjc1MDU0QzEuMjY3MTMgNy44NzUgMC44NzUyNDQgNy40ODMyNSAwLjg3NTI0NCA3QzAuODc1MjQ0IDYuNTE2NzUgMS4yNjcxMyA2LjEyNSAxLjc1MDU0IDYuMTI1SDYuMTI3MDFWMS43NUM2LjEyNzAxIDEuMjY2NzUgNi41MTg4OSAwLjg3NSA3LjAwMjMgMC44NzVaXCJcbiAgICBmaWxsPVwiIzY2N2RmZlwiXG4gIC8+PC9zdmdcbj5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js\n"));

/***/ })

}]);