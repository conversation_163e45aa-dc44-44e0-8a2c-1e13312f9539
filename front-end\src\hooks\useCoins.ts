import { addDays, differenceInSeconds } from "date-fns";
import { useState, useEffect } from "react";

import { fetchTokens } from "../axios/requests";
import { formatSecondsToHMS } from "../utils/helpers";
import { API_CONFIG } from "@/config/environment";

// Define types for our coin data
export interface Coin {
  updatedAt: string | undefined;
  id: string;
  name: string;
  imageUrl: string;
  isLive: boolean;
  price: string;
  username: string;
  handle: string;
  soldCount: number;
  remainingCount: number;
  timeLeft?: string;
  category?: string;
  isVerified?: boolean;
  isPurshases:boolean;
}

interface UseCoinOptions {
  category?: string;
  search?: string;
  sortBy?: "marketCap" | "price" | "newest";
  filterVerified?: boolean | null; // true = verified only, false = unverified only, null = all
}

/**
 * Hook for fetching and filtering coin data
 *
 * This hook handles:
 * - Data fetching from API (mock for now)
 * - Filtering by category
 * - Searching by name
 * - Sorting by different criteria
 * - Filter by verification status
 */
export function useCoins({
  category = "All",
  search = "",
  sortBy = "marketCap",
  filterVerified = null,
}: UseCoinOptions = {}) {
  const [coins, setCoins] = useState<Coin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Simulate API call with setTimeout
    const fetchCoins = async () => {
      try {
        setLoading(true);

        // if connect with server
        if (API_CONFIG.BASE_URL != undefined) {
          const result = await fetchTokens({
            search: search,
            sort: sortBy,
            order: "desc",
            page: 0,
            pageSize: 10,
          });
          // Map API data to Coin[] format
          const apiData: Coin[] = result.data.tokens.map(
            (token: any) => {
              const targetTime = token.time
                ? addDays(new Date(token.time), 1)
                : null;
              const timeLeftInSeconds = targetTime
                ? Math.max(differenceInSeconds(targetTime, new Date()), 0)
                : 0;
              return {
                id: `${token.tokenId}`,
                name: token.name,
                imageUrl: token.image || "/images/placeholder.png",
                isLive: true,
                isVerified: token.isVerified,
                price: `${token.price.toFixed(2)} USD`,
                username: token.user.username,
                handle: `@ ${token.ticker.toLowerCase()}`,
                soldCount: Math.floor(Math.random() * 100), // Placeholder until real data
                remainingCount: Math.floor(Math.random() * 10), // Placeholder until real data
                timeLeft: token.time
                  ? formatSecondsToHMS(timeLeftInSeconds)
                  : "--",
                category: token.category || "Uncategorized",
                updatedAt:"",
                isPurshases:false
                
              };
            }
          );

          setCoins(apiData);
        } else {
          // In a real app, this would be an API call
          // For now, we'll simulate a delay and return mock data
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Mock data generation - in a real app this would come from an API
          const mockData: Coin[] = Array.from({ length: 20 }, (_, i) => ({
            id: `coin-${i + 1}`,
            name: `Coin ${i + 1}`,
            imageUrl: "/images/placeholder.png",
            isLive: i % 3 === 0,
            isVerified: i % 2 === 0, // Alternate between verified and unverified
            price: `${(Math.random() * 10).toFixed(2)} USD`,
            username: `User ${i + 1}`,
            handle: `@user${i + 1}`,
            soldCount: Math.floor(Math.random() * 100),
            remainingCount: Math.floor(Math.random() * 10),
            timeLeft: `${Math.floor(Math.random() * 5)}h : ${Math.floor(
              Math.random() * 60
            )}m`,
            category: ["Art", "Music", "Photography", "Videography", "Utility"][
              i % 5
            ],
            updatedAt:"",
            isPurshases:false
          }));
           setCoins(mockData);
        }

        
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error("Unknown error occurred")
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCoins();
  }, [category, search, sortBy, filterVerified]); // Add dependencies that should trigger a refetch

  // Apply filters to the data (in a real app, these might be API parameters)
  const filteredCoins = coins.filter((coin) => {
    // Filter by category
    if (category !== "All" && coin.category !== category) {
      return false;
    }

    // Filter by search
    if (search && !coin.name.toLowerCase().includes(search.toLowerCase())) {
      return false;
    }
    // Filter by verification status
    if (filterVerified == true && !coin.isVerified) {
      return false;
    }
    if (filterVerified == false && coin.isVerified) {
      return false;
    }

    return true;
  });

  // Sort the data
  const sortedCoins = [...filteredCoins].sort((a, b) => {
    if (sortBy === "price") {
      return parseFloat(b.price) - parseFloat(a.price);
    }
    if (sortBy === "newest") {
      return parseInt(b.id.split("-")[1]) - parseInt(a.id.split("-")[1]);
    }
    // Default to marketCap (using soldCount as a proxy for now)
    return b.soldCount - a.soldCount;
  });

  return {
    coins: sortedCoins,
    loading,
    error,
    totalCount: coins.length,
    filteredCount: sortedCoins.length,
  };
}
