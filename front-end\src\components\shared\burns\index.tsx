"use client";

import React, { useEffect, useState } from 'react';

import { addBurnLogs } from '@/axios/requests';
import { fetchTokenOptions, transferSPLTokenWithSigner } from '@/utils/helpers';

import { useWallet } from '@/hooks/useWallet';
import { useTranslation } from '../../../hooks/useTranslation';

import { WALLET_CONFIG } from '@/config/environment';
import { showErrorToast } from '@/utils/errorHandling';
import { showSuccessToast } from '@/utils/errorHandling';
import dynamic from 'next/dynamic';

const CreateAirdropForm = dynamic(() => import('../avelable-balance').then(mod => ({ default: mod.CreateAirdropForm })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const TextBox = dynamic(() => import('../textBox').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const Burns: React.FC<{ myBurn: any[], myTokens:any[] }> = ({ myTokens }) => {
 
  const [availableBalance, setAvailableBalance] = useState(0);
  const { wallets } = useWallet();
  const { t } = useTranslation();

  const handleAirdropSubmit = async (formData: any) => {
    setAvailableBalance(prev => prev - parseInt(formData.totalTokens));

    const recipientPubkeyHere = WALLET_CONFIG.BURN_ADDRESS || "";

    const privyWallet = wallets.find((w) => w.type === "solana");

    if (!privyWallet || !privyWallet.signTransaction) {
      showErrorToast(t("burns.wrongWallet"));
      throw new Error(
        t("burns.wrongWallet")
      );
    }

    const result = await transferSPLTokenWithSigner(
      privyWallet,
      recipientPubkeyHere,
      myTokens[0]?.tokenAddress,
      formData.totalTokens,
      6
    );
    if (result.success) {
      console.log("✅ Transfer successful:", result.signature);
      showSuccessToast(`${t('burns.burnSuccess')} ${formData.totalTokens} tokens!`);

      const dataIs = {
        signature: result.signature,
        amount: formData.totalTokens,
        tokenId:myTokens[0].tokenId,
        userId:myTokens[0].userId,
        message: `Burn ${formData.totalTokens}${myTokens[0].name}`
      };

      await addBurnLogs(dataIs);

    }
  
  };

  const columns = [
    { header: t('burns.colLink'), key: 'link', width: 'flex-1' },
    { header: t('burns.colTotalTokens'), key: 'totalTokens', width: 'flex-1' },
    { header: t('burns.colRemaining'), key: 'remaining', width: 'w-24' }
  ];

  const data = [
    {
      id: '1',
      link: 'Funhi.com/token-8829-ANS<br/>7 days ago',
      totalTokens: '10,000',
      remaining: '300'
    },
    {
      id: '2',
      link: 'Funhi.com/token-8829-ANS<br/>7 days ago',
      totalTokens: '10,000',
      remaining: '200'
    },
    {
      id: '3',
      link: 'Funhi.com/token-8829-ANS<br/>7 days ago',
      totalTokens: '10,000',
      remaining: '100'
    }
  ];

  useEffect(() => {
    const checkAndGetBalance = async () => {

      const wallet = wallets.find((w) => w.type === "solana");
      if (!wallet?.address) return;
      const updatedTokens = await fetchTokenOptions(wallet.address);
      const tokenToCheck = myTokens[0]?.tokenAddress;
  
      if (!tokenToCheck) return;
  
      const match = updatedTokens.find(token => token.tokenAddress === tokenToCheck);
  
      if (match) {
        setAvailableBalance(match.balance);
        console.log(`Balance for token ${match.symbol}:`, match.balance);
        // You can now use match.balance as needed
      } else {
        console.log("Token not found in updated list.");
      }
    };
  
    checkAndGetBalance();
  }, [myTokens]);

  return (
    <div className="flex flex-col mt-18">
      <div className="flex flex-col lg:flex-row justify-between gap-8 mb-12">
        <div className='lg:flex-1 lg:block lg:justify-start'>
          <CreateAirdropForm
            title={t('burns.formTitle')}
            availableBalance={availableBalance}
            buttonText={t('burns.formButton')}
            onSubmit={handleAirdropSubmit}
            totalTokensPlaceholder={t('burns.formPlaceholder')}
            componentType="burn"
          />
        </div>
        <div className='flex-1 flex justify-center lg:justify-end'>
          <TextBox
            title={t('burns.aboutTitle')}
            description={t('burns.aboutDescription')}
          />
        </div>
      </div>
      {/* <div className="">
        <Table
          title="Burns"
          columns={columns}
          data={data}
          currentPage={1}
          totalPages={5}
          onPageChange={(page: number) => console.log(`Navigate to page ${page}`)}
        />
      </div> */}
    </div>
  );
};

export default Burns;
