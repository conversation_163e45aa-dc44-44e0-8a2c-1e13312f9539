import { MetadataRoute } from 'next';
import { SEO_CONFIG } from '@/constants/seo';

// Robots configuration for better maintainability
const SITE_URL = SEO_CONFIG.SITE_URL;

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/', '/admin/', '/private/'],
      },
    ],
    sitemap: `${SITE_URL}/sitemap.xml`,
  };
} 