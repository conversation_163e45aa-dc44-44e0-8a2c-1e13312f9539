import React, { useCallback } from "react";

interface FileUploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  fileInputId?: string;
}

interface UseFileUploadOptions<T> {
  formData: T;
  setFormData: React.Dispatch<React.SetStateAction<T>>;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  fieldName: keyof T;
  errorFieldName?: string;
  options?: FileUploadOptions;
}

import { UPLOAD_CONFIG } from "@/config/environment";

export const useFileUpload = <T extends Record<string, any>>({
  formData,
  setFormData,
  errors,
  setErrors,
  fieldName,
  errorFieldName,
  options = {},
}: UseFileUploadOptions<T>) => {
  const {
    maxSize = UPLOAD_CONFIG.MAX_FILE_SIZE,
    allowedTypes = UPLOAD_CONFIG.ALLOWED_FILE_TYPES,
    fileInputId = "file-upload",
  } = options;

  const errorKey = errorFieldName || String(fieldName);

  const validateFile = useCallback(
    (file: File): string => {
      if (file.size > maxSize) {
        return `File must be less than ${Math.round(
          maxSize / (1024 * 1024)
        )}MB`;
      }

      if (!allowedTypes.includes(file.type)) {
        return `Only ${allowedTypes
          .map((type) => type.split("/")[1].toUpperCase())
          .join(", ")} files are allowed`;
      }

      return "";
    },
    [maxSize, allowedTypes]
  );

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];

        // Update form data
        setFormData((prev) => ({ ...prev, [fieldName]: file }));

        // Validate file
        const error = validateFile(file);
        setErrors((prev) => ({ ...prev, [errorKey]: error }));
      }
    },
    [fieldName, errorKey]
  );

  const handleDeleteFile = useCallback(() => {
    // Clear form data
    setFormData((prev) => ({ ...prev, [fieldName]: null }));

    // Clear error
    setErrors((prev) => ({ ...prev, [errorKey]: "" }));

    // Reset file input
    const fileInput = document.getElementById(fileInputId) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  }, [fieldName, errorKey, fileInputId]);

  const setFile = useCallback(
    (file: File | null) => {
      setFormData((prev) => ({ ...prev, [fieldName]: file }));

      if (file) {
        const error = validateFile(file);
        setErrors((prev) => ({ ...prev, [errorKey]: error }));
      } else {
        setErrors((prev) => ({ ...prev, [errorKey]: "" }));
      }
    },
    [fieldName, errorKey]
  );

  return {
    handleFileChange,
    handleDeleteFile,
    setFile,
    validateFile,
  };
};
