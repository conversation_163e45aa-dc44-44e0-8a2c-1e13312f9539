'use client';
import { motion } from 'framer-motion';
import { ChevronDown, SortAsc, Grid3X3 } from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import { fadeInUp, staggerContainer } from '@/lib/animations';
import { useTranslation } from '../../../hooks/useTranslation';

import dynamic from 'next/dynamic';

const PerksCard = dynamic(() => import('../perks-card').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});



interface AvailablePerksProps {
  perks?: any;
  sortOptions?: string[];
  activeSort?: string;
  onSortChange?: (option: string) => void;
  showHeader?: boolean;
  maxItems?: number;
}

const AvailablePerks: React.FC<AvailablePerksProps> = ({
  perks = [
    {
      id: 'perk-1',
      name: 'Premium Water Bottle',
      imageUrl: '/images/placeholder.png',
      price: '2.33 USD',
      timeLeft: '2h : 4m : 32s',
      username: 'Zennie',
      handle: '@32zennieq',
      isLive: true,
      soldCount: 39,
      remainingCount: 12,
      isVerified: true,
    },
    {
      id: 'perk-2',
      name: 'Exclusive T-Shirt',
      imageUrl: '/images/placeholder.png',
      price: '15.99 USD',
      timeLeft: '1h : 30m : 15s',
      username: 'Creator',
      handle: '@creator123',
      isLive: true,
      soldCount: 25,
      remainingCount: 8,
      isVerified: false,
    },
    {
      id: 'perk-3',
      name: 'Digital Art NFT',
      imageUrl: '/images/placeholder.png',
      price: '50.00 USD',
      timeLeft: '5h : 20m : 45s',
      username: 'Artist',
      handle: '@digitalart',
      isLive: true,
      soldCount: 15,
      remainingCount: 5,
      isVerified: true,
    },
  ],
  sortOptions = [
    'sortBestSelling',
    'sortNewest',
    'sortLowToHigh',
    'sortHighToLow',
  ],
  activeSort = 'sortBestSelling',
  onSortChange = () => {},
  showHeader = true,
  maxItems,
}) => {
  const { t } = useTranslation();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [currentSort, setCurrentSort] = useState(activeSort);

  const displayPerks = maxItems ? perks.slice(0, maxItems) : perks;

  const handleSortChange = (newSort: string) => {
    setCurrentSort(newSort);
    setIsDropdownOpen(false);
    if (onSortChange) {
      onSortChange(newSort);
    }
  };

  if (!perks || perks.length === 0) {
    return (
      <motion.div className="w-full my-8" {...fadeInUp}>
        {showHeader && (
          <div className="w-full flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 px-4">
            <h2 className="font-['IBM_Plex_Sans'] font-semibold text-[24px] sm:text-[28px] md:text-[32px] leading-[100%] text-[#262626] mb-3 sm:mb-0">
              {t('availablePerks.title')}
            </h2>
          </div>
        )}
        <div className="border-2 border-[#F58A38] rounded-xl bg-[#FFF7F1] w-full overflow-hidden">
          <div className="p-12 text-center bg-[#FFF7F1]">
            <Grid3X3 size={48} className="text-[#F58A38]/40 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              {t('availablePerks.noPerks')}
            </h3>
            <p className="text-gray-500">
              {t('availablePerks.noPerksDescription')}
            </p>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="w-full my-8"
      variants={staggerContainer}
      initial="initial"
      animate="animate"
    >
      {showHeader && (
        <motion.div
          className="w-full flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 px-4"
          variants={{
            initial: { opacity: 0, y: -20 },
            animate: { opacity: 1, y: 0 },
          }}
        >
          <h2 className="font-['IBM_Plex_Sans'] font-semibold text-[24px] sm:text-[28px] md:text-[32px] leading-[100%] text-[#262626] mb-3 sm:mb-0">
            {t('availablePerks.title')}
          </h2>

          {/* Sort Dropdown */}
          <div className="relative">
            <motion.button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center gap-2 bg-white border-2 border-[#F58A38] rounded-lg px-4 py-2 font-['IBM_Plex_Sans'] font-semibold text-[16px] sm:text-[18px] md:text-[20px] leading-[22px] text-[#161616] hover:bg-[#FFF7F1] transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <SortAsc size={18} className="text-[#F58A38]" />
              <span>{t('availablePerks.sortBy', { sort: t(`availablePerks.${currentSort}`) })}</span>
              <motion.div
                animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown size={18} className="text-[#F58A38]" />
              </motion.div>
            </motion.button>

            {isDropdownOpen && (
              <motion.div
                className="absolute top-full right-0 mt-2 w-64 bg-white border-2 border-[#F58A38] rounded-lg shadow-lg z-50 overflow-hidden"
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                {sortOptions.map((option, index) => (
                  <motion.button
                    key={option}
                    onClick={() => handleSortChange(option)}
                    className={`w-full text-left px-4 py-3 font-['IBM_Plex_Sans'] font-medium text-[16px] transition-colors ${
                      currentSort === option
                        ? 'bg-[#F58A38] text-white'
                        : 'text-[#161616] hover:bg-[#FFF7F1]'
                    }`}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    {t(`availablePerks.${option}`)}
                  </motion.button>
                ))}
              </motion.div>
            )}
          </div>
        </motion.div>
      )}

      <motion.div
        className="border-2 border-[#F58A38] rounded-xl bg-[#FFF7F1] w-full overflow-hidden shadow-sm"
        variants={{
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
        }}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-6 bg-[#FFF7F1]">
          {displayPerks.map((perk: any, index: number) => (
            <motion.div
              key={perk.perkId || perk.id}
              variants={{
                initial: { opacity: 0, y: 20 },
                animate: {
                  opacity: 1,
                  y: 0,
                  transition: { delay: index * 0.1 },
                },
              }}
            >
              <Link href={`/perks-shop/${perk.perkId || perk.id}`}>
                <PerksCard
                  id={perk.perkId || perk.id}
                  name={perk.name}
                  price={
                    typeof perk.price === 'string'
                      ? perk.price
                      : `${perk.price} USD`
                  }
                  timeLeft={perk.createdAt || perk.timeLeft}
                  username={perk.user?.username || perk.username || 'User'}
                  handle={perk.user?.handle || perk.handle || '@user'}
                  isLive={perk.isLive !== undefined ? perk.isLive : true}
                  soldCount={perk.soldCount || 0}
                  remainingCount={perk.remainingCount || 0}
                  imageUrl={
                    perk.image || perk.imageUrl || '/images/placeholder.png'
                  }
                  isVerified={perk.isVerified || false}
                  compact={true} // Use compact version for this section
                  onBuyClick={() => {
                    console.log(`Buy perk: ${perk.name}`);
                  }}
                />
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Show more link if maxItems is set and there are more items */}
        {maxItems && perks.length > maxItems && (
          <motion.div
            className="p-6 pt-0 bg-[#FFF7F1]"
            variants={{
              initial: { opacity: 0 },
              animate: { opacity: 1 },
            }}
          >
            <Link href="/perks-shop" className="block w-full">
              <motion.button
                className="w-full bg-[#F58A38] hover:bg-[#FF6600] text-white font-['IBM_Plex_Sans'] font-semibold text-lg py-3 rounded-lg transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {t('availablePerks.viewAll', { count: perks.length - maxItems })}
              </motion.button>
            </Link>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
};

export default AvailablePerks;
