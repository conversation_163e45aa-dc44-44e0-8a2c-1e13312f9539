"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/big.js";
exports.ids = ["vendor-chunks/big.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/big.js/big.mjs":
/*!*************************************!*\
  !*** ./node_modules/big.js/big.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Big: () => (/* binding */ Big),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 Michael Mclaughlin\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\r\n\r\n\r\n/************************************** EDITABLE DEFAULTS *****************************************/\r\n\r\n\r\n  // The default values below must be integers within the stated ranges.\r\n\r\n  /*\r\n   * The maximum number of decimal places (DP) of the results of operations involving division:\r\n   * div and sqrt, and pow with negative exponents.\r\n   */\r\nvar DP = 20,          // 0 to MAX_DP\r\n\r\n  /*\r\n   * The rounding mode (RM) used when rounding to the above decimal places.\r\n   *\r\n   *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n   *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n   *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n   *  3  Away from zero.                                  (ROUND_UP)\r\n   */\r\n  RM = 1,             // 0, 1, 2 or 3\r\n\r\n  // The maximum value of DP and Big.DP.\r\n  MAX_DP = 1E6,       // 0 to 1000000\r\n\r\n  // The maximum magnitude of the exponent argument to the pow method.\r\n  MAX_POWER = 1E6,    // 1 to 1000000\r\n\r\n  /*\r\n   * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n   * (JavaScript numbers: -7)\r\n   * -1000000 is the minimum recommended exponent value of a Big.\r\n   */\r\n  NE = -7,            // 0 to -1000000\r\n\r\n  /*\r\n   * The positive exponent (PE) at and above which toString returns exponential notation.\r\n   * (JavaScript numbers: 21)\r\n   * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n   */\r\n  PE = 21,            // 0 to 1000000\r\n\r\n  /*\r\n   * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n   * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n   * primitive number without a loss of precision.\r\n   */\r\n  STRICT = false,     // true or false\r\n\r\n\r\n/**************************************************************************************************/\r\n\r\n\r\n  // Error messages.\r\n  NAME = '[big.js] ',\r\n  INVALID = NAME + 'Invalid ',\r\n  INVALID_DP = INVALID + 'decimal places',\r\n  INVALID_RM = INVALID + 'rounding mode',\r\n  DIV_BY_ZERO = NAME + 'Division by zero',\r\n\r\n  // The shared prototype object.\r\n  P = {},\r\n  UNDEFINED = void 0,\r\n  NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\r\n\r\n\r\n/*\r\n * Create and return a Big constructor.\r\n */\r\nfunction _Big_() {\r\n\r\n  /*\r\n   * The Big constructor and exported function.\r\n   * Create and return a new instance of a Big number object.\r\n   *\r\n   * n {number|string|Big} A numeric value.\r\n   */\r\n  function Big(n) {\r\n    var x = this;\r\n\r\n    // Enable constructor usage without new.\r\n    if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\r\n\r\n    // Duplicate.\r\n    if (n instanceof Big) {\r\n      x.s = n.s;\r\n      x.e = n.e;\r\n      x.c = n.c.slice();\r\n    } else {\r\n      if (typeof n !== 'string') {\r\n        if (Big.strict === true && typeof n !== 'bigint') {\r\n          throw TypeError(INVALID + 'value');\r\n        }\r\n\r\n        // Minus zero?\r\n        n = n === 0 && 1 / n < 0 ? '-0' : String(n);\r\n      }\r\n\r\n      parse(x, n);\r\n    }\r\n\r\n    // Retain a reference to this Big constructor.\r\n    // Shadow Big.prototype.constructor which points to Object.\r\n    x.constructor = Big;\r\n  }\r\n\r\n  Big.prototype = P;\r\n  Big.DP = DP;\r\n  Big.RM = RM;\r\n  Big.NE = NE;\r\n  Big.PE = PE;\r\n  Big.strict = STRICT;\r\n  Big.roundDown = 0;\r\n  Big.roundHalfUp = 1;\r\n  Big.roundHalfEven = 2;\r\n  Big.roundUp = 3;\r\n\r\n  return Big;\r\n}\r\n\r\n\r\n/*\r\n * Parse the number or string value passed to a Big constructor.\r\n *\r\n * x {Big} A Big number instance.\r\n * n {number|string} A numeric value.\r\n */\r\nfunction parse(x, n) {\r\n  var e, i, nl;\r\n\r\n  if (!NUMERIC.test(n)) {\r\n    throw Error(INVALID + 'number');\r\n  }\r\n\r\n  // Determine sign.\r\n  x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\r\n\r\n  // Decimal point?\r\n  if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = n.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +n.slice(i + 1);\r\n    n = n.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = n.length;\r\n  }\r\n\r\n  nl = n.length;\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\r\n\r\n  if (i == nl) {\r\n\r\n    // Zero.\r\n    x.c = [x.e = 0];\r\n  } else {\r\n\r\n    // Determine trailing zeros.\r\n    for (; nl > 0 && n.charAt(--nl) == '0';);\r\n    x.e = e - i - 1;\r\n    x.c = [];\r\n\r\n    // Convert string to array of digits without leading/trailing zeros.\r\n    for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n *\r\n * x {Big} The Big to round.\r\n * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n * [more] {boolean} Whether the result of division was truncated.\r\n */\r\nfunction round(x, sd, rm, more) {\r\n  var xc = x.c;\r\n\r\n  if (rm === UNDEFINED) rm = x.constructor.RM;\r\n  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\r\n    throw Error(INVALID_RM);\r\n  }\r\n\r\n  if (sd < 1) {\r\n    more =\r\n      rm === 3 && (more || !!xc[0]) || sd === 0 && (\r\n      rm === 1 && xc[0] >= 5 ||\r\n      rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED))\r\n    );\r\n\r\n    xc.length = 1;\r\n\r\n    if (more) {\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      x.e = x.e - sd + 1;\r\n      xc[0] = 1;\r\n    } else {\r\n\r\n      // Zero.\r\n      xc[0] = x.e = 0;\r\n    }\r\n  } else if (sd < xc.length) {\r\n\r\n    // xc[sd] is the digit after the digit that may be rounded up.\r\n    more =\r\n      rm === 1 && xc[sd] >= 5 ||\r\n      rm === 2 && (xc[sd] > 5 || xc[sd] === 5 &&\r\n        (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) ||\r\n      rm === 3 && (more || !!xc[0]);\r\n\r\n    // Remove any digits after the required precision.\r\n    xc.length = sd;\r\n\r\n    // Round up?\r\n    if (more) {\r\n\r\n      // Rounding up may mean the previous digit has to be rounded up.\r\n      for (; ++xc[--sd] > 9;) {\r\n        xc[sd] = 0;\r\n        if (sd === 0) {\r\n          ++x.e;\r\n          xc.unshift(1);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (sd = xc.length; !xc[--sd];) xc.pop();\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a string representing the value of Big x in normal or exponential notation.\r\n * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n */\r\nfunction stringify(x, doExponential, isNonzero) {\r\n  var e = x.e,\r\n    s = x.c.join(''),\r\n    n = s.length;\r\n\r\n  // Exponential notation?\r\n  if (doExponential) {\r\n    s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\r\n\r\n  // Normal notation.\r\n  } else if (e < 0) {\r\n    for (; ++e;) s = '0' + s;\r\n    s = '0.' + s;\r\n  } else if (e > 0) {\r\n    if (++e > n) {\r\n      for (e -= n; e--;) s += '0';\r\n    } else if (e < n) {\r\n      s = s.slice(0, e) + '.' + s.slice(e);\r\n    }\r\n  } else if (n > 1) {\r\n    s = s.charAt(0) + '.' + s.slice(1);\r\n  }\r\n\r\n  return x.s < 0 && isNonzero ? '-' + s : s;\r\n}\r\n\r\n\r\n// Prototype/instance methods\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the absolute value of this Big.\r\n */\r\nP.abs = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return 1 if the value of this Big is greater than the value of Big y,\r\n *       -1 if the value of this Big is less than the value of Big y, or\r\n *        0 if they have the same value.\r\n */\r\nP.cmp = function (y) {\r\n  var isneg,\r\n    x = this,\r\n    xc = x.c,\r\n    yc = (y = new x.constructor(y)).c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  isneg = i < 0;\r\n\r\n  // Compare exponents.\r\n  if (k != l) return k > l ^ isneg ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = -1; ++i < j;) {\r\n    if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ isneg ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.div = function (y) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    a = x.c,                  // dividend\r\n    b = (y = new Big(y)).c,   // divisor\r\n    k = x.s == y.s ? 1 : -1,\r\n    dp = Big.DP;\r\n\r\n  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n\r\n  // Divisor is zero?\r\n  if (!b[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  // Dividend is 0? Return +-0.\r\n  if (!a[0]) {\r\n    y.s = k;\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  var bl, bt, n, cmp, ri,\r\n    bz = b.slice(),\r\n    ai = bl = b.length,\r\n    al = a.length,\r\n    r = a.slice(0, bl),   // remainder\r\n    rl = r.length,\r\n    q = y,                // quotient\r\n    qc = q.c = [],\r\n    qi = 0,\r\n    p = dp + (q.e = x.e - y.e) + 1;    // precision of the result\r\n\r\n  q.s = k;\r\n  k = p < 0 ? 0 : p;\r\n\r\n  // Create version of divisor with leading zero.\r\n  bz.unshift(0);\r\n\r\n  // Add zeros to make remainder as long as divisor.\r\n  for (; rl++ < bl;) r.push(0);\r\n\r\n  do {\r\n\r\n    // n is how many times the divisor goes into current remainder.\r\n    for (n = 0; n < 10; n++) {\r\n\r\n      // Compare divisor and remainder.\r\n      if (bl != (rl = r.length)) {\r\n        cmp = bl > rl ? 1 : -1;\r\n      } else {\r\n        for (ri = -1, cmp = 0; ++ri < bl;) {\r\n          if (b[ri] != r[ri]) {\r\n            cmp = b[ri] > r[ri] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If divisor < remainder, subtract divisor from remainder.\r\n      if (cmp < 0) {\r\n\r\n        // Remainder can't be more than 1 digit longer than divisor.\r\n        // Equalise lengths using divisor with extra leading zero?\r\n        for (bt = rl == bl ? b : bz; rl;) {\r\n          if (r[--rl] < bt[rl]) {\r\n            ri = rl;\r\n            for (; ri && !r[--ri];) r[ri] = 9;\r\n            --r[ri];\r\n            r[rl] += 10;\r\n          }\r\n          r[rl] -= bt[rl];\r\n        }\r\n\r\n        for (; !r[0];) r.shift();\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Add the digit n to the result array.\r\n    qc[qi++] = cmp ? n : ++n;\r\n\r\n    // Update the remainder.\r\n    if (r[0] && cmp) r[rl] = a[ai] || 0;\r\n    else r = [a[ai]];\r\n\r\n  } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\r\n\r\n  // Leading zero? Do not remove if result is simply zero (qi == 1).\r\n  if (!qc[0] && qi != 1) {\r\n\r\n    // There can't be more than one zero.\r\n    qc.shift();\r\n    q.e--;\r\n    p--;\r\n  }\r\n\r\n  // Round?\r\n  if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\r\n\r\n  return q;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n */\r\nP.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n * false.\r\n */\r\nP.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.gte = function (y) {\r\n  return this.cmp(y) > -1;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n */\r\nP.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var i, j, t, xlty,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  // Signs differ?\r\n  if (a != b) {\r\n    y.s = -b;\r\n    return x.plus(y);\r\n  }\r\n\r\n  var xc = x.c.slice(),\r\n    xe = x.e,\r\n    yc = y.c,\r\n    ye = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (yc[0]) {\r\n      y.s = -b;\r\n    } else if (xc[0]) {\r\n      y = new Big(x);\r\n    } else {\r\n      y.s = 1;\r\n    }\r\n    return y;\r\n  }\r\n\r\n  // Determine which is the bigger number. Prepend zeros to equalise exponents.\r\n  if (a = xe - ye) {\r\n\r\n    if (xlty = a < 0) {\r\n      a = -a;\r\n      t = xc;\r\n    } else {\r\n      ye = xe;\r\n      t = yc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (b = a; b--;) t.push(0);\r\n    t.reverse();\r\n  } else {\r\n\r\n    // Exponents equal. Check digit by digit.\r\n    j = ((xlty = xc.length < yc.length) ? xc : yc).length;\r\n\r\n    for (a = b = 0; b < j; b++) {\r\n      if (xc[b] != yc[b]) {\r\n        xlty = xc[b] < yc[b];\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // x < y? Point xc to the array of the bigger number.\r\n  if (xlty) {\r\n    t = xc;\r\n    xc = yc;\r\n    yc = t;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  /*\r\n   * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n   * needs to start at yc.length.\r\n   */\r\n  if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\r\n\r\n  // Subtract yc from xc.\r\n  for (b = i; j > a;) {\r\n    if (xc[--j] < yc[j]) {\r\n      for (i = j; i && !xc[--i];) xc[i] = 9;\r\n      --xc[i];\r\n      xc[j] += 10;\r\n    }\r\n\r\n    xc[j] -= yc[j];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xc[--b] === 0;) xc.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xc[0] === 0;) {\r\n    xc.shift();\r\n    --ye;\r\n  }\r\n\r\n  if (!xc[0]) {\r\n\r\n    // n - n = +0\r\n    y.s = 1;\r\n\r\n    // Result must be zero.\r\n    xc = [ye = 0];\r\n  }\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n */\r\nP.mod = function (y) {\r\n  var ygtx,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  if (!y.c[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  x.s = y.s = 1;\r\n  ygtx = y.cmp(x) == 1;\r\n  x.s = a;\r\n  y.s = b;\r\n\r\n  if (ygtx) return new Big(x);\r\n\r\n  a = Big.DP;\r\n  b = Big.RM;\r\n  Big.DP = Big.RM = 0;\r\n  x = x.div(y);\r\n  Big.DP = a;\r\n  Big.RM = b;\r\n\r\n  return this.minus(x.times(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big negated.\r\n */\r\nP.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n */\r\nP.plus = P.add = function (y) {\r\n  var e, k, t,\r\n    x = this,\r\n    Big = x.constructor;\r\n\r\n  y = new Big(y);\r\n\r\n  // Signs differ?\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  var xe = x.e,\r\n    xc = x.c,\r\n    ye = y.e,\r\n    yc = y.c;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (!yc[0]) {\r\n      if (xc[0]) {\r\n        y = new Big(x);\r\n      } else {\r\n        y.s = x.s;\r\n      }\r\n    }\r\n    return y;\r\n  }\r\n\r\n  xc = xc.slice();\r\n\r\n  // Prepend zeros to equalise exponents.\r\n  // Note: reverse faster than unshifts.\r\n  if (e = xe - ye) {\r\n    if (e > 0) {\r\n      ye = xe;\r\n      t = yc;\r\n    } else {\r\n      e = -e;\r\n      t = xc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (; e--;) t.push(0);\r\n    t.reverse();\r\n  }\r\n\r\n  // Point xc to the longer array.\r\n  if (xc.length - yc.length < 0) {\r\n    t = yc;\r\n    yc = xc;\r\n    xc = t;\r\n  }\r\n\r\n  e = yc.length;\r\n\r\n  // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\r\n  for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\r\n\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n\r\n  if (k) {\r\n    xc.unshift(k);\r\n    ++ye;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (e = xc.length; xc[--e] === 0;) xc.pop();\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a Big whose value is the value of this Big raised to the power n.\r\n * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n * mode Big.RM.\r\n *\r\n * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n */\r\nP.pow = function (n) {\r\n  var x = this,\r\n    one = new x.constructor('1'),\r\n    y = one,\r\n    isneg = n < 0;\r\n\r\n  if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\r\n    throw Error(INVALID + 'exponent');\r\n  }\r\n\r\n  if (isneg) n = -n;\r\n\r\n  for (;;) {\r\n    if (n & 1) y = y.times(x);\r\n    n >>= 1;\r\n    if (!n) break;\r\n    x = x.times(x);\r\n  }\r\n\r\n  return isneg ? one.div(y) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.prec = function (sd, rm) {\r\n  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n    throw Error(INVALID + 'precision');\r\n  }\r\n  return round(new this.constructor(this), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n * using rounding mode rm, or Big.RM if rm is not specified.\r\n * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n * If dp is not specified, round to 0 decimal places.\r\n *\r\n * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.round = function (dp, rm) {\r\n  if (dp === UNDEFINED) dp = 0;\r\n  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n  return round(new this.constructor(this), dp + this.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.sqrt = function () {\r\n  var r, c, t,\r\n    x = this,\r\n    Big = x.constructor,\r\n    s = x.s,\r\n    e = x.e,\r\n    half = new Big('0.5');\r\n\r\n  // Zero?\r\n  if (!x.c[0]) return new Big(x);\r\n\r\n  // Negative?\r\n  if (s < 0) {\r\n    throw Error(NAME + 'No square root');\r\n  }\r\n\r\n  // Estimate.\r\n  s = Math.sqrt(+stringify(x, true, true));\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\r\n  if (s === 0 || s === 1 / 0) {\r\n    c = x.c.join('');\r\n    if (!(c.length + e & 1)) c += '0';\r\n    s = Math.sqrt(c);\r\n    e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\r\n    r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\r\n  } else {\r\n    r = new Big(s + '');\r\n  }\r\n\r\n  e = r.e + (Big.DP += 4);\r\n\r\n  // Newton-Raphson iteration.\r\n  do {\r\n    t = r;\r\n    r = half.times(t.plus(x.div(t)));\r\n  } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\r\n\r\n  return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big times the value of Big y.\r\n */\r\nP.times = P.mul = function (y) {\r\n  var c,\r\n    x = this,\r\n    Big = x.constructor,\r\n    xc = x.c,\r\n    yc = (y = new Big(y)).c,\r\n    a = xc.length,\r\n    b = yc.length,\r\n    i = x.e,\r\n    j = y.e;\r\n\r\n  // Determine sign of result.\r\n  y.s = x.s == y.s ? 1 : -1;\r\n\r\n  // Return signed 0 if either 0.\r\n  if (!xc[0] || !yc[0]) {\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  // Initialise exponent of result as x.e + y.e.\r\n  y.e = i + j;\r\n\r\n  // If array xc has fewer digits than yc, swap xc and yc, and lengths.\r\n  if (a < b) {\r\n    c = xc;\r\n    xc = yc;\r\n    yc = c;\r\n    j = a;\r\n    a = b;\r\n    b = j;\r\n  }\r\n\r\n  // Initialise coefficient array of result with zeros.\r\n  for (c = new Array(j = a + b); j--;) c[j] = 0;\r\n\r\n  // Multiply.\r\n\r\n  // i is initially xc.length.\r\n  for (i = b; i--;) {\r\n    b = 0;\r\n\r\n    // a is yc.length.\r\n    for (j = a + i; j > i;) {\r\n\r\n      // Current sum of products at this digit position, plus carry.\r\n      b = c[j] + yc[i] * xc[j - i - 1] + b;\r\n      c[j--] = b % 10;\r\n\r\n      // carry\r\n      b = b / 10 | 0;\r\n    }\r\n\r\n    c[j] = b;\r\n  }\r\n\r\n  // Increment result exponent if there is a final carry, otherwise remove leading zero.\r\n  if (b) ++y.e;\r\n  else c.shift();\r\n\r\n  // Remove trailing zeros.\r\n  for (i = c.length; !c[--i];) c.pop();\r\n  y.c = c;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), ++dp, rm);\r\n    for (; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, true, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), dp + x.e + 1, rm);\r\n\r\n    // x.e may have changed if the value is rounded up.\r\n    for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, false, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Omit the sign for negative zero.\r\n */\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toJSON = P.toString = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Big as a primitve number.\r\n */\r\nP.toNumber = function () {\r\n  var n = +stringify(this, true, true);\r\n  if (this.constructor.strict === true && !this.eq(n.toString())) {\r\n    throw Error(NAME + 'Imprecise conversion');\r\n  }\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big rounded to sd significant digits using\r\n * rounding mode rm, or Big.RM if rm is not specified.\r\n * Use exponential notation if sd is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    n = x.c[0];\r\n\r\n  if (sd !== UNDEFINED) {\r\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n      throw Error(INVALID + 'precision');\r\n    }\r\n    x = round(new Big(x), sd, rm);\r\n    for (; x.c.length < sd;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Include the sign for negative zero.\r\n */\r\nP.valueOf = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  if (Big.strict === true) {\r\n    throw Error(NAME + 'valueOf disallowed');\r\n  }\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\r\n};\r\n\r\n\r\n// Export\r\n\r\n\r\nvar Big = _Big_();\r\n\r\n/// <reference types=\"https://raw.githubusercontent.com/DefinitelyTyped/DefinitelyTyped/master/types/big.js/index.d.ts\" />\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Big);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/big.js/big.mjs\n");

/***/ })

};
;