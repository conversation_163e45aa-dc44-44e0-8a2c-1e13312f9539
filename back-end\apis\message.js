const express = require("express");
const router = express.Router();
const messageController = require("../controller/message");

router.post('/initiateDirect', messageController.initiateDirectChatRoom);

router.get('/chat-room-info/:chatRoomId', messageController.getChatRoomInfo);


router.get('/chat-room/:chatRoomId', messageController.getMessagesByChatRoom);

router.get("/:tradeId/release-deadline", messageController.getReleaseDeadline);

router.get("/:tradeId", messageController.getMessagesByTrade);

router.post("/", messageController.sendMessage);

router.post("/initiate", messageController.initiateChatRoom);

// Direct user-to-user chat room (not tied to trade/perk)

module.exports = router; 