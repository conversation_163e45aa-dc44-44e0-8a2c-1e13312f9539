import { useState, useCallback } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useWallet } from './useWallet';
import { uploadToPinata } from '@/utils/helpers';
import { isFormValid } from '@/utils/formValidation';
import {
  FormData,
  FormErrors,
  validateForm as validateFormFunction,
  validateName,
  validateTicker,
  validateWebsite,
  validateSocial,
  validateImage,
} from '@/components/auth/create-coin-form/FormValidation';
import { submitForm } from '@/components/auth/create-coin-form/FormSubmission';
import { showErrorToast, showSuccessToast, TOAST_MESSAGES, createAPIError } from '@/utils/errorHandling';

interface UseCreateCoinFormProps {
  onClose: () => void;
}

interface UseCreateCoinFormReturn {
  // Form state
  formData: FormData;
  errors: FormErrors;
  isSubmitting: boolean;
  submitted: boolean;
  previewUrl: string | null;
  showOptions: boolean;

  // Form handlers
  handleChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  handleBlur: (
    e: React.FocusEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;

  // UI handlers
  removeImage: () => void;
  toggleOptions: () => void;

  // Wallet state
  isConnected: boolean;
}

export const useCreateCoinForm = ({
  onClose,
}: UseCreateCoinFormProps): UseCreateCoinFormReturn => {
  const { solanaWallet, isConnected, getWalletAddress } = useWallet();
  const { state } = useAppContext();

  // Form state
  const [formData, setFormData] = useState<FormData>({
    name: '',
    ticker: '',
    description: '',
    picture: null,
    telegram: '',
    website: '',
    twitter: '',
    category: '',
  });

  const [errors, setErrors] = useState<FormErrors>({
    name: '',
    ticker: '',
    description: '',
    picture: '',
    telegram: '',
    website: '',
    twitter: '',
    category: '',
    api: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);

  // Get user ID from context
  const userBo =
    typeof state.userBo === 'string' ? JSON.parse(state.userBo) : state.userBo;
  const userID = userBo?.id;

  // Form change handler
  const handleChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));

      if (errors[name as keyof typeof errors]) {
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    [errors]
  );

  // Form blur handler for validation
  const handleBlur = useCallback(
    (
      e: React.FocusEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;

      let error = '';
      switch (name) {
        case 'name':
          error = validateName(value);
          break;
        case 'ticker':
          error = validateTicker(value);
          break;
        case 'website':
          error = validateWebsite(value);
          break;
        case 'telegram':
          error = validateSocial(value, 'telegram');
          break;
        case 'twitter':
          error = validateSocial(value, 'twitter');
          break;
      }

      if (error) {
        setErrors((prev) => ({ ...prev, [name]: error }));
      }
    },
    []
  );

  // File change handler
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];
        setFormData((prev) => ({ ...prev, picture: file }));

        const error = validateImage(file);
        if (error) {
          setErrors((prev) => ({ ...prev, picture: error }));
          setPreviewUrl(null);
        } else {
          setErrors((prev) => ({ ...prev, picture: '' }));
          const url = URL.createObjectURL(file);
          setPreviewUrl(url);
        }
      }
    },
    []
  );

  // Remove image handler
  const removeImage = useCallback(() => {
    setFormData((prev) => ({ ...prev, picture: null }));
    setPreviewUrl(null);
    setErrors((prev) => ({ ...prev, picture: '' }));
  }, []);

  // Toggle options handler
  const toggleOptions = useCallback(() => {
    setShowOptions(!showOptions);
  }, [showOptions]);

  // Form validation
  const validateForm = useCallback((): boolean => {
    // Pass userID for backward compatibility i needed it, but the function now uses session validation
    const newErrors = validateFormFunction(formData, userID);
    setErrors(newErrors);
    return isFormValid(newErrors);
  }, [formData, userID]);

  // Form submission handler
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) return;

      setIsSubmitting(true);
      let imageUrl = '';
      if (formData.picture) {
        imageUrl = await uploadToPinata(formData.picture);
      }

      if (!isConnected || !solanaWallet) {
        showErrorToast(TOAST_MESSAGES.WALLET.CONNECT_REQUIRED);
        return;
      }

      try {
        const walletAddress = getWalletAddress() || '';
        const result = await submitForm(
          formData,
          walletAddress,
          userID,
          imageUrl
        );

        if (result.success) {
          setSubmitted(true);
          showSuccessToast(result.message);

          setTimeout(() => {
            setFormData({
              name: '',
              ticker: '',
              description: '',
              picture: null,
              telegram: '',
              website: '',
              twitter: '',
              category: '',
            });
            setSubmitted(false);
            onClose();
            window.location.reload();
          }, 2000);
        } else {
          showErrorToast(result.message);
        }
      } catch (error: any) {
        console.error('Error creating token:', error);
        
        // Handle different types of errors with specific messages
        let errorMessage = TOAST_MESSAGES.TOKEN.CREATE_FAILED;
        
        if (error?.response?.status === 401) {
          errorMessage = TOAST_MESSAGES.AUTH.LOGIN_REQUIRED;
        } else if (error?.response?.status === 400) {
          errorMessage = error?.response?.data?.message || TOAST_MESSAGES.FORM.VALIDATION_FAILED;
        } else if (error?.response?.status >= 500) {
          errorMessage = TOAST_MESSAGES.NETWORK.SERVER_ERROR;
        } else if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }
        
        showErrorToast(createAPIError(errorMessage, error?.response?.status));
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      formData,
      validateForm,
      isConnected,
      solanaWallet,
      getWalletAddress,
      userID,
      onClose,
    ]
  );

  return {
    // Form state
    formData,
    errors,
    isSubmitting,
    submitted,
    previewUrl,
    showOptions,

    // Form handlers
    handleChange,
    handleBlur,
    handleFileChange,
    handleSubmit,

    // UI handlers
    removeImage,
    toggleOptions,

    // Wallet state
    isConnected,
  };
};
