"use client";
import React, { useState, useEffect } from "react";
import ChatModal from "./ChatModal";
import ChatIconButton from "./ChatIconButton";
import axios from "axios";

export default function ChatPanelWrapper() {
  const [chatOpen, setChatOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [tradeId, setTradeId] = useState<number | null>(null);
  const [receiverId, setReceiverId] = useState<number | null>(null);

  // Get current user ID
  const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
  const userBo = userBoStr ? JSON.parse(userBoStr) : null;
  const currentUserId = userBo?.id;

  // Fetch unread count (demo: all messages for user, count where receiverId === currentUserId)
  useEffect(() => {
    if (!currentUserId) return;
    async function fetchUnread() {
      try {
        // Example: fetch all messages for the user (replace with your real API for unread count)
        const res = await axios.get(`/messages/user/${currentUserId}`);
        // Count messages where receiverId === currentUserId and not read (if available)
        const unread = (res.data.data || []).filter((msg: any) => msg.receiverId === currentUserId && !msg.read).length;
        setUnreadCount(unread);
      } catch (err) {
        setUnreadCount(0);
      }
    }
    fetchUnread();
  }, [currentUserId, chatOpen]);

  // Mark messages as read when chat is opened
  useEffect(() => {
    if (chatOpen && currentUserId && tradeId) {
      // Example: call backend to mark messages as read for this trade/user
      axios.post(`/messages/mark-read`, { tradeId, userId: currentUserId });
      setUnreadCount(0);
    }
  }, [chatOpen, currentUserId, tradeId]);

  // Example: open chat for a specific trade/user (replace with your notification click logic)
  const handleOpenChat = (tradeId: number, receiverId: number) => {
    setTradeId(tradeId);
    setReceiverId(receiverId);
    setChatOpen(true);
  };

  // For demo: open chat with dummy trade/user on icon click
  return (
    <>
      <ChatIconButton unreadCount={unreadCount} onClick={() => handleOpenChat(1, 2)} />
      {chatOpen && tradeId && receiverId && (
        <ChatModal
          tradeId={tradeId}
          receiverId={receiverId}
          onClose={() => setChatOpen(false)}
        />
      )}
    </>
  );
} 