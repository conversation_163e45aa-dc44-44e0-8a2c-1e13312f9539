'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Database } from 'lucide-react';

import { useTablePagination } from '@/hooks/useTablePagination';
import type { TableColumn, TableProps, TableRow } from './types';
import { useTableSelection } from './components/TableSelection';
import { useTranslation } from '@/hooks/useTranslation';


// Dynamically import heavy components
const TableSkeleton = dynamic(() => import('@/components/ui/LoadingSkeletons').then(mod => ({ default: mod.TableSkeleton })), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const TableContainer = dynamic(() => import('./components/TableContainer').then(mod => ({ default: mod.TableContainer })), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const TableEmptyState = dynamic(() => import('./components/TableEmptyState').then(mod => ({ default: mod.TableEmptyState })), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>,
});

const TableErrorState = dynamic(() => import('./components/TableErrorState').then(mod => ({ default: mod.TableErrorState })), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>,
});

const TablePagination = dynamic(() => import('./components/TablePagination').then(mod => ({ default: mod.TablePagination })), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>,
});

const TableBody = dynamic(() => import('./components/TableBody').then(mod => ({ default: mod.TableBody })), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const TableHeader = dynamic(() => import('./components/TableHeader').then(mod => ({ default: mod.TableHeader })), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>,
});

const Table = <T extends TableRow>({
  title,
  columns,
  data,
  className = '',
  currentPage = 1,
  totalPages,
  rowsPerPage = 10,
  onPageChange,
  loading = false,
  error = null,
  emptyMessage,
  emptyIcon = <Database className="w-12 h-12" />,
  variant = 'default',
  size = 'md',
  onRowClick,
  selectable = false,
  selectedRows = new Set(),
  onSelectionChange,
}: TableProps<T>) => {
  const { t } = useTranslation();
  
  // Use translation key if no custom empty message is provided
  const defaultEmptyMessage = t('table.noDataAvailable');
  const finalEmptyMessage = emptyMessage || defaultEmptyMessage;
  
  // Use pagination hook
  const {
    paginatedData,
    currentPage: currentPageForDisplay,
    totalPages: calculatedTotalPages,
    handlePageChange,
  } = useTablePagination({
    data,
    currentPage,
    totalPages,
    rowsPerPage,
    onPageChange,
  });

  // Use selection hook
  const { handleSelectAll, handleRowSelect } = useTableSelection({
    selectable,
    selectedRows,
    paginatedData,
    onSelectionChange,
  });

  // Loading state
  if (loading) {
    return (
      <Suspense fallback={<div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>}>
        <TableSkeleton />
      </Suspense>
    );
  }

  // Error state
  if (error) {
    return (
      <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>}>
        <TableErrorState
          error={error}
          className={className}
          variant={variant}
        />
      </Suspense>
    );
  }

  return (
    <Suspense fallback={<div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>}>
      <TableContainer
        title={title}
        className={className}
        variant={variant}
        size={size}
      >
        {data.length === 0 ? (
          <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>}>
            <TableEmptyState icon={emptyIcon} message={emptyMessage} />
          </Suspense>
        ) : (
          <>
            <table className="w-full">
              <Suspense fallback={<div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>}>
                <TableHeader
                  columns={columns as TableColumn<TableRow>[]}
                  selectable={selectable}
                  selectedRows={selectedRows}
                  paginatedData={paginatedData}
                  onSelectAll={handleSelectAll}
                />
              </Suspense>
              <Suspense fallback={<div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>}>
                <TableBody
                  columns={columns as TableColumn<TableRow>[]}
                  data={paginatedData}
                  selectable={selectable}
                  selectedRows={selectedRows}
                  onRowSelect={handleRowSelect}
                  onRowClick={onRowClick as (row: TableRow, index: number) => void}
                />
              </Suspense>
            </table>

            {/* Pagination */}
            {calculatedTotalPages > 1 && (
              <Suspense fallback={<div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>}>
                <TablePagination
                  currentPage={currentPageForDisplay}
                  totalPages={calculatedTotalPages}
                  onPageChange={handlePageChange}
                  totalItems={data.length}
                  itemsPerPage={rowsPerPage}
                />
              </Suspense>
            )}
          </>
        )}
      </TableContainer>
    </Suspense>
  );
};

export default Table;
