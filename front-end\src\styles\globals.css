@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }
  
  [dir="rtl"] .text-left {
    text-align: right;
  }
  
  [dir="rtl"] .text-right {
    text-align: left;
  }
  
  [dir="rtl"] .ml-auto {
    margin-left: unset;
    margin-right: auto;
  }
  
  [dir="rtl"] .mr-auto {
    margin-right: unset;
    margin-left: auto;
  }
  
  [dir="rtl"] .pl-4 {
    padding-left: unset;
    padding-right: 1rem;
  }
  
  [dir="rtl"] .pr-4 {
    padding-right: unset;
    padding-left: 1rem;
  }
  
  [dir="rtl"] .border-l {
    border-left: unset;
    border-right: 1px solid;
  }
  
  [dir="rtl"] .border-r {
    border-right: unset;
    border-left: 1px solid;
  }
}

/* globals.css */

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraLightItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/fonts/Poppins/Poppins-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
}

/* Button Styling and Hover Effects */
button {
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

/* Header Navigation Items Hover Effects */
div[class^='hidden lg:flex items-center gap-4'] a {
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

/* Force white icon on hover for navigation links */
div[class^='hidden lg:flex items-center gap-4'] a:hover img {
  filter: brightness(0) invert(1) !important;
  opacity: 1 !important;
}

div[class^='hidden lg:flex items-center gap-4'] a:hover {
  background-color: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
}

/* Style for Dashboard, Perks, Coins buttons */
div[class^='hidden lg:flex items-center gap-4'] a:not(.bg-black):hover span {
  color: white !important;
}

/* Create your token hover effect */
div[class^='hidden lg:flex items-center gap-4'] div {
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

div[class^='hidden lg:flex items-center gap-4'] div:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Tabs Hover Effects */
.hidden.md\:flex.w-full div.bg-neutral-200:hover {
  background-color: #d0d0d0 !important;
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* For tab items inside main container */
div.bg-neutral-200:hover {
  background-color: #d0d0d0 !important;
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* Active tabs don't need hover effect */
.hidden.md\:flex.w-full div.bg-black:hover {
  box-shadow: none;
}

/* Universal hover effect - darken by ~10% */
/* Exclude menu, notification, and profile buttons */
button:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  filter: brightness(90%);
}

/* Specific color overrides for common button colors */
/* Black button hover effect */
button.bg-black:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  background-color: #333333;
  filter: none;
}

/* White/light button hover effect */
button.bg-white:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-gray-100:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-slate-100:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#fff']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#f']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover {
  background-color: #e6e6e6;
  filter: none;
}

/* Blue button hover effects */
button.bg-blue-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-blue-600:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='ff']:hover {
  filter: none;
  background-color: #2563eb;
}

/* Green button hover effects */
button.bg-green-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-\[\#3BB266\]:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#3BB266']:not([class*='menu']):not(
    [class*='notification']
  ):not([class*='profile']):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='bb']:hover {
  filter: none;
  background-color: #309854;
}

/* Red button hover effects */
button.bg-red-500:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button.bg-red-600:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):hover,
button[class*='bg-[#']:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not(
    [class*='header-']
  ):hover[class*='f']:hover[class*='3']:hover {
  filter: none;
  background-color: #f58a38;
}

/* Add subtle press effect on active (for non-menu/notification/profile buttons) */
button:not([class*='menu']):not([class*='notification']):not(
    [class*='profile']
  ):not([class*='toggle']):not([class*='nav-']):not([class*='header-']):active {
  transform: scale(0.98);
}

/* Make sure navigation and menu buttons don't have any hover effects */
button[class*='menu'],
button[class*='notification'],
button[class*='profile'],
button[class*='toggle'],
button[class*='nav-'],
button[class*='header-'] {
  transition: none !important;
  transform: none !important;
  filter: none !important;
}

/* Special buttons hover effects */
.w-\[740px\].h-16.bg-white,
.w-\[581px\].h-\[67px\].bg-white {
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease !important;
}

/* Targeted hover style for these specific buttons */
.w-\[740px\].h-16.bg-white:hover,
.w-\[581px\].h-\[67px\].bg-white:hover {
  background-color: black !important;
}

/* Target text directly */
div.w-\[740px\].h-16.bg-white,
div.w-\[581px\].h-\[67px\].bg-white {
  color: black;
}

div.w-\[740px\].h-16.bg-white:hover,
div.w-\[581px\].h-\[67px\].bg-white:hover {
  color: white !important;
}

/* Add subtle pulsing glow effect */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
  }
  70% {
    box-shadow: 0 0 5px 7px rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

.w-\[740px\].h-16.bg-white:hover,
.w-\[581px\].h-\[67px\].bg-white:hover {
  animation: pulse-glow 2s infinite;
}

/* Create Airdrop button hover effect */
button.bg-black.text-white.px-6.py-3.text-base.font-medium {
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  box-shadow: none;
}

button.bg-black.text-white.px-6.py-3.text-base.font-medium:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
}

button.bg-black.text-white.px-6.py-3.text-base.font-medium::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

button.bg-black.text-white.px-6.py-3.text-base.font-medium:hover::after {
  left: 100%;
}

/* Create perk button hover effect */
div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer:hover {
  background-color: #222 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

div.w-52.h-14.bg-black.flex.items-center.justify-center.cursor-pointer:hover::after {
  left: 100%;
}
