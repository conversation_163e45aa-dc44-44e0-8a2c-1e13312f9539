var express = require("express");
const router = express.Router();
const { body, param } = require("express-validator");
const { validate } = require("../middleware/validator");
const { authenticate, require2FA } = require("../middleware/authenticate");
const {
  getUser,
  addUser,
  loginUser,
  changePassword,
  updateProfile,
  loginUserPrivy,
  getUserPurchasesSummary,
  setup2FA,
  verify2FA,
  login2FA,
  disable2FA,
  getUserTrades,
  getUserById,
  followUser,
  unfollowUser,
  getFollowers,
  getFollowing,
  getPublicProfile
} = require("../controller/user");

//api endpoint for getting user info
router.get(
  "/:id/:name",
  [
    param("id").isUUID().withMessage("User ID must be a valid UUID"),
    param("name")
      .isString()
      .isLength({ min: 1 })
      .withMessage("Name must be a non-empty string"),
  ],
  validate,
  getUser
);

router.post(
  "/addUser",
  [
    body("username").trim().notEmpty().withMessage("Username is required"),
    body("email").isEmail().withMessage("A valid email is required"),
    body("password")
      .isLength({ min: 6 })
      .withMessage("Password must be at least 6 characters long"),
  ],
  validate,
  addUser
);

router.post(
  "/login",
  [
    body("email").isEmail().withMessage("A valid email is required"),
    body("password").isLength({ min: 6 }).withMessage("Password is required"),
  ],
  validate,
  loginUser
);

router.post(
  "/loginPrivy",
  [
    body("privyId").notEmpty().withMessage("privyId is invalid."),
    body("privywallet").notEmpty().withMessage("Valid wallet is required."),
  ],
  validate,
  loginUserPrivy
);
router.post(
  "/changePassword",
  [
    body("email").isEmail().withMessage("A valid email is required"),
    body("oldPassword")
      .isLength({ min: 6 })
      .withMessage("Old password is required"),
    body("newPassword")
      .isLength({ min: 6 })
      .withMessage("New password must be at least 6 characters"),
  ],
  validate,
  changePassword
);

router.post(
  "/updateProfile",
  authenticate,
  [body("userId").isUUID().withMessage("A valid userId is required")],
  validate,
  updateProfile
);
router.post("/getUserPurchasesSummary", authenticate, getUserPurchasesSummary);

// 2FA endpoints
router.post("/2fa/setup", authenticate, setup2FA);
router.post("/2fa/verify", authenticate, verify2FA);
router.post("/2fa/login", login2FA);
router.delete("/2fa/disable", authenticate, disable2FA);

// Social/follow endpoints
router.post('/:id/follow', followUser);
router.post('/:id/unfollow', unfollowUser);
router.get('/:id/followers', getFollowers);
router.get('/:id/following', getFollowing);
router.get('/:userId/trades', getUserTrades);
router.get('/public-profile/:userId', getPublicProfile);

// Generic dynamic routes at the bottom
router.get('/:id', getUserById);

module.exports = router;
