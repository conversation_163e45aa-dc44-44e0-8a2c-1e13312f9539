const i18next = require('i18next');
const Backend = require('i18next-fs-backend');
const path = require('path');

// Initialize i18next
const i18n = i18next.createInstance();

i18n
  .use(Backend)
  .init({
    lng: 'en', // default language
    fallbackLng: 'en',
    preload: ['en', 'ar', 'ch'],
    ns: ['translation'],
    defaultNS: 'translation',
    backend: {
      loadPath: path.join(__dirname, 'locales/{{lng}}/translation.json'),
    },
    interpolation: {
      escapeValue: false, // not needed for backend
    },
    initImmediate: false, // synchronous init
  });

module.exports = i18n; 