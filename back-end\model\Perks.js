const { Model, DataTypes } = require("sequelize");

class Perks extends Model {
    static initModel(sequelize) {
        return Perks.init(
            {
                perkId: {
                    primaryKey: true,
                    allowNull: false,
                    autoIncrement: true,
                    type: DataTypes.BIGINT,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: true,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'SET NULL',
                },
                name: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                },
                description: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
                price: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                    defaultValue: 0,
                },
                address: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                fulfillmentLink: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                image: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                isLimited: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                stockAmount: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                tokenAmount: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    defaultValue: 1,
                    comment: 'Number of tokens buyer receives when purchasing this perk'
                },
                category: {
                    type: DataTypes.STRING(50),
                    allowNull: true,
                },
                isVerified: {
                    type: DataTypes.BOOLEAN,
                    defaultValue: false,
                },
                time: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    defaultValue: DataTypes.NOW,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'perks',
                timestamps: true,
            }
        );
    }
}

module.exports = Perks;
