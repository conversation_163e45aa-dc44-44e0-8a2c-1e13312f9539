use anchor_lang::prelude::*;

#[error_code]
pub enum ErrorCode {
    #[msg("Bonding curve is complete")]
    BondingCurveComplete,
    #[msg("You are not the vault owner")]
    Unauthorized,
    #[msg("Nothing to withdraw")]
    NothingToWithdraw,
    #[msg("Insufficient Sol for first buy")]
    InsufficientSolForFirstBuy,
    #[msg("Bonding curve is not complete")]
    BondingCurveNotComplete,
    #[msg("Withdrawal cooldown active")]
    WithdrawalCooldownActive,
    #[msg("Fee basis points cannot exceed 10000 (100%)")]
    FeeTooHigh,
    #[msg("Total supply must be greater than the initial real token reserves.")] // <-- ADD THIS
    InvalidTokenReserveConfiguration,
    #[msg("Amount need to be greater than zero")]
    InvalidAmount,
    #[msg("Invalid token mint")]
    InvalidMint,
    #[msg("Insufficient funds")]
    InsufficientFunds,
}
