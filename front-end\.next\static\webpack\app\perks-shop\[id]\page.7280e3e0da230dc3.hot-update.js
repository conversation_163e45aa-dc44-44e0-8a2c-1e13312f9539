"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(signedTransaction.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price\n                });\n                // Use trade data to get the numeric escrowId\n                // First try to get it from the extra field, then fall back to original escrowId\n                let numericEscrowId = null;\n                try {\n                    if (tradeData.extra) {\n                        const extraData = JSON.parse(tradeData.extra);\n                        numericEscrowId = extraData.escrowId;\n                    }\n                } catch (e) {\n                    console.log('Could not parse extra data:', e);\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                calculatedPerkMint = ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowId);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});