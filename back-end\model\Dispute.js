const { Model, DataTypes } = require("sequelize");

class Dispute extends Model {
    static initModel(sequelize) {
        return Dispute.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                tradeId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'token_purchased',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                initiatorId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                initiatorRole: {
                    type: DataTypes.ENUM('buyer', 'seller'),
                    allowNull: false,
                },
                reason: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                status: {
                    type: DataTypes.ENUM('open', 'assigned', 'resolved_buyer', 'resolved_seller', 'resolved_split'),
                    allowNull: false,
                    defaultValue: 'open',
                },
                moderatorId: {
                    type: DataTypes.BIGINT,
                    allowNull: true,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'SET NULL',
                },
                buyerPercentage: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                    validate: {
                        min: 0,
                        max: 100,
                    },
                },
                sellerPercentage: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                    validate: {
                        min: 0,
                        max: 100,
                    },
                },
                moderatorNotes: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
                resolvedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },
                disputeDeadline: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: () => {
                        const deadline = new Date();
                        deadline.setDate(deadline.getDate() + 2); // 2 days from creation
                        return deadline;
                    },
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'disputes',
                timestamps: true,
                indexes: [
                    {
                        fields: ['tradeId'],
                        unique: true, // One dispute per trade
                    },
                    {
                        fields: ['status'],
                    },
                    {
                        fields: ['moderatorId'],
                    },
                ],
            }
        );
    }

    static associate(models) {
        // Dispute belongs to a trade
        Dispute.belongsTo(models.TokenPurchased, { 
            foreignKey: 'tradeId', 
            as: 'trade' 
        });

        // Dispute belongs to an initiator (user)
        Dispute.belongsTo(models.User, { 
            foreignKey: 'initiatorId', 
            as: 'initiator' 
        });

        // Dispute belongs to a moderator (user)
        Dispute.belongsTo(models.User, { 
            foreignKey: 'moderatorId', 
            as: 'moderator' 
        });
    }
}

module.exports = Dispute;
