'use client';

import { motion } from 'framer-motion';
import React from 'react';

// TypeScript interfaces
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
  'aria-label'?: string;
}

interface SkeletonProps extends BaseComponentProps {
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'shimmer';
}

// Animation variants
const shimmer = {
  animate: {
    backgroundPosition: ['200% 0', '-200% 0'],
  },
  transition: {
    duration: 2,
    ease: 'linear',
    repeat: Infinity,
  },
};

const pulse = {
  animate: {
    opacity: [0.6, 1, 0.6],
  },
  transition: {
    duration: 1.5,
    ease: 'easeInOut',
    repeat: Infinity,
  },
};

const wave = {
  animate: {
    backgroundPosition: ['-200% 0', '200% 0'],
  },
  transition: {
    duration: 1.5,
    ease: 'linear',
    repeat: Infinity,
  },
};

// Base skeleton component with improved accessibility
const SkeletonBase: React.FC<SkeletonProps> = ({
  className = '',
  children,
  variant = 'rectangular',
  width,
  height,
  animation = 'shimmer',
  'data-testid': dataTestId,
  'aria-label': ariaLabel,
}) => {
  const getAnimationVariant = () => {
    switch (animation) {
      case 'pulse':
        return pulse;
      case 'wave':
        return wave;
      case 'shimmer':
      default:
        return shimmer;
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'circular':
        return 'rounded-full';
      case 'text':
        return 'rounded';
      case 'rectangular':
      default:
        return 'rounded';
    }
  };

  const style: React.CSSProperties = {
    width: width,
    height: height,
  };

  return (
    <motion.div
      className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] ${getVariantClasses()} ${className}`}
      style={style}
      {...getAnimationVariant() as any}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      role="status"
      aria-live="polite"
    >
      {children}
    </motion.div>
  );
};

// Table skeleton with improved structure
export const TableSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="w-full p-5 bg-neutral-200 rounded-2xl"
    data-testid={props['data-testid'] || 'table-skeleton'}
    role="status"
    aria-label="Loading table data"
  >
    <SkeletonBase className="h-8 w-48 mb-6 rounded" aria-label="Loading table title" />
    <div className="space-y-4" role="presentation">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex justify-between items-center">
          <SkeletonBase className="h-4 w-32 rounded" aria-label={`Loading row ${i + 1} column 1`} />
          <SkeletonBase className="h-4 w-24 rounded" aria-label={`Loading row ${i + 1} column 2`} />
          <SkeletonBase className="h-4 w-16 rounded" aria-label={`Loading row ${i + 1} column 3`} />
          <SkeletonBase className="h-8 w-20 rounded" aria-label={`Loading row ${i + 1} action`} />
        </div>
      ))}
    </div>
  </div>
);

// Header skeleton with improved accessibility
const HeaderSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="flex justify-between items-center mb-8"
    data-testid={props['data-testid'] || 'header-skeleton'}
    role="status"
    aria-label="Loading page header"
  >
    <div className="space-y-2">
      <SkeletonBase className="h-12 w-64 rounded" aria-label="Loading page title" />
      <SkeletonBase className="h-6 w-48 rounded" aria-label="Loading page subtitle" />
    </div>
    <SkeletonBase className="h-10 w-32 rounded" aria-label="Loading header action" />
  </div>
);

// Stats skeleton with improved structure
export const StatsSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="bg-white rounded-lg border p-6"
    data-testid={props['data-testid'] || 'stats-skeleton'}
    role="status"
    aria-label="Loading statistics"
  >
    <div className="flex items-center mb-4">
      <SkeletonBase 
        variant="circular"
        className="w-12 h-12 rounded-full mr-4" 
        aria-label="Loading stats icon"
      />
      <div className="space-y-2">
        <SkeletonBase className="h-5 w-32 rounded" aria-label="Loading stats title" />
        <SkeletonBase className="h-4 w-24 rounded" aria-label="Loading stats subtitle" />
      </div>
    </div>
    <div className="grid grid-cols-5 gap-4" role="presentation">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="text-center">
          <SkeletonBase className="h-4 w-16 rounded mb-2" aria-label={`Loading stat ${i + 1} label`} />
          <SkeletonBase className="h-5 w-20 rounded" aria-label={`Loading stat ${i + 1} value`} />
        </div>
      ))}
    </div>
  </div>
);

// Comments skeleton with improved structure
export const CommentsSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="space-y-6"
    data-testid={props['data-testid'] || 'comments-skeleton'}
    role="status"
    aria-label="Loading comments"
  >
    {Array.from({ length: 3 }).map((_, i) => (
      <div key={i} className="flex items-start space-x-4">
        <SkeletonBase 
          variant="circular"
          className="w-14 h-14 rounded-full" 
          aria-label={`Loading comment ${i + 1} avatar`}
        />
        <div className="flex-1 space-y-2">
          <SkeletonBase className="h-4 w-24 rounded" aria-label={`Loading comment ${i + 1} author`} />
          <SkeletonBase className="h-4 w-full rounded" aria-label={`Loading comment ${i + 1} content`} />
          <SkeletonBase className="h-3 w-20 rounded" aria-label={`Loading comment ${i + 1} timestamp`} />
        </div>
      </div>
    ))}
  </div>
);
export const ProductCardSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="bg-white rounded-xl shadow-md p-6"
    data-testid={props['data-testid'] || 'product-card-skeleton'}
    role="status"
    aria-label="Loading product card"
  >
    <div className="flex items-center mb-4">
      <SkeletonBase 
        variant="circular"
        className="w-10 h-10 rounded-full mr-3" 
        aria-label="Loading product avatar"
      />
      <div className="space-y-2 flex-1">
        <SkeletonBase className="h-4 w-24 rounded" aria-label="Loading product name" />
        <SkeletonBase className="h-3 w-16 rounded" aria-label="Loading product category" />
      </div>
    </div>
    <SkeletonBase className="h-16 w-full rounded mb-4" aria-label="Loading product image" />
    <div className="space-y-3" role="presentation">
      <SkeletonBase className="h-4 w-full rounded" aria-label="Loading product description line 1" />
      <SkeletonBase className="h-4 w-3/4 rounded" aria-label="Loading product description line 2" />
      <SkeletonBase className="h-4 w-1/2 rounded" aria-label="Loading product description line 3" />
    </div>
    <div className="mt-6 p-4 border rounded-lg">
      <SkeletonBase className="h-6 w-20 rounded mb-3" aria-label="Loading price label" />
      <div className="flex justify-between items-center">
        <SkeletonBase className="h-8 w-24 rounded" aria-label="Loading price" />
        <SkeletonBase className="h-10 w-32 rounded-full" aria-label="Loading action button" />
      </div>
    </div>
  </div>
);

// Dashboard skeleton with improved structure
export const DashboardSkeleton: React.FC<BaseComponentProps> = (props) => (
  <div 
    className="space-y-8"
    data-testid={props['data-testid'] || 'dashboard-skeleton'}
    role="status"
    aria-label="Loading dashboard"
  >
    <HeaderSkeleton />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="presentation">
      {Array.from({ length: 6 }).map((_, i) => (
        <SkeletonBase 
          key={i} 
          className="h-32 rounded-lg" 
          aria-label={`Loading dashboard card ${i + 1}`}
        />
      ))}
    </div>
    <TableSkeleton />
  </div>
);
