'use client';

import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';

import { useAppContext } from '../../contexts/AppContext';
import {
  validateName,
  validatePrice,
  validateUrl,
  validateStockAmount,
  validateUserLogin,
  hasErrors,
} from '../../utils/formValidation';
import { useTranslation } from '@/hooks/useTranslation';
import { showErrorToast, showSuccessToast, ValidationError, NetworkError } from '@/utils/errorHandling';

import { useFormHandlers } from './components/FormHandlers';
import {
  useFormSubmission,
  FormData,
  FormErrors,
} from './components/FormSubmission';

const FormFields = dynamic(() => import('./components/FormFields').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

// Validation functions using shared utilities
const validateFulfillmentLink = (link: string): string => {
  return validateUrl(link, 'fulfillment link');
};

const validateCategory = (category: string): string => {
  if (!category || category.trim() === '') {
    return 'Please select a category';
  }
  return '';
};

const validateForm = (formData: FormData, userId: number): FormErrors => {
  return {
    name: validateName(formData.name),
    description: '',
    price: validatePrice(formData.price),
    fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),
    picture: !formData.picture ? 'Please upload an image for your perk' : '',
    stockAmount: validateStockAmount(
      formData.stockAmount,
      formData.limitedStock
    ),
    category: validateCategory(formData.category),
    api: validateUserLogin(userId),
  };
};

const CreatePerkForm: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { t } = useTranslation();
  const { state } = useAppContext();
  const boxRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    price: '',
    fulfillmentLink: '',
    picture: null,
    limitedStock: false,
    stockAmount: '',
    tokenAmount: '1', // Default to 1 token
    category: '',
  });

  const [errors, setErrors] = useState<FormErrors>({
    name: '',
    description: '',
    price: '',
    fulfillmentLink: '',
    picture: '',
    stockAmount: '',
    tokenAmount: '',
    category: '',
    api: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationProgress, setValidationProgress] = useState<{[key: string]: boolean}>({});

  const userBo =
    typeof state.userBo === 'string' ? JSON.parse(state.userBo) : state.userBo;
  const userID = userBo?.id;

  // Validation functions using shared utilities
  const validateFulfillmentLink = (link: string): string => {
    return validateUrl(link, t('createPerkForm.fulfillmentLink'));
  };
  const validateCategory = (category: string): string => {
    if (!category || category.trim() === '') {
      return t('createPerkForm.errorSelectCategory');
    }
    return '';
  };

  const validateTokenAmount = (tokenAmount: string): string => {
    if (!tokenAmount || tokenAmount.trim() === '') {
      return 'Token amount is required';
    }
    const amount = parseInt(tokenAmount);
    if (isNaN(amount) || amount < 1) {
      return 'Token amount must be a positive number';
    }
    return '';
  };
  const validateForm = (formData: FormData, userId: number): FormErrors => {
    return {
      name: validateName(formData.name),
      description: '',
      price: validatePrice(formData.price),
      fulfillmentLink: validateFulfillmentLink(formData.fulfillmentLink),
      picture: !formData.picture ? t('createPerkForm.errorUploadPicture') : '',
      stockAmount: validateStockAmount(
        formData.stockAmount,
        formData.limitedStock
      ),
      tokenAmount: validateTokenAmount(formData.tokenAmount),
      category: validateCategory(formData.category),
      api: validateUserLogin(userId),
    };
  };

  const { handleSubmit } = useFormSubmission(
    formData,
    userID,
    onClose,
    setSubmitted,
    setIsSubmitting,
    setFormData,
    validateForm,
    hasErrors,
    t
  );

  const {
    handleChange,
    handleCheckboxChange,
    handleBlur,
    handleFileChange,
    handleDeletePicture,
  } = useFormHandlers(formData, setFormData, errors, setErrors);

  // Enhanced form submission with better error handling
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsValidating(true);
    setValidationProgress({});
    
    // Validate form
    const newErrors = validateForm(formData, userID);
    setErrors(newErrors);
    
    // Check for validation errors
    if (hasErrors(newErrors)) {
      setIsValidating(false);
      showErrorToast('Please fix the errors before submitting');
      return;
    }
    
    setIsValidating(false);
    
    // Proceed with submission
    try {
      await handleSubmit(e);
    } catch (error: any) {
      let errorMessage = 'Failed to create perk';
      if (error instanceof ValidationError) {
        errorMessage = error.message;
        setErrors(prev => ({ ...prev, api: error.message }));
      } else if (error instanceof NetworkError) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
        setErrors(prev => ({ ...prev, api: error.response.data.message }));
      } else if (error.error) {
        errorMessage = error.error;
        setErrors(prev => ({ ...prev, api: error.error }));
      } else {
        setErrors(prev => ({ ...prev, api: 'Something went wrong. Please try again.' }));
      }
      
      showErrorToast(errorMessage);
    }
  };

  // Enhanced field validation with progress tracking
  const handleFieldValidation = (fieldName: string, value: string) => {
    setValidationProgress(prev => ({ ...prev, [fieldName]: true }));
    
    // Simulate validation delay for better UX
    setTimeout(() => {
      let fieldError = '';
      
      switch (fieldName) {
        case 'name':
          fieldError = validateName(value);
          break;
        case 'price':
          fieldError = validatePrice(value);
          break;
        case 'fulfillmentLink':
          fieldError = validateFulfillmentLink(value);
          break;
        case 'category':
          fieldError = validateCategory(value);
          break;
        case 'stockAmount':
          fieldError = validateStockAmount(value, formData.limitedStock);
          break;
        default:
          fieldError = '';
      }
      
      setErrors(prev => ({ ...prev, [fieldName]: fieldError }));
      setValidationProgress(prev => ({ ...prev, [fieldName]: false }));
    }, 300);
  };

  // Enhanced change handler with validation
  const handleEnhancedChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    handleChange(e);
    
    // Clear API error when user starts typing
    if (errors.api) {
      setErrors(prev => ({ ...prev, api: '' }));
    }
    
    // Validate field after change
    handleFieldValidation(e.target.name, e.target.value);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (boxRef.current && !boxRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <motion.div
      className="w-[563px] max-w-full mx-auto bg-white rounded-[24px] md:rounded-[48px] p-4 md:p-10 pt-6 flex flex-col relative"
      ref={boxRef}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      <button
        className="block lg:hidden absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none"
        onClick={onClose}
      >
        <Image src={'/icons/close.svg'} alt={t('common.close')} width={24} height={24} />
      </button>
      
      {submitted ? (
        <motion.div 
          className="flex flex-col items-center gap-7 py-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px]">
            {t('createPerkForm.successTitle')}
          </div>
          <motion.svg
            className="w-24 h-24 text-green-500 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
            ></path>
          </motion.svg>
          <div className="text-2xl font-semibold font-['IBM_Plex_Sans']">
            {t('createPerkForm.successMessage')}
          </div>
          <div className="text-gray-600 font-['IBM_Plex_Sans']">
            {t('createPerkForm.successDescription')}
          </div>
        </motion.div>
      ) : (
        <form onSubmit={handleFormSubmit} className="flex flex-col w-full">
          <div className="text-center text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mb-8">
            {t('createPerkForm.formTitle')}
          </div>

          <FormFields
            formData={formData}
            errors={errors}
            handleChange={handleEnhancedChange}
            handleBlur={handleBlur}
            handleCheckboxChange={handleCheckboxChange}
            handleFileChange={handleFileChange}
            handleDeletePicture={handleDeletePicture}
            t={t}
            validationProgress={validationProgress}
          />

          {/* Enhanced API Error Display */}
          <AnimatePresence>
            {errors.api && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="bg-red-50 border border-red-200 rounded-lg p-3 mt-4"
              >
                <div className="flex items-center">
                  <span className="text-red-500 mr-2">⚠</span>
                  <p className="text-red-600 font-semibold text-base">
                    {errors.api}
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Form Progress Indicator */}
          {isValidating && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center justify-center mt-4"
            >
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-gray-600 text-sm">Validating form...</span>
              </div>
            </motion.div>
          )}

          <button
            type="submit"
            disabled={isSubmitting || isValidating || hasErrors(errors)}
            className={`w-full h-12 px-5 py-2 rounded-sm inline-flex justify-center items-center gap-2.5 mt-8 mx-auto transition-all duration-200 ${
              isSubmitting || isValidating || hasErrors(errors)
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-black hover:bg-gray-800'
            }`}
          >
            <div className="text-center text-white text-lg font-semibold font-['IBM_Plex_Sans'] leading-relaxed">
              {isSubmitting ? 'Creating perk...' : isValidating ? 'Validating...' : 'Create my perk'}
            </div>
            {(isSubmitting || isValidating) && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
          </button>
        </form>
      )}
    </motion.div>
  );
};

export default CreatePerkForm;
