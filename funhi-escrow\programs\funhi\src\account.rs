use anchor_lang::prelude::*;

#[account]
#[derive(InitSpace)]
pub struct Global {
    pub authority: Pubkey,
    pub fee_recipient: Pubkey,
    pub initial_virtual_token_reserves: u64,
    pub initial_virtual_sol_reserves: u64,
    pub initial_real_token_reserves: u64,
    pub token_total_supply: u64,
    pub trading_fee_bps: u64,
    pub first_buy_fee_sol: u64,
    pub graduation_threshold: u64,
}

#[account]
#[derive(InitSpace)]
pub struct BondingCurve {
    pub mint: Pubkey,
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub token_total_supply: u64,
    pub complete: bool,
}

#[account]
#[derive(InitSpace)]
pub struct CreatorVault {
    pub deposited_amount: u64,
    pub withdrawn_amount: u64,
    pub owner: Pubkey,
    pub mint: Pubkey,
    pub creation_ts: i64,
}

#[account]
#[derive(InitSpace)]
pub struct LastWithdraw {
    pub last_withdraw_timestamp: i64,
}
