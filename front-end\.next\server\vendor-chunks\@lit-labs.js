"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lit-labs";
exports.ids = ["vendor-chunks/@lit-labs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomElementRegistry: () => (/* binding */ CustomElementRegistryShimWithRealType),\n/* harmony export */   CustomEvent: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.CustomEvent),\n/* harmony export */   Element: () => (/* binding */ ElementShimWithRealType),\n/* harmony export */   ElementInternals: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ElementInternals),\n/* harmony export */   Event: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.Event),\n/* harmony export */   EventTarget: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventTarget),\n/* harmony export */   HTMLElement: () => (/* binding */ HTMLElementShimWithRealType),\n/* harmony export */   HYDRATE_INTERNALS_ATTR_PREFIX: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.HYDRATE_INTERNALS_ATTR_PREFIX),\n/* harmony export */   ariaMixinAttributes: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ariaMixinAttributes),\n/* harmony export */   customElements: () => (/* binding */ customElements)\n/* harmony export */ });\n/* harmony import */ var _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/element-internals.js */ \"(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js\");\n/* harmony import */ var _lib_events_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/events.js */ \"(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js\");\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n\n\n\n// In an empty Node.js vm, we need to patch the global context.\n// TODO: Remove these globalThis assignments when we remove support\n// for vm modules (--experimental-vm-modules).\nglobalThis.Event ??= _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventShim;\nglobalThis.CustomEvent ??= _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.CustomEventShim;\nconst attributes = new WeakMap();\nconst attributesForElement = (element) => {\n    let attrs = attributes.get(element);\n    if (attrs === undefined) {\n        attributes.set(element, (attrs = new Map()));\n    }\n    return attrs;\n};\n// The typings around the exports below are a little funky:\n//\n// 1. We want the `name` of the shim classes to match the real ones at runtime,\n//    hence e.g. `class Element`.\n// 2. We can't shadow the global types with a simple class declaration, because\n//    then we can't reference the global types for casting, hence e.g.\n//    `const ElementShim = class Element`.\n// 3. We want to export the classes typed as the real ones, hence e.g.\n//    `const ElementShimWithRealType = ElementShim as object as typeof Element;`.\n// 4. We want the exported names to match the real ones, hence e.g.\n//    `export {ElementShimWithRealType as Element}`.\nconst ElementShim = class Element extends _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventTargetShim {\n    constructor() {\n        super(...arguments);\n        this.__shadowRootMode = null;\n        this.__shadowRoot = null;\n        this.__internals = null;\n    }\n    get attributes() {\n        return Array.from(attributesForElement(this)).map(([name, value]) => ({\n            name,\n            value,\n        }));\n    }\n    get shadowRoot() {\n        if (this.__shadowRootMode === 'closed') {\n            return null;\n        }\n        return this.__shadowRoot;\n    }\n    get localName() {\n        return this.constructor.__localName;\n    }\n    get tagName() {\n        return this.localName?.toUpperCase();\n    }\n    setAttribute(name, value) {\n        // Emulate browser behavior that silently casts all values to string. E.g.\n        // `42` becomes `\"42\"` and `{}` becomes `\"[object Object]\"\"`.\n        attributesForElement(this).set(name, String(value));\n    }\n    removeAttribute(name) {\n        attributesForElement(this).delete(name);\n    }\n    toggleAttribute(name, force) {\n        // Steps reference https://dom.spec.whatwg.org/#dom-element-toggleattribute\n        if (this.hasAttribute(name)) {\n            // Step 5\n            if (force === undefined || !force) {\n                this.removeAttribute(name);\n                return false;\n            }\n        }\n        else {\n            // Step 4\n            if (force === undefined || force) {\n                // Step 4.1\n                this.setAttribute(name, '');\n                return true;\n            }\n            else {\n                // Step 4.2\n                return false;\n            }\n        }\n        // Step 6\n        return true;\n    }\n    hasAttribute(name) {\n        return attributesForElement(this).has(name);\n    }\n    attachShadow(init) {\n        const shadowRoot = { host: this };\n        this.__shadowRootMode = init.mode;\n        if (init && init.mode === 'open') {\n            this.__shadowRoot = shadowRoot;\n        }\n        return shadowRoot;\n    }\n    attachInternals() {\n        if (this.__internals !== null) {\n            throw new Error(`Failed to execute 'attachInternals' on 'HTMLElement': ` +\n                `ElementInternals for the specified element was already attached.`);\n        }\n        const internals = new _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ElementInternalsShim(this);\n        this.__internals = internals;\n        return internals;\n    }\n    getAttribute(name) {\n        const value = attributesForElement(this).get(name);\n        return value ?? null;\n    }\n};\nconst ElementShimWithRealType = ElementShim;\n\nconst HTMLElementShim = class HTMLElement extends ElementShim {\n};\nconst HTMLElementShimWithRealType = HTMLElementShim;\n\n// For convenience, we provide a global instance of a HTMLElement as an event\n// target. This facilitates registering global event handlers\n// (e.g. for @lit/context ContextProvider).\n// We use this in in the SSR render function.\n// Note, this is a bespoke element and not simply `document` or `window` since\n// user code relies on these being undefined in the server environment.\nglobalThis.litServerRoot ??= Object.defineProperty(new HTMLElementShimWithRealType(), 'localName', {\n    // Patch localName (and tagName) to return a unique name.\n    get() {\n        return 'lit-server-root';\n    },\n});\nconst CustomElementRegistryShim = class CustomElementRegistry {\n    constructor() {\n        this.__definitions = new Map();\n    }\n    define(name, ctor) {\n        if (this.__definitions.has(name)) {\n            if (true) {\n                console.warn(`'CustomElementRegistry' already has \"${name}\" defined. ` +\n                    `This may have been caused by live reload or hot module ` +\n                    `replacement in which case it can be safely ignored.\\n` +\n                    `Make sure to test your application with a production build as ` +\n                    `repeat registrations will throw in production.`);\n            }\n            else {}\n        }\n        // Provide tagName and localName for the component.\n        ctor.__localName = name;\n        this.__definitions.set(name, {\n            ctor,\n            // Note it's important we read `observedAttributes` in case it is a getter\n            // with side-effects, as is the case in Lit, where it triggers class\n            // finalization.\n            //\n            // TODO(aomarks) To be spec compliant, we should also capture the\n            // registration-time lifecycle methods like `connectedCallback`. For them\n            // to be actually accessible to e.g. the Lit SSR element renderer, though,\n            // we'd need to introduce a new API for accessing them (since `get` only\n            // returns the constructor).\n            observedAttributes: ctor.observedAttributes ?? [],\n        });\n    }\n    get(name) {\n        const definition = this.__definitions.get(name);\n        return definition?.ctor;\n    }\n};\nconst CustomElementRegistryShimWithRealType = CustomElementRegistryShim;\n\nconst customElements = new CustomElementRegistryShimWithRealType();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ElementInternals: () => (/* binding */ ElementInternalsShimWithRealType),\n/* harmony export */   ElementInternalsShim: () => (/* binding */ ElementInternalsShim),\n/* harmony export */   HYDRATE_INTERNALS_ATTR_PREFIX: () => (/* binding */ HYDRATE_INTERNALS_ATTR_PREFIX),\n/* harmony export */   ariaMixinAttributes: () => (/* binding */ ariaMixinAttributes)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Map of ARIAMixin properties to attributes\n */\nconst ariaMixinAttributes = {\n    ariaAtomic: 'aria-atomic',\n    ariaAutoComplete: 'aria-autocomplete',\n    ariaBrailleLabel: 'aria-braillelabel',\n    ariaBrailleRoleDescription: 'aria-brailleroledescription',\n    ariaBusy: 'aria-busy',\n    ariaChecked: 'aria-checked',\n    ariaColCount: 'aria-colcount',\n    ariaColIndex: 'aria-colindex',\n    ariaColSpan: 'aria-colspan',\n    ariaCurrent: 'aria-current',\n    ariaDescription: 'aria-description',\n    ariaDisabled: 'aria-disabled',\n    ariaExpanded: 'aria-expanded',\n    ariaHasPopup: 'aria-haspopup',\n    ariaHidden: 'aria-hidden',\n    ariaInvalid: 'aria-invalid',\n    ariaKeyShortcuts: 'aria-keyshortcuts',\n    ariaLabel: 'aria-label',\n    ariaLevel: 'aria-level',\n    ariaLive: 'aria-live',\n    ariaModal: 'aria-modal',\n    ariaMultiLine: 'aria-multiline',\n    ariaMultiSelectable: 'aria-multiselectable',\n    ariaOrientation: 'aria-orientation',\n    ariaPlaceholder: 'aria-placeholder',\n    ariaPosInSet: 'aria-posinset',\n    ariaPressed: 'aria-pressed',\n    ariaReadOnly: 'aria-readonly',\n    ariaRequired: 'aria-required',\n    ariaRoleDescription: 'aria-roledescription',\n    ariaRowCount: 'aria-rowcount',\n    ariaRowIndex: 'aria-rowindex',\n    ariaRowSpan: 'aria-rowspan',\n    ariaSelected: 'aria-selected',\n    ariaSetSize: 'aria-setsize',\n    ariaSort: 'aria-sort',\n    ariaValueMax: 'aria-valuemax',\n    ariaValueMin: 'aria-valuemin',\n    ariaValueNow: 'aria-valuenow',\n    ariaValueText: 'aria-valuetext',\n    role: 'role',\n};\n// Shim the global element internals object\n// Methods should be fine as noops and properties can generally\n// be while on the server.\nconst ElementInternalsShim = class ElementInternals {\n    get shadowRoot() {\n        // Grab the shadow root instance from the Element shim\n        // to ensure that the shadow root is always available\n        // to the internals instance even if the mode is 'closed'\n        return this.__host\n            .__shadowRoot;\n    }\n    constructor(_host) {\n        this.ariaAtomic = '';\n        this.ariaAutoComplete = '';\n        this.ariaBrailleLabel = '';\n        this.ariaBrailleRoleDescription = '';\n        this.ariaBusy = '';\n        this.ariaChecked = '';\n        this.ariaColCount = '';\n        this.ariaColIndex = '';\n        this.ariaColSpan = '';\n        this.ariaCurrent = '';\n        this.ariaDescription = '';\n        this.ariaDisabled = '';\n        this.ariaExpanded = '';\n        this.ariaHasPopup = '';\n        this.ariaHidden = '';\n        this.ariaInvalid = '';\n        this.ariaKeyShortcuts = '';\n        this.ariaLabel = '';\n        this.ariaLevel = '';\n        this.ariaLive = '';\n        this.ariaModal = '';\n        this.ariaMultiLine = '';\n        this.ariaMultiSelectable = '';\n        this.ariaOrientation = '';\n        this.ariaPlaceholder = '';\n        this.ariaPosInSet = '';\n        this.ariaPressed = '';\n        this.ariaReadOnly = '';\n        this.ariaRequired = '';\n        this.ariaRoleDescription = '';\n        this.ariaRowCount = '';\n        this.ariaRowIndex = '';\n        this.ariaRowSpan = '';\n        this.ariaSelected = '';\n        this.ariaSetSize = '';\n        this.ariaSort = '';\n        this.ariaValueMax = '';\n        this.ariaValueMin = '';\n        this.ariaValueNow = '';\n        this.ariaValueText = '';\n        this.role = '';\n        this.form = null;\n        this.labels = [];\n        this.states = new Set();\n        this.validationMessage = '';\n        this.validity = {};\n        this.willValidate = true;\n        this.__host = _host;\n    }\n    checkValidity() {\n        // TODO(augustjk) Consider actually implementing logic.\n        // See https://github.com/lit/lit/issues/3740\n        console.warn('`ElementInternals.checkValidity()` was called on the server.' +\n            'This method always returns true.');\n        return true;\n    }\n    reportValidity() {\n        return true;\n    }\n    setFormValue() { }\n    setValidity() { }\n};\nconst ElementInternalsShimWithRealType = ElementInternalsShim;\n\nconst HYDRATE_INTERNALS_ATTR_PREFIX = 'hydrate-internals-';\n//# sourceMappingURL=element-internals.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js":
/*!***********************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/lib/events.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomEvent: () => (/* binding */ CustomEventShimWithRealType),\n/* harmony export */   CustomEventShim: () => (/* binding */ CustomEventShimWithRealType),\n/* harmony export */   Event: () => (/* binding */ EventShimWithRealType),\n/* harmony export */   EventShim: () => (/* binding */ EventShimWithRealType),\n/* harmony export */   EventTarget: () => (/* binding */ EventTargetShimWithRealType),\n/* harmony export */   EventTargetShim: () => (/* binding */ EventTargetShimWithRealType)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _Event_cancelable, _Event_bubbles, _Event_composed, _Event_defaultPrevented, _Event_timestamp, _Event_propagationStopped, _Event_type, _Event_target, _Event_isBeingDispatched, _a, _CustomEvent_detail, _b;\nconst isCaptureEventListener = (options) => (typeof options === 'boolean' ? options : options?.capture ?? false);\n// Event phases\nconst NONE = 0;\nconst CAPTURING_PHASE = 1;\nconst AT_TARGET = 2;\nconst BUBBLING_PHASE = 3;\n// Shim the global EventTarget object\nconst EventTargetShim = class EventTarget {\n    constructor() {\n        this.__eventListeners = new Map();\n        this.__captureEventListeners = new Map();\n    }\n    addEventListener(type, callback, options) {\n        if (callback === undefined || callback === null) {\n            return;\n        }\n        const eventListenersMap = isCaptureEventListener(options)\n            ? this.__captureEventListeners\n            : this.__eventListeners;\n        let eventListeners = eventListenersMap.get(type);\n        if (eventListeners === undefined) {\n            eventListeners = new Map();\n            eventListenersMap.set(type, eventListeners);\n        }\n        else if (eventListeners.has(callback)) {\n            return;\n        }\n        const normalizedOptions = typeof options === 'object' && options ? options : {};\n        normalizedOptions.signal?.addEventListener('abort', () => this.removeEventListener(type, callback, options));\n        eventListeners.set(callback, normalizedOptions ?? {});\n    }\n    removeEventListener(type, callback, options) {\n        if (callback === undefined || callback === null) {\n            return;\n        }\n        const eventListenersMap = isCaptureEventListener(options)\n            ? this.__captureEventListeners\n            : this.__eventListeners;\n        const eventListeners = eventListenersMap.get(type);\n        if (eventListeners !== undefined) {\n            eventListeners.delete(callback);\n            if (!eventListeners.size) {\n                eventListenersMap.delete(type);\n            }\n        }\n    }\n    dispatchEvent(event) {\n        const composedPath = [this];\n        let parent = this.__eventTargetParent;\n        if (event.composed) {\n            while (parent) {\n                composedPath.push(parent);\n                parent = parent.__eventTargetParent;\n            }\n        }\n        else {\n            // If the event is not composed and the event was dispatched inside\n            // shadow DOM, we need to stop before the host of the shadow DOM.\n            while (parent && parent !== this.__host) {\n                composedPath.push(parent);\n                parent = parent.__eventTargetParent;\n            }\n        }\n        // We need to patch various properties that would either be empty or wrong\n        // in this scenario.\n        let stopPropagation = false;\n        let stopImmediatePropagation = false;\n        let eventPhase = NONE;\n        let target = null;\n        let tmpTarget = null;\n        let currentTarget = null;\n        const originalStopPropagation = event.stopPropagation;\n        const originalStopImmediatePropagation = event.stopImmediatePropagation;\n        Object.defineProperties(event, {\n            target: {\n                get() {\n                    return target ?? tmpTarget;\n                },\n                ...enumerableProperty,\n            },\n            srcElement: {\n                get() {\n                    return event.target;\n                },\n                ...enumerableProperty,\n            },\n            currentTarget: {\n                get() {\n                    return currentTarget;\n                },\n                ...enumerableProperty,\n            },\n            eventPhase: {\n                get() {\n                    return eventPhase;\n                },\n                ...enumerableProperty,\n            },\n            composedPath: {\n                value: () => composedPath,\n                ...enumerableProperty,\n            },\n            stopPropagation: {\n                value: () => {\n                    stopPropagation = true;\n                    originalStopPropagation.call(event);\n                },\n                ...enumerableProperty,\n            },\n            stopImmediatePropagation: {\n                value: () => {\n                    stopImmediatePropagation = true;\n                    originalStopImmediatePropagation.call(event);\n                },\n                ...enumerableProperty,\n            },\n        });\n        // An event handler can either be a function, an object with a handleEvent\n        // method or null. This function takes care to call the event handler\n        // correctly.\n        const invokeEventListener = (listener, options, eventListenerMap) => {\n            if (typeof listener === 'function') {\n                listener(event);\n            }\n            else if (typeof listener?.handleEvent === 'function') {\n                listener.handleEvent(event);\n            }\n            if (options.once) {\n                eventListenerMap.delete(listener);\n            }\n        };\n        // When an event is finished being dispatched, which can be after the event\n        // tree has been traversed or stopPropagation/stopImmediatePropagation has\n        // been called. Once that is the case, the currentTarget and eventPhase\n        // need to be reset and a value, representing whether the event has not\n        // been prevented, needs to be returned.\n        const finishDispatch = () => {\n            currentTarget = null;\n            eventPhase = NONE;\n            return !event.defaultPrevented;\n        };\n        // An event starts with the capture order, where it starts from the top.\n        // This is done even if bubbles is set to false, which is the default.\n        const captureEventPath = composedPath.slice().reverse();\n        // If the event target, which dispatches the event, is either in the light DOM\n        // or the event is not composed, the target is always itself. If that is not\n        // the case, the target needs to be retargeted: https://dom.spec.whatwg.org/#retarget\n        target = !this.__host || !event.composed ? this : null;\n        const retarget = (eventTargets) => {\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            tmpTarget = this;\n            while (tmpTarget.__host && eventTargets.includes(tmpTarget.__host)) {\n                tmpTarget = tmpTarget.__host;\n            }\n        };\n        for (const eventTarget of captureEventPath) {\n            if (!target && (!tmpTarget || tmpTarget === eventTarget.__host)) {\n                retarget(captureEventPath.slice(captureEventPath.indexOf(eventTarget)));\n            }\n            currentTarget = eventTarget;\n            eventPhase = eventTarget === event.target ? AT_TARGET : CAPTURING_PHASE;\n            const captureEventListeners = eventTarget.__captureEventListeners.get(event.type);\n            if (captureEventListeners) {\n                for (const [listener, options] of captureEventListeners) {\n                    invokeEventListener(listener, options, captureEventListeners);\n                    if (stopImmediatePropagation) {\n                        // Event.stopImmediatePropagation() stops any following invocation\n                        // of an event handler even on the same event target.\n                        return finishDispatch();\n                    }\n                }\n            }\n            if (stopPropagation) {\n                // Event.stopPropagation() stops any following invocation\n                // of an event handler for any following event targets.\n                return finishDispatch();\n            }\n        }\n        const bubbleEventPath = event.bubbles ? composedPath : [this];\n        tmpTarget = null;\n        for (const eventTarget of bubbleEventPath) {\n            if (!target &&\n                (!tmpTarget || eventTarget === tmpTarget.__host)) {\n                retarget(bubbleEventPath.slice(0, bubbleEventPath.indexOf(eventTarget) + 1));\n            }\n            currentTarget = eventTarget;\n            eventPhase = eventTarget === event.target ? AT_TARGET : BUBBLING_PHASE;\n            const captureEventListeners = eventTarget.__eventListeners.get(event.type);\n            if (captureEventListeners) {\n                for (const [listener, options] of captureEventListeners) {\n                    invokeEventListener(listener, options, captureEventListeners);\n                    if (stopImmediatePropagation) {\n                        // Event.stopImmediatePropagation() stops any following invocation\n                        // of an event handler even on the same event target.\n                        return finishDispatch();\n                    }\n                }\n            }\n            if (stopPropagation) {\n                // Event.stopPropagation() stops any following invocation\n                // of an event handler for any following event targets.\n                return finishDispatch();\n            }\n        }\n        return finishDispatch();\n    }\n};\nconst EventTargetShimWithRealType = EventTargetShim;\n\nconst enumerableProperty = { __proto__: null };\nenumerableProperty.enumerable = true;\nObject.freeze(enumerableProperty);\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst EventShim = (_a = class Event {\n        constructor(type, options = {}) {\n            _Event_cancelable.set(this, false);\n            _Event_bubbles.set(this, false);\n            _Event_composed.set(this, false);\n            _Event_defaultPrevented.set(this, false);\n            _Event_timestamp.set(this, Date.now());\n            _Event_propagationStopped.set(this, false);\n            _Event_type.set(this, void 0);\n            _Event_target.set(this, void 0);\n            _Event_isBeingDispatched.set(this, void 0);\n            this.NONE = NONE;\n            this.CAPTURING_PHASE = CAPTURING_PHASE;\n            this.AT_TARGET = AT_TARGET;\n            this.BUBBLING_PHASE = BUBBLING_PHASE;\n            if (arguments.length === 0)\n                throw new Error(`The type argument must be specified`);\n            if (typeof options !== 'object' || !options) {\n                throw new Error(`The \"options\" argument must be an object`);\n            }\n            const { bubbles, cancelable, composed } = options;\n            __classPrivateFieldSet(this, _Event_cancelable, !!cancelable, \"f\");\n            __classPrivateFieldSet(this, _Event_bubbles, !!bubbles, \"f\");\n            __classPrivateFieldSet(this, _Event_composed, !!composed, \"f\");\n            __classPrivateFieldSet(this, _Event_type, `${type}`, \"f\");\n            __classPrivateFieldSet(this, _Event_target, null, \"f\");\n            __classPrivateFieldSet(this, _Event_isBeingDispatched, false, \"f\");\n        }\n        initEvent(_type, _bubbles, _cancelable) {\n            throw new Error('Method not implemented.');\n        }\n        stopImmediatePropagation() {\n            this.stopPropagation();\n        }\n        preventDefault() {\n            __classPrivateFieldSet(this, _Event_defaultPrevented, true, \"f\");\n        }\n        get target() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get currentTarget() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get srcElement() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get type() {\n            return __classPrivateFieldGet(this, _Event_type, \"f\");\n        }\n        get cancelable() {\n            return __classPrivateFieldGet(this, _Event_cancelable, \"f\");\n        }\n        get defaultPrevented() {\n            return __classPrivateFieldGet(this, _Event_cancelable, \"f\") && __classPrivateFieldGet(this, _Event_defaultPrevented, \"f\");\n        }\n        get timeStamp() {\n            return __classPrivateFieldGet(this, _Event_timestamp, \"f\");\n        }\n        composedPath() {\n            return __classPrivateFieldGet(this, _Event_isBeingDispatched, \"f\") ? [__classPrivateFieldGet(this, _Event_target, \"f\")] : [];\n        }\n        get returnValue() {\n            return !__classPrivateFieldGet(this, _Event_cancelable, \"f\") || !__classPrivateFieldGet(this, _Event_defaultPrevented, \"f\");\n        }\n        get bubbles() {\n            return __classPrivateFieldGet(this, _Event_bubbles, \"f\");\n        }\n        get composed() {\n            return __classPrivateFieldGet(this, _Event_composed, \"f\");\n        }\n        get eventPhase() {\n            return __classPrivateFieldGet(this, _Event_isBeingDispatched, \"f\") ? _a.AT_TARGET : _a.NONE;\n        }\n        get cancelBubble() {\n            return __classPrivateFieldGet(this, _Event_propagationStopped, \"f\");\n        }\n        set cancelBubble(value) {\n            if (value) {\n                __classPrivateFieldSet(this, _Event_propagationStopped, true, \"f\");\n            }\n        }\n        stopPropagation() {\n            __classPrivateFieldSet(this, _Event_propagationStopped, true, \"f\");\n        }\n        get isTrusted() {\n            return false;\n        }\n    },\n    _Event_cancelable = new WeakMap(),\n    _Event_bubbles = new WeakMap(),\n    _Event_composed = new WeakMap(),\n    _Event_defaultPrevented = new WeakMap(),\n    _Event_timestamp = new WeakMap(),\n    _Event_propagationStopped = new WeakMap(),\n    _Event_type = new WeakMap(),\n    _Event_target = new WeakMap(),\n    _Event_isBeingDispatched = new WeakMap(),\n    _a.NONE = NONE,\n    _a.CAPTURING_PHASE = CAPTURING_PHASE,\n    _a.AT_TARGET = AT_TARGET,\n    _a.BUBBLING_PHASE = BUBBLING_PHASE,\n    _a);\nObject.defineProperties(EventShim.prototype, {\n    initEvent: enumerableProperty,\n    stopImmediatePropagation: enumerableProperty,\n    preventDefault: enumerableProperty,\n    target: enumerableProperty,\n    currentTarget: enumerableProperty,\n    srcElement: enumerableProperty,\n    type: enumerableProperty,\n    cancelable: enumerableProperty,\n    defaultPrevented: enumerableProperty,\n    timeStamp: enumerableProperty,\n    composedPath: enumerableProperty,\n    returnValue: enumerableProperty,\n    bubbles: enumerableProperty,\n    composed: enumerableProperty,\n    eventPhase: enumerableProperty,\n    cancelBubble: enumerableProperty,\n    stopPropagation: enumerableProperty,\n    isTrusted: enumerableProperty,\n});\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst CustomEventShim = (_b = class CustomEvent extends EventShim {\n        constructor(type, options = {}) {\n            super(type, options);\n            _CustomEvent_detail.set(this, void 0);\n            __classPrivateFieldSet(this, _CustomEvent_detail, options?.detail ?? null, \"f\");\n        }\n        initCustomEvent(_type, _bubbles, _cancelable, _detail) {\n            throw new Error('Method not implemented.');\n        }\n        get detail() {\n            return __classPrivateFieldGet(this, _CustomEvent_detail, \"f\");\n        }\n    },\n    _CustomEvent_detail = new WeakMap(),\n    _b);\nObject.defineProperties(CustomEventShim.prototype, {\n    detail: enumerableProperty,\n});\nconst EventShimWithRealType = EventShim;\nconst CustomEventShimWithRealType = CustomEventShim;\n\n//# sourceMappingURL=events.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js\n");

/***/ })

};
;