"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ChatModalStateContext */ \"(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { updateModalProps, getChatModalState } = (0,_contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(tradeIdToUse, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: tradeIdToUse.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification, resolvedTradeId)=>{\n        return async ()=>{\n            var _notification_data;\n            const tradeIdToUse = resolvedTradeId || ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId);\n            if (!tradeIdToUse || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: tradeIdToUse,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, tradeIdToUse // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(tradeIdToUse, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId,\n            hasTradeId: !!notification.data.tradeId,\n            hasChatRoomId: !!notification.data.chatRoomId,\n            hasPerkId: !!notification.data.perkId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Use getUserTrades to find trades for current user and filter by perkId\n                    const { getUserTrades } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                    const tradesResponse = await getUserTrades(String(myUserId));\n                    if (tradesResponse.status === 200 && tradesResponse.data) {\n                        // Find active trade for this perk\n                        const activeTrades = tradesResponse.data.filter((trade)=>trade.perkId === Number(perkId) && [\n                                'pending_acceptance',\n                                'escrowed',\n                                'completed',\n                                'released'\n                            ].includes(trade.status));\n                        if (activeTrades.length > 0) {\n                            // Get the most recent trade\n                            const sortedTrades = activeTrades.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                            tradeIdToUse = sortedTrades[0].id;\n                            console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                var _tradeResponse_data;\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                console.log('🔍 [NotificationBell] Trade details response:', {\n                    status: tradeResponse.status,\n                    hasData: !!tradeResponse.data,\n                    tradeData: tradeResponse.data,\n                    tradeDataId: (_tradeResponse_data = tradeResponse.data) === null || _tradeResponse_data === void 0 ? void 0 : _tradeResponse_data.id,\n                    tradeDataKeys: tradeResponse.data ? Object.keys(tradeResponse.data) : []\n                });\n                if (tradeResponse.status === 200 && tradeResponse.data) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    // Ensure we have a valid ID - use tradeIdToUse as fallback\n                    const validId = tradeResponse.data.id || tradeIdToUse;\n                    activeTrade = {\n                        id: validId,\n                        status: tradeResponse.data.status,\n                        tradeId: validId,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched successfully:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to,\n                        escrowId: activeTrade.escrowId,\n                        perkId: activeTrade.perkId\n                    });\n                } else {\n                    console.log('⚠️ [NotificationBell] Trade details response not successful, using fallback');\n                    // Fallback to notification data\n                    activeTrade = {\n                        id: tradeIdToUse,\n                        status: notification.data.status || 'pending_acceptance',\n                        tradeId: tradeIdToUse,\n                        from: buyerId,\n                        to: sellerId,\n                        perkId: notification.data.perkId,\n                        escrowId: notification.data.escrowId\n                    };\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: notification.data.status || 'pending_acceptance',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId,\n                    perkId: notification.data.perkId,\n                    escrowId: notification.data.escrowId\n                };\n                console.log('🔄 [NotificationBell] Using fallback trade data:', activeTrade);\n            }\n        } else {\n            console.log('⚠️ [NotificationBell] No tradeId found, chat will open without trade context');\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, update it instead of creating new one\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, updating with new state');\n            // Update the existing modal with new props\n            const newProps = {\n                activeTrade,\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onRelease: tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n                    alert('Cannot release escrow: Trade information not available');\n                },\n                onRefund: tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n                    alert('Cannot refund escrow: Trade information not available');\n                },\n                onAccept: tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n                    console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n                    alert('Cannot accept escrow: Trade information not available');\n                }\n            };\n            // Update modal props through the state context\n            const updated = updateModalProps(chatRoomId, newProps);\n            if (updated) {\n                console.log('✅ [NotificationBell] Successfully updated existing chat modal');\n                existingModal.scrollIntoView({\n                    behavior: 'smooth'\n                });\n                setIsOpen(false);\n                return;\n            } else {\n                console.log('⚠️ [NotificationBell] Failed to update modal, will create new one');\n            }\n        }\n        // Final debug before opening modal\n        console.log('🎯 [NotificationBell] Final modal state:', {\n            notificationType: notification.type,\n            tradeIdToUse,\n            hasActiveTrade: !!activeTrade,\n            activeTrade,\n            chatRoomId,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            walletConnected: isConnected,\n            hasWallet: !!(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address)\n        });\n        // Ensure we always have functional action functions\n        const safeOnRelease = tradeIdToUse ? createReleaseFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot release: No trade ID available');\n            alert('Cannot release escrow: Trade information not available');\n        };\n        const safeOnAccept = tradeIdToUse ? createAcceptFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot accept: No trade ID available');\n            alert('Cannot accept escrow: Trade information not available');\n        };\n        const safeOnRefund = tradeIdToUse ? createRefundFunction(notification, tradeIdToUse) : ()=>{\n            console.error('❌ [NotificationBell] Cannot refund: No trade ID available');\n            alert('Cannot refund escrow: Trade information not available');\n        };\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: safeOnRelease,\n                onRefund: safeOnRefund,\n                onReport: ()=>{\n                    console.log('🔍 [NotificationBell] Report function called');\n                // TODO: Implement report functionality\n                },\n                onAccept: safeOnAccept,\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 754,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 753,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 762,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 770,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 769,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 780,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 778,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 788,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 786,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 804,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 802,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 811,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 822,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 821,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 820,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 836,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 846,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 855,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 853,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 863,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 870,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 880,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 879,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 878,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 909,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 922,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 912,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 957,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 932,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 995,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1025,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 1054,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 993,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 1070,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 1069,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 930,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 907,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"QdiDVwBej9/cSe8ZkWZlFhrudmc=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_10__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _contexts_ChatModalStateContext__WEBPACK_IMPORTED_MODULE_4__.useChatModalState,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_5__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_6__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1e23e8b738a2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMWUyM2U4YjczOGEyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});