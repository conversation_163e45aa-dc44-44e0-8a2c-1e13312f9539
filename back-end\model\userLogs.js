const { Model, DataTypes } = require("sequelize");

class UserLog extends Model {
    static initModel(sequelize) {
        return UserLog.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                },
                message: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                messageDetail: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                messageKey: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                messageVars: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
                type: {
                    type: DataTypes.STRING(50),
                    allowNull: false,
                },
                chatRoomId: {
                    type: DataTypes.STRING(100),
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'userLogs',
                timestamps: true,
            }
        );
    }
}

module.exports = UserLog;
