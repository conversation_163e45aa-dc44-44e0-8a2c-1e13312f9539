"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx":
/*!******************************************************************!*\
  !*** ./src/components/shared/notifications/NotificationBell.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useEscrowOperations */ \"(app-pages-browser)/./src/hooks/useEscrowOperations.ts\");\n/* harmony import */ var _components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/chat/ChatModal */ \"(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\");\n/* harmony import */ var _RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RealTimeNotificationListener */ \"(app-pages-browser)/./src/components/shared/notifications/RealTimeNotificationListener.tsx\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const [systemNotifications, setSystemNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tradeNotifications, setTradeNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemUnreadCount, setSystemUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [tradeUnreadCount, setTradeUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H)();\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const { solanaWallet, isConnected } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const escrowOperations = (0,_hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations)();\n    // Load notifications and separate them by type\n    const loadNotifications = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const [notificationsResponse, unreadResponse] = await Promise.all([\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getNotifications)({\n                    page: 1,\n                    pageSize: 20\n                }),\n                (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.getUnreadNotificationCount)()\n            ]);\n            if (notificationsResponse.status === 200) {\n                const allNotifications = notificationsResponse.data.notifications;\n                // Separate notifications into system and trade categories\n                const systemNots = [];\n                const tradeNots = [];\n                allNotifications.forEach((notification)=>{\n                    if (isTradeNotification(notification)) {\n                        tradeNots.push(notification);\n                    } else {\n                        systemNots.push(notification);\n                    }\n                });\n                console.log('📊 [NotificationBell] Notification categorization:', {\n                    totalNotifications: allNotifications.length,\n                    systemNotifications: systemNots.length,\n                    tradeNotifications: tradeNots.length,\n                    systemTypes: systemNots.map((n)=>n.type),\n                    tradeTypes: tradeNots.map((n)=>n.type)\n                });\n                setSystemNotifications(systemNots);\n                setTradeNotifications(tradeNots);\n                // Calculate unread counts for each category\n                const systemUnread = systemNots.filter((n)=>!n.isRead).length;\n                const tradeUnread = tradeNots.filter((n)=>!n.isRead).length;\n                setSystemUnreadCount(systemUnread);\n                setTradeUnreadCount(tradeUnread);\n            }\n        // Note: We calculate unread counts from the filtered notifications\n        // rather than using the API response since we need separate counts\n        } catch (error) {\n            console.error('Failed to load notifications:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper function to determine if a notification is trade-related\n    const isTradeNotification = (notification)=>{\n        // System notifications (non-trade) - these go to System tab\n        const systemTypes = [\n            'system_announcement',\n            'moderator_added',\n            'perk_created',\n            'token_created',\n            // Moderator-specific notifications (go to moderator dashboard)\n            'dispute_created',\n            'dispute_assigned'\n        ];\n        // If it's a system type, it's NOT a trade notification\n        if (systemTypes.includes(notification.type)) {\n            return false;\n        }\n        // All escrow, trade, and user dispute notifications go to Trade tab\n        const tradeTypes = [\n            // Trade and escrow notifications\n            'perk_purchased',\n            'perk_sold',\n            'escrow_created',\n            'escrow_released',\n            'escrow_release_reminder',\n            'trade_completed',\n            'trade_refunded',\n            'trade_reported',\n            'trade_disputed',\n            // User dispute notifications (trade-related)\n            'dispute_resolved',\n            // Chat notifications (trade-related)\n            'chat_message'\n        ];\n        // Return true if it's a trade type\n        return tradeTypes.includes(notification.type);\n    };\n    // Get user role in trade for display\n    const getUserRoleInTrade = (notification)=>{\n        var _notification_data, _notification_data1;\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.buyerId) || !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.sellerId)) return '';\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        if (notification.data.buyerId === userIdNumber) return 'buyer';\n        if (notification.data.sellerId === userIdNumber) return 'seller';\n        return '';\n    };\n    // Helper function to generate consistent chat room ID for a trade\n    const generateChatRoomId = (buyerId, sellerId, perkId)=>{\n        return \"\".concat(buyerId, \"-\").concat(sellerId, \"-\").concat(perkId);\n    };\n    // Helper function to determine if notification should open chat modal\n    const shouldOpenChatModal = (notification)=>{\n        var _notification_data, _notification_data1;\n        const chatTypes = [\n            'perk_sold',\n            'perk_purchased',\n            'escrow_released',\n            'trade_completed',\n            'trade_disputed',\n            'dispute_resolved',\n            'trade_refunded',\n            'chat_message',\n            'escrow_created',\n            'escrow_pending_acceptance',\n            'escrow_accepted',\n            'escrow_release_reminder',\n            'trade_reported'\n        ];\n        return chatTypes.includes(notification.type) && (!!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) || !!((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId));\n    };\n    // Create release function for escrow - now works globally without route dependency\n    const createReleaseFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for release:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                var _trade_tokenDetails;\n                console.log('🔍 [NotificationBell] Starting global escrow release from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                console.log('🔍 [NotificationBell] Trade data for release:', {\n                    escrowId: trade.escrowId,\n                    tokenDetails: trade.tokenDetails,\n                    perkTokenMint: trade.perkTokenMint,\n                    to: trade.to,\n                    from: trade.from\n                });\n                // Execute escrow sell transaction using the hook\n                await escrowOperations.executeSell(trade.escrowId, 'So11111111111111111111111111111111111111112', ((_trade_tokenDetails = trade.tokenDetails) === null || _trade_tokenDetails === void 0 ? void 0 : _trade_tokenDetails.tokenAddress) || trade.perkTokenMint, trade.to, trade.from, solanaWallet, false // skipValidation\n                );\n                // Update backend with release\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.releasePerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    sellerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow release completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global release failed:', error);\n                // Show user-friendly error message\n                alert('Release failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create refund function for escrow - now works globally without route dependency\n    const createRefundFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for refund:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow refund from notification');\n                // Get trade details for escrow operation\n                const tradeResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trade/\").concat(notification.data.tradeId, \"/escrow-data\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (!tradeResponse.ok) {\n                    throw new Error('Failed to fetch trade details');\n                }\n                const tradeData = await tradeResponse.json();\n                const trade = tradeData.data;\n                // Execute escrow refund transaction using the hook\n                await escrowOperations.executeRefund(trade.escrowId, 'So11111111111111111111111111111111111111112', solanaWallet.address, solanaWallet);\n                // Update backend with refund\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.refundPerk)({\n                    tradeId: notification.data.tradeId.toString(),\n                    buyerWallet: solanaWallet.address\n                });\n                console.log('✅ [NotificationBell] Global escrow refund completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global refund failed:', error);\n                // Show user-friendly error message\n                alert('Refund failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Create accept function for escrow - now works globally without route dependency\n    const createAcceptFunction = (notification)=>{\n        return async ()=>{\n            var _notification_data;\n            if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) || !(solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address) || !isConnected) {\n                var _notification_data1;\n                console.error('❌ [NotificationBell] Missing data for accept:', {\n                    tradeId: (_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId,\n                    wallet: solanaWallet === null || solanaWallet === void 0 ? void 0 : solanaWallet.address,\n                    connected: isConnected\n                });\n                return;\n            }\n            try {\n                console.log('🔍 [NotificationBell] Starting global escrow accept from notification');\n                // Execute escrow accept transaction using the utility function with tradeId\n                // The function will automatically fetch trade details and calculate amounts\n                const { createEscrowAcceptTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\"));\n                const txId = await createEscrowAcceptTransaction(\"\", \"0\", \"0\", \"\", \"\", solanaWallet.address, solanaWallet, notification.data.tradeId // tradeId - this will trigger automatic calculation\n                );\n                // Update backend with acceptance\n                const acceptResponse = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/trades/\").concat(notification.data.tradeId, \"/accept\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    },\n                    body: JSON.stringify({\n                        txId,\n                        acceptedAt: new Date().toISOString()\n                    })\n                });\n                if (!acceptResponse.ok) {\n                    throw new Error('Failed to update backend with acceptance');\n                }\n                console.log('✅ [NotificationBell] Global escrow accept completed successfully!');\n                // Refresh notifications to show updated status\n                await loadNotifications();\n                // Show success message\n                alert('Escrow accepted successfully! The buyer can now release funds.');\n            } catch (error) {\n                console.error('❌ [NotificationBell] Global accept failed:', error);\n                // Show user-friendly error message\n                alert('Accept failed. Please try again or contact support.');\n            }\n        };\n    };\n    // Function to open chat modal\n    const openChatModal = async (notification)=>{\n        var _notification_data, _notification_data1, _notification_data2;\n        // We can open chat modal if we have either chatRoomId or tradeId\n        if (!((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId) && !((_notification_data1 = notification.data) === null || _notification_data1 === void 0 ? void 0 : _notification_data1.tradeId)) {\n            console.error('❌ [NotificationBell] No chatRoomId or tradeId in notification data');\n            return;\n        }\n        // Get current user id from localStorage\n        const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n        const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n        const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n        // Determine the other party (buyer or seller)\n        const userId = user === null || user === void 0 ? void 0 : user.id;\n        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;\n        let buyerId = notification.data.buyerId;\n        let sellerId = notification.data.sellerId;\n        // Special handling for escrow_release_reminder - seller is the current user\n        if (notification.type === 'escrow_release_reminder') {\n            sellerId = userIdNumber; // Current user is the seller\n            buyerId = notification.data.buyerId; // Buyer from notification data\n        } else if (!buyerId || !sellerId) {\n            if (notification.data.buyerId === userIdNumber) {\n                buyerId = userIdNumber;\n                sellerId = notification.data.sellerId || notification.data.receiverId;\n            } else {\n                sellerId = userIdNumber;\n                buyerId = notification.data.buyerId || notification.data.senderId;\n            }\n        }\n        // Generate consistent chat room ID if not provided\n        let chatRoomId = notification.data.chatRoomId;\n        if (!chatRoomId && buyerId && sellerId && ((_notification_data2 = notification.data) === null || _notification_data2 === void 0 ? void 0 : _notification_data2.perkId)) {\n            chatRoomId = generateChatRoomId(buyerId, sellerId, notification.data.perkId);\n            console.log('🔍 [NotificationBell] Generated chatRoomId from trade data:', chatRoomId);\n        }\n        if (!chatRoomId) {\n            console.error('❌ [NotificationBell] Could not determine chatRoomId');\n            return;\n        }\n        console.log('🔍 [NotificationBell] Opening chat modal with:', {\n            chatRoomId,\n            buyerId,\n            sellerId,\n            myUserId,\n            userIdNumber\n        });\n        // Debug notification data\n        console.log('🔍 [NotificationBell] Opening chat modal with data:', {\n            notificationType: notification.type,\n            notificationData: notification.data,\n            buyerId: buyerId || myUserId,\n            sellerId: sellerId || myUserId,\n            myUserId,\n            currentUser: user === null || user === void 0 ? void 0 : user.id,\n            tradeId: notification.data.tradeId\n        });\n        // Fetch real trade details if tradeId exists, or try to find it from chatRoomId\n        let activeTrade = undefined;\n        let tradeIdToUse = notification.data.tradeId;\n        // If no tradeId but we have chatRoomId, try to find the trade\n        if (!tradeIdToUse && notification.data.chatRoomId) {\n            try {\n                console.log('🔍 [NotificationBell] No tradeId found, searching by chatRoomId:', notification.data.chatRoomId);\n                // Extract perkId from chatRoomId format: buyerId-sellerId-perkId\n                const chatRoomParts = notification.data.chatRoomId.split('-');\n                if (chatRoomParts.length === 3) {\n                    const perkId = chatRoomParts[2];\n                    console.log('🔍 [NotificationBell] Extracted perkId from chatRoomId:', perkId);\n                    // Find active trade for this perk and users\n                    const response = await fetch(\"\".concat(_config_environment__WEBPACK_IMPORTED_MODULE_8__.API_CONFIG.BASE_URL, \"/perks/\").concat(perkId, \"/trades\"), {\n                        headers: {\n                            'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                        }\n                    });\n                    if (response.ok) {\n                        const tradesData = await response.json();\n                        if (tradesData.data && tradesData.data.length > 0) {\n                            // Find the most recent active trade\n                            const activeTrades = tradesData.data.filter((trade)=>[\n                                    'pending_acceptance',\n                                    'escrowed',\n                                    'completed',\n                                    'released'\n                                ].includes(trade.status));\n                            if (activeTrades.length > 0) {\n                                tradeIdToUse = activeTrades[0].id;\n                                console.log('✅ [NotificationBell] Found active trade from chatRoomId:', tradeIdToUse);\n                            }\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log('⚠️ [NotificationBell] Could not find trade from chatRoomId:', error);\n            }\n        }\n        if (tradeIdToUse) {\n            try {\n                console.log('🔍 [NotificationBell] Fetching trade details for tradeId:', tradeIdToUse);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeIdToUse));\n                if (tradeResponse.status === 200) {\n                    var _tradeResponse_data_perkDetails, _tradeResponse_data_dispute;\n                    activeTrade = {\n                        id: tradeResponse.data.id,\n                        status: tradeResponse.data.status,\n                        tradeId: tradeResponse.data.id,\n                        from: tradeResponse.data.userId,\n                        to: (_tradeResponse_data_perkDetails = tradeResponse.data.perkDetails) === null || _tradeResponse_data_perkDetails === void 0 ? void 0 : _tradeResponse_data_perkDetails.userId,\n                        createdAt: tradeResponse.data.createdAt,\n                        disputeStatus: ((_tradeResponse_data_dispute = tradeResponse.data.dispute) === null || _tradeResponse_data_dispute === void 0 ? void 0 : _tradeResponse_data_dispute.status) || 'none',\n                        escrowId: tradeResponse.data.escrowId,\n                        perkId: tradeResponse.data.perkId\n                    };\n                    console.log('✅ [NotificationBell] Trade details fetched:', {\n                        tradeId: activeTrade.id,\n                        status: activeTrade.status,\n                        buyer: activeTrade.from,\n                        seller: activeTrade.to\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [NotificationBell] Failed to fetch trade details:', error);\n                // Fallback to notification data\n                activeTrade = {\n                    id: tradeIdToUse,\n                    status: 'escrowed',\n                    tradeId: tradeIdToUse,\n                    from: buyerId,\n                    to: sellerId\n                };\n            }\n        }\n        // Use consistent modal ID based on chatRoomId to prevent multiple modals\n        const modalId = \"chat-modal-\".concat(chatRoomId);\n        // Check if modal is already open - if so, don't close it, just focus it\n        const existingModal = document.getElementById(modalId);\n        if (existingModal) {\n            console.log('✅ [NotificationBell] Chat modal already open, focusing existing modal');\n            existingModal.scrollIntoView({\n                behavior: 'smooth'\n            });\n            return;\n        }\n        // Open ChatModal in global modal\n        openModal({\n            id: modalId,\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_components_shared_chat_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                chatRoomId,\n                buyerId: buyerId || myUserId,\n                sellerId: sellerId || myUserId,\n                onClose: ()=>closeModal(modalId),\n                onRelease: createReleaseFunction(notification),\n                onRefund: createRefundFunction(notification),\n                onReport: ()=>{},\n                onAccept: createAcceptFunction(notification),\n                activeTrade\n            }),\n            closeOnBackdropClick: false,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-black/50 backdrop-blur-sm',\n            modalClassName: '',\n            disableScroll: true\n        });\n        // Close the notification dropdown\n        setIsOpen(false);\n    };\n    // Load notifications on mount and when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            loadNotifications();\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Refresh notifications every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const interval = setInterval(loadNotifications, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>clearInterval(interval)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        user\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationBell.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationBell.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationBell.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], []);\n    // Mark notification as read\n    const handleNotificationClick = async (notification)=>{\n        var _notification_data;\n        console.log('🔍 [NotificationBell] Notification clicked:', {\n            type: notification.type,\n            shouldOpenChat: shouldOpenChatModal(notification),\n            chatRoomId: (_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.chatRoomId,\n            actionUrl: notification.actionUrl\n        });\n        if (!notification.isRead) {\n            try {\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n                // Update the appropriate notification list\n                if (isTradeNotification(notification)) {\n                    setTradeNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setTradeUnreadCount((prev)=>Math.max(0, prev - 1));\n                } else {\n                    setSystemNotifications((prev)=>prev.map((n)=>n.id === notification.id ? {\n                                ...n,\n                                isRead: true\n                            } : n));\n                    setSystemUnreadCount((prev)=>Math.max(0, prev - 1));\n                }\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        // Check if this notification should open chat modal (now all trade notifications open chat modals)\n        if (shouldOpenChatModal(notification)) {\n            console.log('✅ [NotificationBell] Opening chat modal for notification');\n            openChatModal(notification);\n        } else if (notification.actionUrl) {\n            // Navigate to action URL only for system notifications (moderator dashboard, etc.)\n            console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);\n            window.location.href = notification.actionUrl;\n        } else {\n            // For notifications without actionUrl, just mark as read (already handled above)\n            console.log('✅ [NotificationBell] Notification marked as read');\n        }\n    };\n    // Mark all as read for current tab\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markAllNotificationsAsRead)();\n            // Update both notification lists\n            setSystemNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setTradeNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            // Reset unread counts\n            setSystemUnreadCount(0);\n            setTradeUnreadCount(0);\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Mark all as read for specific tab\n    const handleMarkTabAsRead = async (tabType)=>{\n        try {\n            // Note: This would ideally be a more specific API call\n            // For now, we'll mark individual notifications as read\n            const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;\n            const unreadNotifications = notifications.filter((n)=>!n.isRead);\n            for (const notification of unreadNotifications){\n                await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_2__.markNotificationAsRead)(notification.id);\n            }\n            // Update the appropriate list\n            if (tabType === 'system') {\n                setSystemNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setSystemUnreadCount(0);\n            } else {\n                setTradeNotifications((prev)=>prev.map((n)=>({\n                            ...n,\n                            isRead: true\n                        })));\n                setTradeUnreadCount(0);\n            }\n        } catch (error) {\n            console.error(\"Failed to mark \".concat(tabType, \" notifications as read:\"), error);\n        }\n    };\n    // Get notification icon based on type\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'dispute_created':\n            case 'dispute_assigned':\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 11\n                }, this);\n            case 'trade_completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 11\n                }, this);\n            case 'trade_refunded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 11\n                }, this);\n            case 'moderator_added':\n            case 'moderator_removed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 11\n                }, this);\n            case 'system_announcement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 11\n                }, this);\n            case 'perk_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-emerald-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 11\n                }, this);\n            case 'token_created':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-amber-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 704,\n                    columnNumber: 11\n                }, this);\n            case 'chat_message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_created':\n            case 'escrow_released':\n            case 'escrow_release_reminder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-orange-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_pending_acceptance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 730,\n                    columnNumber: 11\n                }, this);\n            case 'escrow_accepted':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_created':\n            case 'dispute_assigned':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 749,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 748,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 747,\n                    columnNumber: 11\n                }, this);\n            case 'dispute_resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 755,\n                    columnNumber: 11\n                }, this);\n            case 'perk_purchased':\n            case 'perk_sold':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-cyan-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 765,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 764,\n                    columnNumber: 11\n                }, this);\n            case 'trade_reported':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 773,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 772,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 782,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                    lineNumber: 780,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    // Format time ago\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    };\n    // Don't render if user is not authenticated\n    if (!user) return null;\n    const totalUnreadCount = systemUnreadCount + tradeUnreadCount;\n    const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;\n    const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeNotificationListener__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onNewNotification: loadNotifications\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 811,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 9\n                    }, this),\n                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: totalUnreadCount > 99 ? '99+' : totalUnreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 824,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 814,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                        children: \"Mark all read\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('system'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'system' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"System\",\n                                            systemUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: systemUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('trade'),\n                                        className: \"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(activeTab === 'trade' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                        children: [\n                                            \"Trades\",\n                                            tradeUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5\",\n                                                children: tradeUnreadCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 13\n                            }, this),\n                            currentUnreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMarkTabAsRead(activeTab),\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        \"Mark \",\n                                        activeTab,\n                                        \" as read\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2\",\n                                    children: \"Loading notifications...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 899,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 15\n                        }, this) : currentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"No \",\n                                        activeTab,\n                                        \" notifications yet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: activeTab === 'system' ? 'System announcements and updates will appear here' : 'Trade-related notifications will appear here'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 15\n                        }, this) : currentNotifications.map((notification)=>{\n                            var _notification_data;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: \"p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-blue-50' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        getNotificationIcon(notification.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                activeTab === 'trade' && getUserRoleInTrade(notification) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-0.5 rounded \".concat(getUserRoleInTrade(notification) === 'buyer' ? 'text-green-600 bg-green-100' : 'text-purple-600 bg-purple-100'),\n                                                                    children: getUserRoleInTrade(notification)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                activeTab === 'trade' && ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.tradeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded\",\n                                                                    children: [\n                                                                        \"Trade #\",\n                                                                        notification.data.tradeId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 956,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                            lineNumber: 943,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 19\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 11\n                    }, this),\n                    (systemNotifications.length > 0 || tradeNotifications.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsOpen(false);\n                            // Navigate to full notifications page if you have one\n                            // window.location.href = '/notifications';\n                            },\n                            className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                            children: [\n                                \"View all \",\n                                activeTab,\n                                \" notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n                lineNumber: 832,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\notifications\\\\NotificationBell.tsx\",\n        lineNumber: 809,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"546dyvfj4DYelV/Z+LobLfDeNOw=\", false, function() {\n    return [\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_9__.H,\n        _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_4__.useWallet,\n        _hooks_useEscrowOperations__WEBPACK_IMPORTED_MODULE_5__.useEscrowOperations\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYXJlZC9ub3RpZmljYXRpb25zL05vdGlmaWNhdGlvbkJlbGwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJEO0FBQ1g7QUFDNkc7QUFDOUY7QUFDakI7QUFDb0I7QUFDUDtBQUNlO0FBQ3hCO0FBc0JuQyxTQUFTaUI7O0lBQ3RCLE1BQU0sQ0FBQ0MscUJBQXFCQyx1QkFBdUIsR0FBR2xCLCtDQUFRQSxDQUFpQixFQUFFO0lBQ2pGLE1BQU0sQ0FBQ21CLG9CQUFvQkMsc0JBQXNCLEdBQUdwQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUMvRSxNQUFNLENBQUNxQixtQkFBbUJDLHFCQUFxQixHQUFHdEIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDdUIsa0JBQWtCQyxvQkFBb0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3lCLFdBQVdDLGFBQWEsR0FBRzFCLCtDQUFRQSxDQUFVO0lBQ3BELE1BQU0sQ0FBQzJCLFFBQVFDLFVBQVUsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQzZCLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0rQixjQUFjN0IsNkNBQU1BLENBQWlCO0lBQzNDLE1BQU0sRUFBRThCLElBQUksRUFBRSxHQUFHN0IsdURBQVFBO0lBQ3pCLE1BQU0sRUFBRThCLFNBQVMsRUFBRUMsVUFBVSxFQUFFLEdBQUd4Qiw0RUFBY0E7SUFDaEQsTUFBTSxFQUFFeUIsWUFBWSxFQUFFQyxXQUFXLEVBQUUsR0FBR3pCLDJEQUFTQTtJQUMvQyxNQUFNMEIsbUJBQW1CekIsK0VBQW1CQTtJQUU1QywrQ0FBK0M7SUFDL0MsTUFBTTBCLG9CQUFvQjtRQUN4QixJQUFJLENBQUNOLE1BQU07UUFFWCxJQUFJO1lBQ0ZGLFdBQVc7WUFDWCxNQUFNLENBQUNTLHVCQUF1QkMsZUFBZSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDaEV0QyxpRUFBZ0JBLENBQUM7b0JBQUV1QyxNQUFNO29CQUFHQyxVQUFVO2dCQUFHO2dCQUN6Q3ZDLDJFQUEwQkE7YUFDM0I7WUFFRCxJQUFJa0Msc0JBQXNCTSxNQUFNLEtBQUssS0FBSztnQkFDeEMsTUFBTUMsbUJBQW1CUCxzQkFBc0JRLElBQUksQ0FBQ0MsYUFBYTtnQkFFakUsMERBQTBEO2dCQUMxRCxNQUFNQyxhQUE2QixFQUFFO2dCQUNyQyxNQUFNQyxZQUE0QixFQUFFO2dCQUVwQ0osaUJBQWlCSyxPQUFPLENBQUMsQ0FBQ0M7b0JBQ3hCLElBQUlDLG9CQUFvQkQsZUFBZTt3QkFDckNGLFVBQVVJLElBQUksQ0FBQ0Y7b0JBQ2pCLE9BQU87d0JBQ0xILFdBQVdLLElBQUksQ0FBQ0Y7b0JBQ2xCO2dCQUNGO2dCQUVBRyxRQUFRQyxHQUFHLENBQUMsc0RBQXNEO29CQUNoRUMsb0JBQW9CWCxpQkFBaUJZLE1BQU07b0JBQzNDekMscUJBQXFCZ0MsV0FBV1MsTUFBTTtvQkFDdEN2QyxvQkFBb0IrQixVQUFVUSxNQUFNO29CQUNwQ0MsYUFBYVYsV0FBV1csR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO29CQUN2Q0MsWUFBWWIsVUFBVVUsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO2dCQUN2QztnQkFFQTVDLHVCQUF1QitCO2dCQUN2QjdCLHNCQUFzQjhCO2dCQUV0Qiw0Q0FBNEM7Z0JBQzVDLE1BQU1jLGVBQWVmLFdBQVdnQixNQUFNLENBQUNKLENBQUFBLElBQUssQ0FBQ0EsRUFBRUssTUFBTSxFQUFFUixNQUFNO2dCQUM3RCxNQUFNUyxjQUFjakIsVUFBVWUsTUFBTSxDQUFDSixDQUFBQSxJQUFLLENBQUNBLEVBQUVLLE1BQU0sRUFBRVIsTUFBTTtnQkFFM0RwQyxxQkFBcUIwQztnQkFDckJ4QyxvQkFBb0IyQztZQUN0QjtRQUVBLG1FQUFtRTtRQUNuRSxtRUFBbUU7UUFDckUsRUFBRSxPQUFPQyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pELFNBQVU7WUFDUnRDLFdBQVc7UUFDYjtJQUNGO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU11QixzQkFBc0IsQ0FBQ0Q7UUFDM0IsNERBQTREO1FBQzVELE1BQU1PLGNBQWM7WUFDbEI7WUFDQTtZQUNBO1lBQ0E7WUFDQSwrREFBK0Q7WUFDL0Q7WUFDQTtTQUNEO1FBRUQsdURBQXVEO1FBQ3ZELElBQUlBLFlBQVlVLFFBQVEsQ0FBQ2pCLGFBQWFVLElBQUksR0FBRztZQUMzQyxPQUFPO1FBQ1Q7UUFFQSxvRUFBb0U7UUFDcEUsTUFBTUMsYUFBYTtZQUNqQixpQ0FBaUM7WUFDakM7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0EsNkNBQTZDO1lBQzdDO1lBQ0EscUNBQXFDO1lBQ3JDO1NBQ0Q7UUFFRCxtQ0FBbUM7UUFDbkMsT0FBT0EsV0FBV00sUUFBUSxDQUFDakIsYUFBYVUsSUFBSTtJQUM5QztJQUVBLHFDQUFxQztJQUNyQyxNQUFNUSxxQkFBcUIsQ0FBQ2xCO1lBQ3JCQSxvQkFBK0JBO1FBQXBDLElBQUksR0FBQ0EscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUJtQixPQUFPLEtBQUksR0FBQ25CLHNCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSywwQ0FBQUEsb0JBQW1Cb0IsUUFBUSxHQUFFLE9BQU87UUFFeEUsTUFBTUMsU0FBU3pDLGlCQUFBQSwyQkFBQUEsS0FBTTBDLEVBQUU7UUFDdkIsTUFBTUMsZUFBZSxPQUFPRixXQUFXLFdBQVdHLFNBQVNILFVBQVVBO1FBRXJFLElBQUlyQixhQUFhTCxJQUFJLENBQUN3QixPQUFPLEtBQUtJLGNBQWMsT0FBTztRQUN2RCxJQUFJdkIsYUFBYUwsSUFBSSxDQUFDeUIsUUFBUSxLQUFLRyxjQUFjLE9BQU87UUFDeEQsT0FBTztJQUNUO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU1FLHFCQUFxQixDQUFDTixTQUFpQkMsVUFBa0JNO1FBQzdELE9BQU8sR0FBY04sT0FBWEQsU0FBUSxLQUFlTyxPQUFaTixVQUFTLEtBQVUsT0FBUE07SUFDbkM7SUFFQSxzRUFBc0U7SUFDdEUsTUFBTUMsc0JBQXNCLENBQUMzQjtZQU93QkEsb0JBQW1DQTtRQU50RixNQUFNNEIsWUFBWTtZQUNoQjtZQUFhO1lBQWtCO1lBQW1CO1lBQ2xEO1lBQWtCO1lBQW9CO1lBQWtCO1lBQ3hEO1lBQWtCO1lBQTZCO1lBQy9DO1lBQTJCO1NBQzVCO1FBQ0QsT0FBT0EsVUFBVVgsUUFBUSxDQUFDakIsYUFBYVUsSUFBSSxLQUFNLEVBQUMsR0FBQ1YscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI2QixVQUFVLEtBQUksQ0FBQyxHQUFDN0Isc0JBQUFBLGFBQWFMLElBQUksY0FBakJLLDBDQUFBQSxvQkFBbUI4QixPQUFPLENBQUQ7SUFDakg7SUFFQSxtRkFBbUY7SUFDbkYsTUFBTUMsd0JBQXdCLENBQUMvQjtRQUM3QixPQUFPO2dCQUNBQTtZQUFMLElBQUksR0FBQ0EscUJBQUFBLGFBQWFMLElBQUksY0FBakJLLHlDQUFBQSxtQkFBbUI4QixPQUFPLEtBQUksRUFBQy9DLHlCQUFBQSxtQ0FBQUEsYUFBY2lELE9BQU8sS0FBSSxDQUFDaEQsYUFBYTtvQkFFOURnQjtnQkFEWEcsUUFBUWEsS0FBSyxDQUFDLGtEQUFrRDtvQkFDOURjLE9BQU8sR0FBRTlCLHNCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSywwQ0FBQUEsb0JBQW1COEIsT0FBTztvQkFDbkNHLE1BQU0sRUFBRWxELHlCQUFBQSxtQ0FBQUEsYUFBY2lELE9BQU87b0JBQzdCRSxXQUFXbEQ7Z0JBQ2I7Z0JBQ0E7WUFDRjtZQUVBLElBQUk7b0JBNkJBbUQ7Z0JBNUJGaEMsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLHlDQUF5QztnQkFDekMsTUFBTWdDLGdCQUFnQixNQUFNQyxNQUFNLEdBQXNDckMsT0FBbkNyQywyREFBVUEsQ0FBQzJFLFFBQVEsRUFBQyxpQkFBeUMsT0FBMUJ0QyxhQUFhTCxJQUFJLENBQUNtQyxPQUFPLEVBQUMsaUJBQWU7b0JBQy9HUyxTQUFTO3dCQUNQLGlCQUFpQixVQUF3QyxPQUE5QkMsYUFBYUMsT0FBTyxDQUFDO29CQUNsRDtnQkFDRjtnQkFFQSxJQUFJLENBQUNMLGNBQWNNLEVBQUUsRUFBRTtvQkFDckIsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNQyxZQUFZLE1BQU1SLGNBQWNTLElBQUk7Z0JBQzFDLE1BQU1WLFFBQVFTLFVBQVVqRCxJQUFJO2dCQUU1QlEsUUFBUUMsR0FBRyxDQUFDLGlEQUFpRDtvQkFDM0QwQyxVQUFVWCxNQUFNVyxRQUFRO29CQUN4QkMsY0FBY1osTUFBTVksWUFBWTtvQkFDaENDLGVBQWViLE1BQU1hLGFBQWE7b0JBQ2xDQyxJQUFJZCxNQUFNYyxFQUFFO29CQUNaQyxNQUFNZixNQUFNZSxJQUFJO2dCQUNsQjtnQkFFQSxpREFBaUQ7Z0JBQ2pELE1BQU1qRSxpQkFBaUJrRSxXQUFXLENBQ2hDaEIsTUFBTVcsUUFBUSxFQUNkLCtDQUNBWCxFQUFBQSxzQkFBQUEsTUFBTVksWUFBWSxjQUFsQlosMENBQUFBLG9CQUFvQmlCLFlBQVksS0FBSWpCLE1BQU1hLGFBQWEsRUFDdkRiLE1BQU1jLEVBQUUsRUFDUmQsTUFBTWUsSUFBSSxFQUNWbkUsY0FDQSxNQUFNLGlCQUFpQjs7Z0JBR3pCLDhCQUE4QjtnQkFDOUIsTUFBTTNCLDREQUFXQSxDQUFDO29CQUNoQjBFLFNBQVM5QixhQUFhTCxJQUFJLENBQUNtQyxPQUFPLENBQUN1QixRQUFRO29CQUMzQ0MsY0FBY3ZFLGFBQWFpRCxPQUFPO2dCQUNwQztnQkFFQTdCLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiwrQ0FBK0M7Z0JBQy9DLE1BQU1sQjtZQUVSLEVBQUUsT0FBTzhCLE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQywrQ0FBK0NBO2dCQUM3RCxtQ0FBbUM7Z0JBQ25DdUMsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLGtGQUFrRjtJQUNsRixNQUFNQyx1QkFBdUIsQ0FBQ3hEO1FBQzVCLE9BQU87Z0JBQ0FBO1lBQUwsSUFBSSxHQUFDQSxxQkFBQUEsYUFBYUwsSUFBSSxjQUFqQksseUNBQUFBLG1CQUFtQjhCLE9BQU8sS0FBSSxFQUFDL0MseUJBQUFBLG1DQUFBQSxhQUFjaUQsT0FBTyxLQUFJLENBQUNoRCxhQUFhO29CQUU5RGdCO2dCQURYRyxRQUFRYSxLQUFLLENBQUMsaURBQWlEO29CQUM3RGMsT0FBTyxHQUFFOUIsc0JBQUFBLGFBQWFMLElBQUksY0FBakJLLDBDQUFBQSxvQkFBbUI4QixPQUFPO29CQUNuQ0csTUFBTSxFQUFFbEQseUJBQUFBLG1DQUFBQSxhQUFjaUQsT0FBTztvQkFDN0JFLFdBQVdsRDtnQkFDYjtnQkFDQTtZQUNGO1lBRUEsSUFBSTtnQkFDRm1CLFFBQVFDLEdBQUcsQ0FBQztnQkFFWix5Q0FBeUM7Z0JBQ3pDLE1BQU1nQyxnQkFBZ0IsTUFBTUMsTUFBTSxHQUFzQ3JDLE9BQW5DckMsMkRBQVVBLENBQUMyRSxRQUFRLEVBQUMsaUJBQXlDLE9BQTFCdEMsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTyxFQUFDLGlCQUFlO29CQUMvR1MsU0FBUzt3QkFDUCxpQkFBaUIsVUFBd0MsT0FBOUJDLGFBQWFDLE9BQU8sQ0FBQztvQkFDbEQ7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDTCxjQUFjTSxFQUFFLEVBQUU7b0JBQ3JCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTUMsWUFBWSxNQUFNUixjQUFjUyxJQUFJO2dCQUMxQyxNQUFNVixRQUFRUyxVQUFVakQsSUFBSTtnQkFFNUIsbURBQW1EO2dCQUNuRCxNQUFNVixpQkFBaUJ3RSxhQUFhLENBQ2xDdEIsTUFBTVcsUUFBUSxFQUNkLCtDQUNBL0QsYUFBYWlELE9BQU8sRUFDcEJqRDtnQkFHRiw2QkFBNkI7Z0JBQzdCLE1BQU0xQiwyREFBVUEsQ0FBQztvQkFDZnlFLFNBQVM5QixhQUFhTCxJQUFJLENBQUNtQyxPQUFPLENBQUN1QixRQUFRO29CQUMzQ0ssYUFBYTNFLGFBQWFpRCxPQUFPO2dCQUNuQztnQkFFQTdCLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiwrQ0FBK0M7Z0JBQy9DLE1BQU1sQjtZQUVSLEVBQUUsT0FBTzhCLE9BQU87Z0JBQ2RiLFFBQVFhLEtBQUssQ0FBQyw4Q0FBOENBO2dCQUM1RCxtQ0FBbUM7Z0JBQ25DdUMsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLGtGQUFrRjtJQUNsRixNQUFNSSx1QkFBdUIsQ0FBQzNEO1FBQzVCLE9BQU87Z0JBQ0FBO1lBQUwsSUFBSSxHQUFDQSxxQkFBQUEsYUFBYUwsSUFBSSxjQUFqQksseUNBQUFBLG1CQUFtQjhCLE9BQU8sS0FBSSxFQUFDL0MseUJBQUFBLG1DQUFBQSxhQUFjaUQsT0FBTyxLQUFJLENBQUNoRCxhQUFhO29CQUU5RGdCO2dCQURYRyxRQUFRYSxLQUFLLENBQUMsaURBQWlEO29CQUM3RGMsT0FBTyxHQUFFOUIsc0JBQUFBLGFBQWFMLElBQUksY0FBakJLLDBDQUFBQSxvQkFBbUI4QixPQUFPO29CQUNuQ0csTUFBTSxFQUFFbEQseUJBQUFBLG1DQUFBQSxhQUFjaUQsT0FBTztvQkFDN0JFLFdBQVdsRDtnQkFDYjtnQkFDQTtZQUNGO1lBRUEsSUFBSTtnQkFDRm1CLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiw0RUFBNEU7Z0JBQzVFLDRFQUE0RTtnQkFDNUUsTUFBTSxFQUFFd0QsNkJBQTZCLEVBQUUsR0FBRyxNQUFNLDhKQUErQjtnQkFDL0UsTUFBTUMsT0FBTyxNQUFNRCw4QkFDakIsSUFDQSxLQUNBLEtBQ0EsSUFDQSxJQUNBN0UsYUFBYWlELE9BQU8sRUFDcEJqRCxjQUNBaUIsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTyxDQUFDLG9EQUFvRDs7Z0JBR2hGLGlDQUFpQztnQkFDakMsTUFBTWdDLGlCQUFpQixNQUFNekIsTUFBTSxHQUF1Q3JDLE9BQXBDckMsMkRBQVVBLENBQUMyRSxRQUFRLEVBQUMsa0JBQTBDLE9BQTFCdEMsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTyxFQUFDLFlBQVU7b0JBQzVHaUMsUUFBUTtvQkFDUnhCLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQixpQkFBaUIsVUFBd0MsT0FBOUJDLGFBQWFDLE9BQU8sQ0FBQztvQkFDbEQ7b0JBQ0F1QixNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQ25CTDt3QkFDQU0sWUFBWSxJQUFJQyxPQUFPQyxXQUFXO29CQUNwQztnQkFDRjtnQkFFQSxJQUFJLENBQUNQLGVBQWVwQixFQUFFLEVBQUU7b0JBQ3RCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUF4QyxRQUFRQyxHQUFHLENBQUM7Z0JBRVosK0NBQStDO2dCQUMvQyxNQUFNbEI7Z0JBRU4sdUJBQXVCO2dCQUN2QnFFLE1BQU07WUFFUixFQUFFLE9BQU92QyxPQUFPO2dCQUNkYixRQUFRYSxLQUFLLENBQUMsOENBQThDQTtnQkFDNUQsbUNBQW1DO2dCQUNuQ3VDLE1BQU07WUFDUjtRQUNGO0lBQ0Y7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTWUsZ0JBQWdCLE9BQU90RTtZQUV0QkEsb0JBQWtDQSxxQkFtQ0dBO1FBcEMxQyxpRUFBaUU7UUFDakUsSUFBSSxHQUFDQSxxQkFBQUEsYUFBYUwsSUFBSSxjQUFqQksseUNBQUFBLG1CQUFtQjZCLFVBQVUsS0FBSSxHQUFDN0Isc0JBQUFBLGFBQWFMLElBQUksY0FBakJLLDBDQUFBQSxvQkFBbUI4QixPQUFPLEdBQUU7WUFDakUzQixRQUFRYSxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUEsd0NBQXdDO1FBQ3hDLE1BQU11RCxZQUFZLEtBQTZCLEdBQUcvQixhQUFhQyxPQUFPLENBQUMsWUFBWSxDQUFJO1FBQ3ZGLE1BQU0rQixTQUFTRCxZQUFZTixLQUFLUSxLQUFLLENBQUNGLGFBQWE7UUFDbkQsTUFBTUcsV0FBV0YsbUJBQUFBLDZCQUFBQSxPQUFRbEQsRUFBRTtRQUUzQiw4Q0FBOEM7UUFDOUMsTUFBTUQsU0FBU3pDLGlCQUFBQSwyQkFBQUEsS0FBTTBDLEVBQUU7UUFDdkIsTUFBTUMsZUFBZSxPQUFPRixXQUFXLFdBQVdHLFNBQVNILFVBQVVBO1FBRXJFLElBQUlGLFVBQVVuQixhQUFhTCxJQUFJLENBQUN3QixPQUFPO1FBQ3ZDLElBQUlDLFdBQVdwQixhQUFhTCxJQUFJLENBQUN5QixRQUFRO1FBRXpDLDRFQUE0RTtRQUM1RSxJQUFJcEIsYUFBYVUsSUFBSSxLQUFLLDJCQUEyQjtZQUNuRFUsV0FBV0csY0FBYyw2QkFBNkI7WUFDdERKLFVBQVVuQixhQUFhTCxJQUFJLENBQUN3QixPQUFPLEVBQUUsK0JBQStCO1FBQ3RFLE9BRUssSUFBSSxDQUFDQSxXQUFXLENBQUNDLFVBQVU7WUFDOUIsSUFBSXBCLGFBQWFMLElBQUksQ0FBQ3dCLE9BQU8sS0FBS0ksY0FBYztnQkFDOUNKLFVBQVVJO2dCQUNWSCxXQUFXcEIsYUFBYUwsSUFBSSxDQUFDeUIsUUFBUSxJQUFJcEIsYUFBYUwsSUFBSSxDQUFDZ0YsVUFBVTtZQUN2RSxPQUFPO2dCQUNMdkQsV0FBV0c7Z0JBQ1hKLFVBQVVuQixhQUFhTCxJQUFJLENBQUN3QixPQUFPLElBQUluQixhQUFhTCxJQUFJLENBQUNpRixRQUFRO1lBQ25FO1FBQ0Y7UUFFQSxtREFBbUQ7UUFDbkQsSUFBSS9DLGFBQWE3QixhQUFhTCxJQUFJLENBQUNrQyxVQUFVO1FBQzdDLElBQUksQ0FBQ0EsY0FBY1YsV0FBV0MsY0FBWXBCLHNCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSywwQ0FBQUEsb0JBQW1CMEIsTUFBTSxHQUFFO1lBQ25FRyxhQUFhSixtQkFBbUJOLFNBQVNDLFVBQVVwQixhQUFhTCxJQUFJLENBQUMrQixNQUFNO1lBQzNFdkIsUUFBUUMsR0FBRyxDQUFDLCtEQUErRHlCO1FBQzdFO1FBRUEsSUFBSSxDQUFDQSxZQUFZO1lBQ2YxQixRQUFRYSxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUFiLFFBQVFDLEdBQUcsQ0FBQyxrREFBa0Q7WUFDNUR5QjtZQUNBVjtZQUNBQztZQUNBc0Q7WUFDQW5EO1FBQ0Y7UUFFQSwwQkFBMEI7UUFDMUJwQixRQUFRQyxHQUFHLENBQUMsdURBQXVEO1lBQ2pFeUUsa0JBQWtCN0UsYUFBYVUsSUFBSTtZQUNuQ29FLGtCQUFrQjlFLGFBQWFMLElBQUk7WUFDbkN3QixTQUFTQSxXQUFXdUQ7WUFDcEJ0RCxVQUFVQSxZQUFZc0Q7WUFDdEJBO1lBQ0FLLFdBQVcsRUFBRW5HLGlCQUFBQSwyQkFBQUEsS0FBTTBDLEVBQUU7WUFDckJRLFNBQVM5QixhQUFhTCxJQUFJLENBQUNtQyxPQUFPO1FBQ3BDO1FBRUEsZ0ZBQWdGO1FBQ2hGLElBQUlrRCxjQUFjQztRQUNsQixJQUFJQyxlQUFlbEYsYUFBYUwsSUFBSSxDQUFDbUMsT0FBTztRQUU1Qyw4REFBOEQ7UUFDOUQsSUFBSSxDQUFDb0QsZ0JBQWdCbEYsYUFBYUwsSUFBSSxDQUFDa0MsVUFBVSxFQUFFO1lBQ2pELElBQUk7Z0JBQ0YxQixRQUFRQyxHQUFHLENBQUMsb0VBQW9FSixhQUFhTCxJQUFJLENBQUNrQyxVQUFVO2dCQUU1RyxpRUFBaUU7Z0JBQ2pFLE1BQU1zRCxnQkFBZ0JuRixhQUFhTCxJQUFJLENBQUNrQyxVQUFVLENBQUN1RCxLQUFLLENBQUM7Z0JBQ3pELElBQUlELGNBQWM3RSxNQUFNLEtBQUssR0FBRztvQkFDOUIsTUFBTW9CLFNBQVN5RCxhQUFhLENBQUMsRUFBRTtvQkFDL0JoRixRQUFRQyxHQUFHLENBQUMsMkRBQTJEc0I7b0JBRXZFLDRDQUE0QztvQkFDNUMsTUFBTTJELFdBQVcsTUFBTWhELE1BQU0sR0FBZ0NYLE9BQTdCL0QsMkRBQVVBLENBQUMyRSxRQUFRLEVBQUMsV0FBZ0IsT0FBUFosUUFBTyxZQUFVO3dCQUM1RWEsU0FBUzs0QkFDUCxpQkFBaUIsVUFBd0MsT0FBOUJDLGFBQWFDLE9BQU8sQ0FBQzt3QkFDbEQ7b0JBQ0Y7b0JBRUEsSUFBSTRDLFNBQVMzQyxFQUFFLEVBQUU7d0JBQ2YsTUFBTTRDLGFBQWEsTUFBTUQsU0FBU3hDLElBQUk7d0JBQ3RDLElBQUl5QyxXQUFXM0YsSUFBSSxJQUFJMkYsV0FBVzNGLElBQUksQ0FBQ1csTUFBTSxHQUFHLEdBQUc7NEJBQ2pELG9DQUFvQzs0QkFDcEMsTUFBTWlGLGVBQWVELFdBQVczRixJQUFJLENBQUNrQixNQUFNLENBQUMsQ0FBQ3NCLFFBQzNDO29DQUFDO29DQUFzQjtvQ0FBWTtvQ0FBYTtpQ0FBVyxDQUFDbEIsUUFBUSxDQUFDa0IsTUFBTTFDLE1BQU07NEJBR25GLElBQUk4RixhQUFhakYsTUFBTSxHQUFHLEdBQUc7Z0NBQzNCNEUsZUFBZUssWUFBWSxDQUFDLEVBQUUsQ0FBQ2pFLEVBQUU7Z0NBQ2pDbkIsUUFBUUMsR0FBRyxDQUFDLDREQUE0RDhFOzRCQUMxRTt3QkFDRjtvQkFDRjtnQkFDRjtZQUNGLEVBQUUsT0FBT2xFLE9BQU87Z0JBQ2RiLFFBQVFDLEdBQUcsQ0FBQywrREFBK0RZO1lBQzdFO1FBQ0Y7UUFFQSxJQUFJa0UsY0FBYztZQUNoQixJQUFJO2dCQUNGL0UsUUFBUUMsR0FBRyxDQUFDLDZEQUE2RDhFO2dCQUN6RSxNQUFNLEVBQUVNLGVBQWUsRUFBRSxHQUFHLE1BQU0sa0tBQWlDO2dCQUNuRSxNQUFNcEQsZ0JBQWdCLE1BQU1vRCxnQkFBZ0JDLE9BQU9QO2dCQUVuRCxJQUFJOUMsY0FBYzNDLE1BQU0sS0FBSyxLQUFLO3dCQU0xQjJDLGlDQUVXQTtvQkFQakI0QyxjQUFjO3dCQUNaMUQsSUFBSWMsY0FBY3pDLElBQUksQ0FBQzJCLEVBQUU7d0JBQ3pCN0IsUUFBUTJDLGNBQWN6QyxJQUFJLENBQUNGLE1BQU07d0JBQ2pDcUMsU0FBU00sY0FBY3pDLElBQUksQ0FBQzJCLEVBQUU7d0JBQzlCNEIsTUFBTWQsY0FBY3pDLElBQUksQ0FBQzBCLE1BQU07d0JBQy9CNEIsRUFBRSxHQUFFYixrQ0FBQUEsY0FBY3pDLElBQUksQ0FBQytGLFdBQVcsY0FBOUJ0RCxzREFBQUEsZ0NBQWdDZixNQUFNO3dCQUMxQ3NFLFdBQVd2RCxjQUFjekMsSUFBSSxDQUFDZ0csU0FBUzt3QkFDdkNDLGVBQWV4RCxFQUFBQSw4QkFBQUEsY0FBY3pDLElBQUksQ0FBQ2tHLE9BQU8sY0FBMUJ6RCxrREFBQUEsNEJBQTRCM0MsTUFBTSxLQUFJO3dCQUNyRHFELFVBQVVWLGNBQWN6QyxJQUFJLENBQUNtRCxRQUFRO3dCQUNyQ3BCLFFBQVFVLGNBQWN6QyxJQUFJLENBQUMrQixNQUFNO29CQUNuQztvQkFDQXZCLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0M7d0JBQ3pEMEIsU0FBU2tELFlBQVkxRCxFQUFFO3dCQUN2QjdCLFFBQVF1RixZQUFZdkYsTUFBTTt3QkFDMUJxRyxPQUFPZCxZQUFZOUIsSUFBSTt3QkFDdkI2QyxRQUFRZixZQUFZL0IsRUFBRTtvQkFDeEI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9qQyxPQUFPO2dCQUNkYixRQUFRYSxLQUFLLENBQUMsdURBQXVEQTtnQkFDckUsZ0NBQWdDO2dCQUNoQ2dFLGNBQWM7b0JBQ1oxRCxJQUFJNEQ7b0JBQ0p6RixRQUFRO29CQUNScUMsU0FBU29EO29CQUNUaEMsTUFBTS9CO29CQUNOOEIsSUFBSTdCO2dCQUNOO1lBQ0Y7UUFDRjtRQUVBLHlFQUF5RTtRQUN6RSxNQUFNNEUsVUFBVSxjQUF5QixPQUFYbkU7UUFFOUIsd0VBQXdFO1FBQ3hFLE1BQU1vRSxnQkFBZ0JDLFNBQVNDLGNBQWMsQ0FBQ0g7UUFDOUMsSUFBSUMsZUFBZTtZQUNqQjlGLFFBQVFDLEdBQUcsQ0FBQztZQUNaNkYsY0FBY0csY0FBYyxDQUFDO2dCQUFFQyxVQUFVO1lBQVM7WUFDbEQ7UUFDRjtRQUVBLGlDQUFpQztRQUNqQ3hILFVBQVU7WUFDUnlDLElBQUkwRTtZQUNKTSx5QkFBVzNKLDBEQUFtQixDQUFDYyx5RUFBU0EsRUFBRTtnQkFDeENvRTtnQkFDQVYsU0FBU0EsV0FBV3VEO2dCQUNwQnRELFVBQVVBLFlBQVlzRDtnQkFDdEI4QixTQUFTLElBQU0xSCxXQUFXa0g7Z0JBQzFCUyxXQUFXMUUsc0JBQXNCL0I7Z0JBQ2pDMEcsVUFBVWxELHFCQUFxQnhEO2dCQUMvQjJHLFVBQVUsS0FBTztnQkFDakJDLFVBQVVqRCxxQkFBcUIzRDtnQkFDL0JnRjtZQUNGO1lBQ0E2QixzQkFBc0I7WUFDdEJDLGVBQWU7WUFDZkMsY0FBYztZQUNkQyxRQUFRO1lBQ1JDLG1CQUFtQjtZQUNuQkMsZ0JBQWdCO1lBQ2hCQyxlQUFlO1FBQ2pCO1FBRUEsa0NBQWtDO1FBQ2xDM0ksVUFBVTtJQUNaO0lBRUEsb0RBQW9EO0lBQ3BEM0IsZ0RBQVNBO3NDQUFDO1lBQ1JxQztRQUNGO3FDQUFHO1FBQUNOO0tBQUs7SUFFVCx5Q0FBeUM7SUFDekMvQixnREFBU0E7c0NBQUM7WUFDUixNQUFNdUssV0FBV0MsWUFBWW5JLG1CQUFtQjtZQUNoRDs4Q0FBTyxJQUFNb0ksY0FBY0Y7O1FBQzdCO3FDQUFHO1FBQUN4STtLQUFLO0lBRVQsdUNBQXVDO0lBQ3ZDL0IsZ0RBQVNBO3NDQUFDO1lBQ1IsTUFBTTBLO2lFQUFxQixDQUFDQztvQkFDMUIsSUFBSTdJLFlBQVk4SSxPQUFPLElBQUksQ0FBQzlJLFlBQVk4SSxPQUFPLENBQUNDLFFBQVEsQ0FBQ0YsTUFBTUcsTUFBTSxHQUFXO3dCQUM5RW5KLFVBQVU7b0JBQ1o7Z0JBQ0Y7O1lBRUEwSCxTQUFTMEIsZ0JBQWdCLENBQUMsYUFBYUw7WUFDdkM7OENBQU8sSUFBTXJCLFNBQVMyQixtQkFBbUIsQ0FBQyxhQUFhTjs7UUFDekQ7cUNBQUcsRUFBRTtJQUVMLDRCQUE0QjtJQUM1QixNQUFNTywwQkFBMEIsT0FBTzlIO1lBSXZCQTtRQUhkRyxRQUFRQyxHQUFHLENBQUMsK0NBQStDO1lBQ3pETSxNQUFNVixhQUFhVSxJQUFJO1lBQ3ZCcUgsZ0JBQWdCcEcsb0JBQW9CM0I7WUFDcEM2QixVQUFVLEdBQUU3QixxQkFBQUEsYUFBYUwsSUFBSSxjQUFqQksseUNBQUFBLG1CQUFtQjZCLFVBQVU7WUFDekNtRyxXQUFXaEksYUFBYWdJLFNBQVM7UUFDbkM7UUFFQSxJQUFJLENBQUNoSSxhQUFhYyxNQUFNLEVBQUU7WUFDeEIsSUFBSTtnQkFDRixNQUFNNUQsdUVBQXNCQSxDQUFDOEMsYUFBYXNCLEVBQUU7Z0JBRTVDLDJDQUEyQztnQkFDM0MsSUFBSXJCLG9CQUFvQkQsZUFBZTtvQkFDckNoQyxzQkFBc0JpSyxDQUFBQSxPQUNwQkEsS0FBS3pILEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWEsRUFBRSxLQUFLdEIsYUFBYXNCLEVBQUUsR0FBRztnQ0FBRSxHQUFHYixDQUFDO2dDQUFFSyxRQUFROzRCQUFLLElBQUlMO29CQUVwRXJDLG9CQUFvQjZKLENBQUFBLE9BQVFDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRixPQUFPO2dCQUNqRCxPQUFPO29CQUNMbkssdUJBQXVCbUssQ0FBQUEsT0FDckJBLEtBQUt6SCxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVhLEVBQUUsS0FBS3RCLGFBQWFzQixFQUFFLEdBQUc7Z0NBQUUsR0FBR2IsQ0FBQztnQ0FBRUssUUFBUTs0QkFBSyxJQUFJTDtvQkFFcEV2QyxxQkFBcUIrSixDQUFBQSxPQUFRQyxLQUFLQyxHQUFHLENBQUMsR0FBR0YsT0FBTztnQkFDbEQ7WUFDRixFQUFFLE9BQU9qSCxPQUFPO2dCQUNkYixRQUFRYSxLQUFLLENBQUMsd0NBQXdDQTtZQUN4RDtRQUNGO1FBRUEsbUdBQW1HO1FBQ25HLElBQUlXLG9CQUFvQjNCLGVBQWU7WUFDckNHLFFBQVFDLEdBQUcsQ0FBQztZQUNaa0UsY0FBY3RFO1FBQ2hCLE9BQU8sSUFBSUEsYUFBYWdJLFNBQVMsRUFBRTtZQUNqQyxtRkFBbUY7WUFDbkY3SCxRQUFRQyxHQUFHLENBQUMsa0RBQWtESixhQUFhZ0ksU0FBUztZQUNwRkksT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUd0SSxhQUFhZ0ksU0FBUztRQUMvQyxPQUFPO1lBQ0wsaUZBQWlGO1lBQ2pGN0gsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLG1DQUFtQztJQUNuQyxNQUFNbUksc0JBQXNCO1FBQzFCLElBQUk7WUFDRixNQUFNcEwsMkVBQTBCQTtZQUVoQyxpQ0FBaUM7WUFDakNXLHVCQUF1Qm1LLENBQUFBLE9BQVFBLEtBQUt6SCxHQUFHLENBQUNDLENBQUFBLElBQU07d0JBQUUsR0FBR0EsQ0FBQzt3QkFBRUssUUFBUTtvQkFBSztZQUNuRTlDLHNCQUFzQmlLLENBQUFBLE9BQVFBLEtBQUt6SCxHQUFHLENBQUNDLENBQUFBLElBQU07d0JBQUUsR0FBR0EsQ0FBQzt3QkFBRUssUUFBUTtvQkFBSztZQUVsRSxzQkFBc0I7WUFDdEI1QyxxQkFBcUI7WUFDckJFLG9CQUFvQjtRQUN0QixFQUFFLE9BQU80QyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyw2Q0FBNkNBO1FBQzdEO0lBQ0Y7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTXdILHNCQUFzQixPQUFPQztRQUNqQyxJQUFJO1lBQ0YsdURBQXVEO1lBQ3ZELHVEQUF1RDtZQUN2RCxNQUFNN0ksZ0JBQWdCNkksWUFBWSxXQUFXNUssc0JBQXNCRTtZQUNuRSxNQUFNMkssc0JBQXNCOUksY0FBY2lCLE1BQU0sQ0FBQ0osQ0FBQUEsSUFBSyxDQUFDQSxFQUFFSyxNQUFNO1lBRS9ELEtBQUssTUFBTWQsZ0JBQWdCMEksb0JBQXFCO2dCQUM5QyxNQUFNeEwsdUVBQXNCQSxDQUFDOEMsYUFBYXNCLEVBQUU7WUFDOUM7WUFFQSw4QkFBOEI7WUFDOUIsSUFBSW1ILFlBQVksVUFBVTtnQkFDeEIzSyx1QkFBdUJtSyxDQUFBQSxPQUFRQSxLQUFLekgsR0FBRyxDQUFDQyxDQUFBQSxJQUFNOzRCQUFFLEdBQUdBLENBQUM7NEJBQUVLLFFBQVE7d0JBQUs7Z0JBQ25FNUMscUJBQXFCO1lBQ3ZCLE9BQU87Z0JBQ0xGLHNCQUFzQmlLLENBQUFBLE9BQVFBLEtBQUt6SCxHQUFHLENBQUNDLENBQUFBLElBQU07NEJBQUUsR0FBR0EsQ0FBQzs0QkFBRUssUUFBUTt3QkFBSztnQkFDbEUxQyxvQkFBb0I7WUFDdEI7UUFDRixFQUFFLE9BQU80QyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxrQkFBMEIsT0FBUnlILFNBQVEsNEJBQTBCekg7UUFDcEU7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNMkgsc0JBQXNCLENBQUNqSTtRQUMzQixPQUFRQTtZQUNOLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztnQkFDSCxxQkFDRSw4REFBQ2tJO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBMEJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF5QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDaEYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXVCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUM5RSw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQTBCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNqRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBMEJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pGLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUEyQkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDbEYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXlCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNoRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBd0JFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQy9FLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQTBCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNqRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBd0JFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQy9FLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0UsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUF5QkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDaEYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO1lBQ0wsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1Y7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO3dCQUEwQkUsTUFBSzt3QkFBT0MsUUFBTzt3QkFBZUMsU0FBUTtrQ0FDakYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztZQUk3RSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXlCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUNoRiw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7d0JBQXdCRSxNQUFLO3dCQUFPQyxRQUFPO3dCQUFlQyxTQUFRO2tDQUMvRSw0RUFBQ0M7NEJBQUtDLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTdFLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBdUJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQzlFLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJN0U7Z0JBQ0UscUJBQ0UsOERBQUNWO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBd0JFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQy9FLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJL0U7SUFDRjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNQyxnQkFBZ0IsQ0FBQ0M7UUFDckIsTUFBTUMsT0FBTyxJQUFJckYsS0FBS29GO1FBQ3RCLE1BQU1FLE1BQU0sSUFBSXRGO1FBQ2hCLE1BQU11RixnQkFBZ0J6QixLQUFLMEIsS0FBSyxDQUFDLENBQUNGLElBQUlHLE9BQU8sS0FBS0osS0FBS0ksT0FBTyxFQUFDLElBQUs7UUFFcEUsSUFBSUYsZ0JBQWdCLElBQUksT0FBTztRQUMvQixJQUFJQSxnQkFBZ0IsTUFBTSxPQUFPLEdBQWtDLE9BQS9CekIsS0FBSzBCLEtBQUssQ0FBQ0QsZ0JBQWdCLEtBQUk7UUFDbkUsSUFBSUEsZ0JBQWdCLE9BQU8sT0FBTyxHQUFvQyxPQUFqQ3pCLEtBQUswQixLQUFLLENBQUNELGdCQUFnQixPQUFNO1FBQ3RFLE9BQU8sR0FBcUMsT0FBbEN6QixLQUFLMEIsS0FBSyxDQUFDRCxnQkFBZ0IsUUFBTztJQUM5QztJQUVBLDRDQUE0QztJQUM1QyxJQUFJLENBQUMvSyxNQUFNLE9BQU87SUFFbEIsTUFBTWtMLG1CQUFtQjdMLG9CQUFvQkU7SUFDN0MsTUFBTTRMLHVCQUF1QjFMLGNBQWMsV0FBV1Isc0JBQXNCRTtJQUM1RSxNQUFNaU0scUJBQXFCM0wsY0FBYyxXQUFXSixvQkFBb0JFO0lBRXhFLHFCQUNFLDhEQUFDeUs7UUFBSUMsV0FBVTtRQUFXb0IsS0FBS3RMOzswQkFFN0IsOERBQUNqQixxRUFBNEJBO2dCQUFDd00sbUJBQW1CaEw7Ozs7OzswQkFHakQsOERBQUNpTDtnQkFDQ0MsU0FBUyxJQUFNNUwsVUFBVSxDQUFDRDtnQkFDMUJzSyxXQUFVOztrQ0FFViw4REFBQ0M7d0JBQUlELFdBQVU7d0JBQVVFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQ2pFLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29CQUl0RVEsbUJBQW1CLG1CQUNsQiw4REFBQ087d0JBQUt4QixXQUFVO2tDQUNiaUIsbUJBQW1CLEtBQUssUUFBUUE7Ozs7Ozs7Ozs7OztZQU10Q3ZMLHdCQUNDLDhEQUFDcUs7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3lCO3dDQUFHekIsV0FBVTtrREFBc0M7Ozs7OztvQ0FDbkRpQixtQkFBbUIsbUJBQ2xCLDhEQUFDSzt3Q0FDQ0MsU0FBUzdCO3dDQUNUTSxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7MENBT0wsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3NCO3dDQUNDQyxTQUFTLElBQU05TCxhQUFhO3dDQUM1QnVLLFdBQVcscUVBSVYsT0FIQ3hLLGNBQWMsV0FDVixxQ0FDQTs7NENBRVA7NENBRUVKLG9CQUFvQixtQkFDbkIsOERBQUNvTTtnREFBS3hCLFdBQVU7MERBQ2I1Szs7Ozs7Ozs7Ozs7O2tEQUlQLDhEQUFDa007d0NBQ0NDLFNBQVMsSUFBTTlMLGFBQWE7d0NBQzVCdUssV0FBVyxxRUFJVixPQUhDeEssY0FBYyxVQUNWLHFDQUNBOzs0Q0FFUDs0Q0FFRUYsbUJBQW1CLG1CQUNsQiw4REFBQ2tNO2dEQUFLeEIsV0FBVTswREFDYjFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT1I2TCxxQkFBcUIsbUJBQ3BCLDhEQUFDcEI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNzQjtvQ0FDQ0MsU0FBUyxJQUFNNUIsb0JBQW9Cbks7b0NBQ25Dd0ssV0FBVTs7d0NBQ1g7d0NBQ094Szt3Q0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU94Qiw4REFBQ3VLO3dCQUFJQyxXQUFVO2tDQUNacEssd0JBQ0MsOERBQUNtSzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUMwQjtvQ0FBRTFCLFdBQVU7OENBQU87Ozs7Ozs7Ozs7O21DQUVwQmtCLHFCQUFxQnpKLE1BQU0sS0FBSyxrQkFDbEMsOERBQUNzSTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVO29DQUF1Q0UsTUFBSztvQ0FBT0MsUUFBTztvQ0FBZUMsU0FBUTs4Q0FDOUYsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7OENBRXZFLDhEQUFDaUI7O3dDQUFFO3dDQUFJbE07d0NBQVU7Ozs7Ozs7OENBQ2pCLDhEQUFDa007b0NBQUUxQixXQUFVOzhDQUNWeEssY0FBYyxXQUNYLHNEQUNBOzs7Ozs7Ozs7OzttQ0FLUjBMLHFCQUFxQnZKLEdBQUcsQ0FBQyxDQUFDUjtnQ0F3Q1lBO2lEQXZDcEMsOERBQUM0STtnQ0FFQ3dCLFNBQVMsSUFBTXRDLHdCQUF3QjlIO2dDQUN2QzZJLFdBQVcsa0ZBRVYsT0FEQyxDQUFDN0ksYUFBYWMsTUFBTSxHQUFHLGVBQWU7MENBR3hDLDRFQUFDOEg7b0NBQUlDLFdBQVU7O3dDQUNaRixvQkFBb0IzSSxhQUFhVSxJQUFJO3NEQUN0Qyw4REFBQ2tJOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDMEI7NERBQUUxQixXQUFXLHVCQUViLE9BREMsQ0FBQzdJLGFBQWFjLE1BQU0sR0FBRyxrQkFBa0I7c0VBRXhDZCxhQUFhd0ssS0FBSzs7Ozs7O3dEQUVwQixDQUFDeEssYUFBYWMsTUFBTSxrQkFDbkIsOERBQUM4SDs0REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUduQiw4REFBQzBCO29EQUFFMUIsV0FBVTs4REFDVjdJLGFBQWF5SyxPQUFPOzs7Ozs7OERBRXZCLDhEQUFDN0I7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDMEI7NERBQUUxQixXQUFVO3NFQUNWVSxjQUFjdkosYUFBYTJGLFNBQVM7Ozs7OztzRUFFdkMsOERBQUNpRDs0REFBSUMsV0FBVTs7Z0VBRVp4SyxjQUFjLFdBQVc2QyxtQkFBbUJsQiwrQkFDM0MsOERBQUNxSztvRUFBS3hCLFdBQVcsK0JBSWhCLE9BSEMzSCxtQkFBbUJsQixrQkFBa0IsVUFDakMsZ0NBQ0E7OEVBRUhrQixtQkFBbUJsQjs7Ozs7O2dFQUl2QjNCLGNBQWMsYUFBVzJCLHFCQUFBQSxhQUFhTCxJQUFJLGNBQWpCSyx5Q0FBQUEsbUJBQW1COEIsT0FBTyxtQkFDbEQsOERBQUN1STtvRUFBS3hCLFdBQVU7O3dFQUF3RDt3RUFDOUQ3SSxhQUFhTCxJQUFJLENBQUNtQyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQXhDeEM5QixhQUFhc0IsRUFBRTs7Ozs7Ozs7Ozs7b0JBcUQxQnpELENBQUFBLG9CQUFvQnlDLE1BQU0sR0FBRyxLQUFLdkMsbUJBQW1CdUMsTUFBTSxHQUFHLG9CQUM5RCw4REFBQ3NJO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDc0I7NEJBQ0NDLFNBQVM7Z0NBQ1A1TCxVQUFVOzRCQUNWLHNEQUFzRDs0QkFDdEQsMkNBQTJDOzRCQUM3Qzs0QkFDQXFLLFdBQVU7O2dDQUNYO2dDQUNXeEs7Z0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFwQztHQTM3QndCVDs7UUFTTGIsbURBQVFBO1FBQ1NPLHdFQUFjQTtRQUNWQyx1REFBU0E7UUFDdEJDLDJFQUFtQkE7OztLQVp0QkkiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWxyYWhtYVxcRGVza3RvcFxcQWhtZWQgQmFyYWthdFxcZnVuSGktcHJvamVjdFxcZnVuSGktcHJvamVjdFxcZnJvbnQtZW5kXFxzcmNcXGNvbXBvbmVudHNcXHNoYXJlZFxcbm90aWZpY2F0aW9uc1xcTm90aWZpY2F0aW9uQmVsbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQcml2eSB9IGZyb20gXCJAcHJpdnktaW8vcmVhY3QtYXV0aFwiO1xuaW1wb3J0IHsgZ2V0Tm90aWZpY2F0aW9ucywgZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQsIG1hcmtOb3RpZmljYXRpb25Bc1JlYWQsIG1hcmtBbGxOb3RpZmljYXRpb25zQXNSZWFkLCByZWxlYXNlUGVyaywgcmVmdW5kUGVyayB9IGZyb20gJ0AvYXhpb3MvcmVxdWVzdHMnO1xuaW1wb3J0IHsgdXNlR2xvYmFsTW9kYWwgfSBmcm9tICdAL2NvbnRleHRzL0dsb2JhbE1vZGFsQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VXYWxsZXQgfSBmcm9tICdAL2hvb2tzL3VzZVdhbGxldCc7XG5pbXBvcnQgeyB1c2VFc2Nyb3dPcGVyYXRpb25zIH0gZnJvbSAnQC9ob29rcy91c2VFc2Nyb3dPcGVyYXRpb25zJztcbmltcG9ydCBDaGF0TW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL3NoYXJlZC9jaGF0L0NoYXRNb2RhbCc7XG5pbXBvcnQgUmVhbFRpbWVOb3RpZmljYXRpb25MaXN0ZW5lciBmcm9tICcuL1JlYWxUaW1lTm90aWZpY2F0aW9uTGlzdGVuZXInO1xuaW1wb3J0IHsgQVBJX0NPTkZJRyB9IGZyb20gJ0AvY29uZmlnL2Vudmlyb25tZW50JztcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBudW1iZXI7XG4gIHR5cGU6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBpc1JlYWQ6IGJvb2xlYW47XG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ3VyZ2VudCc7XG4gIGFjdGlvblVybD86IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGRhdGE/OiB7XG4gICAgdHJhZGVJZD86IG51bWJlcjtcbiAgICBkaXNwdXRlSWQ/OiBudW1iZXI7XG4gICAgYnV5ZXJJZD86IG51bWJlcjtcbiAgICBzZWxsZXJJZD86IG51bWJlcjtcbiAgICBba2V5OiBzdHJpbmddOiBhbnk7XG4gIH07XG59XG5cbnR5cGUgVGFiVHlwZSA9ICdzeXN0ZW0nIHwgJ3RyYWRlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90aWZpY2F0aW9uQmVsbCgpIHtcbiAgY29uc3QgW3N5c3RlbU5vdGlmaWNhdGlvbnMsIHNldFN5c3RlbU5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8Tm90aWZpY2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW3RyYWRlTm90aWZpY2F0aW9ucywgc2V0VHJhZGVOb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXSk7XG4gIGNvbnN0IFtzeXN0ZW1VbnJlYWRDb3VudCwgc2V0U3lzdGVtVW5yZWFkQ291bnRdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFt0cmFkZVVucmVhZENvdW50LCBzZXRUcmFkZVVucmVhZENvdW50XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8VGFiVHlwZT4oJ3N5c3RlbScpO1xuICBjb25zdCBbaXNPcGVuLCBzZXRJc09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGRyb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VQcml2eSgpO1xuICBjb25zdCB7IG9wZW5Nb2RhbCwgY2xvc2VNb2RhbCB9ID0gdXNlR2xvYmFsTW9kYWwoKTtcbiAgY29uc3QgeyBzb2xhbmFXYWxsZXQsIGlzQ29ubmVjdGVkIH0gPSB1c2VXYWxsZXQoKTtcbiAgY29uc3QgZXNjcm93T3BlcmF0aW9ucyA9IHVzZUVzY3Jvd09wZXJhdGlvbnMoKTtcblxuICAvLyBMb2FkIG5vdGlmaWNhdGlvbnMgYW5kIHNlcGFyYXRlIHRoZW0gYnkgdHlwZVxuICBjb25zdCBsb2FkTm90aWZpY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgW25vdGlmaWNhdGlvbnNSZXNwb25zZSwgdW5yZWFkUmVzcG9uc2VdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBnZXROb3RpZmljYXRpb25zKHsgcGFnZTogMSwgcGFnZVNpemU6IDIwIH0pLFxuICAgICAgICBnZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCgpXG4gICAgICBdKTtcblxuICAgICAgaWYgKG5vdGlmaWNhdGlvbnNSZXNwb25zZS5zdGF0dXMgPT09IDIwMCkge1xuICAgICAgICBjb25zdCBhbGxOb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9uc1Jlc3BvbnNlLmRhdGEubm90aWZpY2F0aW9ucztcblxuICAgICAgICAvLyBTZXBhcmF0ZSBub3RpZmljYXRpb25zIGludG8gc3lzdGVtIGFuZCB0cmFkZSBjYXRlZ29yaWVzXG4gICAgICAgIGNvbnN0IHN5c3RlbU5vdHM6IE5vdGlmaWNhdGlvbltdID0gW107XG4gICAgICAgIGNvbnN0IHRyYWRlTm90czogTm90aWZpY2F0aW9uW10gPSBbXTtcblxuICAgICAgICBhbGxOb3RpZmljYXRpb25zLmZvckVhY2goKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgICAgICAgaWYgKGlzVHJhZGVOb3RpZmljYXRpb24obm90aWZpY2F0aW9uKSkge1xuICAgICAgICAgICAgdHJhZGVOb3RzLnB1c2gobm90aWZpY2F0aW9uKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgc3lzdGVtTm90cy5wdXNoKG5vdGlmaWNhdGlvbik7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiBbTm90aWZpY2F0aW9uQmVsbF0gTm90aWZpY2F0aW9uIGNhdGVnb3JpemF0aW9uOicsIHtcbiAgICAgICAgICB0b3RhbE5vdGlmaWNhdGlvbnM6IGFsbE5vdGlmaWNhdGlvbnMubGVuZ3RoLFxuICAgICAgICAgIHN5c3RlbU5vdGlmaWNhdGlvbnM6IHN5c3RlbU5vdHMubGVuZ3RoLFxuICAgICAgICAgIHRyYWRlTm90aWZpY2F0aW9uczogdHJhZGVOb3RzLmxlbmd0aCxcbiAgICAgICAgICBzeXN0ZW1UeXBlczogc3lzdGVtTm90cy5tYXAobiA9PiBuLnR5cGUpLFxuICAgICAgICAgIHRyYWRlVHlwZXM6IHRyYWRlTm90cy5tYXAobiA9PiBuLnR5cGUpXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHNldFN5c3RlbU5vdGlmaWNhdGlvbnMoc3lzdGVtTm90cyk7XG4gICAgICAgIHNldFRyYWRlTm90aWZpY2F0aW9ucyh0cmFkZU5vdHMpO1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSB1bnJlYWQgY291bnRzIGZvciBlYWNoIGNhdGVnb3J5XG4gICAgICAgIGNvbnN0IHN5c3RlbVVucmVhZCA9IHN5c3RlbU5vdHMuZmlsdGVyKG4gPT4gIW4uaXNSZWFkKS5sZW5ndGg7XG4gICAgICAgIGNvbnN0IHRyYWRlVW5yZWFkID0gdHJhZGVOb3RzLmZpbHRlcihuID0+ICFuLmlzUmVhZCkubGVuZ3RoO1xuXG4gICAgICAgIHNldFN5c3RlbVVucmVhZENvdW50KHN5c3RlbVVucmVhZCk7XG4gICAgICAgIHNldFRyYWRlVW5yZWFkQ291bnQodHJhZGVVbnJlYWQpO1xuICAgICAgfVxuXG4gICAgICAvLyBOb3RlOiBXZSBjYWxjdWxhdGUgdW5yZWFkIGNvdW50cyBmcm9tIHRoZSBmaWx0ZXJlZCBub3RpZmljYXRpb25zXG4gICAgICAvLyByYXRoZXIgdGhhbiB1c2luZyB0aGUgQVBJIHJlc3BvbnNlIHNpbmNlIHdlIG5lZWQgc2VwYXJhdGUgY291bnRzXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSBpZiBhIG5vdGlmaWNhdGlvbiBpcyB0cmFkZS1yZWxhdGVkXG4gIGNvbnN0IGlzVHJhZGVOb3RpZmljYXRpb24gPSAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24pOiBib29sZWFuID0+IHtcbiAgICAvLyBTeXN0ZW0gbm90aWZpY2F0aW9ucyAobm9uLXRyYWRlKSAtIHRoZXNlIGdvIHRvIFN5c3RlbSB0YWJcbiAgICBjb25zdCBzeXN0ZW1UeXBlcyA9IFtcbiAgICAgICdzeXN0ZW1fYW5ub3VuY2VtZW50JyxcbiAgICAgICdtb2RlcmF0b3JfYWRkZWQnLFxuICAgICAgJ3BlcmtfY3JlYXRlZCcsXG4gICAgICAndG9rZW5fY3JlYXRlZCcsXG4gICAgICAvLyBNb2RlcmF0b3Itc3BlY2lmaWMgbm90aWZpY2F0aW9ucyAoZ28gdG8gbW9kZXJhdG9yIGRhc2hib2FyZClcbiAgICAgICdkaXNwdXRlX2NyZWF0ZWQnLFxuICAgICAgJ2Rpc3B1dGVfYXNzaWduZWQnXG4gICAgXTtcblxuICAgIC8vIElmIGl0J3MgYSBzeXN0ZW0gdHlwZSwgaXQncyBOT1QgYSB0cmFkZSBub3RpZmljYXRpb25cbiAgICBpZiAoc3lzdGVtVHlwZXMuaW5jbHVkZXMobm90aWZpY2F0aW9uLnR5cGUpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLy8gQWxsIGVzY3JvdywgdHJhZGUsIGFuZCB1c2VyIGRpc3B1dGUgbm90aWZpY2F0aW9ucyBnbyB0byBUcmFkZSB0YWJcbiAgICBjb25zdCB0cmFkZVR5cGVzID0gW1xuICAgICAgLy8gVHJhZGUgYW5kIGVzY3JvdyBub3RpZmljYXRpb25zXG4gICAgICAncGVya19wdXJjaGFzZWQnLFxuICAgICAgJ3Blcmtfc29sZCcsXG4gICAgICAnZXNjcm93X2NyZWF0ZWQnLFxuICAgICAgJ2VzY3Jvd19yZWxlYXNlZCcsXG4gICAgICAnZXNjcm93X3JlbGVhc2VfcmVtaW5kZXInLFxuICAgICAgJ3RyYWRlX2NvbXBsZXRlZCcsXG4gICAgICAndHJhZGVfcmVmdW5kZWQnLFxuICAgICAgJ3RyYWRlX3JlcG9ydGVkJyxcbiAgICAgICd0cmFkZV9kaXNwdXRlZCcsXG4gICAgICAvLyBVc2VyIGRpc3B1dGUgbm90aWZpY2F0aW9ucyAodHJhZGUtcmVsYXRlZClcbiAgICAgICdkaXNwdXRlX3Jlc29sdmVkJyxcbiAgICAgIC8vIENoYXQgbm90aWZpY2F0aW9ucyAodHJhZGUtcmVsYXRlZClcbiAgICAgICdjaGF0X21lc3NhZ2UnXG4gICAgXTtcblxuICAgIC8vIFJldHVybiB0cnVlIGlmIGl0J3MgYSB0cmFkZSB0eXBlXG4gICAgcmV0dXJuIHRyYWRlVHlwZXMuaW5jbHVkZXMobm90aWZpY2F0aW9uLnR5cGUpO1xuICB9O1xuXG4gIC8vIEdldCB1c2VyIHJvbGUgaW4gdHJhZGUgZm9yIGRpc3BsYXlcbiAgY29uc3QgZ2V0VXNlclJvbGVJblRyYWRlID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIW5vdGlmaWNhdGlvbi5kYXRhPy5idXllcklkIHx8ICFub3RpZmljYXRpb24uZGF0YT8uc2VsbGVySWQpIHJldHVybiAnJztcblxuICAgIGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkO1xuICAgIGNvbnN0IHVzZXJJZE51bWJlciA9IHR5cGVvZiB1c2VySWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQodXNlcklkKSA6IHVzZXJJZDtcblxuICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5idXllcklkID09PSB1c2VySWROdW1iZXIpIHJldHVybiAnYnV5ZXInO1xuICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZCA9PT0gdXNlcklkTnVtYmVyKSByZXR1cm4gJ3NlbGxlcic7XG4gICAgcmV0dXJuICcnO1xuICB9O1xuXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZW5lcmF0ZSBjb25zaXN0ZW50IGNoYXQgcm9vbSBJRCBmb3IgYSB0cmFkZVxuICBjb25zdCBnZW5lcmF0ZUNoYXRSb29tSWQgPSAoYnV5ZXJJZDogbnVtYmVyLCBzZWxsZXJJZDogbnVtYmVyLCBwZXJrSWQ6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gICAgcmV0dXJuIGAke2J1eWVySWR9LSR7c2VsbGVySWR9LSR7cGVya0lkfWA7XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSBpZiBub3RpZmljYXRpb24gc2hvdWxkIG9wZW4gY2hhdCBtb2RhbFxuICBjb25zdCBzaG91bGRPcGVuQ2hhdE1vZGFsID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgY2hhdFR5cGVzID0gW1xuICAgICAgJ3Blcmtfc29sZCcsICdwZXJrX3B1cmNoYXNlZCcsICdlc2Nyb3dfcmVsZWFzZWQnLCAndHJhZGVfY29tcGxldGVkJyxcbiAgICAgICd0cmFkZV9kaXNwdXRlZCcsICdkaXNwdXRlX3Jlc29sdmVkJywgJ3RyYWRlX3JlZnVuZGVkJywgJ2NoYXRfbWVzc2FnZScsXG4gICAgICAnZXNjcm93X2NyZWF0ZWQnLCAnZXNjcm93X3BlbmRpbmdfYWNjZXB0YW5jZScsICdlc2Nyb3dfYWNjZXB0ZWQnLFxuICAgICAgJ2VzY3Jvd19yZWxlYXNlX3JlbWluZGVyJywgJ3RyYWRlX3JlcG9ydGVkJ1xuICAgIF07XG4gICAgcmV0dXJuIGNoYXRUeXBlcy5pbmNsdWRlcyhub3RpZmljYXRpb24udHlwZSkgJiYgKCEhbm90aWZpY2F0aW9uLmRhdGE/LmNoYXRSb29tSWQgfHwgISFub3RpZmljYXRpb24uZGF0YT8udHJhZGVJZCk7XG4gIH07XG5cbiAgLy8gQ3JlYXRlIHJlbGVhc2UgZnVuY3Rpb24gZm9yIGVzY3JvdyAtIG5vdyB3b3JrcyBnbG9iYWxseSB3aXRob3V0IHJvdXRlIGRlcGVuZGVuY3lcbiAgY29uc3QgY3JlYXRlUmVsZWFzZUZ1bmN0aW9uID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jICgpID0+IHtcbiAgICAgIGlmICghbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQgfHwgIXNvbGFuYVdhbGxldD8uYWRkcmVzcyB8fCAhaXNDb25uZWN0ZWQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBNaXNzaW5nIGRhdGEgZm9yIHJlbGVhc2U6Jywge1xuICAgICAgICAgIHRyYWRlSWQ6IG5vdGlmaWNhdGlvbi5kYXRhPy50cmFkZUlkLFxuICAgICAgICAgIHdhbGxldDogc29sYW5hV2FsbGV0Py5hZGRyZXNzLFxuICAgICAgICAgIGNvbm5lY3RlZDogaXNDb25uZWN0ZWRcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIFN0YXJ0aW5nIGdsb2JhbCBlc2Nyb3cgcmVsZWFzZSBmcm9tIG5vdGlmaWNhdGlvbicpO1xuXG4gICAgICAgIC8vIEdldCB0cmFkZSBkZXRhaWxzIGZvciBlc2Nyb3cgb3BlcmF0aW9uXG4gICAgICAgIGNvbnN0IHRyYWRlUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQ09ORklHLkJBU0VfVVJMfS9wZXJrcy90cmFkZS8ke25vdGlmaWNhdGlvbi5kYXRhLnRyYWRlSWR9L2VzY3Jvdy1kYXRhYCwge1xuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpfWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghdHJhZGVSZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHRyYWRlIGRldGFpbHMnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHRyYWRlRGF0YSA9IGF3YWl0IHRyYWRlUmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCB0cmFkZSA9IHRyYWRlRGF0YS5kYXRhO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBUcmFkZSBkYXRhIGZvciByZWxlYXNlOicsIHtcbiAgICAgICAgICBlc2Nyb3dJZDogdHJhZGUuZXNjcm93SWQsXG4gICAgICAgICAgdG9rZW5EZXRhaWxzOiB0cmFkZS50b2tlbkRldGFpbHMsXG4gICAgICAgICAgcGVya1Rva2VuTWludDogdHJhZGUucGVya1Rva2VuTWludCxcbiAgICAgICAgICB0bzogdHJhZGUudG8sXG4gICAgICAgICAgZnJvbTogdHJhZGUuZnJvbVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBFeGVjdXRlIGVzY3JvdyBzZWxsIHRyYW5zYWN0aW9uIHVzaW5nIHRoZSBob29rXG4gICAgICAgIGF3YWl0IGVzY3Jvd09wZXJhdGlvbnMuZXhlY3V0ZVNlbGwoXG4gICAgICAgICAgdHJhZGUuZXNjcm93SWQsXG4gICAgICAgICAgJ1NvMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTInLCAvLyBTT0wgbWludFxuICAgICAgICAgIHRyYWRlLnRva2VuRGV0YWlscz8udG9rZW5BZGRyZXNzIHx8IHRyYWRlLnBlcmtUb2tlbk1pbnQsIC8vIFBlcmsgdG9rZW4gbWludFxuICAgICAgICAgIHRyYWRlLnRvLCAvLyBTZWxsZXIgd2FsbGV0IChjdXJyZW50IHVzZXIpXG4gICAgICAgICAgdHJhZGUuZnJvbSwgLy8gQnV5ZXIgd2FsbGV0XG4gICAgICAgICAgc29sYW5hV2FsbGV0LFxuICAgICAgICAgIGZhbHNlIC8vIHNraXBWYWxpZGF0aW9uXG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGJhY2tlbmQgd2l0aCByZWxlYXNlXG4gICAgICAgIGF3YWl0IHJlbGVhc2VQZXJrKHtcbiAgICAgICAgICB0cmFkZUlkOiBub3RpZmljYXRpb24uZGF0YS50cmFkZUlkLnRvU3RyaW5nKCksXG4gICAgICAgICAgc2VsbGVyV2FsbGV0OiBzb2xhbmFXYWxsZXQuYWRkcmVzc1xuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFtOb3RpZmljYXRpb25CZWxsXSBHbG9iYWwgZXNjcm93IHJlbGVhc2UgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKTtcblxuICAgICAgICAvLyBSZWZyZXNoIG5vdGlmaWNhdGlvbnMgdG8gc2hvdyB1cGRhdGVkIHN0YXR1c1xuICAgICAgICBhd2FpdCBsb2FkTm90aWZpY2F0aW9ucygpO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIEdsb2JhbCByZWxlYXNlIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICAgIC8vIFNob3cgdXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlXG4gICAgICAgIGFsZXJ0KCdSZWxlYXNlIGZhaWxlZC4gUGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IHN1cHBvcnQuJyk7XG4gICAgICB9XG4gICAgfTtcbiAgfTtcblxuICAvLyBDcmVhdGUgcmVmdW5kIGZ1bmN0aW9uIGZvciBlc2Nyb3cgLSBub3cgd29ya3MgZ2xvYmFsbHkgd2l0aG91dCByb3V0ZSBkZXBlbmRlbmN5XG4gIGNvbnN0IGNyZWF0ZVJlZnVuZEZ1bmN0aW9uID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jICgpID0+IHtcbiAgICAgIGlmICghbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQgfHwgIXNvbGFuYVdhbGxldD8uYWRkcmVzcyB8fCAhaXNDb25uZWN0ZWQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBNaXNzaW5nIGRhdGEgZm9yIHJlZnVuZDonLCB7XG4gICAgICAgICAgdHJhZGVJZDogbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQsXG4gICAgICAgICAgd2FsbGV0OiBzb2xhbmFXYWxsZXQ/LmFkZHJlc3MsXG4gICAgICAgICAgY29ubmVjdGVkOiBpc0Nvbm5lY3RlZFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gU3RhcnRpbmcgZ2xvYmFsIGVzY3JvdyByZWZ1bmQgZnJvbSBub3RpZmljYXRpb24nKTtcblxuICAgICAgICAvLyBHZXQgdHJhZGUgZGV0YWlscyBmb3IgZXNjcm93IG9wZXJhdGlvblxuICAgICAgICBjb25zdCB0cmFkZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0NPTkZJRy5CQVNFX1VSTH0vcGVya3MvdHJhZGUvJHtub3RpZmljYXRpb24uZGF0YS50cmFkZUlkfS9lc2Nyb3ctZGF0YWAsIHtcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKX1gXG4gICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoIXRyYWRlUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCB0cmFkZSBkZXRhaWxzJyk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB0cmFkZURhdGEgPSBhd2FpdCB0cmFkZVJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgY29uc3QgdHJhZGUgPSB0cmFkZURhdGEuZGF0YTtcblxuICAgICAgICAvLyBFeGVjdXRlIGVzY3JvdyByZWZ1bmQgdHJhbnNhY3Rpb24gdXNpbmcgdGhlIGhvb2tcbiAgICAgICAgYXdhaXQgZXNjcm93T3BlcmF0aW9ucy5leGVjdXRlUmVmdW5kKFxuICAgICAgICAgIHRyYWRlLmVzY3Jvd0lkLFxuICAgICAgICAgICdTbzExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTExMTEyJywgLy8gU09MIG1pbnRcbiAgICAgICAgICBzb2xhbmFXYWxsZXQuYWRkcmVzcywgLy8gQnV5ZXIgd2FsbGV0XG4gICAgICAgICAgc29sYW5hV2FsbGV0XG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGJhY2tlbmQgd2l0aCByZWZ1bmRcbiAgICAgICAgYXdhaXQgcmVmdW5kUGVyayh7XG4gICAgICAgICAgdHJhZGVJZDogbm90aWZpY2F0aW9uLmRhdGEudHJhZGVJZC50b1N0cmluZygpLFxuICAgICAgICAgIGJ1eWVyV2FsbGV0OiBzb2xhbmFXYWxsZXQuYWRkcmVzc1xuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFtOb3RpZmljYXRpb25CZWxsXSBHbG9iYWwgZXNjcm93IHJlZnVuZCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuXG4gICAgICAgIC8vIFJlZnJlc2ggbm90aWZpY2F0aW9ucyB0byBzaG93IHVwZGF0ZWQgc3RhdHVzXG4gICAgICAgIGF3YWl0IGxvYWROb3RpZmljYXRpb25zKCk7XG5cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTm90aWZpY2F0aW9uQmVsbF0gR2xvYmFsIHJlZnVuZCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgICAvLyBTaG93IHVzZXItZnJpZW5kbHkgZXJyb3IgbWVzc2FnZVxuICAgICAgICBhbGVydCgnUmVmdW5kIGZhaWxlZC4gUGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IHN1cHBvcnQuJyk7XG4gICAgICB9XG4gICAgfTtcbiAgfTtcblxuICAvLyBDcmVhdGUgYWNjZXB0IGZ1bmN0aW9uIGZvciBlc2Nyb3cgLSBub3cgd29ya3MgZ2xvYmFsbHkgd2l0aG91dCByb3V0ZSBkZXBlbmRlbmN5XG4gIGNvbnN0IGNyZWF0ZUFjY2VwdEZ1bmN0aW9uID0gKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jICgpID0+IHtcbiAgICAgIGlmICghbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQgfHwgIXNvbGFuYVdhbGxldD8uYWRkcmVzcyB8fCAhaXNDb25uZWN0ZWQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBNaXNzaW5nIGRhdGEgZm9yIGFjY2VwdDonLCB7XG4gICAgICAgICAgdHJhZGVJZDogbm90aWZpY2F0aW9uLmRhdGE/LnRyYWRlSWQsXG4gICAgICAgICAgd2FsbGV0OiBzb2xhbmFXYWxsZXQ/LmFkZHJlc3MsXG4gICAgICAgICAgY29ubmVjdGVkOiBpc0Nvbm5lY3RlZFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gU3RhcnRpbmcgZ2xvYmFsIGVzY3JvdyBhY2NlcHQgZnJvbSBub3RpZmljYXRpb24nKTtcblxuICAgICAgICAvLyBFeGVjdXRlIGVzY3JvdyBhY2NlcHQgdHJhbnNhY3Rpb24gdXNpbmcgdGhlIHV0aWxpdHkgZnVuY3Rpb24gd2l0aCB0cmFkZUlkXG4gICAgICAgIC8vIFRoZSBmdW5jdGlvbiB3aWxsIGF1dG9tYXRpY2FsbHkgZmV0Y2ggdHJhZGUgZGV0YWlscyBhbmQgY2FsY3VsYXRlIGFtb3VudHNcbiAgICAgICAgY29uc3QgeyBjcmVhdGVFc2Nyb3dBY2NlcHRUcmFuc2FjdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCcuLi8uLi8uLi91dGlscy9lc2Nyb3cnKTtcbiAgICAgICAgY29uc3QgdHhJZCA9IGF3YWl0IGNyZWF0ZUVzY3Jvd0FjY2VwdFRyYW5zYWN0aW9uKFxuICAgICAgICAgIFwiXCIsIC8vIGVzY3Jvd0lkIC0gd2lsbCBiZSBmZXRjaGVkIGZyb20gdHJhZGUgZGF0YVxuICAgICAgICAgIFwiMFwiLCAvLyBwdXJjaGFzZVRva2VuQW1vdW50IC0gd2lsbCBiZSBjYWxjdWxhdGVkIGZyb20gdHJhZGUgZGF0YVxuICAgICAgICAgIFwiMFwiLCAvLyBwZXJrVG9rZW5BbW91bnQgLSB3aWxsIGJlIGNhbGN1bGF0ZWQgZnJvbSB0cmFkZSBkYXRhXG4gICAgICAgICAgXCJcIiwgLy8gcHVyY2hhc2VUb2tlbk1pbnQgLSB3aWxsIGJlIHNldCBmcm9tIHRyYWRlIGRhdGFcbiAgICAgICAgICBcIlwiLCAvLyBwZXJrVG9rZW5NaW50IC0gd2lsbCBiZSBmZXRjaGVkIGZyb20gdHJhZGUgZGF0YVxuICAgICAgICAgIHNvbGFuYVdhbGxldC5hZGRyZXNzLCAvLyBTZWxsZXIgd2FsbGV0XG4gICAgICAgICAgc29sYW5hV2FsbGV0LFxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5kYXRhLnRyYWRlSWQgLy8gdHJhZGVJZCAtIHRoaXMgd2lsbCB0cmlnZ2VyIGF1dG9tYXRpYyBjYWxjdWxhdGlvblxuICAgICAgICApO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBiYWNrZW5kIHdpdGggYWNjZXB0YW5jZVxuICAgICAgICBjb25zdCBhY2NlcHRSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9DT05GSUcuQkFTRV9VUkx9L3BlcmtzL3RyYWRlcy8ke25vdGlmaWNhdGlvbi5kYXRhLnRyYWRlSWR9L2FjY2VwdGAsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKX1gXG4gICAgICAgICAgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICB0eElkLFxuICAgICAgICAgICAgYWNjZXB0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSlcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKCFhY2NlcHRSZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBiYWNrZW5kIHdpdGggYWNjZXB0YW5jZScpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gR2xvYmFsIGVzY3JvdyBhY2NlcHQgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKTtcblxuICAgICAgICAvLyBSZWZyZXNoIG5vdGlmaWNhdGlvbnMgdG8gc2hvdyB1cGRhdGVkIHN0YXR1c1xuICAgICAgICBhd2FpdCBsb2FkTm90aWZpY2F0aW9ucygpO1xuXG4gICAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICAgIGFsZXJ0KCdFc2Nyb3cgYWNjZXB0ZWQgc3VjY2Vzc2Z1bGx5ISBUaGUgYnV5ZXIgY2FuIG5vdyByZWxlYXNlIGZ1bmRzLicpO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIEdsb2JhbCBhY2NlcHQgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgICAgLy8gU2hvdyB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICAgICAgYWxlcnQoJ0FjY2VwdCBmYWlsZWQuIFBsZWFzZSB0cnkgYWdhaW4gb3IgY29udGFjdCBzdXBwb3J0LicpO1xuICAgICAgfVxuICAgIH07XG4gIH07XG5cbiAgLy8gRnVuY3Rpb24gdG8gb3BlbiBjaGF0IG1vZGFsXG4gIGNvbnN0IG9wZW5DaGF0TW9kYWwgPSBhc3luYyAobm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24pID0+IHtcbiAgICAvLyBXZSBjYW4gb3BlbiBjaGF0IG1vZGFsIGlmIHdlIGhhdmUgZWl0aGVyIGNoYXRSb29tSWQgb3IgdHJhZGVJZFxuICAgIGlmICghbm90aWZpY2F0aW9uLmRhdGE/LmNoYXRSb29tSWQgJiYgIW5vdGlmaWNhdGlvbi5kYXRhPy50cmFkZUlkKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW05vdGlmaWNhdGlvbkJlbGxdIE5vIGNoYXRSb29tSWQgb3IgdHJhZGVJZCBpbiBub3RpZmljYXRpb24gZGF0YScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEdldCBjdXJyZW50IHVzZXIgaWQgZnJvbSBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCB1c2VyQm9TdHIgPSB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ1c2VyQm9cIikgOiBudWxsO1xuICAgIGNvbnN0IHVzZXJCbyA9IHVzZXJCb1N0ciA/IEpTT04ucGFyc2UodXNlckJvU3RyKSA6IG51bGw7XG4gICAgY29uc3QgbXlVc2VySWQgPSB1c2VyQm8/LmlkO1xuXG4gICAgLy8gRGV0ZXJtaW5lIHRoZSBvdGhlciBwYXJ0eSAoYnV5ZXIgb3Igc2VsbGVyKVxuICAgIGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkO1xuICAgIGNvbnN0IHVzZXJJZE51bWJlciA9IHR5cGVvZiB1c2VySWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQodXNlcklkKSA6IHVzZXJJZDtcblxuICAgIGxldCBidXllcklkID0gbm90aWZpY2F0aW9uLmRhdGEuYnV5ZXJJZDtcbiAgICBsZXQgc2VsbGVySWQgPSBub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZDtcblxuICAgIC8vIFNwZWNpYWwgaGFuZGxpbmcgZm9yIGVzY3Jvd19yZWxlYXNlX3JlbWluZGVyIC0gc2VsbGVyIGlzIHRoZSBjdXJyZW50IHVzZXJcbiAgICBpZiAobm90aWZpY2F0aW9uLnR5cGUgPT09ICdlc2Nyb3dfcmVsZWFzZV9yZW1pbmRlcicpIHtcbiAgICAgIHNlbGxlcklkID0gdXNlcklkTnVtYmVyOyAvLyBDdXJyZW50IHVzZXIgaXMgdGhlIHNlbGxlclxuICAgICAgYnV5ZXJJZCA9IG5vdGlmaWNhdGlvbi5kYXRhLmJ1eWVySWQ7IC8vIEJ1eWVyIGZyb20gbm90aWZpY2F0aW9uIGRhdGFcbiAgICB9XG4gICAgLy8gSWYgd2UgZG9uJ3QgaGF2ZSBib3RoIElEcywgdHJ5IHRvIGRldGVybWluZSBmcm9tIHRoZSBub3RpZmljYXRpb25cbiAgICBlbHNlIGlmICghYnV5ZXJJZCB8fCAhc2VsbGVySWQpIHtcbiAgICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5idXllcklkID09PSB1c2VySWROdW1iZXIpIHtcbiAgICAgICAgYnV5ZXJJZCA9IHVzZXJJZE51bWJlcjtcbiAgICAgICAgc2VsbGVySWQgPSBub3RpZmljYXRpb24uZGF0YS5zZWxsZXJJZCB8fCBub3RpZmljYXRpb24uZGF0YS5yZWNlaXZlcklkO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2VsbGVySWQgPSB1c2VySWROdW1iZXI7XG4gICAgICAgIGJ1eWVySWQgPSBub3RpZmljYXRpb24uZGF0YS5idXllcklkIHx8IG5vdGlmaWNhdGlvbi5kYXRhLnNlbmRlcklkO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEdlbmVyYXRlIGNvbnNpc3RlbnQgY2hhdCByb29tIElEIGlmIG5vdCBwcm92aWRlZFxuICAgIGxldCBjaGF0Um9vbUlkID0gbm90aWZpY2F0aW9uLmRhdGEuY2hhdFJvb21JZDtcbiAgICBpZiAoIWNoYXRSb29tSWQgJiYgYnV5ZXJJZCAmJiBzZWxsZXJJZCAmJiBub3RpZmljYXRpb24uZGF0YT8ucGVya0lkKSB7XG4gICAgICBjaGF0Um9vbUlkID0gZ2VuZXJhdGVDaGF0Um9vbUlkKGJ1eWVySWQsIHNlbGxlcklkLCBub3RpZmljYXRpb24uZGF0YS5wZXJrSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIEdlbmVyYXRlZCBjaGF0Um9vbUlkIGZyb20gdHJhZGUgZGF0YTonLCBjaGF0Um9vbUlkKTtcbiAgICB9XG5cbiAgICBpZiAoIWNoYXRSb29tSWQpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTm90aWZpY2F0aW9uQmVsbF0gQ291bGQgbm90IGRldGVybWluZSBjaGF0Um9vbUlkJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIE9wZW5pbmcgY2hhdCBtb2RhbCB3aXRoOicsIHtcbiAgICAgIGNoYXRSb29tSWQsXG4gICAgICBidXllcklkLFxuICAgICAgc2VsbGVySWQsXG4gICAgICBteVVzZXJJZCxcbiAgICAgIHVzZXJJZE51bWJlclxuICAgIH0pO1xuXG4gICAgLy8gRGVidWcgbm90aWZpY2F0aW9uIGRhdGFcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbTm90aWZpY2F0aW9uQmVsbF0gT3BlbmluZyBjaGF0IG1vZGFsIHdpdGggZGF0YTonLCB7XG4gICAgICBub3RpZmljYXRpb25UeXBlOiBub3RpZmljYXRpb24udHlwZSxcbiAgICAgIG5vdGlmaWNhdGlvbkRhdGE6IG5vdGlmaWNhdGlvbi5kYXRhLFxuICAgICAgYnV5ZXJJZDogYnV5ZXJJZCB8fCBteVVzZXJJZCxcbiAgICAgIHNlbGxlcklkOiBzZWxsZXJJZCB8fCBteVVzZXJJZCxcbiAgICAgIG15VXNlcklkLFxuICAgICAgY3VycmVudFVzZXI6IHVzZXI/LmlkLFxuICAgICAgdHJhZGVJZDogbm90aWZpY2F0aW9uLmRhdGEudHJhZGVJZFxuICAgIH0pO1xuXG4gICAgLy8gRmV0Y2ggcmVhbCB0cmFkZSBkZXRhaWxzIGlmIHRyYWRlSWQgZXhpc3RzLCBvciB0cnkgdG8gZmluZCBpdCBmcm9tIGNoYXRSb29tSWRcbiAgICBsZXQgYWN0aXZlVHJhZGUgPSB1bmRlZmluZWQ7XG4gICAgbGV0IHRyYWRlSWRUb1VzZSA9IG5vdGlmaWNhdGlvbi5kYXRhLnRyYWRlSWQ7XG5cbiAgICAvLyBJZiBubyB0cmFkZUlkIGJ1dCB3ZSBoYXZlIGNoYXRSb29tSWQsIHRyeSB0byBmaW5kIHRoZSB0cmFkZVxuICAgIGlmICghdHJhZGVJZFRvVXNlICYmIG5vdGlmaWNhdGlvbi5kYXRhLmNoYXRSb29tSWQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBObyB0cmFkZUlkIGZvdW5kLCBzZWFyY2hpbmcgYnkgY2hhdFJvb21JZDonLCBub3RpZmljYXRpb24uZGF0YS5jaGF0Um9vbUlkKTtcblxuICAgICAgICAvLyBFeHRyYWN0IHBlcmtJZCBmcm9tIGNoYXRSb29tSWQgZm9ybWF0OiBidXllcklkLXNlbGxlcklkLXBlcmtJZFxuICAgICAgICBjb25zdCBjaGF0Um9vbVBhcnRzID0gbm90aWZpY2F0aW9uLmRhdGEuY2hhdFJvb21JZC5zcGxpdCgnLScpO1xuICAgICAgICBpZiAoY2hhdFJvb21QYXJ0cy5sZW5ndGggPT09IDMpIHtcbiAgICAgICAgICBjb25zdCBwZXJrSWQgPSBjaGF0Um9vbVBhcnRzWzJdO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtOb3RpZmljYXRpb25CZWxsXSBFeHRyYWN0ZWQgcGVya0lkIGZyb20gY2hhdFJvb21JZDonLCBwZXJrSWQpO1xuXG4gICAgICAgICAgLy8gRmluZCBhY3RpdmUgdHJhZGUgZm9yIHRoaXMgcGVyayBhbmQgdXNlcnNcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9DT05GSUcuQkFTRV9VUkx9L3BlcmtzLyR7cGVya0lkfS90cmFkZXNgLCB7XG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpfWBcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgICAgY29uc3QgdHJhZGVzRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIGlmICh0cmFkZXNEYXRhLmRhdGEgJiYgdHJhZGVzRGF0YS5kYXRhLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgLy8gRmluZCB0aGUgbW9zdCByZWNlbnQgYWN0aXZlIHRyYWRlXG4gICAgICAgICAgICAgIGNvbnN0IGFjdGl2ZVRyYWRlcyA9IHRyYWRlc0RhdGEuZGF0YS5maWx0ZXIoKHRyYWRlOiBhbnkpID0+XG4gICAgICAgICAgICAgICAgWydwZW5kaW5nX2FjY2VwdGFuY2UnLCAnZXNjcm93ZWQnLCAnY29tcGxldGVkJywgJ3JlbGVhc2VkJ10uaW5jbHVkZXModHJhZGUuc3RhdHVzKVxuICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgIGlmIChhY3RpdmVUcmFkZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIHRyYWRlSWRUb1VzZSA9IGFjdGl2ZVRyYWRlc1swXS5pZDtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFtOb3RpZmljYXRpb25CZWxsXSBGb3VuZCBhY3RpdmUgdHJhZGUgZnJvbSBjaGF0Um9vbUlkOicsIHRyYWRlSWRUb1VzZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gW05vdGlmaWNhdGlvbkJlbGxdIENvdWxkIG5vdCBmaW5kIHRyYWRlIGZyb20gY2hhdFJvb21JZDonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKHRyYWRlSWRUb1VzZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIEZldGNoaW5nIHRyYWRlIGRldGFpbHMgZm9yIHRyYWRlSWQ6JywgdHJhZGVJZFRvVXNlKTtcbiAgICAgICAgY29uc3QgeyBnZXRUcmFkZURldGFpbHMgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vYXhpb3MvcmVxdWVzdHMnKTtcbiAgICAgICAgY29uc3QgdHJhZGVSZXNwb25zZSA9IGF3YWl0IGdldFRyYWRlRGV0YWlscyhOdW1iZXIodHJhZGVJZFRvVXNlKSk7XG5cbiAgICAgICAgaWYgKHRyYWRlUmVzcG9uc2Uuc3RhdHVzID09PSAyMDApIHtcbiAgICAgICAgICBhY3RpdmVUcmFkZSA9IHtcbiAgICAgICAgICAgIGlkOiB0cmFkZVJlc3BvbnNlLmRhdGEuaWQsXG4gICAgICAgICAgICBzdGF0dXM6IHRyYWRlUmVzcG9uc2UuZGF0YS5zdGF0dXMsXG4gICAgICAgICAgICB0cmFkZUlkOiB0cmFkZVJlc3BvbnNlLmRhdGEuaWQsXG4gICAgICAgICAgICBmcm9tOiB0cmFkZVJlc3BvbnNlLmRhdGEudXNlcklkLCAvLyBidXllclxuICAgICAgICAgICAgdG86IHRyYWRlUmVzcG9uc2UuZGF0YS5wZXJrRGV0YWlscz8udXNlcklkLCAvLyBzZWxsZXJcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogdHJhZGVSZXNwb25zZS5kYXRhLmNyZWF0ZWRBdCxcbiAgICAgICAgICAgIGRpc3B1dGVTdGF0dXM6IHRyYWRlUmVzcG9uc2UuZGF0YS5kaXNwdXRlPy5zdGF0dXMgfHwgJ25vbmUnLFxuICAgICAgICAgICAgZXNjcm93SWQ6IHRyYWRlUmVzcG9uc2UuZGF0YS5lc2Nyb3dJZCxcbiAgICAgICAgICAgIHBlcmtJZDogdHJhZGVSZXNwb25zZS5kYXRhLnBlcmtJZFxuICAgICAgICAgIH07XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gVHJhZGUgZGV0YWlscyBmZXRjaGVkOicsIHtcbiAgICAgICAgICAgIHRyYWRlSWQ6IGFjdGl2ZVRyYWRlLmlkLFxuICAgICAgICAgICAgc3RhdHVzOiBhY3RpdmVUcmFkZS5zdGF0dXMsXG4gICAgICAgICAgICBidXllcjogYWN0aXZlVHJhZGUuZnJvbSxcbiAgICAgICAgICAgIHNlbGxlcjogYWN0aXZlVHJhZGUudG9cbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtOb3RpZmljYXRpb25CZWxsXSBGYWlsZWQgdG8gZmV0Y2ggdHJhZGUgZGV0YWlsczonLCBlcnJvcik7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIG5vdGlmaWNhdGlvbiBkYXRhXG4gICAgICAgIGFjdGl2ZVRyYWRlID0ge1xuICAgICAgICAgIGlkOiB0cmFkZUlkVG9Vc2UsXG4gICAgICAgICAgc3RhdHVzOiAnZXNjcm93ZWQnLCAvLyBGYWxsYmFjayBhc3N1bXB0aW9uXG4gICAgICAgICAgdHJhZGVJZDogdHJhZGVJZFRvVXNlLFxuICAgICAgICAgIGZyb206IGJ1eWVySWQsXG4gICAgICAgICAgdG86IHNlbGxlcklkXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVXNlIGNvbnNpc3RlbnQgbW9kYWwgSUQgYmFzZWQgb24gY2hhdFJvb21JZCB0byBwcmV2ZW50IG11bHRpcGxlIG1vZGFsc1xuICAgIGNvbnN0IG1vZGFsSWQgPSBgY2hhdC1tb2RhbC0ke2NoYXRSb29tSWR9YDtcblxuICAgIC8vIENoZWNrIGlmIG1vZGFsIGlzIGFscmVhZHkgb3BlbiAtIGlmIHNvLCBkb24ndCBjbG9zZSBpdCwganVzdCBmb2N1cyBpdFxuICAgIGNvbnN0IGV4aXN0aW5nTW9kYWwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChtb2RhbElkKTtcbiAgICBpZiAoZXhpc3RpbmdNb2RhbCkge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBbTm90aWZpY2F0aW9uQmVsbF0gQ2hhdCBtb2RhbCBhbHJlYWR5IG9wZW4sIGZvY3VzaW5nIGV4aXN0aW5nIG1vZGFsJyk7XG4gICAgICBleGlzdGluZ01vZGFsLnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIE9wZW4gQ2hhdE1vZGFsIGluIGdsb2JhbCBtb2RhbFxuICAgIG9wZW5Nb2RhbCh7XG4gICAgICBpZDogbW9kYWxJZCxcbiAgICAgIGNvbXBvbmVudDogUmVhY3QuY3JlYXRlRWxlbWVudChDaGF0TW9kYWwsIHtcbiAgICAgICAgY2hhdFJvb21JZCxcbiAgICAgICAgYnV5ZXJJZDogYnV5ZXJJZCB8fCBteVVzZXJJZCxcbiAgICAgICAgc2VsbGVySWQ6IHNlbGxlcklkIHx8IG15VXNlcklkLFxuICAgICAgICBvbkNsb3NlOiAoKSA9PiBjbG9zZU1vZGFsKG1vZGFsSWQpLFxuICAgICAgICBvblJlbGVhc2U6IGNyZWF0ZVJlbGVhc2VGdW5jdGlvbihub3RpZmljYXRpb24pLFxuICAgICAgICBvblJlZnVuZDogY3JlYXRlUmVmdW5kRnVuY3Rpb24obm90aWZpY2F0aW9uKSxcbiAgICAgICAgb25SZXBvcnQ6ICgpID0+IHt9LFxuICAgICAgICBvbkFjY2VwdDogY3JlYXRlQWNjZXB0RnVuY3Rpb24obm90aWZpY2F0aW9uKSxcbiAgICAgICAgYWN0aXZlVHJhZGVcbiAgICAgIH0pLFxuICAgICAgY2xvc2VPbkJhY2tkcm9wQ2xpY2s6IGZhbHNlLFxuICAgICAgY2xvc2VPbkVzY2FwZTogdHJ1ZSxcbiAgICAgIHByZXZlbnRDbG9zZTogZmFsc2UsXG4gICAgICB6SW5kZXg6IDk5OTksXG4gICAgICBiYWNrZHJvcENsYXNzTmFtZTogJ2JnLWJsYWNrLzUwIGJhY2tkcm9wLWJsdXItc20nLFxuICAgICAgbW9kYWxDbGFzc05hbWU6ICcnLFxuICAgICAgZGlzYWJsZVNjcm9sbDogdHJ1ZVxuICAgIH0pO1xuXG4gICAgLy8gQ2xvc2UgdGhlIG5vdGlmaWNhdGlvbiBkcm9wZG93blxuICAgIHNldElzT3BlbihmYWxzZSk7XG4gIH07XG5cbiAgLy8gTG9hZCBub3RpZmljYXRpb25zIG9uIG1vdW50IGFuZCB3aGVuIHVzZXIgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWROb3RpZmljYXRpb25zKCk7XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8gUmVmcmVzaCBub3RpZmljYXRpb25zIGV2ZXJ5IDMwIHNlY29uZHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGxvYWROb3RpZmljYXRpb25zLCAzMDAwMCk7XG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbdXNlcl0pO1xuXG4gIC8vIENsb3NlIGRyb3Bkb3duIHdoZW4gY2xpY2tpbmcgb3V0c2lkZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xuICAgICAgaWYgKGRyb3Bkb3duUmVmLmN1cnJlbnQgJiYgIWRyb3Bkb3duUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XG4gICAgICAgIHNldElzT3BlbihmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gIH0sIFtdKTtcblxuICAvLyBNYXJrIG5vdGlmaWNhdGlvbiBhcyByZWFkXG4gIGNvbnN0IGhhbmRsZU5vdGlmaWNhdGlvbkNsaWNrID0gYXN5bmMgKG5vdGlmaWNhdGlvbjogTm90aWZpY2F0aW9uKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIE5vdGlmaWNhdGlvbiBjbGlja2VkOicsIHtcbiAgICAgIHR5cGU6IG5vdGlmaWNhdGlvbi50eXBlLFxuICAgICAgc2hvdWxkT3BlbkNoYXQ6IHNob3VsZE9wZW5DaGF0TW9kYWwobm90aWZpY2F0aW9uKSxcbiAgICAgIGNoYXRSb29tSWQ6IG5vdGlmaWNhdGlvbi5kYXRhPy5jaGF0Um9vbUlkLFxuICAgICAgYWN0aW9uVXJsOiBub3RpZmljYXRpb24uYWN0aW9uVXJsXG4gICAgfSk7XG5cbiAgICBpZiAoIW5vdGlmaWNhdGlvbi5pc1JlYWQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IG1hcmtOb3RpZmljYXRpb25Bc1JlYWQobm90aWZpY2F0aW9uLmlkKTtcblxuICAgICAgICAvLyBVcGRhdGUgdGhlIGFwcHJvcHJpYXRlIG5vdGlmaWNhdGlvbiBsaXN0XG4gICAgICAgIGlmIChpc1RyYWRlTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbikpIHtcbiAgICAgICAgICBzZXRUcmFkZU5vdGlmaWNhdGlvbnMocHJldiA9PlxuICAgICAgICAgICAgcHJldi5tYXAobiA9PiBuLmlkID09PSBub3RpZmljYXRpb24uaWQgPyB7IC4uLm4sIGlzUmVhZDogdHJ1ZSB9IDogbilcbiAgICAgICAgICApO1xuICAgICAgICAgIHNldFRyYWRlVW5yZWFkQ291bnQocHJldiA9PiBNYXRoLm1heCgwLCBwcmV2IC0gMSkpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldFN5c3RlbU5vdGlmaWNhdGlvbnMocHJldiA9PlxuICAgICAgICAgICAgcHJldi5tYXAobiA9PiBuLmlkID09PSBub3RpZmljYXRpb24uaWQgPyB7IC4uLm4sIGlzUmVhZDogdHJ1ZSB9IDogbilcbiAgICAgICAgICApO1xuICAgICAgICAgIHNldFN5c3RlbVVucmVhZENvdW50KHByZXYgPT4gTWF0aC5tYXgoMCwgcHJldiAtIDEpKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIG1hcmsgbm90aWZpY2F0aW9uIGFzIHJlYWQ6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRoaXMgbm90aWZpY2F0aW9uIHNob3VsZCBvcGVuIGNoYXQgbW9kYWwgKG5vdyBhbGwgdHJhZGUgbm90aWZpY2F0aW9ucyBvcGVuIGNoYXQgbW9kYWxzKVxuICAgIGlmIChzaG91bGRPcGVuQ2hhdE1vZGFsKG5vdGlmaWNhdGlvbikpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgW05vdGlmaWNhdGlvbkJlbGxdIE9wZW5pbmcgY2hhdCBtb2RhbCBmb3Igbm90aWZpY2F0aW9uJyk7XG4gICAgICBvcGVuQ2hhdE1vZGFsKG5vdGlmaWNhdGlvbik7XG4gICAgfSBlbHNlIGlmIChub3RpZmljYXRpb24uYWN0aW9uVXJsKSB7XG4gICAgICAvLyBOYXZpZ2F0ZSB0byBhY3Rpb24gVVJMIG9ubHkgZm9yIHN5c3RlbSBub3RpZmljYXRpb25zIChtb2RlcmF0b3IgZGFzaGJvYXJkLCBldGMuKVxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW05vdGlmaWNhdGlvbkJlbGxdIE5hdmlnYXRpbmcgdG8gYWN0aW9uVXJsOicsIG5vdGlmaWNhdGlvbi5hY3Rpb25VcmwpO1xuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBub3RpZmljYXRpb24uYWN0aW9uVXJsO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGb3Igbm90aWZpY2F0aW9ucyB3aXRob3V0IGFjdGlvblVybCwganVzdCBtYXJrIGFzIHJlYWQgKGFscmVhZHkgaGFuZGxlZCBhYm92ZSlcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgW05vdGlmaWNhdGlvbkJlbGxdIE5vdGlmaWNhdGlvbiBtYXJrZWQgYXMgcmVhZCcpO1xuICAgIH1cbiAgfTtcblxuICAvLyBNYXJrIGFsbCBhcyByZWFkIGZvciBjdXJyZW50IHRhYlxuICBjb25zdCBoYW5kbGVNYXJrQWxsQXNSZWFkID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBtYXJrQWxsTm90aWZpY2F0aW9uc0FzUmVhZCgpO1xuXG4gICAgICAvLyBVcGRhdGUgYm90aCBub3RpZmljYXRpb24gbGlzdHNcbiAgICAgIHNldFN5c3RlbU5vdGlmaWNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChuID0+ICh7IC4uLm4sIGlzUmVhZDogdHJ1ZSB9KSkpO1xuICAgICAgc2V0VHJhZGVOb3RpZmljYXRpb25zKHByZXYgPT4gcHJldi5tYXAobiA9PiAoeyAuLi5uLCBpc1JlYWQ6IHRydWUgfSkpKTtcblxuICAgICAgLy8gUmVzZXQgdW5yZWFkIGNvdW50c1xuICAgICAgc2V0U3lzdGVtVW5yZWFkQ291bnQoMCk7XG4gICAgICBzZXRUcmFkZVVucmVhZENvdW50KDApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbWFyayBhbGwgbm90aWZpY2F0aW9ucyBhcyByZWFkOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gTWFyayBhbGwgYXMgcmVhZCBmb3Igc3BlY2lmaWMgdGFiXG4gIGNvbnN0IGhhbmRsZU1hcmtUYWJBc1JlYWQgPSBhc3luYyAodGFiVHlwZTogVGFiVHlwZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBOb3RlOiBUaGlzIHdvdWxkIGlkZWFsbHkgYmUgYSBtb3JlIHNwZWNpZmljIEFQSSBjYWxsXG4gICAgICAvLyBGb3Igbm93LCB3ZSdsbCBtYXJrIGluZGl2aWR1YWwgbm90aWZpY2F0aW9ucyBhcyByZWFkXG4gICAgICBjb25zdCBub3RpZmljYXRpb25zID0gdGFiVHlwZSA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1Ob3RpZmljYXRpb25zIDogdHJhZGVOb3RpZmljYXRpb25zO1xuICAgICAgY29uc3QgdW5yZWFkTm90aWZpY2F0aW9ucyA9IG5vdGlmaWNhdGlvbnMuZmlsdGVyKG4gPT4gIW4uaXNSZWFkKTtcblxuICAgICAgZm9yIChjb25zdCBub3RpZmljYXRpb24gb2YgdW5yZWFkTm90aWZpY2F0aW9ucykge1xuICAgICAgICBhd2FpdCBtYXJrTm90aWZpY2F0aW9uQXNSZWFkKG5vdGlmaWNhdGlvbi5pZCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgYXBwcm9wcmlhdGUgbGlzdFxuICAgICAgaWYgKHRhYlR5cGUgPT09ICdzeXN0ZW0nKSB7XG4gICAgICAgIHNldFN5c3RlbU5vdGlmaWNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChuID0+ICh7IC4uLm4sIGlzUmVhZDogdHJ1ZSB9KSkpO1xuICAgICAgICBzZXRTeXN0ZW1VbnJlYWRDb3VudCgwKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFRyYWRlTm90aWZpY2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKG4gPT4gKHsgLi4ubiwgaXNSZWFkOiB0cnVlIH0pKSk7XG4gICAgICAgIHNldFRyYWRlVW5yZWFkQ291bnQoMCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBtYXJrICR7dGFiVHlwZX0gbm90aWZpY2F0aW9ucyBhcyByZWFkOmAsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gR2V0IG5vdGlmaWNhdGlvbiBpY29uIGJhc2VkIG9uIHR5cGVcbiAgY29uc3QgZ2V0Tm90aWZpY2F0aW9uSWNvbiA9ICh0eXBlOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ2Rpc3B1dGVfY3JlYXRlZCc6XG4gICAgICBjYXNlICdkaXNwdXRlX2Fzc2lnbmVkJzpcbiAgICAgIGNhc2UgJ2Rpc3B1dGVfcmVzb2x2ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy15ZWxsb3ctMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQteWVsbG93LTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgOXYybTAgNGguMDFtLTYuOTM4IDRoMTMuODU2YzEuNTQgMCAyLjUwMi0xLjY2NyAxLjczMi0yLjVMMTMuNzMyIDRjLS43Ny0uODMzLTEuOTY0LS44MzMtMi43MzIgMEwzLjI2OCAxNi41Yy0uNzcuODMzLjE5MiAyLjUgMS43MzIgMi41elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3RyYWRlX2NvbXBsZXRlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyZWVuLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNSAxM2w0IDRMMTkgN1wiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3RyYWRlX3JlZnVuZGVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctcmVkLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDhjLTEuNjU3IDAtMyAuODk1LTMgMnMxLjM0MyAyIDMgMiAzIC44OTUgMyAyLTEuMzQzIDItMyAybTAtOGMxLjExIDAgMi4wOC40MDIgMi41OTkgMU0xMiA4VjdtMCAxdjhtMCAwdjFtMC0xYy0xLjExIDAtMi4wOC0uNDAyLTIuNTk5LTFcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdtb2RlcmF0b3JfYWRkZWQnOlxuICAgICAgY2FzZSAnbW9kZXJhdG9yX3JlbW92ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1wdXJwbGUtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHVycGxlLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOSAxMmwyIDIgNC00bTUuNjE4LTQuMDE2QTExLjk1NSAxMS45NTUgMCAwMTEyIDIuOTQ0YTExLjk1NSAxMS45NTUgMCAwMS04LjYxOCAzLjA0QTEyLjAyIDEyLjAyIDAgMDAzIDljMCA1LjU5MSAzLjgyNCAxMC4yOSA5IDExLjYyMiA1LjE3Ni0xLjMzMiA5LTYuMDMgOS0xMS42MjIgMC0xLjA0Mi0uMTMzLTIuMDUyLS4zODItMy4wMTZ6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnc3lzdGVtX2Fubm91bmNlbWVudCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWluZGlnby0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1pbmRpZ28tNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMSA1Ljg4MlYxOS4yNGExLjc2IDEuNzYgMCAwMS0zLjQxNy41OTJsLTIuMTQ3LTYuMTVNMTggMTNhMyAzIDAgMTAwLTZNNS40MzYgMTMuNjgzQTQuMDAxIDQuMDAxIDAgMDE3IDZoMS44MzJjNC4xIDAgNy42MjUtMS4yMzQgOS4xNjgtM3YxNGMtMS41NDMtMS43NjYtNS4wNjctMy05LjE2OC0zSDdhMy45ODggMy45ODggMCAwMS0xLjU2NC0uMzE3elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3BlcmtfY3JlYXRlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWVtZXJhbGQtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZW1lcmFsZC02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDZ2Nm0wIDB2Nm0wLTZoNm0tNiAwSDZcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICd0b2tlbl9jcmVhdGVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYW1iZXItMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYW1iZXItNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA4Yy0xLjY1NyAwLTMgLjg5NS0zIDJzMS4zNDMgMiAzIDIgMyAuODk1IDMgMi0xLjM0MyAyLTMgMm0wLThjMS4xMSAwIDIuMDguNDAyIDIuNTk5IDFNMTIgOFY3bTAgMXY4bTAgMHYxbTAtMWMtMS4xMSAwLTIuMDgtLjQwMi0yLjU5OS0xXCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnY2hhdF9tZXNzYWdlJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOCAxMmguMDFNMTIgMTJoLjAxTTE2IDEyaC4wMU0yMSAxMmMwIDQuNDE4LTQuMDMgOC05IDhhOS44NjMgOS44NjMgMCAwMS00LjI1NS0uOTQ5TDMgMjBsMS4zOTUtMy43MkMzLjUxMiAxNS4wNDIgMyAxMy41NzQgMyAxMmMwLTQuNDE4IDQuMDMtOCA5LThzOSAzLjU4MiA5IDh6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnZXNjcm93X2NyZWF0ZWQnOlxuICAgICAgY2FzZSAnZXNjcm93X3JlbGVhc2VkJzpcbiAgICAgIGNhc2UgJ2VzY3Jvd19yZWxlYXNlX3JlbWluZGVyJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctb3JhbmdlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LW9yYW5nZS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDE1djJtLTYgNGgxMmEyIDIgMCAwMDItMnYtNmEyIDIgMCAwMC0yLTJINmEyIDIgMCAwMC0yIDJ2NmEyIDIgMCAwMDIgMnptMTAtMTBWN2E0IDQgMCAwMC04IDB2NGg4elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2VzY3Jvd19wZW5kaW5nX2FjY2VwdGFuY2UnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA4djRsMyAzbTYtM2E5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdlc2Nyb3dfYWNjZXB0ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmVlbi0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTJsMiAyIDQtNG02IDJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnZGlzcHV0ZV9jcmVhdGVkJzpcbiAgICAgIGNhc2UgJ2Rpc3B1dGVfYXNzaWduZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy15ZWxsb3ctMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQteWVsbG93LTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgOXYybTAgNGguMDFtLTYuOTM4IDRoMTMuODU2YzEuNTQgMCAyLjUwMi0xLjY2NyAxLjczMi0yLjVMMTMuNzMyIDRjLS43Ny0uODMzLTEuOTY0LS44MzMtMi43MzIgMEwzLjI2OCAxNi41Yy0uNzcuODMzLjE5MiAyLjUgMS43MzIgMi41elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2Rpc3B1dGVfcmVzb2x2ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmVlbi0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTJsMiAyIDQtNG02IDJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAncGVya19wdXJjaGFzZWQnOlxuICAgICAgY2FzZSAncGVya19zb2xkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctY3lhbi0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1jeWFuLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTYgMTFWN2E0IDQgMCAwMC04IDB2NE01IDloMTRsMSAxMkg0TDUgOXpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICBjYXNlICd0cmFkZV9yZXBvcnRlZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXJlZC0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA5djJtMCA0aC4wMW0tNi45MzggNGgxMy44NTZjMS41NCAwIDIuNTAyLTEuNjY3IDEuNzMyLTIuNUwxMy43MzIgNGMtLjc3LS44MzMtMS45NjQtLjgzMy0yLjczMiAwTDMuMjY4IDE2LjVjLS43Ny44MzMuMTkyIDIuNSAxLjczMiAyLjV6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTMgMTZoLTF2LTRoLTFtMS00aC4wMU0yMSAxMmE5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZvcm1hdCB0aW1lIGFnb1xuICBjb25zdCBmb3JtYXRUaW1lQWdvID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IGRpZmZJblNlY29uZHMgPSBNYXRoLmZsb29yKChub3cuZ2V0VGltZSgpIC0gZGF0ZS5nZXRUaW1lKCkpIC8gMTAwMCk7XG5cbiAgICBpZiAoZGlmZkluU2Vjb25kcyA8IDYwKSByZXR1cm4gJ0p1c3Qgbm93JztcbiAgICBpZiAoZGlmZkluU2Vjb25kcyA8IDM2MDApIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmZJblNlY29uZHMgLyA2MCl9bSBhZ29gO1xuICAgIGlmIChkaWZmSW5TZWNvbmRzIDwgODY0MDApIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmZJblNlY29uZHMgLyAzNjAwKX1oIGFnb2A7XG4gICAgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDg2NDAwKX1kIGFnb2A7XG4gIH07XG5cbiAgLy8gRG9uJ3QgcmVuZGVyIGlmIHVzZXIgaXMgbm90IGF1dGhlbnRpY2F0ZWRcbiAgaWYgKCF1c2VyKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCB0b3RhbFVucmVhZENvdW50ID0gc3lzdGVtVW5yZWFkQ291bnQgKyB0cmFkZVVucmVhZENvdW50O1xuICBjb25zdCBjdXJyZW50Tm90aWZpY2F0aW9ucyA9IGFjdGl2ZVRhYiA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1Ob3RpZmljYXRpb25zIDogdHJhZGVOb3RpZmljYXRpb25zO1xuICBjb25zdCBjdXJyZW50VW5yZWFkQ291bnQgPSBhY3RpdmVUYWIgPT09ICdzeXN0ZW0nID8gc3lzdGVtVW5yZWFkQ291bnQgOiB0cmFkZVVucmVhZENvdW50O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiIHJlZj17ZHJvcGRvd25SZWZ9PlxuICAgICAgey8qIFJlYWwtdGltZSBub3RpZmljYXRpb24gbGlzdGVuZXIgKi99XG4gICAgICA8UmVhbFRpbWVOb3RpZmljYXRpb25MaXN0ZW5lciBvbk5ld05vdGlmaWNhdGlvbj17bG9hZE5vdGlmaWNhdGlvbnN9IC8+XG5cbiAgICAgIHsvKiBOb3RpZmljYXRpb24gQmVsbCBCdXR0b24gKi99XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbighaXNPcGVuKX1cbiAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgcC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiByb3VuZGVkLWZ1bGxcIlxuICAgICAgPlxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTUgMTdoNWwtNS01VjlhNiA2IDAgMTAtMTIgMHYzbC01IDVoNW03IDB2MWEzIDMgMCAxMS02IDB2LTFtNiAwSDlcIiAvPlxuICAgICAgICA8L3N2Zz5cblxuICAgICAgICB7LyogQ29tYmluZWQgVW5yZWFkIENvdW50IEJhZGdlICovfVxuICAgICAgICB7dG90YWxVbnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMSAtcmlnaHQtMSBiZy1yZWQtNTAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkLWZ1bGwgaC01IHctNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAge3RvdGFsVW5yZWFkQ291bnQgPiA5OSA/ICc5OSsnIDogdG90YWxVbnJlYWRDb3VudH1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICl9XG4gICAgICA8L2J1dHRvbj5cblxuICAgICAgey8qIERyb3Bkb3duICovfVxuICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBtdC0yIHctOTYgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCB6LTUwXCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMyBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTNcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+Tm90aWZpY2F0aW9uczwvaDM+XG4gICAgICAgICAgICAgIHt0b3RhbFVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTWFya0FsbEFzUmVhZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBNYXJrIGFsbCByZWFkXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMSBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIHAtMVwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdzeXN0ZW0nKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnc3lzdGVtJ1xuICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyYXktOTAwIHNoYWRvdy1zbSdcbiAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU3lzdGVtXG4gICAgICAgICAgICAgICAge3N5c3RlbVVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIGJnLXJlZC01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBweC0yIHB5LTAuNVwiPlxuICAgICAgICAgICAgICAgICAgICB7c3lzdGVtVW5yZWFkQ291bnR9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3RyYWRlJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3RyYWRlJ1xuICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyYXktOTAwIHNoYWRvdy1zbSdcbiAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgVHJhZGVzXG4gICAgICAgICAgICAgICAge3RyYWRlVW5yZWFkQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIHB4LTIgcHktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgIHt0cmFkZVVucmVhZENvdW50fVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUYWItc3BlY2lmaWMgTWFyayBhcyBSZWFkICovfVxuICAgICAgICAgICAge2N1cnJlbnRVbnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgZmxleCBqdXN0aWZ5LWVuZFwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU1hcmtUYWJBc1JlYWQoYWN0aXZlVGFiKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBNYXJrIHthY3RpdmVUYWJ9IGFzIHJlYWRcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5vdGlmaWNhdGlvbnMgTGlzdCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTUwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMlwiPkxvYWRpbmcgbm90aWZpY2F0aW9ucy4uLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogY3VycmVudE5vdGlmaWNhdGlvbnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgbXgtYXV0byBtYi0yIHRleHQtZ3JheS0zMDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNSAxN2g1bC01LTVWOWE2IDYgMCAxMC0xMiAwdjNsLTUgNWg1bTcgMHYxYTMgMyAwIDExLTYgMHYtMW02IDBIOVwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPHA+Tm8ge2FjdGl2ZVRhYn0gbm90aWZpY2F0aW9ucyB5ZXQ8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdzeXN0ZW0nXG4gICAgICAgICAgICAgICAgICAgID8gJ1N5c3RlbSBhbm5vdW5jZW1lbnRzIGFuZCB1cGRhdGVzIHdpbGwgYXBwZWFyIGhlcmUnXG4gICAgICAgICAgICAgICAgICAgIDogJ1RyYWRlLXJlbGF0ZWQgbm90aWZpY2F0aW9ucyB3aWxsIGFwcGVhciBoZXJlJ1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICBjdXJyZW50Tm90aWZpY2F0aW9ucy5tYXAoKG5vdGlmaWNhdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17bm90aWZpY2F0aW9uLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2sobm90aWZpY2F0aW9uKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtNCBib3JkZXItYiBib3JkZXItZ3JheS0xMDAgaG92ZXI6YmctZ3JheS01MCBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAhbm90aWZpY2F0aW9uLmlzUmVhZCA/ICdiZy1ibHVlLTUwJyA6ICcnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtnZXROb3RpZmljYXRpb25JY29uKG5vdGlmaWNhdGlvbi50eXBlKX1cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIW5vdGlmaWNhdGlvbi5pc1JlYWQgPyAndGV4dC1ncmF5LTkwMCcgOiAndGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHshbm90aWZpY2F0aW9uLmlzUmVhZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgbWwtMiBtdC0xXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtdC0xIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbi5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVBZ28obm90aWZpY2F0aW9uLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyB1c2VyIHJvbGUgZm9yIHRyYWRlIG5vdGlmaWNhdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICd0cmFkZScgJiYgZ2V0VXNlclJvbGVJblRyYWRlKG5vdGlmaWNhdGlvbikgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0wLjUgcm91bmRlZCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0VXNlclJvbGVJblRyYWRlKG5vdGlmaWNhdGlvbikgPT09ICdidXllcidcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tMTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXB1cnBsZS02MDAgYmctcHVycGxlLTEwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0VXNlclJvbGVJblRyYWRlKG5vdGlmaWNhdGlvbil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyB0cmFkZSBJRCBmb3IgdHJhZGUgbm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3RyYWRlJyAmJiBub3RpZmljYXRpb24uZGF0YT8udHJhZGVJZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtMTAwIHB4LTIgcHktMC41IHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyYWRlICN7bm90aWZpY2F0aW9uLmRhdGEudHJhZGVJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRm9vdGVyICovfVxuICAgICAgICAgIHsoc3lzdGVtTm90aWZpY2F0aW9ucy5sZW5ndGggPiAwIHx8IHRyYWRlTm90aWZpY2F0aW9ucy5sZW5ndGggPiAwKSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMyBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHNldElzT3BlbihmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAvLyBOYXZpZ2F0ZSB0byBmdWxsIG5vdGlmaWNhdGlvbnMgcGFnZSBpZiB5b3UgaGF2ZSBvbmVcbiAgICAgICAgICAgICAgICAgIC8vIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9ub3RpZmljYXRpb25zJztcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBWaWV3IGFsbCB7YWN0aXZlVGFifSBub3RpZmljYXRpb25zXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VQcml2eSIsImdldE5vdGlmaWNhdGlvbnMiLCJnZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCIsIm1hcmtOb3RpZmljYXRpb25Bc1JlYWQiLCJtYXJrQWxsTm90aWZpY2F0aW9uc0FzUmVhZCIsInJlbGVhc2VQZXJrIiwicmVmdW5kUGVyayIsInVzZUdsb2JhbE1vZGFsIiwidXNlV2FsbGV0IiwidXNlRXNjcm93T3BlcmF0aW9ucyIsIkNoYXRNb2RhbCIsIlJlYWxUaW1lTm90aWZpY2F0aW9uTGlzdGVuZXIiLCJBUElfQ09ORklHIiwiTm90aWZpY2F0aW9uQmVsbCIsInN5c3RlbU5vdGlmaWNhdGlvbnMiLCJzZXRTeXN0ZW1Ob3RpZmljYXRpb25zIiwidHJhZGVOb3RpZmljYXRpb25zIiwic2V0VHJhZGVOb3RpZmljYXRpb25zIiwic3lzdGVtVW5yZWFkQ291bnQiLCJzZXRTeXN0ZW1VbnJlYWRDb3VudCIsInRyYWRlVW5yZWFkQ291bnQiLCJzZXRUcmFkZVVucmVhZENvdW50IiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaXNPcGVuIiwic2V0SXNPcGVuIiwibG9hZGluZyIsInNldExvYWRpbmciLCJkcm9wZG93blJlZiIsInVzZXIiLCJvcGVuTW9kYWwiLCJjbG9zZU1vZGFsIiwic29sYW5hV2FsbGV0IiwiaXNDb25uZWN0ZWQiLCJlc2Nyb3dPcGVyYXRpb25zIiwibG9hZE5vdGlmaWNhdGlvbnMiLCJub3RpZmljYXRpb25zUmVzcG9uc2UiLCJ1bnJlYWRSZXNwb25zZSIsIlByb21pc2UiLCJhbGwiLCJwYWdlIiwicGFnZVNpemUiLCJzdGF0dXMiLCJhbGxOb3RpZmljYXRpb25zIiwiZGF0YSIsIm5vdGlmaWNhdGlvbnMiLCJzeXN0ZW1Ob3RzIiwidHJhZGVOb3RzIiwiZm9yRWFjaCIsIm5vdGlmaWNhdGlvbiIsImlzVHJhZGVOb3RpZmljYXRpb24iLCJwdXNoIiwiY29uc29sZSIsImxvZyIsInRvdGFsTm90aWZpY2F0aW9ucyIsImxlbmd0aCIsInN5c3RlbVR5cGVzIiwibWFwIiwibiIsInR5cGUiLCJ0cmFkZVR5cGVzIiwic3lzdGVtVW5yZWFkIiwiZmlsdGVyIiwiaXNSZWFkIiwidHJhZGVVbnJlYWQiLCJlcnJvciIsImluY2x1ZGVzIiwiZ2V0VXNlclJvbGVJblRyYWRlIiwiYnV5ZXJJZCIsInNlbGxlcklkIiwidXNlcklkIiwiaWQiLCJ1c2VySWROdW1iZXIiLCJwYXJzZUludCIsImdlbmVyYXRlQ2hhdFJvb21JZCIsInBlcmtJZCIsInNob3VsZE9wZW5DaGF0TW9kYWwiLCJjaGF0VHlwZXMiLCJjaGF0Um9vbUlkIiwidHJhZGVJZCIsImNyZWF0ZVJlbGVhc2VGdW5jdGlvbiIsImFkZHJlc3MiLCJ3YWxsZXQiLCJjb25uZWN0ZWQiLCJ0cmFkZSIsInRyYWRlUmVzcG9uc2UiLCJmZXRjaCIsIkJBU0VfVVJMIiwiaGVhZGVycyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJvayIsIkVycm9yIiwidHJhZGVEYXRhIiwianNvbiIsImVzY3Jvd0lkIiwidG9rZW5EZXRhaWxzIiwicGVya1Rva2VuTWludCIsInRvIiwiZnJvbSIsImV4ZWN1dGVTZWxsIiwidG9rZW5BZGRyZXNzIiwidG9TdHJpbmciLCJzZWxsZXJXYWxsZXQiLCJhbGVydCIsImNyZWF0ZVJlZnVuZEZ1bmN0aW9uIiwiZXhlY3V0ZVJlZnVuZCIsImJ1eWVyV2FsbGV0IiwiY3JlYXRlQWNjZXB0RnVuY3Rpb24iLCJjcmVhdGVFc2Nyb3dBY2NlcHRUcmFuc2FjdGlvbiIsInR4SWQiLCJhY2NlcHRSZXNwb25zZSIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYWNjZXB0ZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsIm9wZW5DaGF0TW9kYWwiLCJ1c2VyQm9TdHIiLCJ1c2VyQm8iLCJwYXJzZSIsIm15VXNlcklkIiwicmVjZWl2ZXJJZCIsInNlbmRlcklkIiwibm90aWZpY2F0aW9uVHlwZSIsIm5vdGlmaWNhdGlvbkRhdGEiLCJjdXJyZW50VXNlciIsImFjdGl2ZVRyYWRlIiwidW5kZWZpbmVkIiwidHJhZGVJZFRvVXNlIiwiY2hhdFJvb21QYXJ0cyIsInNwbGl0IiwicmVzcG9uc2UiLCJ0cmFkZXNEYXRhIiwiYWN0aXZlVHJhZGVzIiwiZ2V0VHJhZGVEZXRhaWxzIiwiTnVtYmVyIiwicGVya0RldGFpbHMiLCJjcmVhdGVkQXQiLCJkaXNwdXRlU3RhdHVzIiwiZGlzcHV0ZSIsImJ1eWVyIiwic2VsbGVyIiwibW9kYWxJZCIsImV4aXN0aW5nTW9kYWwiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImNvbXBvbmVudCIsImNyZWF0ZUVsZW1lbnQiLCJvbkNsb3NlIiwib25SZWxlYXNlIiwib25SZWZ1bmQiLCJvblJlcG9ydCIsIm9uQWNjZXB0IiwiY2xvc2VPbkJhY2tkcm9wQ2xpY2siLCJjbG9zZU9uRXNjYXBlIiwicHJldmVudENsb3NlIiwiekluZGV4IiwiYmFja2Ryb3BDbGFzc05hbWUiLCJtb2RhbENsYXNzTmFtZSIsImRpc2FibGVTY3JvbGwiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVOb3RpZmljYXRpb25DbGljayIsInNob3VsZE9wZW5DaGF0IiwiYWN0aW9uVXJsIiwicHJldiIsIk1hdGgiLCJtYXgiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJoYW5kbGVNYXJrQWxsQXNSZWFkIiwiaGFuZGxlTWFya1RhYkFzUmVhZCIsInRhYlR5cGUiLCJ1bnJlYWROb3RpZmljYXRpb25zIiwiZ2V0Tm90aWZpY2F0aW9uSWNvbiIsImRpdiIsImNsYXNzTmFtZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImZvcm1hdFRpbWVBZ28iLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIm5vdyIsImRpZmZJblNlY29uZHMiLCJmbG9vciIsImdldFRpbWUiLCJ0b3RhbFVucmVhZENvdW50IiwiY3VycmVudE5vdGlmaWNhdGlvbnMiLCJjdXJyZW50VW5yZWFkQ291bnQiLCJyZWYiLCJvbk5ld05vdGlmaWNhdGlvbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwiaDMiLCJwIiwidGl0bGUiLCJtZXNzYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/notifications/NotificationBell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e15d22420ce5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTE1ZDIyNDIwY2U1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});