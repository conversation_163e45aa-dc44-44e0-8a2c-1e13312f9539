import React from "react";
import { useTranslation } from '../../../hooks/useTranslation';

const PasswordSection = ({
  passwordData,
  setPasswordData,
}: {
  passwordData: {
    value: {
      oldPassword: string;
      newPassword: string;
      confirmPassword: string;
    };
    errors: {
      oldPassword: string;
      newPassword: string;
      confirmPassword: string;
    };
  };
  setPasswordData: React.Dispatch<
    React.SetStateAction<{
      value: {
        oldPassword: string;
        newPassword: string;
        confirmPassword: string;
      };
      errors: {
        oldPassword: string;
        newPassword: string;
        confirmPassword: string;
      };
    }>
  >;
}) => {
  const { t } = useTranslation();
  const validatePassword = (password: string) => {
    // Password should be at least 8 characters, contain uppercase, lowercase, number, and special character
    const strongPasswordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(password);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      value: {
        ...prev.value,
        [name]: value,
      },
    }));

    // Clear errors when typing
    if (passwordData.errors[name as keyof typeof passwordData.errors]) {
      setPasswordData((prev) => ({
        ...prev,
        errors: { ...prev.errors, [name]: "" },
      }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Validate fields
    if (name === "newPassword") {
      if (value.trim() && !validatePassword(value)) {
        setPasswordData((prev) => ({
          ...prev,
          errors: {
            ...prev.errors,
            newPassword: t('password.validationStrong'),
          },
        }));
      }
    } else if (name === "confirmPassword") {
      if (value.trim() && value !== passwordData.value.newPassword) {
        setPasswordData((prev) => ({
          ...prev,
          errors: { ...prev.errors, confirmPassword: t('password.validationMatch') },
        }));
      }
    }
  };

  return (
    <div>
      <h2 className="mt-20 font-['IBM_Plex_Sans'] font-semibold text-[32px] leading-[36px] text-[#17181A] mb-8">
        {t('password.sectionTitle')}
      </h2>

      <div className="space-y-6">
        {/* Old Password Field */}
        <div className="flex flex-wrap gap-6">
          <div className="w-full lg:flex-1 min-w-[290px]">
            <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-[#17181A] mb-4">
              {t('password.oldPassword')}
            </label>
            <input
              name="oldPassword"
              type="password"
              value={passwordData.value.oldPassword}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full h-[54px] border ${
                passwordData.errors.oldPassword
                  ? "border-red-500"
                  : "border-[#D9E1E7]"
              } rounded-[10px] px-4 font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8] focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder={t('password.inputPlaceholder')}
            />
            {passwordData.errors.oldPassword && (
              <p className="mt-1 text-red-500 text-sm">
                {passwordData.errors.oldPassword}
              </p>
            )}
          </div>
          <div className="hidden md:flex flex-1" />
        </div>

        <div className="flex flex-wrap gap-6">
          {/* New Password Field */}
          <div className="flex-1 min-w-[290px]">
            <label className="block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-[#17181A] mb-4">
              {t('password.newPassword')}
            </label>
            <input
              name="newPassword"
              type="password"
              value={passwordData.value.newPassword}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full h-[54px] border ${
                passwordData.errors.newPassword
                  ? "border-red-500"
                  : "border-[#D9E1E7]"
              } rounded-[10px] px-4 font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8] focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder={t('password.inputPlaceholder')}
            />
            {passwordData.errors.newPassword && (
              <p className="mt-1 text-red-500 text-sm">
                {passwordData.errors.newPassword}
              </p>
            )}
          </div>

          {/* Repeat Password Field */}
          <div className="flex-1 min-w-[290px]">
            <label className="whitespace-nowrap block font-['IBM_Plex_Sans'] font-semibold text-[20px] leading-[100%] text-[#17181A] mb-4">
              {t('password.repeatNewPassword')}
            </label>
            <input
              name="confirmPassword"
              type="password"
              value={passwordData.value.confirmPassword}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full h-[54px] border ${
                passwordData.errors.confirmPassword
                  ? "border-red-500"
                  : "border-[#D9E1E7]"
              } rounded-[10px] px-4 font-['IBM_Plex_Sans'] font-semibold text-[16px] leading-[19px] text-[#809FB8] focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder={t('password.inputPlaceholder')}
            />
            {passwordData.errors.confirmPassword && (
              <p className="mt-1 text-red-500 text-sm">
                {passwordData.errors.confirmPassword}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordSection;
