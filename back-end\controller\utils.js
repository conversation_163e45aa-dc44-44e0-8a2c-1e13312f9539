const { validateSolAddress } = require('../utilities/utils')
exports.validateSol = async (req, res) => {
    try {
        const { addr } = req.params;
        const isValid = validateSolAddress(addr)
        return res.status(200).json({
            message: 'success',
            data: isValid
        })

    }catch(e){
        console.error(e)
        return res.status(200).json({
            message: 'error',
            error: e
        })
    }
}