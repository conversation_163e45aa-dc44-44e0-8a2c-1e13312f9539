import React from 'react';

import { FormData, FormErrors } from './FormSubmission';
import {
  validateName,
  validatePrice,
  validateFulfillmentLink,
  validateStockAmount,
  validateCategory,
} from './FormValidation';
import { useGenericFormHandler } from '../../../hooks/useGenericFormHandler';
import { useFileUpload } from '../../../hooks/useFileUpload';
import { UPLOAD_CONFIG } from '@/config/environment';

export const useFormHandlers = (
  formData: FormData,
  setFormData: React.Dispatch<React.SetStateAction<FormData>>,
  errors: FormErrors,
  setErrors: React.Dispatch<React.SetStateAction<FormErrors>>,
  fileInputId: string = 'picture-upload'
) => {
  // Define validation rules for the form
  const validationRules = {
    name: (value: string) => validateName(value),
    price: (value: string) => validatePrice(value),
    fulfillmentLink: (value: string) => validateFulfillmentLink(value),
    stockAmount: (value: string) =>
      validateStockAmount(value, formData.limitedStock),
    category: (value: string) => validateCategory(value),
  };

  // Use generic form handler with type assertions for compatibility
  const { handleChange, handleCheckboxChange, handleBlur } =
    useGenericFormHandler({
      formData,
      setFormData,
      errors: errors as any,
      setErrors: setErrors as any,
      validationRules,
    });

  // Use file upload handler with type assertions for compatibility
  const { handleFileChange, handleDeleteFile } = useFileUpload({
    formData,
    setFormData,
    errors: errors as any,
    setErrors: setErrors as any,
    fieldName: 'picture',
    errorFieldName: 'picture',
    options: {
      fileInputId,
      maxSize: UPLOAD_CONFIG.MAX_FILE_SIZE,
      allowedTypes: UPLOAD_CONFIG.ALLOWED_FILE_TYPES,
    },
  });

  // Maintain backward compatibility by renaming the delete function
  const handleDeletePicture = handleDeleteFile;

  return {
    handleChange,
    handleCheckboxChange,
    handleBlur,
    handleFileChange,
    handleDeletePicture,
  };
};
