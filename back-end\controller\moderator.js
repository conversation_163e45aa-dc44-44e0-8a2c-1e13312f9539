const dataContext = require("../db");
const { Op } = require("sequelize");

/**
 * Add a new moderator (Admin only)
 */
exports.addModerator = async (req, res, next) => {
  try {
    const { userId, specializations, maxConcurrentDisputes = 10 } = req.body;
    const addedBy = req.user.id;

    // TODO: Add admin check here
    // For now, we'll assume the requesting user has admin privileges

    // Check if user exists
    const user = await dataContext.User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ status: 404, message: 'User not found' });
    }

    // Check if user is already a moderator
    const existingModerator = await dataContext.Moderator.findOne({ where: { userId } });
    if (existingModerator) {
      return res.status(400).json({ 
        status: 400, 
        message: 'User is already a moderator' 
      });
    }

    // Create moderator
    const moderator = await dataContext.Moderator.create({
      userId,
      specializations,
      maxConcurrentDisputes,
      addedBy
    });

    // Create system notification for new moderator (broadcast to all users)
    try {
      await dataContext.Notification.createSystemNotificationForAllUsers(
        'moderator_added',
        'New Moderator Joined!',
        `${user.username || 'A new user'} has joined as a moderator to help resolve disputes and maintain platform quality.`,
        {
          moderatorId: moderator.id,
          userId,
          username: user.username,
          specializations
        },
        'medium',
        null
      );
    } catch (error) {
      console.error('Failed to create moderator addition notification:', error);
      // Don't fail the moderator creation if notification fails
    }

    res.status(201).json({
      status: 201,
      message: 'Moderator added successfully',
      data: {
        moderatorId: moderator.id,
        userId,
        username: user.username
      }
    });

  } catch (error) {
    console.error('Add moderator error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get all moderators
 */
exports.getModerators = async (req, res, next) => {
  try {
    const { isActive, page = 1, pageSize = 20 } = req.query;

    // Build query conditions
    const whereConditions = {};
    if (isActive !== undefined) {
      whereConditions.isActive = isActive === 'true';
    }

    const offset = (page - 1) * pageSize;
    const moderators = await dataContext.Moderator.findAndCountAll({
      where: whereConditions,
      include: [
        { 
          model: dataContext.User, 
          as: 'user', 
          attributes: ['id', 'username', 'email', 'privywallet'] 
        },
        { 
          model: dataContext.User, 
          as: 'addedByUser', 
          attributes: ['id', 'username'] 
        }
      ],
      order: [['reputationScore', 'DESC']],
      limit: parseInt(pageSize),
      offset: offset
    });

    res.status(200).json({
      status: 200,
      message: 'Moderators retrieved successfully',
      data: {
        moderators: moderators.rows,
        pagination: {
          total: moderators.count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(moderators.count / pageSize)
        }
      }
    });

  } catch (error) {
    console.error('Get moderators error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get moderator dashboard stats
 */
exports.getModeratorDashboard = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Check if user is a moderator
    const moderator = await dataContext.Moderator.findOne({ 
      where: { userId, isActive: true },
      include: [
        { model: dataContext.User, as: 'user', attributes: ['id', 'username'] }
      ]
    });
    
    if (!moderator) {
      return res.status(403).json({ 
        status: 403, 
        message: 'Access denied. Moderator privileges required.' 
      });
    }

    // Get dispute statistics
    const disputeStats = await dataContext.Dispute.findAll({
      attributes: [
        'status',
        [dataContext.sequelize.fn('COUNT', dataContext.sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Get moderator's active disputes
    const activeDisputes = await dataContext.Dispute.findAll({
      where: { 
        moderatorId: userId, 
        status: 'assigned' 
      },
      include: [
        {
          model: dataContext.TokenPurchased,
          as: 'trade',
          include: [
            { model: dataContext.User, as: 'user', attributes: ['id', 'username'] },
            { 
              model: dataContext.Perk, 
              as: 'perkDetails',
              attributes: ['perkId', 'name', 'price'],
              include: [{ model: dataContext.User, as: 'user', attributes: ['id', 'username'] }]
            }
          ]
        }
      ],
      order: [['createdAt', 'ASC']]
    });

    // Get recent resolved disputes by this moderator
    const recentResolutions = await dataContext.Dispute.findAll({
      where: { 
        moderatorId: userId, 
        status: { [Op.in]: ['resolved_buyer', 'resolved_seller', 'resolved_split'] }
      },
      include: [
        {
          model: dataContext.TokenPurchased,
          as: 'trade',
          include: [
            { model: dataContext.User, as: 'user', attributes: ['id', 'username'] },
            { 
              model: dataContext.Perk, 
              as: 'perkDetails',
              attributes: ['perkId', 'name', 'price']
            }
          ]
        }
      ],
      order: [['resolvedAt', 'DESC']],
      limit: 10
    });

    // Calculate average resolution time
    const resolvedDisputes = await dataContext.Dispute.findAll({
      where: { 
        moderatorId: userId, 
        resolvedAt: { [Op.not]: null }
      },
      attributes: ['createdAt', 'resolvedAt']
    });

    let averageResolutionHours = 0;
    if (resolvedDisputes.length > 0) {
      const totalHours = resolvedDisputes.reduce((sum, dispute) => {
        const hours = (new Date(dispute.resolvedAt) - new Date(dispute.createdAt)) / (1000 * 60 * 60);
        return sum + hours;
      }, 0);
      averageResolutionHours = totalHours / resolvedDisputes.length;
    }

    res.status(200).json({
      status: 200,
      message: 'Moderator dashboard retrieved successfully',
      data: {
        moderator: {
          id: moderator.id,
          username: moderator.user.username,
          reputationScore: moderator.reputationScore,
          totalDisputesHandled: moderator.totalDisputesHandled,
          totalDisputesResolved: moderator.totalDisputesResolved,
          currentActiveDisputes: moderator.currentActiveDisputes,
          maxConcurrentDisputes: moderator.maxConcurrentDisputes,
          averageResolutionHours: Math.round(averageResolutionHours * 100) / 100
        },
        disputeStats: disputeStats.reduce((acc, stat) => {
          acc[stat.status] = parseInt(stat.count);
          return acc;
        }, {}),
        activeDisputes,
        recentResolutions
      }
    });

  } catch (error) {
    console.error('Get moderator dashboard error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Update moderator status (activate/deactivate)
 */
exports.updateModeratorStatus = async (req, res, next) => {
  try {
    const { moderatorId } = req.params;
    const { isActive } = req.body;

    // TODO: Add admin check here

    const moderator = await dataContext.Moderator.findByPk(moderatorId);
    if (!moderator) {
      return res.status(404).json({ status: 404, message: 'Moderator not found' });
    }

    await moderator.update({ isActive });

    res.status(200).json({
      status: 200,
      message: `Moderator ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { moderatorId, isActive }
    });

  } catch (error) {
    console.error('Update moderator status error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};
