"use client";

import React, { useState, useCallback } from 'react';
import { ErrorModal, ErrorCategory } from './ErrorModals';
import { EscrowLoadingModal } from './LoadingComponents';
import { DetectedError } from '@/utils/errorDetection';
import { EscrowOperationType } from '@/utils/escrow';

// Error Manager State
interface ErrorState {
  isOpen: boolean;
  error: DetectedError | null;
  operation: EscrowOperationType | null;
}

interface LoadingState {
  isOpen: boolean;
  operation: EscrowOperationType | null;
  currentStep: number;
  customMessage?: string;
}

// Error Manager Props
interface EscrowErrorManagerProps {
  onRetry?: (operation: EscrowOperationType) => void;
  onHelp?: (category: ErrorCategory, operation: EscrowOperationType) => void;
  onProceedAnyway?: (operation: EscrowOperationType) => void;
  onWalletReconnect?: () => void;
  onNetworkSwitch?: () => void;
}

// Error Manager Hook
export const useEscrowErrorManager = ({
  onRetry,
  onHelp,
  onProceedAnyway,
  onWalletReconnect,
  onNetworkSwitch
}: EscrowErrorManagerProps = {}) => {
  const [errorState, setErrorState] = useState<ErrorState>({
    isOpen: false,
    error: null,
    operation: null
  });

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isOpen: false,
    operation: null,
    currentStep: 0
  });

  // Show error modal
  const showError = useCallback((
    error: DetectedError,
    operation: EscrowOperationType
  ) => {
    setErrorState({
      isOpen: true,
      error,
      operation
    });
  }, []);

  // Hide error modal
  const hideError = useCallback(() => {
    setErrorState({
      isOpen: false,
      error: null,
      operation: null
    });
  }, []);

  // Show loading modal
  const showLoading = useCallback((
    operation: EscrowOperationType,
    currentStep: number = 0,
    customMessage?: string
  ) => {
    setLoadingState({
      isOpen: true,
      operation,
      currentStep,
      customMessage
    });
  }, []);

  // Update loading step
  const updateLoadingStep = useCallback((step: number, customMessage?: string) => {
    setLoadingState(prev => ({
      ...prev,
      currentStep: step,
      customMessage: customMessage || prev.customMessage
    }));
  }, []);

  // Hide loading modal
  const hideLoading = useCallback(() => {
    setLoadingState({
      isOpen: false,
      operation: null,
      currentStep: 0
    });
  }, []);

  // Handle retry action
  const handleRetry = useCallback(() => {
    if (errorState.operation && onRetry) {
      hideError();
      onRetry(errorState.operation);
    }
  }, [errorState.operation, onRetry, hideError]);

  // Handle help action
  const handleHelp = useCallback(() => {
    if (errorState.error && errorState.operation && onHelp) {
      onHelp(errorState.error.category, errorState.operation);
    }
  }, [errorState.error, errorState.operation, onHelp]);

  // Handle proceed anyway action
  const handleProceedAnyway = useCallback(() => {
    if (errorState.operation && onProceedAnyway) {
      hideError();
      onProceedAnyway(errorState.operation);
    }
  }, [errorState.operation, onProceedAnyway, hideError]);

  // Handle wallet-specific actions
  const handleWalletReconnect = useCallback(() => {
    if (onWalletReconnect) {
      hideError();
      onWalletReconnect();
    }
  }, [onWalletReconnect, hideError]);

  // Handle network-specific actions
  const handleNetworkSwitch = useCallback(() => {
    if (onNetworkSwitch) {
      hideError();
      onNetworkSwitch();
    }
  }, [onNetworkSwitch, hideError]);

  // Get additional info for error modal
  const getAdditionalInfo = useCallback(() => {
    if (!errorState.error) return undefined;

    return {
      currentBalance: errorState.error.additionalInfo?.currentBalance,
      requiredAmount: errorState.error.additionalInfo?.requiredAmount,
      walletAddress: errorState.error.additionalInfo?.walletAddress,
      transactionId: errorState.error.additionalInfo?.transactionId
    };
  }, [errorState.error]);

  // Render error modal
  const ErrorModalComponent = () => {
    if (!errorState.isOpen || !errorState.error || !errorState.operation) {
      return null;
    }

    return (
      <ErrorModal
        isOpen={errorState.isOpen}
        onClose={hideError}
        category={errorState.error.category}
        operation={errorState.operation}
        message={errorState.error.userFriendlyMessage}
        details={errorState.error.details}
        errorCode={errorState.error.errorCode}
        onRetry={errorState.error.canRetry ? handleRetry : undefined}
        onHelp={handleHelp}
        onProceedAnyway={errorState.error.category === 'INSUFFICIENT_FUNDS' ? handleProceedAnyway : undefined}
        canRetry={errorState.error.canRetry}
        canProceedAnyway={errorState.error.category === 'INSUFFICIENT_FUNDS'}
        additionalInfo={getAdditionalInfo()}
      />
    );
  };

  // Render loading modal
  const LoadingModalComponent = () => {
    if (!loadingState.isOpen || !loadingState.operation) {
      return null;
    }

    return (
      <EscrowLoadingModal
        isOpen={loadingState.isOpen}
        operation={loadingState.operation}
        currentStep={loadingState.currentStep}
        customMessage={loadingState.customMessage}
        showSteps={true}
        onCancel={undefined} // Can be added if needed
        canCancel={false}
      />
    );
  };

  return {
    // State
    errorState,
    loadingState,
    
    // Actions
    showError,
    hideError,
    showLoading,
    updateLoadingStep,
    hideLoading,
    
    // Handlers
    handleRetry,
    handleHelp,
    handleWalletReconnect,
    handleNetworkSwitch,
    
    // Components
    ErrorModalComponent,
    LoadingModalComponent
  };
};

// Standalone Error Manager Component
interface EscrowErrorManagerComponentProps extends EscrowErrorManagerProps {
  children: (manager: ReturnType<typeof useEscrowErrorManager>) => React.ReactNode;
}

export const EscrowErrorManagerComponent: React.FC<EscrowErrorManagerComponentProps> = ({
  children,
  ...props
}) => {
  const manager = useEscrowErrorManager(props);

  return (
    <>
      {children(manager)}
      <manager.ErrorModalComponent />
      <manager.LoadingModalComponent />
    </>
  );
};

// Helper function to show error with automatic detection
export const showEscrowError = (
  manager: ReturnType<typeof useEscrowErrorManager>,
  error: Error | string,
  operation: EscrowOperationType,
  context?: any
) => {
  // Import detectAndEnhanceError dynamically to avoid circular dependencies
  import('@/utils/errorDetection').then(({ detectAndEnhanceError }) => {
    const detectedError = detectAndEnhanceError(error, operation, context);
    manager.showError(detectedError, operation);
  });
};

// Helper function to show loading with operation-specific configuration
export const showEscrowLoading = (
  manager: ReturnType<typeof useEscrowErrorManager>,
  operation: EscrowOperationType,
  step: number = 0,
  customMessage?: string
) => {
  manager.showLoading(operation, step, customMessage);
};

// Helper function to update loading progress
export const updateEscrowLoadingProgress = (
  manager: ReturnType<typeof useEscrowErrorManager>,
  step: number,
  customMessage?: string
) => {
  manager.updateLoadingStep(step, customMessage);
};

// Helper function to hide all modals
export const hideAllEscrowModals = (
  manager: ReturnType<typeof useEscrowErrorManager>
) => {
  manager.hideError();
  manager.hideLoading();
};
