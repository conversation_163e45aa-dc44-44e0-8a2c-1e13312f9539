"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_shared_product-card_index_tsx",{

/***/ "(app-pages-browser)/./src/contexts/GlobalModalContext.tsx":
/*!*********************************************!*\
  !*** ./src/contexts/GlobalModalContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalModalProvider: () => (/* binding */ GlobalModalProvider),\n/* harmony export */   useGlobalModal: () => (/* binding */ useGlobalModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! body-scroll-lock */ \"(app-pages-browser)/./node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js\");\n/* __next_internal_client_entry_do_not_use__ GlobalModalProvider,useGlobalModal auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst GlobalModalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GlobalModalProvider(param) {\n    let { children, defaultZIndex = 1000 } = param;\n    _s();\n    const [modals, setModals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const nextZIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultZIndex);\n    const modalRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // Generate unique modal ID\n    const generateModalId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[generateModalId]\": ()=>{\n            return \"global-modal-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n        }\n    }[\"GlobalModalProvider.useCallback[generateModalId]\"], []);\n    // Open modal\n    const openModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[openModal]\": (config)=>{\n            var _config_id;\n            const id = (_config_id = config.id) !== null && _config_id !== void 0 ? _config_id : generateModalId();\n            const zIndex = config.zIndex || nextZIndex.current;\n            var _config_closeOnBackdropClick, _config_closeOnEscape, _config_preventClose, _config_disableScroll;\n            const modalConfig = {\n                ...config,\n                id,\n                zIndex,\n                closeOnBackdropClick: (_config_closeOnBackdropClick = config.closeOnBackdropClick) !== null && _config_closeOnBackdropClick !== void 0 ? _config_closeOnBackdropClick : true,\n                closeOnEscape: (_config_closeOnEscape = config.closeOnEscape) !== null && _config_closeOnEscape !== void 0 ? _config_closeOnEscape : true,\n                preventClose: (_config_preventClose = config.preventClose) !== null && _config_preventClose !== void 0 ? _config_preventClose : false,\n                disableScroll: (_config_disableScroll = config.disableScroll) !== null && _config_disableScroll !== void 0 ? _config_disableScroll : true\n            };\n            setModals({\n                \"GlobalModalProvider.useCallback[openModal]\": (prev)=>[\n                        ...prev,\n                        modalConfig\n                    ]\n            }[\"GlobalModalProvider.useCallback[openModal]\"]);\n            nextZIndex.current = zIndex + 1;\n            return id;\n        }\n    }[\"GlobalModalProvider.useCallback[openModal]\"], [\n        generateModalId\n    ]);\n    // Close modal\n    const closeModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[closeModal]\": (id)=>{\n            setModals({\n                \"GlobalModalProvider.useCallback[closeModal]\": (prev)=>{\n                    const newModals = prev.filter({\n                        \"GlobalModalProvider.useCallback[closeModal].newModals\": (modal)=>modal.id !== id\n                    }[\"GlobalModalProvider.useCallback[closeModal].newModals\"]);\n                    // Enable body scroll if no modals are open\n                    if (newModals.length === 0) {\n                        (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.enableBodyScroll)(document.body);\n                    }\n                    return newModals;\n                }\n            }[\"GlobalModalProvider.useCallback[closeModal]\"]);\n            // Clean up modal ref\n            modalRefs.current.delete(id);\n        }\n    }[\"GlobalModalProvider.useCallback[closeModal]\"], []);\n    // Close all modals\n    const closeAllModals = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[closeAllModals]\": ()=>{\n            setModals([]);\n            (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.clearAllBodyScrollLocks)();\n            modalRefs.current.clear();\n        }\n    }[\"GlobalModalProvider.useCallback[closeAllModals]\"], []);\n    // Check if modal is open\n    const isModalOpen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[isModalOpen]\": (id)=>{\n            return modals.some({\n                \"GlobalModalProvider.useCallback[isModalOpen]\": (modal)=>modal.id === id\n            }[\"GlobalModalProvider.useCallback[isModalOpen]\"]);\n        }\n    }[\"GlobalModalProvider.useCallback[isModalOpen]\"], [\n        modals\n    ]);\n    // Get modal by ID\n    const getModalById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[getModalById]\": (id)=>{\n            return modals.find({\n                \"GlobalModalProvider.useCallback[getModalById]\": (modal)=>modal.id === id\n            }[\"GlobalModalProvider.useCallback[getModalById]\"]);\n        }\n    }[\"GlobalModalProvider.useCallback[getModalById]\"], [\n        modals\n    ]);\n    // Update existing modal\n    const updateModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[updateModal]\": (id, updates)=>{\n            const modalIndex = modals.findIndex({\n                \"GlobalModalProvider.useCallback[updateModal].modalIndex\": (modal)=>modal.id === id\n            }[\"GlobalModalProvider.useCallback[updateModal].modalIndex\"]);\n            if (modalIndex === -1) {\n                console.log(\"[GlobalModalContext] Modal with id \".concat(id, \" not found for update\"));\n                return false;\n            }\n            setModals({\n                \"GlobalModalProvider.useCallback[updateModal]\": (prev)=>{\n                    const newModals = [\n                        ...prev\n                    ];\n                    newModals[modalIndex] = {\n                        ...newModals[modalIndex],\n                        ...updates\n                    };\n                    console.log(\"[GlobalModalContext] Updated modal \".concat(id, \" with:\"), updates);\n                    return newModals;\n                }\n            }[\"GlobalModalProvider.useCallback[updateModal]\"]);\n            return true;\n        }\n    }[\"GlobalModalProvider.useCallback[updateModal]\"], [\n        modals\n    ]);\n    // Handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalModalProvider.useEffect\": ()=>{\n            const handleEscape = {\n                \"GlobalModalProvider.useEffect.handleEscape\": (event)=>{\n                    if (event.key === 'Escape' && modals.length > 0) {\n                        const topModal = modals[modals.length - 1];\n                        if (topModal.closeOnEscape && !topModal.preventClose) {\n                            var _topModal_onClose;\n                            var _topModal_id;\n                            closeModal((_topModal_id = topModal.id) !== null && _topModal_id !== void 0 ? _topModal_id : \"\");\n                            (_topModal_onClose = topModal.onClose) === null || _topModal_onClose === void 0 ? void 0 : _topModal_onClose.call(topModal);\n                        }\n                    }\n                }\n            }[\"GlobalModalProvider.useEffect.handleEscape\"];\n            if (modals.length > 0) {\n                document.addEventListener('keydown', handleEscape);\n            }\n            return ({\n                \"GlobalModalProvider.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                }\n            })[\"GlobalModalProvider.useEffect\"];\n        }\n    }[\"GlobalModalProvider.useEffect\"], [\n        modals,\n        closeModal\n    ]);\n    // Handle backdrop click\n    const handleBackdropClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[handleBackdropClick]\": (modal, event)=>{\n            console.log(\"modal\", modal);\n            if (event.target === event.currentTarget && modal.closeOnBackdropClick && !modal.preventClose) {\n                var _modal_onClose;\n                var _modal_id;\n                closeModal((_modal_id = modal.id) !== null && _modal_id !== void 0 ? _modal_id : \"\");\n                (_modal_onClose = modal.onClose) === null || _modal_onClose === void 0 ? void 0 : _modal_onClose.call(modal);\n            }\n        }\n    }[\"GlobalModalProvider.useCallback[handleBackdropClick]\"], [\n        closeModal\n    ]);\n    // Handle modal ref for scroll lock\n    const handleModalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GlobalModalProvider.useCallback[handleModalRef]\": (modalId, element)=>{\n            if (element) {\n                modalRefs.current.set(modalId, element);\n                const modal = getModalById(modalId);\n                if (modal === null || modal === void 0 ? void 0 : modal.disableScroll) {\n                    (0,body_scroll_lock__WEBPACK_IMPORTED_MODULE_3__.disableBodyScroll)(element);\n                }\n            }\n        }\n    }[\"GlobalModalProvider.useCallback[handleModalRef]\"], [\n        getModalById\n    ]);\n    const value = {\n        modals,\n        openModal,\n        closeModal,\n        closeAllModals,\n        isModalOpen,\n        getModalById,\n        updateModal\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlobalModalContext.Provider, {\n        value: value,\n        children: [\n            children,\n            modals.length > 0 && \"object\" !== 'undefined' && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"global-modal-portal\",\n                children: modals.map((modal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 flex items-center justify-center \".concat(modal.backdropClassName || 'bg-black/50 backdrop-blur-sm'),\n                        style: {\n                            zIndex: modal.zIndex\n                        },\n                        onClick: (e)=>handleBackdropClick(modal, e),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: (el)=>{\n                                var _modal_id;\n                                return handleModalRef((_modal_id = modal.id) !== null && _modal_id !== void 0 ? _modal_id : \"\", el);\n                            },\n                            className: modal.modalClassName || '',\n                            onClick: (e)=>e.stopPropagation(),\n                            children: modal.component\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this)\n                    }, modal.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this), document.body)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\GlobalModalContext.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(GlobalModalProvider, \"ZR1hIkqZdW4RF0Y8bHNq9mnglo0=\");\n_c = GlobalModalProvider;\nfunction useGlobalModal() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GlobalModalContext);\n    if (context === undefined) {\n        throw new Error('useGlobalModal must be used within a GlobalModalProvider');\n    }\n    return context;\n}\n_s1(useGlobalModal, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"GlobalModalProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9HbG9iYWxNb2RhbENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVtRztBQUMxRDtBQUN1RDtBQWdDaEcsTUFBTVcsbUNBQXFCVixvREFBYUEsQ0FBcUNXO0FBRXRFLFNBQVNDLG9CQUFvQixLQUE0RDtRQUE1RCxFQUFFQyxRQUFRLEVBQUVDLGdCQUFnQixJQUFJLEVBQTRCLEdBQTVEOztJQUNsQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2QsK0NBQVFBLENBQXNCLEVBQUU7SUFDNUQsTUFBTWUsYUFBYVosNkNBQU1BLENBQUNTO0lBQzFCLE1BQU1JLFlBQVliLDZDQUFNQSxDQUEyQixJQUFJYztJQUV2RCwyQkFBMkI7SUFDM0IsTUFBTUMsa0JBQWtCakIsa0RBQVdBOzREQUFDO1lBQ2xDLE9BQU8sZ0JBQThCa0IsT0FBZEMsS0FBS0MsR0FBRyxJQUFHLEtBQTJDLE9BQXhDRixLQUFLRyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztRQUM1RTsyREFBRyxFQUFFO0lBRUwsYUFBYTtJQUNiLE1BQU1DLFlBQVl4QixrREFBV0E7c0RBQUMsQ0FBQ3lCO2dCQUNsQkE7WUFBWCxNQUFNQyxLQUFLRCxDQUFBQSxhQUFBQSxPQUFPQyxFQUFFLGNBQVRELHdCQUFBQSxhQUFhUjtZQUN4QixNQUFNVSxTQUFTRixPQUFPRSxNQUFNLElBQUliLFdBQVdjLE9BQU87Z0JBTTFCSCw4QkFDUEEsdUJBQ0RBLHNCQUNDQTtZQVBqQixNQUFNSSxjQUFpQztnQkFDckMsR0FBR0osTUFBTTtnQkFDVEM7Z0JBQ0FDO2dCQUNBRyxzQkFBc0JMLENBQUFBLCtCQUFBQSxPQUFPSyxvQkFBb0IsY0FBM0JMLDBDQUFBQSwrQkFBK0I7Z0JBQ3JETSxlQUFlTixDQUFBQSx3QkFBQUEsT0FBT00sYUFBYSxjQUFwQk4sbUNBQUFBLHdCQUF3QjtnQkFDdkNPLGNBQWNQLENBQUFBLHVCQUFBQSxPQUFPTyxZQUFZLGNBQW5CUCxrQ0FBQUEsdUJBQXVCO2dCQUNyQ1EsZUFBZVIsQ0FBQUEsd0JBQUFBLE9BQU9RLGFBQWEsY0FBcEJSLG1DQUFBQSx3QkFBd0I7WUFDekM7WUFFQVo7OERBQVVxQixDQUFBQSxPQUFROzJCQUFJQTt3QkFBTUw7cUJBQVk7O1lBQ3hDZixXQUFXYyxPQUFPLEdBQUdELFNBQVM7WUFFOUIsT0FBT0Q7UUFDVDtxREFBRztRQUFDVDtLQUFnQjtJQUVwQixjQUFjO0lBQ2QsTUFBTWtCLGFBQWFuQyxrREFBV0E7dURBQUMsQ0FBQzBCO1lBQzlCYjsrREFBVXFCLENBQUFBO29CQUNSLE1BQU1FLFlBQVlGLEtBQUtHLE1BQU07aUZBQUNDLENBQUFBLFFBQVNBLE1BQU1aLEVBQUUsS0FBS0E7O29CQUVwRCwyQ0FBMkM7b0JBQzNDLElBQUlVLFVBQVVHLE1BQU0sS0FBSyxHQUFHO3dCQUMxQmxDLGtFQUFnQkEsQ0FBQ21DLFNBQVNDLElBQUk7b0JBQ2hDO29CQUVBLE9BQU9MO2dCQUNUOztZQUVBLHFCQUFxQjtZQUNyQnJCLFVBQVVhLE9BQU8sQ0FBQ2MsTUFBTSxDQUFDaEI7UUFDM0I7c0RBQUcsRUFBRTtJQUVMLG1CQUFtQjtJQUNuQixNQUFNaUIsaUJBQWlCM0Msa0RBQVdBOzJEQUFDO1lBQ2pDYSxVQUFVLEVBQUU7WUFDWlAseUVBQXVCQTtZQUN2QlMsVUFBVWEsT0FBTyxDQUFDZ0IsS0FBSztRQUN6QjswREFBRyxFQUFFO0lBRUwseUJBQXlCO0lBQ3pCLE1BQU1DLGNBQWM3QyxrREFBV0E7d0RBQUMsQ0FBQzBCO1lBQy9CLE9BQU9kLE9BQU9rQyxJQUFJO2dFQUFDUixDQUFBQSxRQUFTQSxNQUFNWixFQUFFLEtBQUtBOztRQUMzQzt1REFBRztRQUFDZDtLQUFPO0lBRVgsa0JBQWtCO0lBQ2xCLE1BQU1tQyxlQUFlL0Msa0RBQVdBO3lEQUFDLENBQUMwQjtZQUNoQyxPQUFPZCxPQUFPb0MsSUFBSTtpRUFBQ1YsQ0FBQUEsUUFBU0EsTUFBTVosRUFBRSxLQUFLQTs7UUFDM0M7d0RBQUc7UUFBQ2Q7S0FBTztJQUVYLHdCQUF3QjtJQUN4QixNQUFNcUMsY0FBY2pELGtEQUFXQTt3REFBQyxDQUFDMEIsSUFBWXdCO1lBQzNDLE1BQU1DLGFBQWF2QyxPQUFPd0MsU0FBUzsyRUFBQ2QsQ0FBQUEsUUFBU0EsTUFBTVosRUFBRSxLQUFLQTs7WUFDMUQsSUFBSXlCLGVBQWUsQ0FBQyxHQUFHO2dCQUNyQkUsUUFBUUMsR0FBRyxDQUFDLHNDQUF5QyxPQUFINUIsSUFBRztnQkFDckQsT0FBTztZQUNUO1lBRUFiO2dFQUFVcUIsQ0FBQUE7b0JBQ1IsTUFBTUUsWUFBWTsyQkFBSUY7cUJBQUs7b0JBQzNCRSxTQUFTLENBQUNlLFdBQVcsR0FBRzt3QkFBRSxHQUFHZixTQUFTLENBQUNlLFdBQVc7d0JBQUUsR0FBR0QsT0FBTztvQkFBQztvQkFDL0RHLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBeUMsT0FBSDVCLElBQUcsV0FBU3dCO29CQUM5RCxPQUFPZDtnQkFDVDs7WUFFQSxPQUFPO1FBQ1Q7dURBQUc7UUFBQ3hCO0tBQU87SUFFWCxvQkFBb0I7SUFDcEJYLGdEQUFTQTt5Q0FBQztZQUNSLE1BQU1zRDs4REFBZSxDQUFDQztvQkFDcEIsSUFBSUEsTUFBTUMsR0FBRyxLQUFLLFlBQVk3QyxPQUFPMkIsTUFBTSxHQUFHLEdBQUc7d0JBQy9DLE1BQU1tQixXQUFXOUMsTUFBTSxDQUFDQSxPQUFPMkIsTUFBTSxHQUFHLEVBQUU7d0JBQzFDLElBQUltQixTQUFTM0IsYUFBYSxJQUFJLENBQUMyQixTQUFTMUIsWUFBWSxFQUFFO2dDQUVwRDBCO2dDQURXQTs0QkFBWHZCLFdBQVd1QixDQUFBQSxlQUFBQSxTQUFTaEMsRUFBRSxjQUFYZ0MsMEJBQUFBLGVBQWU7NkJBQzFCQSxvQkFBQUEsU0FBU0MsT0FBTyxjQUFoQkQsd0NBQUFBLHVCQUFBQTt3QkFDRjtvQkFDRjtnQkFDRjs7WUFFQSxJQUFJOUMsT0FBTzJCLE1BQU0sR0FBRyxHQUFHO2dCQUNyQkMsU0FBU29CLGdCQUFnQixDQUFDLFdBQVdMO1lBQ3ZDO1lBRUE7aURBQU87b0JBQ0xmLFNBQVNxQixtQkFBbUIsQ0FBQyxXQUFXTjtnQkFDMUM7O1FBQ0Y7d0NBQUc7UUFBQzNDO1FBQVF1QjtLQUFXO0lBRXZCLHdCQUF3QjtJQUN4QixNQUFNMkIsc0JBQXNCOUQsa0RBQVdBO2dFQUFDLENBQUNzQyxPQUEwQmtCO1lBQ2pFSCxRQUFRQyxHQUFHLENBQUMsU0FBU2hCO1lBQ3JCLElBQUlrQixNQUFNTyxNQUFNLEtBQUtQLE1BQU1RLGFBQWEsSUFBSTFCLE1BQU1SLG9CQUFvQixJQUFJLENBQUNRLE1BQU1OLFlBQVksRUFBRTtvQkFFN0ZNO29CQURXQTtnQkFBWEgsV0FBV0csQ0FBQUEsWUFBQUEsTUFBTVosRUFBRSxjQUFSWSx1QkFBQUEsWUFBWTtpQkFDdkJBLGlCQUFBQSxNQUFNcUIsT0FBTyxjQUFickIscUNBQUFBLG9CQUFBQTtZQUNGO1FBQ0Y7K0RBQUc7UUFBQ0g7S0FBVztJQUVmLG1DQUFtQztJQUNuQyxNQUFNOEIsaUJBQWlCakUsa0RBQVdBOzJEQUFDLENBQUNrRSxTQUFpQkM7WUFDbkQsSUFBSUEsU0FBUztnQkFDWHBELFVBQVVhLE9BQU8sQ0FBQ3dDLEdBQUcsQ0FBQ0YsU0FBU0M7Z0JBQy9CLE1BQU03QixRQUFRUyxhQUFhbUI7Z0JBQzNCLElBQUk1QixrQkFBQUEsNEJBQUFBLE1BQU9MLGFBQWEsRUFBRTtvQkFDeEI3QixtRUFBaUJBLENBQUMrRDtnQkFDcEI7WUFDRjtRQUNGOzBEQUFHO1FBQUNwQjtLQUFhO0lBRWpCLE1BQU1zQixRQUFnQztRQUNwQ3pEO1FBQ0FZO1FBQ0FXO1FBQ0FRO1FBQ0FFO1FBQ0FFO1FBQ0FFO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzFDLG1CQUFtQitELFFBQVE7UUFBQ0QsT0FBT0E7O1lBQ2pDM0Q7WUFDQUUsT0FBTzJCLE1BQU0sR0FBRyxLQUFLLGFBQWtCLDZCQUFlcEMsdURBQVlBLGVBQ2pFLDhEQUFDb0U7Z0JBQUlDLFdBQVU7MEJBQ1o1RCxPQUFPNkQsR0FBRyxDQUFDLENBQUNuQyxzQkFDWCw4REFBQ2lDO3dCQUVDQyxXQUFXLGtEQUE0RyxPQUExRGxDLE1BQU1vQyxpQkFBaUIsSUFBSTt3QkFDeEZDLE9BQU87NEJBQUVoRCxRQUFRVyxNQUFNWCxNQUFNO3dCQUFDO3dCQUM5QmlELFNBQVMsQ0FBQ0MsSUFBTWYsb0JBQW9CeEIsT0FBT3VDO2tDQUUzQyw0RUFBQ047NEJBQ0NPLEtBQUssQ0FBQ0M7b0NBQXNCekM7dUNBQWYyQixlQUFlM0IsQ0FBQUEsWUFBQUEsTUFBTVosRUFBRSxjQUFSWSx1QkFBQUEsWUFBWSxJQUFJeUM7OzRCQUM1Q1AsV0FBV2xDLE1BQU0wQyxjQUFjLElBQUk7NEJBQ25DSixTQUFTLENBQUNDLElBQU1BLEVBQUVJLGVBQWU7c0NBRWhDM0MsTUFBTTRDLFNBQVM7Ozs7Ozt1QkFWYjVDLE1BQU1aLEVBQUU7Ozs7Ozs7OztzQkFlbkJjLFNBQVNDLElBQUk7Ozs7Ozs7QUFJckI7R0FoS2dCaEM7S0FBQUE7QUFrS1QsU0FBUzBFOztJQUNkLE1BQU1DLFVBQVV0RixpREFBVUEsQ0FBQ1M7SUFDM0IsSUFBSTZFLFlBQVk1RSxXQUFXO1FBQ3pCLE1BQU0sSUFBSTZFLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0lBTmdCRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbHJhaG1hXFxEZXNrdG9wXFxBaG1lZCBCYXJha2F0XFxmdW5IaS1wcm9qZWN0XFxmdW5IaS1wcm9qZWN0XFxmcm9udC1lbmRcXHNyY1xcY29udGV4dHNcXEdsb2JhbE1vZGFsQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IGNyZWF0ZVBvcnRhbCB9IGZyb20gJ3JlYWN0LWRvbSc7XHJcbmltcG9ydCB7IGRpc2FibGVCb2R5U2Nyb2xsLCBlbmFibGVCb2R5U2Nyb2xsLCBjbGVhckFsbEJvZHlTY3JvbGxMb2NrcyB9IGZyb20gJ2JvZHktc2Nyb2xsLWxvY2snO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBHbG9iYWxNb2RhbENvbmZpZyB7XHJcbiAgaWQ/OiBzdHJpbmc7XHJcbiAgY29tcG9uZW50OiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgb25DbG9zZT86ICgpID0+IHZvaWQ7XHJcbiAgY2xvc2VPbkJhY2tkcm9wQ2xpY2s/OiBib29sZWFuO1xyXG4gIGNsb3NlT25Fc2NhcGU/OiBib29sZWFuO1xyXG4gIHByZXZlbnRDbG9zZT86IGJvb2xlYW47XHJcbiAgekluZGV4PzogbnVtYmVyO1xyXG4gIGJhY2tkcm9wQ2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIG1vZGFsQ2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIGRpc2FibGVTY3JvbGw/OiBib29sZWFuO1xyXG4gIC8vIE5ldyBwcm9wZXJ0eSB0byBzdXBwb3J0IHVwZGF0aW5nIG1vZGFsIHByb3BzXHJcbiAgdXBkYXRlUHJvcHM/OiBhbnk7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgR2xvYmFsTW9kYWxDb250ZXh0VHlwZSB7XHJcbiAgbW9kYWxzOiBHbG9iYWxNb2RhbENvbmZpZ1tdO1xyXG4gIG9wZW5Nb2RhbDogKGNvbmZpZzogR2xvYmFsTW9kYWxDb25maWcpID0+IHN0cmluZztcclxuICBjbG9zZU1vZGFsOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcclxuICBjbG9zZUFsbE1vZGFsczogKCkgPT4gdm9pZDtcclxuICBpc01vZGFsT3BlbjogKGlkOiBzdHJpbmcpID0+IGJvb2xlYW47XHJcbiAgZ2V0TW9kYWxCeUlkOiAoaWQ6IHN0cmluZykgPT4gR2xvYmFsTW9kYWxDb25maWcgfCB1bmRlZmluZWQ7XHJcbiAgdXBkYXRlTW9kYWw6IChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEdsb2JhbE1vZGFsQ29uZmlnPikgPT4gYm9vbGVhbjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEdsb2JhbE1vZGFsUHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBkZWZhdWx0WkluZGV4PzogbnVtYmVyO1xyXG59XHJcblxyXG5jb25zdCBHbG9iYWxNb2RhbENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEdsb2JhbE1vZGFsQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gR2xvYmFsTW9kYWxQcm92aWRlcih7IGNoaWxkcmVuLCBkZWZhdWx0WkluZGV4ID0gMTAwMCB9OiBHbG9iYWxNb2RhbFByb3ZpZGVyUHJvcHMpIHtcclxuICBjb25zdCBbbW9kYWxzLCBzZXRNb2RhbHNdID0gdXNlU3RhdGU8R2xvYmFsTW9kYWxDb25maWdbXT4oW10pO1xyXG4gIGNvbnN0IG5leHRaSW5kZXggPSB1c2VSZWYoZGVmYXVsdFpJbmRleCk7XHJcbiAgY29uc3QgbW9kYWxSZWZzID0gdXNlUmVmPE1hcDxzdHJpbmcsIEhUTUxFbGVtZW50Pj4obmV3IE1hcCgpKTtcclxuXHJcbiAgLy8gR2VuZXJhdGUgdW5pcXVlIG1vZGFsIElEXHJcbiAgY29uc3QgZ2VuZXJhdGVNb2RhbElkID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgcmV0dXJuIGBnbG9iYWwtbW9kYWwtJHtEYXRlLm5vdygpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gT3BlbiBtb2RhbFxyXG4gIGNvbnN0IG9wZW5Nb2RhbCA9IHVzZUNhbGxiYWNrKChjb25maWc6IEdsb2JhbE1vZGFsQ29uZmlnKSA9PiB7XHJcbiAgICBjb25zdCBpZCA9IGNvbmZpZy5pZCA/PyBnZW5lcmF0ZU1vZGFsSWQoKTtcclxuICAgIGNvbnN0IHpJbmRleCA9IGNvbmZpZy56SW5kZXggfHwgbmV4dFpJbmRleC5jdXJyZW50O1xyXG4gICAgXHJcbiAgICBjb25zdCBtb2RhbENvbmZpZzogR2xvYmFsTW9kYWxDb25maWcgPSB7XHJcbiAgICAgIC4uLmNvbmZpZyxcclxuICAgICAgaWQsXHJcbiAgICAgIHpJbmRleCxcclxuICAgICAgY2xvc2VPbkJhY2tkcm9wQ2xpY2s6IGNvbmZpZy5jbG9zZU9uQmFja2Ryb3BDbGljayA/PyB0cnVlLFxyXG4gICAgICBjbG9zZU9uRXNjYXBlOiBjb25maWcuY2xvc2VPbkVzY2FwZSA/PyB0cnVlLFxyXG4gICAgICBwcmV2ZW50Q2xvc2U6IGNvbmZpZy5wcmV2ZW50Q2xvc2UgPz8gZmFsc2UsXHJcbiAgICAgIGRpc2FibGVTY3JvbGw6IGNvbmZpZy5kaXNhYmxlU2Nyb2xsID8/IHRydWUsXHJcbiAgICB9O1xyXG5cclxuICAgIHNldE1vZGFscyhwcmV2ID0+IFsuLi5wcmV2LCBtb2RhbENvbmZpZ10pO1xyXG4gICAgbmV4dFpJbmRleC5jdXJyZW50ID0gekluZGV4ICsgMTtcclxuICAgIFxyXG4gICAgcmV0dXJuIGlkO1xyXG4gIH0sIFtnZW5lcmF0ZU1vZGFsSWRdKTtcclxuXHJcbiAgLy8gQ2xvc2UgbW9kYWxcclxuICBjb25zdCBjbG9zZU1vZGFsID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcclxuICAgIHNldE1vZGFscyhwcmV2ID0+IHtcclxuICAgICAgY29uc3QgbmV3TW9kYWxzID0gcHJldi5maWx0ZXIobW9kYWwgPT4gbW9kYWwuaWQgIT09IGlkKTtcclxuICAgICAgXHJcbiAgICAgIC8vIEVuYWJsZSBib2R5IHNjcm9sbCBpZiBubyBtb2RhbHMgYXJlIG9wZW5cclxuICAgICAgaWYgKG5ld01vZGFscy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICBlbmFibGVCb2R5U2Nyb2xsKGRvY3VtZW50LmJvZHkpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gbmV3TW9kYWxzO1xyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIC8vIENsZWFuIHVwIG1vZGFsIHJlZlxyXG4gICAgbW9kYWxSZWZzLmN1cnJlbnQuZGVsZXRlKGlkKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIENsb3NlIGFsbCBtb2RhbHNcclxuICBjb25zdCBjbG9zZUFsbE1vZGFscyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHNldE1vZGFscyhbXSk7XHJcbiAgICBjbGVhckFsbEJvZHlTY3JvbGxMb2NrcygpO1xyXG4gICAgbW9kYWxSZWZzLmN1cnJlbnQuY2xlYXIoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIENoZWNrIGlmIG1vZGFsIGlzIG9wZW5cclxuICBjb25zdCBpc01vZGFsT3BlbiA9IHVzZUNhbGxiYWNrKChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gbW9kYWxzLnNvbWUobW9kYWwgPT4gbW9kYWwuaWQgPT09IGlkKTtcclxuICB9LCBbbW9kYWxzXSk7XHJcblxyXG4gIC8vIEdldCBtb2RhbCBieSBJRFxyXG4gIGNvbnN0IGdldE1vZGFsQnlJZCA9IHVzZUNhbGxiYWNrKChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gbW9kYWxzLmZpbmQobW9kYWwgPT4gbW9kYWwuaWQgPT09IGlkKTtcclxuICB9LCBbbW9kYWxzXSk7XHJcblxyXG4gIC8vIFVwZGF0ZSBleGlzdGluZyBtb2RhbFxyXG4gIGNvbnN0IHVwZGF0ZU1vZGFsID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8R2xvYmFsTW9kYWxDb25maWc+KTogYm9vbGVhbiA9PiB7XHJcbiAgICBjb25zdCBtb2RhbEluZGV4ID0gbW9kYWxzLmZpbmRJbmRleChtb2RhbCA9PiBtb2RhbC5pZCA9PT0gaWQpO1xyXG4gICAgaWYgKG1vZGFsSW5kZXggPT09IC0xKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBbR2xvYmFsTW9kYWxDb250ZXh0XSBNb2RhbCB3aXRoIGlkICR7aWR9IG5vdCBmb3VuZCBmb3IgdXBkYXRlYCk7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBzZXRNb2RhbHMocHJldiA9PiB7XHJcbiAgICAgIGNvbnN0IG5ld01vZGFscyA9IFsuLi5wcmV2XTtcclxuICAgICAgbmV3TW9kYWxzW21vZGFsSW5kZXhdID0geyAuLi5uZXdNb2RhbHNbbW9kYWxJbmRleF0sIC4uLnVwZGF0ZXMgfTtcclxuICAgICAgY29uc29sZS5sb2coYFtHbG9iYWxNb2RhbENvbnRleHRdIFVwZGF0ZWQgbW9kYWwgJHtpZH0gd2l0aDpgLCB1cGRhdGVzKTtcclxuICAgICAgcmV0dXJuIG5ld01vZGFscztcclxuICAgIH0pO1xyXG5cclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH0sIFttb2RhbHNdKTtcclxuXHJcbiAgLy8gSGFuZGxlIGVzY2FwZSBrZXlcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlRXNjYXBlID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChldmVudC5rZXkgPT09ICdFc2NhcGUnICYmIG1vZGFscy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc3QgdG9wTW9kYWwgPSBtb2RhbHNbbW9kYWxzLmxlbmd0aCAtIDFdO1xyXG4gICAgICAgIGlmICh0b3BNb2RhbC5jbG9zZU9uRXNjYXBlICYmICF0b3BNb2RhbC5wcmV2ZW50Q2xvc2UpIHtcclxuICAgICAgICAgIGNsb3NlTW9kYWwodG9wTW9kYWwuaWQgPz8gXCJcIik7XHJcbiAgICAgICAgICB0b3BNb2RhbC5vbkNsb3NlPy4oKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaWYgKG1vZGFscy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NhcGUpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NhcGUpO1xyXG4gICAgfTtcclxuICB9LCBbbW9kYWxzLCBjbG9zZU1vZGFsXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBiYWNrZHJvcCBjbGlja1xyXG4gIGNvbnN0IGhhbmRsZUJhY2tkcm9wQ2xpY2sgPSB1c2VDYWxsYmFjaygobW9kYWw6IEdsb2JhbE1vZGFsQ29uZmlnLCBldmVudDogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJtb2RhbFwiLCBtb2RhbCk7XHJcbiAgICBpZiAoZXZlbnQudGFyZ2V0ID09PSBldmVudC5jdXJyZW50VGFyZ2V0ICYmIG1vZGFsLmNsb3NlT25CYWNrZHJvcENsaWNrICYmICFtb2RhbC5wcmV2ZW50Q2xvc2UpIHtcclxuICAgICAgY2xvc2VNb2RhbChtb2RhbC5pZCA/PyBcIlwiKTtcclxuICAgICAgbW9kYWwub25DbG9zZT8uKCk7XHJcbiAgICB9XHJcbiAgfSwgW2Nsb3NlTW9kYWxdKTtcclxuXHJcbiAgLy8gSGFuZGxlIG1vZGFsIHJlZiBmb3Igc2Nyb2xsIGxvY2tcclxuICBjb25zdCBoYW5kbGVNb2RhbFJlZiA9IHVzZUNhbGxiYWNrKChtb2RhbElkOiBzdHJpbmcsIGVsZW1lbnQ6IEhUTUxFbGVtZW50IHwgbnVsbCkgPT4ge1xyXG4gICAgaWYgKGVsZW1lbnQpIHtcclxuICAgICAgbW9kYWxSZWZzLmN1cnJlbnQuc2V0KG1vZGFsSWQsIGVsZW1lbnQpO1xyXG4gICAgICBjb25zdCBtb2RhbCA9IGdldE1vZGFsQnlJZChtb2RhbElkKTtcclxuICAgICAgaWYgKG1vZGFsPy5kaXNhYmxlU2Nyb2xsKSB7XHJcbiAgICAgICAgZGlzYWJsZUJvZHlTY3JvbGwoZWxlbWVudCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbZ2V0TW9kYWxCeUlkXSk7XHJcblxyXG4gIGNvbnN0IHZhbHVlOiBHbG9iYWxNb2RhbENvbnRleHRUeXBlID0ge1xyXG4gICAgbW9kYWxzLFxyXG4gICAgb3Blbk1vZGFsLFxyXG4gICAgY2xvc2VNb2RhbCxcclxuICAgIGNsb3NlQWxsTW9kYWxzLFxyXG4gICAgaXNNb2RhbE9wZW4sXHJcbiAgICBnZXRNb2RhbEJ5SWQsXHJcbiAgICB1cGRhdGVNb2RhbCxcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEdsb2JhbE1vZGFsQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIHttb2RhbHMubGVuZ3RoID4gMCAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiBjcmVhdGVQb3J0YWwoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbG9iYWwtbW9kYWwtcG9ydGFsXCI+XHJcbiAgICAgICAgICB7bW9kYWxzLm1hcCgobW9kYWwpID0+IChcclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgIGtleT17bW9kYWwuaWR9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke21vZGFsLmJhY2tkcm9wQ2xhc3NOYW1lIHx8ICdiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtJ31gfVxyXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHpJbmRleDogbW9kYWwuekluZGV4IH19XHJcbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IGhhbmRsZUJhY2tkcm9wQ2xpY2sobW9kYWwsIGUpfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgcmVmPXsoZWwpID0+IGhhbmRsZU1vZGFsUmVmKG1vZGFsLmlkID8/IFwiXCIsIGVsKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17bW9kYWwubW9kYWxDbGFzc05hbWUgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7bW9kYWwuY29tcG9uZW50fVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvZGl2PixcclxuICAgICAgICBkb2N1bWVudC5ib2R5XHJcbiAgICAgICl9XHJcbiAgICA8L0dsb2JhbE1vZGFsQ29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2xvYmFsTW9kYWwoKSB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoR2xvYmFsTW9kYWxDb250ZXh0KTtcclxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUdsb2JhbE1vZGFsIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBHbG9iYWxNb2RhbFByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsInVzZVJlZiIsImNyZWF0ZVBvcnRhbCIsImRpc2FibGVCb2R5U2Nyb2xsIiwiZW5hYmxlQm9keVNjcm9sbCIsImNsZWFyQWxsQm9keVNjcm9sbExvY2tzIiwiR2xvYmFsTW9kYWxDb250ZXh0IiwidW5kZWZpbmVkIiwiR2xvYmFsTW9kYWxQcm92aWRlciIsImNoaWxkcmVuIiwiZGVmYXVsdFpJbmRleCIsIm1vZGFscyIsInNldE1vZGFscyIsIm5leHRaSW5kZXgiLCJtb2RhbFJlZnMiLCJNYXAiLCJnZW5lcmF0ZU1vZGFsSWQiLCJNYXRoIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwib3Blbk1vZGFsIiwiY29uZmlnIiwiaWQiLCJ6SW5kZXgiLCJjdXJyZW50IiwibW9kYWxDb25maWciLCJjbG9zZU9uQmFja2Ryb3BDbGljayIsImNsb3NlT25Fc2NhcGUiLCJwcmV2ZW50Q2xvc2UiLCJkaXNhYmxlU2Nyb2xsIiwicHJldiIsImNsb3NlTW9kYWwiLCJuZXdNb2RhbHMiLCJmaWx0ZXIiLCJtb2RhbCIsImxlbmd0aCIsImRvY3VtZW50IiwiYm9keSIsImRlbGV0ZSIsImNsb3NlQWxsTW9kYWxzIiwiY2xlYXIiLCJpc01vZGFsT3BlbiIsInNvbWUiLCJnZXRNb2RhbEJ5SWQiLCJmaW5kIiwidXBkYXRlTW9kYWwiLCJ1cGRhdGVzIiwibW9kYWxJbmRleCIsImZpbmRJbmRleCIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVFc2NhcGUiLCJldmVudCIsImtleSIsInRvcE1vZGFsIiwib25DbG9zZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGFuZGxlQmFja2Ryb3BDbGljayIsInRhcmdldCIsImN1cnJlbnRUYXJnZXQiLCJoYW5kbGVNb2RhbFJlZiIsIm1vZGFsSWQiLCJlbGVtZW50Iiwic2V0IiwidmFsdWUiLCJQcm92aWRlciIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsImJhY2tkcm9wQ2xhc3NOYW1lIiwic3R5bGUiLCJvbkNsaWNrIiwiZSIsInJlZiIsImVsIiwibW9kYWxDbGFzc05hbWUiLCJzdG9wUHJvcGFnYXRpb24iLCJjb21wb25lbnQiLCJ1c2VHbG9iYWxNb2RhbCIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\n"));

/***/ })

});