"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/node-fetch-native";
exports.ids = ["vendor-chunks/node-fetch-native"];
exports.modules = {

/***/ "(ssr)/./node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toFormData: () => (/* binding */ v)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n/* harmony import */ var _node_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../node.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/node.mjs\");\n/* harmony import */ var node_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! node:http */ \"node:http\");\n/* harmony import */ var node_https__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:https */ \"node:https\");\n/* harmony import */ var node_zlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! node:zlib */ \"node:zlib\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../shared/node-fetch-native.DfbY2q-x.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs\");\n/* harmony import */ var node_url__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! node:url */ \"node:url\");\n/* harmony import */ var node_net__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! node:net */ \"node:net\");\nvar U=Object.defineProperty;var E=(_,o)=>U(_,\"name\",{value:o,configurable:!0});let D=0;const t={START_BOUNDARY:D++,HEADER_FIELD_START:D++,HEADER_FIELD:D++,HEADER_VALUE_START:D++,HEADER_VALUE:D++,HEADER_VALUE_ALMOST_DONE:D++,HEADERS_ALMOST_DONE:D++,PART_DATA_START:D++,PART_DATA:D++,END:D++};let F=1;const u={PART_BOUNDARY:F,LAST_BOUNDARY:F*=2},g=10,N=13,V=32,S=45,Y=58,x=97,C=122,I=E(_=>_|32,\"lower\"),p=E(()=>{},\"noop\");class M{static{E(this,\"MultipartParser\")}constructor(o){this.index=0,this.flags=0,this.onHeaderEnd=p,this.onHeaderField=p,this.onHeadersEnd=p,this.onHeaderValue=p,this.onPartBegin=p,this.onPartData=p,this.onPartEnd=p,this.boundaryChars={},o=`\\r\n--`+o;const n=new Uint8Array(o.length);for(let r=0;r<o.length;r++)n[r]=o.charCodeAt(r),this.boundaryChars[n[r]]=!0;this.boundary=n,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=t.START_BOUNDARY}write(o){let n=0;const r=o.length;let d=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:e,state:i,flags:A}=this;const H=this.boundary.length,O=H-1,y=o.length;let a,L;const f=E(h=>{this[h+\"Mark\"]=n},\"mark\"),s=E(h=>{delete this[h+\"Mark\"]},\"clear\"),T=E((h,P,R,k)=>{(P===void 0||P!==R)&&this[h](k&&k.subarray(P,R))},\"callback\"),b=E((h,P)=>{const R=h+\"Mark\";R in this&&(P?(T(h,this[R],n,o),delete this[R]):(T(h,this[R],o.length,o),this[R]=0))},\"dataCallback\");for(n=0;n<r;n++)switch(a=o[n],i){case t.START_BOUNDARY:if(e===c.length-2){if(a===S)A|=u.LAST_BOUNDARY;else if(a!==N)return;e++;break}else if(e-1===c.length-2){if(A&u.LAST_BOUNDARY&&a===S)i=t.END,A=0;else if(!(A&u.LAST_BOUNDARY)&&a===g)e=0,T(\"onPartBegin\"),i=t.HEADER_FIELD_START;else return;break}a!==c[e+2]&&(e=-2),a===c[e+2]&&e++;break;case t.HEADER_FIELD_START:i=t.HEADER_FIELD,f(\"onHeaderField\"),e=0;case t.HEADER_FIELD:if(a===N){s(\"onHeaderField\"),i=t.HEADERS_ALMOST_DONE;break}if(e++,a===S)break;if(a===Y){if(e===1)return;b(\"onHeaderField\",!0),i=t.HEADER_VALUE_START;break}if(L=I(a),L<x||L>C)return;break;case t.HEADER_VALUE_START:if(a===V)break;f(\"onHeaderValue\"),i=t.HEADER_VALUE;case t.HEADER_VALUE:a===N&&(b(\"onHeaderValue\",!0),T(\"onHeaderEnd\"),i=t.HEADER_VALUE_ALMOST_DONE);break;case t.HEADER_VALUE_ALMOST_DONE:if(a!==g)return;i=t.HEADER_FIELD_START;break;case t.HEADERS_ALMOST_DONE:if(a!==g)return;T(\"onHeadersEnd\"),i=t.PART_DATA_START;break;case t.PART_DATA_START:i=t.PART_DATA,f(\"onPartData\");case t.PART_DATA:if(d=e,e===0){for(n+=O;n<y&&!(o[n]in m);)n+=H;n-=O,a=o[n]}if(e<c.length)c[e]===a?(e===0&&b(\"onPartData\",!0),e++):e=0;else if(e===c.length)e++,a===N?A|=u.PART_BOUNDARY:a===S?A|=u.LAST_BOUNDARY:e=0;else if(e-1===c.length)if(A&u.PART_BOUNDARY){if(e=0,a===g){A&=~u.PART_BOUNDARY,T(\"onPartEnd\"),T(\"onPartBegin\"),i=t.HEADER_FIELD_START;break}}else A&u.LAST_BOUNDARY&&a===S?(T(\"onPartEnd\"),i=t.END,A=0):e=0;if(e>0)l[e-1]=a;else if(d>0){const h=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);T(\"onPartData\",0,d,h),d=0,f(\"onPartData\"),n--}break;case t.END:break;default:throw new Error(`Unexpected state entered: ${i}`)}b(\"onHeaderField\"),b(\"onHeaderValue\"),b(\"onPartData\"),this.index=e,this.state=i,this.flags=A}end(){if(this.state===t.HEADER_FIELD_START&&this.index===0||this.state===t.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==t.END)throw new Error(\"MultipartParser.end(): stream ended unexpectedly\")}}function $(_){const o=_.match(/\\bfilename=(\"(.*?)\"|([^()<>@,;:\\\\\"/[\\]?={}\\s\\t]+))($|;\\s)/i);if(!o)return;const n=o[2]||o[3]||\"\";let r=n.slice(n.lastIndexOf(\"\\\\\")+1);return r=r.replace(/%22/g,'\"'),r=r.replace(/&#(\\d{4});/g,(d,l)=>String.fromCharCode(l)),r}E($,\"_fileName\");async function v(_,o){if(!/multipart/i.test(o))throw new TypeError(\"Failed to fetch\");const n=o.match(/boundary=(?:\"([^\"]+)\"|([^;]+))/i);if(!n)throw new TypeError(\"no or bad content-type header, no multipart boundary\");const r=new M(n[1]||n[2]);let d,l,c,m,e,i;const A=[],H=new _node_mjs__WEBPACK_IMPORTED_MODULE_2__.FormData,O=E(s=>{c+=f.decode(s,{stream:!0})},\"onPartData\"),y=E(s=>{A.push(s)},\"appendToFile\"),a=E(()=>{const s=new _node_mjs__WEBPACK_IMPORTED_MODULE_2__.File(A,i,{type:e});H.append(m,s)},\"appendFileToFormData\"),L=E(()=>{H.append(m,c)},\"appendEntryToFormData\"),f=new TextDecoder(\"utf-8\");f.decode(),r.onPartBegin=function(){r.onPartData=O,r.onPartEnd=L,d=\"\",l=\"\",c=\"\",m=\"\",e=\"\",i=null,A.length=0},r.onHeaderField=function(s){d+=f.decode(s,{stream:!0})},r.onHeaderValue=function(s){l+=f.decode(s,{stream:!0})},r.onHeaderEnd=function(){if(l+=f.decode(),d=d.toLowerCase(),d===\"content-disposition\"){const s=l.match(/\\bname=(\"([^\"]*)\"|([^()<>@,;:\\\\\"/[\\]?={}\\s\\t]+))/i);s&&(m=s[2]||s[3]||\"\"),i=$(l),i&&(r.onPartData=y,r.onPartEnd=a)}else d===\"content-type\"&&(e=l);l=\"\",d=\"\"};for await(const s of _)r.write(s);return r.end(),H}E(v,\"toFormData\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/node-fetch-native/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/node-fetch-native/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ T),\n/* harmony export */   AbortError: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.AbortError),\n/* harmony export */   Blob: () => (/* binding */ p),\n/* harmony export */   FetchError: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.FetchError),\n/* harmony export */   File: () => (/* binding */ F),\n/* harmony export */   FormData: () => (/* binding */ h),\n/* harmony export */   Headers: () => (/* binding */ n),\n/* harmony export */   Request: () => (/* binding */ c),\n/* harmony export */   Response: () => (/* binding */ R),\n/* harmony export */   blobFrom: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.blobFrom),\n/* harmony export */   blobFromSync: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.blobFromSync),\n/* harmony export */   \"default\": () => (/* binding */ r),\n/* harmony export */   fetch: () => (/* binding */ r),\n/* harmony export */   fileFrom: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFrom),\n/* harmony export */   fileFromSync: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromSync),\n/* harmony export */   isRedirect: () => (/* reexport safe */ _node_mjs__WEBPACK_IMPORTED_MODULE_0__.isRedirect)\n/* harmony export */ });\n/* harmony import */ var _node_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/node.mjs\");\n/* harmony import */ var node_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:http */ \"node:http\");\n/* harmony import */ var node_https__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:https */ \"node:https\");\n/* harmony import */ var node_zlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! node:zlib */ \"node:zlib\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/node-fetch-native.DfbY2q-x.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs\");\n/* harmony import */ var node_url__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! node:url */ \"node:url\");\n/* harmony import */ var node_net__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! node:net */ \"node:net\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! node:path */ \"node:path\");\nconst o=!!globalThis.process?.env?.FORCE_NODE_FETCH,r=!o&&globalThis.fetch||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch,p=!o&&globalThis.Blob||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.Blob,F=!o&&globalThis.File||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.File,h=!o&&globalThis.FormData||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData,n=!o&&globalThis.Headers||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.Headers,c=!o&&globalThis.Request||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.Request,R=!o&&globalThis.Response||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.Response,T=!o&&globalThis.AbortController||_node_mjs__WEBPACK_IMPORTED_MODULE_0__.AbortController;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbm9kZS1mZXRjaC1uYXRpdmUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0Yyw0RUFBNEUsNENBQUMsd0JBQXdCLDJDQUFDLHdCQUF3QiwyQ0FBQyw0QkFBNEIsK0NBQUMsMkJBQTJCLDhDQUFDLDJCQUEyQiw4Q0FBQyw0QkFBNEIsK0NBQUMsbUNBQW1DLHNEQUFDLENBQWdJIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxub2RlLWZldGNoLW5hdGl2ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtmZXRjaCBhcyBlLEJsb2IgYXMgdCxGaWxlIGFzIHMsRm9ybURhdGEgYXMgbCxIZWFkZXJzIGFzIGEsUmVxdWVzdCBhcyBpLFJlc3BvbnNlIGFzIGIsQWJvcnRDb250cm9sbGVyIGFzIG19ZnJvbVwiLi9ub2RlLm1qc1wiO2V4cG9ydHtBYm9ydEVycm9yLEZldGNoRXJyb3IsYmxvYkZyb20sYmxvYkZyb21TeW5jLGZpbGVGcm9tLGZpbGVGcm9tU3luYyxpc1JlZGlyZWN0fWZyb21cIi4vbm9kZS5tanNcIjtpbXBvcnRcIm5vZGU6aHR0cFwiO2ltcG9ydFwibm9kZTpodHRwc1wiO2ltcG9ydFwibm9kZTp6bGliXCI7aW1wb3J0XCJub2RlOnN0cmVhbVwiO2ltcG9ydFwibm9kZTpidWZmZXJcIjtpbXBvcnRcIm5vZGU6dXRpbFwiO2ltcG9ydFwiLi9zaGFyZWQvbm9kZS1mZXRjaC1uYXRpdmUuRGZiWTJxLXgubWpzXCI7aW1wb3J0XCJub2RlOnVybFwiO2ltcG9ydFwibm9kZTpuZXRcIjtpbXBvcnRcIm5vZGU6ZnNcIjtpbXBvcnRcIm5vZGU6cGF0aFwiO2NvbnN0IG89ISFnbG9iYWxUaGlzLnByb2Nlc3M/LmVudj8uRk9SQ0VfTk9ERV9GRVRDSCxyPSFvJiZnbG9iYWxUaGlzLmZldGNofHxlLHA9IW8mJmdsb2JhbFRoaXMuQmxvYnx8dCxGPSFvJiZnbG9iYWxUaGlzLkZpbGV8fHMsaD0hbyYmZ2xvYmFsVGhpcy5Gb3JtRGF0YXx8bCxuPSFvJiZnbG9iYWxUaGlzLkhlYWRlcnN8fGEsYz0hbyYmZ2xvYmFsVGhpcy5SZXF1ZXN0fHxpLFI9IW8mJmdsb2JhbFRoaXMuUmVzcG9uc2V8fGIsVD0hbyYmZ2xvYmFsVGhpcy5BYm9ydENvbnRyb2xsZXJ8fG07ZXhwb3J0e1QgYXMgQWJvcnRDb250cm9sbGVyLHAgYXMgQmxvYixGIGFzIEZpbGUsaCBhcyBGb3JtRGF0YSxuIGFzIEhlYWRlcnMsYyBhcyBSZXF1ZXN0LFIgYXMgUmVzcG9uc2UsciBhcyBkZWZhdWx0LHIgYXMgZmV0Y2h9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/node-fetch-native/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/node-fetch-native/dist/node.mjs":
/*!******************************************************!*\
  !*** ./node_modules/node-fetch-native/dist/node.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ nn),\n/* harmony export */   AbortError: () => (/* binding */ jo),\n/* harmony export */   Blob: () => (/* binding */ Ze),\n/* harmony export */   FetchError: () => (/* binding */ te),\n/* harmony export */   File: () => (/* binding */ Yr),\n/* harmony export */   FormData: () => (/* binding */ Zt),\n/* harmony export */   Headers: () => (/* binding */ ae),\n/* harmony export */   Request: () => (/* binding */ Xe),\n/* harmony export */   Response: () => (/* binding */ H),\n/* harmony export */   blobFrom: () => (/* binding */ ks),\n/* harmony export */   blobFromSync: () => (/* binding */ Ws),\n/* harmony export */   \"default\": () => (/* binding */ ei),\n/* harmony export */   fetch: () => (/* binding */ ei),\n/* harmony export */   fileFrom: () => (/* binding */ qs),\n/* harmony export */   fileFromSync: () => (/* binding */ Os),\n/* harmony export */   isRedirect: () => (/* binding */ Xr)\n/* harmony export */ });\n/* harmony import */ var node_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:http */ \"node:http\");\n/* harmony import */ var node_https__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:https */ \"node:https\");\n/* harmony import */ var node_zlib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:zlib */ \"node:zlib\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/node-fetch-native.DfbY2q-x.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs\");\n/* harmony import */ var node_url__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:url */ \"node:url\");\n/* harmony import */ var node_net__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! node:net */ \"node:net\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! node:path */ \"node:path\");\nvar Va=Object.defineProperty;var n=(i,o)=>Va(i,\"name\",{value:o,configurable:!0});function ts(i){if(!/^data:/i.test(i))throw new TypeError('`uri` does not appear to be a Data URI (must begin with \"data:\")');i=i.replace(/\\r?\\n/g,\"\");const o=i.indexOf(\",\");if(o===-1||o<=4)throw new TypeError(\"malformed data: URI\");const a=i.substring(5,o).split(\";\");let l=\"\",u=!1;const m=a[0]||\"text/plain\";let h=m;for(let A=1;A<a.length;A++)a[A]===\"base64\"?u=!0:a[A]&&(h+=`;${a[A]}`,a[A].indexOf(\"charset=\")===0&&(l=a[A].substring(8)));!a[0]&&!l.length&&(h+=\";charset=US-ASCII\",l=\"US-ASCII\");const S=u?\"base64\":\"ascii\",E=unescape(i.substring(o+1)),w=Buffer.from(E,S);return w.type=m,w.typeFull=h,w.charset=l,w}n(ts,\"dataUriToBuffer\");var Eo={},ct={exports:{}};/**\n * @license\n * web-streams-polyfill v3.3.3\n * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */var rs=ct.exports,vo;function ns(){return vo||(vo=1,function(i,o){(function(a,l){l(o)})(rs,function(a){function l(){}n(l,\"noop\");function u(e){return typeof e==\"object\"&&e!==null||typeof e==\"function\"}n(u,\"typeIsObject\");const m=l;function h(e,t){try{Object.defineProperty(e,\"name\",{value:t,configurable:!0})}catch{}}n(h,\"setFunctionName\");const S=Promise,E=Promise.prototype.then,w=Promise.reject.bind(S);function A(e){return new S(e)}n(A,\"newPromise\");function T(e){return A(t=>t(e))}n(T,\"promiseResolvedWith\");function b(e){return w(e)}n(b,\"promiseRejectedWith\");function q(e,t,r){return E.call(e,t,r)}n(q,\"PerformPromiseThen\");function g(e,t,r){q(q(e,t,r),void 0,m)}n(g,\"uponPromise\");function V(e,t){g(e,t)}n(V,\"uponFulfillment\");function I(e,t){g(e,void 0,t)}n(I,\"uponRejection\");function F(e,t,r){return q(e,t,r)}n(F,\"transformPromiseWith\");function Q(e){q(e,void 0,m)}n(Q,\"setPromiseIsHandledToTrue\");let se=n(e=>{if(typeof queueMicrotask==\"function\")se=queueMicrotask;else{const t=T(void 0);se=n(r=>q(t,r),\"_queueMicrotask\")}return se(e)},\"_queueMicrotask\");function O(e,t,r){if(typeof e!=\"function\")throw new TypeError(\"Argument is not a function\");return Function.prototype.apply.call(e,t,r)}n(O,\"reflectCall\");function z(e,t,r){try{return T(O(e,t,r))}catch(s){return b(s)}}n(z,\"promiseCall\");const $=16384;class M{static{n(this,\"SimpleQueue\")}constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){const r=this._back;let s=r;r._elements.length===$-1&&(s={_elements:[],_next:void 0}),r._elements.push(t),s!==r&&(this._back=s,r._next=s),++this._size}shift(){const t=this._front;let r=t;const s=this._cursor;let f=s+1;const c=t._elements,d=c[s];return f===$&&(r=t._next,f=0),--this._size,this._cursor=f,t!==r&&(this._front=r),c[s]=void 0,d}forEach(t){let r=this._cursor,s=this._front,f=s._elements;for(;(r!==f.length||s._next!==void 0)&&!(r===f.length&&(s=s._next,f=s._elements,r=0,f.length===0));)t(f[r]),++r}peek(){const t=this._front,r=this._cursor;return t._elements[r]}}const pt=Symbol(\"[[AbortSteps]]\"),an=Symbol(\"[[ErrorSteps]]\"),ar=Symbol(\"[[CancelSteps]]\"),sr=Symbol(\"[[PullSteps]]\"),ur=Symbol(\"[[ReleaseSteps]]\");function sn(e,t){e._ownerReadableStream=t,t._reader=e,t._state===\"readable\"?fr(e):t._state===\"closed\"?ri(e):un(e,t._storedError)}n(sn,\"ReadableStreamReaderGenericInitialize\");function lr(e,t){const r=e._ownerReadableStream;return X(r,t)}n(lr,\"ReadableStreamReaderGenericCancel\");function ue(e){const t=e._ownerReadableStream;t._state===\"readable\"?cr(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")):ni(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")),t._readableStreamController[ur](),t._reader=void 0,e._ownerReadableStream=void 0}n(ue,\"ReadableStreamReaderGenericRelease\");function yt(e){return new TypeError(\"Cannot \"+e+\" a stream using a released reader\")}n(yt,\"readerLockException\");function fr(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}n(fr,\"defaultReaderClosedPromiseInitialize\");function un(e,t){fr(e),cr(e,t)}n(un,\"defaultReaderClosedPromiseInitializeAsRejected\");function ri(e){fr(e),ln(e)}n(ri,\"defaultReaderClosedPromiseInitializeAsResolved\");function cr(e,t){e._closedPromise_reject!==void 0&&(Q(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(cr,\"defaultReaderClosedPromiseReject\");function ni(e,t){un(e,t)}n(ni,\"defaultReaderClosedPromiseResetToRejected\");function ln(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(ln,\"defaultReaderClosedPromiseResolve\");const fn=Number.isFinite||function(e){return typeof e==\"number\"&&isFinite(e)},oi=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function ii(e){return typeof e==\"object\"||typeof e==\"function\"}n(ii,\"isDictionary\");function ne(e,t){if(e!==void 0&&!ii(e))throw new TypeError(`${t} is not an object.`)}n(ne,\"assertDictionary\");function G(e,t){if(typeof e!=\"function\")throw new TypeError(`${t} is not a function.`)}n(G,\"assertFunction\");function ai(e){return typeof e==\"object\"&&e!==null||typeof e==\"function\"}n(ai,\"isObject\");function cn(e,t){if(!ai(e))throw new TypeError(`${t} is not an object.`)}n(cn,\"assertObject\");function le(e,t,r){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}n(le,\"assertRequiredArgument\");function dr(e,t,r){if(e===void 0)throw new TypeError(`${t} is required in '${r}'.`)}n(dr,\"assertRequiredField\");function hr(e){return Number(e)}n(hr,\"convertUnrestrictedDouble\");function dn(e){return e===0?0:e}n(dn,\"censorNegativeZero\");function si(e){return dn(oi(e))}n(si,\"integerPart\");function mr(e,t){const s=Number.MAX_SAFE_INTEGER;let f=Number(e);if(f=dn(f),!fn(f))throw new TypeError(`${t} is not a finite number`);if(f=si(f),f<0||f>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!fn(f)||f===0?0:f}n(mr,\"convertUnsignedLongLongWithEnforceRange\");function br(e,t){if(!Te(e))throw new TypeError(`${t} is not a ReadableStream.`)}n(br,\"assertReadableStream\");function ze(e){return new ye(e)}n(ze,\"AcquireReadableStreamDefaultReader\");function hn(e,t){e._reader._readRequests.push(t)}n(hn,\"ReadableStreamAddReadRequest\");function pr(e,t,r){const f=e._reader._readRequests.shift();r?f._closeSteps():f._chunkSteps(t)}n(pr,\"ReadableStreamFulfillReadRequest\");function gt(e){return e._reader._readRequests.length}n(gt,\"ReadableStreamGetNumReadRequests\");function mn(e){const t=e._reader;return!(t===void 0||!ge(t))}n(mn,\"ReadableStreamHasDefaultReader\");class ye{static{n(this,\"ReadableStreamDefaultReader\")}constructor(t){if(le(t,1,\"ReadableStreamDefaultReader\"),br(t,\"First parameter\"),Ce(t))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");sn(this,t),this._readRequests=new M}get closed(){return ge(this)?this._closedPromise:b(_t(\"closed\"))}cancel(t=void 0){return ge(this)?this._ownerReadableStream===void 0?b(yt(\"cancel\")):lr(this,t):b(_t(\"cancel\"))}read(){if(!ge(this))return b(_t(\"read\"));if(this._ownerReadableStream===void 0)return b(yt(\"read from\"));let t,r;const s=A((c,d)=>{t=c,r=d});return et(this,{_chunkSteps:n(c=>t({value:c,done:!1}),\"_chunkSteps\"),_closeSteps:n(()=>t({value:void 0,done:!0}),\"_closeSteps\"),_errorSteps:n(c=>r(c),\"_errorSteps\")}),s}releaseLock(){if(!ge(this))throw _t(\"releaseLock\");this._ownerReadableStream!==void 0&&ui(this)}}Object.defineProperties(ye.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),h(ye.prototype.cancel,\"cancel\"),h(ye.prototype.read,\"read\"),h(ye.prototype.releaseLock,\"releaseLock\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(ye.prototype,Symbol.toStringTag,{value:\"ReadableStreamDefaultReader\",configurable:!0});function ge(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_readRequests\")?!1:e instanceof ye}n(ge,\"IsReadableStreamDefaultReader\");function et(e,t){const r=e._ownerReadableStream;r._disturbed=!0,r._state===\"closed\"?t._closeSteps():r._state===\"errored\"?t._errorSteps(r._storedError):r._readableStreamController[sr](t)}n(et,\"ReadableStreamDefaultReaderRead\");function ui(e){ue(e);const t=new TypeError(\"Reader was released\");bn(e,t)}n(ui,\"ReadableStreamDefaultReaderRelease\");function bn(e,t){const r=e._readRequests;e._readRequests=new M,r.forEach(s=>{s._errorSteps(t)})}n(bn,\"ReadableStreamDefaultReaderErrorReadRequests\");function _t(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}n(_t,\"defaultReaderBrandCheckException\");const li=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class pn{static{n(this,\"ReadableStreamAsyncIteratorImpl\")}constructor(t,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=r}next(){const t=n(()=>this._nextSteps(),\"nextSteps\");return this._ongoingPromise=this._ongoingPromise?F(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){const r=n(()=>this._returnSteps(t),\"returnSteps\");return this._ongoingPromise?F(this._ongoingPromise,r,r):r()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const t=this._reader;let r,s;const f=A((d,p)=>{r=d,s=p});return et(t,{_chunkSteps:n(d=>{this._ongoingPromise=void 0,se(()=>r({value:d,done:!1}))},\"_chunkSteps\"),_closeSteps:n(()=>{this._ongoingPromise=void 0,this._isFinished=!0,ue(t),r({value:void 0,done:!0})},\"_closeSteps\"),_errorSteps:n(d=>{this._ongoingPromise=void 0,this._isFinished=!0,ue(t),s(d)},\"_errorSteps\")}),f}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;const r=this._reader;if(!this._preventCancel){const s=lr(r,t);return ue(r),F(s,()=>({value:t,done:!0}))}return ue(r),T({value:t,done:!0})}}const yn={next(){return gn(this)?this._asyncIteratorImpl.next():b(_n(\"next\"))},return(e){return gn(this)?this._asyncIteratorImpl.return(e):b(_n(\"return\"))}};Object.setPrototypeOf(yn,li);function fi(e,t){const r=ze(e),s=new pn(r,t),f=Object.create(yn);return f._asyncIteratorImpl=s,f}n(fi,\"AcquireReadableStreamAsyncIterator\");function gn(e){if(!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_asyncIteratorImpl\"))return!1;try{return e._asyncIteratorImpl instanceof pn}catch{return!1}}n(gn,\"IsReadableStreamAsyncIterator\");function _n(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}n(_n,\"streamAsyncIteratorBrandCheckException\");const Sn=Number.isNaN||function(e){return e!==e};var yr,gr,_r;function tt(e){return e.slice()}n(tt,\"CreateArrayFromList\");function wn(e,t,r,s,f){new Uint8Array(e).set(new Uint8Array(r,s,f),t)}n(wn,\"CopyDataBlockBytes\");let fe=n(e=>(typeof e.transfer==\"function\"?fe=n(t=>t.transfer(),\"TransferArrayBuffer\"):typeof structuredClone==\"function\"?fe=n(t=>structuredClone(t,{transfer:[t]}),\"TransferArrayBuffer\"):fe=n(t=>t,\"TransferArrayBuffer\"),fe(e)),\"TransferArrayBuffer\"),_e=n(e=>(typeof e.detached==\"boolean\"?_e=n(t=>t.detached,\"IsDetachedBuffer\"):_e=n(t=>t.byteLength===0,\"IsDetachedBuffer\"),_e(e)),\"IsDetachedBuffer\");function Rn(e,t,r){if(e.slice)return e.slice(t,r);const s=r-t,f=new ArrayBuffer(s);return wn(f,0,e,t,s),f}n(Rn,\"ArrayBufferSlice\");function St(e,t){const r=e[t];if(r!=null){if(typeof r!=\"function\")throw new TypeError(`${String(t)} is not a function`);return r}}n(St,\"GetMethod\");function ci(e){const t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),s=r.next;return{iterator:r,nextMethod:s,done:!1}}n(ci,\"CreateAsyncFromSyncIterator\");const Sr=(_r=(yr=Symbol.asyncIterator)!==null&&yr!==void 0?yr:(gr=Symbol.for)===null||gr===void 0?void 0:gr.call(Symbol,\"Symbol.asyncIterator\"))!==null&&_r!==void 0?_r:\"@@asyncIterator\";function Tn(e,t=\"sync\",r){if(r===void 0)if(t===\"async\"){if(r=St(e,Sr),r===void 0){const c=St(e,Symbol.iterator),d=Tn(e,\"sync\",c);return ci(d)}}else r=St(e,Symbol.iterator);if(r===void 0)throw new TypeError(\"The object is not iterable\");const s=O(r,e,[]);if(!u(s))throw new TypeError(\"The iterator method must return an object\");const f=s.next;return{iterator:s,nextMethod:f,done:!1}}n(Tn,\"GetIterator\");function di(e){const t=O(e.nextMethod,e.iterator,[]);if(!u(t))throw new TypeError(\"The iterator.next() method must return an object\");return t}n(di,\"IteratorNext\");function hi(e){return!!e.done}n(hi,\"IteratorComplete\");function mi(e){return e.value}n(mi,\"IteratorValue\");function bi(e){return!(typeof e!=\"number\"||Sn(e)||e<0)}n(bi,\"IsNonNegativeNumber\");function Cn(e){const t=Rn(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}n(Cn,\"CloneAsUint8Array\");function wr(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}n(wr,\"DequeueValue\");function Rr(e,t,r){if(!bi(r)||r===1/0)throw new RangeError(\"Size must be a finite, non-NaN, non-negative number.\");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}n(Rr,\"EnqueueValueWithSize\");function pi(e){return e._queue.peek().value}n(pi,\"PeekQueueValue\");function Se(e){e._queue=new M,e._queueTotalSize=0}n(Se,\"ResetQueue\");function Pn(e){return e===DataView}n(Pn,\"isDataViewConstructor\");function yi(e){return Pn(e.constructor)}n(yi,\"isDataView\");function gi(e){return Pn(e)?1:e.BYTES_PER_ELEMENT}n(gi,\"arrayBufferViewElementSize\");class ve{static{n(this,\"ReadableStreamBYOBRequest\")}constructor(){throw new TypeError(\"Illegal constructor\")}get view(){if(!Tr(this))throw Ar(\"view\");return this._view}respond(t){if(!Tr(this))throw Ar(\"respond\");if(le(t,1,\"respond\"),t=mr(t,\"First parameter\"),this._associatedReadableByteStreamController===void 0)throw new TypeError(\"This BYOB request has been invalidated\");if(_e(this._view.buffer))throw new TypeError(\"The BYOB request's buffer has been detached and so cannot be used as a response\");Ct(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!Tr(this))throw Ar(\"respondWithNewView\");if(le(t,1,\"respondWithNewView\"),!ArrayBuffer.isView(t))throw new TypeError(\"You can only respond with array buffer views\");if(this._associatedReadableByteStreamController===void 0)throw new TypeError(\"This BYOB request has been invalidated\");if(_e(t.buffer))throw new TypeError(\"The given view's buffer has been detached and so cannot be used as a response\");Pt(this._associatedReadableByteStreamController,t)}}Object.defineProperties(ve.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),h(ve.prototype.respond,\"respond\"),h(ve.prototype.respondWithNewView,\"respondWithNewView\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(ve.prototype,Symbol.toStringTag,{value:\"ReadableStreamBYOBRequest\",configurable:!0});class ce{static{n(this,\"ReadableByteStreamController\")}constructor(){throw new TypeError(\"Illegal constructor\")}get byobRequest(){if(!Ae(this))throw nt(\"byobRequest\");return vr(this)}get desiredSize(){if(!Ae(this))throw nt(\"desiredSize\");return Fn(this)}close(){if(!Ae(this))throw nt(\"close\");if(this._closeRequested)throw new TypeError(\"The stream has already been closed; do not close it again!\");const t=this._controlledReadableByteStream._state;if(t!==\"readable\")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);rt(this)}enqueue(t){if(!Ae(this))throw nt(\"enqueue\");if(le(t,1,\"enqueue\"),!ArrayBuffer.isView(t))throw new TypeError(\"chunk must be an array buffer view\");if(t.byteLength===0)throw new TypeError(\"chunk must have non-zero byteLength\");if(t.buffer.byteLength===0)throw new TypeError(\"chunk's buffer must have non-zero byteLength\");if(this._closeRequested)throw new TypeError(\"stream is closed or draining\");const r=this._controlledReadableByteStream._state;if(r!==\"readable\")throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be enqueued to`);Tt(this,t)}error(t=void 0){if(!Ae(this))throw nt(\"error\");Z(this,t)}[ar](t){En(this),Se(this);const r=this._cancelAlgorithm(t);return Rt(this),r}[sr](t){const r=this._controlledReadableByteStream;if(this._queueTotalSize>0){In(this,t);return}const s=this._autoAllocateChunkSize;if(s!==void 0){let f;try{f=new ArrayBuffer(s)}catch(d){t._errorSteps(d);return}const c={buffer:f,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:\"default\"};this._pendingPullIntos.push(c)}hn(r,t),Be(this)}[ur](){if(this._pendingPullIntos.length>0){const t=this._pendingPullIntos.peek();t.readerType=\"none\",this._pendingPullIntos=new M,this._pendingPullIntos.push(t)}}}Object.defineProperties(ce.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),h(ce.prototype.close,\"close\"),h(ce.prototype.enqueue,\"enqueue\"),h(ce.prototype.error,\"error\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(ce.prototype,Symbol.toStringTag,{value:\"ReadableByteStreamController\",configurable:!0});function Ae(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableByteStream\")?!1:e instanceof ce}n(Ae,\"IsReadableByteStreamController\");function Tr(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_associatedReadableByteStreamController\")?!1:e instanceof ve}n(Tr,\"IsReadableStreamBYOBRequest\");function Be(e){if(!Ti(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();g(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Be(e)),null),s=>(Z(e,s),null))}n(Be,\"ReadableByteStreamControllerCallPullIfNeeded\");function En(e){Pr(e),e._pendingPullIntos=new M}n(En,\"ReadableByteStreamControllerClearPendingPullIntos\");function Cr(e,t){let r=!1;e._state===\"closed\"&&(r=!0);const s=vn(t);t.readerType===\"default\"?pr(e,s,r):Bi(e,s,r)}n(Cr,\"ReadableByteStreamControllerCommitPullIntoDescriptor\");function vn(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}n(vn,\"ReadableByteStreamControllerConvertPullIntoDescriptor\");function wt(e,t,r,s){e._queue.push({buffer:t,byteOffset:r,byteLength:s}),e._queueTotalSize+=s}n(wt,\"ReadableByteStreamControllerEnqueueChunkToQueue\");function An(e,t,r,s){let f;try{f=Rn(t,r,r+s)}catch(c){throw Z(e,c),c}wt(e,f,0,s)}n(An,\"ReadableByteStreamControllerEnqueueClonedChunkToQueue\");function Bn(e,t){t.bytesFilled>0&&An(e,t.buffer,t.byteOffset,t.bytesFilled),je(e)}n(Bn,\"ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue\");function Wn(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),s=t.bytesFilled+r;let f=r,c=!1;const d=s%t.elementSize,p=s-d;p>=t.minimumFill&&(f=p-t.bytesFilled,c=!0);const R=e._queue;for(;f>0;){const y=R.peek(),C=Math.min(f,y.byteLength),P=t.byteOffset+t.bytesFilled;wn(t.buffer,P,y.buffer,y.byteOffset,C),y.byteLength===C?R.shift():(y.byteOffset+=C,y.byteLength-=C),e._queueTotalSize-=C,kn(e,C,t),f-=C}return c}n(Wn,\"ReadableByteStreamControllerFillPullIntoDescriptorFromQueue\");function kn(e,t,r){r.bytesFilled+=t}n(kn,\"ReadableByteStreamControllerFillHeadPullIntoDescriptor\");function qn(e){e._queueTotalSize===0&&e._closeRequested?(Rt(e),lt(e._controlledReadableByteStream)):Be(e)}n(qn,\"ReadableByteStreamControllerHandleQueueDrain\");function Pr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}n(Pr,\"ReadableByteStreamControllerInvalidateBYOBRequest\");function Er(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;const t=e._pendingPullIntos.peek();Wn(e,t)&&(je(e),Cr(e._controlledReadableByteStream,t))}}n(Er,\"ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue\");function _i(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(e._queueTotalSize===0)return;const r=t._readRequests.shift();In(e,r)}}n(_i,\"ReadableByteStreamControllerProcessReadRequestsUsingQueue\");function Si(e,t,r,s){const f=e._controlledReadableByteStream,c=t.constructor,d=gi(c),{byteOffset:p,byteLength:R}=t,y=r*d;let C;try{C=fe(t.buffer)}catch(B){s._errorSteps(B);return}const P={buffer:C,bufferByteLength:C.byteLength,byteOffset:p,byteLength:R,bytesFilled:0,minimumFill:y,elementSize:d,viewConstructor:c,readerType:\"byob\"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(P),Ln(f,s);return}if(f._state===\"closed\"){const B=new c(P.buffer,P.byteOffset,0);s._closeSteps(B);return}if(e._queueTotalSize>0){if(Wn(e,P)){const B=vn(P);qn(e),s._chunkSteps(B);return}if(e._closeRequested){const B=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");Z(e,B),s._errorSteps(B);return}}e._pendingPullIntos.push(P),Ln(f,s),Be(e)}n(Si,\"ReadableByteStreamControllerPullInto\");function wi(e,t){t.readerType===\"none\"&&je(e);const r=e._controlledReadableByteStream;if(Br(r))for(;Dn(r)>0;){const s=je(e);Cr(r,s)}}n(wi,\"ReadableByteStreamControllerRespondInClosedState\");function Ri(e,t,r){if(kn(e,t,r),r.readerType===\"none\"){Bn(e,r),Er(e);return}if(r.bytesFilled<r.minimumFill)return;je(e);const s=r.bytesFilled%r.elementSize;if(s>0){const f=r.byteOffset+r.bytesFilled;An(e,r.buffer,f-s,s)}r.bytesFilled-=s,Cr(e._controlledReadableByteStream,r),Er(e)}n(Ri,\"ReadableByteStreamControllerRespondInReadableState\");function On(e,t){const r=e._pendingPullIntos.peek();Pr(e),e._controlledReadableByteStream._state===\"closed\"?wi(e,r):Ri(e,t,r),Be(e)}n(On,\"ReadableByteStreamControllerRespondInternal\");function je(e){return e._pendingPullIntos.shift()}n(je,\"ReadableByteStreamControllerShiftPendingPullInto\");function Ti(e){const t=e._controlledReadableByteStream;return t._state!==\"readable\"||e._closeRequested||!e._started?!1:!!(mn(t)&&gt(t)>0||Br(t)&&Dn(t)>0||Fn(e)>0)}n(Ti,\"ReadableByteStreamControllerShouldCallPull\");function Rt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}n(Rt,\"ReadableByteStreamControllerClearAlgorithms\");function rt(e){const t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!==\"readable\")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){const r=e._pendingPullIntos.peek();if(r.bytesFilled%r.elementSize!==0){const s=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");throw Z(e,s),s}}Rt(e),lt(t)}}n(rt,\"ReadableByteStreamControllerClose\");function Tt(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||r._state!==\"readable\")return;const{buffer:s,byteOffset:f,byteLength:c}=t;if(_e(s))throw new TypeError(\"chunk's buffer is detached and so cannot be enqueued\");const d=fe(s);if(e._pendingPullIntos.length>0){const p=e._pendingPullIntos.peek();if(_e(p.buffer))throw new TypeError(\"The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk\");Pr(e),p.buffer=fe(p.buffer),p.readerType===\"none\"&&Bn(e,p)}if(mn(r))if(_i(e),gt(r)===0)wt(e,d,f,c);else{e._pendingPullIntos.length>0&&je(e);const p=new Uint8Array(d,f,c);pr(r,p,!1)}else Br(r)?(wt(e,d,f,c),Er(e)):wt(e,d,f,c);Be(e)}n(Tt,\"ReadableByteStreamControllerEnqueue\");function Z(e,t){const r=e._controlledReadableByteStream;r._state===\"readable\"&&(En(e),Se(e),Rt(e),lo(r,t))}n(Z,\"ReadableByteStreamControllerError\");function In(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,qn(e);const s=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(s)}n(In,\"ReadableByteStreamControllerFillReadRequestFromQueue\");function vr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(ve.prototype);Pi(s,e,r),e._byobRequest=s}return e._byobRequest}n(vr,\"ReadableByteStreamControllerGetBYOBRequest\");function Fn(e){const t=e._controlledReadableByteStream._state;return t===\"errored\"?null:t===\"closed\"?0:e._strategyHWM-e._queueTotalSize}n(Fn,\"ReadableByteStreamControllerGetDesiredSize\");function Ct(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state===\"closed\"){if(t!==0)throw new TypeError(\"bytesWritten must be 0 when calling respond() on a closed stream\")}else{if(t===0)throw new TypeError(\"bytesWritten must be greater than 0 when calling respond() on a readable stream\");if(r.bytesFilled+t>r.byteLength)throw new RangeError(\"bytesWritten out of range\")}r.buffer=fe(r.buffer),On(e,t)}n(Ct,\"ReadableByteStreamControllerRespond\");function Pt(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state===\"closed\"){if(t.byteLength!==0)throw new TypeError(\"The view's length must be 0 when calling respondWithNewView() on a closed stream\")}else if(t.byteLength===0)throw new TypeError(\"The view's length must be greater than 0 when calling respondWithNewView() on a readable stream\");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError(\"The region specified by view does not match byobRequest\");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError(\"The buffer of view has different capacity than byobRequest\");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError(\"The region specified by view is larger than byobRequest\");const f=t.byteLength;r.buffer=fe(t.buffer),On(e,f)}n(Pt,\"ReadableByteStreamControllerRespondWithNewView\");function zn(e,t,r,s,f,c,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Se(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=f,t._autoAllocateChunkSize=d,t._pendingPullIntos=new M,e._readableStreamController=t;const p=r();g(T(p),()=>(t._started=!0,Be(t),null),R=>(Z(t,R),null))}n(zn,\"SetUpReadableByteStreamController\");function Ci(e,t,r){const s=Object.create(ce.prototype);let f,c,d;t.start!==void 0?f=n(()=>t.start(s),\"startAlgorithm\"):f=n(()=>{},\"startAlgorithm\"),t.pull!==void 0?c=n(()=>t.pull(s),\"pullAlgorithm\"):c=n(()=>T(void 0),\"pullAlgorithm\"),t.cancel!==void 0?d=n(R=>t.cancel(R),\"cancelAlgorithm\"):d=n(()=>T(void 0),\"cancelAlgorithm\");const p=t.autoAllocateChunkSize;if(p===0)throw new TypeError(\"autoAllocateChunkSize must be greater than 0\");zn(e,s,f,c,d,r,p)}n(Ci,\"SetUpReadableByteStreamControllerFromUnderlyingSource\");function Pi(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}n(Pi,\"SetUpReadableStreamBYOBRequest\");function Ar(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}n(Ar,\"byobRequestBrandCheckException\");function nt(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}n(nt,\"byteStreamControllerBrandCheckException\");function Ei(e,t){ne(e,t);const r=e?.mode;return{mode:r===void 0?void 0:vi(r,`${t} has member 'mode' that`)}}n(Ei,\"convertReaderOptions\");function vi(e,t){if(e=`${e}`,e!==\"byob\")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}n(vi,\"convertReadableStreamReaderMode\");function Ai(e,t){var r;ne(e,t);const s=(r=e?.min)!==null&&r!==void 0?r:1;return{min:mr(s,`${t} has member 'min' that`)}}n(Ai,\"convertByobReadOptions\");function jn(e){return new we(e)}n(jn,\"AcquireReadableStreamBYOBReader\");function Ln(e,t){e._reader._readIntoRequests.push(t)}n(Ln,\"ReadableStreamAddReadIntoRequest\");function Bi(e,t,r){const f=e._reader._readIntoRequests.shift();r?f._closeSteps(t):f._chunkSteps(t)}n(Bi,\"ReadableStreamFulfillReadIntoRequest\");function Dn(e){return e._reader._readIntoRequests.length}n(Dn,\"ReadableStreamGetNumReadIntoRequests\");function Br(e){const t=e._reader;return!(t===void 0||!We(t))}n(Br,\"ReadableStreamHasBYOBReader\");class we{static{n(this,\"ReadableStreamBYOBReader\")}constructor(t){if(le(t,1,\"ReadableStreamBYOBReader\"),br(t,\"First parameter\"),Ce(t))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");if(!Ae(t._readableStreamController))throw new TypeError(\"Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source\");sn(this,t),this._readIntoRequests=new M}get closed(){return We(this)?this._closedPromise:b(Et(\"closed\"))}cancel(t=void 0){return We(this)?this._ownerReadableStream===void 0?b(yt(\"cancel\")):lr(this,t):b(Et(\"cancel\"))}read(t,r={}){if(!We(this))return b(Et(\"read\"));if(!ArrayBuffer.isView(t))return b(new TypeError(\"view must be an array buffer view\"));if(t.byteLength===0)return b(new TypeError(\"view must have non-zero byteLength\"));if(t.buffer.byteLength===0)return b(new TypeError(\"view's buffer must have non-zero byteLength\"));if(_e(t.buffer))return b(new TypeError(\"view's buffer has been detached\"));let s;try{s=Ai(r,\"options\")}catch(y){return b(y)}const f=s.min;if(f===0)return b(new TypeError(\"options.min must be greater than 0\"));if(yi(t)){if(f>t.byteLength)return b(new RangeError(\"options.min must be less than or equal to view's byteLength\"))}else if(f>t.length)return b(new RangeError(\"options.min must be less than or equal to view's length\"));if(this._ownerReadableStream===void 0)return b(yt(\"read from\"));let c,d;const p=A((y,C)=>{c=y,d=C});return $n(this,t,f,{_chunkSteps:n(y=>c({value:y,done:!1}),\"_chunkSteps\"),_closeSteps:n(y=>c({value:y,done:!0}),\"_closeSteps\"),_errorSteps:n(y=>d(y),\"_errorSteps\")}),p}releaseLock(){if(!We(this))throw Et(\"releaseLock\");this._ownerReadableStream!==void 0&&Wi(this)}}Object.defineProperties(we.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),h(we.prototype.cancel,\"cancel\"),h(we.prototype.read,\"read\"),h(we.prototype.releaseLock,\"releaseLock\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(we.prototype,Symbol.toStringTag,{value:\"ReadableStreamBYOBReader\",configurable:!0});function We(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_readIntoRequests\")?!1:e instanceof we}n(We,\"IsReadableStreamBYOBReader\");function $n(e,t,r,s){const f=e._ownerReadableStream;f._disturbed=!0,f._state===\"errored\"?s._errorSteps(f._storedError):Si(f._readableStreamController,t,r,s)}n($n,\"ReadableStreamBYOBReaderRead\");function Wi(e){ue(e);const t=new TypeError(\"Reader was released\");Mn(e,t)}n(Wi,\"ReadableStreamBYOBReaderRelease\");function Mn(e,t){const r=e._readIntoRequests;e._readIntoRequests=new M,r.forEach(s=>{s._errorSteps(t)})}n(Mn,\"ReadableStreamBYOBReaderErrorReadIntoRequests\");function Et(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}n(Et,\"byobReaderBrandCheckException\");function ot(e,t){const{highWaterMark:r}=e;if(r===void 0)return t;if(Sn(r)||r<0)throw new RangeError(\"Invalid highWaterMark\");return r}n(ot,\"ExtractHighWaterMark\");function vt(e){const{size:t}=e;return t||(()=>1)}n(vt,\"ExtractSizeAlgorithm\");function At(e,t){ne(e,t);const r=e?.highWaterMark,s=e?.size;return{highWaterMark:r===void 0?void 0:hr(r),size:s===void 0?void 0:ki(s,`${t} has member 'size' that`)}}n(At,\"convertQueuingStrategy\");function ki(e,t){return G(e,t),r=>hr(e(r))}n(ki,\"convertQueuingStrategySize\");function qi(e,t){ne(e,t);const r=e?.abort,s=e?.close,f=e?.start,c=e?.type,d=e?.write;return{abort:r===void 0?void 0:Oi(r,e,`${t} has member 'abort' that`),close:s===void 0?void 0:Ii(s,e,`${t} has member 'close' that`),start:f===void 0?void 0:Fi(f,e,`${t} has member 'start' that`),write:d===void 0?void 0:zi(d,e,`${t} has member 'write' that`),type:c}}n(qi,\"convertUnderlyingSink\");function Oi(e,t,r){return G(e,r),s=>z(e,t,[s])}n(Oi,\"convertUnderlyingSinkAbortCallback\");function Ii(e,t,r){return G(e,r),()=>z(e,t,[])}n(Ii,\"convertUnderlyingSinkCloseCallback\");function Fi(e,t,r){return G(e,r),s=>O(e,t,[s])}n(Fi,\"convertUnderlyingSinkStartCallback\");function zi(e,t,r){return G(e,r),(s,f)=>z(e,t,[s,f])}n(zi,\"convertUnderlyingSinkWriteCallback\");function Un(e,t){if(!Le(e))throw new TypeError(`${t} is not a WritableStream.`)}n(Un,\"assertWritableStream\");function ji(e){if(typeof e!=\"object\"||e===null)return!1;try{return typeof e.aborted==\"boolean\"}catch{return!1}}n(ji,\"isAbortSignal\");const Li=typeof AbortController==\"function\";function Di(){if(Li)return new AbortController}n(Di,\"createAbortController\");class Re{static{n(this,\"WritableStream\")}constructor(t={},r={}){t===void 0?t=null:cn(t,\"First parameter\");const s=At(r,\"Second parameter\"),f=qi(t,\"First parameter\");if(Nn(this),f.type!==void 0)throw new RangeError(\"Invalid type is specified\");const d=vt(s),p=ot(s,1);Xi(this,f,p,d)}get locked(){if(!Le(this))throw Ot(\"locked\");return De(this)}abort(t=void 0){return Le(this)?De(this)?b(new TypeError(\"Cannot abort a stream that already has a writer\")):Bt(this,t):b(Ot(\"abort\"))}close(){return Le(this)?De(this)?b(new TypeError(\"Cannot close a stream that already has a writer\")):oe(this)?b(new TypeError(\"Cannot close an already-closing stream\")):Hn(this):b(Ot(\"close\"))}getWriter(){if(!Le(this))throw Ot(\"getWriter\");return xn(this)}}Object.defineProperties(Re.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),h(Re.prototype.abort,\"abort\"),h(Re.prototype.close,\"close\"),h(Re.prototype.getWriter,\"getWriter\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(Re.prototype,Symbol.toStringTag,{value:\"WritableStream\",configurable:!0});function xn(e){return new de(e)}n(xn,\"AcquireWritableStreamDefaultWriter\");function $i(e,t,r,s,f=1,c=()=>1){const d=Object.create(Re.prototype);Nn(d);const p=Object.create($e.prototype);return Kn(d,p,e,t,r,s,f,c),d}n($i,\"CreateWritableStream\");function Nn(e){e._state=\"writable\",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new M,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}n(Nn,\"InitializeWritableStream\");function Le(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_writableStreamController\")?!1:e instanceof Re}n(Le,\"IsWritableStream\");function De(e){return e._writer!==void 0}n(De,\"IsWritableStreamLocked\");function Bt(e,t){var r;if(e._state===\"closed\"||e._state===\"errored\")return T(void 0);e._writableStreamController._abortReason=t,(r=e._writableStreamController._abortController)===null||r===void 0||r.abort(t);const s=e._state;if(s===\"closed\"||s===\"errored\")return T(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let f=!1;s===\"erroring\"&&(f=!0,t=void 0);const c=A((d,p)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:p,_reason:t,_wasAlreadyErroring:f}});return e._pendingAbortRequest._promise=c,f||kr(e,t),c}n(Bt,\"WritableStreamAbort\");function Hn(e){const t=e._state;if(t===\"closed\"||t===\"errored\")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=A((f,c)=>{const d={_resolve:f,_reject:c};e._closeRequest=d}),s=e._writer;return s!==void 0&&e._backpressure&&t===\"writable\"&&Dr(s),ea(e._writableStreamController),r}n(Hn,\"WritableStreamClose\");function Mi(e){return A((r,s)=>{const f={_resolve:r,_reject:s};e._writeRequests.push(f)})}n(Mi,\"WritableStreamAddWriteRequest\");function Wr(e,t){if(e._state===\"writable\"){kr(e,t);return}qr(e)}n(Wr,\"WritableStreamDealWithRejection\");function kr(e,t){const r=e._writableStreamController;e._state=\"erroring\",e._storedError=t;const s=e._writer;s!==void 0&&Qn(s,t),!Vi(e)&&r._started&&qr(e)}n(kr,\"WritableStreamStartErroring\");function qr(e){e._state=\"errored\",e._writableStreamController[an]();const t=e._storedError;if(e._writeRequests.forEach(f=>{f._reject(t)}),e._writeRequests=new M,e._pendingAbortRequest===void 0){Wt(e);return}const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),Wt(e);return}const s=e._writableStreamController[pt](r._reason);g(s,()=>(r._resolve(),Wt(e),null),f=>(r._reject(f),Wt(e),null))}n(qr,\"WritableStreamFinishErroring\");function Ui(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}n(Ui,\"WritableStreamFinishInFlightWrite\");function xi(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Wr(e,t)}n(xi,\"WritableStreamFinishInFlightWriteWithError\");function Ni(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state===\"erroring\"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state=\"closed\";const r=e._writer;r!==void 0&&to(r)}n(Ni,\"WritableStreamFinishInFlightClose\");function Hi(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Wr(e,t)}n(Hi,\"WritableStreamFinishInFlightCloseWithError\");function oe(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}n(oe,\"WritableStreamCloseQueuedOrInFlight\");function Vi(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}n(Vi,\"WritableStreamHasOperationMarkedInFlight\");function Qi(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}n(Qi,\"WritableStreamMarkCloseRequestInFlight\");function Yi(e){e._inFlightWriteRequest=e._writeRequests.shift()}n(Yi,\"WritableStreamMarkFirstWriteRequestInFlight\");function Wt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;t!==void 0&&jr(t,e._storedError)}n(Wt,\"WritableStreamRejectCloseAndClosedPromiseIfNeeded\");function Or(e,t){const r=e._writer;r!==void 0&&t!==e._backpressure&&(t?sa(r):Dr(r)),e._backpressure=t}n(Or,\"WritableStreamUpdateBackpressure\");class de{static{n(this,\"WritableStreamDefaultWriter\")}constructor(t){if(le(t,1,\"WritableStreamDefaultWriter\"),Un(t,\"First parameter\"),De(t))throw new TypeError(\"This stream has already been locked for exclusive writing by another writer\");this._ownerWritableStream=t,t._writer=this;const r=t._state;if(r===\"writable\")!oe(t)&&t._backpressure?Ft(this):ro(this),It(this);else if(r===\"erroring\")Lr(this,t._storedError),It(this);else if(r===\"closed\")ro(this),ia(this);else{const s=t._storedError;Lr(this,s),eo(this,s)}}get closed(){return ke(this)?this._closedPromise:b(qe(\"closed\"))}get desiredSize(){if(!ke(this))throw qe(\"desiredSize\");if(this._ownerWritableStream===void 0)throw at(\"desiredSize\");return Ji(this)}get ready(){return ke(this)?this._readyPromise:b(qe(\"ready\"))}abort(t=void 0){return ke(this)?this._ownerWritableStream===void 0?b(at(\"abort\")):Gi(this,t):b(qe(\"abort\"))}close(){if(!ke(this))return b(qe(\"close\"));const t=this._ownerWritableStream;return t===void 0?b(at(\"close\")):oe(t)?b(new TypeError(\"Cannot close an already-closing stream\")):Vn(this)}releaseLock(){if(!ke(this))throw qe(\"releaseLock\");this._ownerWritableStream!==void 0&&Yn(this)}write(t=void 0){return ke(this)?this._ownerWritableStream===void 0?b(at(\"write to\")):Gn(this,t):b(qe(\"write\"))}}Object.defineProperties(de.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),h(de.prototype.abort,\"abort\"),h(de.prototype.close,\"close\"),h(de.prototype.releaseLock,\"releaseLock\"),h(de.prototype.write,\"write\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(de.prototype,Symbol.toStringTag,{value:\"WritableStreamDefaultWriter\",configurable:!0});function ke(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_ownerWritableStream\")?!1:e instanceof de}n(ke,\"IsWritableStreamDefaultWriter\");function Gi(e,t){const r=e._ownerWritableStream;return Bt(r,t)}n(Gi,\"WritableStreamDefaultWriterAbort\");function Vn(e){const t=e._ownerWritableStream;return Hn(t)}n(Vn,\"WritableStreamDefaultWriterClose\");function Zi(e){const t=e._ownerWritableStream,r=t._state;return oe(t)||r===\"closed\"?T(void 0):r===\"errored\"?b(t._storedError):Vn(e)}n(Zi,\"WritableStreamDefaultWriterCloseWithErrorPropagation\");function Ki(e,t){e._closedPromiseState===\"pending\"?jr(e,t):aa(e,t)}n(Ki,\"WritableStreamDefaultWriterEnsureClosedPromiseRejected\");function Qn(e,t){e._readyPromiseState===\"pending\"?no(e,t):ua(e,t)}n(Qn,\"WritableStreamDefaultWriterEnsureReadyPromiseRejected\");function Ji(e){const t=e._ownerWritableStream,r=t._state;return r===\"errored\"||r===\"erroring\"?null:r===\"closed\"?0:Jn(t._writableStreamController)}n(Ji,\"WritableStreamDefaultWriterGetDesiredSize\");function Yn(e){const t=e._ownerWritableStream,r=new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");Qn(e,r),Ki(e,r),t._writer=void 0,e._ownerWritableStream=void 0}n(Yn,\"WritableStreamDefaultWriterRelease\");function Gn(e,t){const r=e._ownerWritableStream,s=r._writableStreamController,f=ta(s,t);if(r!==e._ownerWritableStream)return b(at(\"write to\"));const c=r._state;if(c===\"errored\")return b(r._storedError);if(oe(r)||c===\"closed\")return b(new TypeError(\"The stream is closing or closed and cannot be written to\"));if(c===\"erroring\")return b(r._storedError);const d=Mi(r);return ra(s,t,f),d}n(Gn,\"WritableStreamDefaultWriterWrite\");const Zn={};class $e{static{n(this,\"WritableStreamDefaultController\")}constructor(){throw new TypeError(\"Illegal constructor\")}get abortReason(){if(!Ir(this))throw zr(\"abortReason\");return this._abortReason}get signal(){if(!Ir(this))throw zr(\"signal\");if(this._abortController===void 0)throw new TypeError(\"WritableStreamDefaultController.prototype.signal is not supported\");return this._abortController.signal}error(t=void 0){if(!Ir(this))throw zr(\"error\");this._controlledWritableStream._state===\"writable\"&&Xn(this,t)}[pt](t){const r=this._abortAlgorithm(t);return kt(this),r}[an](){Se(this)}}Object.defineProperties($e.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty($e.prototype,Symbol.toStringTag,{value:\"WritableStreamDefaultController\",configurable:!0});function Ir(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_controlledWritableStream\")?!1:e instanceof $e}n(Ir,\"IsWritableStreamDefaultController\");function Kn(e,t,r,s,f,c,d,p){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._abortReason=void 0,t._abortController=Di(),t._started=!1,t._strategySizeAlgorithm=p,t._strategyHWM=d,t._writeAlgorithm=s,t._closeAlgorithm=f,t._abortAlgorithm=c;const R=Fr(t);Or(e,R);const y=r(),C=T(y);g(C,()=>(t._started=!0,qt(t),null),P=>(t._started=!0,Wr(e,P),null))}n(Kn,\"SetUpWritableStreamDefaultController\");function Xi(e,t,r,s){const f=Object.create($e.prototype);let c,d,p,R;t.start!==void 0?c=n(()=>t.start(f),\"startAlgorithm\"):c=n(()=>{},\"startAlgorithm\"),t.write!==void 0?d=n(y=>t.write(y,f),\"writeAlgorithm\"):d=n(()=>T(void 0),\"writeAlgorithm\"),t.close!==void 0?p=n(()=>t.close(),\"closeAlgorithm\"):p=n(()=>T(void 0),\"closeAlgorithm\"),t.abort!==void 0?R=n(y=>t.abort(y),\"abortAlgorithm\"):R=n(()=>T(void 0),\"abortAlgorithm\"),Kn(e,f,c,d,p,R,r,s)}n(Xi,\"SetUpWritableStreamDefaultControllerFromUnderlyingSink\");function kt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(kt,\"WritableStreamDefaultControllerClearAlgorithms\");function ea(e){Rr(e,Zn,0),qt(e)}n(ea,\"WritableStreamDefaultControllerClose\");function ta(e,t){try{return e._strategySizeAlgorithm(t)}catch(r){return it(e,r),1}}n(ta,\"WritableStreamDefaultControllerGetChunkSize\");function Jn(e){return e._strategyHWM-e._queueTotalSize}n(Jn,\"WritableStreamDefaultControllerGetDesiredSize\");function ra(e,t,r){try{Rr(e,t,r)}catch(f){it(e,f);return}const s=e._controlledWritableStream;if(!oe(s)&&s._state===\"writable\"){const f=Fr(e);Or(s,f)}qt(e)}n(ra,\"WritableStreamDefaultControllerWrite\");function qt(e){const t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state===\"erroring\"){qr(t);return}if(e._queue.length===0)return;const s=pi(e);s===Zn?na(e):oa(e,s)}n(qt,\"WritableStreamDefaultControllerAdvanceQueueIfNeeded\");function it(e,t){e._controlledWritableStream._state===\"writable\"&&Xn(e,t)}n(it,\"WritableStreamDefaultControllerErrorIfNeeded\");function na(e){const t=e._controlledWritableStream;Qi(t),wr(e);const r=e._closeAlgorithm();kt(e),g(r,()=>(Ni(t),null),s=>(Hi(t,s),null))}n(na,\"WritableStreamDefaultControllerProcessClose\");function oa(e,t){const r=e._controlledWritableStream;Yi(r);const s=e._writeAlgorithm(t);g(s,()=>{Ui(r);const f=r._state;if(wr(e),!oe(r)&&f===\"writable\"){const c=Fr(e);Or(r,c)}return qt(e),null},f=>(r._state===\"writable\"&&kt(e),xi(r,f),null))}n(oa,\"WritableStreamDefaultControllerProcessWrite\");function Fr(e){return Jn(e)<=0}n(Fr,\"WritableStreamDefaultControllerGetBackpressure\");function Xn(e,t){const r=e._controlledWritableStream;kt(e),kr(r,t)}n(Xn,\"WritableStreamDefaultControllerError\");function Ot(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}n(Ot,\"streamBrandCheckException$2\");function zr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}n(zr,\"defaultControllerBrandCheckException$2\");function qe(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}n(qe,\"defaultWriterBrandCheckException\");function at(e){return new TypeError(\"Cannot \"+e+\" a stream using a released writer\")}n(at,\"defaultWriterLockException\");function It(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState=\"pending\"})}n(It,\"defaultWriterClosedPromiseInitialize\");function eo(e,t){It(e),jr(e,t)}n(eo,\"defaultWriterClosedPromiseInitializeAsRejected\");function ia(e){It(e),to(e)}n(ia,\"defaultWriterClosedPromiseInitializeAsResolved\");function jr(e,t){e._closedPromise_reject!==void 0&&(Q(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"rejected\")}n(jr,\"defaultWriterClosedPromiseReject\");function aa(e,t){eo(e,t)}n(aa,\"defaultWriterClosedPromiseResetToRejected\");function to(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"resolved\")}n(to,\"defaultWriterClosedPromiseResolve\");function Ft(e){e._readyPromise=A((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState=\"pending\"}n(Ft,\"defaultWriterReadyPromiseInitialize\");function Lr(e,t){Ft(e),no(e,t)}n(Lr,\"defaultWriterReadyPromiseInitializeAsRejected\");function ro(e){Ft(e),Dr(e)}n(ro,\"defaultWriterReadyPromiseInitializeAsResolved\");function no(e,t){e._readyPromise_reject!==void 0&&(Q(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"rejected\")}n(no,\"defaultWriterReadyPromiseReject\");function sa(e){Ft(e)}n(sa,\"defaultWriterReadyPromiseReset\");function ua(e,t){Lr(e,t)}n(ua,\"defaultWriterReadyPromiseResetToRejected\");function Dr(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"fulfilled\")}n(Dr,\"defaultWriterReadyPromiseResolve\");function la(){if(typeof globalThis<\"u\")return globalThis;if(typeof self<\"u\")return self;if(typeof _shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_6__.c<\"u\")return _shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_6__.c}n(la,\"getGlobals\");const $r=la();function fa(e){if(!(typeof e==\"function\"||typeof e==\"object\")||e.name!==\"DOMException\")return!1;try{return new e,!0}catch{return!1}}n(fa,\"isDOMExceptionConstructor\");function ca(){const e=$r?.DOMException;return fa(e)?e:void 0}n(ca,\"getFromGlobal\");function da(){const e=n(function(r,s){this.message=r||\"\",this.name=s||\"Error\",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)},\"DOMException\");return h(e,\"DOMException\"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,\"constructor\",{value:e,writable:!0,configurable:!0}),e}n(da,\"createPolyfill\");const ha=ca()||da();function oo(e,t,r,s,f,c){const d=ze(e),p=xn(t);e._disturbed=!0;let R=!1,y=T(void 0);return A((C,P)=>{let B;if(c!==void 0){if(B=n(()=>{const _=c.reason!==void 0?c.reason:new ha(\"Aborted\",\"AbortError\"),v=[];s||v.push(()=>t._state===\"writable\"?Bt(t,_):T(void 0)),f||v.push(()=>e._state===\"readable\"?X(e,_):T(void 0)),x(()=>Promise.all(v.map(W=>W())),!0,_)},\"abortAlgorithm\"),c.aborted){B();return}c.addEventListener(\"abort\",B)}function ee(){return A((_,v)=>{function W(Y){Y?_():q(Ne(),W,v)}n(W,\"next\"),W(!1)})}n(ee,\"pipeLoop\");function Ne(){return R?T(!0):q(p._readyPromise,()=>A((_,v)=>{et(d,{_chunkSteps:n(W=>{y=q(Gn(p,W),void 0,l),_(!1)},\"_chunkSteps\"),_closeSteps:n(()=>_(!0),\"_closeSteps\"),_errorSteps:v})}))}if(n(Ne,\"pipeStep\"),me(e,d._closedPromise,_=>(s?K(!0,_):x(()=>Bt(t,_),!0,_),null)),me(t,p._closedPromise,_=>(f?K(!0,_):x(()=>X(e,_),!0,_),null)),U(e,d._closedPromise,()=>(r?K():x(()=>Zi(p)),null)),oe(t)||t._state===\"closed\"){const _=new TypeError(\"the destination writable stream closed before all data could be piped to it\");f?K(!0,_):x(()=>X(e,_),!0,_)}Q(ee());function Ee(){const _=y;return q(y,()=>_!==y?Ee():void 0)}n(Ee,\"waitForWritesToFinish\");function me(_,v,W){_._state===\"errored\"?W(_._storedError):I(v,W)}n(me,\"isOrBecomesErrored\");function U(_,v,W){_._state===\"closed\"?W():V(v,W)}n(U,\"isOrBecomesClosed\");function x(_,v,W){if(R)return;R=!0,t._state===\"writable\"&&!oe(t)?V(Ee(),Y):Y();function Y(){return g(_(),()=>be(v,W),He=>be(!0,He)),null}n(Y,\"doTheRest\")}n(x,\"shutdownWithAction\");function K(_,v){R||(R=!0,t._state===\"writable\"&&!oe(t)?V(Ee(),()=>be(_,v)):be(_,v))}n(K,\"shutdown\");function be(_,v){return Yn(p),ue(d),c!==void 0&&c.removeEventListener(\"abort\",B),_?P(v):C(void 0),null}n(be,\"finalize\")})}n(oo,\"ReadableStreamPipeTo\");class he{static{n(this,\"ReadableStreamDefaultController\")}constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!zt(this))throw Lt(\"desiredSize\");return Mr(this)}close(){if(!zt(this))throw Lt(\"close\");if(!Ue(this))throw new TypeError(\"The stream is not in a state that permits close\");Oe(this)}enqueue(t=void 0){if(!zt(this))throw Lt(\"enqueue\");if(!Ue(this))throw new TypeError(\"The stream is not in a state that permits enqueue\");return Me(this,t)}error(t=void 0){if(!zt(this))throw Lt(\"error\");J(this,t)}[ar](t){Se(this);const r=this._cancelAlgorithm(t);return jt(this),r}[sr](t){const r=this._controlledReadableStream;if(this._queue.length>0){const s=wr(this);this._closeRequested&&this._queue.length===0?(jt(this),lt(r)):st(this),t._chunkSteps(s)}else hn(r,t),st(this)}[ur](){}}Object.defineProperties(he.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),h(he.prototype.close,\"close\"),h(he.prototype.enqueue,\"enqueue\"),h(he.prototype.error,\"error\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(he.prototype,Symbol.toStringTag,{value:\"ReadableStreamDefaultController\",configurable:!0});function zt(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableStream\")?!1:e instanceof he}n(zt,\"IsReadableStreamDefaultController\");function st(e){if(!io(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();g(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,st(e)),null),s=>(J(e,s),null))}n(st,\"ReadableStreamDefaultControllerCallPullIfNeeded\");function io(e){const t=e._controlledReadableStream;return!Ue(e)||!e._started?!1:!!(Ce(t)&&gt(t)>0||Mr(e)>0)}n(io,\"ReadableStreamDefaultControllerShouldCallPull\");function jt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(jt,\"ReadableStreamDefaultControllerClearAlgorithms\");function Oe(e){if(!Ue(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(jt(e),lt(t))}n(Oe,\"ReadableStreamDefaultControllerClose\");function Me(e,t){if(!Ue(e))return;const r=e._controlledReadableStream;if(Ce(r)&&gt(r)>0)pr(r,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(f){throw J(e,f),f}try{Rr(e,t,s)}catch(f){throw J(e,f),f}}st(e)}n(Me,\"ReadableStreamDefaultControllerEnqueue\");function J(e,t){const r=e._controlledReadableStream;r._state===\"readable\"&&(Se(e),jt(e),lo(r,t))}n(J,\"ReadableStreamDefaultControllerError\");function Mr(e){const t=e._controlledReadableStream._state;return t===\"errored\"?null:t===\"closed\"?0:e._strategyHWM-e._queueTotalSize}n(Mr,\"ReadableStreamDefaultControllerGetDesiredSize\");function ma(e){return!io(e)}n(ma,\"ReadableStreamDefaultControllerHasBackpressure\");function Ue(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&t===\"readable\"}n(Ue,\"ReadableStreamDefaultControllerCanCloseOrEnqueue\");function ao(e,t,r,s,f,c,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Se(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=f,e._readableStreamController=t;const p=r();g(T(p),()=>(t._started=!0,st(t),null),R=>(J(t,R),null))}n(ao,\"SetUpReadableStreamDefaultController\");function ba(e,t,r,s){const f=Object.create(he.prototype);let c,d,p;t.start!==void 0?c=n(()=>t.start(f),\"startAlgorithm\"):c=n(()=>{},\"startAlgorithm\"),t.pull!==void 0?d=n(()=>t.pull(f),\"pullAlgorithm\"):d=n(()=>T(void 0),\"pullAlgorithm\"),t.cancel!==void 0?p=n(R=>t.cancel(R),\"cancelAlgorithm\"):p=n(()=>T(void 0),\"cancelAlgorithm\"),ao(e,f,c,d,p,r,s)}n(ba,\"SetUpReadableStreamDefaultControllerFromUnderlyingSource\");function Lt(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}n(Lt,\"defaultControllerBrandCheckException$1\");function pa(e,t){return Ae(e._readableStreamController)?ga(e):ya(e)}n(pa,\"ReadableStreamTee\");function ya(e,t){const r=ze(e);let s=!1,f=!1,c=!1,d=!1,p,R,y,C,P;const B=A(U=>{P=U});function ee(){return s?(f=!0,T(void 0)):(s=!0,et(r,{_chunkSteps:n(x=>{se(()=>{f=!1;const K=x,be=x;c||Me(y._readableStreamController,K),d||Me(C._readableStreamController,be),s=!1,f&&ee()})},\"_chunkSteps\"),_closeSteps:n(()=>{s=!1,c||Oe(y._readableStreamController),d||Oe(C._readableStreamController),(!c||!d)&&P(void 0)},\"_closeSteps\"),_errorSteps:n(()=>{s=!1},\"_errorSteps\")}),T(void 0))}n(ee,\"pullAlgorithm\");function Ne(U){if(c=!0,p=U,d){const x=tt([p,R]),K=X(e,x);P(K)}return B}n(Ne,\"cancel1Algorithm\");function Ee(U){if(d=!0,R=U,c){const x=tt([p,R]),K=X(e,x);P(K)}return B}n(Ee,\"cancel2Algorithm\");function me(){}return n(me,\"startAlgorithm\"),y=ut(me,ee,Ne),C=ut(me,ee,Ee),I(r._closedPromise,U=>(J(y._readableStreamController,U),J(C._readableStreamController,U),(!c||!d)&&P(void 0),null)),[y,C]}n(ya,\"ReadableStreamDefaultTee\");function ga(e){let t=ze(e),r=!1,s=!1,f=!1,c=!1,d=!1,p,R,y,C,P;const B=A(_=>{P=_});function ee(_){I(_._closedPromise,v=>(_!==t||(Z(y._readableStreamController,v),Z(C._readableStreamController,v),(!c||!d)&&P(void 0)),null))}n(ee,\"forwardReaderError\");function Ne(){We(t)&&(ue(t),t=ze(e),ee(t)),et(t,{_chunkSteps:n(v=>{se(()=>{s=!1,f=!1;const W=v;let Y=v;if(!c&&!d)try{Y=Cn(v)}catch(He){Z(y._readableStreamController,He),Z(C._readableStreamController,He),P(X(e,He));return}c||Tt(y._readableStreamController,W),d||Tt(C._readableStreamController,Y),r=!1,s?me():f&&U()})},\"_chunkSteps\"),_closeSteps:n(()=>{r=!1,c||rt(y._readableStreamController),d||rt(C._readableStreamController),y._readableStreamController._pendingPullIntos.length>0&&Ct(y._readableStreamController,0),C._readableStreamController._pendingPullIntos.length>0&&Ct(C._readableStreamController,0),(!c||!d)&&P(void 0)},\"_closeSteps\"),_errorSteps:n(()=>{r=!1},\"_errorSteps\")})}n(Ne,\"pullWithDefaultReader\");function Ee(_,v){ge(t)&&(ue(t),t=jn(e),ee(t));const W=v?C:y,Y=v?y:C;$n(t,_,1,{_chunkSteps:n(Ve=>{se(()=>{s=!1,f=!1;const Qe=v?d:c;if(v?c:d)Qe||Pt(W._readableStreamController,Ve);else{let To;try{To=Cn(Ve)}catch(Vr){Z(W._readableStreamController,Vr),Z(Y._readableStreamController,Vr),P(X(e,Vr));return}Qe||Pt(W._readableStreamController,Ve),Tt(Y._readableStreamController,To)}r=!1,s?me():f&&U()})},\"_chunkSteps\"),_closeSteps:n(Ve=>{r=!1;const Qe=v?d:c,Vt=v?c:d;Qe||rt(W._readableStreamController),Vt||rt(Y._readableStreamController),Ve!==void 0&&(Qe||Pt(W._readableStreamController,Ve),!Vt&&Y._readableStreamController._pendingPullIntos.length>0&&Ct(Y._readableStreamController,0)),(!Qe||!Vt)&&P(void 0)},\"_closeSteps\"),_errorSteps:n(()=>{r=!1},\"_errorSteps\")})}n(Ee,\"pullWithBYOBReader\");function me(){if(r)return s=!0,T(void 0);r=!0;const _=vr(y._readableStreamController);return _===null?Ne():Ee(_._view,!1),T(void 0)}n(me,\"pull1Algorithm\");function U(){if(r)return f=!0,T(void 0);r=!0;const _=vr(C._readableStreamController);return _===null?Ne():Ee(_._view,!0),T(void 0)}n(U,\"pull2Algorithm\");function x(_){if(c=!0,p=_,d){const v=tt([p,R]),W=X(e,v);P(W)}return B}n(x,\"cancel1Algorithm\");function K(_){if(d=!0,R=_,c){const v=tt([p,R]),W=X(e,v);P(W)}return B}n(K,\"cancel2Algorithm\");function be(){}return n(be,\"startAlgorithm\"),y=uo(be,me,x),C=uo(be,U,K),ee(t),[y,C]}n(ga,\"ReadableByteStreamTee\");function _a(e){return u(e)&&typeof e.getReader<\"u\"}n(_a,\"isReadableStreamLike\");function Sa(e){return _a(e)?Ra(e.getReader()):wa(e)}n(Sa,\"ReadableStreamFrom\");function wa(e){let t;const r=Tn(e,\"async\"),s=l;function f(){let d;try{d=di(r)}catch(R){return b(R)}const p=T(d);return F(p,R=>{if(!u(R))throw new TypeError(\"The promise returned by the iterator.next() method must fulfill with an object\");if(hi(R))Oe(t._readableStreamController);else{const C=mi(R);Me(t._readableStreamController,C)}})}n(f,\"pullAlgorithm\");function c(d){const p=r.iterator;let R;try{R=St(p,\"return\")}catch(P){return b(P)}if(R===void 0)return T(void 0);let y;try{y=O(R,p,[d])}catch(P){return b(P)}const C=T(y);return F(C,P=>{if(!u(P))throw new TypeError(\"The promise returned by the iterator.return() method must fulfill with an object\")})}return n(c,\"cancelAlgorithm\"),t=ut(s,f,c,0),t}n(wa,\"ReadableStreamFromIterable\");function Ra(e){let t;const r=l;function s(){let c;try{c=e.read()}catch(d){return b(d)}return F(c,d=>{if(!u(d))throw new TypeError(\"The promise returned by the reader.read() method must fulfill with an object\");if(d.done)Oe(t._readableStreamController);else{const p=d.value;Me(t._readableStreamController,p)}})}n(s,\"pullAlgorithm\");function f(c){try{return T(e.cancel(c))}catch(d){return b(d)}}return n(f,\"cancelAlgorithm\"),t=ut(r,s,f,0),t}n(Ra,\"ReadableStreamFromDefaultReader\");function Ta(e,t){ne(e,t);const r=e,s=r?.autoAllocateChunkSize,f=r?.cancel,c=r?.pull,d=r?.start,p=r?.type;return{autoAllocateChunkSize:s===void 0?void 0:mr(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:f===void 0?void 0:Ca(f,r,`${t} has member 'cancel' that`),pull:c===void 0?void 0:Pa(c,r,`${t} has member 'pull' that`),start:d===void 0?void 0:Ea(d,r,`${t} has member 'start' that`),type:p===void 0?void 0:va(p,`${t} has member 'type' that`)}}n(Ta,\"convertUnderlyingDefaultOrByteSource\");function Ca(e,t,r){return G(e,r),s=>z(e,t,[s])}n(Ca,\"convertUnderlyingSourceCancelCallback\");function Pa(e,t,r){return G(e,r),s=>z(e,t,[s])}n(Pa,\"convertUnderlyingSourcePullCallback\");function Ea(e,t,r){return G(e,r),s=>O(e,t,[s])}n(Ea,\"convertUnderlyingSourceStartCallback\");function va(e,t){if(e=`${e}`,e!==\"bytes\")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}n(va,\"convertReadableStreamType\");function Aa(e,t){return ne(e,t),{preventCancel:!!e?.preventCancel}}n(Aa,\"convertIteratorOptions\");function so(e,t){ne(e,t);const r=e?.preventAbort,s=e?.preventCancel,f=e?.preventClose,c=e?.signal;return c!==void 0&&Ba(c,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!s,preventClose:!!f,signal:c}}n(so,\"convertPipeOptions\");function Ba(e,t){if(!ji(e))throw new TypeError(`${t} is not an AbortSignal.`)}n(Ba,\"assertAbortSignal\");function Wa(e,t){ne(e,t);const r=e?.readable;dr(r,\"readable\",\"ReadableWritablePair\"),br(r,`${t} has member 'readable' that`);const s=e?.writable;return dr(s,\"writable\",\"ReadableWritablePair\"),Un(s,`${t} has member 'writable' that`),{readable:r,writable:s}}n(Wa,\"convertReadableWritablePair\");class L{static{n(this,\"ReadableStream\")}constructor(t={},r={}){t===void 0?t=null:cn(t,\"First parameter\");const s=At(r,\"Second parameter\"),f=Ta(t,\"First parameter\");if(Ur(this),f.type===\"bytes\"){if(s.size!==void 0)throw new RangeError(\"The strategy for a byte stream cannot have a size function\");const c=ot(s,0);Ci(this,f,c)}else{const c=vt(s),d=ot(s,1);ba(this,f,d,c)}}get locked(){if(!Te(this))throw Ie(\"locked\");return Ce(this)}cancel(t=void 0){return Te(this)?Ce(this)?b(new TypeError(\"Cannot cancel a stream that already has a reader\")):X(this,t):b(Ie(\"cancel\"))}getReader(t=void 0){if(!Te(this))throw Ie(\"getReader\");return Ei(t,\"First parameter\").mode===void 0?ze(this):jn(this)}pipeThrough(t,r={}){if(!Te(this))throw Ie(\"pipeThrough\");le(t,1,\"pipeThrough\");const s=Wa(t,\"First parameter\"),f=so(r,\"Second parameter\");if(Ce(this))throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream\");if(De(s.writable))throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream\");const c=oo(this,s.writable,f.preventClose,f.preventAbort,f.preventCancel,f.signal);return Q(c),s.readable}pipeTo(t,r={}){if(!Te(this))return b(Ie(\"pipeTo\"));if(t===void 0)return b(\"Parameter 1 is required in 'pipeTo'.\");if(!Le(t))return b(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));let s;try{s=so(r,\"Second parameter\")}catch(f){return b(f)}return Ce(this)?b(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream\")):De(t)?b(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream\")):oo(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!Te(this))throw Ie(\"tee\");const t=pa(this);return tt(t)}values(t=void 0){if(!Te(this))throw Ie(\"values\");const r=Aa(t,\"First parameter\");return fi(this,r.preventCancel)}[Sr](t){return this.values(t)}static from(t){return Sa(t)}}Object.defineProperties(L,{from:{enumerable:!0}}),Object.defineProperties(L.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),h(L.from,\"from\"),h(L.prototype.cancel,\"cancel\"),h(L.prototype.getReader,\"getReader\"),h(L.prototype.pipeThrough,\"pipeThrough\"),h(L.prototype.pipeTo,\"pipeTo\"),h(L.prototype.tee,\"tee\"),h(L.prototype.values,\"values\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(L.prototype,Symbol.toStringTag,{value:\"ReadableStream\",configurable:!0}),Object.defineProperty(L.prototype,Sr,{value:L.prototype.values,writable:!0,configurable:!0});function ut(e,t,r,s=1,f=()=>1){const c=Object.create(L.prototype);Ur(c);const d=Object.create(he.prototype);return ao(c,d,e,t,r,s,f),c}n(ut,\"CreateReadableStream\");function uo(e,t,r){const s=Object.create(L.prototype);Ur(s);const f=Object.create(ce.prototype);return zn(s,f,e,t,r,0,void 0),s}n(uo,\"CreateReadableByteStream\");function Ur(e){e._state=\"readable\",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}n(Ur,\"InitializeReadableStream\");function Te(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_readableStreamController\")?!1:e instanceof L}n(Te,\"IsReadableStream\");function Ce(e){return e._reader!==void 0}n(Ce,\"IsReadableStreamLocked\");function X(e,t){if(e._disturbed=!0,e._state===\"closed\")return T(void 0);if(e._state===\"errored\")return b(e._storedError);lt(e);const r=e._reader;if(r!==void 0&&We(r)){const f=r._readIntoRequests;r._readIntoRequests=new M,f.forEach(c=>{c._closeSteps(void 0)})}const s=e._readableStreamController[ar](t);return F(s,l)}n(X,\"ReadableStreamCancel\");function lt(e){e._state=\"closed\";const t=e._reader;if(t!==void 0&&(ln(t),ge(t))){const r=t._readRequests;t._readRequests=new M,r.forEach(s=>{s._closeSteps()})}}n(lt,\"ReadableStreamClose\");function lo(e,t){e._state=\"errored\",e._storedError=t;const r=e._reader;r!==void 0&&(cr(r,t),ge(r)?bn(r,t):Mn(r,t))}n(lo,\"ReadableStreamError\");function Ie(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}n(Ie,\"streamBrandCheckException$1\");function fo(e,t){ne(e,t);const r=e?.highWaterMark;return dr(r,\"highWaterMark\",\"QueuingStrategyInit\"),{highWaterMark:hr(r)}}n(fo,\"convertQueuingStrategyInit\");const co=n(e=>e.byteLength,\"byteLengthSizeFunction\");h(co,\"size\");class Dt{static{n(this,\"ByteLengthQueuingStrategy\")}constructor(t){le(t,1,\"ByteLengthQueuingStrategy\"),t=fo(t,\"First parameter\"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!mo(this))throw ho(\"highWaterMark\");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!mo(this))throw ho(\"size\");return co}}Object.defineProperties(Dt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(Dt.prototype,Symbol.toStringTag,{value:\"ByteLengthQueuingStrategy\",configurable:!0});function ho(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}n(ho,\"byteLengthBrandCheckException\");function mo(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_byteLengthQueuingStrategyHighWaterMark\")?!1:e instanceof Dt}n(mo,\"IsByteLengthQueuingStrategy\");const bo=n(()=>1,\"countSizeFunction\");h(bo,\"size\");class $t{static{n(this,\"CountQueuingStrategy\")}constructor(t){le(t,1,\"CountQueuingStrategy\"),t=fo(t,\"First parameter\"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!yo(this))throw po(\"highWaterMark\");return this._countQueuingStrategyHighWaterMark}get size(){if(!yo(this))throw po(\"size\");return bo}}Object.defineProperties($t.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty($t.prototype,Symbol.toStringTag,{value:\"CountQueuingStrategy\",configurable:!0});function po(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}n(po,\"countBrandCheckException\");function yo(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_countQueuingStrategyHighWaterMark\")?!1:e instanceof $t}n(yo,\"IsCountQueuingStrategy\");function ka(e,t){ne(e,t);const r=e?.cancel,s=e?.flush,f=e?.readableType,c=e?.start,d=e?.transform,p=e?.writableType;return{cancel:r===void 0?void 0:Fa(r,e,`${t} has member 'cancel' that`),flush:s===void 0?void 0:qa(s,e,`${t} has member 'flush' that`),readableType:f,start:c===void 0?void 0:Oa(c,e,`${t} has member 'start' that`),transform:d===void 0?void 0:Ia(d,e,`${t} has member 'transform' that`),writableType:p}}n(ka,\"convertTransformer\");function qa(e,t,r){return G(e,r),s=>z(e,t,[s])}n(qa,\"convertTransformerFlushCallback\");function Oa(e,t,r){return G(e,r),s=>O(e,t,[s])}n(Oa,\"convertTransformerStartCallback\");function Ia(e,t,r){return G(e,r),(s,f)=>z(e,t,[s,f])}n(Ia,\"convertTransformerTransformCallback\");function Fa(e,t,r){return G(e,r),s=>z(e,t,[s])}n(Fa,\"convertTransformerCancelCallback\");class Mt{static{n(this,\"TransformStream\")}constructor(t={},r={},s={}){t===void 0&&(t=null);const f=At(r,\"Second parameter\"),c=At(s,\"Third parameter\"),d=ka(t,\"First parameter\");if(d.readableType!==void 0)throw new RangeError(\"Invalid readableType specified\");if(d.writableType!==void 0)throw new RangeError(\"Invalid writableType specified\");const p=ot(c,0),R=vt(c),y=ot(f,1),C=vt(f);let P;const B=A(ee=>{P=ee});za(this,B,y,C,p,R),La(this,d),d.start!==void 0?P(d.start(this._transformStreamController)):P(void 0)}get readable(){if(!go(this))throw Ro(\"readable\");return this._readable}get writable(){if(!go(this))throw Ro(\"writable\");return this._writable}}Object.defineProperties(Mt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(Mt.prototype,Symbol.toStringTag,{value:\"TransformStream\",configurable:!0});function za(e,t,r,s,f,c){function d(){return t}n(d,\"startAlgorithm\");function p(B){return Ma(e,B)}n(p,\"writeAlgorithm\");function R(B){return Ua(e,B)}n(R,\"abortAlgorithm\");function y(){return xa(e)}n(y,\"closeAlgorithm\"),e._writable=$i(d,p,y,R,r,s);function C(){return Na(e)}n(C,\"pullAlgorithm\");function P(B){return Ha(e,B)}n(P,\"cancelAlgorithm\"),e._readable=ut(d,C,P,f,c),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Ut(e,!0),e._transformStreamController=void 0}n(za,\"InitializeTransformStream\");function go(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_transformStreamController\")?!1:e instanceof Mt}n(go,\"IsTransformStream\");function _o(e,t){J(e._readable._readableStreamController,t),xr(e,t)}n(_o,\"TransformStreamError\");function xr(e,t){Nt(e._transformStreamController),it(e._writable._writableStreamController,t),Nr(e)}n(xr,\"TransformStreamErrorWritableAndUnblockWrite\");function Nr(e){e._backpressure&&Ut(e,!1)}n(Nr,\"TransformStreamUnblockWrite\");function Ut(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=A(r=>{e._backpressureChangePromise_resolve=r}),e._backpressure=t}n(Ut,\"TransformStreamSetBackpressure\");class Pe{static{n(this,\"TransformStreamDefaultController\")}constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!xt(this))throw Ht(\"desiredSize\");const t=this._controlledTransformStream._readable._readableStreamController;return Mr(t)}enqueue(t=void 0){if(!xt(this))throw Ht(\"enqueue\");So(this,t)}error(t=void 0){if(!xt(this))throw Ht(\"error\");Da(this,t)}terminate(){if(!xt(this))throw Ht(\"terminate\");$a(this)}}Object.defineProperties(Pe.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),h(Pe.prototype.enqueue,\"enqueue\"),h(Pe.prototype.error,\"error\"),h(Pe.prototype.terminate,\"terminate\"),typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(Pe.prototype,Symbol.toStringTag,{value:\"TransformStreamDefaultController\",configurable:!0});function xt(e){return!u(e)||!Object.prototype.hasOwnProperty.call(e,\"_controlledTransformStream\")?!1:e instanceof Pe}n(xt,\"IsTransformStreamDefaultController\");function ja(e,t,r,s,f){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=s,t._cancelAlgorithm=f,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}n(ja,\"SetUpTransformStreamDefaultController\");function La(e,t){const r=Object.create(Pe.prototype);let s,f,c;t.transform!==void 0?s=n(d=>t.transform(d,r),\"transformAlgorithm\"):s=n(d=>{try{return So(r,d),T(void 0)}catch(p){return b(p)}},\"transformAlgorithm\"),t.flush!==void 0?f=n(()=>t.flush(r),\"flushAlgorithm\"):f=n(()=>T(void 0),\"flushAlgorithm\"),t.cancel!==void 0?c=n(d=>t.cancel(d),\"cancelAlgorithm\"):c=n(()=>T(void 0),\"cancelAlgorithm\"),ja(e,r,s,f,c)}n(La,\"SetUpTransformStreamDefaultControllerFromTransformer\");function Nt(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}n(Nt,\"TransformStreamDefaultControllerClearAlgorithms\");function So(e,t){const r=e._controlledTransformStream,s=r._readable._readableStreamController;if(!Ue(s))throw new TypeError(\"Readable side is not in a state that permits enqueue\");try{Me(s,t)}catch(c){throw xr(r,c),r._readable._storedError}ma(s)!==r._backpressure&&Ut(r,!0)}n(So,\"TransformStreamDefaultControllerEnqueue\");function Da(e,t){_o(e._controlledTransformStream,t)}n(Da,\"TransformStreamDefaultControllerError\");function wo(e,t){const r=e._transformAlgorithm(t);return F(r,void 0,s=>{throw _o(e._controlledTransformStream,s),s})}n(wo,\"TransformStreamDefaultControllerPerformTransform\");function $a(e){const t=e._controlledTransformStream,r=t._readable._readableStreamController;Oe(r);const s=new TypeError(\"TransformStream terminated\");xr(t,s)}n($a,\"TransformStreamDefaultControllerTerminate\");function Ma(e,t){const r=e._transformStreamController;if(e._backpressure){const s=e._backpressureChangePromise;return F(s,()=>{const f=e._writable;if(f._state===\"erroring\")throw f._storedError;return wo(r,t)})}return wo(r,t)}n(Ma,\"TransformStreamDefaultSinkWriteAlgorithm\");function Ua(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._readable;r._finishPromise=A((c,d)=>{r._finishPromise_resolve=c,r._finishPromise_reject=d});const f=r._cancelAlgorithm(t);return Nt(r),g(f,()=>(s._state===\"errored\"?xe(r,s._storedError):(J(s._readableStreamController,t),Hr(r)),null),c=>(J(s._readableStreamController,c),xe(r,c),null)),r._finishPromise}n(Ua,\"TransformStreamDefaultSinkAbortAlgorithm\");function xa(e){const t=e._transformStreamController;if(t._finishPromise!==void 0)return t._finishPromise;const r=e._readable;t._finishPromise=A((f,c)=>{t._finishPromise_resolve=f,t._finishPromise_reject=c});const s=t._flushAlgorithm();return Nt(t),g(s,()=>(r._state===\"errored\"?xe(t,r._storedError):(Oe(r._readableStreamController),Hr(t)),null),f=>(J(r._readableStreamController,f),xe(t,f),null)),t._finishPromise}n(xa,\"TransformStreamDefaultSinkCloseAlgorithm\");function Na(e){return Ut(e,!1),e._backpressureChangePromise}n(Na,\"TransformStreamDefaultSourcePullAlgorithm\");function Ha(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._writable;r._finishPromise=A((c,d)=>{r._finishPromise_resolve=c,r._finishPromise_reject=d});const f=r._cancelAlgorithm(t);return Nt(r),g(f,()=>(s._state===\"errored\"?xe(r,s._storedError):(it(s._writableStreamController,t),Nr(e),Hr(r)),null),c=>(it(s._writableStreamController,c),Nr(e),xe(r,c),null)),r._finishPromise}n(Ha,\"TransformStreamDefaultSourceCancelAlgorithm\");function Ht(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}n(Ht,\"defaultControllerBrandCheckException\");function Hr(e){e._finishPromise_resolve!==void 0&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(Hr,\"defaultControllerFinishPromiseResolve\");function xe(e,t){e._finishPromise_reject!==void 0&&(Q(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(xe,\"defaultControllerFinishPromiseReject\");function Ro(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}n(Ro,\"streamBrandCheckException\"),a.ByteLengthQueuingStrategy=Dt,a.CountQueuingStrategy=$t,a.ReadableByteStreamController=ce,a.ReadableStream=L,a.ReadableStreamBYOBReader=we,a.ReadableStreamBYOBRequest=ve,a.ReadableStreamDefaultController=he,a.ReadableStreamDefaultReader=ye,a.TransformStream=Mt,a.TransformStreamDefaultController=Pe,a.WritableStream=Re,a.WritableStreamDefaultController=$e,a.WritableStreamDefaultWriter=de})}(ct,ct.exports)),ct.exports}n(ns,\"requirePonyfill_es2018\");var Ao;function os(){if(Ao)return Eo;Ao=1;const i=65536;if(!globalThis.ReadableStream)try{const o=require(\"node:process\"),{emitWarning:a}=o;try{o.emitWarning=()=>{},Object.assign(globalThis,require(\"node:stream/web\")),o.emitWarning=a}catch(l){throw o.emitWarning=a,l}}catch{Object.assign(globalThis,ns())}try{const{Blob:o}=require(\"buffer\");o&&!o.prototype.stream&&(o.prototype.stream=n(function(l){let u=0;const m=this;return new ReadableStream({type:\"bytes\",async pull(h){const E=await m.slice(u,Math.min(m.size,u+i)).arrayBuffer();u+=E.byteLength,h.enqueue(new Uint8Array(E)),u===m.size&&h.close()}})},\"name\"))}catch{}return Eo}n(os,\"requireStreams\"),os();/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */const Bo=65536;async function*Qr(i,o=!0){for(const a of i)if(\"stream\"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let l=a.byteOffset;const u=a.byteOffset+a.byteLength;for(;l!==u;){const m=Math.min(u-l,Bo),h=a.buffer.slice(l,l+m);l+=h.byteLength,yield new Uint8Array(h)}}else yield a;else{let l=0,u=a;for(;l!==u.size;){const h=await u.slice(l,Math.min(u.size,l+Bo)).arrayBuffer();l+=h.byteLength,yield new Uint8Array(h)}}}n(Qr,\"toIterator\");const Wo=class on{static{n(this,\"Blob\")}#e=[];#t=\"\";#r=0;#n=\"transparent\";constructor(o=[],a={}){if(typeof o!=\"object\"||o===null)throw new TypeError(\"Failed to construct 'Blob': The provided value cannot be converted to a sequence.\");if(typeof o[Symbol.iterator]!=\"function\")throw new TypeError(\"Failed to construct 'Blob': The object must have a callable @@iterator property.\");if(typeof a!=\"object\"&&typeof a!=\"function\")throw new TypeError(\"Failed to construct 'Blob': parameter 2 cannot convert to dictionary.\");a===null&&(a={});const l=new TextEncoder;for(const m of o){let h;ArrayBuffer.isView(m)?h=new Uint8Array(m.buffer.slice(m.byteOffset,m.byteOffset+m.byteLength)):m instanceof ArrayBuffer?h=new Uint8Array(m.slice(0)):m instanceof on?h=m:h=l.encode(`${m}`),this.#r+=ArrayBuffer.isView(h)?h.byteLength:h.size,this.#e.push(h)}this.#n=`${a.endings===void 0?\"transparent\":a.endings}`;const u=a.type===void 0?\"\":String(a.type);this.#t=/^[\\x20-\\x7E]*$/.test(u)?u:\"\"}get size(){return this.#r}get type(){return this.#t}async text(){const o=new TextDecoder;let a=\"\";for await(const l of Qr(this.#e,!1))a+=o.decode(l,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){const o=new Uint8Array(this.size);let a=0;for await(const l of Qr(this.#e,!1))o.set(l,a),a+=l.length;return o.buffer}stream(){const o=Qr(this.#e,!0);return new globalThis.ReadableStream({type:\"bytes\",async pull(a){const l=await o.next();l.done?a.close():a.enqueue(l.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,l=\"\"){const{size:u}=this;let m=o<0?Math.max(u+o,0):Math.min(o,u),h=a<0?Math.max(u+a,0):Math.min(a,u);const S=Math.max(h-m,0),E=this.#e,w=[];let A=0;for(const b of E){if(A>=S)break;const q=ArrayBuffer.isView(b)?b.byteLength:b.size;if(m&&q<=m)m-=q,h-=q;else{let g;ArrayBuffer.isView(b)?(g=b.subarray(m,Math.min(q,h)),A+=g.byteLength):(g=b.slice(m,Math.min(q,h)),A+=g.size),h-=q,w.push(g),m=0}}const T=new on([],{type:String(l).toLowerCase()});return T.#r=S,T.#e=w,T}get[Symbol.toStringTag](){return\"Blob\"}static[Symbol.hasInstance](o){return o&&typeof o==\"object\"&&typeof o.constructor==\"function\"&&(typeof o.stream==\"function\"||typeof o.arrayBuffer==\"function\")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}};Object.defineProperties(Wo.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});const Ze=Wo,is=class extends Ze{static{n(this,\"File\")}#e=0;#t=\"\";constructor(o,a,l={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(o,l),l===null&&(l={});const u=l.lastModified===void 0?Date.now():Number(l.lastModified);Number.isNaN(u)||(this.#e=u),this.#t=String(a)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return\"File\"}static[Symbol.hasInstance](o){return!!o&&o instanceof Ze&&/^(File)$/.test(o[Symbol.toStringTag])}},Yr=is;/*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var{toStringTag:dt,iterator:as,hasInstance:ss}=Symbol,ko=Math.random,us=\"append,set,get,getAll,delete,keys,values,entries,forEach,constructor\".split(\",\"),qo=n((i,o,a)=>(i+=\"\",/^(Blob|File)$/.test(o&&o[dt])?[(a=a!==void 0?a+\"\":o[dt]==\"File\"?o.name:\"blob\",i),o.name!==a||o[dt]==\"blob\"?new Yr([o],a,o):o]:[i,o+\"\"]),\"f\"),Gr=n((i,o)=>(o?i:i.replace(/\\r?\\n|\\r/g,`\\r\n`)).replace(/\\n/g,\"%0A\").replace(/\\r/g,\"%0D\").replace(/\"/g,\"%22\"),\"e$1\"),Fe=n((i,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${i}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},\"x\");const Zt=class{static{n(this,\"FormData\")}#e=[];constructor(...o){if(o.length)throw new TypeError(\"Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.\")}get[dt](){return\"FormData\"}[as](){return this.entries()}static[ss](o){return o&&typeof o==\"object\"&&o[dt]===\"FormData\"&&!us.some(a=>typeof o[a]!=\"function\")}append(...o){Fe(\"append\",arguments,2),this.#e.push(qo(...o))}delete(o){Fe(\"delete\",arguments,1),o+=\"\",this.#e=this.#e.filter(([a])=>a!==o)}get(o){Fe(\"get\",arguments,1),o+=\"\";for(var a=this.#e,l=a.length,u=0;u<l;u++)if(a[u][0]===o)return a[u][1];return null}getAll(o,a){return Fe(\"getAll\",arguments,1),a=[],o+=\"\",this.#e.forEach(l=>l[0]===o&&a.push(l[1])),a}has(o){return Fe(\"has\",arguments,1),o+=\"\",this.#e.some(a=>a[0]===o)}forEach(o,a){Fe(\"forEach\",arguments,1);for(var[l,u]of this)o.call(a,u,l,this)}set(...o){Fe(\"set\",arguments,2);var a=[],l=!0;o=qo(...o),this.#e.forEach(u=>{u[0]===o[0]?l&&(l=!a.push(o)):a.push(u)}),l&&a.push(o),this.#e=a}*entries(){yield*this.#e}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}};function ls(i,o=Ze){var a=`${ko()}${ko()}`.replace(/\\./g,\"\").slice(-28).padStart(32,\"-\"),l=[],u=`--${a}\\r\nContent-Disposition: form-data; name=\"`;return i.forEach((m,h)=>typeof m==\"string\"?l.push(u+Gr(h)+`\"\\r\n\\r\n${m.replace(/\\r(?!\\n)|(?<!\\r)\\n/g,`\\r\n`)}\\r\n`):l.push(u+Gr(h)+`\"; filename=\"${Gr(m.name,1)}\"\\r\nContent-Type: ${m.type||\"application/octet-stream\"}\\r\n\\r\n`,m,`\\r\n`)),l.push(`--${a}--`),new o(l,{type:\"multipart/form-data; boundary=\"+a})}n(ls,\"formDataToBlob\");class Kt extends Error{static{n(this,\"FetchBaseError\")}constructor(o,a){super(o),Error.captureStackTrace(this,this.constructor),this.type=a}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class te extends Kt{static{n(this,\"FetchError\")}constructor(o,a,l){super(o,a),l&&(this.code=this.errno=l.code,this.erroredSysCall=l.syscall)}}const Jt=Symbol.toStringTag,Oo=n(i=>typeof i==\"object\"&&typeof i.append==\"function\"&&typeof i.delete==\"function\"&&typeof i.get==\"function\"&&typeof i.getAll==\"function\"&&typeof i.has==\"function\"&&typeof i.set==\"function\"&&typeof i.sort==\"function\"&&i[Jt]===\"URLSearchParams\",\"isURLSearchParameters\"),Xt=n(i=>i&&typeof i==\"object\"&&typeof i.arrayBuffer==\"function\"&&typeof i.type==\"string\"&&typeof i.stream==\"function\"&&typeof i.constructor==\"function\"&&/^(Blob|File)$/.test(i[Jt]),\"isBlob\"),fs=n(i=>typeof i==\"object\"&&(i[Jt]===\"AbortSignal\"||i[Jt]===\"EventTarget\"),\"isAbortSignal\"),cs=n((i,o)=>{const a=new URL(o).hostname,l=new URL(i).hostname;return a===l||a.endsWith(`.${l}`)},\"isDomainOrSubdomain\"),ds=n((i,o)=>{const a=new URL(o).protocol,l=new URL(i).protocol;return a===l},\"isSameProtocol\"),hs=(0,node_util__WEBPACK_IMPORTED_MODULE_5__.promisify)(node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline),N=Symbol(\"Body internals\");class ht{static{n(this,\"Body\")}constructor(o,{size:a=0}={}){let l=null;o===null?o=null:Oo(o)?o=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(o.toString()):Xt(o)||node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.isBuffer(o)||(node_util__WEBPACK_IMPORTED_MODULE_5__.types.isAnyArrayBuffer(o)?o=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(o):ArrayBuffer.isView(o)?o=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(o.buffer,o.byteOffset,o.byteLength):o instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__||(o instanceof Zt?(o=ls(o),l=o.type.split(\"=\")[1]):o=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(String(o))));let u=o;node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.isBuffer(o)?u=node_stream__WEBPACK_IMPORTED_MODULE_3__.Readable.from(o):Xt(o)&&(u=node_stream__WEBPACK_IMPORTED_MODULE_3__.Readable.from(o.stream())),this[N]={body:o,stream:u,boundary:l,disturbed:!1,error:null},this.size=a,o instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__&&o.on(\"error\",m=>{const h=m instanceof Kt?m:new te(`Invalid response body while trying to fetch ${this.url}: ${m.message}`,\"system\",m);this[N].error=h})}get body(){return this[N].stream}get bodyUsed(){return this[N].disturbed}async arrayBuffer(){const{buffer:o,byteOffset:a,byteLength:l}=await Zr(this);return o.slice(a,a+l)}async formData(){const o=this.headers.get(\"content-type\");if(o.startsWith(\"application/x-www-form-urlencoded\")){const l=new Zt,u=new URLSearchParams(await this.text());for(const[m,h]of u)l.append(m,h);return l}const{toFormData:a}=await __webpack_require__.e(/*! import() */ \"vendor-chunks/node-fetch-native\").then(__webpack_require__.bind(__webpack_require__, /*! ./chunks/multipart-parser.mjs */ \"(ssr)/./node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs\"));return a(this.body,o)}async blob(){const o=this.headers&&this.headers.get(\"content-type\")||this[N].body&&this[N].body.type||\"\",a=await this.arrayBuffer();return new Ze([a],{type:o})}async json(){const o=await this.text();return JSON.parse(o)}async text(){const o=await Zr(this);return new TextDecoder().decode(o)}buffer(){return Zr(this)}}ht.prototype.buffer=(0,node_util__WEBPACK_IMPORTED_MODULE_5__.deprecate)(ht.prototype.buffer,\"Please use 'response.arrayBuffer()' instead of 'response.buffer()'\",\"node-fetch#buffer\"),Object.defineProperties(ht.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,node_util__WEBPACK_IMPORTED_MODULE_5__.deprecate)(()=>{},\"data doesn't exist, use json(), text(), arrayBuffer(), or body instead\",\"https://github.com/node-fetch/node-fetch/issues/1000 (response)\")}});async function Zr(i){if(i[N].disturbed)throw new TypeError(`body used already for: ${i.url}`);if(i[N].disturbed=!0,i[N].error)throw i[N].error;const{body:o}=i;if(o===null)return node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.alloc(0);if(!(o instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__))return node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.alloc(0);const a=[];let l=0;try{for await(const u of o){if(i.size>0&&l+u.length>i.size){const m=new te(`content size at ${i.url} over limit: ${i.size}`,\"max-size\");throw o.destroy(m),m}l+=u.length,a.push(u)}}catch(u){throw u instanceof Kt?u:new te(`Invalid response body while trying to fetch ${i.url}: ${u.message}`,\"system\",u)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(u=>typeof u==\"string\")?node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(a.join(\"\")):node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.concat(a,l)}catch(u){throw new te(`Could not create Buffer from response body for ${i.url}: ${u.message}`,\"system\",u)}else throw new te(`Premature close of server response while trying to fetch ${i.url}`)}n(Zr,\"consumeBody\");const Kr=n((i,o)=>{let a,l,{body:u}=i[N];if(i.bodyUsed)throw new Error(\"cannot clone body after it is used\");return u instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__&&typeof u.getBoundary!=\"function\"&&(a=new node_stream__WEBPACK_IMPORTED_MODULE_3__.PassThrough({highWaterMark:o}),l=new node_stream__WEBPACK_IMPORTED_MODULE_3__.PassThrough({highWaterMark:o}),u.pipe(a),u.pipe(l),i[N].stream=a,u=l),u},\"clone\"),ms=(0,node_util__WEBPACK_IMPORTED_MODULE_5__.deprecate)(i=>i.getBoundary(),\"form-data doesn't follow the spec and requires special treatment. Use alternative package\",\"https://github.com/node-fetch/node-fetch/issues/1167\"),Io=n((i,o)=>i===null?null:typeof i==\"string\"?\"text/plain;charset=UTF-8\":Oo(i)?\"application/x-www-form-urlencoded;charset=UTF-8\":Xt(i)?i.type||null:node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.isBuffer(i)||node_util__WEBPACK_IMPORTED_MODULE_5__.types.isAnyArrayBuffer(i)||ArrayBuffer.isView(i)?null:i instanceof Zt?`multipart/form-data; boundary=${o[N].boundary}`:i&&typeof i.getBoundary==\"function\"?`multipart/form-data;boundary=${ms(i)}`:i instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__?null:\"text/plain;charset=UTF-8\",\"extractContentType\"),bs=n(i=>{const{body:o}=i[N];return o===null?0:Xt(o)?o.size:node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.isBuffer(o)?o.length:o&&typeof o.getLengthSync==\"function\"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},\"getTotalBytes\"),ps=n(async(i,{body:o})=>{o===null?i.end():await hs(o,i)},\"writeToStream\"),er=typeof node_http__WEBPACK_IMPORTED_MODULE_0__.validateHeaderName==\"function\"?node_http__WEBPACK_IMPORTED_MODULE_0__.validateHeaderName:i=>{if(!/^[\\^`\\-\\w!#$%&'*+.|~]+$/.test(i)){const o=new TypeError(`Header name must be a valid HTTP token [${i}]`);throw Object.defineProperty(o,\"code\",{value:\"ERR_INVALID_HTTP_TOKEN\"}),o}},Jr=typeof node_http__WEBPACK_IMPORTED_MODULE_0__.validateHeaderValue==\"function\"?node_http__WEBPACK_IMPORTED_MODULE_0__.validateHeaderValue:(i,o)=>{if(/[^\\t\\u0020-\\u007E\\u0080-\\u00FF]/.test(o)){const a=new TypeError(`Invalid character in header content [\"${i}\"]`);throw Object.defineProperty(a,\"code\",{value:\"ERR_INVALID_CHAR\"}),a}};class ae extends URLSearchParams{static{n(this,\"Headers\")}constructor(o){let a=[];if(o instanceof ae){const l=o.raw();for(const[u,m]of Object.entries(l))a.push(...m.map(h=>[u,h]))}else if(o!=null)if(typeof o==\"object\"&&!node_util__WEBPACK_IMPORTED_MODULE_5__.types.isBoxedPrimitive(o)){const l=o[Symbol.iterator];if(l==null)a.push(...Object.entries(o));else{if(typeof l!=\"function\")throw new TypeError(\"Header pairs must be iterable\");a=[...o].map(u=>{if(typeof u!=\"object\"||node_util__WEBPACK_IMPORTED_MODULE_5__.types.isBoxedPrimitive(u))throw new TypeError(\"Each header pair must be an iterable object\");return[...u]}).map(u=>{if(u.length!==2)throw new TypeError(\"Each header pair must be a name/value tuple\");return[...u]})}}else throw new TypeError(\"Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)\");return a=a.length>0?a.map(([l,u])=>(er(l),Jr(l,String(u)),[String(l).toLowerCase(),String(u)])):void 0,super(a),new Proxy(this,{get(l,u,m){switch(u){case\"append\":case\"set\":return(h,S)=>(er(h),Jr(h,String(S)),URLSearchParams.prototype[u].call(l,String(h).toLowerCase(),String(S)));case\"delete\":case\"has\":case\"getAll\":return h=>(er(h),URLSearchParams.prototype[u].call(l,String(h).toLowerCase()));case\"keys\":return()=>(l.sort(),new Set(URLSearchParams.prototype.keys.call(l)).keys());default:return Reflect.get(l,u,m)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(o){const a=this.getAll(o);if(a.length===0)return null;let l=a.join(\", \");return/^content-encoding$/i.test(o)&&(l=l.toLowerCase()),l}forEach(o,a=void 0){for(const l of this.keys())Reflect.apply(o,a,[this.get(l),l,this])}*values(){for(const o of this.keys())yield this.get(o)}*entries(){for(const o of this.keys())yield[o,this.get(o)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((o,a)=>(o[a]=this.getAll(a),o),{})}[Symbol.for(\"nodejs.util.inspect.custom\")](){return[...this.keys()].reduce((o,a)=>{const l=this.getAll(a);return a===\"host\"?o[a]=l[0]:o[a]=l.length>1?l:l[0],o},{})}}Object.defineProperties(ae.prototype,[\"get\",\"entries\",\"forEach\",\"values\"].reduce((i,o)=>(i[o]={enumerable:!0},i),{}));function ys(i=[]){return new ae(i.reduce((o,a,l,u)=>(l%2===0&&o.push(u.slice(l,l+2)),o),[]).filter(([o,a])=>{try{return er(o),Jr(o,String(a)),!0}catch{return!1}}))}n(ys,\"fromRawHeaders\");const gs=new Set([301,302,303,307,308]),Xr=n(i=>gs.has(i),\"isRedirect\"),re=Symbol(\"Response internals\");class H extends ht{static{n(this,\"Response\")}constructor(o=null,a={}){super(o,a);const l=a.status!=null?a.status:200,u=new ae(a.headers);if(o!==null&&!u.has(\"Content-Type\")){const m=Io(o,this);m&&u.append(\"Content-Type\",m)}this[re]={type:\"default\",url:a.url,status:l,statusText:a.statusText||\"\",headers:u,counter:a.counter,highWaterMark:a.highWaterMark}}get type(){return this[re].type}get url(){return this[re].url||\"\"}get status(){return this[re].status}get ok(){return this[re].status>=200&&this[re].status<300}get redirected(){return this[re].counter>0}get statusText(){return this[re].statusText}get headers(){return this[re].headers}get highWaterMark(){return this[re].highWaterMark}clone(){return new H(Kr(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(o,a=302){if(!Xr(a))throw new RangeError('Failed to execute \"redirect\" on \"response\": Invalid status code');return new H(null,{headers:{location:new URL(o).toString()},status:a})}static error(){const o=new H(null,{status:0,statusText:\"\"});return o[re].type=\"error\",o}static json(o=void 0,a={}){const l=JSON.stringify(o);if(l===void 0)throw new TypeError(\"data is not JSON serializable\");const u=new ae(a&&a.headers);return u.has(\"content-type\")||u.set(\"content-type\",\"application/json\"),new H(l,{...a,headers:u})}get[Symbol.toStringTag](){return\"Response\"}}Object.defineProperties(H.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});const _s=n(i=>{if(i.search)return i.search;const o=i.href.length-1,a=i.hash||(i.href[o]===\"#\"?\"#\":\"\");return i.href[o-a.length]===\"?\"?\"?\":\"\"},\"getSearch\");function Fo(i,o=!1){return i==null||(i=new URL(i),/^(about|blob|data):$/.test(i.protocol))?\"no-referrer\":(i.username=\"\",i.password=\"\",i.hash=\"\",o&&(i.pathname=\"\",i.search=\"\"),i)}n(Fo,\"stripURLForUseAsAReferrer\");const zo=new Set([\"\",\"no-referrer\",\"no-referrer-when-downgrade\",\"same-origin\",\"origin\",\"strict-origin\",\"origin-when-cross-origin\",\"strict-origin-when-cross-origin\",\"unsafe-url\"]),Ss=\"strict-origin-when-cross-origin\";function ws(i){if(!zo.has(i))throw new TypeError(`Invalid referrerPolicy: ${i}`);return i}n(ws,\"validateReferrerPolicy\");function Rs(i){if(/^(http|ws)s:$/.test(i.protocol))return!0;const o=i.host.replace(/(^\\[)|(]$)/g,\"\"),a=(0,node_net__WEBPACK_IMPORTED_MODULE_8__.isIP)(o);return a===4&&/^127\\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:i.host===\"localhost\"||i.host.endsWith(\".localhost\")?!1:i.protocol===\"file:\"}n(Rs,\"isOriginPotentiallyTrustworthy\");function Ke(i){return/^about:(blank|srcdoc)$/.test(i)||i.protocol===\"data:\"||/^(blob|filesystem):$/.test(i.protocol)?!0:Rs(i)}n(Ke,\"isUrlPotentiallyTrustworthy\");function Ts(i,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(i.referrer===\"no-referrer\"||i.referrerPolicy===\"\")return null;const l=i.referrerPolicy;if(i.referrer===\"about:client\")return\"no-referrer\";const u=i.referrer;let m=Fo(u),h=Fo(u,!0);m.toString().length>4096&&(m=h),o&&(m=o(m)),a&&(h=a(h));const S=new URL(i.url);switch(l){case\"no-referrer\":return\"no-referrer\";case\"origin\":return h;case\"unsafe-url\":return m;case\"strict-origin\":return Ke(m)&&!Ke(S)?\"no-referrer\":h.toString();case\"strict-origin-when-cross-origin\":return m.origin===S.origin?m:Ke(m)&&!Ke(S)?\"no-referrer\":h;case\"same-origin\":return m.origin===S.origin?m:\"no-referrer\";case\"origin-when-cross-origin\":return m.origin===S.origin?m:h;case\"no-referrer-when-downgrade\":return Ke(m)&&!Ke(S)?\"no-referrer\":m;default:throw new TypeError(`Invalid referrerPolicy: ${l}`)}}n(Ts,\"determineRequestsReferrer\");function Cs(i){const o=(i.get(\"referrer-policy\")||\"\").split(/[,\\s]+/);let a=\"\";for(const l of o)l&&zo.has(l)&&(a=l);return a}n(Cs,\"parseReferrerPolicyFromHeader\");const j=Symbol(\"Request internals\"),mt=n(i=>typeof i==\"object\"&&typeof i[j]==\"object\",\"isRequest\"),Ps=(0,node_util__WEBPACK_IMPORTED_MODULE_5__.deprecate)(()=>{},\".data is not a valid RequestInit property, use .body instead\",\"https://github.com/node-fetch/node-fetch/issues/1000 (request)\");class Xe extends ht{static{n(this,\"Request\")}constructor(o,a={}){let l;if(mt(o)?l=new URL(o.url):(l=new URL(o),o={}),l.username!==\"\"||l.password!==\"\")throw new TypeError(`${l} is an url with embedded credentials.`);let u=a.method||o.method||\"GET\";if(/^(delete|get|head|options|post|put)$/i.test(u)&&(u=u.toUpperCase()),!mt(a)&&\"data\"in a&&Ps(),(a.body!=null||mt(o)&&o.body!==null)&&(u===\"GET\"||u===\"HEAD\"))throw new TypeError(\"Request with GET/HEAD method cannot have body\");const m=a.body?a.body:mt(o)&&o.body!==null?Kr(o):null;super(m,{size:a.size||o.size||0});const h=new ae(a.headers||o.headers||{});if(m!==null&&!h.has(\"Content-Type\")){const w=Io(m,this);w&&h.set(\"Content-Type\",w)}let S=mt(o)?o.signal:null;if(\"signal\"in a&&(S=a.signal),S!=null&&!fs(S))throw new TypeError(\"Expected signal to be an instanceof AbortSignal or EventTarget\");let E=a.referrer==null?o.referrer:a.referrer;if(E===\"\")E=\"no-referrer\";else if(E){const w=new URL(E);E=/^about:(\\/\\/)?client$/.test(w)?\"client\":w}else E=void 0;this[j]={method:u,redirect:a.redirect||o.redirect||\"follow\",headers:h,parsedURL:l,signal:S,referrer:E},this.follow=a.follow===void 0?o.follow===void 0?20:o.follow:a.follow,this.compress=a.compress===void 0?o.compress===void 0?!0:o.compress:a.compress,this.counter=a.counter||o.counter||0,this.agent=a.agent||o.agent,this.highWaterMark=a.highWaterMark||o.highWaterMark||16384,this.insecureHTTPParser=a.insecureHTTPParser||o.insecureHTTPParser||!1,this.referrerPolicy=a.referrerPolicy||o.referrerPolicy||\"\"}get method(){return this[j].method}get url(){return (0,node_url__WEBPACK_IMPORTED_MODULE_7__.format)(this[j].parsedURL)}get headers(){return this[j].headers}get redirect(){return this[j].redirect}get signal(){return this[j].signal}get referrer(){if(this[j].referrer===\"no-referrer\")return\"\";if(this[j].referrer===\"client\")return\"about:client\";if(this[j].referrer)return this[j].referrer.toString()}get referrerPolicy(){return this[j].referrerPolicy}set referrerPolicy(o){this[j].referrerPolicy=ws(o)}clone(){return new Xe(this)}get[Symbol.toStringTag](){return\"Request\"}}Object.defineProperties(Xe.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});const Es=n(i=>{const{parsedURL:o}=i[j],a=new ae(i[j].headers);a.has(\"Accept\")||a.set(\"Accept\",\"*/*\");let l=null;if(i.body===null&&/^(post|put)$/i.test(i.method)&&(l=\"0\"),i.body!==null){const S=bs(i);typeof S==\"number\"&&!Number.isNaN(S)&&(l=String(S))}l&&a.set(\"Content-Length\",l),i.referrerPolicy===\"\"&&(i.referrerPolicy=Ss),i.referrer&&i.referrer!==\"no-referrer\"?i[j].referrer=Ts(i):i[j].referrer=\"no-referrer\",i[j].referrer instanceof URL&&a.set(\"Referer\",i.referrer),a.has(\"User-Agent\")||a.set(\"User-Agent\",\"node-fetch\"),i.compress&&!a.has(\"Accept-Encoding\")&&a.set(\"Accept-Encoding\",\"gzip, deflate, br\");let{agent:u}=i;typeof u==\"function\"&&(u=u(o));const m=_s(o),h={path:o.pathname+m,method:i.method,headers:a[Symbol.for(\"nodejs.util.inspect.custom\")](),insecureHTTPParser:i.insecureHTTPParser,agent:u};return{parsedURL:o,options:h}},\"getNodeRequestOptions\");class jo extends Kt{static{n(this,\"AbortError\")}constructor(o,a=\"aborted\"){super(o,a)}}/*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var en,Lo;function vs(){if(Lo)return en;if(Lo=1,!globalThis.DOMException)try{const{MessageChannel:i}=require(\"worker_threads\"),o=new i().port1,a=new ArrayBuffer;o.postMessage(a,[a,a])}catch(i){i.constructor.name===\"DOMException\"&&(globalThis.DOMException=i.constructor)}return en=globalThis.DOMException,en}n(vs,\"requireNodeDomexception\");var As=vs();const Bs=(0,_shared_node_fetch_native_DfbY2q_x_mjs__WEBPACK_IMPORTED_MODULE_6__.g)(As),{stat:tn}=node_fs__WEBPACK_IMPORTED_MODULE_9__.promises,Ws=n((i,o)=>Do((0,node_fs__WEBPACK_IMPORTED_MODULE_9__.statSync)(i),i,o),\"blobFromSync\"),ks=n((i,o)=>tn(i).then(a=>Do(a,i,o)),\"blobFrom\"),qs=n((i,o)=>tn(i).then(a=>$o(a,i,o)),\"fileFrom\"),Os=n((i,o)=>$o((0,node_fs__WEBPACK_IMPORTED_MODULE_9__.statSync)(i),i,o),\"fileFromSync\"),Do=n((i,o,a=\"\")=>new Ze([new ir({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],{type:a}),\"fromBlob\"),$o=n((i,o,a=\"\")=>new Yr([new ir({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],(0,node_path__WEBPACK_IMPORTED_MODULE_10__.basename)(o),{type:a,lastModified:i.mtimeMs}),\"fromFile\");class ir{static{n(this,\"BlobDataItem\")}#e;#t;constructor(o){this.#e=o.path,this.#t=o.start,this.size=o.size,this.lastModified=o.lastModified}slice(o,a){return new ir({path:this.#e,lastModified:this.lastModified,size:a-o,start:this.#t+o})}async*stream(){const{mtimeMs:o}=await tn(this.#e);if(o>this.lastModified)throw new Bs(\"The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.\",\"NotReadableError\");yield*(0,node_fs__WEBPACK_IMPORTED_MODULE_9__.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return\"Blob\"}}const Is=new Set([\"data:\",\"http:\",\"https:\"]);async function Mo(i,o){return new Promise((a,l)=>{const u=new Xe(i,o),{parsedURL:m,options:h}=Es(u);if(!Is.has(m.protocol))throw new TypeError(`node-fetch cannot load ${i}. URL scheme \"${m.protocol.replace(/:$/,\"\")}\" is not supported.`);if(m.protocol===\"data:\"){const g=ts(u.url),V=new H(g,{headers:{\"Content-Type\":g.typeFull}});a(V);return}const S=(m.protocol===\"https:\"?node_https__WEBPACK_IMPORTED_MODULE_1__:node_http__WEBPACK_IMPORTED_MODULE_0__).request,{signal:E}=u;let w=null;const A=n(()=>{const g=new jo(\"The operation was aborted.\");l(g),u.body&&u.body instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__.Readable&&u.body.destroy(g),!(!w||!w.body)&&w.body.emit(\"error\",g)},\"abort\");if(E&&E.aborted){A();return}const T=n(()=>{A(),q()},\"abortAndFinalize\"),b=S(m.toString(),h);E&&E.addEventListener(\"abort\",T);const q=n(()=>{b.abort(),E&&E.removeEventListener(\"abort\",T)},\"finalize\");b.on(\"error\",g=>{l(new te(`request to ${u.url} failed, reason: ${g.message}`,\"system\",g)),q()}),Fs(b,g=>{w&&w.body&&w.body.destroy(g)}),process.version<\"v14\"&&b.on(\"socket\",g=>{let V;g.prependListener(\"end\",()=>{V=g._eventsCount}),g.prependListener(\"close\",I=>{if(w&&V<g._eventsCount&&!I){const F=new Error(\"Premature close\");F.code=\"ERR_STREAM_PREMATURE_CLOSE\",w.body.emit(\"error\",F)}})}),b.on(\"response\",g=>{b.setTimeout(0);const V=ys(g.rawHeaders);if(Xr(g.statusCode)){const O=V.get(\"Location\");let z=null;try{z=O===null?null:new URL(O,u.url)}catch{if(u.redirect!==\"manual\"){l(new te(`uri requested responds with an invalid redirect URL: ${O}`,\"invalid-redirect\")),q();return}}switch(u.redirect){case\"error\":l(new te(`uri requested responds with a redirect, redirect mode is set to error: ${u.url}`,\"no-redirect\")),q();return;case\"manual\":break;case\"follow\":{if(z===null)break;if(u.counter>=u.follow){l(new te(`maximum redirect reached at: ${u.url}`,\"max-redirect\")),q();return}const $={headers:new ae(u.headers),follow:u.follow,counter:u.counter+1,agent:u.agent,compress:u.compress,method:u.method,body:Kr(u),signal:u.signal,size:u.size,referrer:u.referrer,referrerPolicy:u.referrerPolicy};if(!cs(u.url,z)||!ds(u.url,z))for(const pt of[\"authorization\",\"www-authenticate\",\"cookie\",\"cookie2\"])$.headers.delete(pt);if(g.statusCode!==303&&u.body&&o.body instanceof node_stream__WEBPACK_IMPORTED_MODULE_3__.Readable){l(new te(\"Cannot follow redirect with body being a readable stream\",\"unsupported-redirect\")),q();return}(g.statusCode===303||(g.statusCode===301||g.statusCode===302)&&u.method===\"POST\")&&($.method=\"GET\",$.body=void 0,$.headers.delete(\"content-length\"));const M=Cs(V);M&&($.referrerPolicy=M),a(Mo(new Xe(z,$))),q();return}default:return l(new TypeError(`Redirect option '${u.redirect}' is not a valid value of RequestRedirect`))}}E&&g.once(\"end\",()=>{E.removeEventListener(\"abort\",T)});let I=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(g,new node_stream__WEBPACK_IMPORTED_MODULE_3__.PassThrough,O=>{O&&l(O)});process.version<\"v12.10\"&&g.on(\"aborted\",T);const F={url:u.url,status:g.statusCode,statusText:g.statusMessage,headers:V,size:u.size,counter:u.counter,highWaterMark:u.highWaterMark},Q=V.get(\"Content-Encoding\");if(!u.compress||u.method===\"HEAD\"||Q===null||g.statusCode===204||g.statusCode===304){w=new H(I,F),a(w);return}const se={flush:node_zlib__WEBPACK_IMPORTED_MODULE_2__.Z_SYNC_FLUSH,finishFlush:node_zlib__WEBPACK_IMPORTED_MODULE_2__.Z_SYNC_FLUSH};if(Q===\"gzip\"||Q===\"x-gzip\"){I=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(I,node_zlib__WEBPACK_IMPORTED_MODULE_2__.createGunzip(se),O=>{O&&l(O)}),w=new H(I,F),a(w);return}if(Q===\"deflate\"||Q===\"x-deflate\"){const O=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(g,new node_stream__WEBPACK_IMPORTED_MODULE_3__.PassThrough,z=>{z&&l(z)});O.once(\"data\",z=>{(z[0]&15)===8?I=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(I,node_zlib__WEBPACK_IMPORTED_MODULE_2__.createInflate(),$=>{$&&l($)}):I=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(I,node_zlib__WEBPACK_IMPORTED_MODULE_2__.createInflateRaw(),$=>{$&&l($)}),w=new H(I,F),a(w)}),O.once(\"end\",()=>{w||(w=new H(I,F),a(w))});return}if(Q===\"br\"){I=(0,node_stream__WEBPACK_IMPORTED_MODULE_3__.pipeline)(I,node_zlib__WEBPACK_IMPORTED_MODULE_2__.createBrotliDecompress(),O=>{O&&l(O)}),w=new H(I,F),a(w);return}w=new H(I,F),a(w)}),ps(b,u).catch(l)})}n(Mo,\"fetch$1\");function Fs(i,o){const a=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.from(`0\\r\n\\r\n`);let l=!1,u=!1,m;i.on(\"response\",h=>{const{headers:S}=h;l=S[\"transfer-encoding\"]===\"chunked\"&&!S[\"content-length\"]}),i.on(\"socket\",h=>{const S=n(()=>{if(l&&!u){const w=new Error(\"Premature close\");w.code=\"ERR_STREAM_PREMATURE_CLOSE\",o(w)}},\"onSocketClose\"),E=n(w=>{u=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.compare(w.slice(-5),a)===0,!u&&m&&(u=node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.compare(m.slice(-3),a.slice(0,3))===0&&node_buffer__WEBPACK_IMPORTED_MODULE_4__.Buffer.compare(w.slice(-2),a.slice(3))===0),m=w},\"onData\");h.prependListener(\"close\",S),h.on(\"data\",E),i.on(\"close\",()=>{h.removeListener(\"close\",S),h.removeListener(\"data\",E)})})}n(Fs,\"fixResponseChunkedTransferBadEnding\");const Uo=new WeakMap,rn=new WeakMap;function k(i){const o=Uo.get(i);return console.assert(o!=null,\"'this' is expected an Event object, but got\",i),o}n(k,\"pd\");function xo(i){if(i.passiveListener!=null){typeof console<\"u\"&&typeof console.error==\"function\"&&console.error(\"Unable to preventDefault inside passive event listener invocation.\",i.passiveListener);return}i.event.cancelable&&(i.canceled=!0,typeof i.event.preventDefault==\"function\"&&i.event.preventDefault())}n(xo,\"setCancelFlag\");function Je(i,o){Uo.set(this,{eventTarget:i,event:o,eventPhase:2,currentTarget:i,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:o.timeStamp||Date.now()}),Object.defineProperty(this,\"isTrusted\",{value:!1,enumerable:!0});const a=Object.keys(o);for(let l=0;l<a.length;++l){const u=a[l];u in this||Object.defineProperty(this,u,No(u))}}n(Je,\"Event\"),Je.prototype={get type(){return k(this).event.type},get target(){return k(this).eventTarget},get currentTarget(){return k(this).currentTarget},composedPath(){const i=k(this).currentTarget;return i==null?[]:[i]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return k(this).eventPhase},stopPropagation(){const i=k(this);i.stopped=!0,typeof i.event.stopPropagation==\"function\"&&i.event.stopPropagation()},stopImmediatePropagation(){const i=k(this);i.stopped=!0,i.immediateStopped=!0,typeof i.event.stopImmediatePropagation==\"function\"&&i.event.stopImmediatePropagation()},get bubbles(){return!!k(this).event.bubbles},get cancelable(){return!!k(this).event.cancelable},preventDefault(){xo(k(this))},get defaultPrevented(){return k(this).canceled},get composed(){return!!k(this).event.composed},get timeStamp(){return k(this).timeStamp},get srcElement(){return k(this).eventTarget},get cancelBubble(){return k(this).stopped},set cancelBubble(i){if(!i)return;const o=k(this);o.stopped=!0,typeof o.event.cancelBubble==\"boolean\"&&(o.event.cancelBubble=!0)},get returnValue(){return!k(this).canceled},set returnValue(i){i||xo(k(this))},initEvent(){}},Object.defineProperty(Je.prototype,\"constructor\",{value:Je,configurable:!0,writable:!0}),typeof window<\"u\"&&typeof window.Event<\"u\"&&(Object.setPrototypeOf(Je.prototype,window.Event.prototype),rn.set(window.Event.prototype,Je));function No(i){return{get(){return k(this).event[i]},set(o){k(this).event[i]=o},configurable:!0,enumerable:!0}}n(No,\"defineRedirectDescriptor\");function zs(i){return{value(){const o=k(this).event;return o[i].apply(o,arguments)},configurable:!0,enumerable:!0}}n(zs,\"defineCallDescriptor\");function js(i,o){const a=Object.keys(o);if(a.length===0)return i;function l(u,m){i.call(this,u,m)}n(l,\"CustomEvent\"),l.prototype=Object.create(i.prototype,{constructor:{value:l,configurable:!0,writable:!0}});for(let u=0;u<a.length;++u){const m=a[u];if(!(m in i.prototype)){const S=typeof Object.getOwnPropertyDescriptor(o,m).value==\"function\";Object.defineProperty(l.prototype,m,S?zs(m):No(m))}}return l}n(js,\"defineWrapper\");function Ho(i){if(i==null||i===Object.prototype)return Je;let o=rn.get(i);return o==null&&(o=js(Ho(Object.getPrototypeOf(i)),i),rn.set(i,o)),o}n(Ho,\"getWrapper\");function Ls(i,o){const a=Ho(Object.getPrototypeOf(o));return new a(i,o)}n(Ls,\"wrapEvent\");function Ds(i){return k(i).immediateStopped}n(Ds,\"isStopped\");function $s(i,o){k(i).eventPhase=o}n($s,\"setEventPhase\");function Ms(i,o){k(i).currentTarget=o}n(Ms,\"setCurrentTarget\");function Vo(i,o){k(i).passiveListener=o}n(Vo,\"setPassiveListener\");const Qo=new WeakMap,Yo=1,Go=2,tr=3;function rr(i){return i!==null&&typeof i==\"object\"}n(rr,\"isObject\");function bt(i){const o=Qo.get(i);if(o==null)throw new TypeError(\"'this' is expected an EventTarget object, but got another value.\");return o}n(bt,\"getListeners\");function Us(i){return{get(){let a=bt(this).get(i);for(;a!=null;){if(a.listenerType===tr)return a.listener;a=a.next}return null},set(o){typeof o!=\"function\"&&!rr(o)&&(o=null);const a=bt(this);let l=null,u=a.get(i);for(;u!=null;)u.listenerType===tr?l!==null?l.next=u.next:u.next!==null?a.set(i,u.next):a.delete(i):l=u,u=u.next;if(o!==null){const m={listener:o,listenerType:tr,passive:!1,once:!1,next:null};l===null?a.set(i,m):l.next=m}},configurable:!0,enumerable:!0}}n(Us,\"defineEventAttributeDescriptor\");function Zo(i,o){Object.defineProperty(i,`on${o}`,Us(o))}n(Zo,\"defineEventAttribute\");function Ko(i){function o(){pe.call(this)}n(o,\"CustomEventTarget\"),o.prototype=Object.create(pe.prototype,{constructor:{value:o,configurable:!0,writable:!0}});for(let a=0;a<i.length;++a)Zo(o.prototype,i[a]);return o}n(Ko,\"defineCustomEventTarget\");function pe(){if(this instanceof pe){Qo.set(this,new Map);return}if(arguments.length===1&&Array.isArray(arguments[0]))return Ko(arguments[0]);if(arguments.length>0){const i=new Array(arguments.length);for(let o=0;o<arguments.length;++o)i[o]=arguments[o];return Ko(i)}throw new TypeError(\"Cannot call a class as a function\")}n(pe,\"EventTarget\"),pe.prototype={addEventListener(i,o,a){if(o==null)return;if(typeof o!=\"function\"&&!rr(o))throw new TypeError(\"'listener' should be a function or an object.\");const l=bt(this),u=rr(a),h=(u?!!a.capture:!!a)?Yo:Go,S={listener:o,listenerType:h,passive:u&&!!a.passive,once:u&&!!a.once,next:null};let E=l.get(i);if(E===void 0){l.set(i,S);return}let w=null;for(;E!=null;){if(E.listener===o&&E.listenerType===h)return;w=E,E=E.next}w.next=S},removeEventListener(i,o,a){if(o==null)return;const l=bt(this),m=(rr(a)?!!a.capture:!!a)?Yo:Go;let h=null,S=l.get(i);for(;S!=null;){if(S.listener===o&&S.listenerType===m){h!==null?h.next=S.next:S.next!==null?l.set(i,S.next):l.delete(i);return}h=S,S=S.next}},dispatchEvent(i){if(i==null||typeof i.type!=\"string\")throw new TypeError('\"event.type\" should be a string.');const o=bt(this),a=i.type;let l=o.get(a);if(l==null)return!0;const u=Ls(this,i);let m=null;for(;l!=null;){if(l.once?m!==null?m.next=l.next:l.next!==null?o.set(a,l.next):o.delete(a):m=l,Vo(u,l.passive?l.listener:null),typeof l.listener==\"function\")try{l.listener.call(this,u)}catch(h){typeof console<\"u\"&&typeof console.error==\"function\"&&console.error(h)}else l.listenerType!==tr&&typeof l.listener.handleEvent==\"function\"&&l.listener.handleEvent(u);if(Ds(u))break;l=l.next}return Vo(u,null),$s(u,0),Ms(u,null),!u.defaultPrevented}},Object.defineProperty(pe.prototype,\"constructor\",{value:pe,configurable:!0,writable:!0}),typeof window<\"u\"&&typeof window.EventTarget<\"u\"&&Object.setPrototypeOf(pe.prototype,window.EventTarget.prototype);class nr extends pe{static{n(this,\"AbortSignal\")}constructor(){throw super(),new TypeError(\"AbortSignal cannot be constructed directly\")}get aborted(){const o=or.get(this);if(typeof o!=\"boolean\")throw new TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?\"null\":typeof this}`);return o}}Zo(nr.prototype,\"abort\");function xs(){const i=Object.create(nr.prototype);return pe.call(i),or.set(i,!1),i}n(xs,\"createAbortSignal\");function Ns(i){or.get(i)===!1&&(or.set(i,!0),i.dispatchEvent({type:\"abort\"}))}n(Ns,\"abortSignal\");const or=new WeakMap;Object.defineProperties(nr.prototype,{aborted:{enumerable:!0}}),typeof Symbol==\"function\"&&typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(nr.prototype,Symbol.toStringTag,{configurable:!0,value:\"AbortSignal\"});let nn=class{static{n(this,\"AbortController\")}constructor(){Jo.set(this,xs())}get signal(){return Xo(this)}abort(){Ns(Xo(this))}};const Jo=new WeakMap;function Xo(i){const o=Jo.get(i);if(o==null)throw new TypeError(`Expected 'this' to be an 'AbortController' object, but got ${i===null?\"null\":typeof i}`);return o}n(Xo,\"getSignal\"),Object.defineProperties(nn.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),typeof Symbol==\"function\"&&typeof Symbol.toStringTag==\"symbol\"&&Object.defineProperty(nn.prototype,Symbol.toStringTag,{configurable:!0,value:\"AbortController\"});var Hs=Object.defineProperty,Vs=n((i,o)=>Hs(i,\"name\",{value:o,configurable:!0}),\"e\");const ei=Mo;ti();function ti(){!globalThis.process?.versions?.node&&!globalThis.process?.env?.DISABLE_NODE_FETCH_NATIVE_WARN&&console.warn(\"[node-fetch-native] Node.js compatible build of `node-fetch-native` is being used in a non-Node.js environment. Please make sure you are using proper export conditions or report this issue to https://github.com/unjs/node-fetch-native. You can set `process.env.DISABLE_NODE_FETCH_NATIVE_WARN` to disable this warning.\")}n(ti,\"s\"),Vs(ti,\"checkNodeEnvironment\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/node-fetch-native/dist/node.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   c: () => (/* binding */ n),\n/* harmony export */   g: () => (/* binding */ f)\n/* harmony export */ });\nvar t=Object.defineProperty;var o=(e,l)=>t(e,\"name\",{value:l,configurable:!0});var n=typeof globalThis<\"u\"?globalThis:typeof window<\"u\"?window:typeof global<\"u\"?global:typeof self<\"u\"?self:{};function f(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}o(f,\"getDefaultExportFromCjs\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbm9kZS1mZXRjaC1uYXRpdmUvZGlzdC9zaGFyZWQvbm9kZS1mZXRjaC1uYXRpdmUuRGZiWTJxLXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNEJBQTRCLHlCQUF5Qix3QkFBd0IsRUFBRSxpSEFBaUgsY0FBYyxzRkFBc0YsK0JBQXFEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcbm9kZV9tb2R1bGVzXFxub2RlLWZldGNoLW5hdGl2ZVxcZGlzdFxcc2hhcmVkXFxub2RlLWZldGNoLW5hdGl2ZS5EZmJZMnEteC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHQ9T2JqZWN0LmRlZmluZVByb3BlcnR5O3ZhciBvPShlLGwpPT50KGUsXCJuYW1lXCIse3ZhbHVlOmwsY29uZmlndXJhYmxlOiEwfSk7dmFyIG49dHlwZW9mIGdsb2JhbFRoaXM8XCJ1XCI/Z2xvYmFsVGhpczp0eXBlb2Ygd2luZG93PFwidVwiP3dpbmRvdzp0eXBlb2YgZ2xvYmFsPFwidVwiP2dsb2JhbDp0eXBlb2Ygc2VsZjxcInVcIj9zZWxmOnt9O2Z1bmN0aW9uIGYoZSl7cmV0dXJuIGUmJmUuX19lc01vZHVsZSYmT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGUsXCJkZWZhdWx0XCIpP2UuZGVmYXVsdDplfW8oZixcImdldERlZmF1bHRFeHBvcnRGcm9tQ2pzXCIpO2V4cG9ydHtuIGFzIGMsZiBhcyBnfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/node-fetch-native/dist/shared/node-fetch-native.DfbY2q-x.mjs\n");

/***/ })

};
;