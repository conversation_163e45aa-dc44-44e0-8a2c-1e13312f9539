require("ts-node/register");
const {
  PublicKey,
  Connection,
  clusterApiUrl,
  Transaction,
  SystemProgram,
  LAMPORTS_PER_SOL,
  Keypair,
  sendAndConfirmTransaction,
} = require("@solana/web3.js");
const anchor = require("@coral-xyz/anchor");

const { getBondingCurvePda, global } = require("./funhi-program/pdas");
const { program } = require("./funhi-program/setup");
const { withdraw } = require("./funhi-program/withdraw.ts");
const { raydiumCreateCpmm } = require("./raydium/raydium_create_cpmm.ts");
const { raydiumLockLiquidity } = require("./raydium/raydium_lock_lp.ts");
const config = require("../config/security");
const fs = require("fs");

const connection = new Connection(clusterApiUrl("devnet"));
const colors = require("colors");
const bs58 = require("bs58");
const {
  getAssociatedTokenAddress,
  createTransferInstruction,
  getOrCreateAssociatedTokenAccount,
  createAssociatedTokenAccountInstruction,
  TOKEN_PROGRAM_ID,
} = require("@solana/spl-token");

const {
  getPriceInUsd,
  upsertTokenByMint,
  getUngraduatedTokens,
  markTokenAsGraduated,
} = require("../db/token");

async function isValidConnection(connection) {
  try {
    const blockHeight = await connection.getBlockHeight();
    if (blockHeight === 0) return false;
    return true;
  } catch {
    return false;
  }
}


const { create } = require("./funhi-program/create.ts");

// Helper: Validate Solana public key (string) format and curve check
const isValidPublicKey = (key) => {
  try {
    const pubkey = new PublicKey(key);
    return PublicKey.isOnCurve(pubkey);
  } catch {
    return false;
  }
};

// Helper: Validate positive number (including integer or float)
const isPositiveNumber = (value) => typeof value === "number" && value > 0;

// Helper: Validate non-empty string
const isNonEmptyString = (str) => typeof str === "string" && str.trim() !== "";

// used to create token on SOL contract
const createSOLToken = async (name, ticker, image, userWallet) => {
  if (!isNonEmptyString(name)) {
    throw new Error("Token name is required and must be a non-empty string");
  }
  if (!isNonEmptyString(ticker)) {
    throw new Error("Token ticker is required and must be a non-empty string");
  }
  if (!isValidPublicKey(userWallet)) {
    throw new Error("Invalid user wallet address");
  }

  const connection = new Connection(config.CONNECTION_URL, "confirmed");
  const validConnection = await isValidConnection(connection);
  if (!validConnection) {
    throw new Error("Invalid connection");
  }
  const adminKeypair = Keypair.fromSecretKey(
    bs58.default.decode(config.ADMIN_PRIVATE_KEY)
  );
  const userWalletPK = new PublicKey(userWallet);
  const mintKeypair = Keypair.generate();
  const feePayerKeypair = adminKeypair;

  const instruction = await create(
    name,
    ticker,
    image,
    feePayerKeypair, // signer
    userWalletPK,    // creator
    mintKeypair.publicKey // mint
  );

  const tx = new Transaction().add(instruction);
  tx.feePayer = feePayerKeypair.publicKey;
  tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

  // Sign transaction for simulation
  tx.partialSign(feePayerKeypair, mintKeypair);

  // Simulate transaction
  const simulationResult = await connection.simulateTransaction(tx);

  if (simulationResult.value.err) {
    console.error("Simulation failed:", simulationResult.value.logs);
    throw new Error("Transaction simulation failed");
  }

  console.log("Simulation successful");

  // Send and confirm transaction
  const isConfirm = await sendAndConfirmTransaction(connection, tx, [
    feePayerKeypair,
    mintKeypair,
  ]);

  const isBondingCurvePda = getBondingCurvePda(mintKeypair.publicKey);
  const bondingCurvePda = isBondingCurvePda.toBase58();

  return {
    mintAddress: mintKeypair.publicKey.toString(),
    isConfirm,
    creatorWallet: userWallet,
    bondingCurvePda,
  };
};

const transferSplTokenFromAirdrop = async (
  recipientWalletAddress,
  mintAddress,
  amount,
  decimals
) => {
  // Input validation
  if (!isValidPublicKey(recipientWalletAddress)) {
    throw new Error("Invalid recipient wallet address");
  }
  if (!isValidPublicKey(mintAddress)) {
    throw new Error("Invalid mint address");
  }
  if (!isPositiveNumber(amount)) {
    throw new Error("Invalid transfer amount, must be a positive number");
  }
  if (typeof decimals !== "number" || decimals < 0 || decimals > 18) {
    throw new Error("Invalid decimals value");
  }

  const connection = new Connection(
    config.CONNECTION_URL || clusterApiUrl("devnet"),
    "confirmed"
  );

  const validConnection = await isValidConnection(connection);
  if (!validConnection) {
    throw new Error("Invalid connection");
  }

  const adminKeypair = Keypair.fromSecretKey(
    bs58.default.decode(config.AIRDROP_WALLET_PRIVATE_KEY)
  );
  const recipient = new PublicKey(recipientWalletAddress);
  const mint = new PublicKey(mintAddress);
  const sender = adminKeypair.publicKey;

  const senderATA = await getOrCreateAssociatedTokenAccount(
    connection,
    adminKeypair,
    mint,
    sender
  );
  const recipientATA = await getOrCreateAssociatedTokenAccount(
    connection,
    adminKeypair,
    mint,
    recipient
  );

  const amountInSmallestUnit = BigInt(Math.floor(amount * 10 ** decimals));

  const tx = new Transaction().add(
    createTransferInstruction(
      senderATA.address,
      recipientATA.address,
      sender,
      amountInSmallestUnit
    )
  );

  tx.feePayer = sender;
  tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

  // Partial sign before simulation
  tx.partialSign(adminKeypair);

  // Simulate the transaction
  const simulationResult = await connection.simulateTransaction(tx);

  if (simulationResult.value.err) {
    console.error("Transaction simulation failed:", simulationResult.value.logs);
    throw new Error("Transaction simulation failed");
  }

  console.log("Transaction simulation successful");

  // Send and confirm the transaction
  const signature = await sendAndConfirmTransaction(connection, tx, [
    adminKeypair,
  ]);

  return signature;
};



// Sync BondingCurves address in DB
const syncBondingCurves = async () => {
  const allBondingCurves = await program.account.bondingCurve.all();

  for (const curve of allBondingCurves) {
    const tokenMint = curve.account.mint.toBase58();
    const bondingCurvePda = curve.publicKey.toBase58();
    await upsertTokenByMint(tokenMint, bondingCurvePda);
  }
};

// Check Graduation status and handle logic
const checkGraduation = async () => {
  try {
    const connection = new Connection(config.CONNECTION_URL, "confirmed");
    const validConnection = await isValidConnection(connection);
    if (!validConnection) {
      throw new Error("Invalid connection");
    }

    const ungraduated = await getUngraduatedTokens();

    for (const token of ungraduated) {
      try {
        if (
          !isValidPublicKey(token.bondingCurvePda) ||
          !isValidPublicKey(token.tokenAddress) ||
          !isValidPublicKey(token.creatorWallet)
        ) {
          console.log(
            "Invalid address found in token data, skipping token:",
            token.tokenAddress
          );
          continue;
        }

        const bondingCurvePda = new PublicKey(token.bondingCurvePda);
        const bondingCurve = await program.account.bondingCurve.fetch(bondingCurvePda);
        if (!bondingCurve) {
          throw new Error("Bonding curve account not found");
        }

        const globalData = await program.account.global.fetch(global);
        if (!globalData) {
          throw new Error("Global account not found");
        }

        if (bondingCurve.complete) {
          console.log("Completed");

          const mint = new PublicKey(token.tokenAddress);
          const authority = globalData.authority;

          const withdrawIx = await withdraw(mint, authority);

          const SOL_MINT = new PublicKey(
            "So11111111111111111111111111111111111111112"
          );

          const mintA = bondingCurve.mint;
          const mintB = SOL_MINT;
          const mintAAmount = bondingCurve.realTokenReserves;

          const lamportsPerSolBN = new anchor.BN(LAMPORTS_PER_SOL.toString());
          const oneAndHalfLAMPORTS = lamportsPerSolBN
            .mul(new anchor.BN(3))
            .div(new anchor.BN(2));
          const mintBAmount = bondingCurve.realSolReserves.sub(oneAndHalfLAMPORTS);

          const startTime = new anchor.BN(Math.floor(Date.now() / 1000) + 300);
          const owner = globalData.authority;
          const network = config.ISLIVE === true ? "mainnet" : "devnet";

          const adminKeypair = Keypair.fromSecretKey(
          bs58.decode(config.ADMIN_PRIVATE_KEY)
          );

          const creatorRewardIx = SystemProgram.transfer({
            fromPubkey: adminKeypair.publicKey,
            toPubkey: new PublicKey(token.creatorWallet),
            lamports: 0.5 * LAMPORTS_PER_SOL,
          });

          const platformFeeIx = SystemProgram.transfer({
            fromPubkey: adminKeypair.publicKey,
            toPubkey: adminKeypair.publicKey, // can change to globalData.feeRecipient
            lamports: LAMPORTS_PER_SOL,
          });

          const authorityAta = await getAssociatedTokenAddress(
            bondingCurve.mint,
            adminKeypair.publicKey
          );

          const authorityAtaAccount = await connection.getAccountInfo(authorityAta);
          if (!authorityAtaAccount) {
            const createAtaIx = createAssociatedTokenAccountInstruction(
              adminKeypair.publicKey,
              authorityAta,
              adminKeypair.publicKey,
              bondingCurve.mint
            );
            const tx = new Transaction().add(createAtaIx);
            await sendAndConfirmTransaction(connection, tx, [adminKeypair]);
          }

          const { instructions: createCpmmIx, poolAddress } =
            await raydiumCreateCpmm(
              mintA,
              mintB,
              mintAAmount,
              mintBAmount,
              startTime,
              owner,
              connection,
              network
            );

          const tx = new Transaction()
            .add(withdrawIx)
            .add(creatorRewardIx)
            .add(platformFeeIx)
            .add(...createCpmmIx);

          // SIMULATION
          const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
          tx.recentBlockhash = blockhash;
          tx.feePayer = adminKeypair.publicKey;
          tx.partialSign(adminKeypair);

          const simulationResult = await connection.simulateTransaction(tx);

          if (simulationResult.value.err) {
            console.error("Transaction simulation failed:", simulationResult.value.logs);
            throw new Error("Graduation transaction simulation failed");
          }

          console.log("Transaction simulation successful");

          // BROADCAST
          const poolsignature = await sendAndConfirmTransaction(
            connection,
            tx,
            [adminKeypair]
          );

          // LOCK LP
          const { transaction: lockLpTx, nftMint } = await raydiumLockLiquidity(
            adminKeypair,
            connection,
            network,
            poolAddress.toBase58()
          );

          const { lastValidBlockHeight: lockBlockHeight, blockhash: lockBlockhash } =
            await connection.getLatestBlockhash();
          lockLpTx.recentBlockhash = lockBlockhash;
          lockLpTx.lastValidBlockHeight = lockBlockHeight;
          lockLpTx.sign([adminKeypair]);

          const lockLpSig = await connection.sendTransaction(lockLpTx);

          await markTokenAsGraduated(
            token.tokenAddress,
            poolAddress.toBase58(),
            poolsignature,
            nftMint.toBase58(),
            lockLpSig
          );
        } else {
          console.log("Not Completed");
        }
      } catch (error) {
        console.log(error);
      }
    }
  } catch (error) {
    console.log(error);
  }
};


const getTokenPubkey = async (address, tokenAddress) => {
  if (!isValidPublicKey(address) || !isValidPublicKey(tokenAddress)) {
    throw new Error("Invalid address or token address");
  }
  const pubkey = new PublicKey(address);
  const accounts = await connection.getParsedTokenAccountsByOwner(pubkey, {
    programId: TOKEN_PROGRAM_ID,
  });
  const keys = accounts.value
    .filter((el) => el.account.data.parsed.info.mint === tokenAddress)
    .map((el) => el.pubkey);
  if (keys.length === 0) {
    return null;
  } else {
    return keys[0];
  }
};

const getTokenAccountAddress = async (pubkey, tokenAddress) => {
  if (!isValidPublicKey(pubkey) || !isValidPublicKey(tokenAddress)) {
    throw new Error("Invalid public key or token address");
  }
  const accounts = await connection.getTokenAccountsByOwner(
    new PublicKey(pubkey),
    {
      mint: new PublicKey(tokenAddress),
    }
  );
  if (accounts.value.length === 0) {
    return null;
  } else {
    return accounts.value[0].pubkey.toString();
  }
};

function verifySolanaAddress(address) {
  if (
    typeof address !== "string" ||
    address.length < 32 ||
    address.length > 44
  ) {
    return false;
  }
  try {
    const publicKey = new PublicKey(address);
    return PublicKey.isOnCurve(publicKey);
  } catch {
    return false;
  }
}

async function getLatestSlot() {
  const slot = await connection.getSlot();
  console.log(`Latest slot: ${slot}`);
  return slot;
}

async function getLatestSignatureInSlot(slot) {
  const block = await connection.getBlock(slot, {
    maxSupportedTransactionVersion: 0,
  });

  if (!block || !block.transactions || block.transactions.length === 0) {
    console.log(`No transactions found in slot ${slot}.`);
    return null;
  }

  const latestTransaction = block.transactions[block.transactions.length - 1];
  const latestSignature = latestTransaction.transaction.signatures[0];
  return latestSignature;
}

async function getBonus({ address, bonusMultipliers }) {
  // TO-DO:
}

async function verifyTransaction(txId) {
  try {
    const transactionDetails = await connection.getTransaction(txId, {
      commitment: "confirmed",
    });

    if (transactionDetails) {
      console.log("Transaction found:", transactionDetails);
      return { status: "success", transactionDetails };
    } else {
      console.error("Transaction not found or not confirmed yet.");
      return {
        status: "error",
        message: "Transaction not found or not confirmed yet",
      };
    }
  } catch (error) {
    console.error("Error fetching transaction:", error);
    return { status: "error", message: "Error verifying transaction" };
  }
}


module.exports = {
  transferSplTokenFromAirdrop,
  createSOLToken,
  syncBondingCurves,
  checkGraduation,
  verifySolanaAddress,
  getLatestSlot,
  getLatestSignatureInSlot,
  getTokenPubkey,
  verifyTransaction,
};
