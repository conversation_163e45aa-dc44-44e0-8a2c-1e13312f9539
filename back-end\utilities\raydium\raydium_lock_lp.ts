import {
  ApiV3PoolInfoStandardItemCpmm,
  DEV_LOCK_CPMM_PROGRAM,
  DEV_LOCK_CPMM_AUTH,
  CpmmKeys,
  Raydium,
  TxVersion,
  LOCK_CPMM_PROGRAM,
  LOCK_CPMM_AUTH,
} from "@raydium-io/raydium-sdk-v2";
import { isValidCpmm } from "./utils";
import {
  Connection,
  Keypair,
  PublicKey,
  VersionedTransaction,
} from "@solana/web3.js";
import { isValidConnection } from "../isValidConnection";

export const raydiumLockLiquidity = async (
  signer: PublicKey | Keypair,
  connection: Connection,
  network: "devnet" | "mainnet",
  poolId: string,
): Promise<{ transaction: VersionedTransaction; nftMint: PublicKey }> => {
  const validConnection = isValidConnection(connection);
  if (!validConnection) {
    throw new Error("Invalid connection");
  }
  const raydium = await Raydium.load({
    owner: signer,
    connection,
    cluster: network,
  });

  let poolInfo: ApiV3PoolInfoStandardItemCpmm;
  let poolKeys: CpmmKeys | undefined;
  if (raydium.cluster === "mainnet") {
    // note: api doesn't support get devnet pool info, so in devnet else we go rpc method
    // if you wish to get pool info from rpc, also can modify logic to go rpc method directly
    const data = await raydium.api.fetchPoolById({ ids: poolId });
    if (!data) {
      throw new Error(`Pool with id ${poolId} not found`);
    }
    poolInfo = data[0] as ApiV3PoolInfoStandardItemCpmm;
    if (!isValidCpmm(poolInfo.programId))
      throw new Error("target pool is not CPMM pool");
  } else {
    const data = await raydium.cpmm.getPoolInfoFromRpc(poolId);
    if (!data) {
      throw new Error(`Pool with id ${poolId} not found`);
    }
    poolInfo = data.poolInfo;
    poolKeys = data.poolKeys;
  }

  await raydium.account.fetchWalletTokenAccounts();
  const lpBalance = raydium.account.tokenAccounts.find(
    (a) => a.mint.toBase58() === poolInfo.lpMint.address,
  );
  if (!lpBalance) throw new Error(`you do not have balance in pool: ${poolId}`);

  const { builder, extInfo } = await raydium.cpmm.lockLp({
    programId: network == "mainnet" ? LOCK_CPMM_PROGRAM : DEV_LOCK_CPMM_PROGRAM,
    authProgram: network == "mainnet" ? LOCK_CPMM_AUTH : DEV_LOCK_CPMM_AUTH,
    poolKeys,
    poolInfo,
    lpAmount: lpBalance.amount,
    withMetadata: true,
    txVersion: TxVersion.V0,
  });
  const txBuild = await builder.versionBuild({ txVersion: TxVersion.V0 });
  return {
    transaction: txBuild.transaction as VersionedTransaction,
    nftMint: extInfo.nftMint,
  };
};