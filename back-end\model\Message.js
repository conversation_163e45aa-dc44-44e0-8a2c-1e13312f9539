const { Model, DataTypes } = require("sequelize");

class Message extends Model {
    static initModel(sequelize) {
        return Message.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                tradeId: {
                    type: DataTypes.BIGINT,
                    allowNull: true, // Allow null for unified chat
                    references: {
                        model: 'token_purchased', // Table name in DB
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                senderId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                receiverId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                message: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                type: {
                    type: DataTypes.STRING(20),
                    allowNull: false,
                    defaultValue: 'user', // 'user' or 'system'
                },
                chatRoomId: {
                    type: DataTypes.STRING(100),
                    allowNull: true, // For legacy messages, but should be set for new ones
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'messages',
                timestamps: true,
            }
        );
    }

    static associate(models) {
        Message.belongsTo(models.TokenPurchased, {
            foreignKey: 'tradeId',
            targetKey: 'id',
            as: 'trade',
        });
        Message.belongsTo(models.User, {
            foreignKey: 'senderId',
            targetKey: 'id',
            as: 'sender',
        });
        Message.belongsTo(models.User, {
            foreignKey: 'receiverId',
            targetKey: 'id',
            as: 'receiver',
        });
    }
}

module.exports = Message; 