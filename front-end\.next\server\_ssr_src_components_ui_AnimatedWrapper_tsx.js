"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_AnimatedWrapper_tsx";
exports.ids = ["_ssr_src_components_ui_AnimatedWrapper_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/AnimatedWrapper.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/AnimatedWrapper.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedWrapper: () => (/* binding */ AnimatedWrapper),\n/* harmony export */   PageTransition: () => (/* binding */ PageTransition),\n/* harmony export */   StaggeredGrid: () => (/* binding */ StaggeredGrid),\n/* harmony export */   StaggeredItem: () => (/* binding */ StaggeredItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/animations */ \"(ssr)/./src/lib/animations.ts\");\n/* __next_internal_client_entry_do_not_use__ AnimatedWrapper,StaggeredGrid,StaggeredItem,PageTransition auto */ \n\n\n\n// Custom hook for intersection observer\nconst useIntersectionObserver = (threshold = 0.1, triggerOnce = true)=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasTriggered, setHasTriggered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleIntersection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useIntersectionObserver.useCallback[handleIntersection]\": (entries)=>{\n            const [entry] = entries;\n            if (entry.isIntersecting) {\n                setIsVisible(true);\n                if (triggerOnce) {\n                    setHasTriggered(true);\n                }\n            } else if (!triggerOnce) {\n                setIsVisible(false);\n            }\n        }\n    }[\"useIntersectionObserver.useCallback[handleIntersection]\"], [\n        triggerOnce\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useIntersectionObserver.useEffect\": ()=>{\n            const observer = new IntersectionObserver(handleIntersection, {\n                threshold,\n                rootMargin: '50px'\n            });\n            if (ref.current) {\n                observer.observe(ref.current);\n            }\n            return ({\n                \"useIntersectionObserver.useEffect\": ()=>observer.disconnect()\n            })[\"useIntersectionObserver.useEffect\"];\n        }\n    }[\"useIntersectionObserver.useEffect\"], [\n        handleIntersection,\n        threshold\n    ]);\n    return {\n        ref,\n        isVisible: isVisible || hasTriggered\n    };\n};\n// Animation variants\nconst getAnimationVariants = (animation, delay = 0)=>{\n    const baseTransition = {\n        delay,\n        duration: 0.6,\n        ease: 'easeOut'\n    };\n    switch(animation){\n        case 'fadeInUp':\n            return {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                        delay,\n                        duration: 0.6,\n                        ease: 'easeOut'\n                    }\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                }\n            };\n        case 'fadeIn':\n            return {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1,\n                    transition: baseTransition\n                }\n            };\n        case 'slideIn':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: -20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        case 'scaleIn':\n            return {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1,\n                    transition: baseTransition\n                }\n            };\n        case 'slideInFromLeft':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: -50\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        case 'slideInFromRight':\n            return {\n                initial: {\n                    opacity: 0,\n                    x: 50\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0,\n                    transition: baseTransition\n                }\n            };\n        default:\n            return {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                        delay,\n                        duration: 0.6,\n                        ease: 'easeOut'\n                    }\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                }\n            };\n    }\n};\nconst AnimatedWrapper = ({ children, className = '', delay = 0, animation = 'fadeInUp', threshold = 0.1, triggerOnce = true, disabled = false, 'data-testid': dataTestId, 'aria-label': ariaLabel })=>{\n    const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);\n    if (disabled) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            \"data-testid\": dataTestId,\n            \"aria-label\": ariaLabel,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        ...getAnimationVariants(animation, delay),\n        initial: \"initial\",\n        animate: isVisible ? 'animate' : 'initial',\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\nconst StaggeredGrid = ({ children, className = '', staggerDelay = 0.1, threshold = 0.1, triggerOnce = true, 'data-testid': dataTestId, 'aria-label': ariaLabel })=>{\n    const { ref, isVisible } = useIntersectionObserver(threshold, triggerOnce);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        variants: {\n            ..._lib_animations__WEBPACK_IMPORTED_MODULE_2__.staggerContainer,\n            animate: {\n                transition: {\n                    staggerChildren: staggerDelay,\n                    delayChildren: 0.1\n                }\n            }\n        },\n        initial: \"initial\",\n        animate: isVisible ? 'animate' : 'initial',\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nconst StaggeredItem = ({ children, className = '', index = 0, 'data-testid': dataTestId, 'aria-label': ariaLabel })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: className,\n        \"data-testid\": dataTestId,\n        \"aria-label\": ariaLabel,\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                delay: index * 0.1,\n                duration: 0.4,\n                ease: [\n                    0.4,\n                    0.0,\n                    0.2,\n                    1\n                ]\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: -20,\n            transition: {\n                duration: 0.2,\n                ease: [\n                    0.4,\n                    0.0,\n                    1,\n                    1\n                ]\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\nconst PageTransition = ({ children, className = '', duration = 0.4, 'data-testid': dataTestId, 'aria-label': ariaLabel })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: className,\n            \"data-testid\": dataTestId,\n            \"aria-label\": ariaLabel,\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -10\n            },\n            transition: {\n                duration,\n                ease: 'easeOut'\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\ui\\\\AnimatedWrapper.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/AnimatedWrapper.tsx\n");

/***/ })

};
;