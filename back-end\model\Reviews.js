const { Model, DataTypes } = require("sequelize");

class Reviews extends Model {
    static initModel(sequelize) {
        return Reviews.init(
            {
                ID: {
                    primaryKey: true,
                    allowNull: false,
                    autoIncrement: true,
                    type: DataTypes.BIGINT,
                },
                Perk_ID: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'perks', // references table name, not model name
                        key: 'perkId',
                    },
                    onDelete: 'CASCADE',
                    onUpdate: 'CASCADE',
                },
                User_ID: {
                    type: DataTypes.BIGINT,
                    allowNull: true, // set to false if you want to enforce it
                },
                UserName: {
                    type: DataTypes.STRING(100),
                    allowNull: false,
                },
                Review: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                Avatar: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                Stars: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    validate: {
                        min: 1,
                        max: 5,
                    },
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'reviews',
                timestamps: true,
            }
        );
    }

    static associate(models) {
        Reviews.belongsTo(models.Perks, {
            foreignKey: 'Perk_ID',
            targetKey: 'perkId',
            as: 'perk',
        });
    }
}

module.exports = Reviews;
