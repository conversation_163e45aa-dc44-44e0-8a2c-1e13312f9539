'use client';

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { disableBodyScroll, enableBodyScroll, clearAllBodyScrollLocks } from 'body-scroll-lock';

export interface GlobalModalConfig {
  id?: string;
  component: React.ReactNode;
  onClose?: () => void;
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  preventClose?: boolean;
  zIndex?: number;
  backdropClassName?: string;
  modalClassName?: string;
  disableScroll?: boolean;
}

export interface GlobalModalContextType {
  modals: GlobalModalConfig[];
  openModal: (config: GlobalModalConfig) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  isModalOpen: (id: string) => boolean;
  getModalById: (id: string) => GlobalModalConfig | undefined;
}

interface GlobalModalProviderProps {
  children: React.ReactNode;
  defaultZIndex?: number;
}

const GlobalModalContext = createContext<GlobalModalContextType | undefined>(undefined);

export function GlobalModalProvider({ children, defaultZIndex = 1000 }: GlobalModalProviderProps) {
  const [modals, setModals] = useState<GlobalModalConfig[]>([]);
  const nextZIndex = useRef(defaultZIndex);
  const modalRefs = useRef<Map<string, HTMLElement>>(new Map());

  // Generate unique modal ID
  const generateModalId = useCallback(() => {
    return `global-modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Open modal
  const openModal = useCallback((config: GlobalModalConfig) => {
    const id = config.id ?? generateModalId();
    const zIndex = config.zIndex || nextZIndex.current;
    
    const modalConfig: GlobalModalConfig = {
      ...config,
      id,
      zIndex,
      closeOnBackdropClick: config.closeOnBackdropClick ?? true,
      closeOnEscape: config.closeOnEscape ?? true,
      preventClose: config.preventClose ?? false,
      disableScroll: config.disableScroll ?? true,
    };

    setModals(prev => [...prev, modalConfig]);
    nextZIndex.current = zIndex + 1;
    
    return id;
  }, [generateModalId]);

  // Close modal
  const closeModal = useCallback((id: string) => {
    setModals(prev => {
      const newModals = prev.filter(modal => modal.id !== id);
      
      // Enable body scroll if no modals are open
      if (newModals.length === 0) {
        enableBodyScroll(document.body);
      }
      
      return newModals;
    });
    
    // Clean up modal ref
    modalRefs.current.delete(id);
  }, []);

  // Close all modals
  const closeAllModals = useCallback(() => {
    setModals([]);
    clearAllBodyScrollLocks();
    modalRefs.current.clear();
  }, []);

  // Check if modal is open
  const isModalOpen = useCallback((id: string) => {
    return modals.some(modal => modal.id === id);
  }, [modals]);

  // Get modal by ID
  const getModalById = useCallback((id: string) => {
    return modals.find(modal => modal.id === id);
  }, [modals]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && modals.length > 0) {
        const topModal = modals[modals.length - 1];
        if (topModal.closeOnEscape && !topModal.preventClose) {
          closeModal(topModal.id ?? "");
          topModal.onClose?.();
        }
      }
    };

    if (modals.length > 0) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [modals, closeModal]);

  // Handle backdrop click
  const handleBackdropClick = useCallback((modal: GlobalModalConfig, event: React.MouseEvent) => {
    console.log("modal", modal);
    if (event.target === event.currentTarget && modal.closeOnBackdropClick && !modal.preventClose) {
      closeModal(modal.id ?? "");
      modal.onClose?.();
    }
  }, [closeModal]);

  // Handle modal ref for scroll lock
  const handleModalRef = useCallback((modalId: string, element: HTMLElement | null) => {
    if (element) {
      modalRefs.current.set(modalId, element);
      const modal = getModalById(modalId);
      if (modal?.disableScroll) {
        disableBodyScroll(element);
      }
    }
  }, [getModalById]);

  const value: GlobalModalContextType = {
    modals,
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
    getModalById,
  };

  return (
    <GlobalModalContext.Provider value={value}>
      {children}
      {modals.length > 0 && typeof window !== 'undefined' && createPortal(
        <div className="global-modal-portal">
          {modals.map((modal) => (
            <div
              key={modal.id}
              className={`fixed inset-0 flex items-center justify-center ${modal.backdropClassName || 'bg-black/50 backdrop-blur-sm'}`}
              style={{ zIndex: modal.zIndex }}
              onClick={(e) => handleBackdropClick(modal, e)}
            >
              <div
                ref={(el) => handleModalRef(modal.id ?? "", el)}
                className={modal.modalClassName || ''}
                onClick={(e) => e.stopPropagation()}
              >
                {modal.component}
              </div>
            </div>
          ))}
        </div>,
        document.body
      )}
    </GlobalModalContext.Provider>
  );
}

export function useGlobalModal() {
  const context = useContext(GlobalModalContext);
  if (context === undefined) {
    throw new Error('useGlobalModal must be used within a GlobalModalProvider');
  }
  return context;
} 