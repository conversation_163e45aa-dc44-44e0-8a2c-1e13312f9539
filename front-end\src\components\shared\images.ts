// Placeholder images for testing
export const PLACEHOLDER_IMAGES = {
  avatar: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23cccccc'/%3E%3Ccircle cx='50' cy='40' r='20' fill='%23ffffff'/%3E%3Ccircle cx='50' cy='100' r='40' fill='%23ffffff'/%3E%3C/svg%3E",
  tokenIcon: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%234287f5'/%3E%3Cpath d='M40,40 L60,40 L60,60 L40,60 Z' fill='%23ffffff'/%3E%3C/svg%3E",
  perkItem: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23eeeeee'/%3E%3Ctext x='50' y='55' font-family='Arial' font-size='16' text-anchor='middle' fill='%23999999'%3EITEM%3C/text%3E%3C/svg%3E",
};

// Update these in your components like:
// import { PLACEHOLDER_IMAGES } from '@/components/shared/images';
// <Image src={PLACEHOLDER_IMAGES.avatar} ... /> 