import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { UserCircle } from 'lucide-react';


import React, { useEffect, useState, useMemo, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { staggerContainer } from '@/lib/animations';
import { getTokenDetails } from '../../../utils/helpers';
import { useTranslation } from '@/hooks/useTranslation';
import { useRouter } from 'next/navigation';
const TradeChart = dynamic(() => import('@/components/shared/chart'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});
const TokenHeader = dynamic(() => import('@/components/shared/token-header'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});
const TokenStats = dynamic(() => import('@/components/shared/token-stats'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});
const ExchangeForm = dynamic(() => import('@/components/shared/exchange-form'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});
const Comments = dynamic(() => import('@/components/shared/comments'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});
const AvailablePerks = dynamic(() => import('@/components/shared/available-perks'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const AnimatedWrapper = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.AnimatedWrapper })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

interface TokenContentProps {
  tokenData: any;
  isLoggedIn: boolean;
  onLogin: () => void;
}

export const TokenContent: React.FC<TokenContentProps> = ({
  tokenData,
  isLoggedIn,
  onLogin,
}) => {
  const { t } = useTranslation();

  const [price, setPrice] = useState<number | 0.0>(0.0);
  const [marketcap, setMarketcap] = useState<number | 0.0>(0.0);
  const router = useRouter();

  // Memoize token address to prevent unnecessary API calls
  const tokenAddress = useMemo(() => tokenData?.tokenAddress, [tokenData?.tokenAddress]);

  useEffect(() => {
    const fetchDetails = async () => {
      if (!tokenAddress) return;

      try {
        const { tokenPrice, tokenMarketcap } = await getTokenDetails(tokenAddress);
        setPrice(tokenPrice);
        setMarketcap(tokenMarketcap);
      } catch (err) {
        console.error('Error fetching token details:', err);
      }
    };

    fetchDetails();
  }, [tokenAddress]);

  // Memoize address shortening function
  const shortenAddress = useCallback((address: string) => {
    if (!address) return ''; // Return empty if no address is available
    const start = address.slice(0, 3); // First 3 characters
    const end = address.slice(-3); // Last 3 characters
    return `${start}...${end}`;
  }, []);

  // Memoize expensive calculations
  const formattedMarketcap = useMemo(() => {
    return `${marketcap !== null
      ? marketcap.toFixed(2)
      : !tokenData.marketcap || tokenData.marketcap === 0
        ? 0.0
        : 0.0
    }$`;
  }, [marketcap, tokenData.marketcap]);

  const formattedPrice = useMemo(() => {
    return `${price !== null
      ? price.toFixed(8)
      : !tokenData.price || tokenData.price === 0
        ? 0.0
        : 0.0
    }$`;
  }, [price, tokenData.price]);

  // Memoize changes object to prevent recreation
  const changes = useMemo(() => ({
    h24: `${!tokenData.changes?.h24 || tokenData.changes.h24 === 0
        ? (Math.random() * 10 - 5).toFixed(2)
        : tokenData.changes.h24
      }%`,
    d7: `${!tokenData.changes?.d7 || tokenData.changes.d7 === 0
        ? 0.0
        : tokenData.changes.d7
      }%`,
    m1: `${!tokenData.changes?.m1 || tokenData.changes.m1 === 0
        ? 0.0
        : tokenData.changes.m1
      }%`,
    alltime: `${!tokenData.changes?.alltime || tokenData.changes.alltime === 0
        ? 0.0
        : tokenData.changes.alltime
      }%`,
  }), [tokenData.changes]);

  // Memoize stats object
  const stats = useMemo(() => ({
    usd: marketcap,
    min24h:
      tokenData.stats?.min24h ||
      Math.floor(Math.random() * 50000),
    max24h: tokenData.stats?.max24h || 0.0,
    volume24h: tokenData.stats?.volume24h || 0.0,
    return24h: tokenData.stats?.return24h || 0.0,
  }), [marketcap, tokenData.stats]);

  // Memoize exchange form options
  const exchangeFormOptions = useMemo(() => ({
    fromOptions: [
      {
        id: 1,
        symbol: 'SOL',
        name: 'Solana',
        icon: 'https://upload.wikimedia.org/wikipedia/en/b/b9/Solana_logo.png',
        tokenAddress: '',
        balance: 0,
        creatorWallet: '',
        tokenDetails: [],
      },
    ],
    toOptions: [
      {
        id: tokenData.tokenId,
        symbol: tokenData.ticker,
        name: tokenData.name,
        tokenAddress: tokenData.tokenAddress,
        icon: tokenData.image,
        balance: 0,
        creatorWallet: tokenData.creatorWallet,
        tokenDetails: tokenData,
      },
    ],
  }), [tokenData]);

  // Memoize available perks slice
  const availablePerks = useMemo(() => 
    tokenData.perks?.slice(0, 3) || [], [tokenData.perks]);

  // Memoize creator address
  const creatorAddress = useMemo(() => 
    shortenAddress(tokenData.creatorWallet), [tokenData.creatorWallet, shortenAddress]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-6"
        >
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors group"
          >
            <div className="p-2 rounded-lg group-hover:bg-gray-100 transition-colors">
              <ArrowLeft size={20} />
            </div>
            <span className="font-medium">{t('coins.backToCoins')}</span>
          </Link>
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate="animate"
          className="space-y-8"
        >
          <AnimatedWrapper delay={0.1}>
            <TokenHeader
              name={tokenData.name}
              username={creatorAddress}
              handle=""
              marketcap={formattedMarketcap}
              userProfileLink={tokenData.userId ? `/profile/creator/${tokenData.userId}` : undefined}

              price={formattedPrice}
              changes={changes}
            />
          </AnimatedWrapper>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-12">
            <div className="xl:col-span-2 space-y-8">
              <AnimatedWrapper delay={0.2}>
                <TokenStats
                  symbols={[tokenData.ticker + '/SOL']}
                  creator={creatorAddress}
                  stats={stats}
                />
              </AnimatedWrapper>

              <AnimatedWrapper delay={0.3}>
                <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">
                    {t('coins.priceChart')}
                  </h3>
                  <TradeChart />
                </div>
              </AnimatedWrapper>
            </div>

            <div className="xl:col-span-1">
              <div className="sticky top-8">
                <AnimatedWrapper delay={0.4}>
                  <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
                    <h3 className="text-xl font-semibold text-gray-900 mb-6">
                      {t('coins.trade')}
                    </h3>
                    <ExchangeForm
                      fromOptions={exchangeFormOptions.fromOptions}
                      toOptions={exchangeFormOptions.toOptions}
                    />
                  </div>
                </AnimatedWrapper>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <AnimatedWrapper delay={0.5}>
              <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm">
                <Comments
                  tokenID={tokenData.tokenId}
                  comments={tokenData.comments || []}
                  description={tokenData.description}
                  onSort={() => { }}
                  onShowDetails={() => { }}
                  isLoggedIn={isLoggedIn}
                  onLogin={onLogin}
                />
              </div>
            </AnimatedWrapper>

            <AnimatedWrapper delay={0.6}>
              <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('coins.availablePerks')}
                </h3>
                <AvailablePerks perks={availablePerks} />
                {tokenData.perks?.length > 3 && (
                  <div className="mt-6 text-center">
                    <Link
                      href="/perks-shop"
                      className="inline-flex items-center gap-2 text-[#F58A38] hover:text-[#FF6600] font-medium transition-colors"
                    >
                      {t('coins.viewAllPerks')}
                      <ArrowLeft size={16} className="rotate-180" />
                    </Link>
                  </div>
                )}
              </div>
            </AnimatedWrapper>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
