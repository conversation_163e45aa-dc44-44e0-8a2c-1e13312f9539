import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useState, useEffect, useMemo, useCallback } from 'react';

export const useTranslation = () => {
  const { t, i18n, ready } = useI18nTranslation();
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('i18nextLng') || 'en';
      setCurrentLanguage(savedLanguage);
      setIsRTL(savedLanguage === 'ar');
    }
  }, []);

  const changeLanguage = useCallback((language: string) => {
    i18n.changeLanguage(language);
    setCurrentLanguage(language);
    setIsRTL(language === 'ar');
    
    // Update document attributes
    if (typeof document !== 'undefined') {
      document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = language;
    }
    
    // Save to localStorage
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('i18nextLng', language);
    }
  }, [i18n]);

  // Create a safe translation function
  const safeT = useCallback((key: string, options?: any): string => {
    if (!ready || !i18n.isInitialized) {
      return key;
    }
    const result = t(key, options);
    return typeof result === 'string' ? result : key;
  }, [t, ready, i18n.isInitialized]);

  return {
    t: safeT,
    i18n,
    currentLanguage,
    changeLanguage,
    isRTL,
    isReady: ready && i18n?.isInitialized,
  };
}; 