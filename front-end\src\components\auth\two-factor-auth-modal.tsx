import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import { verify2FALogin } from '@/axios/requests';
import { useAppContext } from '@/contexts/AppContext';
import { usePrivy } from '@privy-io/react-auth';
import { showErrorToast, showSuccessToast, ValidationError, AuthenticationError, NetworkError } from '@/utils/errorHandling';

interface TwoFactorAuthModalProps {
  onClose: () => void;
  userId: string | number;
}

const TwoFactorAuthModal: React.FC<TwoFactorAuthModalProps> = ({ 
  onClose,
  userId
}) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [fieldError, setFieldError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const { login, logout } = useAppContext();
  const { logout: privyLogout } = usePrivy();

  // Validation function
  const validateCode = (code: string): { isValid: boolean; error?: string } => {
    if (!code.trim()) {
      return { isValid: false, error: 'Verification code is required' };
    }
    
    if (code.length !== 6) {
      return { isValid: false, error: 'Code must be exactly 6 digits' };
    }
    
    if (!/^\d{6}$/.test(code)) {
      return { isValid: false, error: 'Code must contain only numbers' };
    }
    
    return { isValid: true };
  };

  // Handle input change with validation
  const handleCodeChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    setVerificationCode(numericValue);
    
    // Clear error when user starts typing
    if (fieldError) {
      setFieldError(null);
    }
    
    // Real-time validation for 6-digit requirement
    if (numericValue.length > 6) {
      setFieldError('Code cannot exceed 6 digits');
    } else if (fieldError && numericValue.length === 6) {
      setFieldError(null);
    }
  };

  const cancelSubmit = () => {
    privyLogout();
    logout();
    onClose();
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate before submission
    const validation = validateCode(verificationCode);
    if (!validation.isValid) {
      setFieldError(validation.error || 'Invalid verification code');
      return;
    }
    
    setIsLoading(true);
    setIsValidating(true);
    setFieldError(null);
    
    try {
      const response = await verify2FALogin({
        userId,
        token: verificationCode
      });
      
      if (response.status === 200) {
        // Complete the login process with the user data and token
        login(response.data, response.token);
        showSuccessToast('Two-factor authentication successful!');
        onClose();
      } else {
        setFieldError(response.message || 'Invalid verification code');
        showErrorToast(response.message || 'Invalid verification code');
      }
    } catch (error: any) {
      let errorMessage = 'Failed to verify 2FA code';
      
      // Handle specific error types
      if (error instanceof ValidationError) {
        errorMessage = error.message;
        setFieldError(error.message);
      } else if (error instanceof AuthenticationError) {
        errorMessage = 'Authentication failed. Please try again.';
        setFieldError('Authentication failed');
      } else if (error instanceof NetworkError) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.code === '2FA_LOGIN_ERROR') {
        errorMessage = error.message || 'Invalid verification code';
        setFieldError('Invalid verification code');
      } else {
        // Generic error handling
        errorMessage = error.message || 'Failed to verify 2FA code';
        setFieldError('Verification failed');
      }
      
      showErrorToast(errorMessage);
    } finally {
      setIsLoading(false);
      setIsValidating(false);
    }
  };
  
  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Two-Factor Authentication</h2>
          <button
            onClick={cancelSubmit}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X size={24} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              Please enter the 6-digit verification code from your Google Authenticator app.
            </p>
            
            <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
              Verification Code
            </label>
            <div className="relative">
              <input
                id="verificationCode"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={6}
                value={verificationCode}
                onChange={(e) => handleCodeChange(e.target.value)}
                placeholder="Enter 6-digit code"
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${
                  fieldError 
                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                } ${isValidating ? 'bg-gray-50' : ''}`}
                required
                autoFocus
                disabled={isLoading}
              />
              {isValidating && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            
            {/* Error message display */}
            {fieldError && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-red-500 text-sm mt-1 flex items-center"
              >
                <span className="mr-1">⚠</span>
                {fieldError}
              </motion.p>
            )}
            
            {/* Success indicator when code is valid */}
            {verificationCode.length === 6 && !fieldError && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-green-500 text-sm mt-1 flex items-center"
              >
                <span className="mr-1">✓</span>
                Code format is valid
              </motion.p>
            )}
          </div>
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={cancelSubmit}
              disabled={isLoading}
              className="mr-2 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 focus:outline-none disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || verificationCode.length !== 6 || !!fieldError}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Verifying...' : 'Verify'}
            </button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
};

export default TwoFactorAuthModal; 