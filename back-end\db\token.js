const { Op } = require("sequelize");
const dataContext = require(".");

exports.upsertTokenByMint = async (tokenAddress, bondingCurvePda) => {
  const existingToken = await dataContext.Token.findOne({
    where: { tokenAddress },
  });

  if (existingToken) {
    if (!existingToken.bondingCurvePda) {
      await dataContext.Token.update(
        {
          bondingCurvePda,
          graduated: false,
        },
        { where: { tokenAddress } }
      );
    }
  } else {
    console.log("Token address missing: " + tokenAddress);
  }
};

exports.getUngraduatedTokens = async () => {
  return await dataContext.Token.findAll({ where: { graduated: false } });
};

exports.markTokenAsGraduated = async (tokenAddress,poolAddress,poolsignature,nftMint,locklpTransaction) => {
    await dataContext.Token.update(
      { graduated: true,poolAddress:poolAddress,poolsignature:poolsignature,nftMint:nftMint,locklpTransaction:locklpTransaction},
      { where: { tokenAddress: tokenAddress } }
    );
  };

exports.getAllTokens = async () => {
  const tokens = await dataContext.Token.findAll({
    attributes: ["tokenId", "name"],
  });
  return tokens.map((el) => ({
    tokenId: el.dataValues.tokenId,
    name: el.dataValues.name,
  }));
};

exports.setTokenPriceInUsd = async ({ tokenId, price }) => {
  await dataContext.Token.update(
    {
      price,
    },
    {
      where: {
        tokenId: {
          [Op.eq]: tokenId,
        },
      },
    }
  );
};

exports.getPriceInUsd = async ({ name, value }) => {
  const price = await dataContext.Token.findOne({
    attributes: ["price", "decimal"],
    where: {
      name,
    },
  });
  return [
    (value * price.dataValues.price) / 10 ** price.dataValues.decimal,
    value / 10 ** price.dataValues.decimal,
  ];
};
