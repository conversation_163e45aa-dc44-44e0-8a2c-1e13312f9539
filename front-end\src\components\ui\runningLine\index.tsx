'use client';

import { motion, AnimatePresence, Variants } from 'framer-motion';
import { Bell, X, Pause, Play, TrendingUp, TrendingDown } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import Marquee from 'react-fast-marquee';
import { Message, RunningLineProps } from './types';
import { fetchFormattedUserLogs } from '@/utils/helpers';
import { useRouter } from 'next/navigation';
import NotificationModal from '@/components/shared/header/Notifications';
import ChatModal from '@/components/shared/chat/ChatModal';
import { useUnreadChatMessages } from '@/contexts/AppContext';
const TRADE_ID = 1;
const RECEIVER_ID = 2;


import { PERFORMANCE_CONFIG } from '@/config/environment';

const RunningLine: React.FC<RunningLineProps> = ({
  messages: initialMessages,
  speed = 'fast',
  pauseOnHover = true,
  showControls = false,
  autoRefresh = false,
  refreshInterval = PERFORMANCE_CONFIG.AUTO_REFRESH_INTERVAL * 1000, // Convert seconds to milliseconds
}) => {
  const [isPaused, setIsPaused] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [messages, setMessages] = useState<Message[]>([]);

  const [notifications, setNotifications] = useState<Message[]>([]);

  const router = useRouter();

  const [openChatModal, setOpenChatModal] = useState(false);

  const { unreadChatMessages } = useUnreadChatMessages();

  useEffect(() => {
    const fetchLogs = async () => {
      if (initialMessages) {
        setMessages(initialMessages);
      } else {
        const userId = "0"; // 0 for general notifications
        const type = "1";
        const data = await fetchFormattedUserLogs(userId, type);
        
        // If no data returned from API, use default messages
        if (data.length === 0) {
          console.log('No system logs found, using default messages');
          const defaultMessages = generateDefaultMessages();
          setMessages(defaultMessages);
          setNotifications(defaultMessages);
        } else {
          setMessages(data);
          setNotifications(data);
        }
      }
    };
  
    fetchLogs();
  }, [initialMessages]);


 

  useEffect(() => {
    if (autoRefresh && !initialMessages) {
      const interval = setInterval(async () => {
        // Fetch fresh data from the API instead of just reusing notifications
        const userId = "0"; // 0 for general notifications
        const type = "1";
        const freshData = await fetchFormattedUserLogs(userId, type);
        
        // If no data returned from API, use default messages
        if (freshData.length === 0) {
          console.log('No system logs found during refresh, using default messages');
          const defaultMessages = generateDefaultMessages();
          setMessages(defaultMessages);
          setNotifications(defaultMessages);
        } else {
          setMessages(freshData);
          setNotifications(freshData);
        }
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, initialMessages]);

  const getMarqueeSpeed = () => {
    switch (speed) {
      case 'slow':
        return 20;
      case 'fast':
        return 80;
      case 'normal':
      default:
        return 50;
    }
  };

  const getMessageColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'text-green-600';
      case 'sell':
        return 'text-blue-600';
      case 'airdrop':
        return 'text-purple-600';
      case 'burn':
        return 'text-red-600';
      default:
        return 'text-[#FF6600]';
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'buy':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'sell':
        return <TrendingDown className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: 'auto',
      transition: { duration: 0.5, ease: 'easeOut' },
    },
    exit: {
      opacity: 0,
      height: 0,
      transition: { duration: 0.3 },
    },
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="w-full bg-gradient-to-r from-[#FFF7F1] via-[#FFF9F5] to-[#FFF7F1] border-b border-orange-200 shadow-sm"
        variants={containerVariants as Variants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="max-w-full mx-auto px-6">
          <div className="h-16 flex items-center w-full overflow-hidden relative group">
            {/* Notification Icon */}
            <button
              type="button"
              onClick={()=>setOpenChatModal(true)}
              className="focus:outline-none"
              aria-label="Open notifications"
              style={{ background: 'none', border: 'none', padding: 0 }}
            >
              <motion.div
                className="flex-shrink-0 ml-4 mr-6 z-10 cursor-pointer"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
              >
                <div className="relative">
                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }}
                  >
                    <div className="w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center shadow-lg">
                      <Bell className="w-5 h-5 text-white" fill="currentColor" />
                    </div>
                  </motion.div>
                  <motion.div
                    className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  >
                    <span className="text-white text-xs font-bold">
                      {unreadChatMessages.length}
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </button>

            {/* Live Indicator */}
            <motion.div
              className="flex-shrink-0 flex items-center gap-2 mr-4 z-10"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <motion.div
                className="w-3 h-3 bg-red-500 rounded-full"
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
              <span className="text-sm font-semibold text-gray-700">LIVE</span>
            </motion.div>

            {/* Marquee Content */}
            <div className="flex-1 overflow-hidden relative">
              {/* Gradient Overlays */}
              <div className="absolute left-0 top-0 w-24 h-full bg-gradient-to-r from-[#FFF7F1] to-transparent z-10 pointer-events-none" />
              <div className="absolute right-0 top-0 w-24 h-full bg-gradient-to-l from-[#FFF7F1] to-transparent z-10 pointer-events-none" />

              <Marquee
                speed={getMarqueeSpeed()}
                pauseOnHover={pauseOnHover}
                pauseOnClick={false}
                play={!isPaused}
                gradient={false}
                className="h-full"
              >
                {messages.map((message, idx) => (
                  <motion.div
                    key={message.id && message.id !== '' ? message.id : `msg-fallback-${message.text || 'no-text'}-${message.timestamp || 'no-timestamp'}-${idx}`}
                    className="inline-flex items-center mx-6 hover:scale-105 transition-transform duration-200 ease-out"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="flex items-center gap-3 bg-white/50 backdrop-blur-sm rounded-full px-4 py-2 border border-orange-100 shadow-sm">
                      {/* Message Icon */}
                      <span className="text-lg">{message.icon}</span>

                      {/* Trend Icon for buy/sell */}
                      {getMessageIcon(message.type)}

                      {/* Message Text */}
                      <span
                        className={`text-sm font-semibold font-['IBM_Plex_Sans'] leading-normal whitespace-nowrap ${getMessageColor(
                          message.type
                        )}`}
                      >
                        {message.text}
                      </span>

                      {/* Type Badge */}
                      <span
                        className={`px-2 py-1 text-xs font-bold rounded-full uppercase ${message.type === 'buy'
                            ? 'bg-green-100 text-green-700'
                            : message.type === 'sell'
                              ? 'bg-blue-100 text-blue-700'
                              : message.type === 'airdrop'
                                ? 'bg-purple-100 text-purple-700'
                                : message.type === 'burn'
                                  ? 'bg-red-100 text-red-700'
                                  : 'bg-orange-100 text-orange-700'
                          }`}
                      >
                        {message.type}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </Marquee>
            </div>

            {/* Controls */}
            <div className="flex-shrink-0 mr-4 flex items-center gap-2 z-10">
              {showControls && (
                <motion.button
                  className="p-2.5 hover:bg-orange-100 rounded-full transition-colors bg-white/70 backdrop-blur-sm border border-orange-200"
                  onClick={() => setIsPaused(!isPaused)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  title={isPaused ? 'Resume' : 'Pause'}
                >
                  {isPaused ? (
                    <Play size={16} className="text-[#FF6600]" />
                  ) : (
                    <Pause size={16} className="text-[#FF6600]" />
                  )}
                </motion.button>
              )}

              <motion.button
                className="p-2.5 hover:bg-orange-100 rounded-full transition-colors bg-white/70 backdrop-blur-sm border border-orange-200"
                onClick={() => setIsVisible(false)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Close notifications"
              >
                <X size={16} className="text-[#FF6600]" />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
      {openChatModal && (
        <ChatModal  
          chatRoomId={`chat-${TRADE_ID}`}
          buyerId={1}
          sellerId={RECEIVER_ID} 
          onClose={() => setOpenChatModal(false)} 
        />
      )}
    </AnimatePresence>
  );
};

import { FEATURE_FLAGS } from '@/config/environment';

export default function RunningLineWithStyles() {
  return (
    <RunningLine
      showControls={true}
      autoRefresh={FEATURE_FLAGS.ENABLE_AUTO_REFRESH}
      refreshInterval={PERFORMANCE_CONFIG.AUTO_REFRESH_INTERVAL * 1000}
    />
  );
}
