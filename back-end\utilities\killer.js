/** @format */

const cluster = require("cluster");
var colors = require("colors");

/**
 * runs a function for a duration of time
 * @param fn - function - the function to run
 * @param delay - int - the duration to run the function for
 */
const killer = (fn, delay) => {
  if (cluster.isMaster) {
    console.log(colors.magenta("This is a master cluster"));
    const fn = cluster.fork();
    setTimeout((_) => {
      console.log(colors.red("killing process"));
      return fn.process.kill();
    }, delay);
  } else {
    console.log(colors.red("This is not a master cluster"));
    fn();
  }
};

module.exports = killer;
