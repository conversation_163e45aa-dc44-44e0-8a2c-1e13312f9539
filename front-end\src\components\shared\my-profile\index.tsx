import { useRouter } from 'next/navigation';
import React, { useState, useMemo, useCallback } from 'react';
import dynamic from 'next/dynamic';

const TokenData = dynamic(() => import('@/components/shared/tokenData').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


import { useTranslation } from '../../../hooks/useTranslation';

type TokenDataType = {
  totalDollorPrice: any;
  tokenDetails: {
    name: string;
    ticker: string;
    tokenId?: number;
    tokenAddress?: string;
  };
  totalAmount: string;
  price: string;
  tokenName: string;
  percentChange: string;
  showPositive: boolean;
};

const formatPrice = (price: any) => {
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  }).format(Number(price));

  return formattedPrice;
};

// Function to calculate total balance from token data
const calculateTotalBalance = (tokens: TokenDataType[]) => {
  let totalBalance = 0;

  tokens.forEach((token) => {
    const amountValue = Number(token.totalDollorPrice);
    if (!isNaN(amountValue)) {
      totalBalance += amountValue;
    }
  });

  return formatPrice(totalBalance.toFixed(4));
};

// Function to generate random coin colors
const getCoinColor = (index: number, ticker: string) => {
  const colors = [
    'bg-gradient-to-br from-orange-400 to-orange-600',
    'bg-gradient-to-br from-blue-400 to-blue-600',
    'bg-gradient-to-br from-green-400 to-green-600',
    'bg-gradient-to-br from-purple-400 to-purple-600',
    'bg-gradient-to-br from-pink-400 to-pink-600',
    'bg-gradient-to-br from-indigo-400 to-indigo-600',
    'bg-gradient-to-br from-red-400 to-red-600',
    'bg-gradient-to-br from-yellow-400 to-yellow-600',
    'bg-gradient-to-br from-teal-400 to-teal-600',
    'bg-gradient-to-br from-cyan-400 to-cyan-600',
  ];

  // Use ticker hash for consistent color per token
  const hash = ticker.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

// Function to get coin logo/initials
const getCoinLogo = (ticker: string, name: string) => {
  // If there's an official logo, we can add logic here to return it
  // For now, return initials
  const initials =
    ticker.length >= 2
      ? ticker.substring(0, 2).toUpperCase()
      : name
          .split(' ')
          .map((word) => word[0])
          .join('')
          .substring(0, 2)
          .toUpperCase();
  return initials;
};

// Memoized Token Card Component
const TokenCard = React.memo(({ 
  token, 
  index, 
  onCoinClick 
}: { 
  token: TokenDataType; 
  index: number; 
  onCoinClick: (token: TokenDataType) => void;
}) => {
  const coinColor = useMemo(() => getCoinColor(index, token.tokenDetails.ticker), [index, token.tokenDetails.ticker]);
  const coinLogo = useMemo(() => getCoinLogo(token.tokenDetails.ticker, token.tokenDetails.name), [token.tokenDetails.ticker, token.tokenDetails.name]);
  const formattedAmount = useMemo(() => `${Number(token.totalAmount).toFixed(3)} ${token.tokenDetails.ticker.toUpperCase()}`, [token.totalAmount, token.tokenDetails.ticker]);
  const formattedPrice = useMemo(() => formatPrice(Number(token.totalDollorPrice).toFixed(4)), [token.totalDollorPrice]);
  const percentChange = useMemo(() => `${(Math.random() * (20 - 5) + 5).toFixed(2)}%`, []);

  const handleClick = useCallback(() => {
    onCoinClick(token);
  }, [onCoinClick, token]);

  return (
    <div className="relative">
      <div
        className="cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg"
        onClick={handleClick}
      >
        <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
          {/* Token Logo */}
          <div className="flex items-center gap-3 mb-3">
            <div
              className={`w-12 h-12 rounded-full ${coinColor} flex items-center justify-center text-white font-bold text-sm shadow-md`}
            >
              {coinLogo}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">
                {token.tokenDetails.name}
              </h3>
              <p className="text-xs text-gray-500 uppercase">
                {token.tokenDetails.ticker}
              </p>
            </div>
          </div>

          {/* Token Data */}
          <TokenData
            amount={formattedAmount}
            price={formattedPrice}
            tokenName={token.tokenDetails.name}
            percentChange={percentChange}
            showPositive={token.showPositive}
          />
        </div>
      </div>
    </div>
  );
});

TokenCard.displayName = 'TokenCard';

export default function MyProfile({
  purchasedTokens = [],
}: {
  purchasedTokens?: TokenDataType[];
}) {
  const router = useRouter();
  const [isAscending, setIsAscending] = useState(true);
  const { t } = useTranslation();

  const handleSortToggle = useCallback(() => {
    setIsAscending((prev) => !prev);
  }, []);

  const calculateTotalPrice = useCallback((token: TokenDataType) => {
    const amountValue = parseFloat(token.totalAmount);
    return amountValue * parseFloat(token.price);
  }, []);

  // Memoize sorted tokens
  const sortedTokens = useMemo(() => {
    return purchasedTokens.sort((a, b) => {
      const totalPriceA = calculateTotalPrice(a);
      const totalPriceB = calculateTotalPrice(b);
      return isAscending ? totalPriceA - totalPriceB : totalPriceB - totalPriceA;
    });
  }, [purchasedTokens, isAscending, calculateTotalPrice]);

  // Memoize total balance calculation
  const totalBalance = useMemo(() => {
    return calculateTotalBalance(sortedTokens);
  }, [sortedTokens]);

  // Handle coin click to navigate to coin page
  const handleCoinClick = useCallback((token: TokenDataType) => {
    const coinId =
      token.tokenDetails.tokenId || token.tokenDetails.tokenAddress;
    if (coinId) {
      router.push(`/coins/${coinId}`);
    }
  }, [router]);

  return (
    <>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="text-lg sm:text-xl md:text-2xl font-semibold">
          {t('myProfile.totalBalance')}
        </div>
        <div
          onClick={handleSortToggle}
          className="text-lg sm:text-xl md:text-2xl font-semibold mt-2 sm:mt-0 cursor-pointer"
        >
          {t('myProfile.sortBy')} {isAscending ? t('myProfile.low24h') : t('myProfile.high24h')}
        </div>
      </div>
      <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-normal mt-2 sm:mt-3 md:mt-4">
        {purchasedTokens.length === 0 ? '$0.00' : totalBalance}
      </div>

      {purchasedTokens.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-3 sm:gap-4 md:gap-5 lg:gap-6 mt-6 sm:mt-8 md:mt-12 lg:mt-16">
          {sortedTokens.map((token, index) => (
            <TokenCard
              key={`${token.tokenDetails.tokenId || token.tokenDetails.tokenAddress || index}`}
              token={token}
              index={index}
              onCoinClick={handleCoinClick}
            />
          ))}
        </div>
      ) : (
        <div className="mt-6 sm:mt-8 md:mt-12 lg:mt-16 text-center text-lg sm:text-xl md:text-2xl font-semibold">
          {t('myProfile.noTokens')}
        </div>
      )}
    </>
  );
}
