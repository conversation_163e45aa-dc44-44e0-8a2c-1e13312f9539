import React from "react";
import { Perk } from "./types";

interface PerksListProps {
  sortedPerks: Perk[];
}

const PerksList: React.FC<PerksListProps> = ({ sortedPerks }) => (
  <div className="flex flex-col gap-6 w-full  lg:hidden">
    {sortedPerks.map((perk, idx) => (
      <div
        key={perk.perkId || idx}
        className="bg-white rounded-2xl shadow-lg p-4 w-full"
        aria-label={`Perk card: ${perk.name}`}
        tabIndex={0}
      >
        <img
          src={perk.image}
          alt={perk.name}
          className="w-full h-40 object-cover rounded-xl mb-3"
          aria-label={`Perk image: ${perk.name}`}
        />
        <div className="font-bold text-base mb-1">{perk.name}</div>
        <div className="flex justify-between text-xs text-gray-600 mt-2">
          <div>
            <div className="text-gray-500">Price</div>
            <div className="font-semibold">{perk.price}</div>
          </div>
          <div className="text-right">
            <div className="text-gray-500">Sales</div>
            <div className="font-semibold">{perk.soldCount ?? '-'} </div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

export default PerksList; 