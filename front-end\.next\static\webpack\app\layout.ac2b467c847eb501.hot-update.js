"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx":
/*!************************************************!*\
  !*** ./src/contexts/ChatModalStateContext.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatModalStateProvider: () => (/* binding */ ChatModalStateProvider),\n/* harmony export */   useChatModalState: () => (/* binding */ useChatModalState)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobalModalContext */ \"(app-pages-browser)/./src/contexts/GlobalModalContext.tsx\");\n/* harmony import */ var _SocketProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SocketProvider */ \"(app-pages-browser)/./src/contexts/SocketProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatModalStateProvider,useChatModalState auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst ChatModalStateContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatModalStateProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [chatModalStates, setChatModalStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const { updateModal, getModalById } = (0,_GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal)();\n    const socket = (0,_SocketProvider__WEBPACK_IMPORTED_MODULE_3__.useSocket)();\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // Keep ref in sync with state\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ChatModalStateProvider.useEffect\": ()=>{\n            stateRef.current = chatModalStates;\n        }\n    }[\"ChatModalStateProvider.useEffect\"], [\n        chatModalStates\n    ]);\n    const getChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[getChatModalState]\": (chatRoomId)=>{\n            return stateRef.current.get(chatRoomId);\n        }\n    }[\"ChatModalStateProvider.useCallback[getChatModalState]\"], []);\n    const updateChatModalState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateChatModalState]\": (chatRoomId, updates)=>{\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating state for chatRoom \".concat(chatRoomId, \":\"), updates);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[updateChatModalState]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    const currentState = newMap.get(chatRoomId);\n                    if (currentState) {\n                        const updatedState = {\n                            ...currentState,\n                            ...updates,\n                            lastUpdated: Date.now()\n                        };\n                        newMap.set(chatRoomId, updatedState);\n                        console.log(\"✅ [ChatModalStateContext] Updated state for \".concat(chatRoomId, \":\"), updatedState);\n                    } else {\n                        console.log(\"⚠️ [ChatModalStateContext] No state found for chatRoom \".concat(chatRoomId));\n                    }\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[updateChatModalState]\"], []);\n    const registerChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[registerChatModal]\": (chatRoomId, initialState)=>{\n            console.log(\"\\uD83D\\uDCDD [ChatModalStateContext] Registering chatRoom \".concat(chatRoomId, \":\"), initialState);\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[registerChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.set(chatRoomId, {\n                        ...initialState,\n                        lastUpdated: Date.now()\n                    });\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[registerChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[registerChatModal]\"], []);\n    const unregisterChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (chatRoomId)=>{\n            console.log(\"\\uD83D\\uDDD1️ [ChatModalStateContext] Unregistering chatRoom \".concat(chatRoomId));\n            setChatModalStates({\n                \"ChatModalStateProvider.useCallback[unregisterChatModal]\": (prev)=>{\n                    const newMap = new Map(prev);\n                    newMap.delete(chatRoomId);\n                    return newMap;\n                }\n            }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"]);\n        }\n    }[\"ChatModalStateProvider.useCallback[unregisterChatModal]\"], []);\n    const updateModalProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModalStateProvider.useCallback[updateModalProps]\": (chatRoomId, newProps)=>{\n            const modalId = \"chat-modal-\".concat(chatRoomId);\n            const existingModal = getModalById(modalId);\n            if (!existingModal) {\n                console.log(\"⚠️ [ChatModalStateContext] No modal found with id \".concat(modalId));\n                return false;\n            }\n            console.log(\"\\uD83D\\uDD04 [ChatModalStateContext] Updating modal props for \".concat(modalId, \":\"), newProps);\n            // Update the modal component with new props\n            const updatedComponent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(existingModal.component, {\n                ...existingModal.component.props || {},\n                ...newProps,\n                key: \"\".concat(modalId, \"-\").concat(Date.now()) // Force re-render with new props\n            });\n            return updateModal(modalId, {\n                component: updatedComponent,\n                updateProps: newProps\n            });\n        }\n    }[\"ChatModalStateProvider.useCallback[updateModalProps]\"], [\n        getModalById,\n        updateModal\n    ]);\n    const value = {\n        getChatModalState,\n        updateChatModalState,\n        registerChatModal,\n        unregisterChatModal,\n        updateModalProps\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatModalStateContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\contexts\\\\ChatModalStateContext.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModalStateProvider, \"3cAO7hHL/vIGFSw3n+vUPWZTEQo=\", false, function() {\n    return [\n        _GlobalModalContext__WEBPACK_IMPORTED_MODULE_2__.useGlobalModal,\n        _SocketProvider__WEBPACK_IMPORTED_MODULE_3__.useSocket\n    ];\n});\n_c = ChatModalStateProvider;\nconst useChatModalState = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatModalStateContext);\n    if (!context) {\n        throw new Error('useChatModalState must be used within a ChatModalStateProvider');\n    }\n    return context;\n};\n_s1(useChatModalState, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatModalStateProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatModalStateContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a8af28be109e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYThhZjI4YmUxMDllXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});