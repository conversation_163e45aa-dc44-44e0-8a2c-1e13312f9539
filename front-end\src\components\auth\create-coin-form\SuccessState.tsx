"use client";

import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import React from "react";
import { useTranslation } from '@/hooks/useTranslation';

const SuccessState: React.FC = () => {
  const { t } = useTranslation();
  return (
    <motion.div
      className="w-full mx-auto text-center p-8"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <CheckCircle className="w-24 h-24 text-green-500 mx-auto mb-4" />
      <h2 className="text-2xl font-semibold mb-4 font-['IBM_Plex_Sans']">
        {t('createCoinForm.coinCreatedSuccess')}
      </h2>
      <p className="text-gray-600 font-['IBM_Plex_Sans']">
        {t('createCoinForm.coinCreatedProcessing')}
      </p>
    </motion.div>
  );
};

export default SuccessState; 