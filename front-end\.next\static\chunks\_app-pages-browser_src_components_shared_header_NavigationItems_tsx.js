"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_header_NavigationItems_tsx"],{

/***/ "(app-pages-browser)/./src/components/shared/header/NavigationItems.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shared/header/NavigationItems.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst NavigationItems = (param)=>{\n    let { isActiveRoute, handleNavItemClick, hideCreate, handleCreateTokenClick, handleCreatePerkClick } = param;\n    _s();\n    const { t, isReady, i18n, currentLanguage } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const navigationItems = [\n        {\n            label: t('navigation.dashboard'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.DASHBOARD,\n            icon: \"/icons/dashboard.svg\",\n            activeIcon: \"/icons/dashboard.svg\",\n            requiresAuth: true\n        },\n        {\n            label: t('navigation.perks'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.PERKS,\n            icon: \"/icons/perks.svg\",\n            activeIcon: \"/icons/perks.svg\",\n            requiresAuth: false\n        },\n        {\n            label: t('navigation.coins'),\n            route: _constants__WEBPACK_IMPORTED_MODULE_3__.ROUTES.HOME,\n            icon: \"/icons/coin.svg\",\n            activeIcon: \"/icons/coin.svg\",\n            requiresAuth: false\n        }\n    ];\n    return !isReady ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        className: \"hidden lg:flex items-center gap-2 xl:gap-3 2xl:gap-4\",\n        variants: {\n            hidden: {\n                opacity: 0,\n                x: 20\n            },\n            visible: {\n                opacity: 1,\n                x: 0,\n                transition: {\n                    staggerChildren: 0.1,\n                    delayChildren: 0.2\n                }\n            }\n        },\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: {\n                        hidden: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        visible: {\n                            opacity: 1,\n                            x: 0\n                        }\n                    },\n                    className: \"group h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center gap-2 rounded-full text-sm lg:text-base transition-all duration-300 text-white cursor-pointer \".concat(isActiveRoute(item.route) ? \"bg-black/20 scale-105\" : \"hover:bg-black/10\"),\n                    onClick: ()=>handleNavItemClick(item),\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: isActiveRoute(item.route) ? item.activeIcon : item.icon,\n                            alt: item.label,\n                            width: 20,\n                            height: 20,\n                            className: \"invert group-hover:brightness-0 group-hover:invert filter transition-filter duration-300 ease-in-out h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden xl:inline\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, item.label, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, undefined)),\n            !hideCreate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: {\n                            hidden: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            visible: {\n                                opacity: 1,\n                                x: 0\n                            }\n                        },\n                        className: \"bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30\",\n                        onClick: handleCreateTokenClick,\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:inline\",\n                                children: t('coins.createCoin')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"lg:hidden\",\n                                children: t('coins.createCoin')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: {\n                            hidden: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            visible: {\n                                opacity: 1,\n                                x: 0\n                            }\n                        },\n                        className: \"bg-black/20 text-white h-[44px] lg:h-[46px] px-3 xl:px-6 flex items-center rounded-full text-sm lg:text-base whitespace-nowrap cursor-pointer transition-all duration-300 hover:bg-black/30 ml-1\",\n                        onClick: handleCreatePerkClick,\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:inline\",\n                                children: t('perks.createPerk')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"lg:hidden\",\n                                children: t('perks.perk')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NavigationItems.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n};\n_s(NavigationItems, \"W04ylz4q/1rs+970gW8lP1/sYNs=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = NavigationItems;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavigationItems);\nvar _c;\n$RefreshReg$(_c, \"NavigationItems\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/header/NavigationItems.tsx\n"));

/***/ })

}]);