import React, { useState, useEffect, useCallback } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import {
  extractAirdropCode,
  claimAirdropTokens,
  AirdropData,
} from '@/utils/collect-airdrop';
import { claimAirDrop } from '@/axios/requests';
import { showErrorToast, showSuccessToast } from '@/utils/errorHandling';
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import AirdropModal from '@/components/shared/collect-airdrop/AirdropModal';

interface AirdropState {
  airdropCode: string | null;
  airdropData: AirdropData | null;
  isLoading: boolean;
  isClaiming: boolean;
}

export const useAirdrop = () => {
  const { authenticated, user, login } = usePrivy();
  const { openModal, closeModal, isModalOpen } = useGlobalModal();
  const [state, setState] = useState<AirdropState>({
    airdropCode: null,
    airdropData: null,
    isLoading: false,
    isClaiming: false,
  });
  const [modalId, setModalId] = useState<string | null>(null);

  useEffect(() => {
    const checkForAirdropCode = async () => {
      if (typeof window === 'undefined') return;

      const hash = window.location.hash;
      const airdropCode = extractAirdropCode(hash);

      if (airdropCode) {
        // Check if user is already authenticated - if so, skip modal and let AuthHandlers handle it
        if (authenticated && user?.wallet?.address) {
          console.log('User already authenticated, skipping airdrop modal - AuthHandlers will handle it');
          return;
        }

        setState((prev) => ({ ...prev, isLoading: true, airdropCode }));

        try {
          const rawHash = window.location.hash.replace("#", "");
          if (rawHash != "") {
            const dataIs = {
              code: rawHash,
            };
            const data = await claimAirDrop(dataIs);
            if (data.status == 201) {
              let airdropData = {
                isValid: true,
                tokenAmount: data.claimedAmount,
                message: data.message,
              }

              setState((prev) => ({
                ...prev,
                airdropData,
                isLoading: false,
              }));

              // Open modal using the proper GlobalModalContext API
              const newModalId = openModal({
                component: React.createElement(AirdropModal, {
                  isOpen: true,
                  onClose: () => closeModal(newModalId),
                  onClaim: handleClaim,
                  onConnect: handleConnect,
                  airdropCode: airdropCode,
                  tokenAmount: airdropData.tokenAmount,
                  isWalletConnected: authenticated && !!user?.wallet?.address,
                  isValidCode: airdropData.isValid,
                  isClaiming: state.isClaiming,
                  isLoading: false,
                }),
                closeOnBackdropClick: true,
                closeOnEscape: true,
                disableScroll: true,
              });
              setModalId(newModalId);

            } else {
              setState((prev) => ({
                ...prev,
                isLoading: false,
                airdropData: {
                  isValid: false,
                  tokenAmount: 0,
                  message: 'Error validating airdrop code',
                },
              }));
            }
          }
        } catch (error) {
          console.error('Error validating airdrop code:', error);
          setState((prev) => ({
            ...prev,
            isLoading: false,
            airdropData: {
              isValid: false,
              tokenAmount: 0,
              message: 'Error validating airdrop code',
            },
          }));
        }
      }
    };

    checkForAirdropCode();
  }, [authenticated, user?.wallet?.address, openModal, closeModal]);

  const closeModalHandler = useCallback(() => {
    setState((prev) => ({
      ...prev,
      airdropCode: null,
      airdropData: null,
    }));
    if (modalId) {
      closeModal(modalId);
      setModalId(null);
    }
  }, [closeModal, modalId]);

  const handleConnect = useCallback(async () => {
    try {
      await login();
      const oldHash = window.location.hash.slice(1); // "oysrhpvk8w"
      const newHash = `#c=${oldHash}&i=1`;
      window.history.replaceState(
        null,
        "",
        window.location.pathname + newHash
      );
    } catch (error) {
      console.error('Error connecting wallet:', error);
      showErrorToast('Failed to connect wallet');
    }
  }, [login]);

  const handleClaim = useCallback(async () => {
    if (!state.airdropCode || !user?.wallet?.address) {
      showErrorToast('Missing airdrop code or wallet address');
      return;
    }

    // Start claiming process
    setState((prev) => ({ ...prev, isClaiming: true }));

    try {
      const result = await claimAirdropTokens(
        state.airdropCode,
        user.wallet.address
      );

      if (result.success && result.txHash !== undefined) {
        if (modalId) {
          closeModal(modalId);
          setModalId(null);
        }
        showSuccessToast(
          `Successfully claimed ${state.airdropData?.tokenAmount?.toLocaleString()} tokens!`
        );

        window.history.replaceState(null, '', window.location.pathname);
      } else {
        if (modalId) {
          closeModal(modalId);
          setModalId(null);
        }
        console.log("Failed to claim tokens");
        window.history.replaceState(null, '', window.location.pathname);
        showErrorToast('Token has already been claimed.');
      }
    } catch (error) {
      console.error('Error claiming airdrop:', error);
      showErrorToast('Failed to claim tokens');
    } finally {
      setState((prev) => ({ ...prev, isClaiming: false }));
    }
  }, [state.airdropCode, state.airdropData?.tokenAmount, user?.wallet?.address, closeModal]);

  return {
    isModalOpen: modalId ? isModalOpen(modalId) : false,
    airdropCode: state.airdropCode,
    airdropData: state.airdropData,
    isLoading: state.isLoading,
    isClaiming: state.isClaiming,
    isWalletConnected: authenticated && !!user?.wallet?.address,
    closeModal: closeModalHandler,
    handleConnect,
    handleClaim,
  };
};
