"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_shared_header_NotificationButton_tsx";
exports.ids = ["_ssr_src_components_shared_header_NotificationButton_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/shared/header/NotificationButton.tsx":
/*!*************************************************************!*\
  !*** ./src/components/shared/header/NotificationButton.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/GlobalModalContext */ \"(ssr)/./src/contexts/GlobalModalContext.tsx\");\n\n\n\n\n\n\nconst NotificationModal = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_shared_header_Notifications_index_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./Notifications */ \"(ssr)/./src/components/shared/header/Notifications/index.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\shared\\\\header\\\\NotificationButton.tsx -> \" + \"./Notifications\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 8,\n            columnNumber: 18\n        }, undefined)\n});\nconst ChatModal = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/components/shared/chat/ChatModal */ \"(ssr)/./src/components/shared/chat/ChatModal.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\shared\\\\header\\\\NotificationButton.tsx -> \" + \"@/components/shared/chat/ChatModal\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 12,\n            columnNumber: 18\n        }, undefined)\n});\nconst NotificationButton = ({ notifications })=>{\n    const { openModal, closeModal } = (0,_contexts_GlobalModalContext__WEBPACK_IMPORTED_MODULE_3__.useGlobalModal)();\n    const handleOpenModal = ()=>{\n        openModal({\n            id: \"notification\",\n            component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(NotificationModal, {\n                onClose: ()=>closeModal(\"notification\"),\n                onOpenChat: async (chatRoomId, receiverId)=>{\n                    // Get current user id from localStorage\n                    const userBoStr =  false ? 0 : null;\n                    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n                    const myUserId = userBo?.id;\n                    console.log('🔍 [NotificationButton] Opening chat with:', {\n                        chatRoomId,\n                        receiverId,\n                        myUserId\n                    });\n                    // Use consistent modal ID to prevent multiple modals\n                    const modalId = `chat-modal-${chatRoomId}`;\n                    // Close existing modal if it exists\n                    closeModal(modalId);\n                    // For now, we'll use basic implementation since we don't have notification data\n                    // In the future, this should be unified with NotificationBell logic\n                    openModal({\n                        id: modalId,\n                        component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ChatModal, {\n                            chatRoomId,\n                            buyerId: myUserId,\n                            sellerId: receiverId,\n                            onClose: ()=>closeModal(modalId),\n                            onRelease: ()=>{},\n                            onRefund: ()=>{},\n                            onReport: ()=>{},\n                            activeTrade: undefined // No trade data available from header notifications\n                        }),\n                        closeOnBackdropClick: true,\n                        closeOnEscape: true,\n                        preventClose: false,\n                        zIndex: 10000,\n                        backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',\n                        modalClassName: 'flex flex-col items-start justify-end p-4',\n                        disableScroll: true\n                    });\n                }\n            }),\n            closeOnBackdropClick: true,\n            closeOnEscape: true,\n            preventClose: false,\n            zIndex: 9999,\n            backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',\n            modalClassName: 'flex flex-col items-start justify-end p-4',\n            disableScroll: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n            className: \"relative bg-[#F5F5F5] p-1 lg:p-2 h-[40px] w-[40px] lg:h-[53px] lg:w-[53px] flex items-center justify-center rounded-full transition-all duration-300 hover:bg-gray-200\",\n            whileHover: {\n                scale: 1.05\n            },\n            whileTap: {\n                scale: 0.95\n            },\n            onClick: ()=>handleOpenModal(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: 24,\n                    className: \"lg:w-8 lg:h-8 text-gray-700\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                    className: \"absolute top-[6px] right-[7px] lg:top-[12px] lg:right-[15px] bg-red-500 text-white rounded-full w-[10px] h-[10px] flex items-center justify-center text-xs\",\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 500,\n                        damping: 15\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\header\\\\NotificationButton.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/header/NotificationButton.tsx\n");

/***/ })

};
;