'use client';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Calendar,
  Clock,
  Verified,
  UserCircle,
} from 'lucide-react';
import Image from 'next/image';
import React from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/useTranslation';

interface TokenHeaderProps {
  name: string;
  marketcap: string;
  price: string;
  changes: {
    h24: string;
    d7: string;
    m1: string;
    alltime: string;
  };
  profileImage?: string;
  tokenImage?: string;
  username?: string;
  handle?: string;
  description?: string;
  userProfileLink?: string;
}

const TokenHeader: React.FC<TokenHeaderProps> = ({
  name = "ANAS'S TOKEN",
  marketcap = '49,492$',
  price = '0.5$',
  changes = {
    h24: '2%',
    d7: '-2%',
    m1: '2%',
    alltime: '22%',
  },
  profileImage = '/images/default-profile.png',
  tokenImage = '/images/default-token.png',
  username = 'Zennie---',
  handle = '@32zennieq',
  description = 'About this token',
  userProfileLink,
}) => {
  const router = useRouter();
  const { t } = useTranslation();
  const getChangeData = (value: string) => {
    const isPositive = !value.startsWith('-');
    const numValue = parseFloat(value.replace('%', ''));
    return {
      isPositive,
      numValue,
      colorClass: isPositive ? 'text-green-600' : 'text-red-600',
      bgClass: isPositive ? 'bg-green-50' : 'bg-red-50',
      borderClass: isPositive ? 'border-green-200' : 'border-red-200',
      icon: isPositive ? TrendingUp : TrendingDown,
    };
  };

  const changeItems = [
    { label: t('tokenHeader.24h'), value: changes.h24, icon: Clock },
    { label: t('tokenHeader.7d'), value: changes.d7, icon: Calendar },
    { label: t('tokenHeader.1m'), value: changes.m1, icon: BarChart3 },
    { label: t('tokenHeader.allTime'), value: changes.alltime, icon: TrendingUp },
  ];

  return (
    <motion.div
      className="w-full bg-gradient-to-r from-white via-gray-50 to-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="p-8">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start space-y-6 lg:space-y-0 mb-8">
          {/* Token Info */}
          <div className="flex-1">
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#FF6600] to-[#F58A38] rounded-2xl flex items-center justify-center shadow-lg">
                    <DollarSign size={32} className="text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-[#F58A38] rounded-full flex items-center justify-center">
                    <Verified size={14} className="text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
                    {name}
                  </h1>
                  <p className="text-gray-500 font-medium">{t('tokenHeader.tokenActive')}</p>
                </div>
              </div>

              {/* Price and Market Cap */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <motion.div
                  className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-[#FFF7F1] rounded-lg flex items-center justify-center">
                      <DollarSign size={20} className="text-[#FF6600]" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t('tokenHeader.currentPrice')}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {price}
                      </p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-[#FFF7F1] rounded-lg flex items-center justify-center">
                      <BarChart3 size={20} className="text-[#F58A38]" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t('tokenHeader.marketCap')}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {marketcap}
                      </p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>

          {/* Creator Info */}
          <motion.div
            className="flex items-center max-md:justify-between space-x-4 lg:ml-8"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="text-right flex items-center gap-2">
              <div>
                <p className="text-sm text-gray-500 font-medium">{t('tokenHeader.createdBy')}</p>
                <p className="text-xl font-bold text-gray-900 flex items-center gap-1">
                  {username}
                  {userProfileLink && (
                    <button
                      type="button"
                      onClick={() => router.push(userProfileLink)}
                      className="ml-1 flex items-center group focus:outline-none"
                      title={t('tokenHeader.tokenCreatorProfile')}
                    >
                      <UserCircle size={18} className="text-[#F58A38] group-hover:text-[#FF6600] transition-colors" />
                      <span className="ml-0.5 px-1 py-0.5 bg-[#F58A38] text-white text-[10px] rounded font-semibold leading-none">{t('tokenHeader.creator')}</span>
                    </button>
                  )}
                </p>
                <p className="text-sm text-gray-500">{handle}</p>
              </div>
            </div>
            <div className="relative">
              <div className="w-16 h-16 rounded-full overflow-hidden ring-4 ring-white shadow-lg">
                <Image
                  src="/icons/owl.png"
                  alt={t('tokenHeader.creator')}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
          </motion.div>
        </div>

        {/* Performance Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('tokenHeader.performance')}
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {changeItems.map((item, index) => {
              const changeData = getChangeData(item.value);
              const IconComponent = changeData.icon;

              return (
                <motion.div
                  key={item.label}
                  className={`bg-white rounded-xl border p-4 shadow-sm ${
                    changeData.isPositive
                      ? 'border-[#F58A38]/30 bg-[#FFF7F1]/30'
                      : 'border-red-200 bg-red-50/30'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600">
                      {item.label}
                    </span>
                    <IconComponent
                      size={16}
                      className={
                        changeData.isPositive
                          ? 'text-[#F58A38]'
                          : 'text-red-600'
                      }
                    />
                  </div>
                  <div
                    className={`text-xl font-bold ${
                      changeData.isPositive ? 'text-[#FF6600]' : 'text-red-600'
                    }`}
                  >
                    {changeData.isPositive ? '+' : ''}
                    {item.value}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {changeData.isPositive ? t('tokenHeader.gain') : t('tokenHeader.loss')}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default TokenHeader;
