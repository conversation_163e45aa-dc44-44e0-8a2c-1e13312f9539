"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/perks-shop/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/escrow.ts":
/*!*****************************!*\
  !*** ./src/utils/escrow.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockchainError: () => (/* binding */ BlockchainError),\n/* harmony export */   DuplicateTransactionError: () => (/* binding */ DuplicateTransactionError),\n/* harmony export */   EscrowError: () => (/* binding */ EscrowError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   TransactionError: () => (/* binding */ TransactionError),\n/* harmony export */   canInitiateDispute: () => (/* binding */ canInitiateDispute),\n/* harmony export */   createEscrowAcceptTransaction: () => (/* binding */ createEscrowAcceptTransaction),\n/* harmony export */   createEscrowBuyTransaction: () => (/* binding */ createEscrowBuyTransaction),\n/* harmony export */   createEscrowRefundTransaction: () => (/* binding */ createEscrowRefundTransaction),\n/* harmony export */   createEscrowSellTransaction: () => (/* binding */ createEscrowSellTransaction),\n/* harmony export */   getDisputeStatusInfo: () => (/* binding */ getDisputeStatusInfo),\n/* harmony export */   initiateEscrowDispute: () => (/* binding */ initiateEscrowDispute)\n/* harmony export */ });\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"(app-pages-browser)/./node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/state/mint.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js\");\n/* harmony import */ var _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @solana/spl-token */ \"(app-pages-browser)/./node_modules/@solana/spl-token/lib/esm/constants.js\");\n/* harmony import */ var _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @coral-xyz/anchor */ \"(app-pages-browser)/./node_modules/@coral-xyz/anchor/dist/browser/index.js\");\n/* harmony import */ var _sol_setup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sol/setup */ \"(app-pages-browser)/./src/utils/sol/setup.ts\");\n/* harmony import */ var _axios_requests__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\");\n/* harmony import */ var _errorHandling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errorHandling */ \"(app-pages-browser)/./src/utils/errorHandling.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n// Enhanced error types for escrow operations\nclass EscrowError extends _errorHandling__WEBPACK_IMPORTED_MODULE_4__.AppError {\n    constructor(message, code = 'ESCROW_ERROR', status = 400){\n        super(message, code, status);\n        this.name = 'EscrowError';\n    }\n}\nclass TransactionError extends EscrowError {\n    constructor(message, txId){\n        super(message, 'TRANSACTION_ERROR', 400), this.txId = txId;\n        this.name = 'TransactionError';\n    }\n}\nclass InsufficientFundsError extends EscrowError {\n    constructor(message = 'Insufficient funds to complete the transaction'){\n        super(message, 'INSUFFICIENT_FUNDS', 400);\n        this.name = 'InsufficientFundsError';\n    }\n}\nclass DuplicateTransactionError extends EscrowError {\n    constructor(message = 'Transaction already in progress or completed'){\n        super(message, 'DUPLICATE_TRANSACTION', 409);\n        this.name = 'DuplicateTransactionError';\n    }\n}\nclass BlockchainError extends EscrowError {\n    constructor(message, originalError){\n        super(message, 'BLOCKCHAIN_ERROR', 500), this.originalError = originalError;\n        this.name = 'BlockchainError';\n    }\n}\n// Transaction state management to prevent duplicates\nconst transactionStates = new Map();\n// Last transaction attempt timestamps to prevent rapid duplicates\nconst lastTransactionAttempts = new Map();\n// Clean up old transaction states (older than 5 minutes)\nconst cleanupOldTransactions = ()=>{\n    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n    for (const [key, state] of transactionStates.entries()){\n        if (state.timestamp < fiveMinutesAgo) {\n            transactionStates.delete(key);\n        }\n    }\n};\n// Utility function to send transaction with retry logic\nconst sendTransactionWithRetry = async function(connection, signedTransaction) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1000, wallet = arguments.length > 4 ? arguments[4] : void 0, originalTransaction = arguments.length > 5 ? arguments[5] : void 0;\n    let lastError;\n    let currentSignedTx = signedTransaction;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            console.log(\"\\uD83D\\uDD04 Transaction attempt \".concat(attempt, \"/\").concat(maxRetries));\n            const txId = await connection.sendRawTransaction(currentSignedTx.serialize(), {\n                skipPreflight: false,\n                preflightCommitment: 'confirmed',\n                maxRetries: 0 // Prevent automatic retries that could cause duplicates\n            });\n            console.log(\"✅ Transaction sent successfully on attempt \".concat(attempt, \":\"), txId);\n            return txId;\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n            lastError = error;\n            console.error(\"❌ Transaction attempt \".concat(attempt, \" failed:\"), error.message);\n            // Don't retry for certain errors - throw immediately\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Transaction already processed')) || ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('already been processed')) || ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('User rejected')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('user rejected')) || error.code === 4001 || error.code === -32003) {\n                console.log(\"\\uD83D\\uDEAB Not retrying - error type should not be retried\");\n                throw error;\n            }\n            // Handle blockhash not found error by refreshing blockhash and re-signing\n            if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Blockhash not found')) && wallet && originalTransaction && attempt < maxRetries) {\n                console.log(\"\\uD83D\\uDD04 Blockhash expired, getting fresh blockhash and re-signing...\");\n                try {\n                    // Get fresh blockhash\n                    const { blockhash } = await connection.getLatestBlockhash('finalized');\n                    originalTransaction.recentBlockhash = blockhash;\n                    // Re-sign the transaction with fresh blockhash\n                    currentSignedTx = await wallet.signTransaction(originalTransaction);\n                    console.log(\"✅ Transaction re-signed with fresh blockhash\");\n                    continue;\n                } catch (resignError) {\n                    console.error(\"❌ Failed to re-sign transaction:\", resignError);\n                // Fall through to normal retry logic\n                }\n            }\n            // Don't retry on last attempt\n            if (attempt >= maxRetries) {\n                console.log(\"\\uD83D\\uDEAB Max retries reached, throwing error\");\n                break;\n            }\n            // Wait before retrying\n            console.log(\"⏳ Waiting \".concat(retryDelay, \"ms before retry...\"));\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            retryDelay *= 1.5; // Exponential backoff\n        }\n    }\n    throw lastError;\n};\n// Enhanced transaction status checking with detailed error information\nconst checkTransactionSuccess = async function(connection, signature) {\n    let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5, retryDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n    for(let i = 0; i < maxAttempts; i++){\n        try {\n            const status = await connection.getSignatureStatus(signature);\n            if (status === null || status === void 0 ? void 0 : status.value) {\n                const confirmationStatus = status.value.confirmationStatus;\n                if (confirmationStatus === 'confirmed' || confirmationStatus === 'finalized') {\n                    if (status.value.err) {\n                        return {\n                            success: false,\n                            error: \"Transaction failed: \".concat(JSON.stringify(status.value.err)),\n                            confirmationStatus\n                        };\n                    }\n                    return {\n                        success: true,\n                        confirmationStatus\n                    };\n                }\n                // Transaction is still processing\n                if (i < maxAttempts - 1) {\n                    console.log(\"Transaction \".concat(signature, \" still processing, attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                    continue;\n                }\n            }\n            // No status available yet\n            if (i < maxAttempts - 1) {\n                console.log(\"No status available for transaction \".concat(signature, \", attempt \").concat(i + 1, \"/\").concat(maxAttempts));\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n            }\n        } catch (error) {\n            console.log(\"Attempt \".concat(i + 1, \" to check transaction status failed:\"), error);\n            if (i === maxAttempts - 1) {\n                return {\n                    success: false,\n                    error: \"Failed to check transaction status: \".concat(error)\n                };\n            }\n            await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n        }\n    }\n    return {\n        success: false,\n        error: 'Transaction status check timed out'\n    };\n};\n// Enhanced connection health check\nconst checkConnectionHealth = async (connection)=>{\n    try {\n        const startTime = Date.now();\n        const slot = await connection.getSlot();\n        const responseTime = Date.now() - startTime;\n        if (responseTime > 10000) {\n            return {\n                healthy: false,\n                error: 'Connection response time too slow'\n            };\n        }\n        if (typeof slot !== 'number' || slot <= 0) {\n            return {\n                healthy: false,\n                error: 'Invalid slot response from connection'\n            };\n        }\n        return {\n            healthy: true\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: \"Connection health check failed: \".concat(error)\n        };\n    }\n};\n// Enhanced wallet validation\nconst validateWalletConnection = (wallet)=>{\n    if (!wallet) {\n        return {\n            valid: false,\n            error: 'Wallet not connected. Please connect your wallet and try again.'\n        };\n    }\n    if (typeof wallet.signTransaction !== 'function') {\n        return {\n            valid: false,\n            error: 'Wallet does not support transaction signing. Please use a compatible wallet.'\n        };\n    }\n    if (!wallet.address) {\n        return {\n            valid: false,\n            error: 'Wallet address not available. Please reconnect your wallet.'\n        };\n    }\n    // Check if wallet address is valid Solana address\n    try {\n        new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n    } catch (error) {\n        return {\n            valid: false,\n            error: 'Invalid wallet address format.'\n        };\n    }\n    return {\n        valid: true\n    };\n};\n/**\n * Create and sign escrow buy transaction\n */ async function createEscrowBuyTransaction(escrowTxData, wallet) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // Validate escrow transaction data\n        if (!escrowTxData) {\n            throw new EscrowError(\"Escrow transaction data is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.escrowId) {\n            throw new EscrowError(\"Escrow ID is required\", \"INVALID_DATA\");\n        }\n        if (!escrowTxData.solAmount || Number(escrowTxData.solAmount) <= 0) {\n            throw new EscrowError(\"Valid SOL amount is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if there's already a successful transaction for this escrow on the blockchain\n        try {\n            console.log('🔍 Checking for existing transactions for escrow:', escrowTxData.escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n            // Calculate the purchase PDA to check if it already exists\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account already exists\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (purchaseAccount) {\n                console.log('✅ Purchase account already exists, escrow transaction was successful');\n                // Try to find the transaction signature in recent history\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"existing-\".concat(escrowTxData.escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing purchase account:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        const { escrowId, solAmount, perkTokenAmount, purchasePda, vaultPda, purchaseTokenMint, perkTokenMint, buyerWallet } = escrowTxData;\n        console.log('Creating escrow buy transaction:', escrowTxData);\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(escrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(escrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        // Check buyer's native SOL balance first\n        const buyerBalance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet));\n        console.log(\"Buyer native SOL balance: \".concat(buyerBalance / **********, \" SOL (\").concat(buyerBalance, \" lamports)\"));\n        console.log(\"Required amount: \".concat(Number(solAmount) / **********, \" SOL (\").concat(solAmount, \" lamports)\"));\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const solAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(solAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(perkTokenAmount);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        const purchasePdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchasePda);\n        const vaultPdaPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(vaultPda);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Check if buyer's ATA exists and create if needed\n        const buyerAtaInfo = await connection.getAccountInfo(buyerTokenAccount);\n        let preInstructions = [];\n        // Check if this is wrapped SOL\n        const isWrappedSOL = purchaseTokenMintPubkey.equals(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey('So11111111111111111111111111111111111111112'));\n        if (!buyerAtaInfo) {\n            console.log('Creating ATA for buyer:', buyerTokenAccount.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(buyerPubkey, buyerTokenAccount, buyerPubkey, purchaseTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // If it's wrapped SOL, we need to ensure the ATA has enough wrapped SOL\n        if (isWrappedSOL) {\n            console.log('Handling wrapped SOL transaction');\n            // For wrapped SOL, we always need to fund the account with the exact amount\n            // Plus some extra for rent and fees\n            const requiredAmount = Number(solAmount);\n            const rentExemption = await connection.getMinimumBalanceForRentExemption(165); // Token account size\n            const totalAmountNeeded = requiredAmount + rentExemption;\n            console.log(\"Required wrapped SOL: \".concat(requiredAmount, \" lamports\"));\n            console.log(\"Rent exemption: \".concat(rentExemption, \" lamports\"));\n            console.log(\"Total amount needed: \".concat(totalAmountNeeded, \" lamports\"));\n            // Check if we have enough native SOL\n            if (buyerBalance < totalAmountNeeded + 5000) {\n                throw new Error(\"Insufficient SOL balance. Required: \".concat((totalAmountNeeded + 5000) / **********, \" SOL, Available: \").concat(buyerBalance / **********, \" SOL\"));\n            }\n            // Transfer native SOL to the wrapped SOL account to fund it\n            const transferIx = _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.transfer({\n                fromPubkey: buyerPubkey,\n                toPubkey: buyerTokenAccount,\n                lamports: totalAmountNeeded\n            });\n            preInstructions.push(transferIx);\n            // Sync native instruction to convert the transferred SOL to wrapped SOL\n            const syncIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_7__.createSyncNativeInstruction)(buyerTokenAccount);\n            preInstructions.push(syncIx);\n            console.log('Added instructions to wrap SOL');\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowBuy(escrowIdBN, solAmountBN, perkTokenAmountBN).accounts({\n            signer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: buyerTokenAccount,\n            purchase: purchasePdaPubkey,\n            vault: vaultPdaPubkey,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-buy-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Transaction details:', {\n            escrowId: escrowTxData.escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow buy transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow buy transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowTxData.escrowId, \"-\").concat(wallet.address, \"-buy\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually created...');\n            // Check if the purchase account was actually created (meaning transaction was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowTxData.escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (purchaseAccount) {\n                    console.log('✅ Purchase account exists - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-\".concat(escrowTxData.escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account does not exist - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify purchase account:', checkError);\n            }\n            throw new DuplicateTransactionError('Transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient funds to complete the transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient funds or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow accept transaction (for sellers)\n */ async function createEscrowAcceptTransaction(escrowId, purchaseTokenAmount, perkTokenAmount, purchaseTokenMint, perkTokenMint, sellerWallet, wallet, tradeId) {\n    try {\n        // Enhanced wallet validation\n        const walletValidation = validateWalletConnection(wallet);\n        if (!walletValidation.valid) {\n            throw new EscrowError(walletValidation.error, \"WALLET_ERROR\");\n        }\n        // If tradeId is provided, fetch trade details and calculate amounts\n        let calculatedPurchaseAmount = purchaseTokenAmount;\n        let calculatedPerkAmount = perkTokenAmount;\n        let calculatedPurchaseMint = purchaseTokenMint;\n        let calculatedPerkMint = perkTokenMint;\n        let calculatedEscrowId = escrowId;\n        if (tradeId) {\n            try {\n                var _tradeData_perkDetails, _tradeData_tokenDetails, _tradeData_perkDetails_token, _tradeData_perkDetails1, _tradeData_tokenDetails1, _tradeData_perkDetails_token1, _tradeData_perkDetails2;\n                console.log('🔍 [EscrowAccept] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                const tradeData = tradeResponse.data;\n                console.log('✅ [EscrowAccept] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    escrowTxId: tradeData.escrowTxId,\n                    amount: tradeData.amount,\n                    price: tradeData.price,\n                    perkDetails: tradeData.perkDetails,\n                    perkTokenMint: tradeData.perkTokenMint\n                });\n                // Use trade data to get the numeric escrowId\n                // Priority: 1. tradeData.escrowId, 2. extra field, 3. original escrowId\n                let numericEscrowId = null;\n                // First try the direct escrowId field from trade data\n                if (tradeData.escrowId) {\n                    // Validate that it's numeric before using it\n                    const escrowIdStr = String(tradeData.escrowId);\n                    if (/^\\d+$/.test(escrowIdStr)) {\n                        numericEscrowId = tradeData.escrowId;\n                    } else {\n                        console.warn('⚠️ Trade escrowId is not numeric:', tradeData.escrowId);\n                    }\n                } else {\n                    // Fall back to extra field\n                    try {\n                        if (tradeData.extra) {\n                            const extraData = JSON.parse(tradeData.extra);\n                            if (extraData.escrowId) {\n                                const escrowIdStr = String(extraData.escrowId);\n                                if (/^\\d+$/.test(escrowIdStr)) {\n                                    numericEscrowId = extraData.escrowId;\n                                } else {\n                                    console.warn('⚠️ Extra escrowId is not numeric:', extraData.escrowId);\n                                }\n                            }\n                        }\n                    } catch (e) {\n                        console.log('Could not parse extra data:', e);\n                    }\n                }\n                // Only use numericEscrowId if it's actually numeric, otherwise use original escrowId\n                // Never use escrowTxId as it's a transaction signature, not a numeric ID\n                calculatedEscrowId = numericEscrowId || escrowId;\n                console.log('🔍 [EscrowAccept] EscrowId resolution:', {\n                    originalEscrowId: escrowId,\n                    tradeDataEscrowId: tradeData.escrowId,\n                    numericEscrowId,\n                    finalCalculatedEscrowId: calculatedEscrowId\n                });\n                // Calculate SOL amount in lamports\n                const solAmount = tradeData.amount || 0;\n                calculatedPurchaseAmount = (solAmount * 1e9).toString();\n                // Calculate perk token amount based on SOL amount and perk price\n                const perkPrice = ((_tradeData_perkDetails = tradeData.perkDetails) === null || _tradeData_perkDetails === void 0 ? void 0 : _tradeData_perkDetails.price) || 0.002;\n                const perkTokensToReceive = solAmount / perkPrice;\n                calculatedPerkAmount = Math.floor(perkTokensToReceive * 1e6).toString();\n                // Set token mints\n                calculatedPurchaseMint = 'So11111111111111111111111111111111111111112'; // SOL mint\n                // Try multiple sources for perk token mint\n                calculatedPerkMint = ((_tradeData_tokenDetails = tradeData.tokenDetails) === null || _tradeData_tokenDetails === void 0 ? void 0 : _tradeData_tokenDetails.tokenAddress) || ((_tradeData_perkDetails1 = tradeData.perkDetails) === null || _tradeData_perkDetails1 === void 0 ? void 0 : (_tradeData_perkDetails_token = _tradeData_perkDetails1.token) === null || _tradeData_perkDetails_token === void 0 ? void 0 : _tradeData_perkDetails_token.tokenAddress) || tradeData.perkTokenMint || perkTokenMint;\n                console.log('🔍 [EscrowAccept] Perk token mint resolution:', {\n                    'tokenDetails.tokenAddress': (_tradeData_tokenDetails1 = tradeData.tokenDetails) === null || _tradeData_tokenDetails1 === void 0 ? void 0 : _tradeData_tokenDetails1.tokenAddress,\n                    'perkDetails.token.tokenAddress': (_tradeData_perkDetails2 = tradeData.perkDetails) === null || _tradeData_perkDetails2 === void 0 ? void 0 : (_tradeData_perkDetails_token1 = _tradeData_perkDetails2.token) === null || _tradeData_perkDetails_token1 === void 0 ? void 0 : _tradeData_perkDetails_token1.tokenAddress,\n                    'tradeData.perkTokenMint': tradeData.perkTokenMint,\n                    'original perkTokenMint': perkTokenMint,\n                    'final calculatedPerkMint': calculatedPerkMint\n                });\n                console.log('🔍 [EscrowAccept] Calculated amounts from trade data:', {\n                    escrowId: calculatedEscrowId,\n                    purchaseAmount: calculatedPurchaseAmount,\n                    perkAmount: calculatedPerkAmount,\n                    purchaseMint: calculatedPurchaseMint,\n                    perkMint: calculatedPerkMint\n                });\n            } catch (error) {\n                console.error('❌ [EscrowAccept] Failed to fetch trade data:', error);\n                throw new EscrowError(\"Failed to fetch trade data: \".concat(error.message), \"TRADE_DATA_ERROR\");\n            }\n        }\n        // Validate escrow accept data\n        if (!calculatedEscrowId) {\n            throw new EscrowError(\"Escrow ID is required but not found. Please ensure the trade has a valid escrowId. TradeId: \".concat(tradeId), \"INVALID_DATA\");\n        }\n        // Validate that escrowId is numeric\n        const escrowIdStr = String(calculatedEscrowId);\n        if (!/^\\d+$/.test(escrowIdStr)) {\n            throw new EscrowError(\"Invalid escrow ID format: \".concat(calculatedEscrowId, \". Expected numeric value.\"), \"INVALID_DATA\");\n        }\n        if (!calculatedPerkAmount || Number(calculatedPerkAmount) <= 0) {\n            throw new EscrowError(\"Valid perk token amount is required\", \"INVALID_DATA\");\n        }\n        if (!calculatedPerkMint) {\n            throw new EscrowError(\"Perk token mint address is required\", \"INVALID_DATA\");\n        }\n        // Check connection health before proceeding\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const connectionHealth = await checkConnectionHealth(connection);\n        if (!connectionHealth.healthy) {\n            throw new EscrowError(\"Network connection issue: \".concat(connectionHealth.error), \"NETWORK_ERROR\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowIdStr, \"-\").concat(wallet.address, \"-accept\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new DuplicateTransactionError(\"Please wait a moment before trying again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        cleanupOldTransactions();\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Escrow accept transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing escrow accept transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been accepted on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been accepted:', escrowIdStr);\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account exists and is already accepted\n            const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n            if (purchaseAccount.accepted) {\n                console.log('✅ Escrow already accepted');\n                // Try to find the transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"accepted-\".concat(escrowIdStr);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check for existing acceptance:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow accept transaction:', {\n            escrowId: escrowIdStr,\n            purchaseTokenAmount: calculatedPurchaseAmount,\n            perkTokenAmount: calculatedPerkAmount,\n            sellerWallet\n        });\n        // Convert values\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowIdStr);\n        const purchaseTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPurchaseAmount);\n        const perkTokenAmountBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(calculatedPerkAmount);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPurchaseMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(calculatedPerkMint);\n        // Calculate PDAs\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        // Get seller's perk token account\n        const sellerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, sellerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens will be stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Check if seller's perk ATA exists and create if needed\n        const sellerPerkAtaInfo = await connection.getAccountInfo(sellerPerkTokenAta);\n        let preInstructions = [];\n        if (!sellerPerkAtaInfo) {\n            console.log('Creating perk ATA for seller:', sellerPerkTokenAta.toString());\n            // Create associated token account instruction\n            const createAtaIx = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_6__.createAssociatedTokenAccountInstruction)(sellerPubkey, sellerPerkTokenAta, sellerPubkey, perkTokenMintPubkey // mint\n            );\n            preInstructions.push(createAtaIx);\n        }\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowAccept(escrowIdBN, purchaseTokenAmountBN, perkTokenAmountBN).accounts({\n            signer: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            perkTokenAta: sellerPerkTokenAta,\n            purchase: purchasePda,\n            vault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).preInstructions(preInstructions).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = sellerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-accept-\".concat(escrowIdStr, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Accept transaction details:', {\n            escrowId: escrowIdStr,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending accept transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow accept transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5, _error_message6, _error_message7, _error_message8, _error_message9, _error_message10, _error_message11;\n        console.error('Escrow accept transaction failed:', error);\n        // Only mark as failed if it's not an \"already processed\" error\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // If it's already an EscrowError, re-throw it\n        if (error instanceof EscrowError) {\n            throw error;\n        }\n        // Handle specific error types with enhanced error messages\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-accept\");\n            console.log('🔍 Transaction already processed error - checking if escrow was actually accepted...');\n            // Check if the purchase account was actually accepted (meaning transaction was successful)\n            try {\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.account.purchase.fetch(purchasePda);\n                if (purchaseAccount.accepted) {\n                    console.log('✅ Purchase account is accepted - transaction was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-accept-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful accept transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account is not accepted - transaction actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify accept status:', checkError);\n            }\n            throw new DuplicateTransactionError('Accept transaction already processed. Please refresh the page and try again.');\n        }\n        // Handle specific Solana/blockchain errors\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new EscrowError('Token account not initialized. Please ensure you have the required tokens.', 'ACCOUNT_NOT_INITIALIZED');\n        }\n        if (((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) || ((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('insufficient funds'))) {\n            throw new InsufficientFundsError('Insufficient perk tokens to accept the escrow transaction.');\n        }\n        if (((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('blockhash not found')) || ((_error_message6 = error.message) === null || _error_message6 === void 0 ? void 0 : _error_message6.includes('Transaction expired'))) {\n            throw new TransactionError('Transaction expired. Please try again with a fresh transaction.');\n        }\n        if (((_error_message7 = error.message) === null || _error_message7 === void 0 ? void 0 : _error_message7.includes('User rejected')) || ((_error_message8 = error.message) === null || _error_message8 === void 0 ? void 0 : _error_message8.includes('user rejected')) || error.code === 4001) {\n            throw new EscrowError('Transaction was rejected by user', 'USER_REJECTED');\n        }\n        if (((_error_message9 = error.message) === null || _error_message9 === void 0 ? void 0 : _error_message9.includes('Network Error')) || ((_error_message10 = error.message) === null || _error_message10 === void 0 ? void 0 : _error_message10.includes('Failed to fetch'))) {\n            throw new EscrowError('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR');\n        }\n        if ((_error_message11 = error.message) === null || _error_message11 === void 0 ? void 0 : _error_message11.includes('Simulation failed')) {\n            throw new TransactionError('Transaction simulation failed. This may be due to insufficient perk tokens or invalid transaction parameters.');\n        }\n        // Handle RPC errors\n        if (error.code && typeof error.code === 'number') {\n            if (error.code === -32002) {\n                throw new EscrowError('RPC request failed. Please try again.', 'RPC_ERROR');\n            }\n            if (error.code === -32003) {\n                throw new DuplicateTransactionError('Transaction already processed.');\n            }\n        }\n        // Generic blockchain error\n        if (error.name === 'SendTransactionError' || error.name === 'TransactionError') {\n            throw new BlockchainError(\"Blockchain transaction failed: \".concat(error.message), error);\n        }\n        // Fallback for unknown errors\n        throw new EscrowError(error.message || 'An unexpected error occurred during the escrow accept transaction', 'UNKNOWN_ERROR');\n    }\n}\n/**\n * Create and sign escrow sell transaction (for sellers)\n */ async function createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId) {\n    try {\n        // Validate wallet\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Step 1: Fetch trade data first if tradeId is provided\n        let tradeData = null;\n        if (tradeId) {\n            try {\n                console.log('🔍 [EscrowSell] Fetching trade data for tradeId:', tradeId);\n                const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                const tradeResponse = await getTradeDetails(Number(tradeId));\n                tradeData = tradeResponse.data;\n                console.log('✅ [EscrowSell] Trade data fetched successfully:', {\n                    tradeId: tradeData.id,\n                    status: tradeData.status,\n                    escrowId: tradeData.escrowId,\n                    perkId: tradeData.perkId,\n                    price: tradeData.price\n                });\n                // Validate that the trade data matches the escrow parameters\n                if (tradeData.escrowId && tradeData.escrowId !== escrowId) {\n                    console.warn('⚠️ [EscrowSell] Trade escrowId mismatch:', {\n                        provided: escrowId,\n                        fromTrade: tradeData.escrowId\n                    });\n                }\n                // Ensure trade is in correct status for release\n                if (tradeData.status !== 'escrowed') {\n                    throw new Error(\"Trade is not in escrowed status. Current status: \".concat(tradeData.status));\n                }\n                // Additional validation: check if seller wallet matches\n                if (tradeData.to && tradeData.to !== sellerWallet) {\n                    console.warn('⚠️ [EscrowSell] Seller wallet mismatch:', {\n                        provided: sellerWallet,\n                        fromTrade: tradeData.to\n                    });\n                }\n                // Additional validation: check if buyer wallet matches\n                if (tradeData.from && tradeData.from !== buyerWallet) {\n                    console.warn('⚠️ [EscrowSell] Buyer wallet mismatch:', {\n                        provided: buyerWallet,\n                        fromTrade: tradeData.from\n                    });\n                }\n            } catch (error) {\n                console.error('❌ [EscrowSell] Failed to fetch trade data:', error);\n                throw new Error(\"Failed to fetch trade data: \".concat(error.message));\n            }\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to release again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Clean up old transactions periodically\n        if (Math.random() < 0.1) {\n            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n            for (const [key, state] of transactionStates.entries()){\n                if (state.timestamp < fiveMinutesAgo) {\n                    transactionStates.delete(key);\n                }\n            }\n            // Also cleanup last attempts\n            for (const [key, timestamp] of lastTransactionAttempts.entries()){\n                if (timestamp < fiveMinutesAgo) {\n                    lastTransactionAttempts.delete(key);\n                }\n            }\n        }\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Release transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing release transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been released on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been released:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if released, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already released');\n                // Try to find the release transaction signature in recent history\n                const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"released-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check release status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow sell transaction:', {\n            escrowId,\n            sellerWallet,\n            buyerWallet,\n            tradeData: tradeData ? {\n                id: tradeData.id,\n                status: tradeData.status,\n                perkId: tradeData.perkId,\n                price: tradeData.price,\n                escrowTxId: tradeData.escrowTxId\n            } : 'No trade data provided'\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(sellerWallet);\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        const perkTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(perkTokenMint);\n        // Get token accounts\n        const sellerPurchaseTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, sellerPubkey);\n        const buyerPerkTokenAta = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(perkTokenMintPubkey, buyerPubkey);\n        // Calculate the perk vault PDA (where seller's perk tokens are stored)\n        const [perkVaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            perkTokenMintPubkey.toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        console.log('🔍 Account debugging:', {\n            sellerWallet,\n            buyerWallet,\n            buyerPerkTokenAta: buyerPerkTokenAta.toString(),\n            sellerPurchaseTokenAta: sellerPurchaseTokenAta.toString(),\n            perkVaultPda: perkVaultPda.toString(),\n            whoOwnsWhat: {\n                purchaseTokenGoesTo: 'seller',\n                perkTokenGoesTo: 'buyer' // Should be buyer\n            }\n        });\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowSell(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            seller: sellerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            perkTokenMint: perkTokenMintPubkey,\n            purchaseTokenAta: sellerPurchaseTokenAta,\n            perkTokenDestinationAta: buyerPerkTokenAta,\n            purchase: purchasePda,\n            vault: vaultPda,\n            perkVault: perkVaultPda,\n            associatedTokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey; // Buyer pays the fees and is the signer\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-sell-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Sell transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending sell transaction...');\n        // Send the transaction with retry logic (with blockhash refresh capability)\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500, wallet, tx);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow sell transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow sell transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-sell\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Release transaction already processed error - checking if escrow was actually released...');\n            // Check if the purchase account was actually closed (meaning release was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - release was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const sellerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(sellerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-release-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful release transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - release actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify release status:', checkError);\n            }\n            throw new Error('Release transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the release transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Release transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Create and sign escrow refund transaction (for buyers)\n */ async function createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet) {\n    try {\n        // Validate wallet - same pattern as in helpers.ts\n        if (!wallet || typeof wallet.signTransaction !== \"function\" || !wallet.address) {\n            throw new Error(\"Wallet not connected or signTransaction missing\");\n        }\n        // Create unique transaction key to prevent duplicates\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Check for rapid duplicate attempts (within 3 seconds)\n        const lastAttempt = lastTransactionAttempts.get(transactionKey);\n        const now = Date.now();\n        if (lastAttempt && now - lastAttempt < 3000) {\n            throw new Error(\"Please wait a moment before trying to refund again. Transaction attempt too soon.\");\n        }\n        lastTransactionAttempts.set(transactionKey, now);\n        // Check if transaction is already in progress or completed\n        const existingState = transactionStates.get(transactionKey);\n        if (existingState) {\n            if (existingState.status === 'pending') {\n                throw new Error(\"Refund transaction already in progress. Please wait for the current transaction to complete.\");\n            }\n            if (existingState.status === 'completed' && existingState.txId) {\n                // If completed recently (within 5 minutes), return existing txId\n                const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                if (existingState.timestamp > fiveMinutesAgo) {\n                    console.log(\"Returning existing refund transaction ID:\", existingState.txId);\n                    return existingState.txId;\n                }\n            }\n        }\n        // Check if the escrow has already been refunded on the blockchain\n        try {\n            console.log('🔍 Checking if escrow has already been refunded:', escrowId);\n            const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n            const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n            const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                Buffer.from(\"purchase\"),\n                escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n            ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n            // Check if the purchase account still exists (if refunded, it should be closed)\n            const purchaseAccount = await connection.getAccountInfo(purchasePda);\n            if (!purchaseAccount) {\n                console.log('✅ Purchase account no longer exists, escrow was already refunded');\n                // Try to find the refund transaction signature in recent history\n                const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                    limit: 20\n                });\n                for (const sig of signatures){\n                    // Check if this signature is recent (within last 10 minutes)\n                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;\n                    if (sig.blockTime && sig.blockTime * 1000 > tenMinutesAgo) {\n                        // Mark as completed in our state\n                        transactionStates.set(transactionKey, {\n                            status: 'completed',\n                            timestamp: Date.now(),\n                            txId: sig.signature\n                        });\n                        return sig.signature;\n                    }\n                }\n                // If we can't find the exact signature, create a placeholder\n                const placeholderTxId = \"refunded-\".concat(escrowId);\n                transactionStates.set(transactionKey, {\n                    status: 'completed',\n                    timestamp: Date.now(),\n                    txId: placeholderTxId\n                });\n                return placeholderTxId;\n            }\n        } catch (checkError) {\n            console.log('Could not check refund status:', checkError);\n        // Continue with normal flow if check fails\n        }\n        // Mark transaction as pending\n        transactionStates.set(transactionKey, {\n            status: 'pending',\n            timestamp: Date.now()\n        });\n        console.log('Creating escrow refund transaction:', {\n            escrowId,\n            buyerWallet\n        });\n        // Calculate PDAs\n        const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n        const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            Buffer.from(\"purchase\"),\n            escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n        ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n        const [vaultPda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n            purchasePda.toBuffer(),\n            _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID.toBuffer(),\n            new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint).toBuffer()\n        ], _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.ASSOCIATED_TOKEN_PROGRAM_ID);\n        // Create public keys\n        const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(buyerWallet);\n        const purchaseTokenMintPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(purchaseTokenMint);\n        // Get buyer's token account\n        const buyerTokenAccount = (0,_solana_spl_token__WEBPACK_IMPORTED_MODULE_5__.getAssociatedTokenAddressSync)(purchaseTokenMintPubkey, buyerPubkey);\n        // Create the transaction\n        const tx = await _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.methods.escrowRefund(escrowIdBN).accounts({\n            buyer: buyerPubkey,\n            purchaseTokenMint: purchaseTokenMintPubkey,\n            buyerTokenAta: buyerTokenAccount,\n            purchase: purchasePda,\n            vault: vaultPda,\n            tokenProgram: _solana_spl_token__WEBPACK_IMPORTED_MODULE_8__.TOKEN_PROGRAM_ID,\n            systemProgram: _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.SystemProgram.programId\n        }).transaction();\n        // Get fresh blockhash to ensure transaction uniqueness\n        const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');\n        tx.recentBlockhash = blockhash;\n        tx.feePayer = buyerPubkey;\n        // Add a unique memo instruction to prevent duplicate transactions\n        const memoInstruction = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.web3.TransactionInstruction({\n            keys: [],\n            programId: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\"),\n            data: Buffer.from(\"escrow-refund-\".concat(escrowId, \"-\").concat(Date.now()), 'utf-8')\n        });\n        tx.add(memoInstruction);\n        console.log('🔍 Refund transaction details:', {\n            escrowId,\n            blockhash: blockhash.slice(0, 8) + '...',\n            lastValidBlockHeight\n        });\n        // Sign the transaction\n        const signedTx = await wallet.signTransaction(tx);\n        console.log('🔍 Sending refund transaction...');\n        // Send the transaction with retry logic\n        const txId = await sendTransactionWithRetry(connection, signedTx, 2, 1500);\n        console.log('✅ Transaction sent:', txId);\n        // Confirm the transaction with timeout\n        const confirmation = await connection.confirmTransaction({\n            signature: txId,\n            blockhash: tx.recentBlockhash,\n            lastValidBlockHeight: lastValidBlockHeight\n        }, 'confirmed');\n        if (confirmation.value.err) {\n            throw new Error(\"Transaction failed: \".concat(confirmation.value.err));\n        }\n        console.log('✅ Escrow refund transaction confirmed:', txId);\n        // Mark transaction as completed\n        transactionStates.set(transactionKey, {\n            status: 'completed',\n            timestamp: Date.now(),\n            txId\n        });\n        // Small delay to ensure transaction is fully processed\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        return txId;\n    } catch (error) {\n        var _error_message, _error_message1, _error_message2, _error_message3, _error_message4, _error_message5;\n        console.error('Escrow refund transaction failed:', error);\n        const transactionKey = \"\".concat(escrowId, \"-\").concat(wallet.address, \"-refund\");\n        // Only mark as failed if it's not an \"already processed\" error\n        // (which might actually be successful)\n        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('This transaction has already been processed'))) {\n            transactionStates.set(transactionKey, {\n                status: 'failed',\n                timestamp: Date.now()\n            });\n        }\n        // Enhanced error handling for Solana transactions\n        if (error.logs) {\n            console.error('Transaction logs:', error.logs);\n        }\n        // Handle specific duplicate transaction error\n        if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('This transaction has already been processed')) {\n            console.log('🔍 Refund transaction already processed error - checking if escrow was actually refunded...');\n            // Check if the purchase account was actually closed (meaning refund was successful)\n            try {\n                const connection = _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.provider.connection;\n                const escrowIdBN = new _coral_xyz_anchor__WEBPACK_IMPORTED_MODULE_1__.BN(escrowId);\n                const [purchasePda] = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey.findProgramAddressSync([\n                    Buffer.from(\"purchase\"),\n                    escrowIdBN.toArrayLike(Buffer, \"le\", 8)\n                ], _sol_setup__WEBPACK_IMPORTED_MODULE_2__.program.programId);\n                const purchaseAccount = await connection.getAccountInfo(purchasePda);\n                if (!purchaseAccount) {\n                    console.log('✅ Purchase account no longer exists - refund was actually successful!');\n                    // Try to find the transaction signature in recent history\n                    const buyerPubkey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(wallet.address);\n                    const signatures = await connection.getSignaturesForAddress(buyerPubkey, {\n                        limit: 10\n                    });\n                    let foundTxId = null;\n                    for (const sig of signatures){\n                        // Check if this signature is recent (within last 5 minutes)\n                        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;\n                        if (sig.blockTime && sig.blockTime * 1000 > fiveMinutesAgo) {\n                            foundTxId = sig.signature;\n                            break;\n                        }\n                    }\n                    if (!foundTxId) {\n                        foundTxId = \"successful-refund-\".concat(escrowId, \"-\").concat(Date.now());\n                    }\n                    // Mark as completed in our state\n                    transactionStates.set(transactionKey, {\n                        status: 'completed',\n                        timestamp: Date.now(),\n                        txId: foundTxId\n                    });\n                    console.log('🎉 Returning successful refund transaction ID:', foundTxId);\n                    return foundTxId;\n                } else {\n                    console.log('❌ Purchase account still exists - refund actually failed');\n                }\n            } catch (checkError) {\n                console.log('Could not verify refund status:', checkError);\n            }\n            throw new Error('Refund transaction already processed. Please refresh the page and try again.');\n        }\n        if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('AccountNotInitialized')) {\n            throw new Error('Token account not initialized. Please ensure you have the required tokens.');\n        }\n        if ((_error_message3 = error.message) === null || _error_message3 === void 0 ? void 0 : _error_message3.includes('InsufficientFunds')) {\n            throw new Error('Insufficient funds to complete the refund transaction.');\n        }\n        // Handle transaction timeout or blockhash expiry\n        if (((_error_message4 = error.message) === null || _error_message4 === void 0 ? void 0 : _error_message4.includes('blockhash not found')) || ((_error_message5 = error.message) === null || _error_message5 === void 0 ? void 0 : _error_message5.includes('Transaction expired'))) {\n            throw new Error('Refund transaction expired. Please try again with a fresh transaction.');\n        }\n        throw error;\n    }\n}\n/**\n * Initiate a dispute for an escrow transaction\n * Can be called by buyer or seller when there's an issue with the trade\n */ async function initiateEscrowDispute(tradeId, reason, initiatorRole) {\n    try {\n        console.log('Initiating escrow dispute:', {\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        const response = await (0,_axios_requests__WEBPACK_IMPORTED_MODULE_3__.initiateDispute)({\n            tradeId,\n            reason,\n            initiatorRole\n        });\n        if (response.status === 201) {\n            console.log('Dispute initiated successfully:', response.data);\n            return response.data;\n        } else {\n            throw new Error(response.message || 'Failed to initiate dispute');\n        }\n    } catch (error) {\n        console.error('Dispute initiation failed:', error);\n        throw error;\n    }\n}\n/**\n * Check if a trade is eligible for dispute\n * Disputes can only be initiated for escrowed trades within the deadline\n */ function canInitiateDispute(tradeStatus, tradeCreatedAt) {\n    let disputeDeadlineDays = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2, disputeStatus = arguments.length > 3 ? arguments[3] : void 0;\n    // Check if trade is in escrowed status\n    if (tradeStatus !== 'escrowed') {\n        return {\n            canDispute: false,\n            reason: 'Dispute can only be initiated for escrowed trades'\n        };\n    }\n    // Check if dispute already exists\n    if (disputeStatus && disputeStatus !== 'none') {\n        return {\n            canDispute: false,\n            reason: 'A dispute already exists for this trade'\n        };\n    }\n    // Check if dispute deadline has passed\n    const createdDate = new Date(tradeCreatedAt);\n    const deadlineDate = new Date(createdDate);\n    deadlineDate.setDate(deadlineDate.getDate() + disputeDeadlineDays);\n    if (new Date() > deadlineDate) {\n        return {\n            canDispute: false,\n            reason: 'Dispute deadline has passed'\n        };\n    }\n    return {\n        canDispute: true\n    };\n}\n/**\n * Get dispute status information for display\n */ function getDisputeStatusInfo(disputeStatus) {\n    switch(disputeStatus){\n        case 'none':\n            return {\n                label: 'No Dispute',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n        case 'open':\n            return {\n                label: 'Dispute Open',\n                color: 'text-yellow-800',\n                bgColor: 'bg-yellow-100'\n            };\n        case 'assigned':\n            return {\n                label: 'Under Review',\n                color: 'text-blue-800',\n                bgColor: 'bg-blue-100'\n            };\n        case 'resolved':\n            return {\n                label: 'Dispute Resolved',\n                color: 'text-green-800',\n                bgColor: 'bg-green-100'\n            };\n        default:\n            return {\n                label: 'Unknown Status',\n                color: 'text-gray-600',\n                bgColor: 'bg-gray-100'\n            };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/escrow.ts\n"));

/***/ })

});