import React from "react";
import { Users, UserRoundPlus } from "lucide-react";
import { TabContentLoader } from "@/components/ui/TabContentLoader";
import { useTranslation } from '@/hooks/useTranslation';

interface ProfileHeaderProps {
  isOwnProfile: boolean;
  followersCount: number;
  followingCount: number;
  isFollowing: boolean;
  followLoading: boolean;
  allowMessages: boolean;
  allowMessagesLoading: boolean;
  onFollow: () => void;
  onUnfollow: () => void;
  onAllowMessagesToggle: () => void;
  onMessage?: () => void; // Added prop
}

const OrangeSpinner = () => (
  <span className="inline-block w-6 h-6 align-middle">
    <svg className="animate-spin" viewBox="0 0 24 24">
      <circle
        className="opacity-25"
        cx="12" cy="12" r="10"
        stroke="#FF6600" strokeWidth="4" fill="none"
      />
      <path
        className="opacity-75"
        fill="#FF6600"
        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
      />
    </svg>
  </span>
);

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  isOwnProfile,
  followersCount,
  followingCount,
  isFollowing,
  followLoading,
  allowMessages,
  allowMessagesLoading,
  onFollow,
  onUnfollow,
  onAllowMessagesToggle,
  onMessage,
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col justify-center items-center w-full p-6 bg-white shadow-md rounded-lg border-[#FF6600] border-1 ">
      <p className="font-bold text-3xl text-[#FF6600] text-center">
        {t('profileHeader.profileDetails')}
      </p>
      <div className="flex justify-between items-center w-full sm:mt-4 mt-2">
        <div className="flex items-start sm:gap-4 gap-2">
          {/* Avatar */}
          <div className="flex justify-between items-center w-full sm:gap-3 gap-1">
            <div className="sm:w-30 sm:h-30 w-20 h-20 rounded-full bg-gradient-to-br from-orange-400 to-red-600 flex items-center justify-center text-white font-bold sm:text-2xl text-lg overflow-hidden">
              <img
                src="/api/placeholder/80/80"
                alt={t('profileHeader.profileAvatar')}
                className="w-full h-full object-cover"
                aria-label={t('profileHeader.profileAvatar')}
              />
            </div>
            <div className="flex flex-col justify-center items-start ">
              <p className="sm:text-base text-sm font-semibold text-[#FF6600] truncate">The horde</p>
              <p className="sm:text-base text-sm font-semibold text-[#FF6600] truncate">@Ray</p>
              <div className="flex items-center gap-2 text-[#FF6600] sm:text-xs text-[12px] truncate text-center">
                <Users className="w-4 h-4 sm:w-3 sm:h-3" />
                <span>{followersCount} {t('profileHeader.followers')}</span>
              </div>
              <div className="flex items-center gap-2 text-[#FF6600] sm:text-xs text-[12px] truncate text-center">
                <UserRoundPlus className="w-4 h-4 sm:w-3 sm:h-3" />
                <span>{followingCount} {t('profileHeader.following')}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:items-end  gap-2 ">
          {/* Only show follow/unfollow and message if not viewing own profile */}
          {!isOwnProfile && (
            isFollowing ? (
              <button
                className="px-2 py-1 text-xs sm:text-base h-8 sm:min-w-[150px] min-w-[80px] sm:px-4 sm:py-2 text-[#FF6600] rounded-full border-2 font-semibold transition-colors duration-200 border-[#FF6600] hover:bg-[#FF6600] hover:text-white flex items-center justify-center"
                onClick={onUnfollow}
                disabled={followLoading}
                aria-label={t('profileHeader.unfollowUser')}
              >
                {followLoading ? (
                  <OrangeSpinner />
                ) : (
                  t('profileHeader.unfollow')
                )}
              </button>
            ) : (
              <button
                className="px-2 py-1 text-xs sm:text-base h-8 sm:min-w-[150px] min-w-[80px] sm:px-4 sm:py-2 text-[#FF6600] rounded-full border-2 font-semibold transition-colors duration-200 border-[#FF6600] hover:bg-[#FF6600] hover:text-white flex items-center justify-center"
                onClick={onFollow}
                disabled={followLoading}
                aria-label={t('profileHeader.followUser')}
              >
                {followLoading ? (
                  <OrangeSpinner />
                ) : (
                  t('profileHeader.follow')
                )}
              </button>
            )
          )}
          {/* Only show message button if not viewing own profile and allowMessages is true */}
          {!isOwnProfile && allowMessages && (
            <button className="px-2 py-1 text-xs sm:text-base h-8 sm:min-w-[150px] min-w-[80px] sm:px-4 sm:py-2 text-[#FF6600] rounded-full border-2 font-semibold transition-colors duration-200 border-[#FF6600] hover:bg-[#FF6600] hover:text-white flex items-center justify-center" aria-label={t('profileHeader.sendMessage')} onClick={onMessage}>
              {t('profileHeader.message')}
            </button>
          )}
          {/* If not own profile and allowMessages is false, show disabled message button with tooltip */}
          {!isOwnProfile && !allowMessages && (
            <button className="px-2 py-1 text-xs sm:text-base h-8 sm:min-w-[150px] min-w-[80px] sm:px-4 sm:py-2 border-[#FF6600] border-2 text-[#FF6600] rounded-full opacity-50 cursor-not-allowed flex items-center justify-center" disabled title={t('profileHeader.notAcceptingMessages')} aria-label={t('profileHeader.sendMessageDisabled')}>
              {t('profileHeader.message')}
            </button>
          )}
          {/* Show messaging privacy toggle if viewing own profile */}
          {isOwnProfile && (
            <div className="flex items-center gap-2 mt-2 w-full  justify-end">
              <button
                id="allowMessagesToggle"
                type="button"
                onClick={onAllowMessagesToggle}
                disabled={allowMessagesLoading}
                className={`px-2 py-1 text-xs sm:text-base h-8 sm:min-w-[150px] min-w-[80px] sm:px-4 sm:py-2 text-[#FF6600] rounded-full border-2 font-semibold transition-colors duration-200 border-[#FF6600] hover:bg-[#FF6600] hover:text-white flex items-center justify-center ${allowMessages ? 'bg-[#FF6600] text-white' : ''} ${allowMessagesLoading ? 'opacity-50 cursor-not-allowed' : ''} mt-2`}
                aria-pressed={allowMessages}
                aria-label={t('profileHeader.allowOthersToMessageMe')}
              >
                {allowMessagesLoading ? (
                  <TabContentLoader />
                ) : allowMessages ? t('profileHeader.rejectMessage') : t('profileHeader.acceptMessage')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader; 