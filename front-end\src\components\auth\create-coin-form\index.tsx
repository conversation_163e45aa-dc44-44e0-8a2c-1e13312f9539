'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { X, Loader2 } from 'lucide-react';

import { useCreateCoinForm } from '../../../hooks/useCreateCoinForm';
import { modalVariants } from './constants';
import { useTranslation } from '@/hooks/useTranslation';

// Dynamically import heavy form components
const FormFields = dynamic(() => import('./FormFields'), {
  loading: () => <div className="animate-pulse h-8 bg-gray-200 rounded"></div>,
});

const ImageUpload = dynamic(() => import('./ImageUpload'), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded"></div>,
});

const SubmitButton = dynamic(() => import('./SubmitButton'), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded"></div>,
});

const SuccessState = dynamic(() => import('./SuccessState'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded"></div>,
});

const CreateCoinForm: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const {
    formData,
    errors,
    isSubmitting,
    submitted,
    previewUrl,
    showOptions,
    handleChange,
    handleBlur,
    handleFileChange,
    handleSubmit,
    removeImage,
    toggleOptions,
    isConnected,
  } = useCreateCoinForm({ onClose });

  // Show loading state if wallet is not connected
  if (!isConnected) {
    return (
      <motion.div
        className="w-full max-w-[697px] mx-auto bg-white rounded-[24px] md:rounded-[48px] p-8 shadow-2xl"
        variants={modalVariants as Variants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <motion.button
          className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none p-2 rounded-full hover:bg-gray-100 transition-colors z-10"
          onClick={onClose}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <X size={24} />
        </motion.button>

        <div className="flex items-center justify-center p-8">
          <Loader2 className="animate-spin mr-2" />
          Please connect your Solana wallet...
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="w-full max-w-[697px] mx-auto bg-white rounded-[24px] md:rounded-[48px] p-4 md:pl-[110px] md:pr-[110px] md:pt-8 md:pb-[97px] max-h-[90vh] overflow-auto relative shadow-2xl"
      variants={modalVariants as Variants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <motion.button
        className="absolute top-4 right-4 md:top-8 md:right-8 text-gray-600 hover:text-gray-900 focus:outline-none p-2 rounded-full hover:bg-gray-100 transition-colors z-10"
        onClick={onClose}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <X size={24} />
      </motion.button>

      <motion.div className="text-center justify-start text-gray-900 text-3xl font-semibold font-['IBM_Plex_Sans'] leading-[48px] mb-6">
        Create Coin
      </motion.div>

      <AnimatePresence mode="wait">
        {submitted ? (
          <SuccessState />
        ) : (
          <motion.form
            onSubmit={handleSubmit}
            className="space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <FormFields
              formData={formData}
              errors={errors}
              isSubmitting={isSubmitting}
              showOptions={showOptions}
              onToggleOptions={toggleOptions}
              onChange={handleChange}
              onBlur={handleBlur}
            />

            <ImageUpload
              picture={formData.picture}
              previewUrl={previewUrl}
              error={errors.picture}
              isSubmitting={isSubmitting}
              onFileChange={handleFileChange}
              onRemoveImage={removeImage}
            />

            <SubmitButton isSubmitting={isSubmitting} />
          </motion.form>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CreateCoinForm;
