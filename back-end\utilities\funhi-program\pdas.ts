import { PublicKey } from "@solana/web3.js";
import { program } from "./setup";

export const [global] = PublicKey.findProgramAddressSync(
  [Buffer.from("global")],
  program.programId,
);

export const [lastWithdraw] = PublicKey.findProgramAddressSync(
  [Buffer.from("last_withdraw")],
  program.programId,
);

export const getBondingCurvePda = (mint: PublicKey): PublicKey => {
  const [bondingCurve] = PublicKey.findProgramAddressSync(
    [Buffer.from("bonding_curve"), mint.toBuffer()],
    program.programId,
  );
  return bondingCurve;
};

export const getCreatorVault = (
  mint: <PERSON><PERSON><PERSON>,
  creator: <PERSON><PERSON><PERSON>,
): PublicKey => {
  const [creatorVault] = PublicKey.findProgramAddressSync(
    [Buffer.from("creator_vault"), creator.toBuffer(), mint.toBuffer()],
    program.programId,
  );
  return creatorVault;
};
