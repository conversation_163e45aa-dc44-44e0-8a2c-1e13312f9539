import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import dynamic from 'next/dynamic';

import { useWallet } from '@/hooks/useWallet';
import { useAppContext } from '@/contexts/AppContext';
import { executeWithdrawUnlocked, getTokenDetails } from '@/utils/helpers';
import { showErrorToast, showSuccessToast } from '@/utils/errorHandling';
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import { useTranslation } from '@/hooks/useTranslation';

const AnimatedWrapper = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.AnimatedWrapper })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const AirDrops = dynamic(() => import('../air-drops'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const Burns = dynamic(() => import('../burns'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const TradeChart = dynamic(() => import('../chart'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const ExchangeForm = dynamic(() => import('../exchange-form'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const Perks = dynamic(() => import('../perks'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
});

const Tabs = dynamic(() => import('../tabs'), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>,
});

const TokenHeader = dynamic(() => import('../token-header'), {
  loading: () => <div className="animate-pulse h-24 bg-gray-200 rounded-lg"></div>,
});

const TokenStats = dynamic(() => import('../token-stats'), {
  loading: () => <div className="animate-pulse h-32 bg-gray-200 rounded-lg"></div>,
});

const CreatorWithdrawButton = dynamic(() => import('../creator-withdraw-button'), {
  loading: () => <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>,
});

const CreateCoinPage = dynamic(() => import('../create-coin'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

type PerkType = {
  time: string | undefined;
  user: any;
  perkId: number;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isVerified: boolean;
};

type MyCoinProps = {
  myPerks: PerkType[];
  myTokens: any;
  myAirDrop: any;
};

const MyCoin: React.FC<MyCoinProps> = ({ myPerks, myTokens, myAirDrop }) => {
  const { t } = useTranslation();
  const [openCreateCoinModal, setOpenCreateCoinModal] = useState(false);
  const { openModal, closeModal } = useGlobalModal();
  const tabData = [
    {
      name: 'Perks',
      icon: (
        <Image
          src="/icons/tab-icons/perk.png"
          alt="Perks"
          width={32}
          height={32}
          className="w-8 h-8"
        />
      ),
    },
    {
      name: 'Air drops',
      icon: (
        <Image
          src="/icons/tab-icons/airdrop.png"
          alt="Air Drops"
          width={32}
          height={32}
          className="w-8 h-8"
        />
      ),
    },
    {
      name: 'Burns',
      icon: (
        <Image
          src="/icons/tab-icons/burn.png"
          alt="Burns"
          width={32}
          height={32}
          className="w-8 h-8"
        />
      ),
    },
  ];
  const [selectedTab, setSelectedTab] = useState('Perks');
  const { solanaWallet, getWalletAddress } = useWallet();
  const currentUserWallet = getWalletAddress();
  const state = useAppContext();

  const [price, setPrice] = useState<number | 0.0>(0.0);
  const [marketcap, setMarketcap] = useState<number | 0.0>(0.0);

  useEffect(() => {
    const fetchDetails = async () => {
      if (!myTokens || !myTokens[0] || !myTokens[0].tokenAddress) {
        console.warn('[MyCoin] Skipping token detail fetch due to missing tokenAddress as no token is created');
        return;
      }

      try {
        const { tokenPrice, tokenMarketcap } = await getTokenDetails(myTokens[0].tokenAddress);
        setPrice(tokenPrice);
        setMarketcap(tokenMarketcap);
      } catch (err) {
        console.error('[MyCoin] Error fetching token details:', err);
      }
    };

    fetchDetails();
  }, [myTokens]);

  const onTabChange = (tabName: string) => {
    setSelectedTab(tabName);
  };

  const handleWithdraw = async () => {
    console.log('Withdraw button clicked - wallet will show available amount');

    if (solanaWallet) {
      const mint = myTokens[0].tokenAddress;
      const creator = myTokens[0].creatorWallet;
      const txId = await executeWithdrawUnlocked(solanaWallet, mint, creator)
      if (txId) {
        showSuccessToast("Successfully withdrawn!");
      } else {
        showErrorToast("Not availabe!");
      }
      console.log(txId);
    }

  };

  const getActiveTabContent = (name: string) => {
    switch (name) {
      case 'Perks':
        return <Perks myPerks={myPerks} />;
      case 'Air drops':
        return <AirDrops myAirDrop={myAirDrop} myTokens={myTokens} />;
      case 'Burns':
        return <Burns myBurn={myAirDrop} myTokens={myTokens} />;
      default:
        return <AirDrops myAirDrop={myAirDrop} myTokens={myTokens} />;
    }
  };

  const handleCreateTokenClick = () => {
    handleOpenModal("create-coin", <CreateCoinPage onClose={() => closeModal("create-coin")} />);
  };

  const handleOpenModal = (modalId: string, component: React.ReactNode) => {
    openModal({
      id: modalId,
      component: component,
      onClose: () => {
        closeModal(modalId);
      },
      closeOnBackdropClick: true,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-[#000000c7] backdrop-blur-sm',
      modalClassName: 'flex flex-col items-center justify-center p-4',
      disableScroll: true
    });
  };

  if (!myTokens || myTokens.length === 0) {
    return (
      <div className="w-full max-w-[1745px] m-auto">
        {/* Enhanced Empty State */}
        <div className="text-center py-16">
          <div className="mb-6">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-orange-100 to-orange-200 rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-orange-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </div>
          </div>
          <h2 className="font-['IBM_Plex_Sans'] font-semibold text-2xl text-[#090A0B] mb-4">
            {t('table.noCoinsCreated')}
          </h2>
          <p className="text-gray-600 text-lg mb-8 max-w-md mx-auto">
            {t('table.noCoinsCreatedDescription')}
          </p>
          <button
            className="bg-[#FF6600] text-white px-8 py-3 rounded-lg font-semibold hover:bg-[#e55a00] transition-colors"
            onClick={handleCreateTokenClick}
          >
            {t('table.createYourFirstToken')}
          </button>
        </div>

        {/* Show tabs section even when no tokens */}
        <div className="mt-16">
          <div className="w-full flex justify-center mb-8">
            <Tabs
              tabData={tabData}
              onTabChange={onTabChange}
              selectedTab={selectedTab}
              variant="wide" // Use wide variant for better appearance
            />
          </div>
          <div className="mt-8">{getActiveTabContent(selectedTab)}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-[1745px] m-auto">
      {state.state.isLoggedIn && (
        <AnimatedWrapper delay={0.15}>
          <div className="flex justify-end mb-4">
            <CreatorWithdrawButton
              tokenData={myTokens[0]}
              currentUserWallet={currentUserWallet || ''}
              onWithdraw={handleWithdraw}
            />
          </div>
        </AnimatedWrapper>
      )}
      <div className="flex justify-between mb-8 flex-col lg:flex-row">
        <TokenHeader
          name={myTokens ? myTokens[0].name : 'ANAS TOKEN'}
          marketcap={marketcap ? marketcap.toFixed(2) + '$' : '0$'}
          price={price ? price.toFixed(6) + '$' : '0.00$'}
          changes={{
            h24: '2%',
            d7: '-2%',
            m1: '2%',
            alltime: '22%',
          }}
          username={myTokens ? myTokens[0].user.username : 'ANAS TOKEN'}
          handle={myTokens ? myTokens[0].user.username : 'ANAS TOKEN'}
        />

        {/* Enhanced Manage Coin Button */}
      </div>

      <div className="mt-10 flex flex-col lg:flex-row gap-6 2xl:gap-18 w-full">
        <div className="flex-[880] min-w-[300px] lg:basis-[880px] max-w-full">
          <TokenStats
            creator={myTokens ? myTokens[0].user.username : 'ANAS TOKEN'}
            symbols={[myTokens[0].ticker + '/SOL']}
            stats={{
              usd: price,
              min24h: 35351,
              max24h: 38028,
              volume24h: 37453,
              return24h: 1.38,
            }}
            className="mb-8"
          />
          <TradeChart />
        </div>

        <div className="mt-6 lg:mt-52 xl:mt-32 flex-[792] min-w-[250px] lg:basis-[792px] max-w-full">
          <ExchangeForm
            fromOptions={[
              {
                id: 1,
                symbol: 'SOL',
                name: 'Solana',
                icon: 'https://upload.wikimedia.org/wikipedia/en/b/b9/Solana_logo.png',
                tokenAddress: '',
                balance: 0,
                creatorWallet: '',
                tokenDetails: [],
              },
            ]}
            toOptions={[
              {
                id: myTokens[0].tokenId,
                symbol: myTokens[0].ticker,
                name: myTokens[0].name,
                tokenAddress: myTokens[0].tokenAddress,
                icon: myTokens[0].image,
                balance: 0,
                creatorWallet: myTokens[0].creatorWallet,
                tokenDetails: myTokens[0],
              },
            ]}
            classname="flex-1"
          />
        </div>
      </div>

      {/* Enhanced Tabs Section with Better Spacing and Width */}
      <div className="w-full flex justify-center mt-20 mb-8">
        <Tabs
          tabData={tabData}
          onTabChange={onTabChange}
          selectedTab={selectedTab}
          variant="wide" // Use wide variant for better appearance
        />
      </div>

      {/* Tab Content */}
      <div className="mt-8">{getActiveTabContent(selectedTab)}</div>
    </div>
  );
};

export default MyCoin;
