'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, SortAsc } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { COIN_CATEGORIES, SORT_OPTIONS } from '@/constants';
import { useFilters, SortOption } from '@/hooks/useFilters';
import { fadeInUp, staggerContainer } from '@/lib/animations';

import { FiltersProps } from './types';
import { useTranslation } from '@/hooks/useTranslation';

const CustomDropdown = dynamic(() => import('@/components/ui/CustomDropdown').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const StaggeredGrid = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredGrid })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const StaggeredItem = dynamic(() => import('@/components/ui/AnimatedWrapper').then(mod => ({ default: mod.StaggeredItem })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const Filters: React.FC<FiltersProps> = ({
  onFilterChange,
  title,
  search = true,
  loading = false,
}) => {
  const { filters, setCategory, setSearch, setSortBy, setFilterVerified } =
    useFilters();

  const [searchInput, setSearchInput] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (onFilterChange) {
      onFilterChange(filters);
    }
  }, [filters, onFilterChange]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchInput);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchInput, setSearch]);

  const handleVerificationChange = (isVerified: boolean | null) => {
    if (filters.filterVerified === isVerified) {
      setFilterVerified(null);
    } else {
      setFilterVerified(isVerified);
    }
  };

  const handleCategorySelect = (category: string) => {
    setCategory(category);
  };

  return (
    <motion.div
      className="w-full py-4"
      variants={staggerContainer}
      initial="initial"
      animate="animate"
    >
      <motion.div
        className="flex flex-col md:flex-row items-center justify-between"
        variants={fadeInUp}
      >
        <div className="flex items-center gap-4 mb-4 md:mb-0 flex-wrap justify-center">
          <motion.span
            className="text-xl font-semibold"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            {title}
          </motion.span>

          {search && (
            <motion.div
              className="relative flex items-center w-full md:w-auto"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <motion.div
                className="absolute left-2 z-10"
                animate={{
                  scale: isSearchFocused ? 1.1 : 1,
                  color: isSearchFocused ? '#F58A38' : '#666',
                }}
                transition={{ duration: 0.2 }}
              >
                <Search size={20} />
              </motion.div>
              <motion.input
                type="text"
                className="w-full h-[31px] rounded-md px-8 py-1 outline-none border-0 bg-[#F5F5F5] focus:bg-white focus:ring-2 focus:ring-[#F58A38] transition-all duration-300"
                placeholder={t('common.search') + '...'}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                whileFocus={{ scale: 1.02 }}
                disabled={loading}
              />
              {searchInput && (
                <motion.button
                  className="absolute right-2 text-gray-400 hover:text-gray-600"
                  onClick={() => setSearchInput('')}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  ×
                </motion.button>
              )}
            </motion.div>
          )}
        </div>

        <motion.div
          className="flex flex-col md:flex-row items-center gap-4 md:gap-8"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-600" />
            <CustomDropdown
              options={SORT_OPTIONS.map(option => ({ ...option, label: t(`sort.${option.value}`) }))}
              selectedValue={filters.sortBy}
              onChange={(value) => setSortBy(value as SortOption)}
              label={t('common.sort')}
            />
          </div>

          <div className="flex gap-4">
            {[
              { value: null, label: t('categories.all') },
              { value: true, label: t('filters.verified') },
              { value: false, label: t('filters.notVerified') },
            ].map((option) => (
              <motion.label
                key={option.label}
                className="flex items-center gap-1 text-sm font-semibold cursor-pointer"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
                whileHover={{ scale: 1.05 }}
              >
                <motion.input
                  type="checkbox"
                  checked={filters.filterVerified === option.value}
                  onChange={() => handleVerificationChange(option.value)}
                  className="form-checkbox h-4 w-4 text-[#F58A38] rounded focus:ring-[#F58A38] focus:ring-offset-0 transition-all duration-200"
                  whileTap={{ scale: 0.9 }}
                  disabled={loading}
                />
                <span
                  className={`transition-colors duration-200 ${filters.filterVerified === option.value
                      ? 'text-[#F58A38]'
                      : 'text-gray-700'
                    }`}
                >
                  {option.label}
                </span>
              </motion.label>
            ))}
          </div>
        </motion.div>
      </motion.div>

      <StaggeredGrid
        className="flex flex-wrap gap-3 mt-6 justify-center md:justify-start"
        staggerDelay={0.05}
      >
        {COIN_CATEGORIES.map((category, index) => (
          <StaggeredItem key={category}>
            <motion.button
              onClick={() => handleCategorySelect(category)}
              className={`px-6 py-2 rounded-full border cursor-pointer font-semibold transition-all duration-300 ${filters.category === category
                  ? 'bg-[#F58A38] text-white border-[#F58A38] shadow-lg'
                  : 'bg-white text-[#514141] border-[#514141] hover:border-[#F58A38] hover:text-[#F58A38]'
                }`}
              whileHover={{
                scale: 1.05,
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
              }}
              whileTap={{ scale: 0.95 }}
              disabled={loading}
            >
              <motion.span
                className="relative"
                initial={false}
                animate={{
                  color: filters.category === category ? '#ffffff' : '#514141',
                }}
              >
                {t(`categories.${category.toLowerCase()}`)}
                {filters.category === category && (
                  <motion.div
                    className="absolute inset-0 bg-[#F58A38] rounded-full -z-10"
                    layoutId="activeCategory"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.span>
            </motion.button>
          </StaggeredItem>
        ))}
      </StaggeredGrid>

      <AnimatePresence>
        {loading && (
          <motion.div
            className="flex justify-center mt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="flex items-center gap-2 text-gray-500"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <SortAsc size={16} />
              <span>{t('filters.applyingFilters')}</span>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Filters;
