import type { Metadata } from 'next';

// Core SEO constants
export const SEO_CONFIG = {
  SITE_NAME: 'FunHi',
  SITE_URL: 'https://fun-hi-front-end.vercel.app/',
  SITE_DESCRIPTION: 'FunHi is the premier platform for discovering, trading, and investing in trending coins. Join our community of crypto enthusiasts and explore the latest opportunities in the digital currency market.',
  SITE_IMAGE: '/images/funhi-logo.png',
  SITE_IMAGE_WIDTH: 1200,
  SITE_IMAGE_HEIGHT: 630,
  TWITTER_HANDLE: '@funhi',
  LOCALE: 'en_US',
  TYPE: 'website',
} as const;

// Base metadata configuration
export const BASE_METADATA: Metadata = {
  metadataBase: new URL(SEO_CONFIG.SITE_URL),
  title: {
    default: `${SEO_CONFIG.SITE_NAME} - Discover and Trade Trending Coins`,
    template: `%s | ${SEO_CONFIG.SITE_NAME}`
  },
  description: SEO_CONFIG.SITE_DESCRIPTION,
  keywords: [
    'cryptocurrency',
    'trading',
    'coins',
    'crypto',
    'investment',
    'blockchain',
    'digital assets',
    'trending coins',
    'crypto trading platform',
    'token discovery'
  ],
  authors: [{ name: `${SEO_CONFIG.SITE_NAME} Team` }],
  creator: SEO_CONFIG.SITE_NAME,
  publisher: SEO_CONFIG.SITE_NAME,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: SEO_CONFIG.TYPE,
    locale: SEO_CONFIG.LOCALE,
    url: SEO_CONFIG.SITE_URL,
    siteName: SEO_CONFIG.SITE_NAME,
    title: `${SEO_CONFIG.SITE_NAME} - Discover and Trade Trending Coins`,
    description: SEO_CONFIG.SITE_DESCRIPTION,
    images: [
      {
        url: SEO_CONFIG.SITE_IMAGE,
        width: SEO_CONFIG.SITE_IMAGE_WIDTH,
        height: SEO_CONFIG.SITE_IMAGE_HEIGHT,
        alt: `${SEO_CONFIG.SITE_NAME} - Crypto Trading Platform`,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: `${SEO_CONFIG.SITE_NAME} - Discover and Trade Trending Coins`,
    description: SEO_CONFIG.SITE_DESCRIPTION,
    images: [SEO_CONFIG.SITE_IMAGE],
    creator: SEO_CONFIG.TWITTER_HANDLE,
    site: SEO_CONFIG.TWITTER_HANDLE,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
  },
  other: {
    'theme-color': '#FF6600',
    'color-scheme': 'light',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': SEO_CONFIG.SITE_NAME,
    'application-name': SEO_CONFIG.SITE_NAME,
    'msapplication-TileColor': '#FF6600',
    'msapplication-config': '/browserconfig.xml',
  },
};

// Structured data helpers
export const STRUCTURED_DATA = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SEO_CONFIG.SITE_NAME,
    url: SEO_CONFIG.SITE_URL,
    logo: `${SEO_CONFIG.SITE_URL}${SEO_CONFIG.SITE_IMAGE}`,
    description: SEO_CONFIG.SITE_DESCRIPTION,
    sameAs: [
      `https://twitter.com/${SEO_CONFIG.TWITTER_HANDLE.replace('@', '')}`,
      // Add other social media URLs here
    ],
  },
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: SEO_CONFIG.SITE_NAME,
    url: SEO_CONFIG.SITE_URL,
    description: SEO_CONFIG.SITE_DESCRIPTION,
  },
  breadcrumb: (items: Array<{ name: string; url: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${SEO_CONFIG.SITE_URL}${item.url}`,
    })),
  }),
}; 