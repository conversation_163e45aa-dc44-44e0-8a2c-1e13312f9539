import { motion } from 'framer-motion';
import React from 'react';
import { ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react';

import type { TableColumn, TableRow } from '../types';

interface TableHeaderProps<T = TableRow> {
  columns: TableColumn<T>[];
  selectable?: boolean;
  selectedRows?: Set<string | number>;
  paginatedData?: T[];
  onSelectAll?: () => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string) => void;
}

const TableHeader = <T extends TableRow>({
  columns,
  selectable = false,
  selectedRows = new Set(),
  paginatedData = [],
  onSelectAll,
  sortColumn,
  sortDirection,
  onSort,
}: TableHeaderProps<T>) => {
  const isAllSelected =
    paginatedData.length > 0 &&
    paginatedData.every((row) => selectedRows.has(row.id));
  const isIndeterminate =
    paginatedData.some((row) => selectedRows.has(row.id)) && !isAllSelected;

  const getSortIcon = (columnKey: string) => {
    if (sortColumn !== columnKey) {
      return <ChevronsUpDown className="w-4 h-4 text-gray-400" />;
    }
    return sortDirection === 'asc' ? (
      <ChevronUp className="w-4 h-4 text-blue-600" />
    ) : (
      <ChevronDown className="w-4 h-4 text-blue-600" />
    );
  };

  const getAlignmentClass = (align?: string) => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  return (
    <thead className="bg-gray-50">
      <tr>
        {/* Selection column */}
        {selectable && (
          <motion.th
            className="w-12 px-6 py-4 text-left"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={isAllSelected}
                ref={(input) => {
                  if (input) input.indeterminate = isIndeterminate;
                }}
                onChange={onSelectAll}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                aria-label="Select all rows"
              />
            </div>
          </motion.th>
        )}

        {/* Column headers */}
        {columns.map((column, index) => (
          <motion.th
            key={column.key}
            className={`px-6 py-4 font-semibold text-gray-900 ${getAlignmentClass(
              column.align
            )} ${
              column.sortable
                ? 'cursor-pointer hover:bg-gray-100 transition-colors'
                : ''
            }`}
            style={{ width: column.width }}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05, duration: 0.2 }}
            onClick={() => column.sortable && onSort?.(column.key)}
          >
            <div
              className={`flex items-center gap-2 ${
                column.align === 'center'
                  ? 'justify-center'
                  : column.align === 'right'
                  ? 'justify-end'
                  : 'justify-start'
              }`}
            >
              <span className="select-none">{column.header}</span>
              {column.sortable && getSortIcon(column.key)}
            </div>
          </motion.th>
        ))}
      </tr>
    </thead>
  );
};

export { TableHeader };
