const dataContext = require("../db");
const { Op } = require("sequelize");

/**
 * Initiate a dispute for a trade
 * Can be called by buyer or seller
 */
exports.initiateDispute = async (req, res, next) => {
  try {
    const { tradeId, reason, initiatorRole } = req.body;
    const userId = req.user.id; // From authentication middleware

    // Validate inputs
    if (!tradeId || !reason || !initiatorRole) {
      return res.status(400).json({ 
        status: 400, 
        message: 'Trade ID, reason, and initiator role are required' 
      });
    }

    if (!['buyer', 'seller'].includes(initiatorRole)) {
      return res.status(400).json({ 
        status: 400, 
        message: 'Initiator role must be buyer or seller' 
      });
    }

    // Get the trade
    const trade = await dataContext.TokenPurchased.findByPk(tradeId, {
      include: [
        { model: dataContext.User, as: 'user' },
        { model: dataContext.Perk, as: 'perkDetails', include: [{ model: dataContext.User, as: 'user' }] }
      ]
    });

    if (!trade) {
      return res.status(404).json({ status: 404, message: 'Trade not found' });
    }

    // Check if trade is in escrowed status
    if (trade.status !== 'escrowed') {
      return res.status(400).json({ 
        status: 400, 
        message: 'Dispute can only be initiated for escrowed trades' 
      });
    }

    // Check if user is authorized to initiate dispute
    const buyerId = trade.userId;
    const sellerId = trade.perkDetails.userId;
    
    if (initiatorRole === 'buyer' && userId !== buyerId) {
      return res.status(403).json({ status: 403, message: 'Not authorized as buyer' });
    }
    
    if (initiatorRole === 'seller' && userId !== sellerId) {
      return res.status(403).json({ status: 403, message: 'Not authorized as seller' });
    }

    // Check if dispute already exists
    const existingDispute = await dataContext.Dispute.findOne({ where: { tradeId } });
    if (existingDispute) {
      return res.status(400).json({ 
        status: 400, 
        message: 'Dispute already exists for this trade' 
      });
    }

    // Check dispute deadline (2 days after trade creation)
    const disputeDeadline = new Date(trade.createdAt);
    disputeDeadline.setDate(disputeDeadline.getDate() + 2);
    
    if (new Date() > disputeDeadline) {
      return res.status(400).json({ 
        status: 400, 
        message: 'Dispute deadline has passed' 
      });
    }

    // Create dispute
    const dispute = await dataContext.Dispute.create({
      tradeId,
      initiatorId: userId,
      initiatorRole,
      reason,
      status: 'open',
      disputeDeadline
    });

    // Update trade status
    await trade.update({
      disputeStatus: 'open',
      fulfillmentStatus: 'disputed'
    });

    // Send notification to all active moderators
    try {
      const notificationCount = await dataContext.Notification.createDisputeNotification(
        dispute.id,
        tradeId,
        initiatorRole,
        reason
      );
      console.log(`Sent dispute notifications to ${notificationCount} moderators`);
    } catch (error) {
      console.error('Failed to send dispute notifications:', error);
      // Don't fail the dispute creation if notifications fail
    }

    res.status(201).json({
      status: 201,
      message: 'Dispute initiated successfully',
      data: {
        disputeId: dispute.id,
        tradeId,
        status: 'open'
      }
    });

  } catch (error) {
    console.error('Initiate dispute error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get all disputes for moderator dashboard
 * Only accessible by moderators
 */
exports.getDisputes = async (req, res, next) => {
  try {
    const { status, page = 1, pageSize = 20 } = req.query;
    const userId = req.user.id;

    // Check if user is a moderator
    const moderator = await dataContext.Moderator.findOne({ 
      where: { userId, isActive: true } 
    });
    
    if (!moderator) {
      return res.status(403).json({ 
        status: 403, 
        message: 'Access denied. Moderator privileges required.' 
      });
    }

    // Build query conditions
    const whereConditions = {};
    if (status) {
      whereConditions.status = status;
    }

    // Get disputes with pagination
    const offset = (page - 1) * pageSize;
    const disputes = await dataContext.Dispute.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: dataContext.TokenPurchased,
          as: 'trade',
          include: [
            { model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] },
            { 
              model: dataContext.Perk, 
              as: 'perkDetails',
              include: [{ model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] }]
            }
          ]
        },
        { model: dataContext.User, as: 'initiator', attributes: ['id', 'username'] },
        { model: dataContext.User, as: 'moderator', attributes: ['id', 'username'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset: offset
    });

    res.status(200).json({
      status: 200,
      message: 'Disputes retrieved successfully',
      data: {
        disputes: disputes.rows,
        pagination: {
          total: disputes.count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(disputes.count / pageSize)
        }
      }
    });

  } catch (error) {
    console.error('Get disputes error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Assign a dispute to a moderator
 */
exports.assignDispute = async (req, res, next) => {
  try {
    const { disputeId } = req.params;
    const userId = req.user.id;

    // Check if user is a moderator
    const moderator = await dataContext.Moderator.findOne({ 
      where: { userId, isActive: true } 
    });
    
    if (!moderator) {
      return res.status(403).json({ 
        status: 403, 
        message: 'Access denied. Moderator privileges required.' 
      });
    }

    // Check if moderator can take more disputes
    if (!moderator.canTakeMoreDisputes()) {
      return res.status(400).json({ 
        status: 400, 
        message: 'Moderator has reached maximum concurrent disputes limit' 
      });
    }

    // Get the dispute
    const dispute = await dataContext.Dispute.findByPk(disputeId);
    if (!dispute) {
      return res.status(404).json({ status: 404, message: 'Dispute not found' });
    }

    if (dispute.status !== 'open') {
      return res.status(400).json({ 
        status: 400, 
        message: 'Dispute is not available for assignment' 
      });
    }

    // Assign dispute to moderator
    await dispute.update({
      moderatorId: userId,
      status: 'assigned'
    });

    // Update moderator stats
    await moderator.update({
      currentActiveDisputes: moderator.currentActiveDisputes + 1
    });
    await moderator.updateActivity();

    // Send assignment notification to moderator
    try {
      await dataContext.Notification.createAssignmentNotification(
        disputeId,
        userId,
        dispute.tradeId
      );
    } catch (error) {
      console.error('Failed to send assignment notification:', error);
    }

    res.status(200).json({
      status: 200,
      message: 'Dispute assigned successfully',
      data: { disputeId, moderatorId: userId }
    });

  } catch (error) {
    console.error('Assign dispute error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Resolve a dispute by distributing funds
 * Only the assigned moderator can resolve
 */
exports.resolveDispute = async (req, res, next) => {
  try {
    const { disputeId } = req.params;
    const { buyerPercentage, sellerPercentage, moderatorNotes, resolution } = req.body;
    const userId = req.user.id;

    // Validate inputs
    if (buyerPercentage + sellerPercentage !== 100) {
      return res.status(400).json({
        status: 400,
        message: 'Buyer and seller percentages must sum to 100'
      });
    }

    if (!['resolved_buyer', 'resolved_seller', 'resolved_split'].includes(resolution)) {
      return res.status(400).json({
        status: 400,
        message: 'Invalid resolution type'
      });
    }

    // Get the dispute
    const dispute = await dataContext.Dispute.findByPk(disputeId, {
      include: [
        {
          model: dataContext.TokenPurchased,
          as: 'trade',
          include: [
            { model: dataContext.User, as: 'user' },
            { model: dataContext.Perk, as: 'perkDetails' }
          ]
        }
      ]
    });

    if (!dispute) {
      return res.status(404).json({ status: 404, message: 'Dispute not found' });
    }

    // Check if user is the assigned moderator
    if (dispute.moderatorId !== userId) {
      return res.status(403).json({
        status: 403,
        message: 'Only the assigned moderator can resolve this dispute'
      });
    }

    if (dispute.status !== 'assigned') {
      return res.status(400).json({
        status: 400,
        message: 'Dispute is not in assigned status'
      });
    }

    // Update dispute with resolution
    await dispute.update({
      status: resolution,
      buyerPercentage,
      sellerPercentage,
      moderatorNotes,
      resolvedAt: new Date()
    });

    // Update trade status
    await dispute.trade.update({
      disputeStatus: 'resolved',
      fulfillmentStatus: 'resolved'
    });

    // Update moderator stats
    const moderator = await dataContext.Moderator.findOne({ where: { userId } });
    if (moderator) {
      await moderator.update({
        currentActiveDisputes: Math.max(0, moderator.currentActiveDisputes - 1),
        totalDisputesResolved: moderator.totalDisputesResolved + 1
      });
      await moderator.updateActivity();
    }

    // Send resolution notifications to buyer and seller
    try {
      const buyerId = dispute.trade.userId;
      const sellerId = dispute.trade.perkDetails.userId;

      await dataContext.Notification.createResolutionNotification(
        disputeId,
        buyerId,
        sellerId,
        resolution,
        dispute.tradeId
      );
    } catch (error) {
      console.error('Failed to send resolution notifications:', error);
    }

    // TODO: Execute the actual fund distribution on-chain
    // This would involve calling the smart contract to distribute the escrowed funds
    // based on the percentages decided by the moderator

    res.status(200).json({
      status: 200,
      message: 'Dispute resolved successfully',
      data: {
        disputeId,
        resolution,
        buyerPercentage,
        sellerPercentage,
        resolvedAt: dispute.resolvedAt
      }
    });

  } catch (error) {
    console.error('Resolve dispute error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get dispute details
 */
exports.getDisputeDetails = async (req, res, next) => {
  try {
    const { disputeId } = req.params;
    const userId = req.user.id;

    const dispute = await dataContext.Dispute.findByPk(disputeId, {
      include: [
        {
          model: dataContext.TokenPurchased,
          as: 'trade',
          include: [
            { model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] },
            {
              model: dataContext.Perk,
              as: 'perkDetails',
              attributes: ['perkId', 'name', 'description', 'price'],
              include: [{ model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] }]
            }
          ]
        },
        { model: dataContext.User, as: 'initiator', attributes: ['id', 'username'] },
        { model: dataContext.User, as: 'moderator', attributes: ['id', 'username'] }
      ]
    });

    if (!dispute) {
      return res.status(404).json({ status: 404, message: 'Dispute not found' });
    }

    // Check access permissions
    const buyerId = dispute.trade.userId;
    const sellerId = dispute.trade.perkDetails.userId;
    const moderatorId = dispute.moderatorId;

    // Check if user is a moderator
    const userModerator = await dataContext.Moderator.findOne({
      where: { userId, isActive: true }
    });

    const hasAccess = userId === buyerId ||
                     userId === sellerId ||
                     userId === moderatorId ||
                     userModerator;

    if (!hasAccess) {
      return res.status(403).json({
        status: 403,
        message: 'Access denied'
      });
    }

    res.status(200).json({
      status: 200,
      message: 'Dispute details retrieved successfully',
      data: dispute
    });

  } catch (error) {
    console.error('Get dispute details error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};
