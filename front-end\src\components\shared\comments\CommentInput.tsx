import React, { useState } from "react";
import { motion } from "framer-motion";
import { User, Send } from "lucide-react";
import { useTranslation } from '@/hooks/useTranslation';


interface CommentInputProps {
  isLoggedIn: boolean;
  onLogin: () => void;
  onSubmit: (comment: string) => Promise<void>;
}

export const CommentInput: React.FC<CommentInputProps> = ({
  isLoggedIn,
  onLogin,
  onSubmit,
}) => {
  const { t } = useTranslation();
  const [newComment, setNewComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePostComment = async () => {
    if (!newComment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit(newComment);
      setNewComment("");
    } catch (error) {
      console.error("Error posting comment:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="p-6 bg-white border-t border-gray-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 }}
    >
      {!isLoggedIn ? (
        <motion.button
          onClick={onLogin}
          className="w-full h-12 bg-gray-100 rounded-xl flex items-center justify-center text-gray-600 font-medium hover:bg-gray-200 transition-colors"
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
        >
          <User size={18} className="mr-2" />
          {t('comments.loginToComment')}
        </motion.button>
      ) : (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
            <User size={18} className="text-gray-500" />
          </div>
          <motion.input
            type="text"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder={t('comments.writeAComment')}
            className="w-full h-12 px-4 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6600] focus:border-transparent transition-all duration-300"
            onKeyDown={(e) =>
              e.key === "Enter" && !isSubmitting && handlePostComment()
            }
            disabled={isSubmitting}
          />
          <motion.button
            onClick={handlePostComment}
            disabled={isSubmitting || !newComment.trim()}
            className="min-w-12 w-12 h-12 bg-[#F58A38] rounded-xl flex items-center justify-center text-white hover:bg-[#FF6600] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            whileHover={
              !isSubmitting && newComment.trim() ? { scale: 1.05 } : {}
            }
            whileTap={!isSubmitting && newComment.trim() ? { scale: 0.95 } : {}}
          >
            {isSubmitting ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: 'linear',
                }}
              >
                <Send size={16} />
              </motion.div>
            ) : (
              <Send size={16} />
            )}
          </motion.button>
        </div>
      )}
    </motion.div>
  );
};
