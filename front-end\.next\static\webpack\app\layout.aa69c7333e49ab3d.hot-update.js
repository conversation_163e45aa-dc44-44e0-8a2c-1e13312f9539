"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/shared/chat/ChatModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCheck,DollarSign,Flag,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @privy-io/react-auth */ \"(app-pages-browser)/./node_modules/@privy-io/react-auth/dist/esm/privy-provider-Bg_oLXHQ.mjs\");\n/* harmony import */ var _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useChatSocket */ \"(app-pages-browser)/./src/hooks/useChatSocket.ts\");\n/* harmony import */ var _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useWallet */ \"(app-pages-browser)/./src/hooks/useWallet.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AppContext */ \"(app-pages-browser)/./src/contexts/AppContext.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(app-pages-browser)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _utils_escrow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/escrow */ \"(app-pages-browser)/./src/utils/escrow.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// \"use client\";\n// import React, { useState, useEffect, useCallback, useRef } from \"react\";\n// import { ArrowLeft, MoreVertical, CheckCheck, Send, X } from \"lucide-react\";\n// import { usePrivy } from \"@privy-io/react-auth\";\n// import { useChatSocket } from \"@/hooks/useChatSocket\";\n// import axios from \"axios\";\n// import dayjs from 'dayjs';\n// import { useAppContext, useUnreadChatMessages } from '@/contexts/AppContext';\n// import { useTranslation } from '@/hooks/useTranslation';\n// interface ChatModalProps {\n//   chatRoomId: string;\n//   buyerId: string | number;\n//   sellerId: string | number;\n//   onClose: () => void;\n// }\n// const API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\n// const ChatModal: React.FC<ChatModalProps> = ({ chatRoomId, buyerId, sellerId, onClose }) => {\n//   const { t, isRTL } = useTranslation();\n//   // All hooks at the top\n//   const { user } = usePrivy();\n//   const { removeUnreadChatMessagesForTrade } = useUnreadChatMessages();\n//   const { setOpenChatTradeId } = useAppContext();\n//   const [messages, setMessages] = useState<any[]>([]);\n//   const [input, setInput] = useState(\"\");\n//   const messagesEndRef = useRef<HTMLDivElement>(null);\n//   const [currentStatus, setCurrentStatus] = useState<string | null>(null);\n//   const [releaseDeadline, setReleaseDeadline] = useState<Date | null>(null);\n//   const [timeLeft, setTimeLeft] = useState<string | null>(null);\n//   const [receiverInfo, setReceiverInfo] = useState<{ name?: string; email?: string; wallet?: string } | null>(null);\n//   const notificationAudioRef = useRef<HTMLAudioElement | null>(null);\n//   // Get userBo.id from localStorage for consistent sender check\n//   const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//   const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//   const myUserId = userBo?.id;\n//   // Fetch receiver info (email) when receiverId changes\n//   useEffect(() => {\n//     async function fetchReceiver() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/users/${sellerId}`);\n//         // Try to extract email from possible locations\n//         let email = res.data.email || (res.data.user && res.data.user.email) || (res.data.data && res.data.data.email);\n//         let name = res.data.name || (res.data.user && res.data.user.name) || (res.data.data && res.data.data.name);\n//         let wallet = res.data.wallet || (res.data.user && res.data.user.wallet) || (res.data.data && res.data.data.wallet);\n//         setReceiverInfo({ name, email, wallet });\n//       } catch (err) {\n//         setReceiverInfo(null);\n//       }\n//     }\n//     if (sellerId) fetchReceiver();\n//   }, [sellerId]);\n//   // Fetch message history on mount or when chatRoomId changes\n//   useEffect(() => {\n//     async function fetchMessages() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/chat-room/${chatRoomId}`);\n//         // Replace messages state with fetched history (no merge)\n//         setMessages((res.data.data || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()));\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchMessages();\n//   }, [chatRoomId]);\n//   // Fetch release deadline on mount or when tradeId changes\n//   useEffect(() => {\n//     async function fetchDeadline() {\n//       try {\n//         const res = await axios.get(`${API_BASE}/messages/${chatRoomId}/release-deadline`);\n//         const deadline = new Date(res.data.releaseDeadline);\n//         setReleaseDeadline(deadline);\n//       } catch (err) {\n//         // handle error\n//       }\n//     }\n//     fetchDeadline();\n//   }, [chatRoomId]);\n//   // Timer countdown (only updates local state)\n//   useEffect(() => {\n//     if (!releaseDeadline) return;\n//     const interval = setInterval(() => {\n//       const now = new Date();\n//       const diff = releaseDeadline.getTime() - now.getTime();\n//       if (diff <= 0) {\n//         setTimeLeft(t('chat.autoReleaseInProgress'));\n//         clearInterval(interval);\n//       } else {\n//         // Format as translation string\n//         const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n//         const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);\n//         const minutes = Math.floor((diff / (1000 * 60)) % 60);\n//         const seconds = Math.floor((diff / 1000) % 60);\n//         setTimeLeft(t('chat.timeFormat', { days, hours, minutes, seconds }));\n//       }\n//     }, 1000);\n//     return () => clearInterval(interval);\n//   }, [releaseDeadline, t]);\n//   // Handle incoming real-time messages (deduplicate by id, tempId, and content)\n//   const handleMessage = useCallback((msg: any) => {\n//     setMessages(prev => {\n//       // If message already exists (by id, tempId, or identical content+createdAt+senderId), skip\n//       if (prev.some(m =>\n//         (m.id && msg.id && m.id === msg.id) ||\n//         (m.tempId && msg.tempId && m.tempId === msg.tempId) ||\n//         (m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId)\n//       )) {\n//         return prev;\n//       }\n//       // Play notification sound if the current user is the receiver\n//       const userBoStr = typeof window !== \"undefined\" ? localStorage.getItem(\"userBo\") : null;\n//       const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//       if (userBo && msg.receiverId === userBo.id && notificationAudioRef.current) {\n//         notificationAudioRef.current.currentTime = 0;\n//         notificationAudioRef.current.play();\n//       }\n//       return [...prev, msg].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n//     });\n//   }, []);\n//   const handleTradeStatus = useCallback((data: { status: string }) => setCurrentStatus(data.status), []);\n//   // Setup socket\n//   const { sendMessage, release, report, authenticated, joinedRoom, tradeStatus } = useChatSocket({\n//     chatRoomId, // Pass chatRoomId to useChatSocket\n//     userId: user?.id || user?.wallet?.address || \"unknown\",\n//     wallet: user?.wallet?.address || \"\",\n//     onMessage: handleMessage,\n//     onTradeStatus: handleTradeStatus,\n//   });\n//   // Scroll to bottom on new message\n//   useEffect(() => {\n//     messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n//   }, [messages]);\n//   // Send message handler (optimistic update)\n//   const handleSend = (e: React.FormEvent) => {\n//     e.preventDefault();\n//     if (!input.trim()) return;\n//     const userBoStr = localStorage.getItem(\"userBo\");\n//     const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n//     if (!userBo?.id) {\n//       alert(t('chat.userIdNotFound'));\n//       return;\n//     }\n//     sendMessage({\n//       chatRoomId,\n//       senderId: userBo.id,\n//       receiverId: userBo.id === buyerId ? sellerId : buyerId,\n//       message: input,\n//     });\n//     setInput(\"\");\n//   };\n//   // Helper: format time\n//   const formatTime = (dateStr: string) => {\n//     const date = new Date(dateStr);\n//     return date.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\n//   };\n//   // Helper: format date for separator\n//   const formatDateSeparator = (date: Date) => {\n//     const today = dayjs().startOf('day');\n//     const msgDay = dayjs(date).startOf('day');\n//     if (msgDay.isSame(today)) return t('chat.today');\n//     if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n//     return msgDay.format('D MMM YYYY');\n//   };\n//   // Helper to get initials from email or name\n//   const getInitials = (email?: string, name?: string) => {\n//     if (name && name.trim()) return name.slice(0, 2).toUpperCase();\n//     if (email && email.trim()) return email.slice(0, 2).toUpperCase();\n//     return \"?\";\n//   };\n//   // Helper to format wallet address (first 4 + last 4 characters)\n//   const formatWalletAddress = (wallet?: string) => {\n//     if (!wallet || wallet.length < 8) return wallet;\n//     return `${wallet.slice(0, 4)}...${wallet.slice(-4)}`;\n//   };\n//   // Helper to get display name with priority: name > email > formatted wallet\n//   const getDisplayName = () => {\n//     if (receiverInfo?.name && receiverInfo.name.trim()) {\n//       return receiverInfo.name;\n//     }\n//     if (receiverInfo?.email && receiverInfo.email.trim()) {\n//       return receiverInfo.email;\n//     }\n//     if (receiverInfo?.wallet && receiverInfo.wallet.trim()) {\n//       return formatWalletAddress(receiverInfo.wallet);\n//     }\n//     return t('chat.user');\n//   };\n//   // Show status in the UI and disable buttons if released or reported\n//   const isActionDisabled = currentStatus === 'released' || currentStatus === 'reported';\n//   // useEffect to set openChatTradeId and clear unread chat messages for this chatRoomId\n//   useEffect(() => {\n//     setOpenChatTradeId(chatRoomId);\n//     removeUnreadChatMessagesForTrade(chatRoomId);\n//     return () => {\n//       setOpenChatTradeId(null);\n//     };\n//   }, [chatRoomId, setOpenChatTradeId, removeUnreadChatMessagesForTrade]);\n//   // Only after all hooks, do conditional returns\n//   if (!user?.wallet?.address) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectWalletToChat')}</p>\n//           {/* You can trigger Privy login here if needed */}\n//         </div>\n//       </div>\n//     );\n//   }\n//   if (!authenticated || !joinedRoom) {\n//     return (\n//       <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\">\n//         <div className=\"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\">\n//           <p className=\"mb-4 text-gray-700 text-center\">{t('chat.connectingToChat')}</p>\n//         </div>\n//       </div>\n//     );\n//   }\n//   return (\n//     <div\n//       className={`fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300 ${isRTL ? 'rtl' : 'ltr'}`}\n//     >\n//       {/* Notification sound */}\n//       <audio ref={notificationAudioRef} src=\"/sounds/notification.mp3\" preload=\"auto\" />\n//         {/* Close button */}\n//         <button\n//           className=\"absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1\"\n//           onClick={onClose}\n//         >\n//           <X className=\"w-5 h-5 text-gray-700\" />\n//         </button>\n//         {/* Header */}\n//         {/* <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button\n//             className={isRTL ? 'ml-3' : 'mr-3'}\n//             onClick={onClose}\n//             style={isRTL ? { marginLeft: '12px', marginRight: 0 } : { marginRight: '12px', marginLeft: 0 }}\n//           >\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button> */}\n//           <div\n//           className={`flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl ${isRTL ? 'flex-row-reverse' : ''}`}\n//         >\n//           <button className={isRTL ? 'ml-3' : 'mr-3'} onClick={onClose}>\n//             <ArrowLeft className=\"w-6 h-6 text-gray-700\" />\n//           </button>\n//           {/* <div\n//             className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}\n//             style={isRTL ? { direction: 'rtl', textAlign: 'right', alignItems: 'flex-end' } : { direction: 'ltr', textAlign: 'left', alignItems: 'flex-start' }}\n//           >\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div> */}\n//           <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`} style={isRTL ? { direction: 'rtl' } : {}}>\n//             <div className=\"relative\">\n//               <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n//                 <span className=\"text-white font-semibold text-sm\">{getInitials(receiverInfo?.email, receiverInfo?.name)}</span>\n//               </div>\n//               <span className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></span>\n//             </div>\n//             <div className=\"flex flex-col items-start\" style={isRTL ? { alignItems: 'flex-end', textAlign: 'right' } : {}}>\n//               <span className=\"font-semibold text-gray-900 text-sm\">{getDisplayName()}</span>\n//               {receiverInfo?.email && receiverInfo.email !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-500\">{receiverInfo.email}</span>\n//               )}\n//               {receiverInfo?.wallet && receiverInfo.wallet !== getDisplayName() && (\n//                 <span className=\"text-xs text-gray-400 font-mono\">{formatWalletAddress(receiverInfo.wallet)}</span>\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//         {/* Messages Container */}\n//         <div className=\"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto\" style={{ maxHeight: 400 }}>\n//           {(() => {\n//             let lastDate: string | null = null;\n//             return messages.map((msg, idx) => {\n//               const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n//               const showDate = msgDate && msgDate !== lastDate;\n//               lastDate = msgDate;\n//               return (\n//                 <React.Fragment key={msg.id ? `id-${msg.id}` : msg.tempId ? `temp-${msg.tempId}` : `fallback-${msg.senderId}-${msg.createdAt}-${idx}` }>\n//                   {showDate && (\n//                     <div className=\"flex justify-center my-2\">\n//                       <span className=\"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\">{msgDate}</span>\n//                     </div>\n//                   )}\n//                   <div className={`flex flex-col ${msg.senderId === myUserId ? \"items-end\" : \"items-start\"}`}>\n//                     <div\n//                       className={\n//                         msg.senderId === myUserId\n//                           ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\"\n//                           : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\"\n//                       }\n//                     >\n//                       <p className=\"text-sm leading-relaxed whitespace-pre-line\">{msg.message || msg.text}</p>\n//                     </div>\n//                     <div className={`flex items-center gap-1 mt-1 ${msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"}`}>\n//                       <span className=\"text-xs text-gray-400\">\n//                         {msg.createdAt ? formatTime(msg.createdAt) : msg.time || \"\"}\n//                       </span>\n//                       {msg.senderId === myUserId && <CheckCheck className=\"w-4 h-4 text-green-500\" />}\n//                     </div>\n//                   </div>\n//                 </React.Fragment>\n//               );\n//             });\n//           })()}\n//           <div ref={messagesEndRef} />\n//         </div>\n//         {/* Bottom Section */}\n//         {/* Auto-release Info */}\n//         <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n//           <p className=\"text-xs text-gray-500 text-center leading-relaxed\">\n//             {timeLeft ? (\n//               <>{t('chat.autoReleaseIn', { time: timeLeft })}</>\n//             ) : (\n//               <>{t('chat.loadingAutoRelease')}</>\n//             )}<br />\n//             {t('chat.reportTrade')}\n//           </p>\n//         </div>\n//         {/* Action Buttons */}\n//         <div className=\"px-4 py-3 space-y-2\">\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={release}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.iGotTheItem')}\n//           </button>\n//           <button\n//             className={`w-full bg-orange-500 text-white font-semibold rounded-lg py-3 hover:bg-orange-600 transition-colors ${isActionDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n//             onClick={report}\n//             disabled={isActionDisabled}\n//           >\n//             {t('chat.reportTrade')}\n//           </button>\n//           {currentStatus && (\n//             <div className=\"text-center text-xs text-gray-500 mt-2\">\n//               {t('chat.tradeStatus')} <span className=\"font-semibold\">{currentStatus}</span>\n//             </div>\n//           )}\n//         </div>\n//         {/* Message Input */}\n//         <form className=\"px-4 py-3 bg-orange-50\" onSubmit={handleSend}>\n//           <div className=\"flex items-center gap-2\">\n//             <input\n//               type=\"text\"\n//               placeholder={t('chat.typeMessage')}\n//               className=\"flex-1 bg-orange-100 rounded-full px-4 py-2 text-sm outline-none border border-transparent focus:border-orange-300 placeholder:text-gray-500\"\n//               value={input}\n//               onChange={e => setInput(e.target.value)}\n//               disabled={!authenticated || !joinedRoom}\n//             />\n//             <button type=\"submit\" className=\"bg-orange-500 p-2 rounded-full hover:bg-orange-600 transition-colors\" disabled={!authenticated || !joinedRoom}>\n//               <Send className=\"w-5 h-5 text-white\" />\n//             </button>\n//           </div>\n//         </form>\n//     </div>\n//   );\n// };\n// export default ChatModal; \n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst API_BASE = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8081\";\nconst ChatModal = (param)=>{\n    let { chatRoomId, buyerId, sellerId, onClose, onRelease, onRefund, onReport, onAccept, onInitiateDispute, activeTrade } = param;\n    var _user_wallet, _user_wallet1, _receiverInfo_name, _receiverInfo_email;\n    _s();\n    const { t, isRTL } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { user } = (0,_privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H)();\n    const { solanaWallet, isConnected, getWalletAddress } = (0,_hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { removeUnreadChatMessagesForTrade } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages)();\n    const { setOpenChatTradeId } = (0,_contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [receiverInfo, setReceiverInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const notificationAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentTradeStatus, setCurrentTradeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status) || null);\n    const [isOperationInProgress, setIsOperationInProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Request notification permission when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            if ('Notification' in window && Notification.permission === 'default') {\n                Notification.requestPermission().then({\n                    \"ChatModal.useEffect\": (permission)=>{\n                        console.log('🔔 [ChatModal] Notification permission:', permission);\n                    }\n                }[\"ChatModal.useEffect\"]);\n            }\n        }\n    }[\"ChatModal.useEffect\"], []);\n    // Get user ID\n    const userBoStr =  true ? localStorage.getItem(\"userBo\") : 0;\n    const userBo = userBoStr ? JSON.parse(userBoStr) : null;\n    const myUserId = userBo === null || userBo === void 0 ? void 0 : userBo.id;\n    // Determine user role - ensure type consistency\n    const buyerIdNum = typeof buyerId === 'string' ? parseInt(buyerId) : buyerId;\n    const sellerIdNum = typeof sellerId === 'string' ? parseInt(sellerId) : sellerId;\n    const myUserIdNum = typeof myUserId === 'string' ? parseInt(myUserId) : myUserId;\n    const isBuyer = myUserIdNum === buyerIdNum;\n    const isSeller = myUserIdNum === sellerIdNum;\n    // Use current trade status (which can be updated via Socket.IO) or fallback to activeTrade status\n    const tradeStatus = currentTradeStatus || (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.status);\n    const isEscrowed = tradeStatus === 'escrowed';\n    const isPendingAcceptance = tradeStatus === 'pending_acceptance';\n    const isEscrowReleased = tradeStatus === 'completed' || tradeStatus === 'released';\n    const isUserAuthorized = user && (isBuyer || isSeller);\n    // Trade action buttons should only show when escrow is active (not released)\n    const shouldShowTradeActions = (isEscrowed || isPendingAcceptance) && !isEscrowReleased;\n    // Seller can accept when escrow is pending acceptance\n    const canSellerAccept = isPendingAcceptance && isSeller && onAccept;\n    // Buyer can only release after seller has accepted\n    const canBuyerRelease = isEscrowed && !isPendingAcceptance && isBuyer;\n    // Debug logging\n    console.log('🔍 [ChatModal] User role debugging:', {\n        myUserId,\n        myUserIdNum,\n        buyerId,\n        buyerIdNum,\n        sellerId,\n        sellerIdNum,\n        isBuyer,\n        isSeller,\n        activeTrade: activeTrade ? {\n            id: activeTrade.id,\n            status: activeTrade.status,\n            tradeId: activeTrade.tradeId\n        } : null,\n        tradeStatus,\n        isEscrowed,\n        isPendingAcceptance,\n        isEscrowReleased,\n        shouldShowTradeActions,\n        canSellerAccept,\n        canBuyerRelease,\n        isUserAuthorized\n    });\n    // Chat should be enabled if user is authorized (buyer or seller), regardless of escrow status\n    const canChat = isUserAuthorized && isConnected && solanaWallet;\n    // Enhanced dispute button logic\n    const canShowDisputeButton = (userRole)=>{\n        if (!activeTrade || !onInitiateDispute) return false;\n        // Use the enhanced canInitiateDispute function with dispute status\n        const disputeCheck = (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.canInitiateDispute)(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus);\n        // Debug logging for dispute button visibility\n        console.log(\"\\uD83D\\uDD0D [ChatModal] Dispute button check for \".concat(userRole, \":\"), {\n            tradeStatus: activeTrade.status,\n            disputeStatus: activeTrade.disputeStatus,\n            canDispute: disputeCheck.canDispute,\n            reason: disputeCheck.reason,\n            createdAt: activeTrade.createdAt\n        });\n        return disputeCheck.canDispute;\n    };\n    // Fetch receiver info\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchReceiver() {\n                try {\n                    const targetId = isBuyer ? sellerId : buyerId;\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/users/\").concat(targetId));\n                    let email = res.data.email || res.data.user && res.data.user.email || res.data.data && res.data.data.email;\n                    let name = res.data.name || res.data.user && res.data.user.name || res.data.data && res.data.data.name;\n                    let wallet = res.data.wallet || res.data.user && res.data.user.wallet || res.data.data && res.data.data.wallet;\n                    setReceiverInfo({\n                        name,\n                        email,\n                        wallet\n                    });\n                } catch (err) {\n                    setReceiverInfo(null);\n                }\n            }\n            fetchReceiver();\n        }\n    }[\"ChatModal.useEffect\"], [\n        buyerId,\n        sellerId,\n        isBuyer\n    ]);\n    // Fetch messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            async function fetchMessages() {\n                try {\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(API_BASE, \"/messages/chat-room/\").concat(chatRoomId));\n                    setMessages((res.data.data || []).sort({\n                        \"ChatModal.useEffect.fetchMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useEffect.fetchMessages\"]));\n                } catch (err) {\n                    console.error(\"Error fetching messages:\", err);\n                }\n            }\n            fetchMessages();\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId\n    ]);\n    // Handle incoming messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleMessage]\": (msg)=>{\n            console.log('📨 [ChatModal] Received message:', msg, 'myUserId:', myUserId);\n            setMessages({\n                \"ChatModal.useCallback[handleMessage]\": (prev)=>{\n                    // Check for duplicates\n                    const isDuplicate = prev.some({\n                        \"ChatModal.useCallback[handleMessage].isDuplicate\": (m)=>m.id && msg.id && m.id === msg.id || m.tempId && msg.tempId && m.tempId === msg.tempId || m.message === msg.message && m.createdAt === msg.createdAt && m.senderId === msg.senderId\n                    }[\"ChatModal.useCallback[handleMessage].isDuplicate\"]);\n                    if (isDuplicate) {\n                        console.log('🔄 [ChatModal] Duplicate message, skipping');\n                        return prev;\n                    }\n                    // Play notification sound for received messages (not sent by me)\n                    if (msg.senderId !== myUserId && notificationAudioRef.current) {\n                        console.log('🔊 [ChatModal] Playing notification sound');\n                        try {\n                            notificationAudioRef.current.currentTime = 0;\n                            notificationAudioRef.current.play().catch({\n                                \"ChatModal.useCallback[handleMessage]\": (e)=>{\n                                    console.log('🔇 [ChatModal] Could not play notification sound (user interaction required):', e);\n                                    // Fallback: show browser notification if audio fails\n                                    if ('Notification' in window && Notification.permission === 'granted') {\n                                        new Notification('New Message', {\n                                            body: msg.message,\n                                            icon: '/images/funhi-logo.png'\n                                        });\n                                    }\n                                }\n                            }[\"ChatModal.useCallback[handleMessage]\"]);\n                        } catch (error) {\n                            console.log('🔇 [ChatModal] Audio play error:', error);\n                        }\n                    }\n                    const newMessages = [\n                        ...prev,\n                        msg\n                    ].sort({\n                        \"ChatModal.useCallback[handleMessage].newMessages\": (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n                    }[\"ChatModal.useCallback[handleMessage].newMessages\"]);\n                    console.log('✅ [ChatModal] Added new message, total messages:', newMessages.length);\n                    return newMessages;\n                }\n            }[\"ChatModal.useCallback[handleMessage]\"]);\n        }\n    }[\"ChatModal.useCallback[handleMessage]\"], [\n        myUserId\n    ]);\n    // Handle trade status updates\n    const handleTradeStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleTradeStatusUpdate]\": (data)=>{\n            console.log(\"[ChatModal] Trade status update received:\", data);\n            if (data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) || data.tradeId === (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId)) {\n                setCurrentTradeStatus(data.status);\n                console.log(\"[ChatModal] Updated trade status to: \".concat(data.status));\n            }\n        }\n    }[\"ChatModal.useCallback[handleTradeStatusUpdate]\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id,\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.tradeId\n    ]);\n    // Get the correct wallet address\n    const walletAddress = getWalletAddress();\n    console.log('🔍 [ChatModal] Wallet info:', {\n        privyWallet: user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : _user_wallet.address,\n        useWalletAddress: walletAddress,\n        isConnected,\n        chatRoomId\n    });\n    // Setup socket\n    const { sendMessage, authenticated, joinedRoom } = (0,_hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket)({\n        chatRoomId,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || myUserId || \"unknown\",\n        wallet: walletAddress || \"\",\n        onMessage: handleMessage,\n        onTradeStatus: handleTradeStatusUpdate\n    });\n    // Scroll to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }[\"ChatModal.useEffect\"], [\n        messages\n    ]);\n    // Set chat as open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            setOpenChatTradeId(chatRoomId);\n            removeUnreadChatMessagesForTrade(chatRoomId);\n            return ({\n                \"ChatModal.useEffect\": ()=>setOpenChatTradeId(null)\n            })[\"ChatModal.useEffect\"];\n        }\n    }[\"ChatModal.useEffect\"], [\n        chatRoomId,\n        setOpenChatTradeId,\n        removeUnreadChatMessagesForTrade\n    ]);\n    // Fetch latest trade status when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatModal.useEffect\": ()=>{\n            const fetchLatestTradeStatus = {\n                \"ChatModal.useEffect.fetchLatestTradeStatus\": async ()=>{\n                    if (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id) {\n                        try {\n                            console.log('🔄 [ChatModal] Fetching latest trade status for tradeId:', activeTrade.id);\n                            const { getTradeDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../axios/requests */ \"(app-pages-browser)/./src/axios/requests.ts\"));\n                            const tradeResponse = await getTradeDetails(Number(activeTrade.id));\n                            if (tradeResponse.status === 200 && tradeResponse.data.status !== currentTradeStatus) {\n                                console.log(\"\\uD83D\\uDD04 [ChatModal] Trade status updated: \".concat(currentTradeStatus, \" → \").concat(tradeResponse.data.status));\n                                setCurrentTradeStatus(tradeResponse.data.status);\n                            }\n                        } catch (error) {\n                            console.error('❌ [ChatModal] Failed to fetch latest trade status:', error);\n                        }\n                    }\n                }\n            }[\"ChatModal.useEffect.fetchLatestTradeStatus\"];\n            fetchLatestTradeStatus();\n        }\n    }[\"ChatModal.useEffect\"], [\n        activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.id\n    ]); // Only run when trade ID changes\n    // Send message\n    const handleSend = (e)=>{\n        e.preventDefault();\n        if (!input.trim() || !canChat) return;\n        const receiverId = isBuyer ? sellerId : buyerId;\n        console.log('🔍 [ChatModal] Sending message:', {\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim(),\n            canChat,\n            isEscrowed\n        });\n        sendMessage({\n            chatRoomId,\n            senderId: myUserId,\n            receiverId,\n            message: input.trim()\n        });\n        setInput(\"\");\n    };\n    // Enhanced action handlers that update status immediately and send system messages\n    const handleReleaseWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleReleaseWithStatusUpdate]\": async ()=>{\n            if (!onRelease) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow release...');\n                // Call the original release function\n                await onRelease();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('released');\n                console.log('✅ [ChatModal] Escrow released, status updated to released');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"\\uD83C\\uDF89 Escrow has been released! Trade completed successfully.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Release failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleReleaseWithStatusUpdate]\"], [\n        onRelease,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    const handleAcceptWithStatusUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatModal.useCallback[handleAcceptWithStatusUpdate]\": async ()=>{\n            if (!onAccept) return;\n            try {\n                setIsOperationInProgress(true);\n                console.log('🔄 [ChatModal] Starting escrow accept...');\n                // Call the original accept function\n                await onAccept();\n                // Update status immediately for better UX\n                setCurrentTradeStatus('escrowed');\n                console.log('✅ [ChatModal] Escrow accepted, status updated to escrowed');\n                // Send a system message to the chat\n                const receiverId = isBuyer ? sellerId : buyerId;\n                sendMessage({\n                    chatRoomId,\n                    senderId: myUserId,\n                    receiverId,\n                    message: \"✅ Escrow accepted! Buyer can now release funds when ready.\"\n                });\n            } catch (error) {\n                console.error('❌ [ChatModal] Accept failed:', error);\n            } finally{\n                setIsOperationInProgress(false);\n            }\n        }\n    }[\"ChatModal.useCallback[handleAcceptWithStatusUpdate]\"], [\n        onAccept,\n        sendMessage,\n        chatRoomId,\n        myUserId,\n        isBuyer,\n        sellerId,\n        buyerId\n    ]);\n    // Format time\n    const formatTime = (dateStr)=>{\n        const date = new Date(dateStr);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Format date separator\n    const formatDateSeparator = (date)=>{\n        const today = dayjs__WEBPACK_IMPORTED_MODULE_4___default()().startOf('day');\n        const msgDay = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).startOf('day');\n        if (msgDay.isSame(today)) return t('chat.today');\n        if (msgDay.isSame(today.subtract(1, 'day'))) return t('chat.yesterday');\n        return msgDay.format('D MMM YYYY');\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.name) return receiverInfo.name;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) return receiverInfo.email;\n        if (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) {\n            const wallet = receiverInfo.wallet;\n            return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n        }\n        return t('chat.user');\n    };\n    // Wallet formatting\n    const formatWalletAddress = (wallet)=>{\n        if (!wallet || wallet.length < 8) return wallet;\n        return \"\".concat(wallet.slice(0, 4), \"...\").concat(wallet.slice(-4));\n    };\n    if (!(user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : _user_wallet1.address)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-700 text-center\",\n                        children: t('chat.connectWalletToChat')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 824,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!authenticated || !joinedRoom) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white flex flex-col rounded-2xl shadow-2xl relative max-h-[90vh] p-8 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-gray-700 text-center\",\n                    children: t('chat.connectingToChat')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 842,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 841,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n            lineNumber: 840,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-8 left-0 ml-6 max-h-[90vh] w-[400px] z-50 bg-white shadow-2xl flex flex-col transition-transform duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: notificationAudioRef,\n                src: \"/sounds/notification.mp3\",\n                preload: \"auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 850,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center px-4 py-3 bg-white border-b border-gray-200 rounded-t-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-2 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 ml-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-[#FF6600] rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_name = receiverInfo.name) === null || _receiverInfo_name === void 0 ? void 0 : _receiverInfo_name.slice(0, 2).toUpperCase()) || (receiverInfo === null || receiverInfo === void 0 ? void 0 : (_receiverInfo_email = receiverInfo.email) === null || _receiverInfo_email === void 0 ? void 0 : _receiverInfo_email.slice(0, 2).toUpperCase()) || \"?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute bottom-0 right-0 w-3 h-3 bg-emerald-500 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900 text-sm\",\n                                        children: getDisplayName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: receiverInfo.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (receiverInfo === null || receiverInfo === void 0 ? void 0 : receiverInfo.wallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 font-mono\",\n                                        children: formatWalletAddress(receiverInfo.wallet)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 860,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 853,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 border-b \".concat(isEscrowReleased ? 'bg-green-50 border-green-100' : isEscrowed ? 'bg-orange-50 border-orange-100' : isPendingAcceptance ? 'bg-blue-50 border-blue-100' : 'bg-amber-50 border-amber-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full \".concat(isEscrowReleased ? 'bg-green-600' : isEscrowed ? 'bg-[#FF6600]' : isPendingAcceptance ? 'bg-blue-500' : 'bg-amber-500')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-slate-700\",\n                                    children: isEscrowReleased ? 'Trade Completed' : isEscrowed ? 'Escrow Active' : isPendingAcceptance ? 'Awaiting Seller Acceptance' : 'Pre-Purchase Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 896,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500\",\n                            children: isEscrowReleased ? 'Funds released successfully' : isEscrowed ? 'Funds secured on-chain' : isPendingAcceptance ? 'Seller needs to accept escrow' : 'Discussing before purchase'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                    lineNumber: 895,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 886,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 px-4 py-4 space-y-2 bg-gray-50 overflow-y-auto max-h-[400px]\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-orange-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: isEscrowed ? 'Escrow Chat Started' : 'Start the Conversation'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 max-w-xs\",\n                                children: isEscrowed ? 'Your funds are secured. Chat with the other party about the trade details.' : 'Discuss the details before making a purchase. Ask questions and clarify expectations.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 942,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 933,\n                        columnNumber: 11\n                    }, undefined) : messages.map((msg, idx)=>{\n                        const msgDate = msg.createdAt ? formatDateSeparator(new Date(msg.createdAt)) : '';\n                        const showDate = idx === 0 || msgDate !== formatDateSeparator(new Date(messages[idx - 1].createdAt));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gray-200 text-gray-700 px-4 py-1 rounded-full text-xs font-medium shadow\",\n                                        children: msgDate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col \".concat(msg.senderId === myUserId ? \"items-end\" : \"items-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: msg.senderId === myUserId ? \"bg-[#FF6600] text-white rounded-2xl rounded-br-sm px-4 py-2 max-w-xs shadow\" : \"bg-gray-100 text-gray-800 rounded-2xl rounded-bl-sm px-4 py-2 max-w-xs shadow\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed whitespace-pre-line\",\n                                                children: msg.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 mt-1 \".concat(msg.senderId === myUserId ? \"mr-2\" : \"ml-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: msg.createdAt ? formatTime(msg.createdAt) : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                msg.senderId === myUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, msg.id || \"msg-\".concat(idx), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 931,\n                columnNumber: 7\n            }, undefined),\n            activeTrade && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Trade Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium capitalize \".concat((currentTradeStatus || activeTrade.status) === 'escrowed' ? 'bg-blue-100 text-blue-800' : (currentTradeStatus || activeTrade.status) === 'completed' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'released' ? 'bg-green-100 text-green-800' : (currentTradeStatus || activeTrade.status) === 'pending_acceptance' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'),\n                                        children: currentTradeStatus || activeTrade.status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 989,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTrade.disputeStatus && activeTrade.disputeStatus !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Dispute:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1005,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).bgColor, \" \").concat((0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).color),\n                                        children: (0,_utils_escrow__WEBPACK_IMPORTED_MODULE_7__.getDisputeStatusInfo)(activeTrade.disputeStatus).label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 11\n                    }, undefined),\n                    shouldShowTradeActions && isConnected && solanaWallet && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 p-4 bg-slate-50 rounded-lg\",\n                        children: [\n                            isBuyer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canBuyerRelease && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleReleaseWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Releasing...' : t('chat.releaseFunds')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-700 font-medium\",\n                                                        children: \"Waiting for seller to accept escrow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 text-center mt-1\",\n                                                children: \"You'll be able to release funds once the seller accepts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1039,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('buyer') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('buyer'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            isSeller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canSellerAccept && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-[#FF6600] text-white font-semibold rounded-lg py-3 px-4 hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: handleAcceptWithStatusUpdate,\n                                        disabled: !isConnected || isOperationInProgress,\n                                        children: [\n                                            isOperationInProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            isOperationInProgress ? 'Accepting...' : 'Accept Escrow'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    isEscrowed && !isPendingAcceptance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-700 font-medium\",\n                                                        children: \"Escrow accepted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 text-center mt-1\",\n                                                children: \"Waiting for buyer to release funds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-slate-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-slate-700 focus:ring-2 focus:ring-slate-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: onReport,\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            t('chat.reportTrade')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-amber-600 text-white font-semibold rounded-lg py-3 px-4 hover:bg-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 focus:ring-offset-2 transition-all duration-200 text-sm flex items-center justify-center gap-2 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        onClick: ()=>onInitiateDispute === null || onInitiateDispute === void 0 ? void 0 : onInitiateDispute('seller'),\n                                        disabled: !isConnected,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            \"Initiate Dispute\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1122,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    !canShowDisputeButton('seller') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-800 text-center\",\n                                            children: (activeTrade === null || activeTrade === void 0 ? void 0 : activeTrade.disputeStatus) && activeTrade.disputeStatus !== 'none' ? \"Dispute status: \".concat(activeTrade.disputeStatus) : 'Dispute option available within 2 days of escrow creation'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1135,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1134,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowReleased && isUserAuthorized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"Trade Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-700\",\n                                            children: \"Escrow has been released. This trade is now complete.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1151,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1150,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && isUserAuthorized && (!isConnected || !solanaWallet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Wallet Connection Required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700\",\n                                            children: \"Please connect your Solana wallet to perform trade actions like releasing funds or initiating disputes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1166,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1165,\n                        columnNumber: 13\n                    }, undefined),\n                    isEscrowed && (!user || !isBuyer && !isSeller) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-red-400 mr-2\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1184,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Access Restricted\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1188,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: \"Only the buyer and seller can perform trade actions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1182,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'open' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-800\",\n                            children: \"A dispute has been initiated. A moderator will review this case shortly.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1200,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1199,\n                        columnNumber: 13\n                    }, undefined),\n                    activeTrade.disputeStatus === 'resolved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-green-50 border border-green-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-800\",\n                            children: \"The dispute for this trade has been resolved by a moderator.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                            lineNumber: 1208,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1207,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 986,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"px-4 py-3 bg-slate-50 border-t border-slate-200\",\n                onSubmit: handleSend,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: canChat ? t('chat.typeMessage') : 'Connect wallet to chat...',\n                                className: \"flex-1 bg-white rounded-lg px-4 py-3 text-sm outline-none border border-slate-300 focus:border-[#FF6600] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 placeholder:text-slate-500 transition-all duration-200\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                disabled: !canChat\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"p-3 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm \".concat(canChat && input.trim() ? 'bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2' : 'bg-slate-300 cursor-not-allowed opacity-50'),\n                                disabled: !input.trim() || !canChat,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCheck_DollarSign_Flag_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 9\n                    }, undefined),\n                    !canChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-gray-600 text-center\",\n                        children: !isUserAuthorized ? 'Only buyer and seller can chat' : !isConnected ? 'Please connect your wallet to chat' : 'Wallet connection required'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                        lineNumber: 1240,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n                lineNumber: 1217,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ahmed Barakat\\\\funHi-project\\\\funHi-project\\\\front-end\\\\src\\\\components\\\\shared\\\\chat\\\\ChatModal.tsx\",\n        lineNumber: 849,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatModal, \"DNr/tjJYbHPK+3Qa5/mVdoIMeas=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _privy_io_react_auth__WEBPACK_IMPORTED_MODULE_8__.H,\n        _hooks_useWallet__WEBPACK_IMPORTED_MODULE_3__.useWallet,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useUnreadChatMessages,\n        _contexts_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        _hooks_useChatSocket__WEBPACK_IMPORTED_MODULE_2__.useChatSocket\n    ];\n});\n_c = ChatModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatModal);\nvar _c;\n$RefreshReg$(_c, \"ChatModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/chat/ChatModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a130de80c4ef\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFscmFobWFcXERlc2t0b3BcXEFobWVkIEJhcmFrYXRcXGZ1bkhpLXByb2plY3RcXGZ1bkhpLXByb2plY3RcXGZyb250LWVuZFxcc3JjXFxzdHlsZXNcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTEzMGRlODBjNGVmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});